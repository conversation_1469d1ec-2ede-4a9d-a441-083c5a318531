# Infinitium Signal Configuration Template
# Copy this file to .env and update the values for your environment

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Application Environment
RUST_ENV=development
RUST_LOG=info,infinitum_signal=debug
RUST_BACKTRACE=1

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
SERVER_WORKERS=4
SERVER_TIMEOUT=30
SERVER_KEEP_ALIVE=75

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database
DATABASE_URL=postgresql://infinitum_user:infinitum_pass@localhost:5432/infinitum_signal
DATABASE_MAX_CONNECTIONS=10
DATABASE_MIN_CONNECTIONS=1
DATABASE_CONNECT_TIMEOUT=30
DATABASE_IDLE_TIMEOUT=600

# Redis Cache
REDIS_URL=redis://localhost:6379
REDIS_MAX_CONNECTIONS=10
REDIS_TIMEOUT=5

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRATION=3600
JWT_REFRESH_EXPIRATION=86400

# Encryption
ENCRYPTION_KEY=your-32-byte-encryption-key-change-this
HASH_ROUNDS=12

# API Keys
API_KEY_HEADER=X-API-Key
API_RATE_LIMIT=1000
API_RATE_WINDOW=3600

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# Vulnerability Databases
NVD_API_KEY=your-nvd-api-key
NVD_BASE_URL=https://services.nvd.nist.gov/rest/json
NVD_RATE_LIMIT=50

# GitHub Security Advisories
GITHUB_TOKEN=your-github-token
GITHUB_API_URL=https://api.github.com

# Snyk Integration
SNYK_API_TOKEN=your-snyk-api-token
SNYK_ORG_ID=your-snyk-org-id
SNYK_BASE_URL=https://api.snyk.io

# OSV Database
OSV_API_URL=https://api.osv.dev

# =============================================================================
# BLOCKCHAIN CONFIGURATION
# =============================================================================

# Hyperledger Fabric
BLOCKCHAIN_NETWORK=hyperledger-fabric
BLOCKCHAIN_CHANNEL=compliance-channel
BLOCKCHAIN_CHAINCODE=infinitum-compliance
BLOCKCHAIN_MSP_ID=InfinitumMSP
BLOCKCHAIN_PEER_ENDPOINT=grpc://localhost:7051
BLOCKCHAIN_ORDERER_ENDPOINT=grpc://localhost:7050

# Blockchain Credentials
BLOCKCHAIN_CERT_PATH=/path/to/cert.pem
BLOCKCHAIN_KEY_PATH=/path/to/key.pem
BLOCKCHAIN_CA_PATH=/path/to/ca.pem

# =============================================================================
# SCANNING CONFIGURATION
# =============================================================================

# Scanner Settings
SCANNER_TIMEOUT=300
SCANNER_MAX_CONCURRENT=5
SCANNER_TEMP_DIR=/tmp/infinitum-scans
SCANNER_CLEANUP_INTERVAL=3600

# SBOM Scanner
SBOM_ENABLED=true
SBOM_FORMATS=cyclonedx,spdx
SBOM_INCLUDE_DEV_DEPS=false
SBOM_MAX_DEPTH=10

# Vulnerability Scanner
VULN_ENABLED=true
VULN_SEVERITY_THRESHOLD=medium
VULN_AUTO_UPDATE=true
VULN_UPDATE_INTERVAL=3600

# Hardware Scanner
HBOM_ENABLED=true
HBOM_FIRMWARE_ANALYSIS=true
HBOM_BINARY_ANALYSIS=true

# =============================================================================
# COMPLIANCE CONFIGURATION
# =============================================================================

# Compliance Frameworks
COMPLIANCE_CERT_IN_ENABLED=true
COMPLIANCE_SEBI_ENABLED=true
COMPLIANCE_RBI_ENABLED=false
COMPLIANCE_ISO27001_ENABLED=true
COMPLIANCE_SOC2_ENABLED=false
COMPLIANCE_GDPR_ENABLED=true

# Report Generation
REPORT_OUTPUT_DIR=/var/lib/infinitum-signal/reports
REPORT_RETENTION_DAYS=90
REPORT_AUTO_CLEANUP=true

# PDF Generation
PDF_ENGINE=wkhtmltopdf
PDF_TEMPLATE_DIR=templates/pdf
PDF_FONT_DIR=fonts

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================

# Metrics
METRICS_ENABLED=true
METRICS_PORT=9090
METRICS_PATH=/metrics

# Prometheus
PROMETHEUS_ENDPOINT=http://localhost:9090
PROMETHEUS_JOB_NAME=infinitum-signal

# Grafana
GRAFANA_URL=http://localhost:3000
GRAFANA_API_KEY=your-grafana-api-key

# Logging
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE=/var/log/infinitum-signal/app.log
LOG_MAX_SIZE=100MB
LOG_MAX_FILES=10

# Tracing
TRACING_ENABLED=true
JAEGER_ENDPOINT=http://localhost:14268/api/traces
TRACE_SAMPLE_RATE=0.1

# =============================================================================
# MESSAGING & QUEUES
# =============================================================================

# RabbitMQ
RABBITMQ_URL=amqp://guest:guest@localhost:5672
RABBITMQ_EXCHANGE=infinitum-signal
RABBITMQ_QUEUE_SCANS=scans
RABBITMQ_QUEUE_REPORTS=reports
RABBITMQ_QUEUE_NOTIFICATIONS=notifications

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================

# File Storage
STORAGE_TYPE=local
STORAGE_PATH=/var/lib/infinitum-signal/storage

# S3 Configuration (if using S3)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=infinitum-signal-storage

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================

# Email Notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>

# Slack Integration
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
SLACK_CHANNEL=#security-alerts

# Microsoft Teams
TEAMS_WEBHOOK_URL=https://outlook.office.com/webhook/YOUR/TEAMS/WEBHOOK

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================

# Development Mode
DEV_MODE=true
DEV_AUTO_RELOAD=true
DEV_CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Testing
TEST_DATABASE_URL=postgresql://test_user:test_pass@localhost:5432/infinitum_signal_test
TEST_REDIS_URL=redis://localhost:6380
TEST_PARALLEL=true

# Mock Services
MOCK_EXTERNAL_APIS=false
MOCK_BLOCKCHAIN=false
MOCK_SCANNERS=false

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================

# Connection Pools
DB_POOL_SIZE=10
REDIS_POOL_SIZE=10
HTTP_CLIENT_POOL_SIZE=20

# Timeouts
HTTP_TIMEOUT=30
DB_TIMEOUT=30
REDIS_TIMEOUT=5
SCAN_TIMEOUT=300

# Concurrency
MAX_CONCURRENT_SCANS=5
MAX_CONCURRENT_REPORTS=3
WORKER_THREADS=4

# Memory Limits
MAX_MEMORY_USAGE=2GB
MAX_FILE_SIZE=100MB
MAX_SCAN_SIZE=1GB
