name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1

jobs:
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: infinitum_signal_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Rust toolchain
      uses: dtolnay/rust-toolchain@stable
      with:
        components: rustfmt, clippy

    - name: Cache Rust dependencies
      uses: Swatinem/rust-cache@v2
      with:
        key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          pkg-config \
          libssl-dev \
          libpq-dev \
          wkhtmltopdf \
          xvfb

    - name: Install security tools
      run: |
        # Install Trivy
        curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sudo sh -s -- -b /usr/local/bin
        
        # Install Syft
        curl -sSfL https://raw.githubusercontent.com/anchore/syft/main/install.sh | sudo sh -s -- -b /usr/local/bin
        
        # Install Grype
        curl -sSfL https://raw.githubusercontent.com/anchore/grype/main/install.sh | sudo sh -s -- -b /usr/local/bin

    - name: Check formatting
      run: cargo fmt --all -- --check

    - name: Run Clippy
      run: cargo clippy --all-targets --all-features -- -D warnings

    - name: Run unit tests
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/infinitum_signal_test
        REDIS_URL: redis://localhost:6379
      run: cargo test --lib --bins

    - name: Run integration tests
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/infinitum_signal_test
        REDIS_URL: redis://localhost:6379
      run: cargo test --test '*'

    - name: Generate test coverage
      uses: taiki-e/install-action@cargo-tarpaulin
    
    - name: Run coverage
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/infinitum_signal_test
        REDIS_URL: redis://localhost:6379
      run: |
        cargo tarpaulin --verbose --all-features --workspace --timeout 120 --out xml

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: cobertura.xml
        fail_ci_if_error: true

  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Rust toolchain
      uses: dtolnay/rust-toolchain@stable

    - name: Cache Rust dependencies
      uses: Swatinem/rust-cache@v2

    - name: Install cargo-audit
      run: cargo install cargo-audit

    - name: Run security audit
      run: cargo audit

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  build:
    name: Build and Package
    runs-on: ubuntu-latest
    needs: [test, security-audit]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Rust toolchain
      uses: dtolnay/rust-toolchain@stable

    - name: Cache Rust dependencies
      uses: Swatinem/rust-cache@v2

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          pkg-config \
          libssl-dev \
          libpq-dev

    - name: Build release binary
      run: cargo build --release

    - name: Run binary tests
      run: |
        ./target/release/infinitum-signal --version
        ./target/release/infinitum-signal-cli --help

    - name: Create release archive
      run: |
        mkdir -p release
        cp target/release/infinitum-signal release/
        cp target/release/infinitum-signal-cli release/
        cp README.md LICENSE release/
        tar -czf infinitum-signal-${{ github.sha }}.tar.gz -C release .

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: infinitum-signal-${{ github.sha }}
        path: infinitum-signal-${{ github.sha }}.tar.gz

  docker:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: [test, security-audit]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Docker Hub
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: infinitumsignal/infinitum-signal
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./docker/Dockerfile
        platforms: linux/amd64,linux/arm64
        push: ${{ github.event_name != 'pull_request' }}
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Run Trivy vulnerability scanner on image
      if: github.event_name != 'pull_request'
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: infinitumsignal/infinitum-signal:${{ github.sha }}
        format: 'sarif'
        output: 'trivy-image-results.sarif'

    - name: Upload Trivy image scan results
      if: github.event_name != 'pull_request'
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-image-results.sarif'

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build, docker]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_STAGING }}

    - name: Deploy to staging
      run: |
        # Update image tag in deployment
        sed -i "s|infinitum-signal:latest|infinitum-signal:${{ github.sha }}|g" deployment/kubernetes/deployment.yaml
        
        # Apply Kubernetes manifests
        kubectl apply -f deployment/kubernetes/namespace.yaml
        kubectl apply -f deployment/kubernetes/
        
        # Wait for deployment to be ready
        kubectl rollout status deployment/infinitum-signal -n infinitum-signal --timeout=300s

    - name: Run smoke tests
      run: |
        # Get service URL
        STAGING_URL=$(kubectl get service infinitum-signal -n infinitum-signal -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
        
        # Wait for service to be ready
        sleep 30
        
        # Run health check
        curl -f "http://${STAGING_URL}:8080/health" || exit 1
        
        # Run basic API tests
        curl -f "http://${STAGING_URL}:8080/api/v1/health" || exit 1

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build, docker]
    if: github.event_name == 'release'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_PRODUCTION }}

    - name: Deploy to production
      run: |
        # Update image tag in deployment
        sed -i "s|infinitum-signal:latest|infinitum-signal:${{ github.event.release.tag_name }}|g" deployment/kubernetes/deployment.yaml
        
        # Apply Kubernetes manifests
        kubectl apply -f deployment/kubernetes/namespace.yaml
        kubectl apply -f deployment/kubernetes/
        
        # Wait for deployment to be ready
        kubectl rollout status deployment/infinitum-signal -n infinitum-signal --timeout=600s

    - name: Run production smoke tests
      run: |
        # Get service URL
        PROD_URL=$(kubectl get service infinitum-signal -n infinitum-signal -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
        
        # Wait for service to be ready
        sleep 60
        
        # Run comprehensive health checks
        curl -f "http://${PROD_URL}:8080/health" || exit 1
        curl -f "http://${PROD_URL}:8080/api/v1/health" || exit 1
        
        # Check metrics endpoint
        curl -f "http://${PROD_URL}:9090/metrics" || exit 1

    - name: Notify deployment success
      if: success()
      run: |
        echo "✅ Production deployment successful!"
        echo "🚀 Infinitium Signal v${{ github.event.release.tag_name }} is now live"
