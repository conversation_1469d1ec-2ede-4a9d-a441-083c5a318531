name: OSV Vulnerability Scan (PR)

on:
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'Cargo.toml'
      - 'Cargo.lock'
      - 'src/**'
      - '.github/workflows/**'

jobs:
  osv-scan:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup Rust
      uses: dtolnay/rust-toolchain@stable

    - name: Cache dependencies
      uses: Swatinem/rust-cache@v2

    - name: Build project
      run: cargo build --release

    - name: Install dependencies for scanning
      run: |
        sudo apt-get update
        sudo apt-get install -y jq curl

    - name: Extract dependencies from Cargo.toml
      id: extract-deps
      run: |
        # Extract dependencies from Cargo.toml
        cargo metadata --format-version 1 | jq -r '.packages[] | select(.name == "infinitium-signal") | .dependencies[] | "\(.name) \(.version)"' > dependencies.txt
        echo "dependencies=$(cat dependencies.txt | wc -l)" >> $GITHUB_OUTPUT

    - name: Run OSV Scan
      id: osv-scan
      run: |
        echo "## OSV Vulnerability Scan Results" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        # Run the built binary with scan command
        SCAN_OUTPUT=$(./target/release/infinitum-signal scan . --format json 2>&1 || echo '{"error": "Scan failed"}')

        # Check if scan was successful
        if echo "$SCAN_OUTPUT" | jq -e '.error' >/dev/null 2>&1; then
          echo "❌ OSV scan failed" >> $GITHUB_STEP_SUMMARY
          echo "$SCAN_OUTPUT" >> $GITHUB_STEP_SUMMARY
          echo "scan_failed=true" >> $GITHUB_OUTPUT
        else
          # Parse and format results
          VULN_COUNT=$(echo "$SCAN_OUTPUT" | jq '.vulnerabilities | length' 2>/dev/null || echo "0")
          echo "🔍 Found $VULN_COUNT vulnerabilities" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          if [ "$VULN_COUNT" -gt 0 ]; then
            echo "### Vulnerabilities Found:" >> $GITHUB_STEP_SUMMARY
            echo "$SCAN_OUTPUT" | jq -r '.vulnerabilities[] | "- **\(.id)**: \(.summary) (CVSS: \(.cvss_score // "N/A"))"' >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "scan_failed=true" >> $GITHUB_OUTPUT
          else
            echo "✅ No vulnerabilities found" >> $GITHUB_STEP_SUMMARY
            echo "scan_failed=false" >> $GITHUB_OUTPUT
          fi
        fi

    - name: Comment on PR
      if: always()
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const summary = fs.readFileSync(process.env.GITHUB_STEP_SUMMARY, 'utf8');

          const body = `## 🔒 OSV Vulnerability Scan

${summary}

*This scan was performed on commit: \`${process.env.GITHUB_SHA.substring(0, 7)}\`*`;

          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: body
          });

    - name: Fail if vulnerabilities found
      if: steps.osv-scan.outputs.scan_failed == 'true'
      run: |
        echo "Vulnerabilities found in OSV scan. Please review and fix before merging."
        exit 1