name: OSV Vulnerability Scan (Scheduled)

on:
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to scan'
        required: false
        default: 'main'
        type: string

jobs:
  osv-scan-scheduled:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      issues: write
      pull-requests: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        ref: ${{ github.event.inputs.branch || 'main' }}
        fetch-depth: 0

    - name: Setup Rust
      uses: dtolnay/rust-toolchain@stable

    - name: Cache dependencies
      uses: Swatinem/rust-cache@v2

    - name: Build project
      run: cargo build --release

    - name: Install dependencies for scanning
      run: |
        sudo apt-get update
        sudo apt-get install -y jq curl

    - name: Extract dependencies from Cargo.toml
      id: extract-deps
      run: |
        # Extract dependencies from Cargo.toml
        cargo metadata --format-version 1 | jq -r '.packages[] | select(.name == "infinitium-signal") | .dependencies[] | "\(.name) \(.version)"' > dependencies.txt
        echo "dependencies=$(cat dependencies.txt | wc -l)" >> $GITHUB_OUTPUT

    - name: Run OSV Scan
      id: osv-scan
      run: |
        echo "## OSV Vulnerability Scan Results (Scheduled)" >> $GITHUB_STEP_SUMMARY
        echo "**Branch:** ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
        echo "**Date:** $(date -u)" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        # Run the built binary with scan command
        SCAN_OUTPUT=$(./target/release/infinitum-signal scan . --format json 2>&1 || echo '{"error": "Scan failed"}')

        # Save scan results for later use
        echo "$SCAN_OUTPUT" > osv_scan_results.json

        # Check if scan was successful
        if echo "$SCAN_OUTPUT" | jq -e '.error' >/dev/null 2>&1; then
          echo "❌ OSV scan failed" >> $GITHUB_STEP_SUMMARY
          echo "$SCAN_OUTPUT" >> $GITHUB_STEP_SUMMARY
          echo "scan_failed=true" >> $GITHUB_OUTPUT
          echo "vuln_count=0" >> $GITHUB_OUTPUT
        else
          # Parse and format results
          VULN_COUNT=$(echo "$SCAN_OUTPUT" | jq '.vulnerabilities | length' 2>/dev/null || echo "0")
          HIGH_SEVERITY=$(echo "$SCAN_OUTPUT" | jq '[.vulnerabilities[] | select(.severity == "HIGH" or (.cvss_score // 0) >= 7.0)] | length' 2>/dev/null || echo "0")
          MEDIUM_SEVERITY=$(echo "$SCAN_OUTPUT" | jq '[.vulnerabilities[] | select(.severity == "MEDIUM" or ((.cvss_score // 0) >= 4.0 and (.cvss_score // 0) < 7.0))] | length' 2>/dev/null || echo "0")

          echo "🔍 Scan Summary:" >> $GITHUB_STEP_SUMMARY
          echo "- Total vulnerabilities: $VULN_COUNT" >> $GITHUB_STEP_SUMMARY
          echo "- High severity: $HIGH_SEVERITY" >> $GITHUB_STEP_SUMMARY
          echo "- Medium severity: $MEDIUM_SEVERITY" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          if [ "$VULN_COUNT" -gt 0 ]; then
            echo "### Vulnerabilities Found:" >> $GITHUB_STEP_SUMMARY
            echo "$SCAN_OUTPUT" | jq -r '.vulnerabilities[] | "- **\(.id)** (\(.severity // "Unknown")): \(.summary)"' >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
          else
            echo "✅ No vulnerabilities found" >> $GITHUB_STEP_SUMMARY
          fi

          echo "scan_failed=false" >> $GITHUB_OUTPUT
          echo "vuln_count=$VULN_COUNT" >> $GITHUB_OUTPUT
          echo "high_severity=$HIGH_SEVERITY" >> $GITHUB_OUTPUT
        fi

    - name: Upload scan results
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: osv-scan-results-${{ github.run_id }}
        path: osv_scan_results.json
        retention-days: 30

    - name: Create issue on high severity vulnerabilities
      if: steps.osv-scan.outputs.high_severity > 0
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const scanResults = JSON.parse(fs.readFileSync('osv_scan_results.json', 'utf8'));

          const highSeverityVulns = scanResults.vulnerabilities.filter(v =>
            v.severity === 'HIGH' || (v.cvss_score && v.cvss_score >= 7.0)
          );

          if (highSeverityVulns.length > 0) {
            const issueBody = `## 🚨 High Severity Vulnerabilities Detected

**Scan Date:** ${new Date().toISOString()}
**Branch:** ${process.env.GITHUB_REF_NAME}
**Total High Severity:** ${highSeverityVulns.length}

### Details:
${highSeverityVulns.map(v => `- **${v.id}**: ${v.summary}
  - Severity: ${v.severity || 'Unknown'}
  - CVSS Score: ${v.cvss_score || 'N/A'}
  - Package: ${v.package.name}@${v.package.version}
  - Affected Versions: ${v.affected_versions.join(', ') || 'N/A'}
  - Fixed Versions: ${v.fixed_versions.join(', ') || 'N/A'}`).join('\n\n')}

### Recommended Actions:
1. Review the vulnerabilities listed above
2. Update affected dependencies to fixed versions
3. Test thoroughly after updates
4. Consider security implications for production deployment

*This issue was automatically created by the scheduled OSV vulnerability scan.*`;

            // Check if similar issue already exists
            const existingIssues = await github.rest.issues.listForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: ['security', 'vulnerability', 'osv-scan'],
              state: 'open'
            });

            const similarIssue = existingIssues.data.find(issue =>
              issue.title.includes('High Severity Vulnerabilities Detected')
            );

            if (!similarIssue) {
              await github.rest.issues.create({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: `🚨 High Severity Vulnerabilities Detected (${highSeverityVulns.length})`,
                body: issueBody,
                labels: ['security', 'vulnerability', 'osv-scan', 'auto-generated']
              });
            } else {
              // Update existing issue
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: similarIssue.number,
                body: `## 🔄 Updated Scan Results

**New Scan Date:** ${new Date().toISOString()}
**New High Severity Count:** ${highSeverityVulns.length}

${highSeverityVulns.map(v => `- **${v.id}**: ${v.summary}`).join('\n')}`
              });
            }
          }

    - name: Send notification to Slack (if configured)
      if: steps.osv-scan.outputs.high_severity > 0 && env.SLACK_WEBHOOK_URL != ''
      run: |
        curl -X POST -H 'Content-type: application/json' \
          --data "{\"text\":\"🚨 High severity vulnerabilities detected in ${{ github.repository }} (${{ steps.osv-scan.outputs.high_severity }})\nView: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}\"}" \
          ${{ secrets.SLACK_WEBHOOK_URL }}