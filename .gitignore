# =============================================================================
# Infinitium Signal - Enterprise Cybersecurity Platform .gitignore
# =============================================================================
# Production-ready .gitignore for Rust cybersecurity project with comprehensive
# coverage of build artifacts, security files, and development tools.

# =============================================================================
# RUST-SPECIFIC FILES
# =============================================================================

# Build artifacts and target directory
/target/
**/target/

# Cargo.lock should be committed for applications, ignored for libraries
# Since this is an application (binary crates), we commit Cargo.lock
# Cargo.lock

# Cargo build cache
.cargo/

# Rust analyzer cache
.rust-analyzer/

# Rustfmt backup files
**/*.rs.bk

# Clippy cache
.clippy.toml

# =============================================================================
# SECURITY & SENSITIVE DATA
# =============================================================================

# Environment files containing secrets
.env
.env.local
.env.*.local
.env.production
.env.staging
.env.development

# API keys and secrets
**/api_keys/
**/secrets/
**/*.key
**/*.pem
**/*.p12
**/*.pfx
**/*.crt
**/*.cer
**/*.der

# Private keys and certificates (except sample/demo ones)
private_keys/
certificates/
!certs/blockchain.crt
!certs/blockchain.key
!certs/ca.crt

# Database files and backups
*.db
*.sqlite
*.sqlite3
*.db-journal
*.db-wal
*.db-shm
database_backups/
db_dumps/

# Configuration files with sensitive data
config.yaml
config.yml
config.toml
!test-config.yaml
!config.example.yaml

# JWT secrets and encryption keys
jwt_secret*
encryption_key*

# =============================================================================
# PROJECT-SPECIFIC EXCLUSIONS
# =============================================================================

# Generated SBOM and vulnerability reports
/output/
**/output/
/reports/
**/reports/
/scan_results/
**/scan_results/

# Temporary scanning files
/tmp/
**/tmp/
/temp/
**/temp/
/infinitum-scans/
**/infinitum-scans/

# Blockchain data that shouldn't be committed
/blockchain_data/
**/blockchain_data/
/blockchain_storage/
**/blockchain_storage/

# Compliance report PDFs and generated documents
**/*.pdf
!docs/**/*.pdf
compliance_reports/
audit_reports/

# Performance test results and benchmarks
/performance_results/
**/performance_results/
/benchmark_results/
**/benchmark_results/
criterion_outputs/

# Generated documentation
/target/doc/
**/target/doc/

# =============================================================================
# IDE AND EDITOR FILES
# =============================================================================

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# IntelliJ IDEA / CLion / RustRover
.idea/
*.iml
*.ipr
*.iws
.idea_modules/

# Vim
*.swp
*.swo
*~
.netrwhist
Session.vim
Sessionx.vim

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# =============================================================================
# OPERATING SYSTEM FILES
# =============================================================================

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
.AppleDouble
.LSOverride
Icon?

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# BUILD AND DEPLOYMENT ARTIFACTS
# =============================================================================

# Docker build cache and temporary files
.dockerignore.bak
docker-compose.override.yml
.docker/

# Kubernetes secrets and temporary manifests
k8s-secrets/
*.secret.yaml
*.secret.yml
deployment/kubernetes/secrets/

# Terraform state and variables
*.tfstate
*.tfstate.*
*.tfvars
.terraform/
.terraform.lock.hcl
terraform.tfplan

# Helm charts temporary files
deployment/helm/*/charts/
deployment/helm/*/Chart.lock

# =============================================================================
# DEVELOPMENT TOOLS AND ARTIFACTS
# =============================================================================

# Coverage reports
/coverage/
**/coverage/
/target/coverage/
**/target/coverage/
*.profraw
*.profdata

# Test results and artifacts
/target/test-results/
**/target/test-results/
test_output/
test_artifacts/

# Profiling data
*.prof
perf.data*
flamegraph.svg
profiling_data/

# Benchmark outputs
criterion/
bench_results/

# Logs
*.log
logs/
**/logs/
/logging/
!logging/loki/
!logging/fluentd/

# Temporary files
*.tmp
*.temp
*.bak
*.backup
*.orig

# =============================================================================
# MONITORING AND OBSERVABILITY
# =============================================================================

# Prometheus data
prometheus_data/
grafana_data/

# Metrics and monitoring temporary files
metrics_cache/
monitoring_temp/

# =============================================================================
# PACKAGE MANAGERS AND DEPENDENCIES
# =============================================================================

# Node.js (for any JS tooling)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Python (for any Python tooling)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
pip-log.txt
pip-delete-this-directory.txt

# =============================================================================
# MISCELLANEOUS
# =============================================================================

# Archive files
*.zip
*.tar.gz
*.tar.bz2
*.rar
*.7z

# Backup files
*.bak
*.backup
*.old
*.orig

# Lock files (except Cargo.lock for applications)
package-lock.json
yarn.lock
poetry.lock

# Cache directories
.cache/
cache/

# Editor swap files
.*.swp
.*.swo

# OS thumbnails
._*

# =============================================================================
# PROJECT-SPECIFIC TEMPORARY FILES
# =============================================================================

# Demo and test data outputs (keep source data, ignore generated)
demo-data/outputs/
demo-data/generated/
tests/fixtures/generated/

# Encrypted files (except examples)
*.enc
*.encrypted
!examples/**/*.enc

# Compliance framework temporary files
cert_in_temp/
sebi_temp/
iso27001_temp/

# SBOM/HBOM temporary processing files
sbom_processing/
hbom_processing/
vulnerability_cache/

# =============================================================================
# END OF .gitignore
# =============================================================================
.kilocode/mcp.json
package.json
