[package]
name = "infinitium-signal"
version = "0.1.0"
edition = "2021"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
description = "Enterprise Cyber-Compliance Platform with SBOM/HBOM scanning, vulnerability analysis, and blockchain audit trails"
license = "Proprietary"
repository = "https://github.com/tanm-sys/infinitium-signal"
homepage = "https://github.com/tanm-sys/infinitium-signal"
documentation = "https://github.com/tanm-sys/infinitium-signal/blob/main/README.md"
keywords = ["cybersecurity", "compliance", "sbom", "vulnerability", "blockchain"]
categories = ["security", "compliance", "enterprise"]
readme = "README.md"
rust-version = "1.80"

[dependencies]
# Async Runtime
tokio = { version = "1.42", features = ["full"] }
futures = "0.3"
async-trait = "0.1"

# CLI and Configuration
clap = { version = "4.5", features = ["derive", "env"] }
config = "0.14"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"
toml = "0.8"

# API Server
axum = { version = "0.7", features = ["ws", "multipart", "macros"] }
tower = { version = "0.5", features = ["full"] }
tower-http = { version = "0.6", features = ["full"] }
hyper = { version = "1.5", features = ["full"] }

# HTTP Client
reqwest = { version = "0.12", features = ["json", "rustls-tls", "multipart"] }
warp = "0.3"

# Database
sqlx = { version = "0.8.6", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid", "json"] }
diesel = { version = "2.2", features = ["postgres", "chrono", "uuid"] }
diesel_migrations = "2.2"
redis = { version = "0.26", features = ["tokio-comp", "connection-manager"] }

# Authentication & Security
jsonwebtoken = "9.3"
argon2 = "0.5"
ring = "0.17"
ed25519-dalek = "2.1"
rand = "0.8"
uuid = { version = "1.10", features = ["v4", "serde"] }

# Cryptography & Blockchain
merkletree = "0.23"
sha2 = "0.10"
blake3 = "1.5"
aes-gcm = "0.10"
crc32fast = "1.4"
encoding_rs = "0.8"
md5 = "0.7"
openssl = { version = "0.10", features = ["vendored"] }
strsim = "0.11"
sysinfo = "0.30"

# Time & Date
chrono = { version = "0.4", features = ["serde"] }
time = "0.3"

# Logging & Observability
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-appender = "0.2"
prometheus = "0.14"
metrics = "0.24"
metrics-prometheus = "0.8"

# OpenTelemetry Integration
opentelemetry = { version = "0.23", features = ["logs"] }
opentelemetry_sdk = { version = "0.23", features = ["rt-tokio"] }
opentelemetry-prometheus = "0.16"
opentelemetry-jaeger = "0.22"
opentelemetry-zipkin = "0.21"
opentelemetry-otlp = { version = "0.16", features = ["grpc-tonic", "http-proto", "reqwest-client"] }

# Error Handling
anyhow = "1.0"
thiserror = "1.0"
color-eyre = "0.6"

# File & Path Operations
walkdir = "2.4"
tempfile = "3.8"
tar = "0.4"
flate2 = "1.0"
zip = "0.6"

# Serialization & Formats
bincode = "1.3"
csv = "1.3"
xml-rs = "0.8"

# Compliance & Standards
cyclonedx-bom = "0.4"
spdx = "0.9"

# PDF Generation
printpdf = "0.6"
wkhtmltopdf = "0.4"

# Template Engine
tera = "1.19"
handlebars = "4.5"

# Orchestration & Scheduling
tokio-cron-scheduler = "0.9"
lapin = "3.2"

# Validation
validator = { version = "0.20", features = ["derive"] }
regex = "1.10"

# Machine Learning
linfa = { version = "0.7", features = ["serde"] }
linfa-linear = "0.7"
linfa-logistic = "0.7"
linfa-svm = "0.7"
linfa-clustering = "0.7"
linfa-reduction = "0.7"
rust-bert = { version = "0.21", optional = true }
ndarray = { version = "0.15", features = ["serde"] }
ndarray-rand = "0.14"
smartcore = { version = "0.3", features = ["serde"] }
# Utilities
# Progress bars
indicatif = "0.17"
once_cell = "1.19"
lazy_static = "1.4"
itertools = "0.12"
rayon = "1.8"
semver = "1.0"
base64 = "0.22"
hex = "0.4"
url = "2.5"

# Enhanced Security & Performance
tokio-stream = "0.1"
dashmap = "6.0"
rustls = "0.23"
rustls-pemfile = "2.1"
tokio-rustls = "0.26"
criterion = { version = "0.5", features = ["html_reports"] }
metrics-exporter-prometheus = "0.15"
governor = "0.6"
futures-util = "0.3"

# API Documentation
utoipa = { version = "5.0", features = ["axum_extras", "chrono", "uuid"] }
utoipa-swagger-ui = { version = "8.0", features = ["axum"] }

# Rate Limiting
urlencoding = "2.1.3"

[dev-dependencies]
# Testing
tokio-test = "0.4"
criterion = { version = "0.5", features = ["html_reports"] }
proptest = "1.4"
mockall = "0.12"
wiremock = "0.6"
testcontainers = "0.15"

# Test Utilities
pretty_assertions = "1.4"
serial_test = "3.0"
rstest = "0.18"

# Profiling
flamegraph = "0.6"
pprof = "0.13"
# jemalloc = "0.3"
# jemalloc-ctl = "0.3"
tikv-jemallocator = "0.5"

[build-dependencies]
# Build Scripts

[[bin]]
name = "infinitium-signal"
path = "src/main.rs"

[[bin]]
name = "infinitium-signal-cli"
path = "src/cli.rs"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.dev]
opt-level = 0
debug = true
split-debuginfo = "unpacked"

[profile.test]
opt-level = 1
debug = true

# [workspace]
# members = [
#     ".",
#     "crates/*"
# ]

[features]
default = ["full"]
full = [
    "sbom-scanning",
    "vulnerability-analysis",
    "blockchain-integration",
    "compliance-frameworks",
    "monitoring"
]
sbom-scanning = []
vulnerability-analysis = []
blockchain-integration = []
compliance-frameworks = []
ml-features = ["rust-bert"]
monitoring = []
