apiVersion: v2
name: infinitium-signal
description: Enterprise Cyber-Compliance Platform with SBOM/HBOM scanning, vulnerability analysis, and blockchain audit trails
type: application
version: 0.1.0
appVersion: "0.1.0"

keywords:
  - cybersecurity
  - compliance
  - sbom
  - vulnerability
  - blockchain
  - audit
  - scanning

home: https://github.com/tanm-sys/infinitium-signal
sources:
  - https://github.com/tanm-sys/infinitium-signal

maintainers:
  - name: <PERSON><PERSON><PERSON>
    email: <EMAIL>
    url: https://github.com/tanm-sys

annotations:
  category: Security
  licenses: Proprietary
  images: |
    - name: infinitium-signal
      image: ghcr.io/infinitium-signal/infinitium-signal:0.1.0
    - name: postgresql
      image: postgres:14
    - name: redis
      image: redis:7-alpine

dependencies:
  - name: postgresql
    version: "15.2.0"
    repository: https://charts.bitnami.com/bitnami
    condition: postgresql.enabled
  - name: redis
    version: "19.0.2"
    repository: https://charts.bitnami.com/bitnami
    condition: redis.enabled
  - name: prometheus
    version: "25.8.0"
    repository: https://prometheus-community.github.io/helm-charts
    condition: monitoring.prometheus.enabled
  - name: grafana
    version: "8.3.6"
    repository: https://grafana.github.io/helm-charts
    condition: monitoring.grafana.enabled
