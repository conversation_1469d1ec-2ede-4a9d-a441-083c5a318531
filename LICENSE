INFINITIUM SIGNAL PROPRIETARY LICENSE AGREEMENT

Copyright © 2024-2025 Tanmay Patil. All Rights Reserved.

IMPORTANT NOTICE: This is a proprietary software license agreement. By accessing, installing, copying, or otherwise using the Infinitum Signal software ("Software"), you acknowledge that you have read, understood, and agree to be bound by the terms and conditions of this Agreement. If you do not agree to these terms, you must immediately cease all use of the Software and destroy all copies in your possession.

This Agreement is entered into between Tan<PERSON>y Patil ("Licensor" or "Owner"), the sole owner, copyright holder, and proprietor of the Software, and any individual or entity ("Licensee") who accesses or uses the Software.

1. DEFINITIONS

1.1 "Software" means the Infinitum Signal enterprise cybersecurity platform, including all source code, object code, executables, libraries, documentation, updates, patches, and any derivative works thereof, developed using technologies such as Rust, Tokio, RustCrypto, Rayon, Redis, and other proprietary components.

1.2 "Intellectual Property" means all copyrights, patents, trademarks, trade secrets, know-how, and any other proprietary rights in the Software, including but not limited to algorithms, data structures, security protocols, vulnerability detection methods, compliance reporting mechanisms, and blockchain-based audit trails.

1.3 "Derivative Works" means any modification, adaptation, translation, enhancement, improvement, or other alteration of the Software, including any work based upon the Software or any portion thereof.

1.4 "Authorized Use" means use of the Software solely by the Licensor for internal purposes, or use expressly authorized in writing by the Licensor.

2. SOLE OWNERSHIP AND EXCLUSIVE RIGHTS

2.1 The Software is the exclusive property of Tanmay Patil. All right, title, and interest in and to the Software, including all Intellectual Property rights therein, are owned solely and exclusively by the Licensor.

2.2 The Licensor retains all rights not expressly granted herein. This Agreement does not convey any rights by implication, estoppel, or otherwise.

2.3 The Licensor is the sole and exclusive owner of all copyrights, patents, trade secrets, and other intellectual property rights in the Software. No rights are granted to the Licensee except as explicitly stated herein.

3. LICENSE GRANT

3.1 Subject to the terms and conditions of this Agreement, the Licensor may, at their sole discretion, grant a limited, revocable, non-exclusive, non-transferable license to use the Software for specific purposes authorized by the Licensor in writing.

3.2 No license is granted by default. Any use of the Software without express written authorization from the Licensor constitutes a material breach of this Agreement.

3.3 The Licensor reserves the right to modify, suspend, or terminate any granted license at any time, for any reason, without notice or liability.

4. RESTRICTIONS

Licensee shall not, directly or indirectly:

4.1 Use, copy, modify, distribute, sublicense, rent, lease, loan, or transfer the Software or any portion thereof to any third party without the Licensor's express written consent.

4.2 Reverse engineer, decompile, disassemble, or attempt to derive the source code, algorithms, or underlying technology of the Software.

4.3 Create Derivative Works based on the Software, including but not limited to modifications, enhancements, or integrations with other software.

4.4 Use the Software for commercial exploitation, resale, or distribution, including as a service, platform, or component of another product.

4.5 Access, analyze, or attempt to access the Software's source code, internal workings, or proprietary algorithms.

4.6 Benchmark, test, or evaluate the Software's performance against competing products without the Licensor's written consent.

4.7 Remove, alter, or obscure any copyright notices, proprietary legends, or other markings on the Software.

4.8 Use the Software in violation of applicable laws, regulations, or this Agreement.

4.9 Attempt to circumvent any technical protection measures, encryption, or access controls implemented in the Software.

4.10 Develop or distribute competing cybersecurity products or services that incorporate concepts, methods, or technologies derived from the Software.

5. INTELLECTUAL PROPERTY PROTECTION

5.1 The Software contains valuable trade secrets and proprietary information. Licensee agrees to maintain the confidentiality of all such information and not disclose it to any third party.

5.2 All Intellectual Property rights, including but not limited to copyrights under the Berne Convention, patents, trademarks, and trade secrets, are reserved exclusively to the Licensor.

5.3 The Software incorporates proprietary cybersecurity technologies, including but not limited to:
   - SBOM/HBOM scanning and analysis algorithms
   - Vulnerability detection and risk assessment methodologies
   - Blockchain-based verifiable audit trails
   - Compliance reporting for CERT-In, SEBI, and other regulatory frameworks
   - Integration with technologies such as Tokio, RustCrypto, Rayon, and Redis

5.4 Any unauthorized use, disclosure, or exploitation of these technologies constitutes theft of intellectual property.

6. CONFIDENTIALITY

6.1 The Software and all related documentation contain confidential and proprietary information. Licensee agrees to treat the Software as confidential and not disclose it to any third party.

6.2 Confidentiality obligations survive the termination of this Agreement.

7. WARRANTIES AND DISCLAIMERS

7.1 THE SOFTWARE IS PROVIDED "AS IS" WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED.

7.2 LICENSOR DISCLAIMS ALL WARRANTIES, INCLUDING BUT NOT LIMITED TO:
   - MERCHANTABILITY
   - FITNESS FOR A PARTICULAR PURPOSE
   - NON-INFRINGEMENT
   - SECURITY OR RELIABILITY
   - ACCURACY OR COMPLETENESS OF RESULTS

7.3 LICENSOR DOES NOT WARRANT THAT THE SOFTWARE WILL MEET LICENSEE'S REQUIREMENTS OR THAT OPERATION WILL BE UNINTERRUPTED OR ERROR-FREE.

8. LIMITATION OF LIABILITY

8.1 IN NO EVENT SHALL LICENSOR BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES, INCLUDING BUT NOT LIMITED TO LOSS OF PROFITS, DATA, USE, OR OTHER INTANGIBLE LOSSES.

8.2 LICENSOR'S TOTAL LIABILITY UNDER THIS AGREEMENT SHALL NOT EXCEED THE AMOUNT OF ONE UNITED STATES DOLLAR ($1.00).

8.3 Licensee assumes all risks associated with the use of the Software.

9. INDEMNIFICATION

9.1 Licensee agrees to indemnify, defend, and hold harmless the Licensor, its affiliates, and their respective officers, directors, employees, and agents from and against any and all claims, liabilities, damages, losses, costs, and expenses (including reasonable attorneys' fees) arising out of or related to:
   - Licensee's use of the Software
   - Licensee's breach of this Agreement
   - Licensee's violation of applicable laws or regulations
   - Any third-party claims related to Licensee's actions

10. TERMINATION

10.1 This Agreement is effective until terminated. The Licensor may terminate this Agreement at any time, for any reason, with or without cause.

10.2 Upon termination, Licensee must immediately cease all use of the Software and destroy all copies, including backups and derivatives.

10.3 Termination does not relieve Licensee of obligations incurred prior to termination.

11. GOVERNING LAW AND JURISDICTION

11.1 This Agreement shall be governed by and construed in accordance with the laws of India, without regard to conflict of laws principles.

11.2 Any disputes arising under this Agreement shall be subject to the exclusive jurisdiction of the courts of India.

11.3 This Agreement incorporates international copyright conventions, including the Berne Convention for the Protection of Literary and Artistic Works, and complies with software licensing standards under applicable international law.

12. DISPUTE RESOLUTION

12.1 Any disputes shall first be resolved through good faith negotiations between the parties.

12.2 If negotiations fail, disputes shall be resolved through binding arbitration in accordance with the Arbitration and Conciliation Act, 1996, as amended.

12.3 The arbitration shall be conducted in English and held in India.

13. MISCELLANEOUS

13.1 This Agreement constitutes the entire agreement between the parties and supersedes all prior agreements, understandings, or representations.

13.2 Any amendments must be in writing and signed by both parties.

13.3 If any provision is held invalid, the remaining provisions shall remain in effect.

13.4 Licensee may not assign this Agreement without the Licensor's written consent.

13.5 The Licensor's failure to enforce any provision does not constitute a waiver.

13.6 This Agreement is binding upon and inures to the benefit of the parties and their successors and assigns.

14. EXPORT CONTROL

14.1 The Software may be subject to export control laws and regulations. Licensee agrees to comply with all applicable export and import laws.

15. SEVERABILITY AND ENFORCEABILITY

15.1 Each provision of this Agreement is severable. If any provision is held invalid or unenforceable, the remaining provisions shall continue in full force and effect.

15.2 This Agreement is intended to be enforceable to the maximum extent permitted by applicable law.

16. ACKNOWLEDGMENT

By accessing or using the Software, Licensee acknowledges that they have read, understood, and agree to be bound by this Agreement. Licensee further acknowledges that this Agreement grants no rights except as expressly stated herein, and that all rights are reserved exclusively to the Licensor.

CONTACT INFORMATION

For questions regarding this Agreement or to request authorization for use:

Tanmay Patil
Email: <EMAIL>

Last Updated: September 1, 2025

VIOLATION OF THIS AGREEMENT MAY RESULT IN CIVIL AND CRIMINAL PENALTIES UNDER APPLICABLE LAWS, INCLUDING BUT NOT LIMITED TO INTELLECTUAL PROPERTY INFRINGEMENT CLAIMS.
