use criterion::{black_box, criterion_group, criterion_main, BenchmarkId, Criterion, Throughput};
use infinitium_signal::scanners::{ScanResult, ScanRequest, ScanStatus, ScanType, ScanOptions, OutputFormat, SoftwareComponent};
use infinitium_signal::config::Config;
use serde_json;
use sha2::{Digest, Sha256};
use std::collections::HashMap;
use std::time::Duration;
use tempfile::TempDir;
use uuid::Uuid;
use chrono::Utc;

/// Benchmark JSON serialization performance
fn bench_json_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("json_operations");
    group.measurement_time(Duration::from_secs(8));

    // Test different data sizes
    let data_sizes = vec![
        ("small", 10),   // 10 components
        ("medium", 100), // 100 components
        ("large", 1000), // 1000 components
    ];

    for (size_name, component_count) in data_sizes {
        group.throughput(Throughput::Elements(component_count as u64));
        group.bench_with_input(
            BenchmarkId::new("serialize_components", size_name),
            &component_count,
            |b, &component_count| {
                b.iter(|| {
                    let components = create_test_json_data(component_count);
                    let serialized = serde_json::to_string(&components).unwrap();
                    black_box(serialized)
                });
            },
        );
    }

    group.finish();
}

/// Benchmark hash computation performance
fn bench_hash_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("hash_operations");
    group.measurement_time(Duration::from_secs(8));

    // Test different data sizes
    let data_sizes = vec![
        ("1kb", 1024),
        ("10kb", 10 * 1024),
        ("100kb", 100 * 1024),
        ("1mb", 1024 * 1024),
    ];

    for (size_name, size_bytes) in data_sizes {
        group.throughput(Throughput::Bytes(size_bytes as u64));
        group.bench_with_input(
            BenchmarkId::new("sha256_hash", size_name),
            &size_bytes,
            |b, &size_bytes| {
                b.iter(|| {
                    let data = create_test_data(size_bytes);
                    let hash = Sha256::digest(&data);
                    black_box(hash)
                });
            },
        );
    }

    group.finish();
}

/// Benchmark data serialization operations
fn bench_data_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("data_operations");
    group.measurement_time(Duration::from_secs(8));

    // Simulate database operations without actual DB connection
    group.bench_function("serialize_scan_result", |b| {
        b.iter(|| {
            let scan_result = create_test_scan_result();
            let serialized = serde_json::to_string(&scan_result).unwrap();
            black_box(serialized)
        });
    });

    group.bench_function("deserialize_scan_result", |b| {
        b.iter(|| {
            let scan_result = create_test_scan_result();
            let serialized = serde_json::to_string(&scan_result).unwrap();
            let deserialized: ScanResult = serde_json::from_str(&serialized).unwrap();
            black_box(deserialized)
        });
    });

    group.bench_function("process_scan_metadata", |b| {
        b.iter(|| {
            let scan_result = create_test_scan_result();
            let metadata = format!("{:?}_{:?}", scan_result.scan_type, scan_result.status);
            black_box(metadata)
        });
    });

    group.finish();
}

/// Benchmark cryptographic operations
fn bench_crypto_operations(c: &mut Criterion) {
    let config = Config::default();
    let blockchain = BlockchainOrchestrator::new(config.blockchain.clone());

    let mut group = c.benchmark_group("crypto_operations");
    group.measurement_time(Duration::from_secs(8));

    group.bench_function("hash_generation", |b| {
        b.iter(|| {
            let data = create_test_blockchain_data();
            let hash = Sha256::digest(&data);
            black_box(hash)
        });
    });

    group.bench_function("data_processing", |b| {
        b.iter(|| {
            let data = create_test_blockchain_data();
            // Simulate blockchain processing
            let processed = data.len() + 42;
            black_box(processed)
        });
    });

    group.finish();
}

/// Benchmark compliance report generation
fn bench_compliance_reports(c: &mut Criterion) {
    let config = Config::default();
    let compliance_orchestrator = ComplianceOrchestrator::new(config.compliance.clone());

    let mut group = c.benchmark_group("compliance_reports");
    group.measurement_time(Duration::from_secs(10));

    group.bench_function("compliance_data_processing", |b| {
        b.iter(|| {
            let scan_data = create_test_compliance_data();
            // Simulate compliance processing
            let processed = scan_data.as_object().map(|o| o.len()).unwrap_or(0);
            black_box(processed)
        });
    });

    group.bench_function("compliance_serialization", |b| {
        b.iter(|| {
            let scan_data = create_test_compliance_data();
            let serialized = serde_json::to_string(&scan_data).unwrap();
            black_box(serialized)
        });
    });

    group.finish();
}

/// Benchmark SBOM scanning performance
fn bench_sbom_scanning(c: &mut Criterion) {
    let config = Config::default();
    let scanner = infinitium_signal::scanners::sbom_scanner::SbomScanner::new(&config.scanning);

    let mut group = c.benchmark_group("sbom_scanning");
    group.measurement_time(Duration::from_secs(10));

    // Create test projects of different sizes
    let project_sizes = vec![
        ("small", 5),   // 5 files
        ("medium", 20), // 20 files
        ("large", 50),  // 50 files
    ];

    for (size_name, file_count) in project_sizes {
        let temp_project = create_test_project(file_count);

        group.bench_with_input(
            BenchmarkId::new("project_scan", size_name),
            &temp_project,
            |b, temp_project| {
                b.iter(|| {
                    let rt = tokio::runtime::Runtime::new().unwrap();
                    let result = rt.block_on(scanner.scan_project(temp_project.path())).unwrap();
                    black_box(result)
                });
            },
        );
    }

    group.finish();
}

/// Benchmark vulnerability analysis performance
fn bench_vulnerability_analysis(c: &mut Criterion) {
    let mut group = c.benchmark_group("vulnerability_analysis");
    group.measurement_time(Duration::from_secs(8));

    // Test different vulnerability database sizes
    let vuln_counts = vec![
        ("small", 100),   // 100 vulnerabilities
        ("medium", 1000), // 1000 vulnerabilities
        ("large", 10000), // 10000 vulnerabilities
    ];

    for (size_name, vuln_count) in vuln_counts {
        group.throughput(Throughput::Elements(vuln_count as u64));
        group.bench_with_input(
            BenchmarkId::new("vulnerability_matching", size_name),
            &vuln_count,
            |b, &vuln_count| {
                b.iter(|| {
                    let vulnerabilities = create_test_vulnerabilities(vuln_count);
                    let matched = vulnerabilities.into_iter().filter(|v| v.severity == "HIGH").count();
                    black_box(matched)
                });
            },
        );
    }

    group.finish();
}

/// Benchmark hardware scanning (HBOM)
fn bench_hbom_scanning(c: &mut Criterion) {
    let mut group = c.benchmark_group("hbom_scanning");
    group.measurement_time(Duration::from_secs(10));

    let firmware_sizes = vec![
        ("small", 1024 * 1024),      // 1MB
        ("medium", 2 * 1024 * 1024), // 2MB
        ("large", 5 * 1024 * 1024),  // 5MB
    ];

    for (size_name, size_bytes) in firmware_sizes {
        group.throughput(Throughput::Bytes(size_bytes as u64));
        group.bench_with_input(
            BenchmarkId::new("firmware_analysis", size_name),
            &size_bytes,
            |b, &size_bytes| {
                b.iter(|| {
                    let firmware_data = create_test_firmware(size_bytes);
                    // Simulate firmware analysis
                    let analysis_result = firmware_data.len() / 1024; // KB processed
                    black_box(analysis_result)
                });
            },
        );
    }

    group.finish();
}

// Helper functions for creating test data
fn create_test_project(file_count: usize) -> TempDir {
    let temp_dir = TempDir::new().unwrap();

    // Create Cargo.toml
    std::fs::write(
        temp_dir.path().join("Cargo.toml"),
        format!(
            r#"
[package]
name = "test-project"
version = "0.1.0"
edition = "2021"

[dependencies]
serde = "1.0"
tokio = "1.0"
"#
        ),
    )
    .unwrap();

    // Create src directory and files
    let src_dir = temp_dir.path().join("src");
    std::fs::create_dir(&src_dir).unwrap();

    for i in 0..file_count {
        let file_content = format!(
            r#"
pub fn function_{}() {{
    println!("Function {}", {});
}}
"#,
            i, i, i
        );
        std::fs::write(src_dir.join(format!("module_{}.rs", i)), file_content).unwrap();
    }

    // Create main.rs
    let main_content = (0..file_count)
        .map(|i| format!("mod module_{};", i))
        .collect::<Vec<_>>()
        .join("\n");
    std::fs::write(src_dir.join("main.rs"), main_content).unwrap();

    temp_dir
}

fn create_test_components(component_count: usize) -> Vec<SoftwareComponent> {
    (0..component_count)
        .map(|i| {
            let mut metadata = HashMap::new();
            metadata.insert(
                "test_id".to_string(),
                serde_json::Value::String(format!("component-{}", i)),
            );
            metadata.insert(
                "version".to_string(),
                serde_json::Value::String(format!("1.{}.0", i)),
            );

            SoftwareComponent {
                name: format!("component-{}", i),
                version: format!("1.{}.0", i),
                license: Some("MIT".to_string()),
                description: Some(format!("Test component {}", i)),
                homepage: None,
                repository: None,
                dependencies: vec![],
                package_type: Some("cargo".to_string()),
                package_manager: Some("cargo".to_string()),
                file_path: None,
                metadata,
            }
        })
        .collect()
}

fn create_test_scan_result() -> ScanResult {
    let mut metadata = HashMap::new();
    metadata.insert(
        "test_key".to_string(),
        serde_json::Value::String("test_value".to_string()),
    );

    let scan_request = ScanRequest {
        id: Uuid::new_v4(),
        scan_type: ScanType::Sbom,
        target: "/test/project".to_string(),
        options: ScanOptions {
            include_dev_dependencies: true,
            max_depth: 10,
            timeout: 300,
            include_transitive: true,
            exclude_patterns: vec![],
            include_patterns: vec![],
            output_formats: vec![OutputFormat::Json],
            enable_vulnerability_scan: true,
            enable_license_scan: true,
        },
        metadata: HashMap::new(),
    };

    ScanResult {
        request: scan_request,
        status: ScanStatus::Completed,
        started_at: Utc::now(),
        completed_at: Some(Utc::now()),
        duration: Some(Duration::from_secs(30)),
        software_components: create_test_components(10),
        hardware_components: vec![],
        repository_info: None,
        dependency_tree: None,
        vulnerabilities: vec![],
        licenses: vec![],
        issues: vec![],
        metadata,
    }
}

fn create_test_blockchain_data() -> Vec<u8> {
    serde_json::to_vec(&serde_json::json!({
        "scan_id": "test-scan-123",
        "timestamp": Utc::now(),
        "hash": "abc123def456",
        "components": 50,
        "record_type": "scan_result",
        "organization": "Test Corp"
    }))
    .unwrap()
}

fn create_test_compliance_data() -> serde_json::Value {
    serde_json::json!({
        "organization": "Test Corp",
        "scan_results": [
            {"type": "sbom", "components": 100, "vulnerabilities": 5},
            {"type": "hbom", "firmware_files": 10, "security_issues": 2}
        ],
        "compliance_framework": "cert-in"
    })
}

fn create_test_firmware(size_bytes: usize) -> Vec<u8> {
    // Create mock firmware data with realistic patterns
    let mut firmware = Vec::with_capacity(size_bytes);

    // Add ELF header-like pattern
    firmware.extend_from_slice(b"\x7fELF");

    // Fill with pseudo-random data
    for i in 0..((size_bytes - 4) / 4) {
        firmware.extend_from_slice(&(i as u32).to_le_bytes());
    }

    // Pad to exact size
    firmware.resize(size_bytes, 0);
    firmware
}

fn create_test_vulnerabilities(count: usize) -> Vec<infinitium_signal::vulnerability::VulnerabilityInfo> {
    (0..count)
        .map(|i| {
            infinitium_signal::vulnerability::VulnerabilityInfo {
                cve_id: format!("CVE-2023-{:04}", i),
                severity: if i % 10 == 0 { "CRITICAL" } else if i % 5 == 0 { "HIGH" } else { "MEDIUM" }.to_string(),
                cvss_score: Some((i % 10) as f64),
                description: format!("Test vulnerability {}", i),
                affected_component: format!("component-{}", i),
                fixed_version: Some(format!("1.{}.1", i)),
                references: vec![format!("https://example.com/cve-{}", i)],
            }
        })
        .collect()
}

criterion_group!(
    benches,
    bench_sbom_scanning,
    bench_vulnerability_analysis,
    bench_data_operations,
    bench_crypto_operations,
    bench_compliance_reports,
    bench_hbom_scanning
);
criterion_main!(benches);
