use criterion::{black_box, criterion_group, criterion_main, BenchmarkId, Criterion, Throughput};
use serde_json;
use sha2::{Digest, Sha256};
use std::collections::HashMap;
use std::time::Duration;

/// Benchmark JSON serialization performance
fn bench_json_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("json_operations");
    group.measurement_time(Duration::from_secs(8));

    // Test different data sizes
    let data_sizes = vec![
        ("small", 10),   // 10 components
        ("medium", 100), // 100 components
        ("large", 1000), // 1000 components
    ];

    for (size_name, component_count) in data_sizes {
        group.throughput(Throughput::Elements(component_count as u64));
        group.bench_with_input(
            BenchmarkId::new("serialize_components", size_name),
            &component_count,
            |b, &component_count| {
                b.iter(|| {
                    let components = create_test_json_data(component_count);
                    let serialized = serde_json::to_string(&components).unwrap();
                    black_box(serialized)
                });
            },
        );
    }

    group.finish();
}

/// Benchmark hash computation performance
fn bench_hash_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("hash_operations");
    group.measurement_time(Duration::from_secs(8));

    // Test different data sizes
    let data_sizes = vec![
        ("1kb", 1024),
        ("10kb", 10 * 1024),
        ("100kb", 100 * 1024),
        ("1mb", 1024 * 1024),
    ];

    for (size_name, size_bytes) in data_sizes {
        group.throughput(Throughput::Bytes(size_bytes as u64));
        group.bench_with_input(
            BenchmarkId::new("sha256_hash", size_name),
            &size_bytes,
            |b, &size_bytes| {
                b.iter(|| {
                    let data = create_test_data(size_bytes);
                    let hash = Sha256::digest(&data);
                    black_box(hash)
                });
            },
        );
    }

    group.finish();
}

/// Benchmark string processing operations
fn bench_string_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("string_operations");
    group.measurement_time(Duration::from_secs(6));

    group.bench_function("string_concatenation", |b| {
        b.iter(|| {
            let mut result = String::new();
            for i in 0..1000 {
                result.push_str(&format!("component-{}", i));
            }
            black_box(result)
        });
    });

    group.bench_function("string_parsing", |b| {
        b.iter(|| {
            let test_string = "pkg:cargo/serde@1.0.0";
            let parts: Vec<&str> = test_string.split('@').collect();
            black_box(parts)
        });
    });

    group.bench_function("regex_matching", |b| {
        b.iter(|| {
            let test_strings = vec![
                "pkg:cargo/serde@1.0.0",
                "pkg:npm/lodash@4.17.21",
                "pkg:pypi/requests@2.25.1",
            ];
            let results: Vec<bool> = test_strings.iter().map(|s| s.contains("@")).collect();
            black_box(results)
        });
    });

    group.finish();
}

/// Benchmark collection operations
fn bench_collection_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("collection_operations");
    group.measurement_time(Duration::from_secs(6));

    group.bench_function("hashmap_operations", |b| {
        b.iter(|| {
            let mut map = HashMap::new();
            for i in 0..1000 {
                map.insert(format!("key-{}", i), format!("value-{}", i));
            }

            let mut sum = 0;
            for i in 0..1000 {
                if map.contains_key(&format!("key-{}", i)) {
                    sum += 1;
                }
            }
            black_box(sum)
        });
    });

    group.bench_function("vector_operations", |b| {
        b.iter(|| {
            let mut vec = Vec::new();
            for i in 0..1000 {
                vec.push(format!("item-{}", i));
            }

            vec.sort();
            let filtered: Vec<String> = vec.iter().filter(|s| s.contains("5")).cloned().collect();
            black_box(filtered)
        });
    });

    group.finish();
}

/// Benchmark file I/O simulation
fn bench_io_simulation(c: &mut Criterion) {
    let mut group = c.benchmark_group("io_simulation");
    group.measurement_time(Duration::from_secs(6));

    group.bench_function("memory_allocation", |b| {
        b.iter(|| {
            let data: Vec<u8> = (0..10000).map(|i| (i % 256) as u8).collect();
            black_box(data)
        });
    });

    group.bench_function("data_transformation", |b| {
        b.iter(|| {
            let data: Vec<u8> = (0..1000).map(|i| (i % 256) as u8).collect();
            let transformed: Vec<u8> = data.iter().map(|&x| x.wrapping_mul(2)).collect();
            black_box(transformed)
        });
    });

    group.finish();
}

// Helper functions for creating test data
fn create_test_json_data(component_count: usize) -> serde_json::Value {
    let components: Vec<serde_json::Value> = (0..component_count)
        .map(|i| {
            serde_json::json!({
                "name": format!("component-{}", i),
                "version": format!("1.{}.0", i),
                "license": "MIT",
                "description": format!("Test component {}", i),
                "dependencies": [],
                "metadata": {
                    "test_id": i,
                    "category": "library"
                }
            })
        })
        .collect();

    serde_json::json!({
        "bomFormat": "CycloneDX",
        "specVersion": "1.4",
        "components": components
    })
}

fn create_test_data(size_bytes: usize) -> Vec<u8> {
    (0..size_bytes).map(|i| (i % 256) as u8).collect()
}

criterion_group!(
    benches,
    bench_json_operations,
    bench_hash_operations,
    bench_string_operations,
    bench_collection_operations,
    bench_io_simulation
);
criterion_main!(benches);
