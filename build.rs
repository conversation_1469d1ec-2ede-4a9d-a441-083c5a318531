use std::process::Command;

fn main() {
    // Get build timestamp
    let output = Command::new("date")
        .args(["-u", "+%Y-%m-%d %H:%M:%S UTC"])
        .output()
        .expect("Failed to get build timestamp");

    let timestamp = String::from_utf8_lossy(&output.stdout).trim().to_string();
    println!("cargo:rustc-env=VERGEN_BUILD_TIMESTAMP={}", timestamp);

    // Get Git SHA
    let git_sha = if let Ok(output) = Command::new("git").args(["rev-parse", "HEAD"]).output() {
        String::from_utf8_lossy(&output.stdout).trim().to_string()
    } else {
        "unknown".to_string()
    };
    println!("cargo:rustc-env=VERGEN_GIT_SHA={}", git_sha);

    // Get Rust version
    let rust_version = if let Ok(output) = Command::new("rustc").args(["--version"]).output() {
        let version_str = String::from_utf8_lossy(&output.stdout);
        version_str
            .split_whitespace()
            .nth(1)
            .unwrap_or("unknown")
            .to_string()
    } else {
        "unknown".to_string()
    };
    println!("cargo:rustc-env=VERGEN_RUSTC_SEMVER={}", rust_version);

    // Get target triple
    let target_triple = std::env::var("TARGET").unwrap_or_else(|_| "unknown".to_string());
    println!(
        "cargo:rustc-env=VERGEN_CARGO_TARGET_TRIPLE={}",
        target_triple
    );

    // Re-run if git changes
    println!("cargo:rerun-if-changed=.git/HEAD");
    println!("cargo:rerun-if-changed=.git/refs/heads/");
}
