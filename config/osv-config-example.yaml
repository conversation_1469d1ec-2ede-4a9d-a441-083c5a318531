# OSV Scanner Configuration Example
# This file demonstrates how to configure OSV scanner settings for optimal performance and accuracy

scanning:
  timeout: 300
  max_concurrent: 5
  temp_dir: "/tmp/infinitum-scans"
  cleanup_interval: 3600
  max_file_size: 104857600  # 100MB
  supported_languages:
    - "rust"
    - "javascript"
    - "python"
    - "java"
    - "go"
    - "c"
    - "cpp"

  # OSV Scanner specific configuration
  osv_scanner:
    api_url: "https://api.osv.dev"
    timeout_seconds: 30
    max_concurrent_requests: 10
    cache_ttl_seconds: 3600
    enable_caching: true
    batch_size: 50

    # Circuit breaker configuration
    circuit_breaker:
      service_name: "osv_api"
      failure_threshold: 5
      recovery_timeout_seconds: 60
      success_threshold: 3
      timeout_seconds: 30
      monitoring_window_seconds: 300
      enable_auto_recovery: true
      max_concurrent_requests: 10

    # Performance optimization settings
    performance_config:
      enable_incremental_scanning: true
      enable_parallel_processing: true
      max_parallel_requests: 10
      memory_limit_mb: 512
      rate_limit_per_second: 50
      enable_connection_pooling: true
      connection_pool_size: 20
      request_timeout_seconds: 10

    # False positive filtering configuration
    false_positive_filters:
      enabled: true
      # Allowlisted packages (format: "package:version" or just "package")
      allowlist:
        - "lodash:*"  # Allow all versions of lodash
        - "express:4.*"  # Allow express version 4.x
        - "react:17.0.0"  # Allow specific version
      # Minimum severity threshold (low, medium, high, critical)
      min_severity: "low"
      # Maximum CVSS score threshold (0.0-10.0, null = no limit)
      max_cvss_score: null
      # Enable version validation
      enable_version_validation: true
      # Skip vulnerabilities without fixed versions
      skip_unfixed_vulnerabilities: false
      # Skip vulnerabilities older than specified days (null = no limit)
      skip_vulnerabilities_older_than_days: null

# Compliance configuration
compliance:
  frameworks:
    - "cert-in"
    - "sebi"
    - "iso27001"
    - "owasp"
    - "nist"
  report_output_dir: "/var/lib/infinitum-signal/reports"
  report_retention_days: 90
  auto_cleanup: true
  template_dir: "templates"

# Vulnerability configuration
vulnerability:
  enabled: true
  severity_threshold: "medium"
  auto_update: true
  update_interval: 3600
  sources:
    - "nvd"
    - "github"
    - "osv"

# External services configuration
external_services:
  osv:
    api_url: "https://api.osv.dev"
    timeout: 30

# Performance tuning examples:

# High-performance configuration (for large-scale scanning)
# performance_config:
#   enable_incremental_scanning: true
#   enable_parallel_processing: true
#   max_parallel_requests: 20
#   memory_limit_mb: 1024
#   rate_limit_per_second: 100
#   enable_connection_pooling: true
#   connection_pool_size: 50
#   request_timeout_seconds: 5

# Conservative configuration (for resource-constrained environments)
# performance_config:
#   enable_incremental_scanning: true
#   enable_parallel_processing: false
#   max_parallel_requests: 2
#   memory_limit_mb: 256
#   rate_limit_per_second: 10
#   enable_connection_pooling: true
#   connection_pool_size: 5
#   request_timeout_seconds: 30

# Aggressive false positive filtering (for high accuracy)
# false_positive_filters:
#   enabled: true
#   allowlist:
#     - "lodash:*"
#     - "express:*"
#     - "react:*"
#   min_severity: "medium"
#   max_cvss_score: 7.0
#   enable_version_validation: true
#   skip_unfixed_vulnerabilities: true
#   skip_vulnerabilities_older_than_days: 365

# Permissive false positive filtering (for comprehensive scanning)
# false_positive_filters:
#   enabled: false
#   allowlist: []
#   min_severity: "low"
#   max_cvss_score: null
#   enable_version_validation: false
#   skip_unfixed_vulnerabilities: false
#   skip_vulnerabilities_older_than_days: null