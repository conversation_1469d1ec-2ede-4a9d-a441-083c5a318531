# Multi-stage Dockerfile for Infinitium Signal
# Production-ready container with security hardening

# Build stage
FROM rust:1.80-slim as builder

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libpq-dev \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN useradd -m -u 1001 appuser

# Set working directory
WORKDIR /app

# Copy dependency files first for better caching
COPY Cargo.toml Cargo.lock ./
COPY src/lib.rs src/

# Build dependencies (this layer will be cached)
RUN cargo build --release --lib
RUN rm src/lib.rs

# Copy source code
COPY src/ src/
COPY build.rs ./

# Build the application
RUN cargo build --release

# Runtime stage
FROM debian:bookworm-slim

# Add security labels
LABEL org.opencontainers.image.title="Infinitium Signal"
LABEL org.opencontainers.image.description="Enterprise Cyber-Compliance Platform"
LABEL org.opencontainers.image.vendor="Infinitium Signal"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.source="https://github.com/infinitium-signal/infinitium-signal"

# Install runtime dependencies and security tools
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    libpq5 \
    curl \
    wget \
    jq \
    wkhtmltopdf \
    xvfb \
    && rm -rf /var/lib/apt/lists/*

# Install security scanning tools
RUN curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin \
    && curl -sSfL https://raw.githubusercontent.com/anchore/syft/main/install.sh | sh -s -- -b /usr/local/bin \
    && curl -sSfL https://raw.githubusercontent.com/anchore/grype/main/install.sh | sh -s -- -b /usr/local/bin

# Create app user and directories with security hardening
RUN useradd -m -u 1001 -s /bin/bash -d /app appuser \
    && groupadd -g 1001 appgroup \
    && usermod -a -G appgroup appuser \
    && mkdir -p /app/data /app/logs /app/config /app/output \
    && chown -R appuser:appgroup /app \
    && chmod -R 755 /app \
    && chmod 700 /app/data /app/logs

# Copy binary from builder stage
COPY --from=builder /app/target/release/infinitium-signal /usr/local/bin/infinitium-signal
COPY --from=builder /app/target/release/infinitium-signal-cli /usr/local/bin/infinitium-signal-cli

# Copy configuration files
COPY docker/docker-entrypoint.sh /usr/local/bin/
COPY .env.example /app/config/
COPY monitoring/ /app/monitoring/
COPY logging/ /app/logging/

# Set permissions
RUN chmod +x /usr/local/bin/docker-entrypoint.sh \
    && chmod +x /usr/local/bin/infinitium-signal \
    && chmod +x /usr/local/bin/infinitium-signal-cli

# Security hardening
RUN apt-get update && apt-get install -y --no-install-recommends \
    dumb-init \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Switch to non-root user
USER appuser

# Set working directory
WORKDIR /app

# Environment variables
ENV RUST_LOG=info
ENV RUST_BACKTRACE=1
ENV APP_ENV=production
ENV DATABASE_URL=postgresql://infinitium_user:infinitium_pass@localhost:5432/infinitium_signal
ENV REDIS_URL=redis://localhost:6379
ENV API_HOST=0.0.0.0
ENV API_PORT=8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Expose ports
EXPOSE 8080 9090

# Use dumb-init for proper signal handling
ENTRYPOINT ["/usr/bin/dumb-init", "--"]

# Default command
CMD ["/usr/local/bin/docker-entrypoint.sh"]
