# Development Dockerfile for Infinitium Signal
# This Dockerfile is optimized for development with hot reloading and debugging capabilities

FROM rust:1.80-slim-bullseye

# Add security labels
LABEL org.opencontainers.image.title="Infinitium Signal (Development)"
LABEL org.opencontainers.image.description="Development container for Infinitium Signal cybersecurity platform"
LABEL org.opencontainers.image.vendor="Infinitium Signal"
LABEL org.opencontainers.image.version="dev"

# Set environment variables
ENV RUST_LOG=debug
ENV RUST_BACKTRACE=1
ENV CARGO_TERM_COLOR=always
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libpq-dev \
    postgresql-client \
    redis-tools \
    curl \
    wget \
    git \
    build-essential \
    cmake \
    protobuf-compiler \
    libprotobuf-dev \
    ca-certificates \
    gnupg \
    lsb-release \
    && rm -rf /var/lib/apt/lists/*

# Install Docker CLI for container scanning
RUN curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null \
    && apt-get update \
    && apt-get install -y docker-ce-cli \
    && rm -rf /var/lib/apt/lists/*

# Install additional security tools for development
RUN cargo install cargo-audit cargo-deny cargo-outdated cargo-watch

# Install Trivy for vulnerability scanning
RUN wget -qO - https://aquasecurity.github.io/trivy-repo/deb/public.key | apt-key add - \
    && echo "deb https://aquasecurity.github.io/trivy-repo/deb $(lsb_release -sc) main" | tee -a /etc/apt/sources.list.d/trivy.list \
    && apt-get update \
    && apt-get install -y trivy \
    && rm -rf /var/lib/apt/lists/*

# Install Syft for SBOM generation
RUN curl -sSfL https://raw.githubusercontent.com/anchore/syft/main/install.sh | sh -s -- -b /usr/local/bin

# Install Node.js and npm for frontend development (if needed)
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# Create app user
RUN useradd -m -u 1000 app && \
    mkdir -p /app && \
    chown -R app:app /app

# Set working directory
WORKDIR /app

# Switch to app user
USER app

# Create directories for development
RUN mkdir -p \
    /app/src \
    /app/target \
    /app/logs \
    /app/storage \
    /app/reports \
    /app/temp

# Copy dependency files first for better caching
COPY --chown=app:app Cargo.toml Cargo.lock ./

# Create a dummy main.rs to build dependencies
RUN mkdir -p src && echo "fn main() {}" > src/main.rs

# Build dependencies (this layer will be cached)
RUN cargo build --release && rm -rf src target/release/deps/infinitium*

# Copy source code
COPY --chown=app:app src/ ./src/
COPY --chown=app:app tests/ ./tests/
COPY --chown=app:app .env.example ./.env

# Copy configuration files
COPY --chown=app:app monitoring/ ./monitoring/
COPY --chown=app:app logging/ ./logging/
COPY --chown=app:app scripts/ ./scripts/

# Make scripts executable
RUN chmod +x scripts/*.sh

# Build the application in debug mode for faster compilation
RUN cargo build

# Install cargo-watch for hot reloading
RUN cargo install cargo-watch

# Expose ports
EXPOSE 8080 9091

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Development entrypoint with hot reloading
CMD ["cargo", "watch", "-x", "run"]

# Alternative commands for different development scenarios:
# For running tests: docker run --rm -it infinitium-signal:dev cargo test
# For running with specific features: docker run --rm -it infinitium-signal:dev cargo run --features "feature1,feature2"
# For debugging: docker run --rm -it infinitium-signal:dev bash

# Development environment variables
ENV ENVIRONMENT=development
ENV LOG_LEVEL=debug
ENV RUST_LOG=infinitium_signal=debug,tower_http=debug
ENV DATABASE_URL=********************************************/infinitium_signal_dev
ENV REDIS_URL=redis://redis:6379/0

# Volume mounts for development
VOLUME ["/app/src", "/app/target", "/app/logs", "/app/storage"]

# Labels
LABEL maintainer="Infinitium Signal Team"
LABEL version="dev"
LABEL description="Development container for Infinitium Signal cybersecurity platform"
LABEL org.opencontainers.image.source="https://github.com/infinitium-signal/infinitium-signal"
LABEL org.opencontainers.image.documentation="https://docs.infinitium-signal.com"
LABEL org.opencontainers.image.licenses="MIT"
