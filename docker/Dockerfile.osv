# OSV Scanner Docker Image
# Provides a containerized environment for running OSV vulnerability scans

FROM rust:1.70-slim AS builder

# Install system dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    git \
    curl \
    jq \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy source code
COPY . .

# Build the application
RUN cargo build --release

# Runtime stage
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    curl \
    jq \
    git \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN useradd -r -s /bin/false osvscanner

# Copy binary from builder
COPY --from=builder /app/target/release/infinitum-signal /usr/local/bin/infinitum-signal

# Create directories
RUN mkdir -p /app/results /app/logs /app/config
RUN chown -R osvscanner:osvscanner /app

# Set working directory
WORKDIR /app

# Switch to non-root user
USER osvscanner

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD /usr/local/bin/infinitum-signal scan --help > /dev/null 2>&1 || exit 1

# Default command
CMD ["/usr/local/bin/infinitum-signal", "scan", ".", "--format", "json"]