version: '3.8'

services:
  osv-scanner:
    build:
      context: ..
      dockerfile: docker/Dockerfile.osv
    container_name: infinitum-osv-scanner
    volumes:
      - ../:/app/source:ro
      - ./results:/app/results
      - ./logs:/app/logs
    environment:
      - RUST_LOG=info
      - SCAN_FORMAT=json
      - SCAN_TIMEOUT=1800
    command: ["scan", "/app/source", "--format", "json"]
    networks:
      - osv-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "/usr/local/bin/infinitum-signal", "scan", "--help"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  osv-scheduler:
    image: alpine:latest
    container_name: osv-scheduler
    command: sh -c "while true; do echo 'Triggering OSV scan at $(date)'; curl -X POST http://osv-scanner:8080/scan; sleep 3600; done"
    depends_on:
      osv-scanner:
        condition: service_healthy
    networks:
      - osv-network
    restart: unless-stopped

  osv-webhook:
    build:
      context: ..
      dockerfile: docker/Dockerfile.osv
    container_name: osv-webhook-handler
    ports:
      - "8080:8080"
    environment:
      - WEBHOOK_SECRET=${WEBHOOK_SECRET}
      - ALLOWED_REPOS=${ALLOWED_REPOS}
      - NOTIFICATION_URL=${NOTIFICATION_URL}
    volumes:
      - ./webhook_results:/app/webhook_results
    command: ["webhook", "--port", "8080"]
    networks:
      - osv-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  osv-database:
    image: postgres:15-alpine
    container_name: osv-database
    environment:
      - POSTGRES_DB=osv_scanner
      - POSTGRES_USER=osv_user
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - osv_db_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - osv-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U osv_user -d osv_scanner"]
      interval: 30s
      timeout: 10s
      retries: 3

  osv-redis:
    image: redis:7-alpine
    container_name: osv-redis
    command: redis-server --appendonly yes
    volumes:
      - osv_redis_data:/data
    networks:
      - osv-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  osv-network:
    driver: bridge

volumes:
  osv_db_data:
    driver: local
  osv_redis_data:
    driver: local