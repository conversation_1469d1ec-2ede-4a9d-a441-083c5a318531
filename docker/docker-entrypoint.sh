#!/bin/bash
set -euo pipefail
IFS=$'\n\t'

# Infinitium Signal Docker Entrypoint Script
# Handles initialization, configuration, and service startup

# Colors for logging
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# Configuration
APP_NAME="infinitum-signal"
CONFIG_DIR="/app/config"
DATA_DIR="/app/data"
LOGS_DIR="/app/logs"
OUTPUT_DIR="/app/output"

# Default environment variables
export RUST_LOG="${RUST_LOG:-info}"
export RUST_BACKTRACE="${RUST_BACKTRACE:-1}"
export APP_ENV="${APP_ENV:-production}"
export API_HOST="${API_HOST:-0.0.0.0}"
export API_PORT="${API_PORT:-8080}"
export METRICS_PORT="${METRICS_PORT:-9090}"

# Database configuration
export DATABASE_URL="${DATABASE_URL:-********************************************************/infinitum_signal}"
export REDIS_URL="${REDIS_URL:-redis://redis:6379}"

# Security configuration
export JWT_SECRET="${JWT_SECRET:-$(openssl rand -hex 32)}"
export ENCRYPTION_KEY="${ENCRYPTION_KEY:-$(openssl rand -hex 32)}"

# Blockchain configuration
export BLOCKCHAIN_ENABLED="${BLOCKCHAIN_ENABLED:-true}"
export BLOCKCHAIN_STORAGE_PATH="${BLOCKCHAIN_STORAGE_PATH:-${DATA_DIR}/blockchain}"

# Initialize directories
init_directories() {
    log_info "Initializing application directories"
    
    mkdir -p "${DATA_DIR}" "${LOGS_DIR}" "${OUTPUT_DIR}"
    mkdir -p "${DATA_DIR}/blockchain" "${DATA_DIR}/scans" "${DATA_DIR}/reports"
    mkdir -p "${OUTPUT_DIR}/sbom" "${OUTPUT_DIR}/hbom" "${OUTPUT_DIR}/compliance"
    
    # Set proper permissions
    chmod 755 "${DATA_DIR}" "${LOGS_DIR}" "${OUTPUT_DIR}"
    
    log_success "Directories initialized"
}

# Wait for dependencies
wait_for_dependencies() {
    log_info "Waiting for dependencies to be ready"
    local max_attempts=30
    local attempt=1

    # Wait for PostgreSQL
    if [[ -n "${DATABASE_URL}" ]]; then
        log_info "Waiting for PostgreSQL..."
        while [[ $attempt -le $max_attempts ]]; do
            if pg_isready -d "${DATABASE_URL}" >/dev/null 2>&1; then
                log_success "PostgreSQL is ready"
                break
            fi
            log_info "PostgreSQL is unavailable - attempt ${attempt}/${max_attempts}, sleeping..."
            sleep 2
            ((attempt++))
        done

        if [[ $attempt -gt $max_attempts ]]; then
            log_error "PostgreSQL failed to become ready after ${max_attempts} attempts"
            exit 1
        fi
    fi

    # Reset attempt counter for Redis
    attempt=1

    # Wait for Redis
    if [[ -n "${REDIS_URL}" ]]; then
        log_info "Waiting for Redis..."
        redis_host=$(echo "${REDIS_URL}" | sed 's|redis://||' | cut -d: -f1)
        redis_port=$(echo "${REDIS_URL}" | sed 's|redis://||' | cut -d: -f2 | cut -d'/' -f1)
        redis_port=${redis_port:-6379}

        while [[ $attempt -le $max_attempts ]]; do
            if timeout 1 bash -c "cat < /dev/null > /dev/tcp/${redis_host}/${redis_port}" 2>/dev/null; then
                log_success "Redis is ready"
                break
            fi
            log_info "Redis is unavailable - attempt ${attempt}/${max_attempts}, sleeping..."
            sleep 2
            ((attempt++))
        done

        if [[ $attempt -gt $max_attempts ]]; then
            log_error "Redis failed to become ready after ${max_attempts} attempts"
            exit 1
        fi
    fi
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations"
    
    if infinitum-signal-cli db migrate --check; then
        log_info "Database is up to date"
    else
        log_info "Running database migrations..."
        infinitum-signal-cli db migrate
        log_success "Database migrations completed"
    fi
}

# Initialize configuration
init_configuration() {
    log_info "Initializing configuration"
    
    # Create configuration file if it doesn't exist
    if [[ ! -f "${CONFIG_DIR}/config.toml" ]]; then
        log_info "Creating default configuration"
        cat > "${CONFIG_DIR}/config.toml" << EOF
[api]
host = "${API_HOST}"
port = ${API_PORT}
metrics_port = ${METRICS_PORT}
cors_enabled = true
rate_limit_requests_per_minute = 1000

[database]
url = "${DATABASE_URL}"
max_connections = 20
connection_timeout_seconds = 30

[redis]
url = "${REDIS_URL}"
pool_size = 10

[logging]
level = "${RUST_LOG}"
format = "json"
output_dir = "${LOGS_DIR}"

[security]
jwt_secret = "${JWT_SECRET}"
encryption_key = "${ENCRYPTION_KEY}"
session_timeout_minutes = 60

[blockchain]
enabled = ${BLOCKCHAIN_ENABLED}
storage_path = "${BLOCKCHAIN_STORAGE_PATH}"
chain_id = "infinitum-signal-chain"

[scanning]
output_dir = "${OUTPUT_DIR}"
parallel_scans = 4
timeout_minutes = 30

[compliance]
frameworks = ["cert-in", "sebi", "iso27001"]
report_formats = ["pdf", "json"]
organization = "Default Organization"

[vulnerability]
sources = ["nvd", "snyk", "github", "osv"]
severity_threshold = "medium"
include_epss = true
EOF
        log_success "Configuration file created"
    fi
}

# Setup monitoring
setup_monitoring() {
    log_info "Setting up monitoring"
    
    # Start Prometheus metrics endpoint
    if [[ "${METRICS_ENABLED:-true}" == "true" ]]; then
        log_info "Metrics endpoint will be available on port ${METRICS_PORT}"
    fi
    
    # Setup log rotation
    if command -v logrotate >/dev/null 2>&1; then
        cat > /tmp/infinitum-signal-logrotate << EOF
${LOGS_DIR}/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 appuser appuser
}
EOF
        log_info "Log rotation configured"
    fi
}

# Health check function
health_check() {
    local max_attempts=30
    local attempt=1
    
    log_info "Performing health check"
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f "http://localhost:${API_PORT}/health" >/dev/null 2>&1; then
            log_success "Health check passed"
            return 0
        fi
        
        log_info "Health check attempt ${attempt}/${max_attempts} failed, retrying..."
        sleep 2
        ((attempt++))
    done
    
    log_error "Health check failed after ${max_attempts} attempts"
    return 1
}

# Signal handlers for graceful shutdown
shutdown_handler() {
    log_info "Received shutdown signal, gracefully stopping..."
    
    # Send SIGTERM to the main process
    if [[ -n "${MAIN_PID:-}" ]]; then
        kill -TERM "${MAIN_PID}" 2>/dev/null || true
        wait "${MAIN_PID}" 2>/dev/null || true
    fi
    
    log_success "Shutdown completed"
    exit 0
}

# Set up signal handlers
trap shutdown_handler SIGTERM SIGINT

# Main execution
main() {
    log_info "Starting Infinitium Signal Enterprise Cyber-Compliance Platform"
    log_info "Version: $(infinitum-signal-cli --version 2>/dev/null || echo 'unknown')"
    log_info "Environment: ${APP_ENV}"
    
    # Initialize
    init_directories
    init_configuration
    wait_for_dependencies
    run_migrations
    setup_monitoring
    
    # Determine startup mode
    case "${1:-server}" in
        "server")
            log_info "Starting API server mode"
            infinitum-signal \
                --config "${CONFIG_DIR}/config.toml" \
                server \
                --host "${API_HOST}" \
                --port "${API_PORT}" &
            MAIN_PID=$!
            ;;
        "cli")
            log_info "Starting CLI mode"
            shift
            exec infinitum-signal-cli "$@"
            ;;
        "scan")
            log_info "Starting scan mode"
            shift
            exec infinitum-signal-cli scan "$@"
            ;;
        "worker")
            log_info "Starting worker mode"
            infinitum-signal \
                --config "${CONFIG_DIR}/config.toml" \
                worker &
            MAIN_PID=$!
            ;;
        *)
            log_info "Starting custom command: $*"
            exec "$@"
            ;;
    esac
    
    # Wait for service to be ready
    if [[ -n "${MAIN_PID:-}" ]]; then
        sleep 5
        health_check
        
        log_success "Infinitium Signal started successfully"
        log_info "API available at: http://${API_HOST}:${API_PORT}"
        log_info "Health check: http://${API_HOST}:${API_PORT}/health"
        log_info "API docs: http://${API_HOST}:${API_PORT}/docs"
        log_info "Metrics: http://${API_HOST}:${METRICS_PORT}/metrics"
        
        # Wait for main process
        wait "${MAIN_PID}"
    fi
}

# Execute main function
main "$@"
