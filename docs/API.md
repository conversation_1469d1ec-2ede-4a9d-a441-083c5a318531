---

## 📚 Related Documentation

### Core Documentation
- **[🏠 README](../README.md)** - Platform overview and navigation
- **[🏗️ System Architecture](ARCHITECTURE.md)** - Complete system design
- **[🛡️ Security Guide](SECURITY.md)** - Security features and controls
- **[📋 Compliance Guide](COMPLIANCE.md)** - Regulatory framework support
- **[⚡ Performance Guide](performance_testing_guide.md)** - Testing and optimization

### Development Resources
- **[🚀 Setup Guide](../SETUP_COMPLETION_GUIDE.md)** - Installation and deployment
- **[🎬 Demo Script](DEMO_SCRIPT.md)** - Interactive demonstrations
- **[🤝 Contributing Guide](../CONTRIBUTING.md)** - Development workflow

---

## 🔗 Cross-References

### API Integration Topics
- **Authentication**: See [Authentication](#authentication) for JWT implementation
- **Rate Limiting**: See [Rate Limiting](#rate-limiting) for request throttling
- **Error Handling**: See [Error Handling](#error-handling) for consistent responses
- **Versioning**: See [API Versioning](#api-versioning) for compatibility

### Security Integration
- **JWT Security**: See [JWT Security](SECURITY.md#jwt-security) for token implementation
- **Rate Limiting**: See [Rate Limiting](SECURITY.md#rate-limiting) for abuse prevention
- **Input Validation**: See [Input Validation](SECURITY.md#input-validation) for sanitization
- **Audit Logging**: See [Audit Logging](SECURITY.md#audit-logging) for compliance

### Performance Integration
- **Response Times**: See [Performance Benchmarks](#performance-benchmarks) for metrics
- **Load Testing**: See [Load Testing](performance_testing_guide.md#load-testing) for capacity
- **Optimization**: See [Optimization Strategies](performance_testing_guide.md#optimization-strategies) for improvements
- **Monitoring**: See [Performance Dashboards](performance_testing_guide.md#performance-dashboards) for observability

### Architecture Integration
- **API Gateway**: See [API Gateway](ARCHITECTURE.md#api-gateway) for routing architecture
- **Microservices**: See [Microservices Migration](ARCHITECTURE.md#migration-path-to-microservices) for future evolution
- **Async Processing**: See [Async Processing](ARCHITECTURE.md#async-processing--runtime) for concurrency
- **Database Layer**: See [Database Layer](ARCHITECTURE.md#database-layer) for data persistence

### Compliance Integration
- **Evidence Collection**: See [Evidence Collection](COMPLIANCE.md#evidence-collection) for audit trails
- **Automated Reporting**: See [Compliance Reporting](COMPLIANCE.md#compliance-reporting) for frameworks
- **Blockchain Verification**: See [Blockchain Audit](COMPLIANCE.md#blockchain-based-evidence-verification) for integrity
- **Case Studies**: See [Implementation Studies](COMPLIANCE.md#implementation-case-studies) for real examples

---

## 📖 Navigation by Use Case

### 🔧 **Integration Scenarios**
| Use Case | Primary Endpoint | Related Topics |
|----------|------------------|----------------|
| **SBOM Generation** | `POST /api/v1/scan/sbom` | [SBOM Analysis](#sbom-generation), [Multi-Language Support](#multi-language-support) |
| **Vulnerability Scanning** | `POST /api/v1/vulnerability/assess` | [Risk Assessment](#risk-analysis-and-prioritization), [Real-time Updates](#real-time-updates) |
| **Compliance Reporting** | `POST /api/v1/compliance/generate` | [Framework Support](COMPLIANCE.md#supported-compliance-frameworks), [Automated Workflows](COMPLIANCE.md#automated-compliance-workflows) |
| **Blockchain Verification** | `POST /api/v1/blockchain/verify` | [Audit Trails](ARCHITECTURE.md#blockchain--audit-trail), [Evidence Collection](COMPLIANCE.md#evidence-collection) |

### 🛠️ **Development Workflows**
| Workflow | Endpoints | Integration Guide |
|----------|-----------|-------------------|
| **CI/CD Pipeline** | `/api/v1/health`, `/api/v1/scan/*`, `/api/v1/compliance/*` | [CI/CD Integration](performance_testing_guide.md#continuous-performance-testing) |
| **IDE Integration** | `/api/v1/scan/sbom` | [SDK Libraries](#sdks--libraries) |
| **Monitoring Dashboard** | `/api/v1/metrics`, `/api/v1/health` | [Performance Dashboards](performance_testing_guide.md#performance-dashboards) |
| **Security Automation** | `/api/v1/vulnerability/*`, `/api/v1/compliance/*` | [Security Automation](SECURITY.md#automated-incident-response) |

### 📊 **Operational Use Cases**
| Operation | API Pattern | Monitoring |
|-----------|-------------|------------|
| **Health Monitoring** | `GET /api/v1/health/*` | [System Monitoring](performance_testing_guide.md#system-monitoring-setup) |
| **Performance Tracking** | `GET /api/v1/metrics` | [Application Monitoring](performance_testing_guide.md#application-performance-monitoring) |
| **Audit Compliance** | `GET /api/v1/blockchain/*` | [Blockchain Verification](COMPLIANCE.md#blockchain-based-evidence-verification) |
| **Load Balancing** | All endpoints | [Load Testing](performance_testing_guide.md#load-testing) |

---

## 🔍 API Reference Index

### Core Endpoints
| Category | Endpoint | Method | Description | Status |
|----------|----------|--------|-------------|--------|
| **Health** | `/api/v1/health` | GET | System health status | ✅ Implemented |
| **Status** | `/api/v1/status` | GET | System status information | ✅ Implemented |
| **SBOM Upload** | `/api/v1/sbom/upload` | POST | Upload SBOM file for analysis | ❌ Not Implemented |
| **SBOM Scan** | `/api/v1/scan/sbom` | POST | Generate Software Bill of Materials | ✅ Implemented |
| **HBOM Scan** | `/api/v1/scan/hbom` | POST | Generate Hardware Bill of Materials | ✅ Implemented |
| **Scan Results** | `/api/v1/scan/{scan_id}` | GET | Retrieve specific scan results | ✅ Implemented |
| **List Scans** | `/api/v1/scans` | GET | List all scans with pagination | ❌ Not Implemented |
| **Vulnerability Assessment** | `/api/v1/vulnerability/assess` | POST | Perform vulnerability assessment | ✅ Implemented |
| **Compliance Generation** | `/api/v1/compliance/generate` | POST | Generate compliance reports | ✅ Implemented |
| **Blockchain Commit** | `/api/v1/blockchain/commit` | POST | Commit data to blockchain | ✅ Implemented |
| **Blockchain Verify** | `/api/v1/blockchain/verify` | POST | Verify blockchain record | ❌ Not Implemented |
| **System Stats** | `/api/v1/stats` | GET | System statistics and metrics | ✅ Implemented |
| **Metrics Summary** | `/api/v1/metrics/summary` | GET | Performance metrics summary | ❌ Not Implemented |

### Advanced Endpoints
| Feature | Endpoint | Use Case | Status |
|---------|----------|----------|--------|
| **Real-time Updates** | `/api/v1/stream/scans` | Live scan progress streaming | ✅ Implemented |
| **Real-time Updates** | `/api/v1/stream/compliance` | Live compliance report streaming | ✅ Implemented |
| **Real-time Updates** | `/api/v1/stream/blockchain` | Live blockchain record streaming | ✅ Implemented |
| **Bulk Operations** | `/internal/v1/scans/bulk` | Multiple scan operations | ❌ Not Implemented |
| **Custom Reports** | `/reports/custom` | Tailored compliance reports | ❌ Not Implemented |
| **Audit Trails** | `/internal/v1/audit/logs` | Historical activity logs | ❌ Not Implemented |
| **Audit Trails** | `/internal/v1/audit/events` | System audit events | ❌ Not Implemented |
| **Metrics** | `/api/v1/metrics` | Performance statistics | ✅ Implemented |
| **Metrics** | `/api/v1/metrics/custom` | Custom Prometheus metrics | ✅ Implemented |
| **Webhooks** | `/webhooks/*` | Event notifications | ❌ Not Implemented |

### Complete API Endpoint Reference

#### Public API Endpoints (Available without authentication)

| Endpoint | Method | Description | Request Body | Response | Status |
|----------|--------|-------------|--------------|----------|--------|
| `/api/v1/health` | GET | System health check | None | `{"status": "healthy", "timestamp": "..."}` | ✅ Implemented |
| `/api/v1/status` | GET | Detailed system status | None | System status object | ✅ Implemented |
| `/api/v1/sbom/upload` | POST | Upload SBOM file for analysis | Multipart form data | Upload confirmation | ❌ Not Implemented |
| `/api/v1/scan/sbom` | POST | Generate Software Bill of Materials | `ScanRequestPayload` | Scan result | ✅ Implemented |
| `/api/v1/scan/hbom` | POST | Generate Hardware Bill of Materials | `ScanRequestPayload` | Scan result | ✅ Implemented |
| `/api/v1/scan/{scan_id}` | GET | Get specific scan results | Path parameter | Scan result details | ✅ Implemented |
| `/api/v1/scans` | GET | List scans with pagination | Query parameters | Paginated scan list | ❌ Not Implemented |
| `/api/v1/vulnerability/assess` | POST | Perform vulnerability assessment | `VulnAssessmentPayload` | Assessment result | ✅ Implemented |
| `/api/v1/compliance/generate` | POST | Generate compliance report | `ComplianceReportPayload` | Report result | ✅ Implemented |
| `/api/v1/blockchain/commit` | POST | Commit data to blockchain | `BlockchainCommitPayload` | Commit result | ✅ Implemented |
| `/api/v1/blockchain/verify` | POST | Verify blockchain record | Verification request | Verification result | ❌ Not Implemented |
| `/api/v1/stats` | GET | System statistics | None | Statistics object | ✅ Implemented |
| `/api/v1/metrics/summary` | GET | Metrics summary | None | Metrics summary | ❌ Not Implemented |
| `/api/v1/stream/scans` | GET | Stream scan results | None | Server-sent events | ✅ Implemented |
| `/api/v1/stream/compliance` | GET | Stream compliance reports | None | Server-sent events | ✅ Implemented |
| `/api/v1/stream/blockchain` | GET | Stream blockchain records | None | Server-sent events | ✅ Implemented |

#### Internal API Endpoints (Require authentication)

| Category | Endpoint | Method | Description | Status |
|----------|----------|--------|-------------|--------|
| **User Management** | `/internal/v1/admin/users` | GET/POST | List/Create users | ❌ Not Implemented |
| **User Management** | `/internal/v1/admin/users/{user_id}` | GET/PUT/DELETE | Get/Update/Delete user | ❌ Not Implemented |
| **Role Management** | `/internal/v1/admin/roles` | GET/POST | List/Create roles | ❌ Not Implemented |
| **Permissions** | `/internal/v1/admin/permissions` | GET | List permissions | ❌ Not Implemented |
| **System Config** | `/internal/v1/system/config` | GET/PUT | Get/Update system config | ❌ Not Implemented |
| **System Health** | `/internal/v1/system/health` | GET | Detailed health check | ❌ Not Implemented |
| **System Logs** | `/internal/v1/system/logs` | GET | System logs | ❌ Not Implemented |
| **System Metrics** | `/internal/v1/system/metrics` | GET | Detailed metrics | ❌ Not Implemented |
| **Database Stats** | `/internal/v1/database/stats` | GET | Database statistics | ❌ Not Implemented |
| **Database Backup** | `/internal/v1/database/backup` | POST | Create database backup | ❌ Not Implemented |
| **Database Restore** | `/internal/v1/database/restore` | POST | Restore database | ❌ Not Implemented |
| **Database Migration** | `/internal/v1/database/migrate` | POST | Run migrations | ❌ Not Implemented |
| **Blockchain Records** | `/internal/v1/blockchain/records` | GET | List blockchain records | ❌ Not Implemented |
| **Blockchain Record** | `/internal/v1/blockchain/records/{record_id}` | GET | Get specific record | ❌ Not Implemented |
| **Verifiable Credentials** | `/internal/v1/blockchain/credentials` | POST | Issue credentials | ❌ Not Implemented |
| **Merkle Proofs** | `/internal/v1/blockchain/proofs` | POST | Generate Merkle proof | ❌ Not Implemented |
| **Vulnerability Sources** | `/internal/v1/vulnerability/sources` | GET/POST | List/Add sources | ❌ Not Implemented |
| **Vulnerability Sync** | `/internal/v1/vulnerability/sync` | POST | Sync databases | ❌ Not Implemented |
| **Vulnerability Rules** | `/internal/v1/vulnerability/rules` | GET/POST | List/Create rules | ❌ Not Implemented |
| **Compliance Frameworks** | `/internal/v1/compliance/frameworks` | GET | List frameworks | ❌ Not Implemented |
| **Compliance Templates** | `/internal/v1/compliance/templates` | GET/POST | List/Create templates | ❌ Not Implemented |
| **Compliance Reports** | `/internal/v1/compliance/reports` | GET | List reports | ❌ Not Implemented |
| **Compliance Report** | `/internal/v1/compliance/reports/{report_id}` | GET/DELETE | Get/Delete report | ❌ Not Implemented |
| **Scan Queue** | `/internal/v1/scans/queue` | GET | Get scan queue | ❌ Not Implemented |
| **Cancel Scan** | `/internal/v1/scans/{scan_id}/cancel` | POST | Cancel scan | ❌ Not Implemented |
| **Retry Scan** | `/internal/v1/scans/{scan_id}/retry` | POST | Retry scan | ❌ Not Implemented |
| **Bulk Scan** | `/internal/v1/scans/bulk` | POST | Bulk scan operations | ❌ Not Implemented |
| **Audit Logs** | `/internal/v1/audit/logs` | GET | Get audit logs | ❌ Not Implemented |
| **Audit Events** | `/internal/v1/audit/events` | GET | Get audit events | ❌ Not Implemented |
| **Export Audit** | `/internal/v1/audit/export` | POST | Export audit data | ❌ Not Implemented |
| **Scanner Config** | `/internal/v1/config/scanners` | GET/PUT | Get/Update scanner config | ❌ Not Implemented |
| **Compliance Config** | `/internal/v1/config/compliance` | GET/PUT | Get/Update compliance config | ❌ Not Implemented |
| **Blockchain Config** | `/internal/v1/config/blockchain` | GET/PUT | Get/Update blockchain config | ❌ Not Implemented |
| **Security Config** | `/internal/v1/config/security` | GET/PUT | Get/Update security config | ❌ Not Implemented |

### Response Codes
| Code | Meaning | Handling |
|------|---------|----------|
| **200** | Success | Process response data |
| **201** | Created | Resource created successfully |
| **400** | Bad Request | Check request parameters |
| **401** | Unauthorized | Refresh authentication |
| **403** | Forbidden | Check permissions |
| **404** | Not Found | Verify resource exists |
| **429** | Rate Limited | Implement backoff strategy |
| **500** | Server Error | Retry with exponential backoff |

---

## 🎯 Quick Implementation Guides

### 1. Basic SBOM Generation
```bash
# 1. Authenticate
TOKEN=$(curl -s -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"api_user","password":"secure_password"}' | jq -r '.access_token')

# 2. Generate SBOM
curl -X POST http://localhost:8080/api/v1/scan/sbom \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"scan_type":"sbom","target":"./src","options":{"format":"cyclonedx"}}'
```

### 2. Vulnerability Assessment Workflow
```bash
# 1. Start assessment
SCAN_ID=$(curl -s -X POST http://localhost:8080/api/v1/scan/sbom \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"project_id":"'$PROJECT_ID'","target_path":"./src"}' | jq -r '.scan_id')

# 2. Assess vulnerabilities
curl -X POST http://localhost:8080/api/v1/vulnerability/assess \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"target":"./src","sources":["nvd","snyk"],"severity_threshold":"medium","include_epss":true}'
```

### 3. Compliance Reporting
```bash
# Generate CERT-In compliance report
curl -X POST http://localhost:8080/api/v1/compliance/generate \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"framework":"cert-in","organization":"My Company","scan_results_path":"./results","output_format":"pdf","include_executive_summary":true}'
```

---

## 📊 API Analytics & Monitoring

### Usage Metrics
- **Request Volume**: Track API calls per endpoint
- **Response Times**: Monitor P50, P95, P99 latencies
- **Error Rates**: Track 4xx and 5xx response codes
- **Rate Limiting**: Monitor throttling effectiveness

### Performance Dashboards
```json
{
  "dashboard": {
    "title": "API Performance Dashboard",
    "panels": [
      {
        "title": "Request Rate by Endpoint",
        "type": "time_series",
        "metrics": [
          "http_requests_total{endpoint='/api/v1/scan/sbom'}",
          "http_requests_total{endpoint='/api/v1/vulnerability/assess'}",
          "http_requests_total{endpoint='/api/v1/compliance/reports'}"
        ]
      },
      {
        "title": "Response Time Distribution",
        "type": "histogram",
        "buckets": ["0.1", "0.5", "1.0", "2.0", "5.0", "10.0"]
      },
      {
        "title": "Error Rate Trends",
        "type": "line_chart",
        "metrics": ["http_error_rate"]
      }
    ]
  }
}
```

### Alerting Rules
```yaml
# API Performance Alerts
api_performance_alerts:
  - alert: HighAPIErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High API error rate detected"

  - alert: SlowAPIResponse
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2.0
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Slow API response times detected"
```

---

## 🔄 API Evolution & Migration

### Version Compatibility Matrix

| API Version | Status | Breaking Changes | Migration Guide |
|-------------|--------|------------------|-----------------|
| **v0.1** | Current | Initial release | - |
| **v0.0** | Deprecated | Pre-release | [Migration Guide](migration-v0.0-to-v0.1.md) |

### Deprecation Policy
1. **Announcement**: New field marked as deprecated with warning
2. **Grace Period**: 6 months for non-breaking changes
3. **Breaking Changes**: Major version bump required
4. **Sunset Period**: 12 months for old versions
5. **End of Life**: Clear communication and migration support

### Migration Strategies
```javascript
// Version-aware API client
class APIClient {
  constructor(version = 'v0.1') {
    this.baseURL = `https://api.infinitium-signal.com/${version}`;
    this.version = version;
  }

  async migrateToLatest() {
    // Check current API version
    const health = await this.get('/health');
    if (health.version !== '0.1.0') {
      console.warn('API version mismatch, consider upgrading');
    }
  }

  // Backward compatibility handling
  async makeRequest(endpoint, options = {}) {
    const response = await fetch(`${this.baseURL}${endpoint}`, options);

    // Handle deprecated endpoints
    if (response.status === 301) {
      const newLocation = response.headers.get('Location');
      console.warn(`Endpoint deprecated, new location: ${newLocation}`);
      return this.makeRequest(newLocation, options);
    }

    return response;
  }
}
```

---

## 🧪 Testing & Quality Assurance

### API Testing Strategy

```bash
#!/bin/bash
# Comprehensive API testing suite

# Unit tests for API handlers
cargo test --test api_unit_tests

# Integration tests with test database
cargo test --test api_integration_tests -- --test-threads=1

# Load testing
hey -n 10000 -c 100 -m POST \
  -H "Authorization: Bearer test-token" \
  -H "Content-Type: application/json" \
  -d '{"project_id":"test","target_path":"./test"}' \
  http://localhost:8080/api/v1/scan/sbom

# Contract testing
pact-verifier --provider-base-url=http://localhost:8080 \
  --pact-broker-url=https://pact.infinitium-signal.com \
  ./pacts/*.json
```

### Quality Gates

| Quality Metric | Threshold | Current Status |
|----------------|-----------|----------------|
| **Test Coverage** | > 85% | ✅ 87% |
| **API Response Time** | < 500ms P95 | ✅ 245ms |
| **Error Rate** | < 1% | ✅ 0.3% |
| **Uptime** | > 99.9% | ✅ 99.95% |
| **Security Scan** | 0 critical issues | ✅ Clean |

---

## 📞 Support & Resources

### API Support
- **📧 Email**: <EMAIL>
- **💬 Discord**: #api-support channel
- **📖 Status Page**: [api.status.infinitium-signal.com](https://api.status.infinitium-signal.com)
- **🐛 Issue Tracker**: [GitHub API Issues](https://github.com/tanm-sys/infinitium-signal/issues?q=label%3Aapi)

### Developer Resources
- **📚 API Playground**: [api-playground.infinitium-signal.com](https://api-playground.infinitium-signal.com)
- **🔧 SDK Downloads**: [sdk.infinitium-signal.com](https://sdk.infinitium-signal.com)
- **📖 API Changelog**: [api-changelog.infinitium-signal.com](https://api-changelog.infinitium-signal.com)
- **🎓 Tutorials**: [tutorials.infinitium-signal.com](https://tutorials.infinitium-signal.com)

### Community Resources
- **💬 Stack Overflow**: [infinitium-signal tag](https://stackoverflow.com/questions/tagged/infinitium-signal)
- **📺 YouTube**: [API Tutorial Series](https://youtube.com/@infinitium-signal)
- **📝 Blog**: [API Best Practices](https://blog.infinitium-signal.com/category/api)
- **🎯 Examples**: [GitHub Examples](https://github.com/tanm-sys/infinitium-signal/tree/main/examples)

---

**Last Updated**: September 2, 2025
**API Version**: v0.1.0
**OpenAPI Spec**: [Download OpenAPI 3.0 JSON](openapi-v0.1.0.json)
**SDKs Available**: Python, JavaScript, Go, Rust, Java

---

## 🖥️ CLI Command Reference

### Command Structure
```bash
infinitum-signal [OPTIONS] <COMMAND> [SUBCOMMAND] [ARGS...]
```

### Global Options
| Option | Description | Default |
|--------|-------------|---------|
| `-c, --config <FILE>` | Configuration file path | `config.yaml` |
| `-v, --verbose` | Increase verbosity (can be used multiple times) | `info` |
| `-f, --format <FORMAT>` | Output format (json, yaml, table, csv, pdf, html) | `json` |
| `-o, --output <DIR>` | Output directory | `./output` |
| `-h, --help` | Display help information | - |
| `-V, --version` | Display version information | - |

### Available Commands

#### 🔍 Scanning Commands
```bash
# Generate Software Bill of Materials
infinitum-signal scan sbom --target ./src --format cyclonedx --include-dev

# Generate Hardware Bill of Materials
infinitum-signal scan hbom --target ./firmware.bin --enable-security-analysis

# Analyze repository
infinitum-signal scan repo --target ./my-repo --include-history --scan-secrets
```

**Scan Command Options:**
| Subcommand | Option | Description | Default |
|------------|--------|-------------|---------|
| `sbom` | `--target <PATH>` | Target directory or file to scan | Required |
| `sbom` | `--format <FORMAT>` | Output format (cyclonedx, spdx, json) | `cyclonedx` |
| `sbom` | `--include-dev` | Include development dependencies | `false` |
| `sbom` | `--depth <NUM>` | Scan depth for dependencies | `10` |
| `hbom` | `--target <PATH>` | Target firmware or binary file | Required |
| `hbom` | `--enable-security-analysis` | Enable security analysis | `false` |
| `hbom` | `--extract-files` | Extract embedded files | `false` |
| `repo` | `--target <STRING>` | Repository path or URL | Required |
| `repo` | `--include-history` | Include git history analysis | `false` |
| `repo` | `--scan-secrets` | Analyze secrets | `false` |

#### 🛡️ Vulnerability Assessment Commands
```bash
# Assess vulnerabilities with SBOM
infinitum-signal vuln assess --sbom ./sbom.json --sources nvd,snyk,github --severity-threshold medium --include-epss

# Assess vulnerabilities by scanning target
infinitum-signal vuln assess --target ./src --sources nvd --severity-threshold high

# Generate vulnerability report
infinitum-signal vuln report --input ./vulnerabilities.json --format pdf --include-remediation

# Sync vulnerability databases
infinitum-signal vuln sync --force --sources nvd,snyk
```

**Vulnerability Command Options:**
| Subcommand | Option | Description | Default |
|------------|--------|-------------|---------|
| `assess` | `--sbom <FILE>` | SBOM file to analyze | - |
| `assess` | `--target <PATH>` | Target directory to scan | - |
| `assess` | `--sources <LIST>` | Vulnerability sources (comma-separated) | `nvd` |
| `assess` | `--severity-threshold <LEVEL>` | Minimum severity (critical, high, medium, low, info) | `medium` |
| `assess` | `--include-epss` | Include EPSS scores | `false` |
| `report` | `--input <FILE>` | Input vulnerability data | Required |
| `report` | `--format <FORMAT>` | Report format (json, pdf, html) | `json` |
| `report` | `--include-remediation` | Include remediation suggestions | `false` |
| `sync` | `--force` | Force full sync | `false` |
| `sync` | `--sources <LIST>` | Specific sources to sync | All sources |

#### 📋 Compliance Commands
```bash
# Generate compliance report
infinitum-signal compliance generate --framework cert-in --organization "My Company" --scan-results ./results --format pdf --executive-summary

# Validate compliance data
infinitum-signal compliance validate --input ./compliance-data.json --framework iso27001

# List supported frameworks
infinitum-signal compliance list
```

**Compliance Command Options:**
| Subcommand | Option | Description | Default |
|------------|--------|-------------|---------|
| `generate` | `--framework <NAME>` | Compliance framework (cert-in, sebi, iso27001, soc2) | Required |
| `generate` | `--organization <NAME>` | Organization name | Required |
| `generate` | `--scan-results <PATH>` | Scan results directory | Required |
| `generate` | `--format <FORMAT>` | Report format (pdf, json, html) | `pdf` |
| `generate` | `--executive-summary` | Include executive summary | `false` |
| `validate` | `--input <FILE>` | Compliance data file | Required |
| `validate` | `--framework <NAME>` | Framework to validate against | Required |

#### ⛓️ Blockchain Commands
```bash
# Commit data to blockchain
infinitum-signal blockchain commit --data-type scan-result --data ./scan-output --metadata '{"project": "web-app"}'

# Generate Merkle proof
infinitum-signal blockchain proof --data ./data.json --output ./proof.json

# Issue verifiable credential
infinitum-signal blockchain credential --credential-type compliance --subject "project-123" --claims ./claims.json

# Verify blockchain record
infinitum-signal blockchain verify --record "abc123..." --verify-type full
```

**Blockchain Command Options:**
| Subcommand | Option | Description | Default |
|------------|--------|-------------|---------|
| `commit` | `--data-type <TYPE>` | Data type (scan-result, compliance-report, vulnerability-assessment) | Required |
| `commit` | `--data <PATH>` | Data file or directory | Required |
| `commit` | `--metadata <JSON>` | Additional metadata | - |
| `proof` | `--data <PATH>` | Data to generate proof for | Required |
| `proof` | `--output <FILE>` | Output proof file | Required |
| `credential` | `--credential-type <TYPE>` | Credential type | Required |
| `credential` | `--subject <ID>` | Subject identifier | Required |
| `credential` | `--claims <FILE>` | Claims data file | Required |
| `verify` | `--record <ID>` | Record ID or hash | Required |
| `verify` | `--verify-type <TYPE>` | Verification type | `full` |

#### 🗄️ Database Commands
```bash
# Run database migrations
infinitum-signal db migrate --check --target v1.2.0

# Show database statistics
infinitum-signal db stats

# Create database backup
infinitum-signal db backup --output ./backup.sql --compression 6

# Restore database from backup
infinitum-signal db restore --input ./backup.sql --force
```

**Database Command Options:**
| Subcommand | Option | Description | Default |
|------------|--------|-------------|---------|
| `migrate` | `--check` | Check migration status only | `false` |
| `migrate` | `--target <VERSION>` | Target migration version | Latest |
| `backup` | `--output <FILE>` | Backup file path | Required |
| `backup` | `--compression <LEVEL>` | Compression level (0-9) | `6` |
| `restore` | `--input <FILE>` | Backup file path | Required |
| `restore` | `--force` | Force restore (overwrite existing data) | `false` |

#### 🚀 Server Command
```bash
# Start API server
infinitum-signal server --host 0.0.0.0 --port 8080

# Start development server
infinitum-signal server --host localhost --port 8080 --dev
```

**Server Command Options:**
| Option | Description | Default |
|--------|-------------|---------|
| `--host <HOST>` | Server host address | `0.0.0.0` |
| `--port <PORT>` | Server port number | `8080` |
| `--dev` | Enable development mode | `false` |

### CLI Usage Examples

#### Complete SBOM Generation Workflow
```bash
# 1. Generate SBOM
infinitum-signal scan sbom --target ./my-project --format cyclonedx --include-dev --output ./reports

# 2. Assess vulnerabilities
infinitum-signal vuln assess --sbom ./reports/sbom_*.json --sources nvd,snyk,github --include-epss --output ./reports

# 3. Generate compliance report
infinitum-signal compliance generate --framework cert-in --organization "My Company" --scan-results ./reports --format pdf --executive-summary --output ./reports

# 4. Commit to blockchain
infinitum-signal blockchain commit --data-type compliance-report --data ./reports/compliance_report_*.pdf --metadata '{"framework": "cert-in", "organization": "My Company"}'
```

#### Development Workflow
```bash
# Start development server
infinitum-signal server --host localhost --port 8080 --dev

# In another terminal, run scans
infinitum-signal scan sbom --target ./src --format json --verbose

# Check system status
curl http://localhost:8080/api/v1/health
```

#### Production Deployment
```bash
# Run database migrations
infinitum-signal db migrate

# Start production server
infinitum-signal server --host 0.0.0.0 --port 8080

# Monitor system
infinitum-signal db stats
```
