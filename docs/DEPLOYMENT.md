# Deployment Guide

This guide covers deploying Infinitium Signal in various environments, from development to production.

## Table of Contents
- [Prerequisites](#prerequisites)
- [Docker Deployment](#docker-deployment)
- [Kubernetes Deployment](#kubernetes-deployment)
- [Production Setup](#production-setup)
- [Scaling Strategies](#scaling-strategies)
- [Rollback Procedures](#rollback-procedures)
- [Monitoring and Maintenance](#monitoring-and-maintenance)
- [Troubleshooting Deployment](#troubleshooting-deployment)

## Prerequisites

### System Requirements
- **CPU:** 2 cores minimum, 4+ cores recommended
- **Memory:** 4GB minimum, 8GB+ recommended
- **Storage:** 20GB minimum, 100GB+ for production
- **Network:** Stable internet connection

### Required Software
- **Docker:** 20.10.0+
- **Docker Compose:** 2.0.0+
- **Kubernetes:** 1.24+ (for K8s deployment)
- **Helm:** 3.0+ (for K8s deployment)
- **PostgreSQL/MySQL:** Compatible database server

### Environment Preparation
```bash
# Create deployment directory
mkdir -p /opt/infinitium-signal
cd /opt/infinitium-signal

# Clone or copy application files
git clone https://github.com/infinitium-signal/infinitium-signal.git .
# OR copy your built artifacts
```

## Docker Deployment

### Single Container Deployment
```yaml
# docker-compose.yml
version: '3.8'

services:
  infinitium-signal:
    image: infinitiumsignal/infinitium-signal:latest
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=**********************************/infinitium_signal
      - API_PORT=8080
      - LOG_LEVEL=info
    depends_on:
      - db
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=infinitium_signal
      - POSTGRES_USER=infinitium
      - POSTGRES_PASSWORD=secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  postgres_data:
```

### Multi-Container Deployment
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  app:
    image: infinitiumsignal/infinitium-signal:${TAG}
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=**********************************/infinitium_signal
      - REDIS_URL=redis://redis:6379
      - API_PORT=8080
      - LOG_LEVEL=info
      - ENABLE_METRICS=true
    depends_on:
      - db
      - redis
    volumes:
      - ./certs:/app/certs:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=infinitium_signal
      - POSTGRES_USER=infinitium
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_MAX_CONNECTIONS=100
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    command: >
      postgres -c max_connections=100
               -c shared_buffers=256MB
               -c effective_cache_size=1GB
               -c maintenance_work_mem=64MB
               -c checkpoint_completion_target=0.9
               -c wal_buffers=16MB
               -c default_statistics_target=100

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./certs:/etc/ssl/certs:ro
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### Deployment Commands
```bash
# Build and deploy
export TAG=v1.2.0
docker-compose -f docker-compose.prod.yml up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f app

# Scale services
docker-compose up -d --scale app=3

# Update to new version
docker-compose pull
docker-compose up -d

# Stop deployment
docker-compose down
```

## Kubernetes Deployment

### Using Helm Chart
```bash
# Add Helm repository
helm repo add infinitium-signal https://charts.infinitium-signal.com
helm repo update

# Install with default values
helm install infinitium-signal infinitium-signal/infinitium-signal

# Install with custom values
helm install infinitium-signal infinitium-signal/infinitium-signal \
  --values custom-values.yaml \
  --namespace infinitium-signal \
  --create-namespace
```

### Manual Kubernetes Deployment
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: infinitium-signal
  namespace: infinitium-signal
spec:
  replicas: 3
  selector:
    matchLabels:
      app: infinitium-signal
  template:
    metadata:
      labels:
        app: infinitium-signal
    spec:
      containers:
      - name: infinitium-signal
        image: infinitiumsignal/infinitium-signal:v1.2.0
        ports:
        - containerPort: 8080
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: database-url
        - name: API_PORT
          value: "8080"
        - name: LOG_LEVEL
          value: "info"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: certs
          mountPath: /app/certs
          readOnly: true
      volumes:
      - name: certs
        secret:
          secretName: tls-certs
---
apiVersion: v1
kind: Service
metadata:
  name: infinitium-signal
  namespace: infinitium-signal
spec:
  selector:
    app: infinitium-signal
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: infinitium-signal
  namespace: infinitium-signal
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - api.infinitium-signal.com
    secretName: infinitium-signal-tls
  rules:
  - host: api.infinitium-signal.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: infinitium-signal
            port:
              number: 80
```

### Database Deployment
```yaml
# postgres-deployment.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: infinitium-signal
spec:
  serviceName: postgres
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          value: infinitium_signal
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: password
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 50Gi
```

### Deployment Commands
```bash
# Create namespace
kubectl create namespace infinitium-signal

# Create secrets
kubectl create secret generic db-secret \
  --from-literal=username=infinitium \
  --from-literal=password=secure_password \
  --from-literal=database-url=*****************************************************/infinitium_signal \
  --namespace infinitium-signal

# Deploy database
kubectl apply -f postgres-deployment.yaml

# Deploy application
kubectl apply -f deployment.yaml

# Check deployment status
kubectl get pods -n infinitium-signal
kubectl get services -n infinitium-signal

# View logs
kubectl logs -f deployment/infinitium-signal -n infinitium-signal

# Scale deployment
kubectl scale deployment infinitium-signal --replicas=5 -n infinitium-signal
```

## Production Setup

### Security Hardening
```bash
# Generate SSL certificates
openssl req -x509 -newkey rsa:4096 \
  -keyout certs/server.key -out certs/server.crt \
  -days 365 -nodes -subj "/CN=api.infinitium-signal.com"

# Set proper permissions
chmod 600 certs/server.key
chmod 644 certs/server.crt

# Configure firewall
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 22/tcp
ufw --force enable
```

### Environment Configuration
```bash
# Production environment variables
export DATABASE_URL=***************************************/infinitium_signal
export API_PORT=8080
export LOG_LEVEL=warn
export ENABLE_METRICS=true
export METRICS_PORT=9090
export SSL_CERT_PATH=/app/certs/server.crt
export SSL_KEY_PATH=/app/certs/server.key
export SESSION_SECRET=your-secure-session-secret
export API_KEY_EXPIRY=24h
export RATE_LIMIT_REQUESTS=1000
export RATE_LIMIT_WINDOW=60
```

### Database Optimization
```sql
-- PostgreSQL production configuration
ALTER SYSTEM SET max_connections = '200';
ALTER SYSTEM SET shared_buffers = '512MB';
ALTER SYSTEM SET effective_cache_size = '2GB';
ALTER SYSTEM SET maintenance_work_mem = '128MB';
ALTER SYSTEM SET checkpoint_completion_target = '0.9';
ALTER SYSTEM SET wal_buffers = '32MB';
ALTER SYSTEM SET default_statistics_target = '500';
ALTER SYSTEM SET random_page_cost = '1.1';
ALTER SYSTEM SET effective_io_concurrency = '200';
ALTER SYSTEM SET work_mem = '4MB';
ALTER SYSTEM SET min_wal_size = '1GB';
ALTER SYSTEM SET max_wal_size = '4GB';
```

### Monitoring Setup
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'infinitium-signal'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: '/metrics'
```

## Scaling Strategies

### Horizontal Scaling
```bash
# Docker Compose scaling
docker-compose up -d --scale app=5

# Kubernetes scaling
kubectl scale deployment infinitium-signal --replicas=10 -n infinitium-signal

# Auto-scaling with HPA
kubectl autoscale deployment infinitium-signal \
  --cpu-percent=70 \
  --min=3 \
  --max=20 \
  -n infinitium-signal
```

### Vertical Scaling
```yaml
# Update resource limits
kubectl patch deployment infinitium-signal -n infinitium-signal --type='json' \
  -p='[{"op": "replace", "path": "/spec/template/spec/containers/0/resources/limits/memory", "value": "2Gi"}]'
```

### Database Scaling
```yaml
# Read replicas configuration
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres-read-replica
spec:
  replicas: 2
  template:
    spec:
      containers:
      - name: postgres
        image: postgres:15
        env:
        - name: POSTGRES_MASTER_HOST
          value: postgres-primary
        # Additional replica configuration
```

### Load Balancing
```yaml
# NGINX load balancer configuration
upstream infinitium_backend {
    server app1:8080;
    server app2:8080;
    server app3:8080;
}

server {
    listen 80;
    server_name api.infinitium-signal.com;

    location / {
        proxy_pass http://infinitium_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Rollback Procedures

### Docker Rollback
```bash
# List available images
docker images infinitiumsignal/infinitium-signal

# Rollback to previous version
docker tag infinitiumsignal/infinitium-signal:v1.1.0 infinitiumsignal/infinitium-signal:latest
docker-compose up -d

# Or rollback specific service
docker-compose up -d app --no-deps
```

### Kubernetes Rollback
```bash
# Check deployment history
kubectl rollout history deployment/infinitium-signal -n infinitium-signal

# Rollback to previous revision
kubectl rollout undo deployment/infinitium-signal -n infinitium-signal

# Rollback to specific revision
kubectl rollout undo deployment/infinitium-signal --to-revision=2 -n infinitium-signal
```

### Database Rollback
```bash
# List available migrations
cargo run --bin migrate -- --list

# Rollback specific migration
cargo run --bin migrate -- down 1

# Restore from backup
pg_restore -h localhost -U infinitium -d infinitium_signal /path/to/backup.sql
```

### Automated Rollback Script
```bash
#!/bin/bash
# rollback.sh

set -e

echo "Starting rollback process..."

# Health check before rollback
if curl -f http://localhost:8080/health; then
    echo "Service is healthy, proceeding with rollback..."
else
    echo "Service is unhealthy, aborting rollback"
    exit 1
fi

# Create backup before rollback
echo "Creating backup..."
docker exec postgres pg_dump -U infinitium infinitium_signal > backup_$(date +%Y%m%d_%H%M%S).sql

# Rollback application
echo "Rolling back application..."
docker-compose pull app
docker-compose up -d app

# Wait for health check
echo "Waiting for service to be healthy..."
timeout 300 bash -c 'until curl -f http://localhost:8080/health; do sleep 5; done'

echo "Rollback completed successfully"
```

## Monitoring and Maintenance

### Health Checks
```bash
# Application health
curl http://localhost:8080/health

# Database health
docker exec postgres pg_isready -U infinitium -d infinitium_signal

# Kubernetes health
kubectl get pods -n infinitium-signal
kubectl describe pod <pod-name> -n infinitium-signal
```

### Log Management
```bash
# View application logs
docker-compose logs -f app

# Kubernetes logs
kubectl logs -f deployment/infinitium-signal -n infinitium-signal

# Log rotation (logrotate configuration)
cat > /etc/logrotate.d/infinitium-signal << EOF
/app/logs/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 infinitium infinitium
    postrotate
        docker-compose restart app
    endscript
}
EOF
```

### Backup Strategy
```bash
# Database backup script
#!/bin/bash
BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
docker exec postgres pg_dump -U infinitium infinitium_signal > $BACKUP_DIR/db_backup_$DATE.sql

# Application data backup
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz /opt/infinitium-signal/data/

# Clean old backups (keep last 7 days)
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: $BACKUP_DIR/db_backup_$DATE.sql"
```

### Update Procedures
```bash
# Zero-downtime update
docker-compose pull
docker-compose up -d --no-deps app

# Kubernetes rolling update
kubectl set image deployment/infinitium-signal infinitium-signal=infinitiumsignal/infinitium-signal:v1.3.0
kubectl rollout status deployment/infinitium-signal -n infinitium-signal
```

## Troubleshooting Deployment

### Common Issues

#### Container Won't Start
```bash
# Check container logs
docker-compose logs app

# Check container status
docker-compose ps

# Verify environment variables
docker-compose exec app env

# Check disk space
df -h
```

#### Database Connection Issues
```bash
# Test database connection
docker-compose exec db psql -U infinitium -d infinitium_signal -c "SELECT 1;"

# Check database logs
docker-compose logs db

# Verify connection string
echo $DATABASE_URL
```

#### High Resource Usage
```bash
# Monitor resource usage
docker stats

# Check application metrics
curl http://localhost:9090/metrics

# Profile application
docker-compose exec app cargo flamegraph --bin infinitium-signal
```

#### Network Issues
```bash
# Test service connectivity
curl -v http://localhost:8080/health

# Check firewall rules
ufw status

# Verify DNS resolution
nslookup api.infinitium-signal.com
```

### Performance Tuning

#### Application Tuning
```bash
# Adjust worker threads
export RUST_THREADS=4

# Configure connection pooling
export DATABASE_POOL_SIZE=20

# Enable caching
export CACHE_ENABLED=true
export CACHE_TTL=3600
```

#### Database Tuning
```sql
-- Connection pooling
ALTER SYSTEM SET max_connections = '100';

-- Query optimization
CREATE INDEX CONCURRENTLY idx_scans_created_at ON scans (created_at);
CREATE INDEX CONCURRENTLY idx_scans_status ON scans (status);

-- Vacuum and analyze
VACUUM ANALYZE;
```

#### System Tuning
```bash
# Increase file descriptors
echo "infinitium soft nofile 65536" >> /etc/security/limits.conf
echo "infinitium hard nofile 65536" >> /etc/security/limits.conf

# Configure kernel parameters
echo "net.core.somaxconn = 1024" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 1024" >> /etc/sysctl.conf
sysctl -p
```

---

## Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [Kubernetes Documentation](https://kubernetes.io/docs/)
- [Helm Documentation](https://helm.sh/docs/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [NGINX Documentation](https://nginx.org/en/docs/)

For deployment support, contact the DevOps team or create an issue in the repository.