# Development Guide

This guide provides comprehensive information for developers contributing to Infinitium Signal.

## Table of Contents
- [Prerequisites](#prerequisites)
- [Development Environment Setup](#development-environment-setup)
- [Project Structure](#project-structure)
- [Coding Standards](#coding-standards)
- [Testing](#testing)
- [CI/CD Pipeline](#cicd-pipeline)
- [Code Review Process](#code-review-process)
- [Architecture Guidelines](#architecture-guidelines)
- [Contributing Guidelines](#contributing-guidelines)

## Prerequisites

### System Requirements
- **Operating System:** Linux (Ubuntu 20.04+), macOS (10.15+), or Windows (with WSL2)
- **Memory:** 8GB RAM minimum, 16GB recommended
- **Disk Space:** 10GB free space
- **Network:** Stable internet connection for dependency downloads

### Required Software
- **Rust:** 1.70.0 or later ([Install Rust](https://rustup.rs/))
- **Git:** 2.30.0 or later
- **Docker:** 20.10.0 or later (for containerized development)
- **Docker Compose:** 2.0.0 or later
- **PostgreSQL:** 13.0 or later (or MySQL 8.0+)
- **Node.js:** 18.0+ (for frontend development, if applicable)

### Optional Tools
- **VS Code** with Rust extensions (rust-analyzer, CodeLLDB)
- **IntelliJ IDEA** with Rust plugin
- **GitHub CLI** for streamlined Git operations
- **kubectl** for Kubernetes development
- **Helm** for Kubernetes deployments

## Development Environment Setup

### 1. Clone the Repository
```bash
git clone https://github.com/infinitium-signal/infinitium-signal.git
cd infinitium-signal
```

### 2. Install Rust Toolchain
```bash
# Install rustup (if not already installed)
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# Install required toolchain
rustup install stable
rustup default stable

# Add required components
rustup component add rustfmt
rustup component add clippy
rustup component add rust-src
```

### 3. Setup Database
```bash
# Using Docker (recommended)
docker run -d \
  --name infinitium-postgres \
  -e POSTGRES_DB=infinitium_signal \
  -e POSTGRES_USER=infinitium \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 \
  postgres:15

# Or install PostgreSQL locally
sudo apt-get install postgresql postgresql-contrib  # Ubuntu/Debian
brew install postgresql                            # macOS
```

### 4. Configure Environment
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your local settings
nano .env
```

### 5. Build and Run
```bash
# Build the project
cargo build

# Run database migrations
cargo run --bin migrate

# Start the application
cargo run

# Or run with Docker Compose
docker-compose up -d
```

### 6. Verify Setup
```bash
# Check health endpoint
curl http://localhost:8080/health

# Run tests
cargo test

# Check code formatting
cargo fmt --check

# Run linter
cargo clippy
```

## Project Structure

```
infinitium-signal/
├── src/
│   ├── main.rs                 # Application entry point
│   ├── lib.rs                  # Library definitions
│   ├── api/                    # REST API handlers
│   │   ├── handlers.rs         # Request handlers
│   │   ├── server.rs           # HTTP server setup
│   │   └── middleware.rs       # Custom middleware
│   ├── blockchain/             # Blockchain integration
│   ├── compliance/             # Compliance scanning logic
│   ├── database/               # Database models and connections
│   ├── scanners/               # Various scanners (SBOM, license, etc.)
│   ├── utils/                  # Utility functions
│   └── observability/          # Monitoring and logging
├── benches/                    # Performance benchmarks
├── tests/                      # Integration and unit tests
├── scripts/                    # Utility scripts
├── docs/                       # Documentation
├── docker/                     # Docker configurations
├── deployment/                 # Deployment configurations
├── monitoring/                 # Monitoring setup
├── certs/                      # SSL certificates
└── load_test_results/          # Load testing results
```

## Coding Standards

### Rust Style Guidelines

#### Code Formatting
- Use `rustfmt` for consistent formatting
- Maximum line length: 100 characters
- Use 4 spaces for indentation

```bash
# Format code
cargo fmt

# Check formatting without changes
cargo fmt --check
```

#### Naming Conventions
- **Functions/Methods:** `snake_case`
- **Types/Structs:** `PascalCase`
- **Constants:** `SCREAMING_SNAKE_CASE`
- **Modules:** `snake_case`
- **Files:** `snake_case.rs`

#### Documentation
- Document all public APIs with `///` comments
- Use `//!` for module-level documentation
- Include examples in documentation where appropriate

```rust
/// Calculates the compliance score for a given SBOM
///
/// # Arguments
/// * `sbom` - The Software Bill of Materials to analyze
/// * `rules` - Compliance rules to apply
///
/// # Returns
/// The compliance score as a percentage (0.0 to 100.0)
///
/// # Examples
/// ```
/// let score = calculate_compliance_score(sbom, &rules);
/// assert!(score >= 0.0 && score <= 100.0);
/// ```
pub fn calculate_compliance_score(sbom: &Sbom, rules: &[ComplianceRule]) -> f64 {
    // Implementation
}
```

#### Error Handling
- Use `Result<T, E>` for functions that can fail
- Define custom error types using `thiserror`
- Use `anyhow` for application-level error handling
- Avoid `unwrap()` and `expect()` in production code

```rust
use thiserror::Error;

#[derive(Error, Debug)]
pub enum ComplianceError {
    #[error("Invalid SBOM format: {0}")]
    InvalidFormat(String),
    #[error("License conflict detected: {0}")]
    LicenseConflict(String),
    #[error("Network error: {0}")]
    Network(#[from] reqwest::Error),
}
```

### Code Quality Tools

#### Clippy
```bash
# Run clippy for code quality checks
cargo clippy

# Fix auto-fixable issues
cargo clippy --fix
```

#### Rust Analyzer
- Use VS Code with rust-analyzer extension
- Enable all available checks
- Configure for strict mode

## Testing

### Testing Strategy
- **Unit Tests:** Test individual functions and modules
- **Integration Tests:** Test component interactions
- **End-to-End Tests:** Test complete workflows
- **Performance Tests:** Benchmark critical paths

### Writing Tests
```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_calculate_compliance_score() {
        let sbom = create_test_sbom();
        let rules = vec![ComplianceRule::default()];
        let score = calculate_compliance_score(&sbom, &rules);
        assert!(score >= 0.0 && score <= 100.0);
    }

    #[test]
    fn test_license_detection() {
        let content = "MIT License\n\nCopyright (c) 2023";
        let license = detect_license(content);
        assert_eq!(license, Some("MIT".to_string()));
    }
}
```

### Running Tests
```bash
# Run all tests
cargo test

# Run specific test
cargo test test_calculate_compliance_score

# Run with coverage (requires tarpaulin)
cargo tarpaulin --out Html

# Run benchmarks
cargo bench
```

### Test Coverage Requirements
- Minimum 80% code coverage for new code
- Critical paths must have 90%+ coverage
- Integration tests for all major workflows

## CI/CD Pipeline

### GitHub Actions Workflow
The project uses GitHub Actions for continuous integration:

```yaml
# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
      - uses: actions-rs/cargo@v1
        with:
          command: test
          args: --all-features

  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
      - run: cargo fmt --check
      - run: cargo clippy -- -D warnings

  security:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
      - run: cargo audit
```

### Pipeline Stages
1. **Lint:** Code formatting and quality checks
2. **Test:** Unit and integration tests
3. **Security:** Vulnerability scanning
4. **Build:** Compile and package
5. **Deploy:** Automated deployment to staging/production

### Branch Protection Rules
- Require PR reviews (2 minimum)
- Require status checks to pass
- Require branches to be up to date
- Include administrators in restrictions

## Code Review Process

### Pull Request Guidelines
- Create descriptive PR titles and descriptions
- Reference related issues
- Include screenshots for UI changes
- Update documentation as needed

### Review Checklist
- [ ] Code compiles without warnings
- [ ] Tests pass and coverage maintained
- [ ] Documentation updated
- [ ] Security considerations addressed
- [ ] Performance impact assessed
- [ ] Breaking changes documented

### Review Process
1. **Automated Checks:** CI pipeline runs
2. **Self Review:** Author reviews their own code
3. **Peer Review:** 2+ reviewers provide feedback
4. **Approval:** Maintainers approve for merge
5. **Merge:** Squash merge with descriptive commit message

### Code Review Best Practices
- Focus on code quality, not style preferences
- Suggest improvements, don't demand changes
- Provide context for requested changes
- Be respectful and constructive
- Use GitHub's review tools effectively

## Architecture Guidelines

### Design Principles
- **Modularity:** Clear separation of concerns
- **Testability:** Dependency injection and mocking
- **Observability:** Comprehensive logging and metrics
- **Security:** Defense in depth approach
- **Performance:** Efficient algorithms and data structures

### Key Architectural Patterns
- **Hexagonal Architecture:** Clear boundaries between domains
- **CQRS:** Separate read and write models
- **Event Sourcing:** Immutable event logs
- **Saga Pattern:** Distributed transaction management

### API Design
- RESTful principles
- JSON API specification
- Versioned endpoints (`/api/v1/`)
- Consistent error responses
- Pagination for list endpoints

### Database Design
- Normalized schema
- Proper indexing
- Migration scripts
- Connection pooling
- Query optimization

### Security Considerations
- Input validation and sanitization
- Authentication and authorization
- Secure communication (TLS)
- Audit logging
- Regular security updates

## Contributing Guidelines

### Getting Started
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature`
3. Make your changes
4. Write tests
5. Update documentation
6. Submit a pull request

### Commit Message Format
```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Testing
- `chore`: Maintenance

### Issue Tracking
- Use GitHub Issues for bug reports and feature requests
- Follow issue templates
- Label issues appropriately
- Link PRs to issues

### Release Process
1. Create release branch from main
2. Update version in `Cargo.toml`
3. Update changelog
4. Create GitHub release
5. Deploy to production

### Communication
- Use GitHub Discussions for questions
- Join our Slack community
- Follow code of conduct
- Be respectful and inclusive

---

## Additional Resources

- [Rust Book](https://doc.rust-lang.org/book/)
- [Rust API Guidelines](https://rust-lang.github.io/api-guidelines/)
- [Tokio Documentation](https://tokio.rs/tokio/tutorial)
- [Axum Web Framework](https://docs.rs/axum/latest/axum/)
- [Diesel ORM](https://diesel.rs/guides/getting-started)

For questions or help, please reach out to the development team or create an issue on GitHub.