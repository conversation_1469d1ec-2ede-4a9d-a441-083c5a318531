# Frequently Asked Questions (FAQ)

This document addresses common questions about Infinitium Signal. If you don't find your answer here, please check the [Troubleshooting Guide](TROUBLESHOOTING.md) or create an issue on GitHub.

## Table of Contents
- [General](#general)
- [Installation](#installation)
- [Configuration](#configuration)
- [Features](#features)
- [Performance](#performance)
- [Security](#security)
- [Licensing](#licensing)
- [Integrations](#integrations)
- [Troubleshooting](#troubleshooting)

## General

### What is Infinitium Signal?

Infinitium Signal is a comprehensive software supply chain security and compliance platform that provides automated scanning, analysis, and reporting for software dependencies, licenses, vulnerabilities, and compliance requirements. It integrates blockchain technology for verifiable credentials and supports multiple compliance standards.

### What programming languages and package managers does it support?

Infinitium Signal supports:
- Rust (Cargo)
- JavaScript/Node.js (npm, yarn)
- Python (pip, poetry)
- Java (Maven, Gradle)
- Go (Go modules)
- .NET (NuGet)
- PHP (Composer)
- <PERSON> (Bundler)

### Is it open source?

Yes, Infinitium Signal is open source software licensed under the Apache License 2.0.

### How does it differ from other security scanners?

Infinitium Signal provides:
- Unified scanning across multiple languages
- Blockchain-based verifiable credentials
- Advanced compliance reporting (SPDX, CycloneDX)
- Machine learning-powered license detection
- Real-time monitoring and alerting
- Integration with major CI/CD platforms

## Installation

### What are the system requirements?

**Minimum Requirements:**
- Linux/Windows/macOS
- 4GB RAM
- 2GB disk space
- Rust 1.70+ (for building from source)

**Recommended Requirements:**
- Linux (Ubuntu 20.04+ or CentOS 7+)
- 8GB RAM
- 10GB disk space
- PostgreSQL 13+ or MySQL 8+
- Docker 20.10+

### Can I install it without Docker?

Yes, you can install it directly on your system:
1. Install Rust: `curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh`
2. Clone the repository: `git clone https://github.com/infinitium-signal/infinitium-signal`
3. Build: `cargo build --release`
4. Run: `./target/release/infinitium-signal`

### How do I update Infinitium Signal?

**Using Docker:**
```bash
docker pull infinitiumsignal/infinitium-signal:latest
docker-compose up -d
```

**From source:**
```bash
git pull origin main
cargo build --release
# Restart the service
```

### Can I run it on Windows?

Yes, but Linux is recommended for production deployments. On Windows:
1. Install Rust using rustup
2. Use WSL2 for better compatibility
3. Follow the standard installation guide

## Configuration

### How do I configure the database connection?

Create a `.env` file in the project root:

```env
DATABASE_URL=postgresql://user:password@localhost:5432/infinitium_signal
DB_MAX_CONNECTIONS=10
DB_TIMEOUT=30
```

For MySQL:
```env
DATABASE_URL=mysql://user:password@localhost:3306/infinitium_signal
```

### What are the available configuration options?

Key configuration options include:
- `API_PORT`: Server port (default: 8080)
- `LOG_LEVEL`: Logging verbosity (debug, info, warn, error)
- `SCAN_TIMEOUT`: Maximum scan duration in seconds
- `CACHE_TTL`: Cache expiration time
- `ENABLE_METRICS`: Enable Prometheus metrics
- `BLOCKCHAIN_ENABLED`: Enable blockchain integration

See `config.rs` for complete configuration options.

### How do I configure SSL/TLS?

1. Obtain SSL certificates
2. Place them in `certs/` directory
3. Update configuration:
```env
SSL_CERT_PATH=certs/server.crt
SSL_KEY_PATH=certs/server.key
API_PORT=8443
```

### Can I use environment variables for configuration?

Yes, all configuration options can be set via environment variables. Use uppercase with underscores:
```bash
export API_PORT=9090
export DATABASE_URL=postgresql://...
```

## Features

### What compliance standards does it support?

Infinitium Signal supports:
- SPDX (Software Package Data Exchange)
- CycloneDX
- OWASP Dependency Check format
- Custom compliance frameworks
- Regulatory requirements (GDPR, HIPAA, PCI-DSS)

### How does the blockchain integration work?

The blockchain integration provides:
- Verifiable credentials for scan results
- Immutable audit trails
- Decentralized trust establishment
- Integration with Hyperledger Fabric or Ethereum

### What types of scans does it perform?

- **License Scanning:** Detects open source licenses
- **Vulnerability Scanning:** Checks for known CVEs
- **Dependency Analysis:** Maps dependency trees
- **Compliance Checking:** Validates against standards
- **Code Quality Analysis:** Basic static analysis

### Can it scan private repositories?

Yes, with proper authentication:
- GitHub: Personal Access Token
- GitLab: Personal Access Token
- Bitbucket: App Password
- Azure DevOps: Personal Access Token

## Performance

### How fast is the scanning process?

Scan times vary by project size:
- Small projects (< 100 dependencies): 30-60 seconds
- Medium projects (100-1000 dependencies): 2-5 minutes
- Large projects (> 1000 dependencies): 5-15 minutes

Factors affecting performance:
- Network connectivity
- Package registry response times
- System resources
- Cache utilization

### Can it handle large codebases?

Yes, with optimizations:
- Parallel processing
- Incremental scanning
- Caching mechanisms
- Distributed scanning (enterprise feature)

### What's the maximum project size it can handle?

There's no hard limit, but performance considerations:
- 10,000+ dependencies: May require distributed setup
- Very large monorepos: Consider breaking into smaller scans
- Memory usage scales with dependency complexity

## Security

### How are API keys stored?

API keys are:
- Encrypted at rest using AES-256
- Hashed using bcrypt for authentication
- Rotated automatically every 90 days
- Audited for access patterns

### Does it store sensitive data?

Infinitium Signal:
- Does not store source code
- Anonymizes scan results
- Uses encrypted database connections
- Implements data retention policies
- Supports GDPR compliance features

### How does it handle vulnerabilities in dependencies?

- Real-time CVE monitoring
- Automated alerts for critical vulnerabilities
- Dependency update recommendations
- Risk scoring and prioritization
- Integration with vulnerability databases (NVD, OSV)

## Licensing

### What license is Infinitium Signal under?

Infinitium Signal is licensed under Apache License 2.0. See [LICENSE.md](LICENSE.md) for details.

### Can I use it for commercial purposes?

Yes, Apache License 2.0 allows commercial use, modification, distribution, and private use.

### Are there any usage restrictions?

No usage restrictions for the open source version. Enterprise features may have additional licensing terms.

### How does license detection work?

License detection uses:
- File pattern matching
- SPDX license list
- Machine learning classification
- Manual review workflows
- Confidence scoring

## Integrations

### Which CI/CD platforms are supported?

Supported CI/CD platforms:
- GitHub Actions
- GitLab CI/CD
- Jenkins
- Azure DevOps
- CircleCI
- Travis CI
- Custom webhooks

### How do I integrate with GitHub Actions?

Add to your workflow:

```yaml
- name: Security Scan
  uses: infinitium-signal/action@v1
  with:
    api-key: ${{ secrets.INFINITIUM_API_KEY }}
    project-path: .
```

### Can it integrate with Jira/ServiceNow?

Yes, through webhooks and REST APIs:
- Automated ticket creation for vulnerabilities
- Compliance report attachments
- Status synchronization
- Custom field mapping

### What monitoring systems does it support?

- Prometheus metrics collection
- Grafana dashboards
- ELK stack (Elasticsearch, Logstash, Kibana)
- Splunk
- Custom webhook integrations

## Troubleshooting

### Why are my scans failing?

Common reasons:
- Network connectivity issues
- Invalid authentication credentials
- Unsupported package manager
- Corrupted dependency files
- Resource limitations

Check the [Troubleshooting Guide](TROUBLESHOOTING.md) for detailed solutions.

### How do I debug slow performance?

1. Enable debug logging: `RUST_LOG=debug`
2. Check system resources
3. Review scan logs for bottlenecks
4. Use the performance monitoring dashboard
5. Consider cache configuration

### Can I run scans locally without internet?

Yes, with limitations:
- Offline license database required
- No real-time vulnerability checks
- Limited package registry access
- Use `--offline` flag

### Where can I find logs?

Logs are located in:
- Docker: `docker logs infinitium-signal`
- Systemd: `journalctl -u infinitium-signal`
- File: `logs/infinitium-signal.log`
- Kubernetes: `kubectl logs <pod-name>`

---

## Getting Help

If your question isn't answered here:
1. Check the [documentation](https://docs.infinitium-signal.com)
2. Search existing [GitHub issues](https://github.com/infinitium-signal/infinitium-signal/issues)
3. Create a new issue with detailed information
4. Contact <NAME_EMAIL>

For urgent security issues, email <EMAIL>