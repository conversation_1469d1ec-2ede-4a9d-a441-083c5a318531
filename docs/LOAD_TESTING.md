---
version: 0.1.0
last_updated: 2025-09-03
author: Infinitium Signal Team
status: active
---

# Load Testing Guide

This guide covers comprehensive load testing for Infinitium Signal, including test execution, result interpretation, and performance analysis.

## Table of Contents
- [Overview](#overview)
- [Prerequisites](#prerequisites)
- [Running Load Tests](#running-load-tests)
- [Test Scenarios](#test-scenarios)
- [Result Interpretation](#result-interpretation)
- [Performance Metrics](#performance-metrics)
- [Load Test Results Analysis](#load-test-results-analysis)
- [Troubleshooting](#troubleshooting)
- [Best Practices](#best-practices)

## Overview

Load testing evaluates how the Infinitium Signal platform performs under various load conditions, helping identify performance bottlenecks, scalability limits, and system stability issues.

### Types of Load Tests

1. **Health Check Tests** - Basic API availability and response times
2. **SBOM Upload Tests** - File upload performance and throughput
3. **Vulnerability Query Tests** - Database query performance under load
4. **Compliance Report Tests** - Resource-intensive report generation
5. **Stress Tests** - System behavior at breaking points
6. **Spike Tests** - Sudden load increases
7. **Endurance Tests** - Sustained load over extended periods

### Load Testing Goals

- **Performance Baseline** - Establish normal operating metrics
- **Scalability Limits** - Find maximum sustainable load
- **Bottleneck Identification** - Locate performance constraints
- **Stability Validation** - Ensure system reliability under load
- **Resource Planning** - Guide infrastructure scaling decisions

## Prerequisites

### System Requirements

- **Test Environment**: Isolated environment matching production
- **API Server**: Running Infinitium Signal instance
- **Database**: PostgreSQL with test data
- **Monitoring**: System monitoring tools (optional but recommended)
- **Network**: Stable connection between load generator and target

### Required Tools

- **curl**: HTTP client for API testing
- **jq**: JSON processing for response parsing
- **bc**: Calculator for performance calculations
- **gnuplot** or **matplotlib**: Result visualization (optional)

### Test Data Preparation

```bash
# Generate test data
mkdir -p load_test_results
cd load_test_results

# Create test SBOM
cat > test_sbom.json << 'EOF'
{
  "bomFormat": "CycloneDX",
  "specVersion": "1.4",
  "serialNumber": "urn:uuid:test-load-sbom",
  "version": 1,
  "metadata": {
    "timestamp": "2024-01-01T00:00:00Z",
    "tools": [{
      "vendor": "Infinitium Signal",
      "name": "Load Test Generator",
      "version": "1.0.0"
    }]
  },
  "components": [
    {
      "type": "library",
      "bom-ref": "pkg:cargo/serde@1.0.0",
      "name": "serde",
      "version": "1.0.0",
      "purl": "pkg:cargo/serde@1.0.0"
    }
  ]
}
EOF

# Create test compliance request
cat > test_compliance_request.json << 'EOF'
{
  "framework": "cert-in",
  "organization": "Load Test Corp",
  "scan_results": ["test-scan-id"]
}
EOF

# Create test scan request
cat > test_scan_request.json << 'EOF'
{
  "project_path": "/tmp/test-project",
  "scan_type": "sbom",
  "output_format": "cyclonedx"
}
EOF
```

## Running Load Tests

### Basic Load Test Execution

```bash
# Run load test with default settings (10 users, 60 seconds)
./scripts/load_test.sh

# Custom duration (5 minutes)
./scripts/load_test.sh 300

# Environment variable configuration
API_BASE_URL="http://localhost:8080" \
CONCURRENT_USERS=25 \
TEST_DURATION=180 \
./scripts/load_test.sh
```

### Advanced Load Testing

```bash
# High-load testing
CONCURRENT_USERS=50 TEST_DURATION=300 ./scripts/load_test.sh

# API endpoint testing
API_BASE_URL="https://api.infinitium-signal.com" \
CONCURRENT_USERS=25 \
TEST_DURATION=180 \
./scripts/load_test.sh

# Stress testing
CONCURRENT_USERS=100 TEST_DURATION=600 ./scripts/load_test.sh
```

### Load Test Script Options

| Variable | Description | Default | Range |
|----------|-------------|---------|-------|
| `API_BASE_URL` | Target API endpoint | `http://localhost:8080` | Any valid URL |
| `CONCURRENT_USERS` | Number of concurrent users | `10` | 1-1000 |
| `TEST_DURATION` | Test duration in seconds | `60` | 10-3600 |
| `RAMP_UP_TIME` | Ramp-up period in seconds | `10` | 0-300 |

## Test Scenarios

### 1. Health Check Load Test

**Purpose**: Test basic API availability and response times

**Configuration**:
```bash
# Simple health check
curl -s "$API_BASE_URL/health"

# Load test health endpoint
CONCURRENT_USERS=20 TEST_DURATION=120 ./scripts/load_test.sh
```

**Expected Results**:
- Response time < 100ms
- Success rate > 99.9%
- No memory leaks
- Consistent CPU usage

### 2. SBOM Upload Load Test

**Purpose**: Test file upload performance and API throughput

**Test Data**: Large SBOM files with multiple components

**Configuration**:
```bash
# Generate large test SBOM
python3 -c "
import json
components = []
for i in range(1000):
    components.append({
        'type': 'library',
        'name': f'package-{i}',
        'version': '1.0.0',
        'purl': f'pkg:npm/package-{i}@1.0.0'
    })

sbom = {
    'bomFormat': 'CycloneDX',
    'specVersion': '1.4',
    'components': components
}

with open('large_sbom.json', 'w') as f:
    json.dump(sbom, f, indent=2)
"
```

**Performance Targets**:
- Upload time < 5 seconds for 1000 components
- Throughput > 10 uploads/minute
- Memory usage < 500MB during uploads

### 3. Vulnerability Query Load Test

**Purpose**: Test database query performance under concurrent load

**Test Scenarios**:
- Simple queries (single severity filter)
- Complex queries (multiple filters)
- Large result sets (pagination testing)
- Cache performance testing

**Configuration**:
```bash
# Test different query patterns
QUERIES=(
  "severity=high&limit=10"
  "severity=critical&limit=100"
  "source=nvd&limit=50"
  "epss_score=0.8&limit=25"
)

for query in "${QUERIES[@]}"; do
  echo "Testing query: $query"
  CONCURRENT_USERS=15 TEST_DURATION=90 ./scripts/load_test.sh
done
```

### 4. Compliance Report Generation Load Test

**Purpose**: Test resource-intensive compliance report generation

**Test Data**: Complex compliance scenarios with multiple frameworks

**Configuration**:
```bash
# Test different compliance frameworks
FRAMEWORKS=("cert-in" "sebi" "iso27001" "nist" "pci-dss")

for framework in "${FRAMEWORKS[@]}"; do
  echo "Testing $framework compliance reports"
  # Modify test_compliance_request.json
  sed -i "s/\"framework\": \"[^\"]*\"/\"framework\": \"$framework\"/" test_compliance_request.json
  CONCURRENT_USERS=5 TEST_DURATION=300 ./scripts/load_test.sh
done
```

### 5. Stress Testing

**Purpose**: Find system breaking points and failure modes

**Methodology**:
- Start with normal load
- Gradually increase concurrent users
- Monitor for performance degradation
- Identify failure thresholds

**Configuration**:
```bash
# Automated stress test
MAX_USERS=200
STEP_SIZE=10

for ((users=10; users<=MAX_USERS; users+=STEP_SIZE)); do
  echo "Testing with $users concurrent users"
  CONCURRENT_USERS=$users TEST_DURATION=60 ./scripts/load_test.sh
  
  # Check if success rate drops below threshold
  if grep -q "Success Rate: [0-9]*\.[0-9]*%" load_test_results/*/health_check_results.txt; then
    success_rate=$(grep "Success Rate:" load_test_results/*/health_check_results.txt | tail -1 | grep -o "[0-9]*\.[0-9]*")
    if (( $(echo "$success_rate < 95.0" | bc -l) )); then
      echo "Breaking point found at $users users (Success rate: $success_rate%)"
      break
    fi
  fi
done
```

## Result Interpretation

### Load Test Results Structure

```
load_test_results/
└── YYYYMMDD_HHMMSS/
    ├── health_check_results.txt
    ├── sbom_upload_results.txt
    ├── vulnerability_query_results.txt
    ├── compliance_report_results.txt
    ├── stress_test_results.txt
    ├── sbom_upload_times.csv
    ├── vuln_query_times.csv
    ├── compliance_times.csv
    ├── test_sbom.json
    ├── test_scan_request.json
    └── test_compliance_request.json
```

### Key Metrics to Analyze

#### Response Time Metrics
```bash
# Calculate response time statistics
cat sbom_upload_times.csv | awk -F',' '
  NR>1 {
    times[NR] = $3
    sum += $3
    count++
    if ($3 > max) max = $3
    if ($3 < min || NR==2) min = $3
  }
  END {
    avg = sum/count
    print "Min:", min "s"
    print "Max:", max "s"
    print "Avg:", avg "s"
    print "Count:", count
  }
'
```

#### Success Rate Analysis
```bash
# Calculate success rates
grep "Success Rate:" *_results.txt | awk '
  {
    rate = $3
    sub(/%$/, "", rate)
    print FILENAME ": " rate "%"
  }
'
```

#### Throughput Calculation
```bash
# Calculate requests per second
awk -F',' '
  NR>1 {
    timestamp = $1
    if (NR == 2) start_time = timestamp
    end_time = timestamp
    count++
  }
  END {
    duration = end_time - start_time
    rps = count / duration
    print "Total Requests:", count
    print "Duration:", duration "s"
    print "Requests/sec:", rps
  }
' sbom_upload_times.csv
```

### Performance Thresholds

| Metric | Good | Warning | Critical |
|--------|------|---------|----------|
| Response Time (health) | < 100ms | 100-500ms | > 500ms |
| Response Time (upload) | < 2s | 2-10s | > 10s |
| Success Rate | > 99.9% | 95-99.9% | < 95% |
| CPU Usage | < 70% | 70-85% | > 85% |
| Memory Usage | < 80% | 80-90% | > 90% |
| Error Rate | < 0.1% | 0.1-1% | > 1% |

## Performance Metrics

### System Metrics

#### CPU Performance
- **User CPU**: Application processing time
- **System CPU**: Kernel operations time
- **I/O Wait**: Disk I/O waiting time
- **Idle Time**: Available processing capacity

#### Memory Metrics
- **Used Memory**: Currently allocated memory
- **Available Memory**: Free memory for allocation
- **Swap Usage**: Disk-based memory extension
- **Memory Pressure**: System memory contention

#### Disk I/O Metrics
- **Read/Write Operations**: I/O operation counts
- **Throughput**: Data transfer rates
- **Latency**: I/O operation response times
- **Queue Depth**: Pending I/O operations

#### Network Metrics
- **Bandwidth Usage**: Data transfer rates
- **Connection Count**: Active network connections
- **Error Rates**: Network transmission errors
- **Latency**: Network round-trip times

### Application Metrics

#### API Performance
- **Request Rate**: Requests per second
- **Response Time Distribution**: P50, P95, P99 latencies
- **Error Rates**: HTTP error response percentages
- **Throughput**: Successful requests per second

#### Database Performance
- **Query Response Times**: Database query latencies
- **Connection Pool Usage**: Database connection utilization
- **Lock Contention**: Database locking conflicts
- **Cache Hit Rates**: Database cache effectiveness

#### Resource Utilization
- **Memory Allocation**: Application memory usage patterns
- **CPU Utilization**: Application CPU consumption
- **Thread Count**: Active processing threads
- **File Descriptors**: Open file handles

## Load Test Results Analysis

### Automated Analysis Script

```bash
#!/bin/bash
# analyze_load_test_results.sh

RESULTS_DIR="${1:-load_test_results}"
LATEST_DIR=$(ls -td "$RESULTS_DIR"/*/ | head -1)

echo "Analyzing results from: $LATEST_DIR"

# Overall success rate
echo "=== OVERALL SUCCESS RATES ==="
grep "Success Rate:" "$LATEST_DIR"/*_results.txt | sort -V

# Response time analysis
echo -e "\n=== RESPONSE TIME ANALYSIS ==="
for csv_file in "$LATEST_DIR"/*.csv; do
  if [[ -f "$csv_file" ]]; then
    echo "File: $(basename "$csv_file")"
    awk -F',' '
      NR>1 {
        sum += $3
        count++
        times[count] = $3
        if ($3 > max) max = $3
        if ($3 < min || NR==2) min = $3
      }
      END {
        if (count > 0) {
          avg = sum/count
          # Simple percentile calculation
          asort(times)
          p50 = times[int(count*0.5)]
          p95 = times[int(count*0.95)]
          p99 = times[int(count*0.99)]
          
          print "  Count:", count
          print "  Min:", min "s"
          print "  Max:", max "s"
          print "  Avg:", avg "s"
          print "  P50:", p50 "s"
          print "  P95:", p95 "s"
          print "  P99:", p99 "s"
        }
      }
    ' "$csv_file"
    echo ""
  fi
done

# Error analysis
echo "=== ERROR ANALYSIS ==="
grep -h "ERROR" "$LATEST_DIR"/*.csv | wc -l | xargs echo "Total errors:"
grep -h "ERROR" "$LATEST_DIR"/*.csv | cut -d',' -f4 | sort | uniq -c | sort -nr
```

### Performance Visualization

```python
# load_test_visualization.py
import pandas as pd
import matplotlib.pyplot as plt
import glob
import os

def create_performance_charts(results_dir):
    """Create performance visualization charts"""
    
    # Find CSV files
    csv_files = glob.glob(os.path.join(results_dir, "*.csv"))
    
    for csv_file in csv_files:
        df = pd.read_csv(csv_file)
        
        # Create response time chart
        plt.figure(figsize=(12, 6))
        plt.plot(df['timestamp'], df['response_time'], 'b-', alpha=0.7)
        plt.title(f'Response Times - {os.path.basename(csv_file)}')
        plt.xlabel('Time')
        plt.ylabel('Response Time (s)')
        plt.grid(True, alpha=0.3)
        plt.savefig(f"{csv_file}_response_times.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        # Create histogram
        plt.figure(figsize=(10, 6))
        plt.hist(df['response_time'], bins=50, alpha=0.7, color='blue', edgecolor='black')
        plt.title(f'Response Time Distribution - {os.path.basename(csv_file)}')
        plt.xlabel('Response Time (s)')
        plt.ylabel('Frequency')
        plt.grid(True, alpha=0.3)
        plt.savefig(f"{csv_file}_histogram.png", dpi=300, bbox_inches='tight')
        plt.close()

if __name__ == "__main__":
    import sys
    results_dir = sys.argv[1] if len(sys.argv) > 1 else "load_test_results"
    latest_dir = max(glob.glob(os.path.join(results_dir, "*/")), key=os.path.getmtime)
    create_performance_charts(latest_dir)
```

### Comparative Analysis

```bash
# compare_load_tests.sh
#!/bin/bash

RESULTS_DIR="load_test_results"
echo "=== LOAD TEST COMPARISON ==="
echo "Test Date | Users | Duration | Success Rate | Avg Response"
echo "----------|-------|----------|--------------|-------------"

for test_dir in $(ls -d "$RESULTS_DIR"/*/ 2>/dev/null | sort); do
  test_name=$(basename "$test_dir")
  
  # Extract metrics
  users=$(grep "Concurrent Users:" "$test_dir"/*_results.txt 2>/dev/null | head -1 | grep -o "[0-9]*" || echo "N/A")
  duration=$(grep "Duration:" "$test_dir"/*_results.txt 2>/dev/null | head -1 | grep -o "[0-9]*" || echo "N/A")
  success_rate=$(grep "Success Rate:" "$test_dir"/*_results.txt 2>/dev/null | head -1 | grep -o "[0-9]*\.[0-9]*" || echo "N/A")
  
  # Calculate average response time
  avg_response="N/A"
  if [[ -f "$test_dir/sbom_upload_times.csv" ]]; then
    avg_response=$(awk -F',' 'NR>1 {sum+=$3; count++} END {if(count>0) printf "%.3f", sum/count}' "$test_dir/sbom_upload_times.csv")
  fi
  
  printf "%s | %s | %s | %s%% | %ss\n" "$test_name" "$users" "$duration" "$success_rate" "$avg_response"
done
```

## Troubleshooting

### Common Load Testing Issues

#### High Error Rates
```bash
# Check API server logs
tail -f /var/log/infinitium-signal/api.log

# Monitor system resources
top -b -n1 | head -20

# Check database connections
psql -h localhost -U infinitium -d infinitium_signal -c "SELECT count(*) FROM pg_stat_activity;"

# Network connectivity
curl -v --max-time 5 "$API_BASE_URL/health"
```

#### Memory Issues
```bash
# Monitor memory usage
free -h
vmstat 1 10

# Check for memory leaks
valgrind --tool=memcheck ./target/release/infinitum-signal 2>&1 | grep "definitely lost"

# Application memory profiling
curl "$API_BASE_URL/debug/pprof/heap"
```

#### Database Bottlenecks
```bash
# Check slow queries
psql -h localhost -U infinitium -d infinitium_signal -c "
SELECT query, total_time, calls, mean_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;
"

# Monitor connection pool
psql -h localhost -U infinitium -d infinitium_signal -c "
SELECT state, count(*)
FROM pg_stat_activity
GROUP BY state;
"
```

#### Network Issues
```bash
# Network interface statistics
ip -s link

# Connection tracking
netstat -tuln | wc -l

# Packet loss testing
ping -c 10 localhost

# Bandwidth testing
iperf -c localhost -t 10
```

### Load Test Validation

```bash
# Validate test environment
check_load_test_environment() {
  echo "=== LOAD TEST ENVIRONMENT VALIDATION ==="
  
  # Check API availability
  if curl -s "$API_BASE_URL/health" > /dev/null; then
    echo "✓ API server is accessible"
  else
    echo "✗ API server is not accessible"
    return 1
  fi
  
  # Check database connectivity
  if psql "$DATABASE_URL" -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✓ Database is accessible"
  else
    echo "✗ Database is not accessible"
    return 1
  fi
  
  # Check system resources
  memory_usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
  if [ "$memory_usage" -gt 90 ]; then
    echo "⚠ High memory usage: ${memory_usage}%"
  else
    echo "✓ Memory usage acceptable: ${memory_usage}%"
  fi
  
  cpu_usage=$(top -bn1 | grep "Cpu(s)" | sed "s/.*, *\([0-9.]*\)%* id.*/\1/" | awk '{print 100 - $1}')
  if (( $(echo "$cpu_usage > 90" | bc -l) )); then
    echo "⚠ High CPU usage: ${cpu_usage}%"
  else
    echo "✓ CPU usage acceptable: ${cpu_usage}%"
  fi
  
  echo "=== ENVIRONMENT VALIDATION COMPLETE ==="
}
```

## Best Practices

### Test Planning

1. **Define Clear Objectives**
   - What performance aspects to test
   - Success criteria and thresholds
   - Expected user load patterns

2. **Environment Preparation**
   - Use production-like environment
   - Ensure proper monitoring setup
   - Prepare realistic test data

3. **Test Data Management**
   - Use representative data sets
   - Avoid test data pollution
   - Plan for data cleanup

### Test Execution

1. **Gradual Load Increase**
   - Start with low concurrent users
   - Gradually increase load
   - Monitor system behavior

2. **Realistic Scenarios**
   - Mimic actual user behavior
   - Include think time between requests
   - Test various API endpoints

3. **Monitoring Strategy**
   - Monitor all system components
   - Capture detailed metrics
   - Set up alerts for critical thresholds

### Result Analysis

1. **Identify Bottlenecks**
   - Analyze response time distributions
   - Find resource utilization peaks
   - Correlate metrics with load levels

2. **Performance Baselines**
   - Establish normal operating ranges
   - Set performance budgets
   - Define acceptable degradation

3. **Actionable Insights**
   - Prioritize performance improvements
   - Plan infrastructure scaling
   - Guide optimization efforts

### Continuous Load Testing

1. **Automated Testing**
   - Integrate into CI/CD pipeline
   - Run regular performance tests
   - Monitor performance trends

2. **Performance Regression**
   - Compare results across versions
   - Set performance gates
   - Alert on significant changes

3. **Scalability Testing**
   - Test horizontal scaling
   - Validate auto-scaling
   - Plan capacity requirements

---

## Summary

Load testing is crucial for ensuring Infinitium Signal can handle production workloads effectively. This guide provides comprehensive coverage of:

- **Test Execution**: Running various load test scenarios
- **Result Analysis**: Interpreting performance metrics and identifying issues
- **Troubleshooting**: Diagnosing and resolving performance problems
- **Best Practices**: Planning and executing effective load testing strategies

Regular load testing helps maintain system performance, identify bottlenecks early, and ensure scalability as user demands grow.

For additional support or questions about load testing, contact the performance engineering team.