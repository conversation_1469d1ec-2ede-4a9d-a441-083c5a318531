# Monitoring Setup Guide

This guide covers setting up comprehensive monitoring for Infinitium Signal using Prometheus, Grafana, and related tools.

## Table of Contents
- [Prerequisites](#prerequisites)
- [Prometheus Setup](#prometheus-setup)
- [Grafana Setup](#grafana-setup)
- [Metrics Integration](#metrics-integration)
- [Alerting Rules](#alerting-rules)
- [Dashboards](#dashboards)
- [Monitoring Best Practices](#monitoring-best-practices)
- [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements
- **CPU:** 1 core minimum, 2+ cores recommended
- **Memory:** 2GB minimum, 4GB+ recommended
- **Storage:** 10GB minimum for metrics retention
- **Network:** Access to application endpoints

### Required Software
- **Prometheus:** 2.40.0+
- **Grafana:** 9.0.0+
- **Node Exporter:** For system metrics
- **Alertmanager:** For alert management
- **cAdvisor:** For container metrics (if using Docker/K8s)

### Ports and Endpoints
- Prometheus: `http://localhost:9090`
- <PERSON><PERSON>: `http://localhost:3000`
- Node Exporter: `http://localhost:9100`
- cAdvisor: `http://localhost:8080` (if used)
- Application Metrics: `http://localhost:8080/metrics`

## Prometheus Setup

### Installation
```bash
# Download and extract Prometheus
wget https://github.com/prometheus/prometheus/releases/download/v2.40.0/prometheus-2.40.0.linux-amd64.tar.gz
tar -xzf prometheus-2.40.0.linux-amd64.tar.gz
cd prometheus-2.40.0.linux-amd64

# Create directories
sudo mkdir -p /etc/prometheus /var/lib/prometheus
sudo useradd --no-create-home --shell /bin/false prometheus
sudo chown prometheus:prometheus /etc/prometheus /var/lib/prometheus

# Move binaries
sudo mv prometheus promtool /usr/local/bin/
sudo chown prometheus:prometheus /usr/local/bin/prometheus /usr/local/bin/promtool
```

### Configuration
```yaml
# /etc/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  scrape_timeout: 10s

rule_files:
  - "rules.yml"
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - localhost:9093

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'infinitium-signal'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s

  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']

  - job_name: 'cadvisor'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/metrics/cadvisor'
    scrape_interval: 15s
```

### Service Configuration
```ini
# /etc/systemd/system/prometheus.service
[Unit]
Description=Prometheus
Wants=network-online.target
After=network-online.target

[Service]
User=prometheus
Group=prometheus
Type=simple
ExecStart=/usr/local/bin/prometheus \
  --config.file /etc/prometheus/prometheus.yml \
  --storage.tsdb.path /var/lib/prometheus/ \
  --web.console.templates=/etc/prometheus/consoles \
  --web.console.libraries=/etc/prometheus/console_libraries \
  --storage.tsdb.retention.time=200h \
  --web.listen-address=0.0.0.0:9090

[Install]
WantedBy=multi-user.target
```

### Docker Setup
```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  node-exporter:
    image: prom/node-exporter:latest
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    restart: unless-stopped

volumes:
  prometheus_data:
```

## Grafana Setup

### Installation
```bash
# Add Grafana repository
sudo apt-get install -y apt-transport-https
sudo apt-get install -y software-properties-common wget
wget -q -O - https://packages.grafana.com/gpg.key | sudo apt-key add -

# Add repository
echo "deb https://packages.grafana.com/oss/deb stable main" | sudo tee -a /etc/apt/sources.list.d/grafana.list

# Install Grafana
sudo apt-get update
sudo apt-get install grafana

# Start and enable service
sudo systemctl start grafana-server
sudo systemctl enable grafana-server
```

### Docker Setup
```yaml
# grafana service in docker-compose
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    restart: unless-stopped

volumes:
  grafana_data:
```

### Configuration
```ini
# /etc/grafana/grafana.ini
[server]
http_port = 3000
domain = localhost

[security]
admin_user = admin
admin_password = admin

[users]
allow_sign_up = false

[auth.anonymous]
enabled = true
org_role = Viewer

[log]
mode = console
level = info

[metrics]
enabled = true
```

### Data Source Configuration
```yaml
# /etc/grafana/provisioning/datasources/prometheus.yml
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
```

## Metrics Integration

### Application Metrics
```rust
// In your Rust application
use prometheus::{Encoder, TextEncoder, register_counter, register_histogram};
use lazy_static::lazy_static;

lazy_static! {
    static ref HTTP_REQUESTS_TOTAL: Counter = register_counter!(
        "http_requests_total",
        "Total number of HTTP requests"
    ).expect("Can't create metric");

    static ref HTTP_REQUEST_DURATION: Histogram = register_histogram!(
        "http_request_duration_seconds",
        "HTTP request duration in seconds"
    ).expect("Can't create metric");
}

// In your request handler
async fn handle_request() {
    let timer = HTTP_REQUEST_DURATION.start_timer();
    HTTP_REQUESTS_TOTAL.inc();

    // Your request logic here

    timer.observe_duration();
}
```

### Custom Metrics
```rust
// Compliance scan metrics
static ref COMPLIANCE_SCANS_TOTAL: CounterVec = register_counter_vec!(
    "compliance_scans_total",
    "Total number of compliance scans",
    &["status", "scanner_type"]
).expect("Can't create metric");

static ref SCAN_DURATION: HistogramVec = register_histogram_vec!(
    "scan_duration_seconds",
    "Scan duration in seconds",
    &["scanner_type"]
).expect("Can't create metric");

// Usage
COMPLIANCE_SCANS_TOTAL.with_label_values(&["success", "license"]).inc();
let timer = SCAN_DURATION.with_label_values(&["license"]).start_timer();
// ... scan logic ...
timer.observe_duration();
```

### Metrics Endpoint
```rust
// In your main.rs or server setup
use prometheus::TextEncoder;

async fn metrics_handler() -> impl IntoResponse {
    let encoder = TextEncoder::new();
    let metric_families = prometheus::gather();
    let mut buffer = Vec::new();
    encoder.encode(&metric_families, &mut buffer).unwrap();
    (StatusCode::OK, [(header::CONTENT_TYPE, "text/plain; charset=utf-8")], buffer)
}

// Add to your router
let app = Router::new()
    .route("/metrics", get(metrics_handler))
    // ... other routes
```

## Alerting Rules

### Prometheus Alerting Rules
```yaml
# /etc/prometheus/alert_rules.yml
groups:
  - name: infinitium-signal
    rules:
      - alert: HighRequestLatency
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High request latency detected"
          description: "95th percentile request latency is {{ $value }}s"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | printf \"%.2f\" }}%"

      - alert: LowDiskSpace
        expr: (1 - node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Low disk space"
          description: "Disk usage is {{ $value | printf \"%.2f\" }}%"

      - alert: HighMemoryUsage
        expr: (1 - node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes) * 100 > 90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value | printf \"%.2f\" }}%"

      - alert: ServiceDown
        expr: up{job="infinitium-signal"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Infinitium Signal service is down"
          description: "Service has been down for more than 1 minute"

      - alert: DatabaseConnectionIssues
        expr: rate(database_connection_errors_total[5m]) > 5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Database connection issues detected"
          description: "Database connection errors: {{ $value }}"
```

### Alertmanager Configuration
```yaml
# /etc/alertmanager/alertmanager.yml
global:
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-password'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'email'
  routes:
    - match:
        severity: critical
      receiver: 'critical-email'

receivers:
  - name: 'email'
    email_configs:
      - to: '<EMAIL>'
        send_resolved: true

  - name: 'critical-email'
    email_configs:
      - to: '<EMAIL>'
        send_resolved: true
```

## Dashboards

### Main Overview Dashboard
```json
{
  "dashboard": {
    "title": "Infinitium Signal Overview",
    "tags": ["infinitium-signal", "overview"],
    "timezone": "UTC",
    "panels": [
      {
        "title": "HTTP Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{status}}"
          }
        ]
      },
      {
        "title": "Request Latency",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "System Resources",
        "type": "row",
        "panels": [
          {
            "title": "CPU Usage",
            "type": "graph",
            "targets": [
              {
                "expr": "100 - (avg by(instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
                "legendFormat": "CPU Usage %"
              }
            ]
          },
          {
            "title": "Memory Usage",
            "type": "graph",
            "targets": [
              {
                "expr": "(1 - node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes) * 100",
                "legendFormat": "Memory Usage %"
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### Compliance Dashboard
```json
{
  "dashboard": {
    "title": "Compliance Monitoring",
    "tags": ["infinitium-signal", "compliance"],
    "panels": [
      {
        "title": "Scan Success Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(compliance_scans_total{status=\"success\"}[1h]) / rate(compliance_scans_total[1h]) * 100",
            "legendFormat": "Success Rate %"
          }
        ]
      },
      {
        "title": "License Conflicts",
        "type": "table",
        "targets": [
          {
            "expr": "license_conflicts_total",
            "legendFormat": "License Conflicts"
          }
        ]
      },
      {
        "title": "Vulnerability Trends",
        "type": "graph",
        "targets": [
          {
            "expr": "vulnerabilities_found_total",
            "legendFormat": "Vulnerabilities Found"
          }
        ]
      }
    ]
  }
}
```

### Performance Dashboard
```json
{
  "dashboard": {
    "title": "Performance Metrics",
    "tags": ["infinitium-signal", "performance"],
    "panels": [
      {
        "title": "Scan Duration",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(scan_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile scan duration"
          }
        ]
      },
      {
        "title": "Database Query Performance",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(database_query_duration_seconds_sum[5m]) / rate(database_query_duration_seconds_count[5m])",
            "legendFormat": "Average query duration"
          }
        ]
      },
      {
        "title": "Cache Hit Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "cache_hits_total / (cache_hits_total + cache_misses_total) * 100",
            "legendFormat": "Cache Hit Rate %"
          }
        ]
      }
    ]
  }
}
```

## Monitoring Best Practices

### Metrics Collection
- **Use appropriate metric types:** Counters for events, gauges for states, histograms for distributions
- **Follow naming conventions:** `namespace_subsystem_metric_name`
- **Add relevant labels:** But avoid high cardinality
- **Document all metrics:** Include help text and units

### Alerting Strategy
- **Alert on symptoms, not causes:** Alert when user experience is impacted
- **Use appropriate severity levels:** Critical, warning, info
- **Set reasonable thresholds:** Based on historical data and business requirements
- **Include runbooks:** Document response procedures for each alert

### Dashboard Design
- **Keep it simple:** Focus on key metrics that drive decisions
- **Use consistent layouts:** Standardize across similar services
- **Include context:** Show related metrics together
- **Make it actionable:** Include links to relevant documentation or tools

### Data Retention
```yaml
# Prometheus configuration for retention
global:
  scrape_interval: 15s
  evaluation_interval: 15s

# Storage settings
storage:
  tsdb:
    retention:
      time: 30d  # Keep metrics for 30 days
      size: 100GB  # Limit storage size
```

### Backup and Recovery
```bash
# Backup Prometheus data
#!/bin/bash
BACKUP_DIR="/opt/monitoring/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Stop Prometheus
docker-compose stop prometheus

# Create backup
tar -czf $BACKUP_DIR/prometheus_backup_$DATE.tar.gz /opt/monitoring/prometheus/data/

# Start Prometheus
docker-compose start prometheus

echo "Backup completed: $BACKUP_DIR/prometheus_backup_$DATE.tar.gz"
```

## Troubleshooting

### Common Issues

#### Prometheus Not Scraping Metrics
```bash
# Check Prometheus targets
curl http://localhost:9090/api/v1/targets

# Test metrics endpoint
curl http://localhost:8080/metrics

# Check Prometheus logs
docker-compose logs prometheus
```

#### Grafana Not Connecting to Prometheus
```bash
# Test Prometheus connectivity
curl http://localhost:9090/api/v1/query?query=up

# Check Grafana logs
docker-compose logs grafana

# Verify data source configuration
curl -u admin:admin http://localhost:3000/api/datasources
```

#### High Cardinality Issues
```bash
# Check metric cardinality
curl http://localhost:9090/api/v1/query?query=count({__name__=~".+"}) by (__name__)

# Identify high cardinality metrics
curl http://localhost:9090/api/v1/query?query=count({__name__=~".+"}) by (__name__) | sort_desc
```

#### Alertmanager Not Sending Alerts
```bash
# Check Alertmanager status
curl http://localhost:9093/api/v2/status

# Test email configuration
curl -X POST http://localhost:9093/api/v2/alerts \
  -H "Content-Type: application/json" \
  -d '[{"labels":{"alertname":"TestAlert"},"annotations":{"summary":"Test"}}]'

# Check Alertmanager logs
docker-compose logs alertmanager
```

### Performance Optimization

#### Prometheus Optimization
```yaml
# Optimized Prometheus configuration
global:
  scrape_interval: 30s  # Increase interval to reduce load
  evaluation_interval: 30s

# Use remote write for long-term storage
remote_write:
  - url: "http://victoriametrics:8428/api/v1/write"
```

#### Grafana Optimization
```ini
# Grafana configuration for performance
[server]
router_logging = false

[log]
level = warn

[metrics]
enabled = false  # Disable Grafana metrics if not needed
```

#### Monitoring Stack Scaling
```yaml
# Horizontal scaling with Thanos
# Add to docker-compose
  thanos:
    image: quay.io/thanos/thanos:latest
    command:
      - "sidecar"
      - "--tsdb.path=/prometheus"
      - "--prometheus.url=http://prometheus:9090"
      - "--grpc-server-tls-*"
    ports:
      - "10901:10901"
    volumes:
      - prometheus_data:/prometheus
```

---

## Additional Resources

- [Prometheus Documentation](https://prometheus.io/docs/)
- [Grafana Documentation](https://grafana.com/docs/)
- [Alertmanager Documentation](https://prometheus.io/docs/alerting/latest/alertmanager/)
- [Monitoring Best Practices](https://prometheus.io/docs/practices/)
- [Grafana Dashboards](https://grafana.com/grafana/dashboards/)

For monitoring support, contact the DevOps team or create an issue in the repository.