---
version: 0.1.0
last_updated: 2025-09-03
author: Infinitium Signal Team
status: active
---

# Scripts Guide

This guide provides comprehensive usage instructions for all scripts in the `scripts/` directory of Infinitium Signal.

## Table of Contents
- [backup.sh](#backupsh)
- [demo.sh](#demosh)
- [load_test.sh](#load_testsh)
- [migrate.sh](#migratesh)
- [performance_dashboard.py](#performance_dashboardpy)
- [performance_monitor.sh](#performance_monitorsh)
- [setup.sh](#setupsh)
- [test_all.sh](#test_allsh)

## backup.sh

**Purpose:** Creates comprehensive backups of database, configuration, and application data.

**Location:** `scripts/backup.sh`

### Usage

```bash
# Basic usage - full backup
./scripts/backup.sh

# Backup specific components
./scripts/backup.sh --type database    # Database only
./scripts/backup.sh --type config      # Configuration only
./scripts/backup.sh --type data        # Application data only

# Advanced options
./scripts/backup.sh \
  --type full \
  --database-url "********************************/db" \
  --redis-url "redis://host:6379" \
  --output-dir "/path/to/backups" \
  --encrypt \
  --gpg-recipient "<EMAIL>" \
  --retention-days 30 \
  --verbose

# Dry run to see what would be backed up
./scripts/backup.sh --dry-run
```

### Options

| Option | Description | Default |
|--------|-------------|---------|
| `--type` | Backup type: `full`, `database`, `config`, `data` | `full` |
| `--database-url` | PostgreSQL connection URL | From `DATABASE_URL` env var |
| `--redis-url` | Redis connection URL | From `REDIS_URL` env var |
| `--output-dir` | Backup destination directory | `./backups` |
| `--no-compress` | Skip compression | `false` |
| `--encrypt` | Encrypt backup files | `false` |
| `--gpg-recipient` | GPG recipient for encryption | None |
| `--password` | Password for OpenSSL encryption | None |
| `--retention-days` | Keep backups for N days | `30` |
| `--dry-run` | Show what would be done | `false` |
| `--verbose` | Enable verbose logging | `false` |
| `--help` | Show help message | |

### Environment Variables

```bash
export DATABASE_URL="********************************/db"
export REDIS_URL="redis://host:6379"
export BACKUP_ROOT="/custom/backup/location"
export GPG_RECIPIENT="<EMAIL>"
export ENCRYPTION_PASSWORD="secure_password"
```

### Examples

#### Full Production Backup
```bash
./scripts/backup.sh \
  --type full \
  --database-url "*********************************************/infinitium_prod" \
  --redis-url "redis://prod-redis:6379" \
  --encrypt \
  --gpg-recipient "<EMAIL>" \
  --retention-days 90 \
  --verbose
```

#### Database-Only Backup
```bash
./scripts/backup.sh \
  --type database \
  --database-url "postgresql://backup_user:backup_pass@localhost:5432/infinitium" \
  --output-dir "/mnt/backup/database" \
  --encrypt \
  --password "my_secure_password"
```

#### Configuration Backup
```bash
./scripts/backup.sh \
  --type config \
  --output-dir "/secure/config-backups" \
  --encrypt \
  --gpg-recipient "<EMAIL>"
```

### Output Structure

```
backups/
└── 20231201_143022/
    ├── database.sql.gz.enc    # Encrypted compressed database
    ├── config.tar.gz.gpg      # Encrypted compressed config
    ├── data.tar.gz.enc        # Encrypted compressed data
    ├── manifest.txt           # Backup manifest
    └── backup.log             # Backup log
```

### Security Features

- **Encryption:** GPG or OpenSSL encryption support
- **Secure Permissions:** 700 permissions on backup directories
- **Automatic Cleanup:** Removes old backups based on retention policy
- **Verification:** Database connection testing before backup

## demo.sh

**Purpose:** Runs a comprehensive demonstration of all Infinitium Signal features.

**Location:** `scripts/demo.sh`

### Usage

```bash
# Basic demo run
./scripts/demo.sh

# Custom CLI binary location
CLI_BINARY="./target/debug/infinitum-signal-cli" ./scripts/demo.sh

# Custom API endpoint
API_BASE_URL="http://localhost:9090" ./scripts/demo.sh
```

### Prerequisites

- Built CLI binary (`./target/release/infinitum-signal-cli`)
- Running API server (`http://localhost:8080`)
- Demo data directory (`demo-data/`)

### Demo Flow

1. **Prerequisites Check**
   - Verifies CLI binary and API server
   - Creates demo data if missing

2. **SBOM Generation**
   - Scans Rust, Node.js, and Python projects
   - Generates CycloneDX and SPDX formats

3. **HBOM Generation**
   - Analyzes firmware binaries
   - Performs security analysis

4. **Vulnerability Assessment**
   - Multi-source vulnerability scanning (NVD, GitHub, OSV)
   - EPSS scoring integration

5. **Compliance Reports**
   - CERT-In, SEBI CSCRF, ISO 27001 reports
   - PDF and JSON formats

6. **Blockchain Audit Trail**
   - Scan result commitments
   - Merkle proof generation
   - Verifiable credentials

7. **API Integration**
   - REST API endpoint testing
   - SBOM upload and querying

8. **Monitoring & Security**
   - System metrics collection
   - Digital signature verification
   - Integrity checking

### Generated Artifacts

```
output/
├── sbom/                    # Software Bills of Materials
├── hbom/                    # Hardware Bills of Materials
├── vulnerabilities/         # Vulnerability assessments
├── compliance/              # Compliance reports (PDF, JSON)
├── blockchain/              # Blockchain records and proofs
├── reports/                 # Summary reports
└── encrypted/               # Encrypted sensitive data
```

### Environment Variables

```bash
export CLI_BINARY="./target/release/infinitum-signal-cli"
export API_BASE_URL="http://localhost:8080"
export DEMO_DIR="demo-data"
```

## load_test.sh

**Purpose:** Performs comprehensive API load testing with concurrent users and stress scenarios.

**Location:** `scripts/load_test.sh`

### Usage

```bash
# Basic load test (10 users, 60 seconds)
./scripts/load_test.sh

# Custom configuration
./scripts/load_test.sh 300  # 5 minutes duration

# Environment variable configuration
CONCURRENT_USERS=20 TEST_DURATION=120 ./scripts/load_test.sh
```

### Configuration Options

| Variable | Description | Default |
|----------|-------------|---------|
| `API_BASE_URL` | API endpoint URL | `http://localhost:8080` |
| `CONCURRENT_USERS` | Number of concurrent users | `10` |
| `TEST_DURATION` | Test duration in seconds | `60` |
| `RAMP_UP_TIME` | Ramp-up time in seconds | `10` |
| `OUTPUT_DIR` | Results output directory | `./load_test_results` |

### Test Scenarios

1. **Health Check Load Test**
   - Basic endpoint availability testing
   - Measures response times and success rates

2. **SBOM Upload Load Test**
   - Tests file upload performance
   - Measures upload throughput and latency

3. **Vulnerability Query Load Test**
   - Tests database query performance
   - Various severity filters and pagination

4. **Compliance Report Generation**
   - Tests report generation performance
   - Resource-intensive operations

5. **Stress Test**
   - Increasing load until failure
   - Determines system breaking points

### Output Files

```
load_test_results/YYYYMMDD_HHMMSS/
├── health_check_results.txt
├── sbom_upload_results.txt
├── vulnerability_query_results.txt
├── compliance_report_results.txt
├── stress_test_results.txt
├── sbom_upload_times.csv
├── vuln_query_times.csv
├── compliance_times.csv
├── test_sbom.json
├── test_scan_request.json
└── test_compliance_request.json
```

### Performance Metrics

- **Response Time:** Average, 95th percentile, max
- **Throughput:** Requests per second
- **Success Rate:** Percentage of successful requests
- **Error Rate:** Types and frequency of errors
- **Resource Usage:** CPU, memory, network during tests

### Examples

#### High-Load Testing
```bash
CONCURRENT_USERS=50 TEST_DURATION=300 ./scripts/load_test.sh
```

#### API Endpoint Testing
```bash
API_BASE_URL="https://api.infinitium-signal.com" \
CONCURRENT_USERS=25 \
TEST_DURATION=180 \
./scripts/load_test.sh
```

#### Stress Testing
```bash
# Test with increasing load
CONCURRENT_USERS=100 TEST_DURATION=600 ./scripts/load_test.sh
```

## migrate.sh

**Purpose:** Manages database schema migrations and data migrations.

**Location:** `scripts/migrate.sh`

### Usage

```bash
# Run all pending migrations
./scripts/migrate.sh up

# Rollback last migration
./scripts/migrate.sh down

# Reset database (drop all tables and re-run migrations)
./scripts/migrate.sh reset

# Show migration status
./scripts/migrate.sh status

# Create new migration
./scripts/migrate.sh create "add_user_preferences"

# Validate migration files
./scripts/migrate.sh validate
```

### Options

| Option | Description | Default |
|--------|-------------|---------|
| `--database-url` | Database connection URL | From `DATABASE_URL` env var |
| `--dry-run` | Show what would be done | `false` |
| `--force` | Force operation without confirmation | `false` |
| `--no-backup` | Skip backup before migration | `false` |
| `--verbose` | Enable verbose logging | `false` |

### Environment Variables

```bash
export DATABASE_URL="********************************/db"
export RUST_LOG="info"  # Logging level
```

### Migration Commands

#### Up Migration
```bash
./scripts/migrate.sh up --verbose
```
- Runs all pending migrations
- Creates backup before migration (unless `--no-backup`)
- Requires confirmation for production databases

#### Down Migration
```bash
./scripts/migrate.sh down --force
```
- Rolls back the last migration
- Creates backup before rollback
- Requires confirmation (unless `--force`)

#### Reset Database
```bash
./scripts/migrate.sh reset
```
- Drops all tables and data
- Re-runs all migrations from scratch
- Requires typing "RESET" for confirmation

#### Create Migration
```bash
./scripts/migrate.sh create "add_audit_logs_table"
```
- Creates new migration files
- Follows naming convention: `YYYYMMDD_HHMMSS_description`

#### Status Check
```bash
./scripts/migrate.sh status
```
- Shows applied and pending migrations
- Displays migration timestamps
- Indicates current database version

### Safety Features

- **Automatic Backups:** Before destructive operations
- **Confirmation Prompts:** For dangerous operations
- **Dry Run Mode:** Preview changes without executing
- **Transaction Safety:** All migrations run in transactions
- **Rollback Support:** Easy reversal of changes

## performance_dashboard.py

**Purpose:** Generates performance visualizations from monitoring data.

**Location:** `scripts/performance_dashboard.py`

### Usage

```bash
# Basic usage
python3 scripts/performance_dashboard.py results_dir

# Example
python3 scripts/performance_dashboard.py ./performance_results/20231201_143000
```

### Prerequisites

- Python 3.7+
- Required packages: `matplotlib`, `pandas`
- Performance monitoring data from `performance_monitor.sh`

### Input Data Format

The script expects CSV files in the results directory:

```
results_dir/
├── cpu/
│   ├── load_average.csv     # timestamp,load_1m,load_5m,load_15m
│   └── ...
├── memory/
│   ├── memory_usage.csv     # timestamp,total,used,free,available
│   └── ...
└── system/
    └── system_info.txt
```

### Generated Outputs

```
results_dir/visualizations/
├── cpu_load.png            # CPU load average chart
├── memory_usage.png        # Memory usage chart
├── performance_stats.json  # Statistical summary
└── performance_report.html # Complete HTML report
```

### Features

- **CPU Metrics:** Load average over time
- **Memory Metrics:** Usage, available memory trends
- **Statistical Analysis:** Min, max, average values
- **HTML Report:** Interactive performance dashboard
- **PNG Charts:** High-resolution visualizations

### Example Output

```json
{
  "cpu": {
    "avg_load_1m": 2.34,
    "max_load_1m": 5.67,
    "avg_load_5m": 2.12,
    "max_load_5m": 4.89
  },
  "memory": {
    "total_gb": 16.0,
    "avg_used_gb": 8.45,
    "max_used_gb": 12.34,
    "avg_utilization_percent": 52.8
  }
}
```

## performance_monitor.sh

**Purpose:** Captures comprehensive system metrics during benchmark execution.

**Location:** `scripts/performance_monitor.sh`

### Usage

```bash
# Basic monitoring (5 minutes)
./scripts/performance_monitor.sh

# Custom duration
./scripts/performance_monitor.sh 600  # 10 minutes

# Custom output directory
OUTPUT_DIR="/custom/path" ./scripts/performance_monitor.sh 300
```

### Configuration

| Variable | Description | Default |
|----------|-------------|---------|
| `MONITOR_DURATION` | Monitoring duration in seconds | `300` |
| `OUTPUT_DIR` | Results output directory | `./performance_results` |

### Monitored Metrics

#### CPU Metrics
- Overall CPU usage (`iostat`)
- Per-core CPU usage (`mpstat`)
- CPU load average
- Top CPU-consuming processes

#### Memory Metrics
- Memory usage over time
- Per-process memory consumption
- Swap usage
- Memory pressure indicators

#### Disk I/O Metrics
- Disk I/O statistics (`iostat`)
- Disk usage by mount point
- I/O wait times
- Read/write throughput

#### Network Metrics
- Interface statistics
- Network connections
- Bandwidth usage
- Packet loss/errors

#### Docker Metrics (if available)
- Container resource usage
- Docker daemon statistics

#### Application Metrics
- Infinitium Signal process monitoring
- PostgreSQL process monitoring
- Custom application metrics

### Output Structure

```
performance_results/YYYYMMDD_HHMMSS/
├── cpu/
│   ├── iostat_cpu.log
│   ├── mpstat_cores.log
│   ├── load_average.csv
│   └── top_processes.log
├── memory/
│   ├── memory_usage.csv
│   ├── process_memory.log
│   └── swap_usage.csv
├── disk/
│   ├── iostat_disk.log
│   ├── disk_usage.csv
│   └── iowait.csv
├── network/
│   ├── interface_stats.csv
│   └── connections.log
├── docker/
│   └── container_stats.csv
├── system/
│   ├── system_info.txt
│   ├── infinitum_signal_processes.csv
│   └── postgres_stats.csv
└── visualizations/  # Generated by performance_dashboard.py
```

### Dependencies

- `iostat` and `mpstat` from `sysstat` package
- `ps`, `free`, `df`, `netstat` (standard Unix tools)
- `docker` (optional, for container monitoring)

### Usage Examples

#### Benchmark Monitoring
```bash
# Start monitoring in background
./scripts/performance_monitor.sh 1800 &

# Run your benchmark
cargo bench

# Generate visualizations
python3 scripts/performance_dashboard.py ./performance_results/$(ls -t performance_results | head -1)
```

#### Production Monitoring
```bash
# Monitor production system for 1 hour
MONITOR_DURATION=3600 ./scripts/performance_monitor.sh
```

## setup.sh

**Purpose:** Complete setup script for development and production environments.

**Location:** `scripts/setup.sh`

### Usage

```bash
# Run complete setup
./scripts/setup.sh

# The script will:
# - Detect OS and package manager
# - Install system dependencies
# - Install Rust and development tools
# - Setup PostgreSQL and Redis
# - Configure monitoring stack
# - Build the project
# - Generate demo data
```

### What It Installs

#### System Dependencies
- Build tools (gcc, make, pkg-config)
- SSL libraries (openssl-dev)
- Database clients (postgresql-client)
- Development tools (git, curl, wget)

#### Rust Ecosystem
- Rust toolchain (stable)
- Cargo components (clippy, rustfmt)
- Development tools (cargo-watch, diesel_cli, etc.)

#### Security Tools
- Trivy (container scanning)
- Syft (SBOM generation)
- Grype (vulnerability scanning)
- ScanCode (license scanning)

#### Infrastructure
- PostgreSQL database
- Redis cache
- Docker and Docker Compose
- Monitoring stack (Prometheus, Grafana)

### Environment Setup

The script creates:
- Project directory structure
- Environment configuration (`.env`)
- Database and user setup
- Monitoring configuration
- Demo data for testing

### Post-Setup Steps

After running setup, you should:
1. Edit `.env` with your configuration
2. Run database migrations: `./scripts/migrate.sh up`
3. Start the application: `cargo run`
4. Run the demo: `./scripts/demo.sh`

## test_all.sh

**Purpose:** Comprehensive test runner for unit, integration, security, and performance tests.

**Location:** `scripts/test_all.sh`

### Usage

```bash
# Run all tests
./scripts/test_all.sh

# Run specific test types
./scripts/test_all.sh --unit-only
./scripts/test_all.sh --integration-only
./scripts/test_all.sh --security-only

# Advanced options
./scripts/test_all.sh \
  --with-performance \
  --no-coverage \
  --sequential
```

### Test Types

#### Unit Tests
- Individual function and module testing
- Mocked dependencies
- Fast execution (< 30 seconds)

#### Integration Tests
- Component interaction testing
- Database integration
- External service dependencies

#### Security Tests
- Dependency vulnerability scanning (`cargo audit`)
- License compliance checking
- Static security analysis

#### Performance Tests
- Benchmark execution
- Memory leak detection
- Load testing integration

### Options

| Option | Description | Default |
|--------|-------------|---------|
| `--unit-only` | Run only unit tests | `false` |
| `--integration-only` | Run only integration tests | `false` |
| `--security-only` | Run only security tests | `false` |
| `--with-performance` | Include performance tests | `false` |
| `--no-coverage` | Skip coverage generation | `false` |
| `--sequential` | Run tests sequentially | `false` |

### Output

```
target/test-results/
├── test.log              # Complete test log
└── coverage/
    ├── index.html        # HTML coverage report
    └── ...               # Additional coverage files
```

### Coverage Requirements

- Minimum 80% code coverage
- Critical paths: 90%+ coverage
- Integration tests for all major workflows

### CI/CD Integration

The script is designed for CI/CD pipelines:

```yaml
# GitHub Actions example
- name: Run Tests
  run: ./scripts/test_all.sh --with-performance
- name: Upload Coverage
  uses: codecov/codecov-action@v3
  with:
    file: ./target/coverage/index.html
```

### Test Environment

The script automatically:
- Sets up test database (if Docker available)
- Configures test environment variables
- Manages test parallelism
- Generates coverage reports
- Cleans up test resources

### Exit Codes

- `0`: All tests passed
- `1`: Some tests failed
- Other: Script execution error

---

## Common Usage Patterns

### Development Workflow
```bash
# Initial setup
./scripts/setup.sh

# Development loop
cargo build
./scripts/test_all.sh --unit-only
cargo run

# Before commit
./scripts/test_all.sh
./scripts/demo.sh
```

### Production Deployment
```bash
# Backup before deployment
./scripts/backup.sh --type full

# Deploy new version
# ... deployment steps ...

# Run integration tests
./scripts/test_all.sh --integration-only

# Monitor performance
./scripts/performance_monitor.sh 300 &
```

### Troubleshooting
```bash
# Check system status
./scripts/demo.sh  # Will verify all components

# Run diagnostics
./scripts/test_all.sh --verbose

# Performance analysis
./scripts/performance_monitor.sh 600
python3 scripts/performance_dashboard.py ./performance_results/latest
```

For additional help with any script, use the `--help` option or check the script's header comments.