# Troubleshooting Guide

This guide helps you diagnose and resolve common issues with Infinitium Signal.

## Table of Contents
- [Common Issues](#common-issues)
- [API Errors](#api-errors)
- [Deployment Problems](#deployment-problems)
- [Performance Issues](#performance-issues)
- [Debugging Guides](#debugging-guides)
- [Support Contacts](#support-contacts)

## Common Issues

### Application Won't Start

**Symptoms:** Error messages during startup, service fails to bind to port.

**Possible Causes:**
- Port already in use
- Invalid configuration
- Missing dependencies
- Database connection issues

**Solutions:**
1. Check if port 8080 is available: `netstat -tlnp | grep 8080`
2. Verify configuration file exists and is valid JSON/YAML
3. Ensure all required environment variables are set
4. Check database connectivity: `cargo run --bin check-db`
5. Review startup logs for detailed error messages

### High Memory Usage

**Symptoms:** Application consumes excessive RAM, leading to OOM kills.

**Possible Causes:**
- Memory leaks in application code
- Large dataset processing
- Inefficient data structures

**Solutions:**
1. Monitor memory usage: `docker stats` or `kubectl top pods`
2. Enable memory profiling: Set `RUST_BACKTRACE=1` and `RUST_LOG=trace`
3. Check for memory leaks using `valgrind` in development
4. Adjust heap size limits in Docker/Kubernetes configs
5. Optimize data processing pipelines

### License Detection Failures

**Symptoms:** Scans complete but license information is missing or incorrect.

**Possible Causes:**
- Outdated license database
- Unsupported file formats
- Network connectivity issues for online license checks

**Solutions:**
1. Update license database: `./scripts/update-licenses.sh`
2. Check supported file types in configuration
3. Verify internet connectivity for OSI license API
4. Review scan logs for specific file failures

## API Errors

### 500 Internal Server Error

**Symptoms:** Server returns 500 status code with generic error message.

**Debugging Steps:**
1. Check application logs for stack traces
2. Verify request payload format and size
3. Ensure all required dependencies are installed
4. Test with minimal request to isolate issue
5. Check database connectivity and health

**Common Causes:**
- Database connection timeout
- Invalid JSON in request body
- Missing required fields
- Internal service communication failures

### 400 Bad Request

**Symptoms:** Client receives 400 status with validation errors.

**Solutions:**
1. Validate request against API documentation in `docs/API.md`
2. Check Content-Type header (should be `application/json`)
3. Ensure required fields are present and correctly formatted
4. Use API testing tools like curl or Postman for validation
5. Check request size limits

### 401 Unauthorized

**Symptoms:** Authentication failures.

**Solutions:**
1. Verify API key is provided in `Authorization` header
2. Check API key format and validity
3. Ensure correct authentication method (Bearer token)
4. Review authentication logs for specific errors

### 403 Forbidden

**Symptoms:** Access denied to resources.

**Solutions:**
1. Check user permissions and roles
2. Verify API key has required scopes
3. Ensure resource ownership
4. Review access control policies

## Deployment Problems

### Docker Container Fails to Start

**Symptoms:** Container exits immediately after start.

**Solutions:**
1. Check container logs: `docker logs <container_id>`
2. Verify environment variables are set correctly
3. Ensure volumes are mounted with proper permissions
4. Check Docker image build process and base image
5. Validate entrypoint script: `./docker/docker-entrypoint.sh`

### Kubernetes Pod Crashes

**Symptoms:** Pods in CrashLoopBackOff or Error state.

**Solutions:**
1. Check pod logs: `kubectl logs <pod_name> -n <namespace>`
2. Verify ConfigMaps and Secrets are correctly mounted
3. Check resource limits and requests in deployment.yaml
4. Ensure service accounts have proper RBAC permissions
5. Validate liveness and readiness probes

### Database Migration Failures

**Symptoms:** Application fails during startup with migration errors.

**Solutions:**
1. Check database connectivity and credentials
2. Verify migration files are present and valid
3. Run migrations manually: `cargo run --bin migrate`
4. Check database schema compatibility
5. Review migration logs for specific SQL errors

## Performance Issues

### Slow Response Times

**Symptoms:** API calls take longer than expected (>5 seconds).

**Solutions:**
1. Enable performance monitoring via `/metrics` endpoint
2. Check database query performance with EXPLAIN plans
3. Profile application code using `cargo flamegraph`
4. Optimize I/O operations and reduce blocking calls
5. Implement caching for frequently accessed data
6. Consider horizontal scaling

### High CPU Usage

**Symptoms:** CPU utilization consistently above 80%.

**Solutions:**
1. Profile CPU usage: `perf record -g ./target/release/infinitium-signal`
2. Identify CPU-intensive operations in code
3. Optimize algorithms and data structures
4. Use async processing for I/O operations
5. Implement rate limiting for resource-intensive endpoints
6. Scale vertically or horizontally as needed

### Memory Leaks

**Symptoms:** Memory usage grows over time without release.

**Solutions:**
1. Use memory profiling tools: `cargo build --release && valgrind ./target/release/infinitium-signal`
2. Check for circular references in data structures
3. Implement proper resource cleanup in async tasks
4. Monitor garbage collection patterns
5. Use bounded channels and queues to prevent unbounded growth

## Debugging Guides

### Enabling Debug Logging

1. Set environment variables:
   ```bash
   export RUST_LOG=debug
   export RUST_BACKTRACE=1
   ```
2. Restart the application
3. Monitor logs for detailed information
4. Use structured logging for better searchability

### Using the Health Check Endpoint

Access the `/health` endpoint to verify service status:
```bash
curl http://localhost:8080/health
```

Expected response:
```json
{
  "status": "healthy",
  "version": "1.2.0",
  "uptime": "2h 30m"
}
```

### Collecting Diagnostic Information

Run the diagnostic collection script:
```bash
./scripts/diagnostics.sh
```

This will collect:
- System information
- Application logs
- Configuration files (sanitized)
- Performance metrics
- Database connection status

### Remote Debugging

For production issues:
1. Enable remote debugging in configuration
2. Use `kubectl port-forward` for Kubernetes deployments
3. Connect debugger to exposed port
4. Set breakpoints in critical code paths

## Support Contacts

- **Primary Support:** <EMAIL>
- **Security Issues:** <EMAIL> (for vulnerability reports)
- **GitHub Issues:** https://github.com/infinitium-signal/infinitium-signal/issues
- **Documentation:** https://docs.infinitium-signal.com
- **Community Forum:** https://forum.infinitium-signal.com
- **Slack Channel:** #infinitium-signal-support

### Escalation Process
1. Check existing GitHub issues for similar problems
2. Create a new issue with detailed information
3. Include diagnostic information and logs
4. For urgent production issues, email <EMAIL>

---

If this guide doesn't resolve your issue, please provide the following information when seeking help:
- Infinitium Signal version
- Operating system and version
- Deployment method (Docker, Kubernetes, bare metal)
- Relevant configuration (sanitized)
- Full error messages and stack traces
- Steps to reproduce the issue