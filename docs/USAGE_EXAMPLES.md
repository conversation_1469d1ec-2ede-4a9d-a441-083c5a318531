# Infinitium Signal - Comprehensive Usage Examples

## 📚 Table of Contents

- [Quick Start Examples](#-quick-start-examples)
- [Complete Workflows](#-complete-workflows)
- [Industry-Specific Examples](#-industry-specific-examples)
- [Integration Examples](#-integration-examples)
- [Advanced Scenarios](#-advanced-scenarios)
- [Troubleshooting Examples](#-troubleshooting-examples)

---

## 🚀 Quick Start Examples

### 1. Basic SBOM Generation

#### Generate SBOM for a Rust Project
```bash
# Simple SBOM generation
infinitum-signal scan sbom --target ./my-rust-project --format cyclonedx

# Output: sbom_12345678-1234-1234-1234-123456789abc.json
```

#### Generate SBOM for a Node.js Project
```bash
# Include development dependencies
infinitum-signal scan sbom \
  --target ./my-node-app \
  --format spdx \
  --include-dev \
  --depth 5
```

#### Generate SBOM for a Python Project
```bash
# Custom output directory
infinitum-signal scan sbom \
  --target ./my-python-app \
  --format cyclonedx \
  --output ./reports/sbom
```

### 2. Vulnerability Assessment

#### Basic Vulnerability Scan
```bash
# Assess vulnerabilities from SBOM
infinitum-signal vuln assess \
  --sbom ./sbom.json \
  --sources nvd,snyk \
  --severity-threshold medium
```

#### Advanced Vulnerability Assessment
```bash
# Full vulnerability assessment with all sources
infinitum-signal vuln assess \
  --target ./source-code \
  --sources nvd,snyk,github,osv \
  --severity-threshold high \
  --include-epss \
  --output ./security-reports
```

### 3. Compliance Reporting

#### Generate CERT-In Compliance Report
```bash
# CERT-In compliance for Indian organizations
infinitum-signal compliance generate \
  --framework cert-in \
  --organization "My Indian Company" \
  --scan-results ./scan-results \
  --format pdf \
  --executive-summary
```

#### Generate SEBI CSCRF Report
```bash
# SEBI compliance for financial services
infinitum-signal compliance generate \
  --framework sebi \
  --organization "Financial Corp" \
  --scan-results ./compliance-data \
  --format pdf
```

---

## 🔄 Complete Workflows

### Workflow 1: Development to Production Security Pipeline

```bash
#!/bin/bash
# Complete CI/CD security pipeline

PROJECT_PATH="./my-web-application"
OUTPUT_DIR="./security-reports"

echo "🔍 Starting comprehensive security assessment..."

# 1. Generate SBOM
echo "📦 Generating Software Bill of Materials..."
SBOM_FILE=$(infinitum-signal scan sbom \
  --target $PROJECT_PATH \
  --format cyclonedx \
  --include-dev \
  --output $OUTPUT_DIR | grep "SBOM saved to" | cut -d' ' -f4)

# 2. Assess vulnerabilities
echo "🛡️ Assessing vulnerabilities..."
VULN_FILE=$(infinitum-signal vuln assess \
  --sbom $SBOM_FILE \
  --sources nvd,snyk,github \
  --severity-threshold medium \
  --include-epss \
  --output $OUTPUT_DIR | grep "assessment saved to" | cut -d' ' -f4)

# 3. Generate compliance report
echo "📋 Generating compliance report..."
REPORT_FILE=$(infinitum-signal compliance generate \
  --framework cert-in \
  --organization "My Company" \
  --scan-results $OUTPUT_DIR \
  --format pdf \
  --executive-summary \
  --output $OUTPUT_DIR | grep "saved to" | cut -d' ' -f4)

# 4. Commit to blockchain for audit trail
echo "⛓️ Committing to blockchain..."
infinitum-signal blockchain commit \
  --data-type compliance-report \
  --data $REPORT_FILE \
  --metadata '{"pipeline": "ci-cd", "commit": "'$CI_COMMIT_SHA'"}'

echo "✅ Security assessment complete!"
echo "📊 Results:"
echo "  • SBOM: $SBOM_FILE"
echo "  • Vulnerabilities: $VULN_FILE"
echo "  • Compliance Report: $REPORT_FILE"
```

### Workflow 2: Multi-Framework Compliance Assessment

```bash
#!/bin/bash
# Multi-framework compliance assessment

PROJECT_NAME="Enterprise Web App"
SCAN_RESULTS="./scan-results"
REPORTS_DIR="./compliance-reports"

FRAMEWORKS=("cert-in" "sebi" "iso27001" "soc2")

echo "🏢 Starting multi-framework compliance assessment for $PROJECT_NAME..."

# Generate reports for all frameworks
for framework in "${FRAMEWORKS[@]}"; do
    echo "📋 Generating $framework compliance report..."

    case $framework in
        "cert-in")
            ORG_TYPE="Technology Company"
            ;;
        "sebi")
            ORG_TYPE="Financial Services"
            ;;
        "iso27001")
            ORG_TYPE="Information Technology"
            ;;
        "soc2")
            ORG_TYPE="Software as a Service"
            ;;
    esac

    infinitum-signal compliance generate \
        --framework $framework \
        --organization "$ORG_TYPE" \
        --scan-results $SCAN_RESULTS \
        --format pdf \
        --executive-summary \
        --output $REPORTS_DIR

    echo "✅ $framework report generated"
done

# Generate summary report
echo "📊 Generating compliance summary..."
infinitum-signal compliance generate \
    --framework cert-in \
    --organization "Multi-Framework Assessment" \
    --scan-results $SCAN_RESULTS \
    --format html \
    --executive-summary \
    --output $REPORTS_DIR

echo "🎉 Multi-framework compliance assessment complete!"
echo "📁 Reports available in: $REPORTS_DIR"
```

### Workflow 3: Real-time Monitoring and Alerting

```bash
#!/bin/bash
# Real-time security monitoring

MONITOR_INTERVAL=300  # 5 minutes
ALERT_THRESHOLD=5     # Critical vulnerabilities threshold

echo "👀 Starting real-time security monitoring..."

while true; do
    echo "$(date): Running security scan..."

    # Generate fresh SBOM
    SBOM_FILE=$(infinitum-signal scan sbom \
        --target ./live-application \
        --format cyclonedx \
        --output ./monitoring | grep "saved to" | cut -d' ' -f4)

    # Assess vulnerabilities
    VULN_FILE=$(infinitum-signal vuln assess \
        --sbom $SBOM_FILE \
        --sources nvd,snyk \
        --severity-threshold critical \
        --output ./monitoring | grep "saved to" | cut -d' ' -f4)

    # Check for critical vulnerabilities
    CRITICAL_COUNT=$(jq '.vulnerabilities | map(select(.severity == "critical")) | length' $VULN_FILE)

    if [ "$CRITICAL_COUNT" -gt "$ALERT_THRESHOLD" ]; then
        echo "🚨 ALERT: $CRITICAL_COUNT critical vulnerabilities detected!"

        # Generate emergency report
        infinitum-signal compliance generate \
            --framework cert-in \
            --organization "Security Monitoring Alert" \
            --scan-results ./monitoring \
            --format pdf \
            --output ./alerts

        # Send notification (implement your notification logic)
        echo "📧 Security alert sent to security team"
    else
        echo "✅ Security status: $CRITICAL_COUNT critical vulnerabilities (within threshold)"
    fi

    # Wait for next scan
    echo "⏰ Next scan in $MONITOR_INTERVAL seconds..."
    sleep $MONITOR_INTERVAL
done
```

---

## 🏭 Industry-Specific Examples

### Financial Services (SEBI Compliance)

```bash
#!/bin/bash
# SEBI CSCRF v2.0 compliance workflow

# 1. Comprehensive SBOM generation
infinitum-signal scan sbom \
    --target ./trading-platform \
    --format cyclonedx \
    --include-dev \
    --depth 10

# 2. Multi-source vulnerability assessment
infinitum-signal vuln assess \
    --sbom ./sbom.json \
    --sources nvd,snyk,github,osv \
    --severity-threshold high \
    --include-epss

# 3. SEBI CSCRF compliance report
infinitum-signal compliance generate \
    --framework sebi \
    --organization "Financial Technology Ltd" \
    --scan-results ./scan-results \
    --format pdf \
    --executive-summary

# 4. Blockchain audit trail
infinitum-signal blockchain commit \
  --data-type compliance-report \
  --data ./compliance-report.pdf \
  --metadata '{"framework": "sebi-cscrf", "quarter": "Q3-2025"}'
```

### Healthcare (HIPAA Compliance)

```bash
#!/bin/bash
# HIPAA compliance workflow

# 1. Medical application SBOM
infinitum-signal scan sbom \
    --target ./ehr-system \
    --format spdx \
    --include-dev

# 2. Healthcare-focused vulnerability assessment
infinitum-signal vuln assess \
    --sbom ./sbom.json \
    --sources nvd,snyk \
    --severity-threshold medium

# 3. HIPAA compliance report
infinitum-signal compliance generate \
    --framework hipaa \
    --organization "Healthcare Provider Inc" \
    --scan-results ./scan-results \
    --format pdf

# 4. PHI data protection verification
infinitum-signal compliance validate \
    --input ./phi-assessment.json \
    --framework hipaa
```

### Government & Critical Infrastructure

```bash
#!/bin/bash
# Government cybersecurity assessment

# 1. Government system SBOM
infinitum-signal scan sbom \
    --target ./government-portal \
    --format cyclonedx \
    --depth 15

# 2. Comprehensive security assessment
infinitum-signal vuln assess \
    --sbom ./sbom.json \
    --sources nvd,snyk,github,osv \
    --severity-threshold medium \
    --include-epss

# 3. CERT-In compliance report
infinitum-signal compliance generate \
    --framework cert-in \
    --organization "Government Agency" \
    --scan-results ./scan-results \
    --format pdf \
    --executive-summary

# 4. ISO 27001 alignment
infinitum-signal compliance generate \
    --framework iso27001 \
    --organization "Government Agency" \
    --scan-results ./scan-results \
    --format pdf
```

---

## 🔗 Integration Examples

### CI/CD Pipeline Integration

#### GitHub Actions Integration
```yaml
# .github/workflows/security-scan.yml
name: Security Scan

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  security-scan:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup Infinitum Signal
      run: |
        wget https://github.com/tanm-sys/infinitum-signal/releases/download/v0.1.0/infinitum-signal-linux-x64.tar.gz
        tar -xzf infinitum-signal-linux-x64.tar.gz
        sudo mv infinitum-signal /usr/local/bin/

    - name: Generate SBOM
      run: |
        infinitum-signal scan sbom \
          --target . \
          --format cyclonedx \
          --output ./security-reports

    - name: Vulnerability Assessment
      run: |
        infinitum-signal vuln assess \
          --sbom ./security-reports/sbom.json \
          --sources nvd,snyk \
          --severity-threshold high \
          --output ./security-reports

    - name: Compliance Check
      run: |
        infinitum-signal compliance generate \
          --framework cert-in \
          --organization "${{ github.repository_owner }}" \
          --scan-results ./security-reports \
          --format json \
          --output ./security-reports

    - name: Upload Security Reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: ./security-reports/
```

#### Jenkins Pipeline Integration
```groovy
// Jenkinsfile
pipeline {
    agent any

    stages {
        stage('Security Scan') {
            steps {
                script {
                    // Download Infinitum Signal CLI
                    sh 'wget https://github.com/tanm-sys/infinitum-signal/releases/download/v0.1.0/infinitum-signal-linux-x64.tar.gz'
                    sh 'tar -xzf infinitum-signal-linux-x64.tar.gz'
                    sh 'sudo mv infinitum-signal /usr/local/bin/'

                    // Generate SBOM
                    sh 'infinitum-signal scan sbom --target . --format cyclonedx --output ./security-reports'

                    // Vulnerability assessment
                    sh 'infinitum-signal vuln assess --sbom ./security-reports/sbom.json --sources nvd,snyk --output ./security-reports'

                    // Compliance report
                    sh 'infinitum-signal compliance generate --framework cert-in --organization "Company Name" --scan-results ./security-reports --format pdf --output ./security-reports'
                }
            }
        }

        stage('Archive Reports') {
            steps {
                archiveArtifacts artifacts: 'security-reports/**/*', fingerprint: true
            }
        }
    }

    post {
        always {
            publishHTML([
                allowMissing: false,
                alwaysLinkToLastBuild: true,
                keepAll: true,
                reportDir: 'security-reports',
                reportFiles: 'compliance-report.html',
                reportName: 'Compliance Report'
            ])
        }
    }
}
```

### API Integration Examples

#### Python API Client
```python
#!/usr/bin/env python3
# Python client for Infinitum Signal API

import requests
import json
from typing import Dict, List, Optional

class InfinitumSignalClient:
    def __init__(self, base_url: str = "http://localhost:8080", api_key: str = None):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        if api_key:
            self.session.headers.update({'Authorization': f'Bearer {api_key}'})

    def generate_sbom(self, target_path: str, format: str = "cyclonedx") -> Dict:
        """Generate SBOM for target path"""
        payload = {
            "scan_type": "sbom",
            "target": target_path,
            "options": {"format": format}
        }

        response = self.session.post(f"{self.base_url}/api/v1/scan/sbom", json=payload)
        response.raise_for_status()
        return response.json()

    def assess_vulnerabilities(self, sbom_path: str, sources: List[str] = None) -> Dict:
        """Assess vulnerabilities from SBOM"""
        if sources is None:
            sources = ["nvd", "snyk"]

        # Read SBOM file
        with open(sbom_path, 'r') as f:
            sbom_content = f.read()

        payload = {
            "sbom": sbom_content,
            "sources": sources,
            "severity_threshold": "medium",
            "include_epss": True
        }

        response = self.session.post(f"{self.base_url}/api/v1/vulnerability/assess", json=payload)
        response.raise_for_status()
        return response.json()

    def generate_compliance_report(self, framework: str, organization: str, scan_results_path: str) -> Dict:
        """Generate compliance report"""
        payload = {
            "framework": framework,
            "organization": organization,
            "scan_results_path": scan_results_path,
            "output_format": "pdf",
            "include_executive_summary": True
        }

        response = self.session.post(f"{self.base_url}/api/v1/compliance/generate", json=payload)
        response.raise_for_status()
        return response.json()

# Usage example
if __name__ == "__main__":
    client = InfinitumSignalClient()

    # Generate SBOM
    sbom_result = client.generate_sbom("./my-project")
    print(f"SBOM generated: {sbom_result['data']['id']}")

    # Assess vulnerabilities
    vuln_result = client.assess_vulnerabilities("./sbom.json")
    print(f"Vulnerabilities assessed: {len(vuln_result['data']['results']['vulnerabilities'])} found")

    # Generate compliance report
    compliance_result = client.generate_compliance_report("cert-in", "My Company", "./scan-results")
    print(f"Compliance report generated: {compliance_result['data']['id']}")
```

#### JavaScript/Node.js API Client
```javascript
// JavaScript client for Infinitum Signal API

const axios = require('axios');
const fs = require('fs').promises;

class InfinitumSignalClient {
    constructor(baseURL = 'http://localhost:8080', apiKey = null) {
        this.client = axios.create({
            baseURL,
            headers: apiKey ? { 'Authorization': `Bearer ${apiKey}` } : {}
        });
    }

    async generateSBOM(targetPath, format = 'cyclonedx') {
        const payload = {
            scan_type: 'sbom',
            target: targetPath,
            options: { format }
        };

        const response = await this.client.post('/api/v1/scan/sbom', payload);
        return response.data;
    }

    async assessVulnerabilities(sbomPath, sources = ['nvd', 'snyk']) {
        const sbomContent = await fs.readFile(sbomPath, 'utf8');

        const payload = {
            sbom: sbomContent,
            sources,
            severity_threshold: 'medium',
            include_epss: true
        };

        const response = await this.client.post('/api/v1/vulnerability/assess', payload);
        return response.data;
    }

    async generateComplianceReport(framework, organization, scanResultsPath) {
        const payload = {
            framework,
            organization,
            scan_results_path: scanResultsPath,
            output_format: 'pdf',
            include_executive_summary: true
        };

        const response = await this.client.post('/api/v1/compliance/generate', payload);
        return response.data;
    }

    async getScanResults(scanId) {
        const response = await this.client.get(`/api/v1/scan/${scanId}`);
        return response.data;
    }

    async listScans(page = 1, size = 20) {
        const response = await this.client.get('/api/v1/scans', {
            params: { page, size }
        });
        return response.data;
    }
}

// Usage example
async function main() {
    const client = new InfinitumSignalClient();

    try {
        // Generate SBOM
        const sbomResult = await client.generateSBOM('./my-project');
        console.log(`SBOM generated: ${sbomResult.data.id}`);

        // Assess vulnerabilities
        const vulnResult = await client.assessVulnerabilities('./sbom.json');
        console.log(`Vulnerabilities found: ${vulnResult.data.results.vulnerabilities.length}`);

        // Generate compliance report
        const complianceResult = await client.generateComplianceReport(
            'cert-in',
            'My Company',
            './scan-results'
        );
        console.log(`Compliance report: ${complianceResult.data.id}`);

    } catch (error) {
        console.error('Error:', error.response?.data || error.message);
    }
}

main();
```

---

## 🔧 Advanced Scenarios

### Scenario 1: Large-Scale Enterprise Scanning

```bash
#!/bin/bash
# Enterprise-scale security assessment

PROJECTS_DIR="./enterprise-projects"
OUTPUT_DIR="./enterprise-reports"
MAX_CONCURRENT=5

echo "🏢 Starting enterprise-scale security assessment..."

# Find all projects
find "$PROJECTS_DIR" -name "Cargo.toml" -o -name "package.json" -o -name "requirements.txt" | while read -r project_file; do
    project_dir=$(dirname "$project_file")

    # Limit concurrent scans
    while [ $(jobs -r | wc -l) -ge $MAX_CONCURRENT ]; do
        sleep 1
    done

    {
        project_name=$(basename "$project_dir")
        echo "🔍 Scanning $project_name..."

        # Generate SBOM
        infinitum-signal scan sbom \
            --target "$project_dir" \
            --format cyclonedx \
            --output "$OUTPUT_DIR/$project_name"

        # Assess vulnerabilities
        infinitum-signal vuln assess \
            --target "$project_dir" \
            --sources nvd,snyk \
            --severity-threshold medium \
            --output "$OUTPUT_DIR/$project_name"

        echo "✅ Completed $project_name"
    } &
done

# Wait for all scans to complete
wait

echo "📊 Generating enterprise summary report..."
infinitum-signal compliance generate \
    --framework cert-in \
    --organization "Enterprise Organization" \
    --scan-results "$OUTPUT_DIR" \
    --format pdf \
    --executive-summary \
    --output "$OUTPUT_DIR"

echo "🎉 Enterprise security assessment complete!"
```

### Scenario 2: Automated Compliance Monitoring

```bash
#!/bin/bash
# Automated compliance monitoring system

COMPLIANCE_DIR="./compliance-monitoring"
THRESHOLD_DAYS=30
FRAMEWORKS=("cert-in" "iso27001")

echo "📊 Starting automated compliance monitoring..."

# Check if reports need updating
for framework in "${FRAMEWORKS[@]}"; do
    report_file="$COMPLIANCE_DIR/${framework}-report.pdf"
    last_modified=$(stat -c %Y "$report_file" 2>/dev/null || echo 0)
    days_since_update=$(( ( $(date +%s) - last_modified ) / 86400 ))

    if [ $days_since_update -gt $THRESHOLD_DAYS ]; then
        echo "📋 Updating $framework compliance report (last updated $days_since_update days ago)..."

        infinitum-signal compliance generate \
            --framework "$framework" \
            --organization "Monitored Organization" \
            --scan-results "$COMPLIANCE_DIR/scan-data" \
            --format pdf \
            --executive-summary \
            --output "$COMPLIANCE_DIR"

        # Commit to blockchain for audit trail
        infinitum-signal blockchain commit \
            --data-type compliance-report \
            --data "$report_file" \
            --metadata "{\"framework\": \"$framework\", \"auto_generated\": true}"

        echo "✅ $framework report updated"
    else
        echo "✅ $framework report is current ($days_since_update days old)"
    fi
done

# Generate compliance dashboard
echo "📊 Generating compliance dashboard..."
infinitum-signal compliance generate \
    --framework cert-in \
    --organization "Compliance Dashboard" \
    --scan-results "$COMPLIANCE_DIR" \
    --format html \
    --output "$COMPLIANCE_DIR"

echo "🎯 Compliance monitoring cycle complete!"
```

---

## 🔍 Troubleshooting Examples

### Common Issues and Solutions

#### Issue 1: Database Connection Failed
```bash
# Check database status
sudo systemctl status postgresql
sudo -u postgres psql -c "SELECT version();"

# Test connection
psql -h localhost -U infinitum_user -d infinitum_signal -c "SELECT 1;"

# Check connection string in .env
cat .env | grep DATABASE_URL

# Fix: Update database URL
# DATABASE_URL=postgresql://infinitum_user:correct_password@localhost:5432/infinitum_signal
```

#### Issue 2: External Tool Not Found
```bash
# Check if tools are installed
which syft
which trivy
which grype

# Install missing tools
curl -sSfL https://raw.githubusercontent.com/anchore/syft/main/install.sh | sh
curl -sSfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh

# Verify installation
syft version
trivy version
```

#### Issue 3: Permission Denied
```bash
# Check file permissions
ls -la ./output/
ls -la ./storage/

# Fix permissions
chmod -R 755 ./output/
chmod -R 755 ./storage/
chown -R $USER:$USER ./output/
chown -R $USER:$USER ./storage/
```

#### Issue 4: API Key Issues
```bash
# Check API keys in environment
env | grep -E "(NVD_API_KEY|GITHUB_TOKEN|SNYK_API_TOKEN)"

# Test API key validity
curl -H "Authorization: Bearer $NVD_API_KEY" "https://services.nvd.nist.gov/rest/json/cves/1.0"

# Update API keys in .env file
# NVD_API_KEY=your-new-api-key
# GITHUB_TOKEN=your-new-github-token
```

#### Issue 5: Memory Issues
```bash
# Check system memory
free -h
vmstat 1 5

# Check process memory usage
ps aux --sort=-%mem | head 10

# Increase system memory or optimize scan
# For large projects, increase memory limits
export RUST_MIN_STACK=8388608
```

#### Issue 6: Slow Performance
```bash
# Check system resources
top -b -n 1 | head 20
iostat -x 1 3
netstat -tunp | grep infinitum

# Optimize database
psql -d infinitum_signal -c "VACUUM ANALYZE;"

# Check Redis performance
redis-cli --latency

# Profile application
cargo build --release
perf record -g ./target/release/infinitum-signal-cli scan sbom --target ./test-project
perf report
```

---

## 📞 Getting Help

### Support Resources
- **Documentation**: [docs.infinitium-signal.com](https://docs.infinitium-signal.com)
- **API Reference**: [api.infinitium-signal.com](https://api.infinitium-signal.com)
- **Community Forum**: [community.infinitium-signal.com](https://community.infinitium-signal.com)
- **GitHub Issues**: [github.com/tanm-sys/infinitum-signal/issues](https://github.com/tanm-sys/infinitum-signal/issues)

### Contact Information
- **Technical Support**: <EMAIL>
- **Security Issues**: <EMAIL>
- **Enterprise Sales**: <EMAIL>

---

**Last Updated**: September 2, 2025

**🎉 Happy Scanning with Infinitum Signal!**

*These examples demonstrate the versatility and power of Infinitum Signal for comprehensive cybersecurity compliance across various industries and use cases.*