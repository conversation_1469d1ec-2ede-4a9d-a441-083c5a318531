    if [ "$regression_detected" = true ]; then
        echo "❌ Performance regression detected!"
        echo "📊 Detailed comparison:"
        echo "• Build Time: ${baseline_build}s → ${current_build}s (+${build_regression}%)"
        echo "• API Response: ${baseline_api}s → ${current_api}s (+${api_regression}%)"
        echo "• Memory Usage: ${baseline_memory}KB → ${current_memory}KB (+${memory_regression}%)"
        return 1
    else
        echo "✅ No performance regression detected"
        # Update baseline with current results
        cp "$current_file" "$baseline_file"
        echo "📊 Baseline updated with current performance metrics"
        return 0
    fi
}

# Run performance regression check
check_performance_regression
```

---

## Troubleshooting

### Common Performance Issues

#### High CPU Usage

**Symptoms:**
- CPU usage consistently > 80%
- Slow response times
- System becomes unresponsive

**Diagnosis:**
```bash
# Check CPU usage by process
ps aux --sort=-%cpu | head -10

# Check system load
uptime
cat /proc/loadavg

# Check CPU affinity
taskset -p $(pidof infinitum-signal)

# Profile CPU usage
perf top
```

**Solutions:**
```rust
// Optimize CPU-intensive operations
use rayon::prelude::*;

// Before: Sequential processing
pub fn process_data_sequential(data: Vec<Item>) -> Vec<Result> {
    data.into_iter()
        .map(|item| expensive_operation(item))
        .collect()
}

// After: Parallel processing
pub fn process_data_parallel(data: Vec<Item>) -> Vec<Result> {
    data.par_iter()
        .map(|item| expensive_operation(item))
        .collect()
}
```

#### High Memory Usage

**Symptoms:**
- Memory usage > 80% of available RAM
- Frequent garbage collection
- Out of memory errors

**Diagnosis:**
```bash
# Check memory usage
free -h
vmstat 1 5

# Check process memory
ps aux --sort=-%mem | head -10
pmap -x $(pidof infinitum-signal)

# Memory profiling
valgrind --tool=massif cargo run --release --bin infinitum-signal demo
```

**Solutions:**
```rust
// Optimize memory usage
use std::collections::HashMap;

// Before: Inefficient data structures
pub struct InefficientStruct {
    data: HashMap<String, Vec<String>>, // Multiple allocations
}

// After: Pre-allocated structures
pub struct OptimizedStruct {
    data: HashMap<String, Vec<String>>,
}

impl OptimizedStruct {
    pub fn new(capacity: usize) -> Self {
        let mut data = HashMap::with_capacity(capacity);
        // Pre-allocate inner vectors
        for _ in 0..capacity {
            data.insert(String::new(), Vec::with_capacity(100));
        }
        Self { data }
    }
}
```

#### Slow Database Queries

**Symptoms:**
- API response times > 500ms
- Database CPU usage high
- Connection pool exhausted

**Diagnosis:**
```bash
# Check database performance
psql -c "SELECT * FROM pg_stat_activity;"

# Check slow queries
psql -c "SELECT * FROM pg_stat_statements ORDER BY total_time DESC LIMIT 10;"

# Check index usage
psql -c "SELECT * FROM pg_stat_user_indexes ORDER BY idx_scan DESC;"

# Check table bloat
psql -c "SELECT schemaname, tablename, n_dead_tup, n_live_tup FROM pg_stat_user_tables;"
```

**Solutions:**
```sql
-- Add performance indexes
CREATE INDEX CONCURRENTLY idx_projects_status_created
ON projects (status, created_at DESC);

CREATE INDEX CONCURRENTLY idx_scans_project_status
ON scans (project_id, status)
WHERE status = 'completed';

-- Optimize query
EXPLAIN ANALYZE
SELECT p.name, COUNT(s.id) as scan_count
FROM projects p
LEFT JOIN scans s ON p.id = s.project_id
WHERE p.status = 'active'
GROUP BY p.id, p.name
ORDER BY scan_count DESC
LIMIT 50;
```

#### Network Bottlenecks

**Symptoms:**
- High network latency
- Connection timeouts
- Packet loss

**Diagnosis:**
```bash
# Network diagnostics
ping -c 5 localhost
traceroute api.infinitium-signal.com

# Check network connections
netstat -tunp | grep infinitum
ss -tunp | grep :8080

# Network performance
iperf3 -c localhost -p 8080
```

**Solutions:**
```rust
// Optimize network operations
use tokio::net::TcpListener;
use tokio::sync::Semaphore;
use std::sync::Arc;

// Connection pooling
pub struct ConnectionPool {
    semaphore: Arc<Semaphore>,
    max_connections: usize,
}

impl ConnectionPool {
    pub fn new(max_connections: usize) -> Self {
        Self {
            semaphore: Arc::new(Semaphore::new(max_connections)),
            max_connections,
        }
    }

    pub async fn acquire(&self) -> Result<ConnectionPermit, Error> {
        let permit = self.semaphore.acquire().await?;
        Ok(ConnectionPermit { _permit: permit })
    }
}

// HTTP client optimization
use reqwest::Client;

pub fn create_optimized_client() -> Result<Client, Error> {
    Client::builder()
        .pool_max_idle_per_host(10)
        .pool_idle_timeout(Duration::from_secs(30))
        .tcp_nodelay(true)
        .build()
}
```

---

## Performance Dashboards

### Grafana Dashboard Configuration

```json
{
  "dashboard": {
    "title": "Infinitium Signal Performance Dashboard",
    "tags": ["performance", "infinitium-signal"],
    "timezone": "UTC",
    "panels": [
      {
        "title": "API Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "P95 Response Time"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "P50 Response Time"
          }
        ]
      },
      {
        "title": "System Resource Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "100 - (avg by (instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
            "legendFormat": "CPU Usage %"
          },
          {
            "expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100",
            "legendFormat": "Memory Usage %"
          }
        ]
      },
      {
        "title": "Database Performance",
        "type": "table",
        "targets": [
          {
            "expr": "pg_stat_database_tup_fetched{datname=\"infinitum_signal\"}",
            "legendFormat": "Rows Fetched"
          },
          {
            "expr": "pg_stat_database_tup_inserted{datname=\"infinitum_signal\"}",
            "legendFormat": "Rows Inserted"
          }
        ]
      },
      {
        "title": "Application Throughput",
        "type": "bargauge",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "Requests/sec"
          }
        ]
      }
    ]
  }
}
```

### Custom Performance Metrics

```rust
// Custom performance metrics
use metrics::{counter, histogram, gauge};
use std::time::Instant;

pub fn record_api_request(endpoint: &str, method: &str, status: u16, duration: Duration) {
    // Request counter
    counter!("http_requests_total", "endpoint" => endpoint.to_string(), "method" => method.to_string(), "status" => status.to_string()).increment(1);

    // Response time histogram
    histogram!("http_request_duration_seconds", "endpoint" => endpoint.to_string(), "method" => method.to_string()).record(duration.as_secs_f64());

    // Error rate gauge
    if status >= 400 {
        gauge!("http_error_rate", "endpoint" => endpoint.to_string()).increment(1.0);
    }
}

pub fn record_database_query(table: &str, operation: &str, duration: Duration, rows_affected: u64) {
    // Query counter
    counter!("database_queries_total", "table" => table.to_string(), "operation" => operation.to_string()).increment(1);

    // Query duration
    histogram!("database_query_duration_seconds", "table" => table.to_string(), "operation" => operation.to_string()).record(duration.as_secs_f64());

    // Rows affected
    counter!("database_rows_affected_total", "table" => table.to_string(), "operation" => operation.to_string()).increment(rows_affected);
}

pub fn record_cache_operation(cache_name: &str, operation: &str, hit: bool) {
    // Cache operation counter
    counter!("cache_operations_total", "cache" => cache_name.to_string(), "operation" => operation.to_string(), "result" => if hit { "hit" } else { "miss" }.to_string()).increment(1);

    // Cache hit rate
    if hit {
        gauge!("cache_hit_rate", "cache" => cache_name.to_string()).increment(1.0);
    }
}
```

### Performance Alerting Rules

```yaml
# Prometheus alerting rules
groups:
  - name: infinitum_signal_performance_alerts
    rules:
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2.0
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High API response time detected"
          description: "95th percentile response time is {{ $value }}s"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }}"

      - alert: HighCPUUsage
        expr: 100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 85
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}%"

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}%"

      - alert: DatabaseConnectionPoolExhausted
        expr: pg_stat_activity_count{datname="infinitum_signal", state="active"} / pg_settings_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Database connection pool nearly exhausted"
          description: "Active connections: {{ $value }}"

      - alert: CacheHitRateLow
        expr: rate(cache_operations_total{result="hit"}[5m]) / rate(cache_operations_total[5m]) < 0.7
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Low cache hit rate detected"
          description: "Cache hit rate is {{ $value | humanizePercentage }}"
```

---

## Conclusion

This comprehensive performance testing guide provides the tools and methodologies needed to ensure Infinitium Signal maintains optimal performance across all components. The guide covers:

**🔧 Testing Infrastructure**
- System monitoring and application profiling
- Build and runtime performance testing
- Benchmarking with Criterion and custom tools
- Load testing with multiple tools and methodologies

**📊 Performance Analysis**
- Flame graph generation and analysis
- Memory leak detection and profiling
- Database performance optimization
- Network bottleneck identification

### Recent Performance Benchmarks

Based on recent analysis (September 1, 2025) on AMD Ryzen 7 4800H system with 16GB RAM:

**System Performance:**
- CPU Load Average: 1-min (2.04-2.35), 5-min (5.05-5.21), 15-min (4.27-4.31)
- Memory Usage: 5.6GB-6.4GB used (35-40% of 16GB total)
- Build Time: ~69 seconds for release build
- Peak Memory During Build: 6.4GB

**Key Findings:**
- Efficient multi-core CPU utilization during compilation
- Reasonable memory usage with adequate headroom
- Fast startup times and response performance
- No signs of memory leaks or performance bottlenecks

**🚀 Optimization Strategies**
- Code-level optimizations for CPU and memory usage
- Database query optimization and indexing
- Concurrent processing improvements
- Memory management best practices

**🔄 Continuous Integration**
- CI/CD performance testing integration
- Performance regression detection
- Automated alerting and monitoring
- Performance dashboard configuration

**🛠️ Troubleshooting**
- Common performance issue diagnosis
- Root cause analysis techniques
- Optimization implementation examples
- Performance monitoring best practices

By following this guide, development teams can ensure Infinitium Signal maintains enterprise-grade performance while scaling to handle increasing loads and complexity.

**Remember**: Performance testing is not a one-time activity but a continuous process that should be integrated into the development lifecycle from the earliest stages through production deployment.

---

**Last Updated**: September 2, 2025
**Performance Framework**: Comprehensive Testing Suite
**Benchmark Tools**: Criterion 0.5, Hyperfine, FlameGraph, Valgrind
**Monitoring**: Prometheus 0.14, Grafana, Custom Metrics
