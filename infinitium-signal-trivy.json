{"SchemaVersion": 2, "CreatedAt": "2025-08-24T02:50:11.784587131-05:00", "ArtifactName": ".", "ArtifactType": "filesystem", "Results": [{"Target": "Cargo.lock", "Class": "lang-pkgs", "Type": "cargo", "Vulnerabilities": [{"VulnerabilityID": "CVE-2024-12224", "PkgID": "idna@0.4.0", "PkgName": "idna", "PkgIdentifier": {"PURL": "pkg:cargo/idna@0.4.0", "UID": "7c2977d1efd7d499"}, "InstalledVersion": "0.4.0", "FixedVersion": "1.0.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2024-12224", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory Rust", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Arust"}, "Title": "idna: idna accepts Punycode labels that do not produce any non-ASCII when decoded", "Description": "Improper Validation of Unsafe Equivalence in punycode by the idna crate from Servo rust-url allows an attacker to create a punycode hostname that one part of a system might treat as distinct while another part of that system would treat as equivalent to another hostname.", "Severity": "MEDIUM", "CweIDs": ["CWE-1289"], "VendorSeverity": {"ghsa": 2, "redhat": 2}, "CVSS": {"redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:L/I:L/A:N", "V3Score": 4.2}}, "References": ["https://access.redhat.com/security/cve/CVE-2024-12224", "https://bugzilla.mozilla.org/show_bug.cgi?id=1887898", "https://github.com/servo/rust-url", "https://nvd.nist.gov/vuln/detail/CVE-2024-12224", "https://rustsec.org/advisories/RUSTSEC-2024-0421.html", "https://www.cve.org/CVERecord?id=CVE-2024-12224"], "PublishedDate": "2025-05-30T02:15:19.67Z", "LastModifiedDate": "2025-05-30T16:31:03.107Z"}, {"VulnerabilityID": "CVE-2025-53605", "PkgID": "protobuf@2.28.0", "PkgName": "protobuf", "PkgIdentifier": {"PURL": "pkg:cargo/protobuf@2.28.0", "UID": "69931a39d9c6511b"}, "InstalledVersion": "2.28.0", "FixedVersion": "3.7.2", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2025-53605", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory Rust", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Arust"}, "Title": "protobuf: Protobuf: Uncontrolled Recursion Vulnerability", "Description": "The protobuf crate before 3.7.2 for Rust allows uncontrolled recursion in the protobuf::coded_input_stream::CodedInputStream::skip_group parsing of unknown fields in untrusted input.", "Severity": "MEDIUM", "CweIDs": ["CWE-674"], "VendorSeverity": {"amazon": 2, "ghsa": 2, "redhat": 2}, "CVSS": {"redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 5.9}}, "References": ["https://access.redhat.com/security/cve/CVE-2025-53605", "https://crates.io/crates/protobuf", "https://github.com/stepancheg/rust-protobuf", "https://github.com/stepancheg/rust-protobuf/commit/f06992f46771c0a092593b9ebf7afd48740b3ed6", "https://github.com/stepancheg/rust-protobuf/issues/749", "https://nvd.nist.gov/vuln/detail/CVE-2025-53605", "https://rustsec.org/advisories/RUSTSEC-2024-0437", "https://rustsec.org/advisories/RUSTSEC-2024-0437.html", "https://www.cve.org/CVERecord?id=CVE-2025-53605"], "PublishedDate": "2025-07-05T01:15:28.523Z", "LastModifiedDate": "2025-07-08T16:18:53.607Z"}, {"VulnerabilityID": "GHSA-xmrp-424f-vfpx", "PkgID": "sqlx@0.7.4", "PkgName": "sqlx", "PkgIdentifier": {"PURL": "pkg:cargo/sqlx@0.7.4", "UID": "1895c7a499622ed2"}, "InstalledVersion": "0.7.4", "FixedVersion": "0.8.1", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://github.com/advisories/GHSA-xmrp-424f-vfpx", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory Rust", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Arust"}, "Title": "SQLx Binary Protocol Misinterpretation caused by Truncating or Overflowing Casts", "Description": "The following presentation at this year's DEF CON was brought to our attention on the SQLx Discord:\n\n> SQL Injection isn't Dead: Smuggling Queries at the Protocol Level  \n> <http://web.archive.org/web/20240812130923/https://media.defcon.org/DEF%20CON%2032/DEF%20CON%2032%20presentations/DEF%20CON%2032%20-%20Paul%20Gerste%20-%20SQL%20Injection%20Isn't%20Dead%20Smuggling%20Queries%20at%20the%20Protocol%20Level.pdf>  \n> (Archive link for posterity.)\n\nEssentially, encoding a value larger than 4GiB can cause the length prefix in the protocol to overflow, \ncausing the server to interpret the rest of the string as binary protocol commands or other data.\n\nIt appears SQLx _does_ perform truncating casts in a way that could be problematic, \nfor example: <https://github.com/launchbadge/sqlx/blob/6f2905695b9606b5f51b40ce10af63ac9e696bb8/sqlx-postgres/src/arguments.rs#L163>\n\nThis code has existed essentially since the beginning, \nso it is reasonable to assume that all published versions `<= 0.8.0` are affected.\n\n## Mitigation\n\nAs always, you should make sure your application is validating untrustworthy user input. \nReject any input over 4 GiB, or any input that could _encode_ to a string longer than 4 GiB. \nDynamically built queries are also potentially problematic if it pushes the message size over this 4 GiB bound.\n\n[`Encode::size_hint()`](https://docs.rs/sqlx/latest/sqlx/trait.Encode.html#method.size_hint) \ncan be used for sanity checks, but do not assume that the size returned is accurate. \nFor example, the `Json<T>` and `Text<T>` adapters have no reasonable way to predict or estimate the final encoded size, \nso they just return `size_of::<T>()` instead.\n\nFor web application backends, consider adding some middleware that limits the size of request bodies by default.\n\n## Resolution\n\nWork has started on a branch to add `#[deny]` directives for the following Clippy lints:\n\n* [`cast_possible_truncation`](https://rust-lang.github.io/rust-clippy/master/#/cast_possible_truncation)\n* [`cast_possible_wrap`](https://rust-lang.github.io/rust-clippy/master/#/cast_possible_wrap)\n* [`cast_sign_loss`](https://rust-lang.github.io/rust-clippy/master/#/cast_sign_loss)\n\nand to manually audit the code that they flag.\n\nA fix is expected to be included in the `0.8.1` release (still WIP as of writing).\n", "Severity": "MEDIUM", "VendorSeverity": {"ghsa": 2}, "References": ["https://github.com/launchbadge/sqlx", "https://github.com/launchbadge/sqlx/blob/6f2905695b9606b5f51b40ce10af63ac9e696bb8/sqlx-postgres/src/arguments.rs#L163", "https://github.com/launchbadge/sqlx/issues/3440", "https://rustsec.org/advisories/RUSTSEC-2024-0363.html"], "PublishedDate": "2024-08-19T16:02:04Z", "LastModifiedDate": "2024-08-26T14:12:11Z"}]}, {"Target": "vulnerable-app/package-lock.json", "Class": "lang-pkgs", "Type": "npm", "Vulnerabilities": [{"VulnerabilityID": "CVE-2024-45590", "PkgID": "body-parser@1.18.3", "PkgName": "body-parser", "PkgIdentifier": {"PURL": "pkg:npm/body-parser@1.18.3", "UID": "766ab08a641c8163"}, "InstalledVersion": "1.18.3", "FixedVersion": "1.20.3", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2024-45590", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "body-parser: Denial of Service Vulnerability in body-parser", "Description": "body-parser is Node.js body parsing middleware. body-parser <1.20.3 is vulnerable to denial of service when url encoding is enabled. A malicious actor using a specially crafted payload could flood the server with a large number of requests, resulting in denial of service. This issue is patched in 1.20.3.", "Severity": "HIGH", "CweIDs": ["CWE-405"], "VendorSeverity": {"azure": 3, "cbl-mariner": 3, "ghsa": 3, "nvd": 3, "redhat": 3}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}, "nvd": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}}, "References": ["https://access.redhat.com/security/cve/CVE-2024-45590", "https://github.com/expressjs/body-parser", "https://github.com/expressjs/body-parser/commit/b2695c4450f06ba3b0ccf48d872a229bb41c9bce", "https://github.com/expressjs/body-parser/security/advisories/GHSA-qwcr-r2fm-qrc7", "https://nvd.nist.gov/vuln/detail/CVE-2024-45590", "https://www.cve.org/CVERecord?id=CVE-2024-45590"], "PublishedDate": "2024-09-10T16:15:21.083Z", "LastModifiedDate": "2024-09-20T16:26:44.977Z"}, {"VulnerabilityID": "CVE-2024-45590", "PkgID": "body-parser@1.19.0", "PkgName": "body-parser", "PkgIdentifier": {"PURL": "pkg:npm/body-parser@1.19.0", "UID": "bc0adba019df6f23"}, "InstalledVersion": "1.19.0", "FixedVersion": "1.20.3", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2024-45590", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "body-parser: Denial of Service Vulnerability in body-parser", "Description": "body-parser is Node.js body parsing middleware. body-parser <1.20.3 is vulnerable to denial of service when url encoding is enabled. A malicious actor using a specially crafted payload could flood the server with a large number of requests, resulting in denial of service. This issue is patched in 1.20.3.", "Severity": "HIGH", "CweIDs": ["CWE-405"], "VendorSeverity": {"azure": 3, "cbl-mariner": 3, "ghsa": 3, "nvd": 3, "redhat": 3}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}, "nvd": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}}, "References": ["https://access.redhat.com/security/cve/CVE-2024-45590", "https://github.com/expressjs/body-parser", "https://github.com/expressjs/body-parser/commit/b2695c4450f06ba3b0ccf48d872a229bb41c9bce", "https://github.com/expressjs/body-parser/security/advisories/GHSA-qwcr-r2fm-qrc7", "https://nvd.nist.gov/vuln/detail/CVE-2024-45590", "https://www.cve.org/CVERecord?id=CVE-2024-45590"], "PublishedDate": "2024-09-10T16:15:21.083Z", "LastModifiedDate": "2024-09-20T16:26:44.977Z"}, {"VulnerabilityID": "CVE-2024-47764", "PkgID": "cookie@0.3.1", "PkgName": "cookie", "PkgIdentifier": {"PURL": "pkg:npm/cookie@0.3.1", "UID": "364b6bdbd18b70fe"}, "InstalledVersion": "0.3.1", "FixedVersion": "0.7.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2024-47764", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "cookie: cookie accepts cookie name, path, and domain with out of bounds characters", "Description": "cookie is a basic HTTP cookie parser and serializer for HTTP servers. The cookie name could be used to set other fields of the cookie, resulting in an unexpected cookie value. A similar escape can be used for path and domain, which could be abused to alter other fields of the cookie. Upgrade to 0.7.0, which updates the validation for name, path, and domain.", "Severity": "LOW", "CweIDs": ["CWE-74"], "VendorSeverity": {"cbl-mariner": 2, "ghsa": 1, "redhat": 1}, "CVSS": {"redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:N/I:L/A:N", "V3Score": 3.7}}, "References": ["https://access.redhat.com/security/cve/CVE-2024-47764", "https://github.com/jshttp/cookie", "https://github.com/jshttp/cookie/commit/e10042845354fea83bd8f34af72475eed1dadf5c", "https://github.com/jshttp/cookie/pull/167", "https://github.com/jshttp/cookie/security/advisories/GHSA-pxg6-pf52-xh8x", "https://nvd.nist.gov/vuln/detail/CVE-2024-47764", "https://www.cve.org/CVERecord?id=CVE-2024-47764"], "PublishedDate": "2024-10-04T20:15:07.31Z", "LastModifiedDate": "2024-10-07T17:48:28.117Z"}, {"VulnerabilityID": "CVE-2023-46233", "PkgID": "crypto-js@3.1.9-1", "PkgName": "crypto-js", "PkgIdentifier": {"PURL": "pkg:npm/crypto-js@3.1.9-1", "UID": "7fd0ca738aa8efea"}, "InstalledVersion": "3.1.9-1", "FixedVersion": "4.2.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2023-46233", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "crypto-js: PBKDF2 1,000 times weaker than specified in 1993 and 1.3M times weaker than current standard", "Description": "crypto-js is a JavaScript library of crypto standards. Prior to version 4.2.0, crypto-js PBKDF2 is 1,000 times weaker than originally specified in 1993, and at least 1,300,000 times weaker than current industry standard. This is because it both defaults to SHA1, a cryptographic hash algorithm considered insecure since at least 2005, and defaults to one single iteration, a 'strength' or 'difficulty' value specified at 1,000 when specified in 1993. PBKDF2 relies on iteration count as a countermeasure to preimage and collision attacks. If used to protect passwords, the impact is high. If used to generate signatures, the impact is high. Version 4.2.0 contains a patch for this issue. As a workaround, configure crypto-js to use SHA256 with at least 250,000 iterations.", "Severity": "CRITICAL", "CweIDs": ["CWE-328", "CWE-916", "CWE-327"], "VendorSeverity": {"ghsa": 4, "nvd": 4, "redhat": 3, "ubuntu": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:N", "V3Score": 9.1}, "nvd": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:N", "V3Score": 9.1}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:N", "V3Score": 9.1}}, "References": ["https://access.redhat.com/security/cve/CVE-2023-46233", "https://github.com/brix/crypto-js", "https://github.com/brix/crypto-js/commit/421dd538b2d34e7c24a5b72cc64dc2b9167db40a", "https://github.com/brix/crypto-js/security/advisories/GHSA-xwcq-pm8m-c4vf", "https://lists.debian.org/debian-lts-announce/2023/11/msg00025.html", "https://nvd.nist.gov/vuln/detail/CVE-2023-46233", "https://ubuntu.com/security/notices/USN-6753-1", "https://www.cve.org/CVERecord?id=CVE-2023-46233"], "PublishedDate": "2023-10-25T21:15:10.307Z", "LastModifiedDate": "2024-11-21T08:28:07.867Z"}, {"VulnerabilityID": "CVE-2020-36732", "PkgID": "crypto-js@3.1.9-1", "PkgName": "crypto-js", "PkgIdentifier": {"PURL": "pkg:npm/crypto-js@3.1.9-1", "UID": "7fd0ca738aa8efea"}, "InstalledVersion": "3.1.9-1", "FixedVersion": "3.2.1", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2020-36732", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "crypto-js uses insecure random numbers", "Description": "The crypto-js package before 3.2.1 for Node.js generates random numbers by concatenating the string \"0.\" with an integer, which makes the output more predictable than necessary.", "Severity": "MEDIUM", "CweIDs": ["CWE-330", "CWE-331"], "VendorSeverity": {"ghsa": 2, "nvd": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N", "V3Score": 5.3}, "nvd": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N", "V3Score": 5.3}}, "References": ["https://github.com/brix/crypto-js", "https://github.com/brix/crypto-js/commit/e4ac157d8b75b962d6538fc0b996e5d4d5a9466b", "https://github.com/brix/crypto-js/compare/3.2.0...3.2.1", "https://github.com/brix/crypto-js/issues/254", "https://github.com/brix/crypto-js/issues/256", "https://github.com/brix/crypto-js/pull/257/commits/e4ac157d8b75b962d6538fc0b996e5d4d5a9466b", "https://nvd.nist.gov/vuln/detail/CVE-2020-36732", "https://security.netapp.com/advisory/ntap-*************", "https://security.netapp.com/advisory/ntap-*************/", "https://security.snyk.io/vuln/SNYK-JS-CRYPTOJS-548472"], "PublishedDate": "2023-06-12T02:15:48.347Z", "LastModifiedDate": "2025-01-06T18:15:11.1Z"}, {"VulnerabilityID": "CVE-2017-16137", "PkgID": "debug@4.1.1", "PkgName": "debug", "PkgIdentifier": {"PURL": "pkg:npm/debug@4.1.1", "UID": "23cdd1a25dc55423"}, "InstalledVersion": "4.1.1", "FixedVersion": "2.6.9, 3.1.0, 3.2.7, 4.3.1", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2017-16137", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-debug: Regular expression Denial of Service", "Description": "The debug module is vulnerable to regular expression denial of service when untrusted user input is passed into the o formatter. It takes around 50k characters to block for 2 seconds making this a low severity issue.", "Severity": "LOW", "CweIDs": ["CWE-400"], "VendorSeverity": {"ghsa": 1, "nvd": 2, "redhat": 2, "ubuntu": 1}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:N/I:N/A:L", "V3Score": 3.7}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:N/I:N/A:P", "V3Vector": "CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "V2Score": 5, "V3Score": 5.3}, "redhat": {"V3Vector": "CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "V3Score": 5.3}}, "References": ["https://access.redhat.com/security/cve/CVE-2017-16137", "https://github.com/debug-js/debug/commit/4e2150207c568adb9ead8f4c4528016081c88020", "https://github.com/debug-js/debug/commit/71169065b5262f9858ac78cc0b688c84a438f290", "https://github.com/debug-js/debug/commit/b6d12fdbc63b483e5c969da33ea6adc09946b5ac", "https://github.com/debug-js/debug/commit/f53962e944a87e6ca9bb622a2a12dffc22a9bb5a", "https://github.com/debug-js/debug/issues/797", "https://github.com/visionmedia/debug", "https://github.com/visionmedia/debug/issues/501", "https://github.com/visionmedia/debug/pull/504", "https://lists.apache.org/thread.html/r8ba4c628fba7181af58817d452119481adce4ba92e889c643e4c7dd3%40%3Ccommits.netbeans.apache.org%3E", "https://lists.apache.org/thread.html/r8ba4c628fba7181af58817d452119481adce4ba92e889c643e4c7dd3@%3Ccommits.netbeans.apache.org%3E", "https://lists.apache.org/thread.html/rb5ac16fad337d1f3bb7079549f97d8166d0ef3082629417c39f12d63%40%3Cnotifications.netbeans.apache.org%3E", "https://lists.apache.org/thread.html/rb5ac16fad337d1f3bb7079549f97d8166d0ef3082629417c39f12d63@%3Cnotifications.netbeans.apache.org%3E", "https://nodesecurity.io/advisories/534", "https://nvd.nist.gov/vuln/detail/CVE-2017-16137", "https://www.cve.org/CVERecord?id=CVE-2017-16137"], "PublishedDate": "2018-06-07T02:29:03.817Z", "LastModifiedDate": "2024-11-21T03:15:53.693Z"}, {"VulnerabilityID": "CVE-2022-24434", "PkgID": "dicer@0.2.5", "PkgName": "dicer", "PkgIdentifier": {"PURL": "pkg:npm/dicer@0.2.5", "UID": "49371ac0607789d0"}, "InstalledVersion": "0.2.5", "Status": "affected", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2022-24434", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "dicer: nodejs service crash by sending a crafted payload", "Description": "This affects all versions of package dicer. A malicious attacker can send a modified form to server, and crash the nodejs service. An attacker could sent the payload again and again so that the service continuously crashes.", "Severity": "HIGH", "VendorSeverity": {"ghsa": 3, "nvd": 3, "redhat": 3}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:N/I:N/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V2Score": 5, "V3Score": 7.5}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}}, "References": ["https://access.redhat.com/security/cve/CVE-2022-24434", "https://github.com/advisories/GHSA-wm7h-9275-46v2", "https://github.com/mscdex/busboy/issues/250", "https://github.com/mscdex/dicer", "https://github.com/mscdex/dicer/commit/b7fca2e93e8e9d4439d8acc5c02f5e54a0112dac", "https://github.com/mscdex/dicer/pull/22", "https://github.com/mscdex/dicer/pull/22/commits/b7fca2e93e8e9d4439d8acc5c02f5e54a0112dac", "https://nvd.nist.gov/vuln/detail/CVE-2022-24434", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-2838865", "https://snyk.io/vuln/SNYK-JS-DICER-2311764", "https://www.cve.org/CVERecord?id=CVE-2022-24434"], "PublishedDate": "2022-05-20T20:15:09.993Z", "LastModifiedDate": "2024-11-21T06:50:24.817Z"}, {"VulnerabilityID": "CVE-2020-36048", "PkgID": "engine.io@3.3.2", "PkgName": "engine.io", "PkgIdentifier": {"PURL": "pkg:npm/engine.io@3.3.2", "UID": "4a517aa18f918044"}, "InstalledVersion": "3.3.2", "FixedVersion": "3.6.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2020-36048", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "yarnpkg-socket.io/engine.io: allows attackers to cause a denial of service (resource consumption) via a POST request to the long polling transport", "Description": "Engine.IO before 4.0.0 allows attackers to cause a denial of service (resource consumption) via a POST request to the long polling transport.", "Severity": "HIGH", "CweIDs": ["CWE-400"], "VendorSeverity": {"ghsa": 3, "nvd": 3, "redhat": 3}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:N/I:N/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V2Score": 5, "V3Score": 7.5}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}}, "References": ["https://access.redhat.com/security/cve/CVE-2020-36048", "https://blog.caller.xyz/socketio-engineio-dos", "https://blog.caller.xyz/socketio-engineio-dos/", "https://github.com/bcaller/kill-engine-io", "https://github.com/socketio/engine.io", "https://github.com/socketio/engine.io/commit/58e274c437e9cbcf69fd913c813aad8fbd253703", "https://github.com/socketio/engine.io/commit/734f9d1268840722c41219e69eb58318e0b2ac6b", "https://nvd.nist.gov/vuln/detail/CVE-2020-36048", "https://snyk.io/vuln/SNYK-JS-ENGINEIO-1056749", "https://www.cve.org/CVERecord?id=CVE-2020-36048"], "PublishedDate": "2021-01-08T00:15:11.093Z", "LastModifiedDate": "2024-11-21T05:28:41.677Z"}, {"VulnerabilityID": "CVE-2022-41940", "PkgID": "engine.io@3.3.2", "PkgName": "engine.io", "PkgIdentifier": {"PURL": "pkg:npm/engine.io@3.3.2", "UID": "4a517aa18f918044"}, "InstalledVersion": "3.3.2", "FixedVersion": "3.6.1, 6.2.1", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2022-41940", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "engine.io: Specially crafted HTTP request can trigger an uncaught exception", "Description": "Engine.IO is the implementation of transport-based cross-browser/cross-device bi-directional communication layer for Socket.IO. A specially crafted HTTP request can trigger an uncaught exception on the Engine.IO server, thus killing the Node.js process. This impacts all the users of the engine.io package, including those who uses depending packages like socket.io. There is no known workaround except upgrading to a safe version. There are patches for this issue released in versions 3.6.1 and 6.2.1.", "Severity": "MEDIUM", "CweIDs": ["CWE-248"], "VendorSeverity": {"ghsa": 2, "nvd": 2, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H", "V3Score": 6.5}, "nvd": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H", "V3Score": 6.5}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H", "V3Score": 6.5}}, "References": ["https://access.redhat.com/security/cve/CVE-2022-41940", "https://github.com/socketio/engine.io", "https://github.com/socketio/engine.io/commit/425e833ab13373edf1dd5a0706f07100db14e3c6", "https://github.com/socketio/engine.io/commit/83c4071af871fc188298d7d591e95670bf9f9085", "https://github.com/socketio/engine.io/security/advisories/GHSA-r7qp-cfhv-p84w", "https://nvd.nist.gov/vuln/detail/CVE-2022-41940", "https://www.cve.org/CVERecord?id=CVE-2022-41940"], "PublishedDate": "2022-11-22T01:15:37.847Z", "LastModifiedDate": "2024-11-21T07:24:06.98Z"}, {"VulnerabilityID": "CVE-2024-29041", "PkgID": "express@4.16.4", "PkgName": "express", "PkgIdentifier": {"PURL": "pkg:npm/express@4.16.4", "UID": "8e7a954eb4a16f80"}, "InstalledVersion": "4.16.4", "FixedVersion": "4.19.2, 5.0.0-beta.3", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2024-29041", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "express: cause malformed URLs to be evaluated", "Description": "Express.js minimalist web framework for node. Versions of Express.js prior to 4.19.0 and all pre-release alpha and beta versions of 5.0 are affected by an open redirect vulnerability using malformed URLs. When a user of Express performs a redirect using a user-provided URL Express performs an encode [using `encodeurl`](https://github.com/pillarjs/encodeurl) on the contents before passing it to the `location` header. This can cause malformed URLs to be evaluated in unexpected ways by common redirect allow list implementations in Express applications, leading to an Open Redirect via bypass of a properly implemented allow list. The main method impacted is `res.location()` but this is also called from within `res.redirect()`. The vulnerability is fixed in 4.19.2 and 5.0.0-beta.3.", "Severity": "MEDIUM", "CweIDs": ["CWE-601", "CWE-1286"], "VendorSeverity": {"cbl-mariner": 2, "ghsa": 2, "redhat": 3, "ubuntu": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N", "V3Score": 6.1}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N", "V3Score": 6.1}}, "References": ["https://access.redhat.com/security/cve/CVE-2024-29041", "https://expressjs.com/en/4x/api.html#res.location", "https://github.com/expressjs/express", "https://github.com/expressjs/express/commit/0867302ddbde0e9463d0564fea5861feb708c2dd", "https://github.com/expressjs/express/commit/0b746953c4bd8e377123527db11f9cd866e39f94", "https://github.com/expressjs/express/pull/5539", "https://github.com/expressjs/express/security/advisories/GHSA-rv95-896h-c2vc", "https://github.com/koajs/koa/issues/1800", "https://nvd.nist.gov/vuln/detail/CVE-2024-29041", "https://ubuntu.com/security/notices/USN-7581-1", "https://www.cve.org/CVERecord?id=CVE-2024-29041"], "PublishedDate": "2024-03-25T21:15:46.847Z", "LastModifiedDate": "2024-11-21T09:07:26.023Z"}, {"VulnerabilityID": "CVE-2024-43796", "PkgID": "express@4.16.4", "PkgName": "express", "PkgIdentifier": {"PURL": "pkg:npm/express@4.16.4", "UID": "8e7a954eb4a16f80"}, "InstalledVersion": "4.16.4", "FixedVersion": "4.20.0, 5.0.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2024-43796", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "express: Improper Input Handling in Express Redirects", "Description": "Express.js minimalist web framework for node. In express < 4.20.0, passing untrusted user input - even after sanitizing it - to response.redirect() may execute untrusted code. This issue is patched in express 4.20.0.", "Severity": "LOW", "CweIDs": ["CWE-79"], "VendorSeverity": {"azure": 2, "cbl-mariner": 2, "ghsa": 1, "nvd": 2, "redhat": 2, "ubuntu": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:L/I:L/A:L", "V3Score": 5}, "nvd": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:C/C:L/I:L/A:N", "V3Score": 4.7}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:L/I:L/A:L", "V3Score": 5}}, "References": ["https://access.redhat.com/security/cve/CVE-2024-43796", "https://github.com/expressjs/express", "https://github.com/expressjs/express/commit/54271f69b511fea198471e6ff3400ab805d6b553", "https://github.com/expressjs/express/security/advisories/GHSA-qw6h-vgh9-j6wx", "https://nvd.nist.gov/vuln/detail/CVE-2024-43796", "https://ubuntu.com/security/notices/USN-7581-1", "https://www.cve.org/CVERecord?id=CVE-2024-43796"], "PublishedDate": "2024-09-10T15:15:17.51Z", "LastModifiedDate": "2024-09-20T16:07:47.997Z"}, {"VulnerabilityID": "CVE-2025-7783", "PkgID": "form-data@2.3.3", "PkgName": "form-data", "PkgIdentifier": {"PURL": "pkg:npm/form-data@2.3.3", "UID": "b6fc375e7de52c2"}, "InstalledVersion": "2.3.3", "FixedVersion": "2.5.4, 3.0.4, 4.0.4", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2025-7783", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "form-data: Unsafe random function in form-data", "Description": "Use of Insufficiently Random Values vulnerability in form-data allows HTTP Parameter Pollution (HPP). This vulnerability is associated with program files lib/form_data.Js.\n\nThis issue affects form-data: < 2.5.4, 3.0.0 - 3.0.3, 4.0.0 - 4.0.3.", "Severity": "CRITICAL", "CweIDs": ["CWE-330"], "VendorSeverity": {"ghsa": 4, "redhat": 2}, "CVSS": {"redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:C/C:L/I:L/A:N", "V3Score": 5.4}}, "References": ["https://access.redhat.com/security/cve/CVE-2025-7783", "https://github.com/benweissmann/CVE-2025-7783-poc", "https://github.com/form-data/form-data", "https://github.com/form-data/form-data/commit/3d1723080e6577a66f17f163ecd345a21d8d0fd0", "https://github.com/form-data/form-data/security/advisories/GHSA-fjxv-7rqg-78g4", "https://nvd.nist.gov/vuln/detail/CVE-2025-7783", "https://www.cve.org/CVERecord?id=CVE-2025-7783"], "PublishedDate": "2025-07-18T17:15:44.747Z", "LastModifiedDate": "2025-07-22T15:15:39.663Z"}, {"VulnerabilityID": "CVE-2019-19919", "PkgID": "handlebars@4.1.2", "PkgName": "handlebars", "PkgIdentifier": {"PURL": "pkg:npm/handlebars@4.1.2", "UID": "bd351504630ed2e5"}, "InstalledVersion": "4.1.2", "FixedVersion": "4.3.0, 3.0.8", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2019-19919", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-handlebars: prototype pollution leading to remote code execution via crafted payloads", "Description": "Versions of handlebars prior to 4.3.0 are vulnerable to Prototype Pollution leading to Remote Code Execution. Templates may alter an Object's __proto__ and __defineGetter__ properties, which may allow an attacker to execute arbitrary code through crafted payloads.", "Severity": "CRITICAL", "CweIDs": ["CWE-1321"], "VendorSeverity": {"ghsa": 4, "nvd": 4, "redhat": 1, "ruby-advisory-db": 4}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "V3Score": 9.8}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:P/I:P/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "V2Score": 7.5, "V3Score": 9.8}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:L/A:N", "V3Score": 4.2}}, "References": ["https://access.redhat.com/security/cve/CVE-2019-19919", "https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2019-19919", "https://github.com/Nerian/bootstrap-wysihtml5-rails/blob/master/vendor/assets/javascripts/bootstrap-wysihtml5/handlebars.runtime.min.js", "https://github.com/Nerian/bootstrap-wysihtml5-rails/tree/master/vendor/assets/javascripts/bootstrap-wysihtml5", "https://github.com/advisories/GHSA-w457-6q6x-cgp9", "https://github.com/handlebars-lang/handlebars.js/commit/156061eb7707575293613d7fdf90e2bdaac029ee", "https://github.com/handlebars-lang/handlebars.js/commit/90ad8d97ad2933852fb83fcc054699dc99e094db", "https://github.com/rubysec/ruby-advisory-db/blob/master/gems/bootstrap-wysihtml5-rails/CVE-2019-19919.yml", "https://github.com/wycats/handlebars.js", "https://github.com/wycats/handlebars.js/commit/2078c727c627f25d4a149962f05c1e069beb18bc", "https://github.com/wycats/handlebars.js/issues/1558", "https://nvd.nist.gov/vuln/detail/CVE-2019-19919", "https://www.cve.org/CVERecord?id=CVE-2019-19919", "https://www.npmjs.com/advisories/1164", "https://www.tenable.com/security/tns-2021-14"], "PublishedDate": "2019-12-20T23:15:11.48Z", "LastModifiedDate": "2024-11-21T04:35:39.797Z"}, {"VulnerabilityID": "CVE-2021-23369", "PkgID": "handlebars@4.1.2", "PkgName": "handlebars", "PkgIdentifier": {"PURL": "pkg:npm/handlebars@4.1.2", "UID": "bd351504630ed2e5"}, "InstalledVersion": "4.1.2", "FixedVersion": "4.7.7", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2021-23369", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-handlebars: Remote code execution when compiling untrusted compile templates with strict:true option", "Description": "The package handlebars before 4.7.7 are vulnerable to Remote Code Execution (RCE) when selecting certain compiling options to compile templates coming from an untrusted source.", "Severity": "CRITICAL", "VendorSeverity": {"ghsa": 4, "nvd": 4, "redhat": 2, "ruby-advisory-db": 4, "ubuntu": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "V3Score": 9.8}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:P/I:P/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "V2Score": 7.5, "V3Score": 9.8}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "V3Score": 9.8}}, "References": ["https://access.redhat.com/security/cve/CVE-2021-23369", "https://github.com/advisories/GHSA-f2jv-r9rf-7988", "https://github.com/handlebars-lang/handlebars.js/commit/b6d3de7123eebba603e321f04afdbae608e8fea8", "https://github.com/handlebars-lang/handlebars.js/commit/f0589701698268578199be25285b2ebea1c1e427", "https://github.com/wycats/handlebars.js", "https://nvd.nist.gov/vuln/detail/CVE-2021-23369", "https://security.netapp.com/advisory/ntap-20210604-0008", "https://security.netapp.com/advisory/ntap-20210604-0008/", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARS-1074950", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWER-1074951", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-1074952", "https://snyk.io/vuln/SNYK-JS-HANDLEBARS-1056767", "https://www.cve.org/CVERecord?id=CVE-2021-23369"], "PublishedDate": "2021-04-12T14:15:14.383Z", "LastModifiedDate": "2024-11-21T05:51:35.303Z"}, {"VulnerabilityID": "CVE-2021-23383", "PkgID": "handlebars@4.1.2", "PkgName": "handlebars", "PkgIdentifier": {"PURL": "pkg:npm/handlebars@4.1.2", "UID": "bd351504630ed2e5"}, "InstalledVersion": "4.1.2", "FixedVersion": "4.7.7", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2021-23383", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-handlebars: Remote code execution when compiling untrusted compile templates with compat:true option", "Description": "The package handlebars before 4.7.7 are vulnerable to Prototype Pollution when selecting certain compiling options to compile templates coming from an untrusted source.", "Severity": "CRITICAL", "CweIDs": ["CWE-1321"], "VendorSeverity": {"ghsa": 4, "nvd": 4, "redhat": 2, "ruby-advisory-db": 4}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "V3Score": 9.8}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:P/I:P/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "V2Score": 7.5, "V3Score": 9.8}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "V3Score": 9.8}}, "References": ["https://access.redhat.com/security/cve/CVE-2021-23383", "https://github.com/advisories/GHSA-765h-qjxv-5f44", "https://github.com/handlebars-lang/handlebars.js", "https://github.com/handlebars-lang/handlebars.js/commit/f0589701698268578199be25285b2ebea1c1e427", "https://github.com/rubysec/ruby-advisory-db/blob/master/gems/handlebars-source/CVE-2021-23383.yml", "https://nvd.nist.gov/vuln/detail/CVE-2021-23383", "https://security.netapp.com/advisory/ntap-20210618-0007", "https://security.netapp.com/advisory/ntap-20210618-0007/", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARS-1279031", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWER-1279032", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-1279030", "https://snyk.io/vuln/SNYK-JS-HANDLEBARS-1279029", "https://www.cve.org/CVERecord?id=CVE-2021-23383", "https://www.npmjs.com/package/handlebars"], "PublishedDate": "2021-05-04T09:15:07.753Z", "LastModifiedDate": "2024-11-21T05:51:36.913Z"}, {"VulnerabilityID": "CVE-2019-20920", "PkgID": "handlebars@4.1.2", "PkgName": "handlebars", "PkgIdentifier": {"PURL": "pkg:npm/handlebars@4.1.2", "UID": "bd351504630ed2e5"}, "InstalledVersion": "4.1.2", "FixedVersion": "3.0.8, 4.5.3", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2019-20920", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-handlebars: lookup helper fails to properly validate templates allowing for arbitrary JavaScript execution", "Description": "Handlebars before 3.0.8 and 4.x before 4.5.3 is vulnerable to Arbitrary Code Execution. The lookup helper fails to properly validate templates, allowing attackers to submit templates that execute arbitrary JavaScript. This can be used to run arbitrary code on a server processing Handlebars templates or in a victim's browser (effectively serving as XSS).", "Severity": "HIGH", "CweIDs": ["CWE-94"], "VendorSeverity": {"ghsa": 3, "nvd": 3, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:C/C:H/I:L/A:L", "V3Score": 8.1}, "nvd": {"V2Vector": "AV:N/AC:M/Au:N/C:P/I:P/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:C/C:H/I:L/A:L", "V2Score": 6.8, "V3Score": 8.1}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:C/C:H/I:L/A:L", "V3Score": 8.1}}, "References": ["https://access.redhat.com/security/cve/CVE-2019-20920", "https://github.com/handlebars-lang/handlebars.js/commit/156061eb7707575293613d7fdf90e2bdaac029ee", "https://github.com/handlebars-lang/handlebars.js/commit/d54137810a49939fd2ad01a91a34e182ece4528e", "https://nvd.nist.gov/vuln/detail/CVE-2019-20920", "https://snyk.io/vuln/SNYK-JS-HANDLEBARS-534478", "https://www.cve.org/CVERecord?id=CVE-2019-20920", "https://www.npmjs.com/advisories/1316", "https://www.npmjs.com/advisories/1324", "https://www.npmjs.com/package/handlebars"], "PublishedDate": "2020-09-30T18:15:17.927Z", "LastModifiedDate": "2024-11-21T04:39:41.583Z"}, {"VulnerabilityID": "CVE-2019-20922", "PkgID": "handlebars@4.1.2", "PkgName": "handlebars", "PkgIdentifier": {"PURL": "pkg:npm/handlebars@4.1.2", "UID": "bd351504630ed2e5"}, "InstalledVersion": "4.1.2", "FixedVersion": "4.4.5", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2019-20922", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-handlebars: an endless loop while processing specially-crafted templates leads to DoS", "Description": "Handlebars before 4.4.5 allows Regular Expression Denial of Service (ReDoS) because of eager matching. The parser may be forced into an endless loop while processing crafted templates. This may allow attackers to exhaust system resources.", "Severity": "HIGH", "CweIDs": ["CWE-400"], "VendorSeverity": {"ghsa": 3, "nvd": 3, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:N/I:N/A:C", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V2Score": 7.8, "V3Score": 7.5}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}}, "References": ["https://access.redhat.com/security/cve/CVE-2019-20922", "https://github.com/handlebars-lang/handlebars.js/commit/8d5530ee2c3ea9f0aee3fde310b9f36887d00b8b", "https://nvd.nist.gov/vuln/detail/CVE-2019-20922", "https://snyk.io/vuln/SNYK-JS-HANDLEBARS-480388", "https://www.cve.org/CVERecord?id=CVE-2019-20922", "https://www.npmjs.com/advisories/1300", "https://www.npmjs.com/package/handlebars"], "PublishedDate": "2020-09-30T18:15:18.1Z", "LastModifiedDate": "2024-11-21T04:39:41.877Z"}, {"VulnerabilityID": "GHSA-2cf5-4w76-r9qv", "PkgID": "handlebars@4.1.2", "PkgName": "handlebars", "PkgIdentifier": {"PURL": "pkg:npm/handlebars@4.1.2", "UID": "bd351504630ed2e5"}, "InstalledVersion": "4.1.2", "FixedVersion": "3.0.8, 4.5.2", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://github.com/advisories/GHSA-2cf5-4w76-r9qv", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "Arbitrary Code Execution in handlebars", "Description": "Versions of `handlebars` prior to 3.0.8 or 4.5.2 are vulnerable to Arbitrary Code Execution. The package's lookup helper fails to properly validate templates, allowing attackers to submit templates that execute arbitrary JavaScript in the system. It can be used to run arbitrary code in a server processing Handlebars templates or on a victim's browser (effectively serving as Cross-Site Scripting).\n\nThe following template can be used to demonstrate the vulnerability:  \n```{{#with \"constructor\"}}\n\t{{#with split as |a|}}\n\t\t{{pop (push \"alert('Vulnerable Handlebars JS');\")}}\n\t\t{{#with (concat (lookup join (slice 0 1)))}}\n\t\t\t{{#each (slice 2 3)}}\n\t\t\t\t{{#with (apply 0 a)}}\n\t\t\t\t\t{{.}}\n\t\t\t\t{{/with}}\n\t\t\t{{/each}}\n\t\t{{/with}}\n\t{{/with}}\n{{/with}}```\n\n\n## Recommendation\n\nUpgrade to version 3.0.8, 4.5.2 or later.", "Severity": "HIGH", "VendorSeverity": {"ghsa": 3}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:L/AC:L/PR:L/UI:R/S:C/C:L/I:H/A:L", "V3Score": 7.3}}, "References": ["https://www.npmjs.com/advisories/1316"], "PublishedDate": "2020-09-04T14:57:38Z", "LastModifiedDate": "2024-01-29T20:54:51Z"}, {"VulnerabilityID": "GHSA-g9r4-xpmj-mj65", "PkgID": "handlebars@4.1.2", "PkgName": "handlebars", "PkgIdentifier": {"PURL": "pkg:npm/handlebars@4.1.2", "UID": "bd351504630ed2e5"}, "InstalledVersion": "4.1.2", "FixedVersion": "3.0.8, 4.5.3", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://github.com/advisories/GHSA-g9r4-xpmj-mj65", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "Prototype Pollution in handlebars", "Description": "Versions of `handlebars` prior to 3.0.8 or 4.5.3 are vulnerable to prototype pollution. It is possible to add or modify properties to the Object prototype through a malicious template. This may allow attackers to crash the application or execute Arbitrary Code in specific conditions.\n\n\n## Recommendation\n\nUpgrade to version 3.0.8, 4.5.3 or later.", "Severity": "HIGH", "VendorSeverity": {"ghsa": 3}, "References": ["https://www.npmjs.com/advisories/1325"], "PublishedDate": "2020-09-04T15:06:32Z", "LastModifiedDate": "2020-08-31T18:55:14Z"}, {"VulnerabilityID": "GHSA-q2c6-c6pm-g3gh", "PkgID": "handlebars@4.1.2", "PkgName": "handlebars", "PkgIdentifier": {"PURL": "pkg:npm/handlebars@4.1.2", "UID": "bd351504630ed2e5"}, "InstalledVersion": "4.1.2", "FixedVersion": "3.0.8, 4.5.3", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://github.com/advisories/GHSA-q2c6-c6pm-g3gh", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "Arbitrary Code Execution in handlebars", "Description": "Versions of `handlebars` prior to 3.0.8 or 4.5.3 are vulnerable to Arbitrary Code Execution. The package's lookup helper fails to properly validate templates, allowing attackers to submit templates that execute arbitrary JavaScript in the system. It is due to an incomplete fix for a [previous issue](https://www.npmjs.com/advisories/1316). This vulnerability can be used to run arbitrary code in a server processing Handlebars templates or on a victim's browser (effectively serving as Cross-Site Scripting).\n\n\n## Recommendation\n\nUpgrade to version 3.0.8, 4.5.3 or later.", "Severity": "HIGH", "VendorSeverity": {"ghsa": 3}, "References": ["https://www.npmjs.com/advisories/1324"], "PublishedDate": "2020-09-04T15:07:38Z", "LastModifiedDate": "2020-08-31T18:55:11Z"}, {"VulnerabilityID": "GHSA-f52g-6jhx-586p", "PkgID": "handlebars@4.1.2", "PkgName": "handlebars", "PkgIdentifier": {"PURL": "pkg:npm/handlebars@4.1.2", "UID": "bd351504630ed2e5"}, "InstalledVersion": "4.1.2", "FixedVersion": "4.4.5", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://github.com/advisories/GHSA-f52g-6jhx-586p", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "Denial of Service in handlebars", "Description": "Affected versions of `handlebars` are vulnerable to Denial of Service. The package's parser may be forced into an endless loop while processing specially-crafted templates. This may allow attackers to exhaust system resources leading to Denial of Service.\n\n\n## Recommendation\n\nUpgrade to version 4.4.5 or later.", "Severity": "MEDIUM", "VendorSeverity": {"ghsa": 2}, "References": ["https://www.npmjs.com/advisories/1300"], "PublishedDate": "2020-09-03T23:20:12Z", "LastModifiedDate": "2020-08-31T18:54:21Z"}, {"VulnerabilityID": "NSWG-ECO-519", "PkgID": "handlebars@4.1.2", "PkgName": "handlebars", "PkgIdentifier": {"PURL": "pkg:npm/handlebars@4.1.2", "UID": "bd351504630ed2e5"}, "InstalledVersion": "4.1.2", "FixedVersion": ">=4.6.0", "Status": "fixed", "SeveritySource": "nodejs-security-wg", "PrimaryURL": "https://hackerone.com/reports/726364", "DataSource": {"ID": "nodejs-security-wg", "Name": "Node.js Ecosystem Security Working Group", "URL": "https://github.com/nodejs/security-wg"}, "Title": "Denial of Service", "Description": "Crash Node.js process from handlebars using a small and simple source", "Severity": "MEDIUM", "VendorSeverity": {"nodejs-security-wg": 2}, "References": ["https://hackerone.com/reports/726364"]}, {"VulnerabilityID": "CVE-2022-23539", "PkgID": "jsonwebtoken@8.5.1", "PkgName": "jsonwebtoken", "PkgIdentifier": {"PURL": "pkg:npm/jsonwebtoken@8.5.1", "UID": "aff27886c61ac898"}, "InstalledVersion": "8.5.1", "FixedVersion": "9.0.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2022-23539", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "jsonwebtoken: Unrestricted key type could lead to legacy keys usagen", "Description": "Versions `<=8.5.1` of `jsonwebtoken` library could be misconfigured so that legacy, insecure key types are used for signature verification. For example, DSA keys could be used with the RS256 algorithm. You are affected if you are using an algorithm and a key type other than a combination listed in the GitHub Security Advisory as unaffected. This issue has been fixed, please update to version 9.0.0. This version validates for asymmetric key type and algorithm combinations. Please refer to the above mentioned algorithm / key type combinations for the valid secure configuration. After updating to version 9.0.0, if you still intend to continue with signing or verifying tokens using invalid key type/algorithm value combinations, you’ll need to set the `allowInvalidAsymmetricKeyTypes` option  to `true` in the `sign()` and/or `verify()` functions.", "Severity": "HIGH", "CweIDs": ["CWE-327"], "VendorSeverity": {"ghsa": 3, "nvd": 3, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:N", "V3Score": 8.1}, "nvd": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:N", "V3Score": 8.1}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:N", "V3Score": 8.1}}, "References": ["https://access.redhat.com/security/cve/CVE-2022-23539", "https://github.com/auth0/node-jsonwebtoken", "https://github.com/auth0/node-jsonwebtoken/commit/e1fa9dcc12054a8681db4e6373da1b30cf7016e3", "https://github.com/auth0/node-jsonwebtoken/security/advisories/GHSA-8cf7-32gw-wr33", "https://nvd.nist.gov/vuln/detail/CVE-2022-23539", "https://security.netapp.com/advisory/ntap-*************", "https://security.netapp.com/advisory/ntap-*************/", "https://www.cve.org/CVERecord?id=CVE-2022-23539"], "PublishedDate": "2022-12-23T00:15:12.347Z", "LastModifiedDate": "2024-11-21T06:48:46.303Z"}, {"VulnerabilityID": "CVE-2022-23540", "PkgID": "jsonwebtoken@8.5.1", "PkgName": "jsonwebtoken", "PkgIdentifier": {"PURL": "pkg:npm/jsonwebtoken@8.5.1", "UID": "aff27886c61ac898"}, "InstalledVersion": "8.5.1", "FixedVersion": "9.0.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2022-23540", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "jsonwebtoken: Insecure default algorithm in jwt.verify() could lead to signature validation bypass", "Description": "In versions `<=8.5.1` of `jsonwebtoken` library, lack of algorithm definition in the `jwt.verify()` function can lead to signature validation bypass due to defaulting to the `none` algorithm for signature verification. Users are affected if you do not specify algorithms in the `jwt.verify()` function. This issue has been fixed, please update to version 9.0.0 which removes the default support for the none algorithm in the `jwt.verify()` method. There will be no impact, if you update to version 9.0.0 and you don’t need to allow for the `none` algorithm. If you need 'none' algorithm, you have to explicitly specify that in `jwt.verify()` options.", "Severity": "MEDIUM", "CweIDs": ["CWE-287", "CWE-347"], "VendorSeverity": {"ghsa": 2, "nvd": 3, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:H/A:L", "V3Score": 6.4}, "nvd": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:H/A:L", "V3Score": 7.6}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:H/A:L", "V3Score": 6.4}}, "References": ["https://access.redhat.com/security/cve/CVE-2022-23540", "https://github.com/auth0/node-jsonwebtoken", "https://github.com/auth0/node-jsonwebtoken/commit/e1fa9dcc12054a8681db4e6373da1b30cf7016e3", "https://github.com/auth0/node-jsonwebtoken/security/advisories/GHSA-qwph-4952-7xr6", "https://nvd.nist.gov/vuln/detail/CVE-2022-23540", "https://security.netapp.com/advisory/ntap-*************", "https://security.netapp.com/advisory/ntap-*************/", "https://www.cve.org/CVERecord?id=CVE-2022-23540"], "PublishedDate": "2022-12-22T19:15:08.967Z", "LastModifiedDate": "2025-02-13T17:15:38.32Z"}, {"VulnerabilityID": "CVE-2022-23541", "PkgID": "jsonwebtoken@8.5.1", "PkgName": "jsonwebtoken", "PkgIdentifier": {"PURL": "pkg:npm/jsonwebtoken@8.5.1", "UID": "aff27886c61ac898"}, "InstalledVersion": "8.5.1", "FixedVersion": "9.0.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2022-23541", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "jsonwebtoken: Insecure implementation of key retrieval function could lead to Forgeable Public/Private Tokens from RSA to HMAC", "Description": "jsonwebtoken is an implementation of JSON Web Tokens. Versions `<= 8.5.1` of `jsonwebtoken` library can be misconfigured so that passing a poorly implemented key retrieval function referring to the `secretOrPublicKey` argument from the readme link will result in incorrect verification of tokens. There is a possibility of using a different algorithm and key combination in verification, other than the one that was used to sign the tokens. Specifically, tokens signed with an asymmetric public key could be verified with a symmetric HS256 algorithm. This can lead to successful validation of  forged tokens. If your application is supporting usage of both symmetric key and asymmetric key in jwt.verify() implementation with the same key retrieval function. This issue has been patched, please update to version 9.0.0.", "Severity": "MEDIUM", "CweIDs": ["CWE-287", "CWE-1259"], "VendorSeverity": {"ghsa": 2, "nvd": 2, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:L/A:L", "V3Score": 5}, "nvd": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:L", "V3Score": 6.3}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:L/A:L", "V3Score": 5}}, "References": ["https://access.redhat.com/security/cve/CVE-2022-23541", "https://github.com/auth0/node-jsonwebtoken", "https://github.com/auth0/node-jsonwebtoken/commit/e1fa9dcc12054a8681db4e6373da1b30cf7016e3", "https://github.com/auth0/node-jsonwebtoken/releases/tag/v9.0.0", "https://github.com/auth0/node-jsonwebtoken/security/advisories/GHSA-hjrf-2m68-5959", "https://nvd.nist.gov/vuln/detail/CVE-2022-23541", "https://security.netapp.com/advisory/ntap-*************", "https://security.netapp.com/advisory/ntap-*************/", "https://www.cve.org/CVERecord?id=CVE-2022-23541"], "PublishedDate": "2022-12-22T18:15:09.39Z", "LastModifiedDate": "2024-11-21T06:48:46.58Z"}, {"VulnerabilityID": "CVE-2019-10744", "PkgID": "lodash@4.17.11", "PkgName": "lodash", "PkgIdentifier": {"PURL": "pkg:npm/lodash@4.17.11", "UID": "c886ecbaf83736bf"}, "InstalledVersion": "4.17.11", "FixedVersion": "4.17.12", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2019-10744", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-lodash: prototype pollution in defaultsDeep function leading to modifying properties", "Description": "Versions of lodash lower than 4.17.12 are vulnerable to Prototype Pollution. The function defaultsDeep could be tricked into adding or modifying properties of Object.prototype using a constructor payload.", "Severity": "CRITICAL", "CweIDs": ["CWE-1321"], "VendorSeverity": {"ghsa": 4, "nvd": 4, "redhat": 3, "ruby-advisory-db": 4}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:H", "V3Score": 9.1}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:N/I:P/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:H", "V2Score": 6.4, "V3Score": 9.1}, "redhat": {"V3Vector": "CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:H", "V3Score": 9.1}}, "References": ["https://access.redhat.com/errata/RHSA-2019:3024", "https://access.redhat.com/security/cve/CVE-2019-10744", "https://github.com/advisories/GHSA-jf85-cpcp-j695", "https://github.com/lodash/lodash/pull/4336", "https://github.com/rubysec/ruby-advisory-db/blob/master/gems/lodash-rails/CVE-2019-10744.yml", "https://nvd.nist.gov/vuln/detail/CVE-2019-10744", "https://security.netapp.com/advisory/ntap-20191004-0005", "https://security.netapp.com/advisory/ntap-20191004-0005/", "https://snyk.io/vuln/SNYK-JS-LODASH-450202", "https://support.f5.com/csp/article/K47105354", "https://support.f5.com/csp/article/K47105354?utm_source=f5support&amp%3Butm_medium=RSS", "https://support.f5.com/csp/article/K47105354?utm_source=f5support&amp;utm_medium=RSS", "https://www.cve.org/CVERecord?id=CVE-2019-10744", "https://www.npmjs.com/advisories/1065", "https://www.oracle.com/security-alerts/cpujan2021.html", "https://www.oracle.com/security-alerts/cpuoct2020.html"], "PublishedDate": "2019-07-26T00:15:11.217Z", "LastModifiedDate": "2024-11-21T04:19:50.123Z"}, {"VulnerabilityID": "CVE-2020-8203", "PkgID": "lodash@4.17.11", "PkgName": "lodash", "PkgIdentifier": {"PURL": "pkg:npm/lodash@4.17.11", "UID": "c886ecbaf83736bf"}, "InstalledVersion": "4.17.11", "FixedVersion": "4.17.19", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2020-8203", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-lodash: prototype pollution in zipObjectDeep function", "Description": "Prototype pollution attack when using _.zipObjectDeep in lodash before 4.17.20.", "Severity": "HIGH", "CweIDs": ["CWE-770", "CWE-1321"], "VendorSeverity": {"ghsa": 3, "nvd": 3, "redhat": 2, "ruby-advisory-db": 3}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:N/I:H/A:H", "V3Score": 7.4}, "nvd": {"V2Vector": "AV:N/AC:M/Au:N/C:N/I:P/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:N/I:H/A:H", "V2Score": 5.8, "V3Score": 7.4}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:N/I:H/A:H", "V3Score": 7.4}}, "References": ["https://access.redhat.com/security/cve/CVE-2020-8203", "https://github.com/advisories/GHSA-p6mc-m468-83gw", "https://github.com/github/advisory-database/pull/2884", "https://github.com/lodash/lodash", "https://github.com/lodash/lodash/commit/c84fe82760fb2d3e03a63379b297a1cc1a2fce12", "https://github.com/lodash/lodash/issues/4744", "https://github.com/lodash/lodash/issues/4874", "https://github.com/lodash/lodash/wiki/Changelog#v41719", "https://github.com/rubysec/ruby-advisory-db/blob/master/gems/lodash-rails/CVE-2020-8203.yml", "https://hackerone.com/reports/712065", "https://hackerone.com/reports/864701", "https://nvd.nist.gov/vuln/detail/CVE-2020-8203", "https://security.netapp.com/advisory/ntap-20200724-0006", "https://security.netapp.com/advisory/ntap-20200724-0006/", "https://web.archive.org/web/20210914001339/https://github.com/lodash/lodash/issues/4744", "https://www.cve.org/CVERecord?id=CVE-2020-8203", "https://www.npmjs.com/advisories/1523", "https://www.oracle.com//security-alerts/cpujul2021.html", "https://www.oracle.com/security-alerts/cpuApr2021.html", "https://www.oracle.com/security-alerts/cpuapr2022.html", "https://www.oracle.com/security-alerts/cpujan2022.html", "https://www.oracle.com/security-alerts/cpuoct2021.html"], "PublishedDate": "2020-07-15T17:15:11.797Z", "LastModifiedDate": "2024-11-21T05:38:29.79Z"}, {"VulnerabilityID": "CVE-2021-23337", "PkgID": "lodash@4.17.11", "PkgName": "lodash", "PkgIdentifier": {"PURL": "pkg:npm/lodash@4.17.11", "UID": "c886ecbaf83736bf"}, "InstalledVersion": "4.17.11", "FixedVersion": "4.17.21", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2021-23337", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-lodash: command injection via template", "Description": "Lodash versions prior to 4.17.21 are vulnerable to Command Injection via the template function.", "Severity": "HIGH", "CweIDs": ["CWE-94"], "VendorSeverity": {"ghsa": 3, "nvd": 3, "redhat": 2, "ruby-advisory-db": 3, "ubuntu": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:U/C:H/I:H/A:H", "V3Score": 7.2}, "nvd": {"V2Vector": "AV:N/AC:L/Au:S/C:P/I:P/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:U/C:H/I:H/A:H", "V2Score": 6.5, "V3Score": 7.2}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:U/C:H/I:H/A:H", "V3Score": 7.2}}, "References": ["https://access.redhat.com/security/cve/CVE-2021-23337", "https://cert-portal.siemens.com/productcert/pdf/ssa-637483.pdf", "https://github.com/advisories/GHSA-35jh-r3h4-6jhm", "https://github.com/lodash/lodash", "https://github.com/lodash/lodash/blob/ddfd9b11a0126db2302cb70ec9973b66baec0975/lodash.js", "https://github.com/lodash/lodash/blob/ddfd9b11a0126db2302cb70ec9973b66baec0975/lodash.js#L14851", "https://github.com/lodash/lodash/blob/ddfd9b11a0126db2302cb70ec9973b66baec0975/lodash.js%23L14851", "https://github.com/lodash/lodash/commit/3469357cff396a26c363f8c1b5a91dde28ba4b1c", "https://github.com/rubysec/ruby-advisory-db/blob/master/gems/lodash-rails/CVE-2021-23337.yml", "https://nvd.nist.gov/vuln/detail/CVE-2021-23337", "https://security.netapp.com/advisory/ntap-20210312-0006", "https://security.netapp.com/advisory/ntap-20210312-0006/", "https://snyk.io/vuln/SNYK-JAVA-ORGFUJIONWEBJARS-1074932", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARS-1074930", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWER-1074928", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWERGITHUBLODASH-1074931", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-1074929", "https://snyk.io/vuln/SNYK-JS-LODASH-1040724", "https://www.cve.org/CVERecord?id=CVE-2021-23337", "https://www.oracle.com//security-alerts/cpujul2021.html", "https://www.oracle.com/security-alerts/cpujan2022.html", "https://www.oracle.com/security-alerts/cpujul2022.html", "https://www.oracle.com/security-alerts/cpuoct2021.html"], "PublishedDate": "2021-02-15T13:15:12.56Z", "LastModifiedDate": "2024-11-21T05:51:31.643Z"}, {"VulnerabilityID": "CVE-2020-28500", "PkgID": "lodash@4.17.11", "PkgName": "lodash", "PkgIdentifier": {"PURL": "pkg:npm/lodash@4.17.11", "UID": "c886ecbaf83736bf"}, "InstalledVersion": "4.17.11", "FixedVersion": "4.17.21", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2020-28500", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-lodash: ReDoS via the toNumber, trim and trimEnd functions", "Description": "Lodash versions prior to 4.17.21 are vulnerable to Regular Expression Denial of Service (ReDoS) via the toNumber, trim and trimEnd functions.", "Severity": "MEDIUM", "VendorSeverity": {"ghsa": 2, "nvd": 2, "redhat": 2, "ruby-advisory-db": 2, "ubuntu": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "V3Score": 5.3}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:N/I:N/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "V2Score": 5, "V3Score": 5.3}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "V3Score": 5.3}}, "References": ["https://access.redhat.com/security/cve/CVE-2020-28500", "https://cert-portal.siemens.com/productcert/pdf/ssa-637483.pdf", "https://github.com/advisories/GHSA-29mw-wpgm-hmr9", "https://github.com/lodash/lodash", "https://github.com/lodash/lodash/blob/npm/trimEnd.js", "https://github.com/lodash/lodash/blob/npm/trimEnd.js#L8", "https://github.com/lodash/lodash/blob/npm/trimEnd.js%23L8", "https://github.com/lodash/lodash/commit/c4847ebe7d14540bb28a8b932a9ce1b9ecbfee1a", "https://github.com/lodash/lodash/pull/5065", "https://github.com/lodash/lodash/pull/5065/commits/02906b8191d3c100c193fe6f7b27d1c40f200bb7", "https://github.com/rubysec/ruby-advisory-db/blob/master/gems/lodash-rails/CVE-2020-28500.yml", "https://nvd.nist.gov/vuln/detail/CVE-2020-28500", "https://security.netapp.com/advisory/ntap-20210312-0006", "https://security.netapp.com/advisory/ntap-20210312-0006/", "https://snyk.io/vuln/SNYK-JAVA-ORGFUJIONWEBJARS-1074896", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARS-1074894", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWER-1074892", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWERGITHUBLODASH-1074895", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-1074893", "https://snyk.io/vuln/SNYK-JS-LODASH-1018905", "https://www.cve.org/CVERecord?id=CVE-2020-28500", "https://www.oracle.com//security-alerts/cpujul2021.html", "https://www.oracle.com/security-alerts/cpujan2022.html", "https://www.oracle.com/security-alerts/cpujul2022.html", "https://www.oracle.com/security-alerts/cpuoct2021.html"], "PublishedDate": "2021-02-15T11:15:12.397Z", "LastModifiedDate": "2024-11-21T05:22:55.053Z"}, {"VulnerabilityID": "CVE-2022-21680", "PkgID": "marked@0.6.2", "PkgName": "marked", "PkgIdentifier": {"PURL": "pkg:npm/marked@0.6.2", "UID": "1e81d61d11d7350a"}, "InstalledVersion": "0.6.2", "FixedVersion": "4.0.10", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2022-21680", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "marked: regular expression block.def may lead Denial of Service", "Description": "Marked is a markdown parser and compiler. Prior to version 4.0.10, the regular expression `block.def` may cause catastrophic backtracking against some strings and lead to a regular expression denial of service (ReDoS). Anyone who runs untrusted markdown through a vulnerable version of marked and does not use a worker with a time limit may be affected. This issue is patched in version 4.0.10. As a workaround, avoid running untrusted markdown through marked or run marked on a worker thread and set a reasonable time limit to prevent draining resources.", "Severity": "HIGH", "CweIDs": ["CWE-400", "CWE-1333"], "VendorSeverity": {"ghsa": 3, "nvd": 3, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:N/I:N/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V2Score": 5, "V3Score": 7.5}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}}, "References": ["https://access.redhat.com/security/cve/CVE-2022-21680", "https://github.com/markedjs/marked", "https://github.com/markedjs/marked/commit/c4a3ccd344b6929afa8a1d50ac54a721e57012c0", "https://github.com/markedjs/marked/releases/tag/v4.0.10", "https://github.com/markedjs/marked/security/advisories/GHSA-rrrm-qjm4-v8hf", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/AIXDMC3CSHYW3YWVSQOXAWLUYQHAO5UX/", "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/AIXDMC3CSHYW3YWVSQOXAWLUYQHAO5UX", "https://nvd.nist.gov/vuln/detail/CVE-2022-21680", "https://www.cve.org/CVERecord?id=CVE-2022-21680"], "PublishedDate": "2022-01-14T17:15:13.21Z", "LastModifiedDate": "2024-11-21T06:45:13.07Z"}, {"VulnerabilityID": "CVE-2022-21681", "PkgID": "marked@0.6.2", "PkgName": "marked", "PkgIdentifier": {"PURL": "pkg:npm/marked@0.6.2", "UID": "1e81d61d11d7350a"}, "InstalledVersion": "0.6.2", "FixedVersion": "4.0.10", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2022-21681", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "marked: regular expression inline.reflinkSearch may lead Denial of Service", "Description": "Marked is a markdown parser and compiler. Prior to version 4.0.10, the regular expression `inline.reflinkSearch` may cause catastrophic backtracking against some strings and lead to a denial of service (DoS). Anyone who runs untrusted markdown through a vulnerable version of marked and does not use a worker with a time limit may be affected. This issue is patched in version 4.0.10. As a workaround, avoid running untrusted markdown through marked or run marked on a worker thread and set a reasonable time limit to prevent draining resources.", "Severity": "HIGH", "CweIDs": ["CWE-400", "CWE-1333"], "VendorSeverity": {"ghsa": 3, "nvd": 3, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:N/I:N/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V2Score": 5, "V3Score": 7.5}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}}, "References": ["https://access.redhat.com/security/cve/CVE-2022-21681", "https://github.com/markedjs/marked", "https://github.com/markedjs/marked/commit/8f806573a3f6c6b7a39b8cdb66ab5ebb8d55a5f5", "https://github.com/markedjs/marked/commit/c4a3ccd344b6929afa8a1d50ac54a721e57012c0", "https://github.com/markedjs/marked/security/advisories/GHSA-5v2h-r2cx-5xgj", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/AIXDMC3CSHYW3YWVSQOXAWLUYQHAO5UX/", "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/AIXDMC3CSHYW3YWVSQOXAWLUYQHAO5UX", "https://nvd.nist.gov/vuln/detail/CVE-2022-21681", "https://www.cve.org/CVERecord?id=CVE-2022-21681"], "PublishedDate": "2022-01-14T17:15:13.27Z", "LastModifiedDate": "2024-11-21T06:45:13.2Z"}, {"VulnerabilityID": "GHSA-ch52-vgq2-943f", "PkgID": "marked@0.6.2", "PkgName": "marked", "PkgIdentifier": {"PURL": "pkg:npm/marked@0.6.2", "UID": "1e81d61d11d7350a"}, "InstalledVersion": "0.6.2", "FixedVersion": "0.7.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://github.com/advisories/GHSA-ch52-vgq2-943f", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "Regular Expression Denial of Service in marked", "Description": "Affected versions of `marked` are vulnerable to Regular Expression Denial of Service (ReDoS). The `_label` subrule may significantly degrade parsing performance of malformed input.\n\n\n## Recommendation\n\nUpgrade to version 0.7.0 or later.", "Severity": "LOW", "VendorSeverity": {"ghsa": 1}, "References": ["https://www.npmjs.com/advisories/1076"], "PublishedDate": "2020-09-03T18:15:53Z", "LastModifiedDate": "2020-08-31T18:46:28Z"}, {"VulnerabilityID": "CVE-2021-44906", "PkgID": "minimist@0.0.10", "PkgName": "minimist", "PkgIdentifier": {"PURL": "pkg:npm/minimist@0.0.10", "UID": "4a61e4699f7a7db5"}, "InstalledVersion": "0.0.10", "FixedVersion": "1.2.6, 0.2.4", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2021-44906", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "minimist: prototype pollution", "Description": "Minimist <=1.2.5 is vulnerable to Prototype Pollution via file index.js, function setKey() (lines 69-95).", "Severity": "CRITICAL", "CweIDs": ["CWE-1321"], "VendorSeverity": {"alma": 2, "ghsa": 4, "nvd": 4, "oracle-oval": 2, "redhat": 2, "rocky": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "V3Score": 9.8}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:P/I:P/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "V2Score": 7.5, "V3Score": 9.8}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:N/I:L/A:N", "V3Score": 3.1}}, "References": ["https://access.redhat.com/errata/RHSA-2023:0321", "https://access.redhat.com/security/cve/CVE-2021-44906", "https://bugzilla.redhat.com/2066009", "https://bugzilla.redhat.com/2130518", "https://bugzilla.redhat.com/2134609", "https://bugzilla.redhat.com/2140911", "https://bugzilla.redhat.com/show_bug.cgi?id=2066009", "https://bugzilla.redhat.com/show_bug.cgi?id=2130518", "https://bugzilla.redhat.com/show_bug.cgi?id=2134609", "https://bugzilla.redhat.com/show_bug.cgi?id=2140911", "https://bugzilla.redhat.com/show_bug.cgi?id=2142808", "https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-44906", "https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-3517", "https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-35256", "https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-43548", "https://errata.almalinux.org/9/ALSA-2023-0321.html", "https://errata.rockylinux.org/RLSA-2023:0321", "https://github.com/Marynk/JavaScript-vulnerability-detection/blob/main/minimist%20PoC.zip", "https://github.com/advisories/GHSA-xvch-5gv4-984h", "https://github.com/minimistjs/minimist/commit/34e20b8461118608703d6485326abbb8e35e1703", "https://github.com/minimistjs/minimist/commit/bc8ecee43875261f4f17eb20b1243d3ed15e70eb", "https://github.com/minimistjs/minimist/commit/c2b981977fa834b223b408cfb860f933c9811e4d", "https://github.com/minimistjs/minimist/commit/ef9153fc52b6cea0744b2239921c5dcae4697f11", "https://github.com/minimistjs/minimist/commits/v0.2.4", "https://github.com/minimistjs/minimist/issues/11", "https://github.com/minimistjs/minimist/pull/24", "https://github.com/substack/minimist", "https://github.com/substack/minimist/blob/master/index.js#L69", "https://github.com/substack/minimist/issues/164", "https://linux.oracle.com/cve/CVE-2021-44906.html", "https://linux.oracle.com/errata/ELSA-2023-0321.html", "https://nvd.nist.gov/vuln/detail/CVE-2021-44906", "https://security.netapp.com/advisory/ntap-*************", "https://security.netapp.com/advisory/ntap-*************/", "https://snyk.io/vuln/SNYK-JS-MINIMIST-559764", "https://stackoverflow.com/questions/8588563/adding-custom-properties-to-a-function/20278068#20278068", "https://www.cve.org/CVERecord?id=CVE-2021-44906"], "PublishedDate": "2022-03-17T16:15:07.51Z", "LastModifiedDate": "2024-11-21T06:31:40.98Z"}, {"VulnerabilityID": "CVE-2020-7598", "PkgID": "minimist@0.0.10", "PkgName": "minimist", "PkgIdentifier": {"PURL": "pkg:npm/minimist@0.0.10", "UID": "4a61e4699f7a7db5"}, "InstalledVersion": "0.0.10", "FixedVersion": "0.2.1, 1.2.3", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2020-7598", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-minimist: prototype pollution allows adding or modifying properties of Object.prototype using a constructor or __proto__ payload", "Description": "minimist before 1.2.2 could be tricked into adding or modifying properties of Object.prototype using a \"constructor\" or \"__proto__\" payload.", "Severity": "MEDIUM", "CweIDs": ["CWE-1321"], "VendorSeverity": {"alma": 3, "ghsa": 2, "nvd": 2, "oracle-oval": 3, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:L/A:L", "V3Score": 5.6}, "nvd": {"V2Vector": "AV:N/AC:M/Au:N/C:P/I:P/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:L/A:L", "V2Score": 6.8, "V3Score": 5.6}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:L/A:L", "V3Score": 5.6}}, "References": ["http://lists.opensuse.org/opensuse-security-announce/2020-06/msg00024.html", "https://access.redhat.com/security/cve/CVE-2020-7598", "https://errata.almalinux.org/8/ALSA-2020-2852.html", "https://github.com/minimistjs/minimist/commit/10bd4cdf49d9686d48214be9d579a9cdfda37c68", "https://github.com/minimistjs/minimist/commit/38a4d1caead72ef99e824bb420a2528eec03d9ab", "https://github.com/minimistjs/minimist/commit/4cf1354839cb972e38496d35e12f806eea92c11f#diff-a1e0ee62c91705696ddb71aa30ad4f95", "https://github.com/minimistjs/minimist/commit/63e7ed05aa4b1889ec2f3b196426db4500cbda94", "https://github.com/substack/minimist", "https://linux.oracle.com/cve/CVE-2020-7598.html", "https://linux.oracle.com/errata/ELSA-2020-2852.html", "https://nvd.nist.gov/vuln/detail/CVE-2020-7598", "https://snyk.io/vuln/SNYK-JS-MINIMIST-559764", "https://www.cve.org/CVERecord?id=CVE-2020-7598", "https://www.npmjs.com/advisories/1179"], "PublishedDate": "2020-03-11T23:15:11.917Z", "LastModifiedDate": "2024-11-21T05:37:26.9Z"}, {"VulnerabilityID": "CVE-2021-44906", "PkgID": "minimist@1.2.0", "PkgName": "minimist", "PkgIdentifier": {"PURL": "pkg:npm/minimist@1.2.0", "UID": "2fd9ea45d56a9f0e"}, "InstalledVersion": "1.2.0", "FixedVersion": "1.2.6, 0.2.4", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2021-44906", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "minimist: prototype pollution", "Description": "Minimist <=1.2.5 is vulnerable to Prototype Pollution via file index.js, function setKey() (lines 69-95).", "Severity": "CRITICAL", "CweIDs": ["CWE-1321"], "VendorSeverity": {"alma": 2, "ghsa": 4, "nvd": 4, "oracle-oval": 2, "redhat": 2, "rocky": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "V3Score": 9.8}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:P/I:P/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "V2Score": 7.5, "V3Score": 9.8}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:N/I:L/A:N", "V3Score": 3.1}}, "References": ["https://access.redhat.com/errata/RHSA-2023:0321", "https://access.redhat.com/security/cve/CVE-2021-44906", "https://bugzilla.redhat.com/2066009", "https://bugzilla.redhat.com/2130518", "https://bugzilla.redhat.com/2134609", "https://bugzilla.redhat.com/2140911", "https://bugzilla.redhat.com/show_bug.cgi?id=2066009", "https://bugzilla.redhat.com/show_bug.cgi?id=2130518", "https://bugzilla.redhat.com/show_bug.cgi?id=2134609", "https://bugzilla.redhat.com/show_bug.cgi?id=2140911", "https://bugzilla.redhat.com/show_bug.cgi?id=2142808", "https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-44906", "https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-3517", "https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-35256", "https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-43548", "https://errata.almalinux.org/9/ALSA-2023-0321.html", "https://errata.rockylinux.org/RLSA-2023:0321", "https://github.com/Marynk/JavaScript-vulnerability-detection/blob/main/minimist%20PoC.zip", "https://github.com/advisories/GHSA-xvch-5gv4-984h", "https://github.com/minimistjs/minimist/commit/34e20b8461118608703d6485326abbb8e35e1703", "https://github.com/minimistjs/minimist/commit/bc8ecee43875261f4f17eb20b1243d3ed15e70eb", "https://github.com/minimistjs/minimist/commit/c2b981977fa834b223b408cfb860f933c9811e4d", "https://github.com/minimistjs/minimist/commit/ef9153fc52b6cea0744b2239921c5dcae4697f11", "https://github.com/minimistjs/minimist/commits/v0.2.4", "https://github.com/minimistjs/minimist/issues/11", "https://github.com/minimistjs/minimist/pull/24", "https://github.com/substack/minimist", "https://github.com/substack/minimist/blob/master/index.js#L69", "https://github.com/substack/minimist/issues/164", "https://linux.oracle.com/cve/CVE-2021-44906.html", "https://linux.oracle.com/errata/ELSA-2023-0321.html", "https://nvd.nist.gov/vuln/detail/CVE-2021-44906", "https://security.netapp.com/advisory/ntap-*************", "https://security.netapp.com/advisory/ntap-*************/", "https://snyk.io/vuln/SNYK-JS-MINIMIST-559764", "https://stackoverflow.com/questions/8588563/adding-custom-properties-to-a-function/20278068#20278068", "https://www.cve.org/CVERecord?id=CVE-2021-44906"], "PublishedDate": "2022-03-17T16:15:07.51Z", "LastModifiedDate": "2024-11-21T06:31:40.98Z"}, {"VulnerabilityID": "CVE-2020-7598", "PkgID": "minimist@1.2.0", "PkgName": "minimist", "PkgIdentifier": {"PURL": "pkg:npm/minimist@1.2.0", "UID": "2fd9ea45d56a9f0e"}, "InstalledVersion": "1.2.0", "FixedVersion": "0.2.1, 1.2.3", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2020-7598", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-minimist: prototype pollution allows adding or modifying properties of Object.prototype using a constructor or __proto__ payload", "Description": "minimist before 1.2.2 could be tricked into adding or modifying properties of Object.prototype using a \"constructor\" or \"__proto__\" payload.", "Severity": "MEDIUM", "CweIDs": ["CWE-1321"], "VendorSeverity": {"alma": 3, "ghsa": 2, "nvd": 2, "oracle-oval": 3, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:L/A:L", "V3Score": 5.6}, "nvd": {"V2Vector": "AV:N/AC:M/Au:N/C:P/I:P/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:L/A:L", "V2Score": 6.8, "V3Score": 5.6}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:L/A:L", "V3Score": 5.6}}, "References": ["http://lists.opensuse.org/opensuse-security-announce/2020-06/msg00024.html", "https://access.redhat.com/security/cve/CVE-2020-7598", "https://errata.almalinux.org/8/ALSA-2020-2852.html", "https://github.com/minimistjs/minimist/commit/10bd4cdf49d9686d48214be9d579a9cdfda37c68", "https://github.com/minimistjs/minimist/commit/38a4d1caead72ef99e824bb420a2528eec03d9ab", "https://github.com/minimistjs/minimist/commit/4cf1354839cb972e38496d35e12f806eea92c11f#diff-a1e0ee62c91705696ddb71aa30ad4f95", "https://github.com/minimistjs/minimist/commit/63e7ed05aa4b1889ec2f3b196426db4500cbda94", "https://github.com/substack/minimist", "https://linux.oracle.com/cve/CVE-2020-7598.html", "https://linux.oracle.com/errata/ELSA-2020-2852.html", "https://nvd.nist.gov/vuln/detail/CVE-2020-7598", "https://snyk.io/vuln/SNYK-JS-MINIMIST-559764", "https://www.cve.org/CVERecord?id=CVE-2020-7598", "https://www.npmjs.com/advisories/1179"], "PublishedDate": "2020-03-11T23:15:11.917Z", "LastModifiedDate": "2024-11-21T05:37:26.9Z"}, {"VulnerabilityID": "CVE-2022-24785", "PkgID": "moment@2.24.0", "PkgName": "moment", "PkgIdentifier": {"PURL": "pkg:npm/moment@2.24.0", "UID": "488dee6c0b68f5d8"}, "InstalledVersion": "2.24.0", "FixedVersion": "2.29.2", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2022-24785", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "Moment.js: Path traversal  in moment.locale", "Description": "Moment.js is a JavaScript date library for parsing, validating, manipulating, and formatting dates. A path traversal vulnerability impacts npm (server) users of Moment.js between versions 1.0.1 and 2.29.1, especially if a user-provided locale string is directly used to switch moment locale. This problem is patched in 2.29.2, and the patch can be applied to all affected versions. As a workaround, sanitize the user-provided locale name before passing it to Moment.js.", "Severity": "HIGH", "CweIDs": ["CWE-22", "CWE-27"], "VendorSeverity": {"ghsa": 3, "nvd": 3, "redhat": 2, "ubuntu": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N", "V3Score": 7.5}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:N/I:P/A:N", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N", "V2Score": 5, "V3Score": 7.5}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N", "V3Score": 7.5}}, "References": ["https://access.redhat.com/security/cve/CVE-2022-24785", "https://github.com/moment/moment", "https://github.com/moment/moment/commit/4211bfc8f15746be4019bba557e29a7ba83d54c5", "https://github.com/moment/moment/security/advisories/GHSA-8hfj-j24r-96c4", "https://lists.debian.org/debian-lts-announce/2023/01/msg00035.html", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/6QIO6YNLTK2T7SPKDS4JEL45FANLNC2Q/", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/ORJX2LF6KMPIHP6B2P6KZIVKMLE3LVJ5/", "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/6QIO6YNLTK2T7SPKDS4JEL45FANLNC2Q", "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/ORJX2LF6KMPIHP6B2P6KZIVKMLE3LVJ5", "https://nvd.nist.gov/vuln/detail/CVE-2022-24785", "https://security.netapp.com/advisory/ntap-*************", "https://security.netapp.com/advisory/ntap-*************/", "https://ubuntu.com/security/notices/USN-5559-1", "https://www.cve.org/CVERecord?id=CVE-2022-24785", "https://www.tenable.com/security/tns-2022-09"], "PublishedDate": "2022-04-04T17:15:07.583Z", "LastModifiedDate": "2024-11-21T06:51:05.483Z"}, {"VulnerabilityID": "CVE-2022-31129", "PkgID": "moment@2.24.0", "PkgName": "moment", "PkgIdentifier": {"PURL": "pkg:npm/moment@2.24.0", "UID": "488dee6c0b68f5d8"}, "InstalledVersion": "2.24.0", "FixedVersion": "2.29.4", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2022-31129", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "moment: inefficient parsing algorithm resulting in DoS", "Description": "moment is a JavaScript date library for parsing, validating, manipulating, and formatting dates. Affected versions of moment were found to use an inefficient parsing algorithm. Specifically using string-to-date parsing in moment (more specifically rfc2822 parsing, which is tried by default) has quadratic (N^2) complexity on specific inputs. Users may notice a noticeable slowdown is observed with inputs above 10k characters. Users who pass user-provided strings without sanity length checks to moment constructor are vulnerable to (Re)DoS attacks. The problem is patched in 2.29.4, the patch can be applied to all affected versions with minimal tweaking. Users are advised to upgrade. Users unable to upgrade should consider limiting date lengths accepted from user input.", "Severity": "HIGH", "CweIDs": ["CWE-400", "CWE-1333"], "VendorSeverity": {"ghsa": 3, "nvd": 3, "redhat": 3, "ubuntu": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:N/I:N/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V2Score": 5, "V3Score": 7.5}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}}, "References": ["https://access.redhat.com/security/cve/CVE-2022-31129", "https://github.com/moment/moment", "https://github.com/moment/moment/commit/9a3b5894f3d5d602948ac8a02e4ee528a49ca3a3", "https://github.com/moment/moment/pull/6015#issuecomment-1152961973", "https://github.com/moment/moment/pull/6015/commits/4bbb9f3ccbe231de40207503f344fe5ce97584f4", "https://github.com/moment/moment/pull/6015/commits/bfd4f2375d5c1a2106246721d693a9611dddfbfe", "https://github.com/moment/moment/pull/6015/commits/dc0d180e90d8a84f7ff13572363330a22b3ea504", "https://github.com/moment/moment/security/advisories/GHSA-wc69-rhjr-hc9g", "https://huntr.dev/bounties/f0952b67-f2ff-44a9-a9cd-99e0a87cb633", "https://huntr.dev/bounties/f0952b67-f2ff-44a9-a9cd-99e0a87cb633/", "https://lists.debian.org/debian-lts-announce/2023/01/msg00035.html", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/6QIO6YNLTK2T7SPKDS4JEL45FANLNC2Q/", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/IWY24RJA3SBJGA5N4CU4VBPHJPPPJL5O/", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/ORJX2LF6KMPIHP6B2P6KZIVKMLE3LVJ5/", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/ZMX5YHELQVCGKKQVFXIYOTBMN23YYSRO/", "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/6QIO6YNLTK2T7SPKDS4JEL45FANLNC2Q", "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/IWY24RJA3SBJGA5N4CU4VBPHJPPPJL5O", "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/ORJX2LF6KMPIHP6B2P6KZIVKMLE3LVJ5", "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/ZMX5YHELQVCGKKQVFXIYOTBMN23YYSRO", "https://nvd.nist.gov/vuln/detail/CVE-2022-31129", "https://security.netapp.com/advisory/ntap-*************", "https://security.netapp.com/advisory/ntap-*************/", "https://ubuntu.com/security/notices/USN-5559-1", "https://ubuntu.com/security/notices/USN-6550-1", "https://www.cve.org/CVERecord?id=CVE-2022-31129"], "PublishedDate": "2022-07-06T18:15:19.57Z", "LastModifiedDate": "2024-11-21T07:03:57.43Z"}, {"VulnerabilityID": "CVE-2025-47935", "PkgID": "m<PERSON>@1.4.2", "PkgName": "multer", "PkgIdentifier": {"PURL": "pkg:npm/multer@1.4.2", "UID": "e35f7266a398e805"}, "InstalledVersion": "1.4.2", "FixedVersion": "2.0.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2025-47935", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "Multer vulnerable to Denial of Service via memory leaks from unclosed streams", "Description": "Multer is a node.js middleware for handling `multipart/form-data`. Versions prior to 2.0.0 are vulnerable to a resource exhaustion and memory leak issue due to improper stream handling. When the HTTP request stream emits an error, the internal `busboy` stream is not closed, violating Node.js stream safety guidance. This leads to unclosed streams accumulating over time, consuming memory and file descriptors. Under sustained or repeated failure conditions, this can result in denial of service, requiring manual server restarts to recover. All users of Multer handling file uploads are potentially impacted. Users should upgrade to 2.0.0 to receive a patch. No known workarounds are available.", "Severity": "HIGH", "CweIDs": ["CWE-401"], "VendorSeverity": {"ghsa": 3}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}}, "References": ["https://github.com/expressjs/multer", "https://github.com/expressjs/multer/commit/2c8505f207d923dd8de13a9f93a4563e59933665", "https://github.com/expressjs/multer/pull/1120", "https://github.com/expressjs/multer/security/advisories/GHSA-44fp-w29j-9vj5", "https://nvd.nist.gov/vuln/detail/CVE-2025-47935"], "PublishedDate": "2025-05-19T20:15:25.863Z", "LastModifiedDate": "2025-05-21T20:25:16.407Z"}, {"VulnerabilityID": "CVE-2019-14939", "PkgID": "mysql@2.17.1", "PkgName": "mysql", "PkgIdentifier": {"PURL": "pkg:npm/mysql@2.17.1", "UID": "a21171966c082a18"}, "InstalledVersion": "2.17.1", "FixedVersion": "2.18.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2019-14939", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "An issue was discovered in the mysql (aka mysqljs) module 2.17.1 for N ...", "Description": "An issue was discovered in the mysql (aka mysqljs) module 2.17.1 for Node.js. The LOAD DATA LOCAL INFILE option is open by default.", "Severity": "MEDIUM", "VendorSeverity": {"ghsa": 2, "nvd": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.0/AV:L/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N", "V3Score": 5.5}, "nvd": {"V2Vector": "AV:L/AC:L/Au:N/C:P/I:N/A:N", "V3Vector": "CVSS:3.0/AV:L/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N", "V2Score": 2.1, "V3Score": 5.5}}, "References": ["https://bugs.debian.org/cgi-bin/bugreport.cgi?bug=934712&gt", "https://github.com/mysqljs/mysql", "https://github.com/mysqljs/mysql/commit/337e87ae5fcea3667864197c65dc758517fcde06", "https://github.com/mysqljs/mysql/issues/2257", "https://github.com/mysqljs/mysql/issues/2471", "https://nvd.nist.gov/vuln/detail/CVE-2019-14939", "https://web.archive.org/web/20190812004403/https://github.com/mysqljs/mysql/issues/2257"], "PublishedDate": "2019-08-12T01:15:13.72Z", "LastModifiedDate": "2024-11-21T04:27:43.603Z"}, {"VulnerabilityID": "CVE-2020-7720", "PkgID": "node-forge@0.8.5", "PkgName": "node-forge", "PkgIdentifier": {"PURL": "pkg:npm/node-forge@0.8.5", "UID": "d8d8aa1c2f7746a"}, "InstalledVersion": "0.8.5", "FixedVersion": "0.10.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2020-7720", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-node-forge: prototype pollution via the util.setPath function", "Description": "The package node-forge before 0.10.0 is vulnerable to Prototype Pollution via the util.setPath function. Note: Version 0.10.0 is a breaking change removing the vulnerable functions.", "Severity": "HIGH", "CweIDs": ["CWE-1321"], "VendorSeverity": {"ghsa": 3, "nvd": 3, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H/E:P/RL:O/RC:C", "V3Score": 8.8}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:P/I:P/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:L", "V2Score": 7.5, "V3Score": 7.3}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:L", "V3Score": 7.3}}, "References": ["https://access.redhat.com/security/cve/CVE-2020-7720", "https://github.com/digitalbazaar/forge", "https://github.com/digitalbazaar/forge/blob/master/CHANGELOG.md", "https://github.com/digitalbazaar/forge/blob/master/CHANGELOG.md#removed", "https://github.com/digitalbazaar/forge/commit/6a1e3ef74f6eb345bcff1b82184201d1e28b6756", "https://nvd.nist.gov/vuln/detail/CVE-2020-7720", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-609293", "https://snyk.io/vuln/SNYK-JS-NODEFORGE-598677", "https://www.cve.org/CVERecord?id=CVE-2020-7720"], "PublishedDate": "2020-09-01T10:15:10.607Z", "LastModifiedDate": "2024-11-21T05:37:40.667Z"}, {"VulnerabilityID": "CVE-2022-24771", "PkgID": "node-forge@0.8.5", "PkgName": "node-forge", "PkgIdentifier": {"PURL": "pkg:npm/node-forge@0.8.5", "UID": "d8d8aa1c2f7746a"}, "InstalledVersion": "0.8.5", "FixedVersion": "1.3.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2022-24771", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "node-forge: Signature verification leniency in checking `digestAlgorithm` structure can lead to signature forgery", "Description": "Forge (also called `node-forge`) is a native implementation of Transport Layer Security in JavaScript. Prior to version 1.3.0, RSA PKCS#1 v1.5 signature verification code is lenient in checking the digest algorithm structure. This can allow a crafted structure that steals padding bytes and uses unchecked portion of the PKCS#1 encoded message to forge a signature when a low public exponent is being used. The issue has been addressed in `node-forge` version 1.3.0. There are currently no known workarounds.", "Severity": "HIGH", "CweIDs": ["CWE-347"], "VendorSeverity": {"ghsa": 3, "nvd": 3, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N", "V3Score": 7.5}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:N/I:P/A:N", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N", "V2Score": 5, "V3Score": 7.5}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N", "V3Score": 7.5}}, "References": ["https://access.redhat.com/security/cve/CVE-2022-24771", "https://github.com/digitalbazaar/forge", "https://github.com/digitalbazaar/forge/commit/3f0b49a0573ef1bb7af7f5673c0cfebf00424df1", "https://github.com/digitalbazaar/forge/commit/bb822c02df0b61211836472e29b9790cc541cdb2", "https://github.com/digitalbazaar/forge/security/advisories/GHSA-cfm4-qjh2-4765", "https://nvd.nist.gov/vuln/detail/CVE-2022-24771", "https://www.cve.org/CVERecord?id=CVE-2022-24771"], "PublishedDate": "2022-03-18T14:15:10.28Z", "LastModifiedDate": "2024-11-21T06:51:03.86Z"}, {"VulnerabilityID": "CVE-2022-24772", "PkgID": "node-forge@0.8.5", "PkgName": "node-forge", "PkgIdentifier": {"PURL": "pkg:npm/node-forge@0.8.5", "UID": "d8d8aa1c2f7746a"}, "InstalledVersion": "0.8.5", "FixedVersion": "1.3.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2022-24772", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "node-forge: Signature verification failing to check tailing garbage bytes can lead to signature forgery", "Description": "Forge (also called `node-forge`) is a native implementation of Transport Layer Security in JavaScript. Prior to version 1.3.0, RSA PKCS#1 v1.5 signature verification code does not check for tailing garbage bytes after decoding a `DigestInfo` ASN.1 structure. This can allow padding bytes to be removed and garbage data added to forge a signature when a low public exponent is being used. The issue has been addressed in `node-forge` version 1.3.0. There are currently no known workarounds.", "Severity": "HIGH", "CweIDs": ["CWE-347"], "VendorSeverity": {"ghsa": 3, "nvd": 3, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N", "V3Score": 7.5}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:N/I:P/A:N", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N", "V2Score": 5, "V3Score": 7.5}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N", "V3Score": 7.5}}, "References": ["https://access.redhat.com/security/cve/CVE-2022-24772", "https://github.com/digitalbazaar/forge", "https://github.com/digitalbazaar/forge/commit/3f0b49a0573ef1bb7af7f5673c0cfebf00424df1", "https://github.com/digitalbazaar/forge/commit/bb822c02df0b61211836472e29b9790cc541cdb2", "https://github.com/digitalbazaar/forge/security/advisories/GHSA-x4jg-mjrx-434g", "https://nvd.nist.gov/vuln/detail/CVE-2022-24772", "https://www.cve.org/CVERecord?id=CVE-2022-24772"], "PublishedDate": "2022-03-18T14:15:10.353Z", "LastModifiedDate": "2024-11-21T06:51:03.973Z"}, {"VulnerabilityID": "CVE-2022-0122", "PkgID": "node-forge@0.8.5", "PkgName": "node-forge", "PkgIdentifier": {"PURL": "pkg:npm/node-forge@0.8.5", "UID": "d8d8aa1c2f7746a"}, "InstalledVersion": "0.8.5", "FixedVersion": "1.0.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2022-0122", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "Open Redirect in node-forge", "Description": "forge is vulnerable to URL Redirection to Untrusted Site", "Severity": "MEDIUM", "CweIDs": ["CWE-601"], "VendorSeverity": {"ghsa": 2, "nvd": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N", "V3Score": 6.1}, "nvd": {"V2Vector": "AV:N/AC:M/Au:N/C:P/I:P/A:N", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N", "V2Score": 5.8, "V3Score": 6.1}}, "References": ["https://github.com/digitalbazaar/forge", "https://github.com/digitalbazaar/forge/commit/db8016c805371e72b06d8e2edfe0ace0df934a5e", "https://huntr.dev/bounties/41852c50-3c6d-4703-8c55-4db27164a4ae", "https://nvd.nist.gov/vuln/detail/CVE-2022-0122"], "PublishedDate": "2022-01-06T05:15:09.49Z", "LastModifiedDate": "2024-11-21T06:37:57.103Z"}, {"VulnerabilityID": "CVE-2022-24773", "PkgID": "node-forge@0.8.5", "PkgName": "node-forge", "PkgIdentifier": {"PURL": "pkg:npm/node-forge@0.8.5", "UID": "d8d8aa1c2f7746a"}, "InstalledVersion": "0.8.5", "FixedVersion": "1.3.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2022-24773", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "node-forge: Signature verification leniency in checking `DigestInfo` structure", "Description": "Forge (also called `node-forge`) is a native implementation of Transport Layer Security in JavaScript. Prior to version 1.3.0, RSA PKCS#1 v1.5 signature verification code does not properly check `DigestInfo` for a proper ASN.1 structure. This can lead to successful verification with signatures that contain invalid structures but a valid digest. The issue has been addressed in `node-forge` version 1.3.0. There are currently no known workarounds.", "Severity": "MEDIUM", "CweIDs": ["CWE-347"], "VendorSeverity": {"ghsa": 2, "nvd": 2, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N", "V3Score": 5.3}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:N/I:P/A:N", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N", "V2Score": 5, "V3Score": 5.3}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N", "V3Score": 5.3}}, "References": ["https://access.redhat.com/security/cve/CVE-2022-24773", "https://github.com/digitalbazaar/forge", "https://github.com/digitalbazaar/forge/commit/3f0b49a0573ef1bb7af7f5673c0cfebf00424df1", "https://github.com/digitalbazaar/forge/commit/bb822c02df0b61211836472e29b9790cc541cdb2", "https://github.com/digitalbazaar/forge/security/advisories/GHSA-2r2c-g63r-vccr", "https://nvd.nist.gov/vuln/detail/CVE-2022-24773", "https://www.cve.org/CVERecord?id=CVE-2022-24773"], "PublishedDate": "2022-03-18T14:15:10.427Z", "LastModifiedDate": "2024-11-21T06:51:04.093Z"}, {"VulnerabilityID": "GHSA-5rrq-pxf6-6jx5", "PkgID": "node-forge@0.8.5", "PkgName": "node-forge", "PkgIdentifier": {"PURL": "pkg:npm/node-forge@0.8.5", "UID": "d8d8aa1c2f7746a"}, "InstalledVersion": "0.8.5", "FixedVersion": "1.0.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://github.com/advisories/GHSA-5rrq-pxf6-6jx5", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "Prototype Pollution in node-forge debug API.", "Description": "### Impact\nThe `forge.debug` API had a potential prototype pollution issue if called with untrusted input. The API was only used for internal debug purposes in a safe way and never documented or advertised.  It is suspected that uses of this API, if any exist, would likely not have used untrusted inputs in a vulnerable way.\n\n### Patches\nThe `forge.debug` API and related functions were removed in 1.0.0.\n\n### Workarounds\nDon't use the `forge.debug` API directly or indirectly with untrusted input.\n\n### References\n- https://www.huntr.dev/bounties/1-npm-node-forge/\n\n### For more information\nIf you have any questions or comments about this advisory:\n* Open an issue in [forge](https://github.com/digitalbazaar/forge).\n* Email <NAME_EMAIL>.", "Severity": "LOW", "VendorSeverity": {"ghsa": 1}, "References": ["https://github.com/digitalbazaar/forge", "https://github.com/digitalbazaar/forge/security/advisories/GHSA-5rrq-pxf6-6jx5"], "PublishedDate": "2022-01-08T00:22:42Z", "LastModifiedDate": "2022-01-07T22:20:53Z"}, {"VulnerabilityID": "GHSA-gf8q-jrpm-jvxq", "PkgID": "node-forge@0.8.5", "PkgName": "node-forge", "PkgIdentifier": {"PURL": "pkg:npm/node-forge@0.8.5", "UID": "d8d8aa1c2f7746a"}, "InstalledVersion": "0.8.5", "FixedVersion": "1.0.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://github.com/advisories/GHSA-gf8q-jrpm-jvxq", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "URL parsing in node-forge could lead to undesired behavior.", "Description": "### Impact\nThe regex used for the `forge.util.parseUrl` API would not properly parse certain inputs resulting in a parsed data structure that could lead to undesired behavior.\n\n### Patches\n`forge.util.parseUrl` and other very old related URL APIs were removed in 1.0.0 in favor of letting applications use the more modern WHATWG URL Standard API.\n\n### Workarounds\nEnsure code does not directly or indirectly call `forge.util.parseUrl` with untrusted input.\n\n### References\n- https://www.huntr.dev/bounties/41852c50-3c6d-4703-8c55-4db27164a4ae/\n\n### For more information\nIf you have any questions or comments about this advisory:\n* Open an issue in [forge](https://github.com/digitalbazaar/forge)\n* Email <NAME_EMAIL>\n", "Severity": "LOW", "VendorSeverity": {"ghsa": 1}, "References": ["https://github.com/digitalbazaar/forge", "https://github.com/digitalbazaar/forge/security/advisories/GHSA-gf8q-jrpm-jvxq", "https://nvd.nist.gov/vuln/detail/CVE-2022-0122", "https://www.huntr.dev/bounties/41852c50-3c6d-4703-8c55-4db27164a4ae"], "PublishedDate": "2022-01-08T00:22:02Z", "LastModifiedDate": "2022-01-07T22:20:43Z"}, {"VulnerabilityID": "GHSA-wxgw-qj99-44c2", "PkgID": "node-forge@0.8.5", "PkgName": "node-forge", "PkgIdentifier": {"PURL": "pkg:npm/node-forge@0.8.5", "UID": "d8d8aa1c2f7746a"}, "InstalledVersion": "0.8.5", "FixedVersion": "0.10.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://github.com/advisories/GHSA-wxgw-qj99-44c2", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "Prototype Pollution in node-forge util.setPath API", "Description": "### Impact\n`forge.util.setPath` had a potential prototype pollution issue if called with untrusted keys. This API was not used by forge itself.\n\n### Patches\nThe `forge.util.setPath` API and related functions were removed in 0.10.0.\n\n### Workarounds\nDon't call `forge.util.setPath` directly or indirectly with untrusted keys.\n\n### References\n- https://security.snyk.io/vuln/SNYK-JS-NODEFORGE-598677\n- https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2020-7720\n\n### For more information\nIf you have any questions or comments about this advisory:\n* Open an issue in [forge](https://github.com/digitalbazaar/forge).\n* Email <NAME_EMAIL>.", "Severity": "LOW", "VendorSeverity": {"ghsa": 1}, "References": ["https://github.com/digitalbazaar/forge/security/advisories/GHSA-wxgw-qj99-44c2"], "PublishedDate": "2022-01-08T00:22:40Z", "LastModifiedDate": "2022-01-07T22:20:50Z"}, {"VulnerabilityID": "CVE-2024-36751", "PkgID": "parseuri@0.0.5", "PkgName": "parseuri", "PkgIdentifier": {"PURL": "pkg:npm/parseuri@0.0.5", "UID": "d4750b50ecf6ab42"}, "InstalledVersion": "0.0.5", "FixedVersion": "2.0.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2024-36751", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "parse-uri Regular expression Denial of Service (ReDoS)", "Description": "An issue in parse-uri v1.0.9 allows attackers to cause a Regular expression Denial of Service (ReDoS) via a crafted URL.", "Severity": "MEDIUM", "CweIDs": ["CWE-1333"], "VendorSeverity": {"ghsa": 2}, "References": ["https://gist.github.com/6en6ar/78168687da94e8aa2e0357f2456b0233", "https://github.com/Kikobeats/parse-uri/issues/14", "https://nvd.nist.gov/vuln/detail/CVE-2024-36751"], "PublishedDate": "2025-01-15T22:15:26.4Z", "LastModifiedDate": "2025-02-03T15:15:16.733Z"}, {"VulnerabilityID": "CVE-2024-45296", "PkgID": "path-to-regexp@0.1.7", "PkgName": "path-to-regexp", "PkgIdentifier": {"PURL": "pkg:npm/path-to-regexp@0.1.7", "UID": "46cb9e2573603b1a"}, "InstalledVersion": "0.1.7", "FixedVersion": "1.9.0, 0.1.10, 8.0.0, 3.3.0, 6.3.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2024-45296", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "path-to-regexp: Backtracking regular expressions cause ReDoS", "Description": "path-to-regexp turns path strings into a regular expressions. In certain cases, path-to-regexp will output a regular expression that can be exploited to cause poor performance. Because JavaScript is single threaded and regex matching runs on the main thread, poor performance will block the event loop and lead to a DoS. The bad regular expression is generated any time you have two parameters within a single segment, separated by something that is not a period (.). For users of 0.1, upgrade to 0.1.10. All other users should upgrade to 8.0.0.", "Severity": "HIGH", "CweIDs": ["CWE-1333"], "VendorSeverity": {"cbl-mariner": 3, "ghsa": 3, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "V3Score": 5.3}}, "References": ["https://access.redhat.com/security/cve/CVE-2024-45296", "https://github.com/pillarjs/path-to-regexp", "https://github.com/pillarjs/path-to-regexp/commit/29b96b4a1de52824e1ca0f49a701183cc4ed476f", "https://github.com/pillarjs/path-to-regexp/commit/60f2121e9b66b7b622cc01080df0aabda9eedee6", "https://github.com/pillarjs/path-to-regexp/commit/925ac8e3c5780b02f58cbd4e52f95da8ad2ac485", "https://github.com/pillarjs/path-to-regexp/commit/d31670ae8f6e69cbfd56e835742195b7d10942ef", "https://github.com/pillarjs/path-to-regexp/commit/f1253b47b347dcb909e3e80b0eb2649109e59894", "https://github.com/pillarjs/path-to-regexp/releases/tag/v6.3.0", "https://github.com/pillarjs/path-to-regexp/security/advisories/GHSA-9wv6-86v2-598j", "https://nvd.nist.gov/vuln/detail/CVE-2024-45296", "https://security.netapp.com/advisory/ntap-*************", "https://security.netapp.com/advisory/ntap-*************/", "https://www.cve.org/CVERecord?id=CVE-2024-45296"], "PublishedDate": "2024-09-09T19:15:13.33Z", "LastModifiedDate": "2025-01-24T20:15:32.68Z"}, {"VulnerabilityID": "CVE-2024-52798", "PkgID": "path-to-regexp@0.1.7", "PkgName": "path-to-regexp", "PkgIdentifier": {"PURL": "pkg:npm/path-to-regexp@0.1.7", "UID": "46cb9e2573603b1a"}, "InstalledVersion": "0.1.7", "FixedVersion": "0.1.12", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2024-52798", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "path-to-regexp: path-to-regexp Unpatched `path-to-regexp` ReDoS in 0.1.x", "Description": "path-to-regexp turns path strings into a regular expressions. In certain cases, path-to-regexp will output a regular expression that can be exploited to cause poor performance. The regular expression that is vulnerable to backtracking can be generated in the 0.1.x release of path-to-regexp. Upgrade to 0.1.12. This vulnerability exists because of an incomplete fix for CVE-2024-45296.", "Severity": "HIGH", "CweIDs": ["CWE-1333"], "VendorSeverity": {"cbl-mariner": 2, "ghsa": 3, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "V3Score": 5.3}}, "References": ["https://access.redhat.com/security/cve/CVE-2024-52798", "https://blakeembrey.com/posts/2024-09-web-redos", "https://github.com/pillarjs/path-to-regexp", "https://github.com/pillarjs/path-to-regexp/commit/f01c26a013b1889f0c217c643964513acf17f6a4", "https://github.com/pillarjs/path-to-regexp/security/advisories/GHSA-rhx6-c78j-4q9w", "https://nvd.nist.gov/vuln/detail/CVE-2024-52798", "https://security.netapp.com/advisory/ntap-*************", "https://security.netapp.com/advisory/ntap-*************/", "https://www.cve.org/CVERecord?id=CVE-2024-52798"], "PublishedDate": "2024-12-05T23:15:06.31Z", "LastModifiedDate": "2025-01-24T20:15:33.107Z"}, {"VulnerabilityID": "CVE-2022-24999", "PkgID": "qs@6.5.2", "PkgName": "qs", "PkgIdentifier": {"PURL": "pkg:npm/qs@6.5.2", "UID": "9b900131954b384f"}, "InstalledVersion": "6.5.2", "FixedVersion": "6.10.3, 6.9.7, 6.8.3, 6.7.3, 6.6.1, 6.5.3, 6.4.1, 6.3.3, 6.2.4", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2022-24999", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "express: \"qs\" prototype poisoning causes the hang of the node process", "Description": "qs before 6.10.3, as used in Express before 4.17.3 and other products, allows attackers to cause a Node process hang for an Express application because an __ proto__ key can be used. In many typical Express use cases, an unauthenticated remote attacker can place the attack payload in the query string of the URL that is used to visit the application, such as a[__proto__]=b&a[__proto__]&a[length]=100000000. The fix was backported to qs 6.9.7, 6.8.3, 6.7.3, 6.6.1, 6.5.3, 6.4.1, 6.3.3, and 6.2.4 (and therefore Express 4.17.3, which has \"deps: qs@6.9.7\" in its release description, is not vulnerable).", "Severity": "HIGH", "CweIDs": ["CWE-1321"], "VendorSeverity": {"alma": 2, "ghsa": 3, "nvd": 3, "oracle-oval": 2, "redhat": 2, "ubuntu": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}, "nvd": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}}, "References": ["https://access.redhat.com/errata/RHSA-2023:0050", "https://access.redhat.com/security/cve/CVE-2022-24999", "https://bugzilla.redhat.com/2044591", "https://bugzilla.redhat.com/2066009", "https://bugzilla.redhat.com/2134609", "https://bugzilla.redhat.com/2140911", "https://bugzilla.redhat.com/2150323", "https://errata.almalinux.org/8/ALSA-2023-0050.html", "https://github.com/expressjs/express/releases/tag/4.17.3", "https://github.com/ljharb/qs", "https://github.com/ljharb/qs/commit/4310742efbd8c03f6495f07906b45213da0a32ec", "https://github.com/ljharb/qs/commit/727ef5d34605108acb3513f72d5435972ed15b68", "https://github.com/ljharb/qs/commit/73205259936317b40f447c5cdb71c5b341848e1b", "https://github.com/ljharb/qs/commit/8b4cc14cda94a5c89341b77e5fe435ec6c41be2d", "https://github.com/ljharb/qs/commit/ba24e74dd17931f825adb52f5633e48293b584e1", "https://github.com/ljharb/qs/commit/e799ba57e573a30c14b67c1889c7c04d508b9105", "https://github.com/ljharb/qs/commit/ed0f5dcbef4b168a8ae299d78b1e4a2e9b1baf1f", "https://github.com/ljharb/qs/commit/f945393cfe442fe8c6e62b4156fd35452c0686ee", "https://github.com/ljharb/qs/commit/fc3682776670524a42e19709ec4a8138d0d7afda", "https://github.com/ljharb/qs/pull/428", "https://github.com/n8tz/CVE-2022-24999", "https://linux.oracle.com/cve/CVE-2022-24999.html", "https://linux.oracle.com/errata/ELSA-2023-0050.html", "https://lists.debian.org/debian-lts-announce/2023/01/msg00039.html", "https://nvd.nist.gov/vuln/detail/CVE-2022-24999", "https://security.netapp.com/advisory/ntap-*************", "https://security.netapp.com/advisory/ntap-*************/", "https://ubuntu.com/security/notices/USN-7693-1", "https://www.cve.org/CVERecord?id=CVE-2022-24999"], "PublishedDate": "2022-11-26T22:15:10.153Z", "LastModifiedDate": "2025-04-29T14:15:20.41Z"}, {"VulnerabilityID": "CVE-2022-24999", "PkgID": "qs@6.7.0", "PkgName": "qs", "PkgIdentifier": {"PURL": "pkg:npm/qs@6.7.0", "UID": "742c4067aa624418"}, "InstalledVersion": "6.7.0", "FixedVersion": "6.10.3, 6.9.7, 6.8.3, 6.7.3, 6.6.1, 6.5.3, 6.4.1, 6.3.3, 6.2.4", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2022-24999", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "express: \"qs\" prototype poisoning causes the hang of the node process", "Description": "qs before 6.10.3, as used in Express before 4.17.3 and other products, allows attackers to cause a Node process hang for an Express application because an __ proto__ key can be used. In many typical Express use cases, an unauthenticated remote attacker can place the attack payload in the query string of the URL that is used to visit the application, such as a[__proto__]=b&a[__proto__]&a[length]=100000000. The fix was backported to qs 6.9.7, 6.8.3, 6.7.3, 6.6.1, 6.5.3, 6.4.1, 6.3.3, and 6.2.4 (and therefore Express 4.17.3, which has \"deps: qs@6.9.7\" in its release description, is not vulnerable).", "Severity": "HIGH", "CweIDs": ["CWE-1321"], "VendorSeverity": {"alma": 2, "ghsa": 3, "nvd": 3, "oracle-oval": 2, "redhat": 2, "ubuntu": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}, "nvd": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}}, "References": ["https://access.redhat.com/errata/RHSA-2023:0050", "https://access.redhat.com/security/cve/CVE-2022-24999", "https://bugzilla.redhat.com/2044591", "https://bugzilla.redhat.com/2066009", "https://bugzilla.redhat.com/2134609", "https://bugzilla.redhat.com/2140911", "https://bugzilla.redhat.com/2150323", "https://errata.almalinux.org/8/ALSA-2023-0050.html", "https://github.com/expressjs/express/releases/tag/4.17.3", "https://github.com/ljharb/qs", "https://github.com/ljharb/qs/commit/4310742efbd8c03f6495f07906b45213da0a32ec", "https://github.com/ljharb/qs/commit/727ef5d34605108acb3513f72d5435972ed15b68", "https://github.com/ljharb/qs/commit/73205259936317b40f447c5cdb71c5b341848e1b", "https://github.com/ljharb/qs/commit/8b4cc14cda94a5c89341b77e5fe435ec6c41be2d", "https://github.com/ljharb/qs/commit/ba24e74dd17931f825adb52f5633e48293b584e1", "https://github.com/ljharb/qs/commit/e799ba57e573a30c14b67c1889c7c04d508b9105", "https://github.com/ljharb/qs/commit/ed0f5dcbef4b168a8ae299d78b1e4a2e9b1baf1f", "https://github.com/ljharb/qs/commit/f945393cfe442fe8c6e62b4156fd35452c0686ee", "https://github.com/ljharb/qs/commit/fc3682776670524a42e19709ec4a8138d0d7afda", "https://github.com/ljharb/qs/pull/428", "https://github.com/n8tz/CVE-2022-24999", "https://linux.oracle.com/cve/CVE-2022-24999.html", "https://linux.oracle.com/errata/ELSA-2023-0050.html", "https://lists.debian.org/debian-lts-announce/2023/01/msg00039.html", "https://nvd.nist.gov/vuln/detail/CVE-2022-24999", "https://security.netapp.com/advisory/ntap-*************", "https://security.netapp.com/advisory/ntap-*************/", "https://ubuntu.com/security/notices/USN-7693-1", "https://www.cve.org/CVERecord?id=CVE-2022-24999"], "PublishedDate": "2022-11-26T22:15:10.153Z", "LastModifiedDate": "2025-04-29T14:15:20.41Z"}, {"VulnerabilityID": "CVE-2023-28155", "PkgID": "request@2.88.0", "PkgName": "request", "PkgIdentifier": {"PURL": "pkg:npm/request@2.88.0", "UID": "cfb3345376d4b994"}, "InstalledVersion": "2.88.0", "Status": "affected", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2023-28155", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "request: bypass of SSRF mitigations when following a cross-protocol redirect", "Description": "The Request package through 2.88.1 for Node.js allows a bypass of SSRF mitigations via an attacker-controller server that does a cross-protocol redirect (HTTP to HTTPS, or HTTPS to HTTP). NOTE: This vulnerability only affects products that are no longer supported by the maintainer.", "Severity": "MEDIUM", "CweIDs": ["CWE-918"], "VendorSeverity": {"cbl-mariner": 2, "ghsa": 2, "nvd": 2, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N", "V3Score": 6.1}, "nvd": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N", "V3Score": 6.1}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N", "V3Score": 6.1}}, "References": ["https://access.redhat.com/security/cve/CVE-2023-28155", "https://doyensec.com/resources/Doyensec_Advisory_RequestSSRF_Q12023.pdf", "https://github.com/cypress-io/request/blob/master/lib/redirect.js#L116", "https://github.com/cypress-io/request/commit/c5bcf21d40fb61feaff21a0e5a2b3934a440024f", "https://github.com/cypress-io/request/pull/28", "https://github.com/cypress-io/request/releases/tag/v3.0.0", "https://github.com/github/advisory-database/pull/2500", "https://github.com/request/request", "https://github.com/request/request/blob/master/lib/redirect.js#L111", "https://github.com/request/request/issues/3442", "https://github.com/request/request/pull/3444", "https://nvd.nist.gov/vuln/detail/CVE-2023-28155", "https://security.netapp.com/advisory/ntap-*************", "https://security.netapp.com/advisory/ntap-*************/", "https://www.cve.org/CVERecord?id=CVE-2023-28155"], "PublishedDate": "2023-03-16T15:15:11.107Z", "LastModifiedDate": "2024-11-21T07:54:30.183Z"}, {"VulnerabilityID": "CVE-2024-43799", "PkgID": "send@0.16.2", "PkgName": "send", "PkgIdentifier": {"PURL": "pkg:npm/send@0.16.2", "UID": "3059ca018d5a5669"}, "InstalledVersion": "0.16.2", "FixedVersion": "0.19.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2024-43799", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "send: Code Execution Vulnerability in Send Library", "Description": "Send is a library for streaming files from the file system as a http response. Send passes untrusted user input to SendStream.redirect() which executes untrusted code. This issue is patched in send 0.19.0.", "Severity": "LOW", "CweIDs": ["CWE-79"], "VendorSeverity": {"cbl-mariner": 2, "ghsa": 1, "nvd": 2, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:L/I:L/A:L", "V3Score": 5}, "nvd": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:C/C:L/I:L/A:N", "V3Score": 4.7}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:L/I:L/A:L", "V3Score": 5}}, "References": ["https://access.redhat.com/security/cve/CVE-2024-43799", "https://github.com/pillarjs/send", "https://github.com/pillarjs/send/commit/ae4f2989491b392ae2ef3b0015a019770ae65d35", "https://github.com/pillarjs/send/security/advisories/GHSA-m6fv-jmcg-4jfg", "https://nvd.nist.gov/vuln/detail/CVE-2024-43799", "https://www.cve.org/CVERecord?id=CVE-2024-43799"], "PublishedDate": "2024-09-10T15:15:17.727Z", "LastModifiedDate": "2024-09-20T16:57:14.687Z"}, {"VulnerabilityID": "CVE-2020-7660", "PkgID": "serialize-javascript@1.7.0", "PkgName": "serialize-javascript", "PkgIdentifier": {"PURL": "pkg:npm/serialize-javascript@1.7.0", "UID": "cd0adc7e08ea167e"}, "InstalledVersion": "1.7.0", "FixedVersion": "3.1.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2020-7660", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "npm-serialize-javascript: allows remote attackers to inject arbitrary code via the function deleteFunctions within index.js", "Description": "serialize-javascript prior to 3.1.0 allows remote attackers to inject arbitrary code via the function \"deleteFunctions\" within \"index.js\".", "Severity": "HIGH", "CweIDs": ["CWE-502"], "VendorSeverity": {"ghsa": 3, "nvd": 3, "redhat": 3}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:H/A:H", "V3Score": 8.1}, "nvd": {"V2Vector": "AV:N/AC:M/Au:N/C:P/I:P/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:H/A:H", "V2Score": 6.8, "V3Score": 8.1}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:H/A:H", "V3Score": 8.1}}, "References": ["https://access.redhat.com/security/cve/CVE-2020-7660", "https://github.com/yahoo/serialize-javascript", "https://github.com/yahoo/serialize-javascript/commit/f21a6fb3ace2353413761e79717b2d210ba6ccbd", "https://nvd.nist.gov/vuln/detail/CVE-2020-7660", "https://www.cve.org/CVERecord?id=CVE-2020-7660"], "PublishedDate": "2020-06-01T15:15:14.76Z", "LastModifiedDate": "2024-11-21T05:37:33.577Z"}, {"VulnerabilityID": "CVE-2019-16769", "PkgID": "serialize-javascript@1.7.0", "PkgName": "serialize-javascript", "PkgIdentifier": {"PURL": "pkg:npm/serialize-javascript@1.7.0", "UID": "cd0adc7e08ea167e"}, "InstalledVersion": "1.7.0", "FixedVersion": "2.1.1", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2019-16769", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "npm-serialize-javascript: XSS via unsafe characters in serialized regular expressions", "Description": "The serialize-javascript npm package before version 2.1.1 is vulnerable to Cross-site Scripting (XSS). It does not properly mitigate against unsafe characters in serialized regular expressions. This vulnerability is not affected on Node.js environment since Node.js's implementation of RegExp.prototype.toString() backslash-escapes all forward slashes in regular expressions. If serialized data of regular expression objects are used in an environment other than Node.js, it is affected by this vulnerability.", "Severity": "MEDIUM", "CweIDs": ["CWE-79"], "VendorSeverity": {"ghsa": 2, "nvd": 2, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:N/I:L/A:L", "V3Score": 4.2}, "nvd": {"V2Vector": "AV:N/AC:M/Au:S/C:N/I:P/A:N", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:R/S:C/C:L/I:L/A:N", "V2Score": 3.5, "V3Score": 5.4}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:R/S:C/C:L/I:L/A:N", "V3Score": 5.4}}, "References": ["https://access.redhat.com/security/cve/CVE-2019-16769", "https://github.com/advisories/GHSA-h9rv-jmmf-4pgx", "https://github.com/yahoo/serialize-javascript/security/advisories/GHSA-h9rv-jmmf-4pgx", "https://nvd.nist.gov/vuln/detail/CVE-2019-16769", "https://www.cve.org/CVERecord?id=CVE-2019-16769", "https://www.npmjs.com/advisories/1426"], "PublishedDate": "2019-12-05T19:15:15.18Z", "LastModifiedDate": "2024-11-21T04:31:09.207Z"}, {"VulnerabilityID": "CVE-2024-43800", "PkgID": "serve-static@1.13.2", "PkgName": "serve-static", "PkgIdentifier": {"PURL": "pkg:npm/serve-static@1.13.2", "UID": "cc76c261c26c3b51"}, "InstalledVersion": "1.13.2", "FixedVersion": "1.16.0, 2.1.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2024-43800", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "serve-static: Improper Sanitization in serve-static", "Description": "serve-static serves static files. serve-static passes untrusted user input - even after sanitizing it - to redirect() may execute untrusted code. This issue is patched in serve-static 1.16.0.", "Severity": "LOW", "CweIDs": ["CWE-79"], "VendorSeverity": {"cbl-mariner": 2, "ghsa": 1, "nvd": 2, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:L/I:L/A:L", "V3Score": 5}, "nvd": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:C/C:L/I:L/A:N", "V3Score": 4.7}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:L/I:L/A:L", "V3Score": 5}}, "References": ["https://access.redhat.com/security/cve/CVE-2024-43800", "https://github.com/expressjs/serve-static", "https://github.com/expressjs/serve-static/commit/0c11fad159898cdc69fd9ab63269b72468ecaf6b", "https://github.com/expressjs/serve-static/commit/ce730896fddce1588111d9ef6fdf20896de5c6fa", "https://github.com/expressjs/serve-static/security/advisories/GHSA-cm22-4g7w-348p", "https://nvd.nist.gov/vuln/detail/CVE-2024-43800", "https://www.cve.org/CVERecord?id=CVE-2024-43800"], "PublishedDate": "2024-09-10T15:15:17.937Z", "LastModifiedDate": "2024-09-20T17:36:30.313Z"}, {"VulnerabilityID": "CVE-2022-0144", "PkgID": "shelljs@0.8.3", "PkgName": "<PERSON><PERSON>s", "PkgIdentifier": {"PURL": "pkg:npm/shelljs@0.8.3", "UID": "ddeaa60ddca20b63"}, "InstalledVersion": "0.8.3", "FixedVersion": "0.8.5", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2022-0144", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-shelljs: improper privilege management", "Description": "shelljs is vulnerable to Improper Privilege Management", "Severity": "HIGH", "CweIDs": ["CWE-269"], "VendorSeverity": {"ghsa": 3, "nvd": 3, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.0/AV:L/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:H", "V3Score": 7.1}, "nvd": {"V2Vector": "AV:L/AC:L/Au:N/C:P/I:N/A:P", "V3Vector": "CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:H", "V2Score": 3.6, "V3Score": 7.1}, "redhat": {"V3Vector": "CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:H", "V3Score": 7.1}}, "References": ["https://access.redhat.com/security/cve/CVE-2022-0144", "https://github.com/shelljs/shelljs", "https://github.com/shelljs/shelljs/commit/d919d22dd6de385edaa9d90313075a77f74b338c", "https://huntr.dev/bounties/50996581-c08e-4eed-a90e-c0bac082679c", "https://huntr.dev/bounties/50996581-c08e-4eed-a90e-c0bac082679c/", "https://nvd.nist.gov/vuln/detail/CVE-2022-0144", "https://www.cve.org/CVERecord?id=CVE-2022-0144"], "PublishedDate": "2022-01-11T07:15:07.26Z", "LastModifiedDate": "2024-11-21T06:37:59.84Z"}, {"VulnerabilityID": "GHSA-64g7-mvw6-v9qj", "PkgID": "shelljs@0.8.3", "PkgName": "<PERSON><PERSON>s", "PkgIdentifier": {"PURL": "pkg:npm/shelljs@0.8.3", "UID": "ddeaa60ddca20b63"}, "InstalledVersion": "0.8.3", "FixedVersion": "0.8.5", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://github.com/advisories/GHSA-64g7-mvw6-v9qj", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "Improper Privilege Management in shelljs", "Description": "### Impact\nOutput from the synchronous version of `shell.exec()` may be visible to other users on the same system. You may be affected if you execute `shell.exec()` in multi-user Mac, Linux, or WSL environments, or if you execute `shell.exec()` as the root user.\n\nOther shelljs functions (including the asynchronous version of `shell.exec()`) are not impacted.\n\n### Patches\nPatched in shelljs 0.8.5\n\n### Workarounds\nRecommended action is to upgrade to 0.8.5.\n\n### References\nhttps://huntr.dev/bounties/50996581-c08e-4eed-a90e-c0bac082679c/\n\n### For more information\nIf you have any questions or comments about this advisory:\n* Ask at https://github.com/shelljs/shelljs/issues/1058\n* Open an issue at https://github.com/shelljs/shelljs/issues/new\n", "Severity": "MEDIUM", "VendorSeverity": {"ghsa": 2}, "References": ["https://github.com/shelljs/shelljs", "https://github.com/shelljs/shelljs/security/advisories/GHSA-64g7-mvw6-v9qj", "https://huntr.dev/bounties/50996581-c08e-4eed-a90e-c0bac082679c"], "PublishedDate": "2022-01-14T21:09:50Z", "LastModifiedDate": "2022-01-14T20:50:57Z"}, {"VulnerabilityID": "CVE-2020-28481", "PkgID": "socket.io@2.2.0", "PkgName": "socket.io", "PkgIdentifier": {"PURL": "pkg:npm/socket.io@2.2.0", "UID": "d5408972919777b1"}, "InstalledVersion": "2.2.0", "FixedVersion": "2.4.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2020-28481", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "CORS misconfiguration in socket.io", "Description": "The package socket.io before 2.4.0 are vulnerable to Insecure Defaults due to CORS Misconfiguration. All domains are whitelisted by default.", "Severity": "MEDIUM", "CweIDs": ["CWE-346"], "VendorSeverity": {"ghsa": 2, "nvd": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:N", "V3Score": 4.3}, "nvd": {"V2Vector": "AV:N/AC:L/Au:S/C:P/I:N/A:N", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:N", "V2Score": 4, "V3Score": 4.3}}, "References": ["https://github.com/socketio/socket.io/commit/f78a575f66ab693c3ea96ea88429ddb1a44c86c7", "https://github.com/socketio/socket.io/issues/3671", "https://nvd.nist.gov/vuln/detail/CVE-2020-28481", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWER-1056358", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-1056357", "https://snyk.io/vuln/SNYK-JS-SOCKETIO-1024859"], "PublishedDate": "2021-01-19T15:15:12.06Z", "LastModifiedDate": "2024-11-21T05:22:52.963Z"}, {"VulnerabilityID": "CVE-2024-38355", "PkgID": "socket.io@2.2.0", "PkgName": "socket.io", "PkgIdentifier": {"PURL": "pkg:npm/socket.io@2.2.0", "UID": "d5408972919777b1"}, "InstalledVersion": "2.2.0", "FixedVersion": "2.5.1, 4.6.2", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2024-38355", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "socket.io: Unhandled 'error' event", "Description": "Socket.IO is an open source, real-time, bidirectional, event-based, communication framework. A specially crafted Socket.IO packet can trigger an uncaught exception on the Socket.IO server, thus killing the Node.js process. This issue is fixed by commit `15af22fc22` which has been included in `socket.io@4.6.2` (released in May 2023). The fix was backported in the 2.x branch as well with commit `d30630ba10`. Users are advised to upgrade. Users unable to upgrade may attach a listener for the \"error\" event to catch these errors.\n", "Severity": "MEDIUM", "CweIDs": ["CWE-20", "CWE-754"], "VendorSeverity": {"ghsa": 2, "redhat": 3}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:L", "V3Score": 7.3}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:L", "V3Score": 7.3}}, "References": ["https://access.redhat.com/security/cve/CVE-2024-38355", "https://github.com/socketio/socket.io", "https://github.com/socketio/socket.io/commit/15af22fc22bc6030fcead322c106f07640336115", "https://github.com/socketio/socket.io/commit/d30630ba10562bf987f4d2b42440fc41a828119c", "https://github.com/socketio/socket.io/security/advisories/GHSA-25hc-qcg6-38wj", "https://nvd.nist.gov/vuln/detail/CVE-2024-38355", "https://www.cve.org/CVERecord?id=CVE-2024-38355", "https://www.vicarius.io/vsociety/posts/unhandled-exception-in-socketio-cve-2024-38355"], "PublishedDate": "2024-06-19T20:15:11.18Z", "LastModifiedDate": "2024-11-21T09:25:25.967Z"}, {"VulnerabilityID": "CVE-2021-32803", "PkgID": "tar@4.4.8", "PkgName": "tar", "PkgIdentifier": {"PURL": "pkg:npm/tar@4.4.8", "UID": "f0a320f7df3111cc"}, "InstalledVersion": "4.4.8", "FixedVersion": "3.2.3, 4.4.15, 5.0.7, 6.1.2", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2021-32803", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-tar: Insufficient symlink protection allowing arbitrary file creation and overwrite", "Description": "The npm package \"tar\" (aka node-tar) before versions 6.1.2, 5.0.7, 4.4.15, and 3.2.3 has an arbitrary File Creation/Overwrite vulnerability via insufficient symlink protection. `node-tar` aims to guarantee that any file whose location would be modified by a symbolic link is not extracted. This is, in part, achieved by ensuring that extracted directories are not symlinks. Additionally, in order to prevent unnecessary `stat` calls to determine whether a given path is a directory, paths are cached when directories are created. This logic was insufficient when extracting tar files that contained both a directory and a symlink with the same name as the directory. This order of operations resulted in the directory being created and added to the `node-tar` directory cache. When a directory is present in the directory cache, subsequent calls to mkdir for that directory are skipped. However, this is also where `node-tar` checks for symlinks occur. By first creating a directory, and then replacing that directory with a symlink, it was thus possible to bypass `node-tar` symlink checks on directories, essentially allowing an untrusted tar file to symlink into an arbitrary location and subsequently extracting arbitrary files into that location, thus allowing arbitrary file creation and overwrite. This issue was addressed in releases 3.2.3, 4.4.15, 5.0.7 and 6.1.2.", "Severity": "HIGH", "CweIDs": ["CWE-22", "CWE-59"], "VendorSeverity": {"alma": 3, "ghsa": 3, "nvd": 3, "oracle-oval": 3, "redhat": 2, "ubuntu": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:N", "V3Score": 8.2}, "nvd": {"V2Vector": "AV:N/AC:M/Au:N/C:N/I:P/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:H/A:H", "V2Score": 5.8, "V3Score": 8.1}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:H/A:H", "V3Score": 8.1}}, "References": ["https://access.redhat.com/security/cve/CVE-2021-32803", "https://cert-portal.siemens.com/productcert/pdf/ssa-389290.pdf", "https://errata.almalinux.org/8/ALSA-2021-3666.html", "https://github.com/isaacs/node-tar", "https://github.com/isaacs/node-tar/commit/46fe35083e2676e31c4e0a81639dce6da7aaa356", "https://github.com/isaacs/node-tar/commit/5987d9a41f6bfbf1ddab1098e1fdcf1a5618f571", "https://github.com/isaacs/node-tar/commit/85d3a942b4064e4ff171f91696fced7975167349", "https://github.com/isaacs/node-tar/commit/9dbdeb6df8e9dbd96fa9e84341b9d74734be6c20", "https://github.com/npm/node-tar/commit/9dbdeb6df8e9dbd96fa9e84341b9d74734be6c20", "https://github.com/npm/node-tar/security/advisories/GHSA-r628-mhmh-qjhw", "https://linux.oracle.com/cve/CVE-2021-32803.html", "https://linux.oracle.com/errata/ELSA-2021-3666.html", "https://nvd.nist.gov/vuln/detail/CVE-2021-32803", "https://ubuntu.com/security/notices/USN-5283-1", "https://www.cve.org/CVERecord?id=CVE-2021-32803", "https://www.npmjs.com/advisories/1771", "https://www.npmjs.com/package/tar", "https://www.oracle.com/security-alerts/cpuoct2021.html"], "PublishedDate": "2021-08-03T19:15:08.297Z", "LastModifiedDate": "2024-11-21T06:07:46.637Z"}, {"VulnerabilityID": "CVE-2021-32804", "PkgID": "tar@4.4.8", "PkgName": "tar", "PkgIdentifier": {"PURL": "pkg:npm/tar@4.4.8", "UID": "f0a320f7df3111cc"}, "InstalledVersion": "4.4.8", "FixedVersion": "3.2.2, 4.4.14, 5.0.6, 6.1.1", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2021-32804", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-tar: Insufficient absolute path sanitization allowing arbitrary file creation and overwrite", "Description": "The npm package \"tar\" (aka node-tar) before versions 6.1.1, 5.0.6, 4.4.14, and 3.3.2 has a arbitrary File Creation/Overwrite vulnerability due to insufficient absolute path sanitization. node-tar aims to prevent extraction of absolute file paths by turning absolute paths into relative paths when the `preservePaths` flag is not set to `true`. This is achieved by stripping the absolute path root from any absolute file paths contained in a tar file. For example `/home/<USER>/.bashrc` would turn into `home/user/.bashrc`. This logic was insufficient when file paths contained repeated path roots such as `////home/<USER>/.bashrc`. `node-tar` would only strip a single path root from such paths. When given an absolute file path with repeating path roots, the resulting path (e.g. `///home/<USER>/.bashrc`) would still resolve to an absolute path, thus allowing arbitrary file creation and overwrite. This issue was addressed in releases 3.2.2, 4.4.14, 5.0.6 and 6.1.1. Users may work around this vulnerability without upgrading by creating a custom `onentry` method which sanitizes the `entry.path` or a `filter` method which removes entries with absolute paths. See referenced GitHub Advisory for details. Be aware of CVE-2021-32803 which fixes a similar bug in later versions of tar.", "Severity": "HIGH", "CweIDs": ["CWE-22"], "VendorSeverity": {"alma": 3, "ghsa": 3, "nvd": 3, "oracle-oval": 3, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:N", "V3Score": 8.2}, "nvd": {"V2Vector": "AV:N/AC:M/Au:N/C:N/I:P/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:H/A:H", "V2Score": 5.8, "V3Score": 8.1}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:H/A:H", "V3Score": 8.1}}, "References": ["https://access.redhat.com/security/cve/CVE-2021-32804", "https://cert-portal.siemens.com/productcert/pdf/ssa-389290.pdf", "https://errata.almalinux.org/8/ALSA-2021-3666.html", "https://github.com/npm/node-tar", "https://github.com/npm/node-tar/commit/1f036ca23f64a547bdd6c79c1a44bc62e8115da4", "https://github.com/npm/node-tar/security/advisories/GHSA-3jfq-g458-7qm9", "https://linux.oracle.com/cve/CVE-2021-32804.html", "https://linux.oracle.com/errata/ELSA-2021-3666.html", "https://nvd.nist.gov/vuln/detail/CVE-2021-32804", "https://www.cve.org/CVERecord?id=CVE-2021-32804", "https://www.npmjs.com/advisories/1770", "https://www.npmjs.com/package/tar", "https://www.oracle.com/security-alerts/cpuoct2021.html"], "PublishedDate": "2021-08-03T19:15:08.41Z", "LastModifiedDate": "2024-11-21T06:07:46.8Z"}, {"VulnerabilityID": "CVE-2021-37701", "PkgID": "tar@4.4.8", "PkgName": "tar", "PkgIdentifier": {"PURL": "pkg:npm/tar@4.4.8", "UID": "f0a320f7df3111cc"}, "InstalledVersion": "4.4.8", "FixedVersion": "4.4.16, 5.0.8, 6.1.7", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2021-37701", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-tar: Insufficient symlink protection due to directory cache poisoning using symbolic links allowing arbitrary file creation and overwrite", "Description": "The npm package \"tar\" (aka node-tar) before versions 4.4.16, 5.0.8, and 6.1.7 has an arbitrary file creation/overwrite and arbitrary code execution vulnerability. node-tar aims to guarantee that any file whose location would be modified by a symbolic link is not extracted. This is, in part, achieved by ensuring that extracted directories are not symlinks. Additionally, in order to prevent unnecessary stat calls to determine whether a given path is a directory, paths are cached when directories are created. This logic was insufficient when extracting tar files that contained both a directory and a symlink with the same name as the directory, where the symlink and directory names in the archive entry used backslashes as a path separator on posix systems. The cache checking logic used both `\\` and `/` characters as path separators, however `\\` is a valid filename character on posix systems. By first creating a directory, and then replacing that directory with a symlink, it was thus possible to bypass node-tar symlink checks on directories, essentially allowing an untrusted tar file to symlink into an arbitrary location and subsequently extracting arbitrary files into that location, thus allowing arbitrary file creation and overwrite. Additionally, a similar confusion could arise on case-insensitive filesystems. If a tar archive contained a directory at `FOO`, followed by a symbolic link named `foo`, then on case-insensitive file systems, the creation of the symbolic link would remove the directory from the filesystem, but _not_ from the internal directory cache, as it would not be treated as a cache hit. A subsequent file entry within the `FOO` directory would then be placed in the target of the symbolic link, thinking that the directory had already been created. These issues were addressed in releases 4.4.16, 5.0.8 and 6.1.7. The v3 branch of node-tar has been deprecated and did not receive patches for these issues. If you are still using a v3 release we recommend you update to a more recent version of node-tar. If this is not possible, a workaround is available in the referenced GHSA-9r2w-394v-53qc.", "Severity": "HIGH", "CweIDs": ["CWE-22", "CWE-59"], "VendorSeverity": {"alma": 2, "ghsa": 3, "nvd": 3, "oracle-oval": 2, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:N", "V3Score": 8.2}, "nvd": {"V2Vector": "AV:L/AC:M/Au:N/C:P/I:P/A:P", "V3Vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:H", "V2Score": 4.4, "V3Score": 8.6}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:H/A:H", "V3Score": 8.1}}, "References": ["https://access.redhat.com/security/cve/CVE-2021-37701", "https://cert-portal.siemens.com/productcert/pdf/ssa-389290.pdf", "https://errata.almalinux.org/8/ALSA-2022-0350.html", "https://github.com/npm/node-tar", "https://github.com/npm/node-tar/security/advisories/GHSA-9r2w-394v-53qc", "https://linux.oracle.com/cve/CVE-2021-37701.html", "https://linux.oracle.com/errata/ELSA-2022-0350.html", "https://lists.debian.org/debian-lts-announce/2022/12/msg00023.html", "https://nvd.nist.gov/vuln/detail/CVE-2021-37701", "https://www.cve.org/CVERecord?id=CVE-2021-37701", "https://www.debian.org/security/2021/dsa-5008", "https://www.npmjs.com/advisories/1779", "https://www.npmjs.com/package/tar", "https://www.oracle.com/security-alerts/cpuoct2021.html"], "PublishedDate": "2021-08-31T17:15:07.963Z", "LastModifiedDate": "2024-11-21T06:15:44.403Z"}, {"VulnerabilityID": "CVE-2021-37712", "PkgID": "tar@4.4.8", "PkgName": "tar", "PkgIdentifier": {"PURL": "pkg:npm/tar@4.4.8", "UID": "f0a320f7df3111cc"}, "InstalledVersion": "4.4.8", "FixedVersion": "4.4.18, 5.0.10, 6.1.9", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2021-37712", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-tar: Insufficient symlink protection due to directory cache poisoning using symbolic links allowing arbitrary file creation and overwrite", "Description": "The npm package \"tar\" (aka node-tar) before versions 4.4.18, 5.0.10, and 6.1.9 has an arbitrary file creation/overwrite and arbitrary code execution vulnerability. node-tar aims to guarantee that any file whose location would be modified by a symbolic link is not extracted. This is, in part, achieved by ensuring that extracted directories are not symlinks. Additionally, in order to prevent unnecessary stat calls to determine whether a given path is a directory, paths are cached when directories are created. This logic was insufficient when extracting tar files that contained both a directory and a symlink with names containing unicode values that normalized to the same value. Additionally, on Windows systems, long path portions would resolve to the same file system entities as their 8.3 \"short path\" counterparts. A specially crafted tar archive could thus include a directory with one form of the path, followed by a symbolic link with a different string that resolves to the same file system entity, followed by a file using the first form. By first creating a directory, and then replacing that directory with a symlink that had a different apparent name that resolved to the same entry in the filesystem, it was thus possible to bypass node-tar symlink checks on directories, essentially allowing an untrusted tar file to symlink into an arbitrary location and subsequently extracting arbitrary files into that location, thus allowing arbitrary file creation and overwrite. These issues were addressed in releases 4.4.18, 5.0.10 and 6.1.9. The v3 branch of node-tar has been deprecated and did not receive patches for these issues. If you are still using a v3 release we recommend you update to a more recent version of node-tar. If this is not possible, a workaround is available in the referenced GHSA-qq89-hq3f-393p.", "Severity": "HIGH", "CweIDs": ["CWE-22", "CWE-59"], "VendorSeverity": {"alma": 2, "ghsa": 3, "nvd": 3, "oracle-oval": 2, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:N", "V3Score": 8.2}, "nvd": {"V2Vector": "AV:L/AC:M/Au:N/C:P/I:P/A:P", "V3Vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:H", "V2Score": 4.4, "V3Score": 8.6}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:H/A:H", "V3Score": 8.1}}, "References": ["https://access.redhat.com/security/cve/CVE-2021-37712", "https://cert-portal.siemens.com/productcert/pdf/ssa-389290.pdf", "https://errata.almalinux.org/8/ALSA-2022-0350.html", "https://github.com/isaacs/node-tar/commit/1739408d3122af897caefd09662bce2ea477533b", "https://github.com/isaacs/node-tar/commit/2f1bca027286c23e110b8dfc7efc10756fa3db5a", "https://github.com/isaacs/node-tar/commit/3aaf19b2501bbddb145d92b3322c80dcaed3c35f", "https://github.com/isaacs/node-tar/commit/b6162c7fafe797f856564ef37f4b82747f051455", "https://github.com/isaacs/node-tar/commit/bb93ba243746f705092905da1955ac3b0509ba1e", "https://github.com/isaacs/node-tar/commit/d56f790bda9fea807dd80c5083f24771dbdd6eb1", "https://github.com/npm/node-tar", "https://github.com/npm/node-tar/security/advisories/GHSA-qq89-hq3f-393p", "https://linux.oracle.com/cve/CVE-2021-37712.html", "https://linux.oracle.com/errata/ELSA-2022-0350.html", "https://lists.debian.org/debian-lts-announce/2022/12/msg00023.html", "https://nvd.nist.gov/vuln/detail/CVE-2021-37712", "https://www.cve.org/CVERecord?id=CVE-2021-37712", "https://www.debian.org/security/2021/dsa-5008", "https://www.npmjs.com/advisories/1780", "https://www.npmjs.com/package/tar", "https://www.oracle.com/security-alerts/cpuoct2021.html"], "PublishedDate": "2021-08-31T17:15:08.023Z", "LastModifiedDate": "2024-11-21T06:15:46.193Z"}, {"VulnerabilityID": "CVE-2021-37713", "PkgID": "tar@4.4.8", "PkgName": "tar", "PkgIdentifier": {"PURL": "pkg:npm/tar@4.4.8", "UID": "f0a320f7df3111cc"}, "InstalledVersion": "4.4.8", "FixedVersion": "4.4.18, 5.0.10, 6.1.9", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2021-37713", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-tar: Arbitrary File Creation/Overwrite on Windows via insufficient relative path sanitization", "Description": "The npm package \"tar\" (aka node-tar) before versions 4.4.18, 5.0.10, and 6.1.9 has an arbitrary file creation/overwrite and arbitrary code execution vulnerability. node-tar aims to guarantee that any file whose location would be outside of the extraction target directory is not extracted. This is, in part, accomplished by sanitizing absolute paths of entries within the archive, skipping archive entries that contain `..` path portions, and resolving the sanitized paths against the extraction target directory. This logic was insufficient on Windows systems when extracting tar files that contained a path that was not an absolute path, but specified a drive letter different from the extraction target, such as `C:some\\path`. If the drive letter does not match the extraction target, for example `D:\\extraction\\dir`, then the result of `path.resolve(extractionDirectory, entryPath)` would resolve against the current working directory on the `C:` drive, rather than the extraction target directory. Additionally, a `..` portion of the path could occur immediately after the drive letter, such as `C:../foo`, and was not properly sanitized by the logic that checked for `..` within the normalized and split portions of the path. This only affects users of `node-tar` on Windows systems. These issues were addressed in releases 4.4.18, 5.0.10 and 6.1.9. The v3 branch of node-tar has been deprecated and did not receive patches for these issues. If you are still using a v3 release we recommend you update to a more recent version of node-tar. There is no reasonable way to work around this issue without performing the same path normalization procedures that node-tar now does. Users are encouraged to upgrade to the latest patched versions of node-tar, rather than attempt to sanitize paths themselves.", "Severity": "HIGH", "CweIDs": ["CWE-22"], "VendorSeverity": {"ghsa": 3, "nvd": 3, "redhat": 3}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:N", "V3Score": 8.2}, "nvd": {"V2Vector": "AV:L/AC:M/Au:N/C:P/I:P/A:P", "V3Vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:H", "V2Score": 4.4, "V3Score": 8.6}, "redhat": {"V3Vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:N", "V3Score": 8.2}}, "References": ["https://access.redhat.com/security/cve/CVE-2021-37713", "https://cert-portal.siemens.com/productcert/pdf/ssa-389290.pdf", "https://github.com/isaacs/node-tar/commit/52b09e309bcae0c741a7eb79a17ef36e7828b946", "https://github.com/isaacs/node-tar/commit/82eac952f7c10765969ed464e549375854b26edc", "https://github.com/isaacs/node-tar/commit/875a37e3ec031186fc6599f6807341f56c584598", "https://github.com/npm/node-tar", "https://github.com/npm/node-tar/security/advisories/GHSA-5955-9wpr-37jh", "https://nvd.nist.gov/vuln/detail/CVE-2021-37713", "https://www.cve.org/CVERecord?id=CVE-2021-37713", "https://www.npmjs.com/package/tar", "https://www.oracle.com/security-alerts/cpuoct2021.html"], "PublishedDate": "2021-08-31T17:15:08.087Z", "LastModifiedDate": "2024-11-21T06:15:46.377Z"}, {"VulnerabilityID": "CVE-2024-28863", "PkgID": "tar@4.4.8", "PkgName": "tar", "PkgIdentifier": {"PURL": "pkg:npm/tar@4.4.8", "UID": "f0a320f7df3111cc"}, "InstalledVersion": "4.4.8", "FixedVersion": "6.2.1", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2024-28863", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "node-tar: denial of service while parsing a tar file due to lack of folders depth validation", "Description": "node-tar is a Tar for Node.js. node-tar prior to version 6.2.1 has no limit on the number of sub-folders created in the folder creation process. An attacker who generates a large number of sub-folders can consume memory on the system running node-tar and even crash the Node.js client within few seconds of running it using a path with too many sub-folders inside. Version 6.2.1 fixes this issue by preventing extraction in excessively deep sub-folders.", "Severity": "MEDIUM", "CweIDs": ["CWE-400", "CWE-770"], "VendorSeverity": {"alma": 2, "amazon": 2, "azure": 2, "cbl-mariner": 2, "ghsa": 2, "oracle-oval": 2, "redhat": 2, "ubuntu": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:N/A:H", "V3Score": 6.5}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:N/A:H", "V3Score": 6.5}}, "References": ["https://access.redhat.com/errata/RHSA-2024:6147", "https://access.redhat.com/security/cve/CVE-2024-28863", "https://bugzilla.redhat.com/2293200", "https://bugzilla.redhat.com/2296417", "https://errata.almalinux.org/9/ALSA-2024-6147.html", "https://github.com/isaacs/node-tar", "https://github.com/isaacs/node-tar/commit/fe8cd57da5686f8695415414bda49206a545f7f7", "https://github.com/isaacs/node-tar/commit/fe8cd57da5686f8695415414bda49206a545f7f7 (v6.2.1)", "https://github.com/isaacs/node-tar/security/advisories/GHSA-f5x3-32g6-xq36", "https://linux.oracle.com/cve/CVE-2024-28863.html", "https://linux.oracle.com/errata/ELSA-2024-6148.html", "https://nvd.nist.gov/vuln/detail/CVE-2024-28863", "https://security.netapp.com/advisory/ntap-*************", "https://security.netapp.com/advisory/ntap-*************/", "https://www.cve.org/CVERecord?id=CVE-2024-28863"], "PublishedDate": "2024-03-21T23:15:10.91Z", "LastModifiedDate": "2024-11-21T09:07:04.023Z"}, {"VulnerabilityID": "CVE-2023-26136", "PkgID": "tough-cookie@2.4.3", "PkgName": "tough-cookie", "PkgIdentifier": {"PURL": "pkg:npm/tough-cookie@2.4.3", "UID": "e11a53f98578fc17"}, "InstalledVersion": "2.4.3", "FixedVersion": "4.1.3", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2023-26136", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "tough-cookie: prototype pollution in cookie memstore", "Description": "Versions of the package tough-cookie before 4.1.3 are vulnerable to Prototype Pollution due to improper handling of Cookies when using CookieJar in rejectPublicSuffixes=false mode. This issue arises from the manner in which the objects are initialized.", "Severity": "MEDIUM", "CweIDs": ["CWE-1321"], "VendorSeverity": {"ghsa": 2, "nvd": 4, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:N", "V3Score": 6.5}, "nvd": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "V3Score": 9.8}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:N", "V3Score": 6.5}}, "References": ["https://access.redhat.com/security/cve/CVE-2023-26136", "https://github.com/salesforce/tough-cookie", "https://github.com/salesforce/tough-cookie/commit/12d474791bb856004e858fdb1c47b7608d09cf6e", "https://github.com/salesforce/tough-cookie/issues/282", "https://github.com/salesforce/tough-cookie/releases/tag/v4.1.3", "https://lists.debian.org/debian-lts-announce/2023/07/msg00010.html", "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/3HUE6ZR5SL73KHL7XUPAOEL6SB7HUDT2", "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/3HUE6ZR5SL73KHL7XUPAOEL6SB7HUDT2/", "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/6PVVPNSAGSDS63HQ74PJ7MZ3MU5IYNVZ", "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/6PVVPNSAGSDS63HQ74PJ7MZ3MU5IYNVZ/", "https://nvd.nist.gov/vuln/detail/CVE-2023-26136", "https://security.netapp.com/advisory/ntap-*************", "https://security.netapp.com/advisory/ntap-*************/", "https://security.snyk.io/vuln/SNYK-JS-TOUGHCOOKIE-5672873", "https://www.cve.org/CVERecord?id=CVE-2023-26136"], "PublishedDate": "2023-07-01T05:15:16.103Z", "LastModifiedDate": "2024-11-21T07:50:51.107Z"}, {"VulnerabilityID": "CVE-2021-3765", "PkgID": "validator@10.11.0", "PkgName": "validator", "PkgIdentifier": {"PURL": "pkg:npm/validator@10.11.0", "UID": "efa0903e4a70be83"}, "InstalledVersion": "10.11.0", "FixedVersion": "13.7.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2021-3765", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "validator: Inefficient Regular Expression Complexity in Validator.js", "Description": "validator.js is vulnerable to Inefficient Regular Expression Complexity", "Severity": "MEDIUM", "CweIDs": ["CWE-1333"], "VendorSeverity": {"ghsa": 2, "nvd": 3, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "V3Score": 5.3}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:N/I:N/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V2Score": 5, "V3Score": 7.5}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}}, "References": ["https://access.redhat.com/security/cve/CVE-2021-3765", "https://github.com/validatorjs/validator.js", "https://github.com/validatorjs/validator.js/commit/496fc8b2a7f5997acaaec33cc44d0b8dba5fb5e1", "https://huntr.dev/bounties/c37e975c-21a3-4c5f-9b57-04d63b28cfc9", "https://nvd.nist.gov/vuln/detail/CVE-2021-3765", "https://www.cve.org/CVERecord?id=CVE-2021-3765"], "PublishedDate": "2021-11-02T07:15:07.28Z", "LastModifiedDate": "2024-11-21T06:22:22.297Z"}, {"VulnerabilityID": "CVE-2024-37890", "PkgID": "ws@6.1.4", "PkgName": "ws", "PkgIdentifier": {"PURL": "pkg:npm/ws@6.1.4", "UID": "3f3cbab0b19b0f"}, "InstalledVersion": "6.1.4", "FixedVersion": "5.2.4, 6.2.3, 7.5.10, 8.17.1", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2024-37890", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-ws: denial of service when handling a request with many HTTP headers", "Description": "ws is an open source WebSocket client and server for Node.js. A request with a number of headers exceeding theserver.maxHeadersCount threshold could be used to crash a ws server. The vulnerability was fixed in ws@8.17.1 (e55e510) and backported to ws@7.5.10 (22c2876), ws@6.2.3 (eeb76d3), and ws@5.2.4 (4abd8f6). In vulnerable versions of ws, the issue can be mitigated in the following ways: 1. Reduce the maximum allowed length of the request headers using the --max-http-header-size=size and/or the maxHeaderSize options so that no more headers than the server.maxHeadersCount limit can be sent. 2. Set server.maxHeadersCount to 0 so that no limit is applied.", "Severity": "HIGH", "CweIDs": ["CWE-476"], "VendorSeverity": {"cbl-mariner": 3, "ghsa": 3, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 5.9}}, "References": ["https://access.redhat.com/security/cve/CVE-2024-37890", "https://github.com/websockets/ws", "https://github.com/websockets/ws/commit/22c28763234aa75a7e1b76f5c01c181260d7917f", "https://github.com/websockets/ws/commit/4abd8f6de4b0b65ef80b3ff081989479ed93377e", "https://github.com/websockets/ws/commit/e55e5106f10fcbaac37cfa89759e4cc0d073a52c", "https://github.com/websockets/ws/commit/eeb76d313e2a00dd5247ca3597bba7877d064a63", "https://github.com/websockets/ws/issues/2230", "https://github.com/websockets/ws/pull/2231", "https://github.com/websockets/ws/security/advisories/GHSA-3h5v-q93c-6h6q", "https://nodejs.org/api/http.html#servermaxheaderscount", "https://nvd.nist.gov/vuln/detail/CVE-2024-37890", "https://www.cve.org/CVERecord?id=CVE-2024-37890"], "PublishedDate": "2024-06-17T20:15:13.203Z", "LastModifiedDate": "2024-11-21T09:24:28.81Z"}, {"VulnerabilityID": "CVE-2021-32640", "PkgID": "ws@6.1.4", "PkgName": "ws", "PkgIdentifier": {"PURL": "pkg:npm/ws@6.1.4", "UID": "3f3cbab0b19b0f"}, "InstalledVersion": "6.1.4", "FixedVersion": "7.4.6, 6.2.2, 5.2.3", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2021-32640", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-ws: Specially crafted value of the `Sec-Websocket-Protocol` header can be used to significantly slow down a ws server", "Description": "ws is an open source WebSocket client and server library for Node.js. A specially crafted value of the `Sec-Websocket-Protocol` header can be used to significantly slow down a ws server. The vulnerability has been fixed in ws@7.4.6 (https://github.com/websockets/ws/commit/00c425ec77993773d823f018f64a5c44e17023ff). In vulnerable versions of ws, the issue can be mitigated by reducing the maximum allowed length of the request headers using the [`--max-http-header-size=size`](https://nodejs.org/api/cli.html#cli_max_http_header_size_size) and/or the [`maxHeaderSize`](https://nodejs.org/api/http.html#http_http_createserver_options_requestlistener) options.", "Severity": "MEDIUM", "CweIDs": ["CWE-400"], "VendorSeverity": {"ghsa": 2, "nvd": 2, "redhat": 1}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "V3Score": 5.3}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:N/I:N/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "V2Score": 5, "V3Score": 5.3}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "V3Score": 5.3}}, "References": ["https://access.redhat.com/security/cve/CVE-2021-32640", "https://github.com/websockets/ws", "https://github.com/websockets/ws/commit/00c425ec77993773d823f018f64a5c44e17023ff", "https://github.com/websockets/ws/issues/1895", "https://github.com/websockets/ws/security/advisories/GHSA-6fc8-4gx4-v693", "https://lists.apache.org/thread.html/rdfa7b6253c4d6271e31566ecd5f30b7ce1b8fb2c89d52b8c4e0f4e30%40%3Ccommits.tinkerpop.apache.org%3E", "https://lists.apache.org/thread.html/rdfa7b6253c4d6271e31566ecd5f30b7ce1b8fb2c89d52b8c4e0f4e30@%3Ccommits.tinkerpop.apache.org%3E", "https://nvd.nist.gov/vuln/detail/CVE-2021-32640", "https://security.netapp.com/advisory/ntap-*************", "https://security.netapp.com/advisory/ntap-*************/", "https://www.cve.org/CVERecord?id=CVE-2021-32640"], "PublishedDate": "2021-05-25T19:15:07.767Z", "LastModifiedDate": "2024-11-21T06:07:26.223Z"}, {"VulnerabilityID": "CVE-2024-37890", "PkgID": "ws@6.2.1", "PkgName": "ws", "PkgIdentifier": {"PURL": "pkg:npm/ws@6.2.1", "UID": "ff2e9e6b9f0c1ebe"}, "InstalledVersion": "6.2.1", "FixedVersion": "5.2.4, 6.2.3, 7.5.10, 8.17.1", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2024-37890", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-ws: denial of service when handling a request with many HTTP headers", "Description": "ws is an open source WebSocket client and server for Node.js. A request with a number of headers exceeding theserver.maxHeadersCount threshold could be used to crash a ws server. The vulnerability was fixed in ws@8.17.1 (e55e510) and backported to ws@7.5.10 (22c2876), ws@6.2.3 (eeb76d3), and ws@5.2.4 (4abd8f6). In vulnerable versions of ws, the issue can be mitigated in the following ways: 1. Reduce the maximum allowed length of the request headers using the --max-http-header-size=size and/or the maxHeaderSize options so that no more headers than the server.maxHeadersCount limit can be sent. 2. Set server.maxHeadersCount to 0 so that no limit is applied.", "Severity": "HIGH", "CweIDs": ["CWE-476"], "VendorSeverity": {"cbl-mariner": 3, "ghsa": 3, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 7.5}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:N/I:N/A:H", "V3Score": 5.9}}, "References": ["https://access.redhat.com/security/cve/CVE-2024-37890", "https://github.com/websockets/ws", "https://github.com/websockets/ws/commit/22c28763234aa75a7e1b76f5c01c181260d7917f", "https://github.com/websockets/ws/commit/4abd8f6de4b0b65ef80b3ff081989479ed93377e", "https://github.com/websockets/ws/commit/e55e5106f10fcbaac37cfa89759e4cc0d073a52c", "https://github.com/websockets/ws/commit/eeb76d313e2a00dd5247ca3597bba7877d064a63", "https://github.com/websockets/ws/issues/2230", "https://github.com/websockets/ws/pull/2231", "https://github.com/websockets/ws/security/advisories/GHSA-3h5v-q93c-6h6q", "https://nodejs.org/api/http.html#servermaxheaderscount", "https://nvd.nist.gov/vuln/detail/CVE-2024-37890", "https://www.cve.org/CVERecord?id=CVE-2024-37890"], "PublishedDate": "2024-06-17T20:15:13.203Z", "LastModifiedDate": "2024-11-21T09:24:28.81Z"}, {"VulnerabilityID": "CVE-2021-32640", "PkgID": "ws@6.2.1", "PkgName": "ws", "PkgIdentifier": {"PURL": "pkg:npm/ws@6.2.1", "UID": "ff2e9e6b9f0c1ebe"}, "InstalledVersion": "6.2.1", "FixedVersion": "7.4.6, 6.2.2, 5.2.3", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2021-32640", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-ws: Specially crafted value of the `Sec-Websocket-Protocol` header can be used to significantly slow down a ws server", "Description": "ws is an open source WebSocket client and server library for Node.js. A specially crafted value of the `Sec-Websocket-Protocol` header can be used to significantly slow down a ws server. The vulnerability has been fixed in ws@7.4.6 (https://github.com/websockets/ws/commit/00c425ec77993773d823f018f64a5c44e17023ff). In vulnerable versions of ws, the issue can be mitigated by reducing the maximum allowed length of the request headers using the [`--max-http-header-size=size`](https://nodejs.org/api/cli.html#cli_max_http_header_size_size) and/or the [`maxHeaderSize`](https://nodejs.org/api/http.html#http_http_createserver_options_requestlistener) options.", "Severity": "MEDIUM", "CweIDs": ["CWE-400"], "VendorSeverity": {"ghsa": 2, "nvd": 2, "redhat": 1}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "V3Score": 5.3}, "nvd": {"V2Vector": "AV:N/AC:L/Au:N/C:N/I:N/A:P", "V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "V2Score": 5, "V3Score": 5.3}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "V3Score": 5.3}}, "References": ["https://access.redhat.com/security/cve/CVE-2021-32640", "https://github.com/websockets/ws", "https://github.com/websockets/ws/commit/00c425ec77993773d823f018f64a5c44e17023ff", "https://github.com/websockets/ws/issues/1895", "https://github.com/websockets/ws/security/advisories/GHSA-6fc8-4gx4-v693", "https://lists.apache.org/thread.html/rdfa7b6253c4d6271e31566ecd5f30b7ce1b8fb2c89d52b8c4e0f4e30%40%3Ccommits.tinkerpop.apache.org%3E", "https://lists.apache.org/thread.html/rdfa7b6253c4d6271e31566ecd5f30b7ce1b8fb2c89d52b8c4e0f4e30@%3Ccommits.tinkerpop.apache.org%3E", "https://nvd.nist.gov/vuln/detail/CVE-2021-32640", "https://security.netapp.com/advisory/ntap-*************", "https://security.netapp.com/advisory/ntap-*************/", "https://www.cve.org/CVERecord?id=CVE-2021-32640"], "PublishedDate": "2021-05-25T19:15:07.767Z", "LastModifiedDate": "2024-11-21T06:07:26.223Z"}, {"VulnerabilityID": "CVE-2023-0842", "PkgID": "xml2js@0.4.19", "PkgName": "xml2js", "PkgIdentifier": {"PURL": "pkg:npm/xml2js@0.4.19", "UID": "4d944744fce940f5"}, "InstalledVersion": "0.4.19", "FixedVersion": "0.5.0", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2023-0842", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "node-xml2js: xml2js is vulnerable to prototype pollution", "Description": "xml2js version 0.4.23 allows an external attacker to edit or add new properties to an object. This is possible because the application does not properly validate incoming JSON keys, thus allowing the __proto__ property to be edited.", "Severity": "MEDIUM", "CweIDs": ["CWE-1321"], "VendorSeverity": {"ghsa": 2, "nvd": 2, "redhat": 2}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N", "V3Score": 5.3}, "nvd": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N", "V3Score": 5.3}, "redhat": {"V3Vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N", "V3Score": 5.3}}, "References": ["https://access.redhat.com/security/cve/CVE-2023-0842", "https://fluidattacks.com/advisories/myers", "https://fluidattacks.com/advisories/myers/", "https://github.com/<PERSON><PERSON>-from-XIV/node-xml2js", "https://github.com/<PERSON><PERSON>-from-XIV/node-xml2js/", "https://github.com/<PERSON><PERSON>-from-XIV/node-xml2js/issues/663", "https://github.com/<PERSON><PERSON>-from-XIV/node-xml2js/pull/603/commits/581b19a62d88f8a3c068b5a45f4542c2d6a495a5", "https://github.com/advisories/GHSA-776f-qx25-q3cc", "https://lists.debian.org/debian-lts-announce/2024/03/msg00013.html", "https://nvd.nist.gov/vuln/detail/CVE-2023-0842", "https://www.cve.org/CVERecord?id=CVE-2023-0842"], "PublishedDate": "2023-04-05T20:15:07.493Z", "LastModifiedDate": "2025-02-13T20:15:47.193Z"}, {"VulnerabilityID": "CVE-2020-7608", "PkgID": "yargs-parser@13.1.0", "PkgName": "yargs-parser", "PkgIdentifier": {"PURL": "pkg:npm/yargs-parser@13.1.0", "UID": "f0cb8f7e67ee6a89"}, "InstalledVersion": "13.1.0", "FixedVersion": "13.1.2, 15.0.1, 18.1.1, 5.0.1", "Status": "fixed", "SeveritySource": "ghsa", "PrimaryURL": "https://avd.aquasec.com/nvd/cve-2020-7608", "DataSource": {"ID": "ghsa", "Name": "GitHub Security Advisory npm", "URL": "https://github.com/advisories?query=type%3Areviewed+ecosystem%3Anpm"}, "Title": "nodejs-yargs-parser: prototype pollution vulnerability", "Description": "yargs-parser could be tricked into adding or modifying properties of Object.prototype using a \"__proto__\" payload.", "Severity": "MEDIUM", "CweIDs": ["CWE-1321"], "VendorSeverity": {"alma": 2, "ghsa": 2, "nvd": 2, "oracle-oval": 2, "redhat": 1}, "CVSS": {"ghsa": {"V3Vector": "CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:L", "V3Score": 5.3}, "nvd": {"V2Vector": "AV:L/AC:L/Au:N/C:P/I:P/A:P", "V3Vector": "CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:L", "V2Score": 4.6, "V3Score": 5.3}, "redhat": {"V3Vector": "CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:L", "V3Score": 5.3}}, "References": ["https://access.redhat.com/security/cve/CVE-2020-7608", "https://errata.almalinux.org/8/ALSA-2021-0548.html", "https://github.com/yargs/yargs-parser", "https://github.com/yargs/yargs-parser/commit/1c417bd0b42b09c475ee881e36d292af4fa2cc36", "https://github.com/yargs/yargs-parser/commit/63810ca1ae1a24b08293a4d971e70e058c7a41e2", "https://linux.oracle.com/cve/CVE-2020-7608.html", "https://linux.oracle.com/errata/ELSA-2021-0548.html", "https://nvd.nist.gov/vuln/detail/CVE-2020-7608", "https://snyk.io/vuln/SNYK-JS-YARGSPARSER-560381", "https://www.cve.org/CVERecord?id=CVE-2020-7608", "https://www.npmjs.com/advisories/1500"], "PublishedDate": "2020-03-16T20:15:12.86Z", "LastModifiedDate": "2024-11-21T05:37:27.963Z"}]}, {"Target": "vulnerable-app/config.js", "Class": "secret", "Secrets": [{"RuleID": "stripe-secret-token", "Category": "Stripe", "Severity": "CRITICAL", "Title": "Stripe Secret Key", "StartLine": 27, "EndLine": 27, "Code": {"Lines": [{"Number": 25, "Content": "    // API keys exposed in configuration", "IsCause": false, "Annotation": "", "Truncated": false, "Highlighted": "    // API keys exposed in configuration", "FirstCause": false, "LastCause": false}, {"Number": 26, "Content": "    apiKeys: {", "IsCause": false, "Annotation": "", "Truncated": false, "Highlighted": "    apiKeys: {", "FirstCause": false, "LastCause": false}, {"Number": 27, "Content": "        stripe: '********************************',", "IsCause": true, "Annotation": "", "Truncated": false, "Highlighted": "        stripe: '********************************',", "FirstCause": true, "LastCause": true}, {"Number": 28, "Content": "        aws: 'AKIAIOSFODNN7EXAMPLE',", "IsCause": false, "Annotation": "", "Truncated": false, "Highlighted": "        aws: 'AKIAIOSFODNN7EXAMPLE',", "FirstCause": false, "LastCause": false}]}, "Match": "        stripe: '********************************',"}]}]}