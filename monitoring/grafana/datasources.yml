# Grafana datasources configuration for Infinitium Signal

apiVersion: 1

datasources:
  # Prometheus datasource for metrics
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "15s"
      queryTimeout: "60s"
      httpMethod: "POST"
      manageAlerts: true
      alertmanagerUid: "alertmanager"
    secureJsonData: {}

  # Loki datasource for logs
  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    editable: true
    jsonData:
      maxLines: 1000
      timeout: 60
      derivedFields:
        - datasourceUid: "prometheus"
          matcherRegex: "trace_id=(\\w+)"
          name: "TraceID"
          url: "$${__value.raw}"

  # Alertmanager datasource
  - name: Alertmanager
    type: alertmanager
    access: proxy
    url: http://alertmanager:9093
    uid: "alertmanager"
    editable: true
    jsonData:
      implementation: "prometheus"

  # PostgreSQL datasource for direct database queries
  - name: PostgreSQL
    type: postgres
    access: proxy
    url: postgres:5432
    database: infinitium_signal
    user: ${POSTGRES_USER}
    editable: true
    jsonData:
      sslmode: "require"
      maxOpenConns: 10
      maxIdleConns: 2
      connMaxLifetime: 14400
      postgresVersion: 1500
      timescaledb: false
    secureJsonData:
      password: ${POSTGRES_PASSWORD}

  # InfluxDB datasource (if using for time series data)
  - name: InfluxDB
    type: influxdb
    access: proxy
    url: http://influxdb:8086
    database: infinitium_signal
    user: ${INFLUXDB_USER}
    editable: true
    jsonData:
      version: "InfluxQL"
      timeInterval: "10s"
      httpMode: "GET"
    secureJsonData:
      password: ${INFLUXDB_PASSWORD}

  # Elasticsearch datasource (if using for log analysis)
  - name: Elasticsearch
    type: elasticsearch
    access: proxy
    url: http://elasticsearch:9200
    database: "infinitium-signal-*"
    editable: true
    jsonData:
      interval: "Daily"
      timeField: "@timestamp"
      esVersion: "7.10.0"
      maxConcurrentShardRequests: 5
      logMessageField: "message"
      logLevelField: "level"

  # Jaeger datasource for distributed tracing
  - name: Jaeger
    type: jaeger
    access: proxy
    url: http://jaeger:16686
    editable: true
    jsonData:
      tracesToLogs:
        datasourceUid: "loki"
        tags: ["job", "instance", "pod", "namespace"]
        mappedTags: [
          {
            key: "service.name",
            value: "service"
          }
        ]
        mapTagNamesEnabled: false
        spanStartTimeShift: "1h"
        spanEndTimeShift: "1h"
        filterByTraceID: false
        filterBySpanID: false

  # Tempo datasource for tracing (alternative to Jaeger)
  - name: Tempo
    type: tempo
    access: proxy
    url: http://tempo:3200
    editable: true
    jsonData:
      tracesToLogs:
        datasourceUid: "loki"
        tags: ["job", "instance"]
        mappedTags: [
          {
            key: "service.name",
            value: "service"
          }
        ]
        mapTagNamesEnabled: false
        spanStartTimeShift: "1h"
        spanEndTimeShift: "1h"
      serviceMap:
        datasourceUid: "prometheus"
      nodeGraph:
        enabled: true
      search:
        hide: false
      lokiSearch:
        datasourceUid: "loki"

  # TestData datasource for testing dashboards
  - name: TestData
    type: testdata
    access: proxy
    editable: true
    jsonData: {}

# Notification channels configuration
notifiers:
  - name: slack-notifications
    type: slack
    uid: "slack"
    settings:
      url: ${SLACK_WEBHOOK_URL}
      channel: "#infinitium-signal-alerts"
      username: "Grafana"
      title: "Infinitium Signal Alert"
      text: "{{ range .Alerts }}{{ .Annotations.summary }}\n{{ .Annotations.description }}{{ end }}"

  - name: email-notifications
    type: email
    uid: "email"
    settings:
      addresses: ${ALERT_EMAIL_ADDRESSES}
      subject: "Infinitium Signal Alert - {{ .CommonLabels.alertname }}"

  - name: webhook-notifications
    type: webhook
    uid: "webhook"
    settings:
      url: ${WEBHOOK_URL}
      httpMethod: "POST"
      maxAlerts: 0
