# Prometheus alerting rules for Infinitium Signal

groups:
  - name: infinitium-signal.rules
    rules:
      # Application Health Rules
      - alert: InfinitiumSignalDown
        expr: up{job="infinitium-signal"} == 0
        for: 1m
        labels:
          severity: critical
          service: infinitium-signal
        annotations:
          summary: "Infinitium Signal service is down"
          description: "Infinitium Signal service has been down for more than 1 minute."

      - alert: InfinitiumSignalHighErrorRate
        expr: rate(infinitium_signal_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: infinitium-signal
        annotations:
          summary: "High error rate in Infinitium Signal"
          description: "Error rate is {{ $value }} errors per second over the last 5 minutes."

      - alert: InfinitiumSignalHighLatency
        expr: histogram_quantile(0.95, rate(infinitium_signal_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: infinitium-signal
        annotations:
          summary: "High latency in Infinitium Signal"
          description: "95th percentile latency is {{ $value }}s over the last 5 minutes."

      # Database Rules
      - alert: PostgreSQLDown
        expr: up{job="postgresql"} == 0
        for: 1m
        labels:
          severity: critical
          service: postgresql
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL database has been down for more than 1 minute."

      - alert: PostgreSQLHighConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
          service: postgresql
        annotations:
          summary: "PostgreSQL high connection usage"
          description: "PostgreSQL connection usage is {{ $value | humanizePercentage }}."

      - alert: PostgreSQLSlowQueries
        expr: rate(pg_stat_database_tup_returned[5m]) / rate(pg_stat_database_tup_fetched[5m]) < 0.1
        for: 10m
        labels:
          severity: warning
          service: postgresql
        annotations:
          summary: "PostgreSQL slow queries detected"
          description: "PostgreSQL query efficiency is low: {{ $value | humanizePercentage }}."

      # System Resource Rules
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}."

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}."

      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "Disk space low"
          description: "Disk usage is {{ $value }}% on {{ $labels.instance }} mount {{ $labels.mountpoint }}."

      # Security Rules
      - alert: SecurityVulnerabilityDetected
        expr: increase(infinitium_signal_vulnerabilities_detected_total[1h]) > 0
        for: 0m
        labels:
          severity: warning
          service: infinitium-signal
          type: security
        annotations:
          summary: "New security vulnerabilities detected"
          description: "{{ $value }} new vulnerabilities detected in the last hour."

      - alert: CriticalVulnerabilityDetected
        expr: increase(infinitium_signal_critical_vulnerabilities_total[1h]) > 0
        for: 0m
        labels:
          severity: critical
          service: infinitium-signal
          type: security
        annotations:
          summary: "Critical security vulnerabilities detected"
          description: "{{ $value }} critical vulnerabilities detected in the last hour."

      - alert: UnauthorizedAccessAttempt
        expr: increase(infinitium_signal_unauthorized_requests_total[5m]) > 10
        for: 0m
        labels:
          severity: warning
          service: infinitium-signal
          type: security
        annotations:
          summary: "Multiple unauthorized access attempts"
          description: "{{ $value }} unauthorized access attempts in the last 5 minutes."

      # Scanning Rules
      - alert: ScanFailureRate
        expr: rate(infinitium_signal_scan_failures_total[10m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: infinitium-signal
        annotations:
          summary: "High scan failure rate"
          description: "Scan failure rate is {{ $value }} failures per second."

      - alert: ScanQueueBacklog
        expr: infinitium_signal_scan_queue_size > 100
        for: 10m
        labels:
          severity: warning
          service: infinitium-signal
        annotations:
          summary: "Large scan queue backlog"
          description: "Scan queue has {{ $value }} pending items."

      - alert: LongRunningScan
        expr: infinitium_signal_scan_duration_seconds > 3600
        for: 0m
        labels:
          severity: warning
          service: infinitium-signal
        annotations:
          summary: "Long running scan detected"
          description: "Scan {{ $labels.scan_id }} has been running for {{ $value | humanizeDuration }}."

      # Compliance Rules
      - alert: ComplianceReportGenerationFailed
        expr: increase(infinitium_signal_compliance_report_failures_total[1h]) > 0
        for: 0m
        labels:
          severity: warning
          service: infinitium-signal
          type: compliance
        annotations:
          summary: "Compliance report generation failed"
          description: "{{ $value }} compliance report generation failures in the last hour."

      - alert: ComplianceViolationDetected
        expr: increase(infinitium_signal_compliance_violations_total[1h]) > 0
        for: 0m
        labels:
          severity: critical
          service: infinitium-signal
          type: compliance
        annotations:
          summary: "Compliance violations detected"
          description: "{{ $value }} compliance violations detected in the last hour."

      # Blockchain Rules
      - alert: BlockchainCommitFailed
        expr: increase(infinitium_signal_blockchain_commit_failures_total[1h]) > 0
        for: 0m
        labels:
          severity: warning
          service: infinitium-signal
          type: blockchain
        annotations:
          summary: "Blockchain commit failures"
          description: "{{ $value }} blockchain commit failures in the last hour."

      - alert: BlockchainSyncLag
        expr: infinitium_signal_blockchain_sync_lag_blocks > 10
        for: 5m
        labels:
          severity: warning
          service: infinitium-signal
          type: blockchain
        annotations:
          summary: "Blockchain sync lag detected"
          description: "Blockchain is {{ $value }} blocks behind."

      # API Rules
      - alert: APIRateLimitExceeded
        expr: increase(infinitium_signal_rate_limit_exceeded_total[5m]) > 50
        for: 2m
        labels:
          severity: warning
          service: infinitium-signal
          type: api
        annotations:
          summary: "API rate limit frequently exceeded"
          description: "Rate limit exceeded {{ $value }} times in the last 5 minutes."

      - alert: APIResponseTimeHigh
        expr: histogram_quantile(0.95, rate(infinitium_signal_api_request_duration_seconds_bucket[5m])) > 5
        for: 5m
        labels:
          severity: warning
          service: infinitium-signal
          type: api
        annotations:
          summary: "High API response time"
          description: "95th percentile API response time is {{ $value }}s."

  - name: infrastructure.rules
    rules:
      # Container Rules
      - alert: ContainerKilled
        expr: time() - container_last_seen > 60
        for: 0m
        labels:
          severity: warning
          service: docker
        annotations:
          summary: "Container killed"
          description: "Container {{ $labels.name }} has disappeared."

      - alert: ContainerCpuUsage
        expr: (sum(rate(container_cpu_usage_seconds_total[3m])) BY (instance, name) * 100) > 80
        for: 2m
        labels:
          severity: warning
          service: docker
        annotations:
          summary: "Container CPU usage"
          description: "Container CPU usage is above 80%."

      - alert: ContainerMemoryUsage
        expr: (sum(container_memory_working_set_bytes) BY (instance, name) / sum(container_spec_memory_limit_bytes > 0) BY (instance, name) * 100) > 80
        for: 2m
        labels:
          severity: warning
          service: docker
        annotations:
          summary: "Container Memory usage"
          description: "Container Memory usage is above 80%."

      # Network Rules
      - alert: HighNetworkReceiveErrors
        expr: rate(node_network_receive_errs_total[5m]) > 10
        for: 5m
        labels:
          severity: warning
          service: network
        annotations:
          summary: "High network receive errors"
          description: "Network interface {{ $labels.device }} has {{ $value }} receive errors per second."

      - alert: HighNetworkTransmitErrors
        expr: rate(node_network_transmit_errs_total[5m]) > 10
        for: 5m
        labels:
          severity: warning
          service: network
        annotations:
          summary: "High network transmit errors"
          description: "Network interface {{ $labels.device }} has {{ $value }} transmit errors per second."

      # Enhanced Alerting Rules for Infinitium Signal
      - alert: LicenseDetectionAccuracyLow
        expr: infinitium_signal_license_precision < 0.8
        for: 10m
        labels:
          severity: warning
          service: infinitium-signal
          type: compliance
        annotations:
          summary: "License detection accuracy is low"
          description: "License detection precision is {{ $value | humanizePercentage }} over the last 10 minutes."

      - alert: MLModelPerformanceDegraded
        expr: infinitium_signal_ml_model_accuracy < 0.85
        for: 15m
        labels:
          severity: error
          service: infinitium-signal
          type: ml
        annotations:
          summary: "ML model performance degraded"
          description: "ML model accuracy dropped to {{ $value | humanizePercentage }}."

      - alert: DatabaseConnectionPoolExhausted
        expr: infinitium_signal_db_connection_pool_active / infinitium_signal_db_connection_pool_total > 0.9
        for: 5m
        labels:
          severity: critical
          service: database
        annotations:
          summary: "Database connection pool nearly exhausted"
          description: "Database connection pool usage is {{ $value | humanizePercentage }}."

      - alert: RequestQueueBacklogHigh
        expr: infinitium_signal_request_queue_depth > 1000
        for: 5m
        labels:
          severity: warning
          service: infinitium-signal
          type: performance
        annotations:
          summary: "High request queue backlog"
          description: "Request queue depth is {{ $value }}, indicating processing bottleneck."

      - alert: MemoryUsageCritical
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 95
        for: 2m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "Critical memory usage"
          description: "Memory usage is {{ $value }}% - immediate action required."

      - alert: DiskIOPerformanceDegraded
        expr: rate(node_disk_io_time_seconds_total[5m]) > 0.8
        for: 10m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "Disk I/O performance degraded"
          description: "Disk I/O time is {{ $value | humanizePercentage }} of total time."

      - alert: APIResponseTimeDegraded
        expr: histogram_quantile(0.99, rate(infinitium_signal_api_request_duration_seconds_bucket[5m])) > 10
        for: 5m
        labels:
          severity: error
          service: infinitium-signal
          type: api
        annotations:
          summary: "API response time severely degraded"
          description: "99th percentile API response time is {{ $value }}s."

      - alert: ComplianceScanFailureRateHigh
        expr: rate(infinitium_signal_compliance_scan_failures_total[10m]) > 0.2
        for: 5m
        labels:
          severity: error
          service: infinitium-signal
          type: compliance
        annotations:
          summary: "High compliance scan failure rate"
          description: "Compliance scan failure rate is {{ $value }} failures per second."

      - alert: SecurityEventRateHigh
        expr: rate(infinitium_signal_security_events_total[5m]) > 50
        for: 2m
        labels:
          severity: critical
          service: infinitium-signal
          type: security
        annotations:
          summary: "High security event rate"
          description: "{{ $value }} security events per second detected."

      - alert: LogAnomalyDetected
        expr: increase(infinitium_signal_log_anomalies_total[5m]) > 0
        for: 0m
        labels:
          severity: warning
          service: infinitium-signal
          type: logs
        annotations:
          summary: "Log anomaly detected"
      - alert: LogAnomalyDetected
        expr: increase(infinitium_signal_log_anomalies_total[5m]) > 0
        for: 0m
        labels:
          severity: warning
          service: infinitium-signal
          type: logs
        annotations:
          summary: "Log anomaly detected"
          description: "Log pattern anomaly detected in the system logs."

   - name: osv.rules
     rules:
       # OSV Scanner Performance Rules
       - alert: OSVScanDurationHigh
         expr: osv_scan_duration_seconds > 300
         for: 5m
         labels:
           severity: warning
           service: osv-scanner
           type: performance
         annotations:
           summary: "OSV scan duration is high"
           description: "OSV scan has been running for {{ $value | humanizeDuration }}."

       - alert: OSVAPIResponseTimeHigh
         expr: osv_api_response_time_seconds > 10
         for: 5m
         labels:
           severity: warning
           service: osv-scanner
           type: performance
         annotations:
           summary: "OSV API response time is high"
           description: "OSV API response time is {{ $value }}s."

       - alert: OSVCacheHitRatioLow
         expr: osv_cache_hit_ratio < 0.5
         for: 10m
         labels:
           severity: warning
           service: osv-scanner
           type: performance
         annotations:
           summary: "OSV cache hit ratio is low"
           description: "OSV cache hit ratio is {{ $value | humanizePercentage }}."

       - alert: OSVFalsePositiveRateHigh
         expr: osv_scan_false_positive_rate > 0.3
         for: 10m
         labels:
           severity: warning
           service: osv-scanner
           type: accuracy
         annotations:
           summary: "OSV false positive rate is high"
           description: "OSV false positive rate is {{ $value | humanizePercentage }}."

       - alert: OSVCriticalVulnerabilitiesDetected
         expr: increase(osv_vulnerabilities_by_severity{severity="critical"}[1h]) > 0
         for: 0m
         labels:
           severity: critical
           service: osv-scanner
           type: security
         annotations:
           summary: "Critical OSV vulnerabilities detected"
           description: "{{ $value }} critical vulnerabilities detected in the last hour."

       - alert: OSVScanFailureRateHigh
         expr: rate(osv_scan_failures_total[10m]) > 0.1
         for: 5m
         labels:
           severity: error
           service: osv-scanner
           type: reliability
         annotations:
           summary: "High OSV scan failure rate"
           description: "OSV scan failure rate is {{ $value }} failures per second."

       - alert: OSVParallelProcessingEfficiencyLow
         expr: osv_parallel_processing_efficiency < 0.7
         for: 10m
         labels:
           severity: warning
           service: osv-scanner
           type: performance
         annotations:
           summary: "OSV parallel processing efficiency is low"
           description: "OSV parallel processing efficiency is {{ $value | humanizePercentage }}."

       - alert: OSVMemoryUsageHigh
         expr: osv_memory_usage_mb > 400
         for: 5m
         labels:
           severity: warning
           service: osv-scanner
           type: performance
         annotations:
           summary: "OSV memory usage is high"
           description: "OSV scanner is using {{ $value }} MB of memory."

       - alert: OSVComplianceScoreLow
         expr: osv_compliance_score < 70
         for: 10m
         labels:
           severity: warning
           service: osv-scanner
           type: compliance
         annotations:
           summary: "OSV compliance score is low"
           description: "OSV compliance score is {{ $value }}%."

       - alert: OSVAccuracyScoreLow
         expr: osv_accuracy_score < 0.8
         for: 15m
         labels:
           severity: error
           service: osv-scanner
           type: accuracy
         annotations:
           summary: "OSV detection accuracy is low"
           description: "OSV detection accuracy is {{ $value | humanizePercentage }}."
          description: "Log pattern anomaly detected in the system logs."
