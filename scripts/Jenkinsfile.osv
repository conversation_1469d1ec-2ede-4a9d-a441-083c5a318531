#!/usr/bin/env groovy

/**
 * Jenkins Pipeline for OSV Vulnerability Scanning
 *
 * This pipeline integrates OSV scanning into the CI/CD process for the Infinitium Signal project.
 * It can be triggered manually, on PRs, or scheduled runs.
 */

pipeline {
    agent {
        docker {
            image 'rust:1.70-slim'
            args '-v /var/run/docker.sock:/var/run/docker.sock'
        }
    }

    parameters {
        choice(name: 'SCAN_TYPE', choices: ['full', 'incremental', 'dependency-only'], description: 'Type of OSV scan to perform')
        string(name: 'BRANCH_NAME', defaultValue: 'main', description: 'Branch to scan')
        booleanParam(name: 'FAIL_ON_VULNERABILITIES', defaultValue: true, description: 'Fail build if vulnerabilities are found')
        booleanParam(name: 'GENERATE_REPORT', defaultValue: true, description: 'Generate detailed HTML report')
        string(name: 'SLACK_CHANNEL', defaultValue: '', description: 'Slack channel for notifications (optional)')
    }

    environment {
        RUST_BACKTRACE = '1'
        CARGO_HOME = '/usr/local/cargo'
        RUSTUP_HOME = '/usr/local/rustup'
        PATH = "/usr/local/cargo/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
    }

    stages {
        stage('Checkout') {
            steps {
                script {
                    if (env.CHANGE_TARGET) {
                        // This is a PR build
                        checkout([
                            $class: 'GitSCM',
                            branches: [[name: "origin/${env.CHANGE_TARGET}"]],
                            doGenerateSubmoduleConfigurations: false,
                            extensions: [[$class: 'CleanCheckout']],
                            submoduleCfg: [],
                            userRemoteConfigs: [[
                                credentialsId: 'github-credentials',
                                url: "${env.GIT_URL}"
                            ]]
                        ])
                    } else {
                        // Regular branch build
                        checkout scm
                    }
                }
            }
        }

        stage('Setup Environment') {
            steps {
                sh '''
                    apt-get update && apt-get install -y \
                        jq \
                        curl \
                        git \
                        build-essential \
                        pkg-config \
                        libssl-dev

                    # Verify Rust installation
                    rustc --version
                    cargo --version

                    # Create necessary directories
                    mkdir -p reports/osv
                    mkdir -p artifacts
                '''
            }
        }

        stage('Build Project') {
            steps {
                sh '''
                    echo "Building Infinitium Signal..."
                    cargo build --release
                '''
            }
        }

        stage('Extract Dependencies') {
            steps {
                script {
                    sh '''
                        echo "Extracting dependencies for OSV scanning..."

                        # Extract dependencies from Cargo.toml
                        cargo metadata --format-version 1 > cargo_metadata.json

                        # Create dependency list for scanning
                        jq -r '.packages[] | select(.name == "infinitium-signal") | .dependencies[] | "\(.name) \(.version)"' cargo_metadata.json > dependencies.txt

                        # Count dependencies
                        DEP_COUNT=$(wc -l < dependencies.txt)
                        echo "Found ${DEP_COUNT} dependencies to scan"
                        echo "DEP_COUNT=${DEP_COUNT}" > dependency_info.txt
                    '''

                    env.DEP_COUNT = sh(script: 'cat dependency_info.txt | grep DEP_COUNT | cut -d= -f2', returnStdout: true).trim()
                }
            }
        }

        stage('Run OSV Scan') {
            steps {
                script {
                    def scanCommand = './target/release/infinitum-signal scan . --format json'

                    if (params.SCAN_TYPE == 'dependency-only') {
                        scanCommand += ' --dependencies-only'
                    }

                    try {
                        sh """
                            echo "Starting OSV scan..."
                            echo "Scan type: ${params.SCAN_TYPE}"
                            echo "Branch: ${params.BRANCH_NAME}"

                            # Run the scan
                            ${scanCommand} > osv_scan_results.json 2>&1 || echo '{"error": "Scan failed", "output": "'"\$(cat /dev/stderr)"'"}' > osv_scan_results.json
                        """

                        // Parse results
                        def scanResults = readJSON file: 'osv_scan_results.json'

                        if (scanResults.error) {
                            echo "OSV scan failed: ${scanResults.error}"
                            currentBuild.result = 'UNSTABLE'
                        } else {
                            def vulnCount = scanResults.vulnerabilities ? scanResults.vulnerabilities.size() : 0
                            def highSeverity = scanResults.vulnerabilities ? scanResults.vulnerabilities.findAll { it.severity == 'HIGH' || (it.cvss_score && it.cvss_score >= 7.0) }.size() : 0

                            echo "OSV Scan completed successfully"
                            echo "Total vulnerabilities found: ${vulnCount}"
                            echo "High severity vulnerabilities: ${highSeverity}"

                            // Set environment variables for later stages
                            env.VULN_COUNT = vulnCount.toString()
                            env.HIGH_SEVERITY = highSeverity.toString()

                            // Archive results
                            sh 'cp osv_scan_results.json reports/osv/'
                        }

                    } catch (Exception e) {
                        echo "OSV scan failed with exception: ${e.getMessage()}"
                        currentBuild.result = 'FAILURE'
                        throw e
                    }
                }
            }
        }

        stage('Generate Report') {
            when {
                expression { params.GENERATE_REPORT }
            }
            steps {
                sh '''
                    echo "Generating OSV scan report..."

                    # Create HTML report
                    cat > reports/osv/osv_report.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>OSV Vulnerability Scan Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f8f9fa; padding: 20px; border-radius: 5px; }
        .summary { background-color: #e9ecef; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .vulnerability { border: 1px solid #dee2e6; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .high-severity { border-left: 5px solid #dc3545; }
        .medium-severity { border-left: 5px solid #ffc107; }
        .low-severity { border-left: 5px solid #28a745; }
        .severity-badge { padding: 3px 8px; border-radius: 3px; color: white; font-size: 12px; }
        .high-badge { background-color: #dc3545; }
        .medium-badge { background-color: #ffc107; }
        .low-badge { background-color: #28a745; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔒 OSV Vulnerability Scan Report</h1>
        <p><strong>Project:</strong> Infinitium Signal</p>
        <p><strong>Branch:</strong> ''' + "${params.BRANCH_NAME}" + '''</p>
        <p><strong>Scan Date:</strong> ''' + "$(date)" + '''</p>
        <p><strong>Build:</strong> ''' + "${BUILD_NUMBER}" + '''</p>
    </div>

    <div class="summary">
        <h2>📊 Scan Summary</h2>
        <p><strong>Total Dependencies Scanned:</strong> ''' + "${env.DEP_COUNT}" + '''</p>
        <p><strong>Total Vulnerabilities Found:</strong> ''' + "${env.VULN_COUNT}" + '''</p>
        <p><strong>High Severity:</strong> ''' + "${env.HIGH_SEVERITY}" + '''</p>
    </div>
EOF

                    # Add vulnerability details if any
                    if [ -f osv_scan_results.json ] && [ "${env.VULN_COUNT}" -gt 0 ]; then
                        echo '<h2>🚨 Vulnerabilities Found</h2>' >> reports/osv/osv_report.html

                        jq -r '.vulnerabilities[] | "<div class=\"vulnerability \(.severity | ascii_downcase)-severity\"><h3>\(.id)</h3><p><strong>Severity:</strong> <span class=\"severity-badge \(.severity | ascii_downcase)-badge\">\(.severity // "Unknown")</span></p><p><strong>CVSS Score:</strong> \(.cvss_score // "N/A")</p><p><strong>Package:</strong> \(.package.name)@\(.package.version)</p><p><strong>Summary:</strong> \(.summary)</p><p><strong>Description:</strong> \(.details // "No description available")</p><p><strong>Affected Versions:</strong> \(.affected_versions | join(", ") // "N/A")</p><p><strong>Fixed Versions:</strong> \(.fixed_versions | join(", ") // "N/A")</p></div>"' osv_scan_results.json >> reports/osv/osv_report.html
                    else
                        echo '<div class="summary"><h2>✅ No Vulnerabilities Found</h2><p>All dependencies are secure according to OSV database.</p></div>' >> reports/osv/osv_report.html
                    fi

                    echo '</body></html>' >> reports/osv/osv_report.html
                '''
            }
        }

        stage('Archive Artifacts') {
            steps {
                archiveArtifacts artifacts: 'reports/osv/**, osv_scan_results.json', allowEmptyArchive: true
                publishHTML([
                    allowMissing: true,
                    alwaysLinkToLastBuild: true,
                    keepAll: true,
                    reportDir: 'reports/osv',
                    reportFiles: 'osv_report.html',
                    reportName: 'OSV Vulnerability Report'
                ])
            }
        }

        stage('Quality Gate') {
            when {
                expression { params.FAIL_ON_VULNERABILITIES }
            }
            steps {
                script {
                    def highSeverity = env.HIGH_SEVERITY ? env.HIGH_SEVERITY.toInteger() : 0

                    if (highSeverity > 0) {
                        echo "❌ Build failed: ${highSeverity} high severity vulnerabilities found"
                        currentBuild.result = 'FAILURE'
                        error("High severity vulnerabilities detected. Failing build as requested.")
                    } else {
                        echo "✅ Quality gate passed: No high severity vulnerabilities found"
                    }
                }
            }
        }

        stage('Notifications') {
            steps {
                script {
                    def vulnCount = env.VULN_COUNT ? env.VULN_COUNT.toInteger() : 0
                    def highSeverity = env.HIGH_SEVERITY ? env.HIGH_SEVERITY.toInteger() : 0

                    // Slack notification
                    if (params.SLACK_CHANNEL && (highSeverity > 0 || currentBuild.result != 'SUCCESS')) {
                        slackSend(
                            channel: params.SLACK_CHANNEL,
                            color: highSeverity > 0 ? 'danger' : 'warning',
                            message: """
🚨 OSV Scan Results for ${env.JOB_NAME}

• Branch: ${params.BRANCH_NAME}
• Vulnerabilities Found: ${vulnCount}
• High Severity: ${highSeverity}
• Build: ${env.BUILD_URL}

${highSeverity > 0 ? '❌ Action Required: Review high severity vulnerabilities' : '✅ Scan completed successfully'}
                            """.stripIndent()
                        )
                    }

                    // Email notification for failures
                    if (currentBuild.result != 'SUCCESS') {
                        emailext(
                            subject: "OSV Scan Failed: ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                            body: """
OSV vulnerability scan has failed or found issues.

Job: ${env.JOB_NAME}
Build: ${env.BUILD_NUMBER}
Branch: ${params.BRANCH_NAME}
Result: ${currentBuild.result}

Vulnerabilities Found: ${vulnCount}
High Severity: ${highSeverity}

View details: ${env.BUILD_URL}
                            """,
                            recipientProviders: [[$class: 'CulpritsRecipientProvider']]
                        )
                    }
                }
            }
        }
    }

    post {
        always {
            sh '''
                # Cleanup
                rm -f dependency_info.txt
                rm -f cargo_metadata.json
            '''
        }
        success {
            echo '✅ OSV scan pipeline completed successfully'
        }
        failure {
            echo '❌ OSV scan pipeline failed'
        }
        unstable {
            echo '⚠️ OSV scan pipeline completed with warnings'
        }
    }
}