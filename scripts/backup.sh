#!/bin/bash

# Backup script for Infinitium Signal
# This script creates backups of database, configuration, and important data

set -euo pipefail
IFS=$'\n\t'

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKUP_ROOT="${BACKUP_ROOT:-${PROJECT_ROOT}/backups}"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="${BACKUP_ROOT}/${TIMESTAMP}"

# Default values
DATABASE_URL="${DATABASE_URL:-postgres://infinitium:password@localhost:5432/infinitium_signal}"
REDIS_URL="${REDIS_URL:-redis://localhost:6379}"
BACKUP_TYPE="full"
COMPRESS=true
ENCRYPT=false
RETENTION_DAYS=30
VERBOSE=false
DRY_RUN=false

# Encryption settings
GPG_RECIPIENT="${GPG_RECIPIENT:-}"
ENCRYPTION_PASSWORD="${ENCRYPTION_PASSWORD:-}"

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
    if [[ "$VERBOSE" == "true" ]]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') - $message" >> "${BACKUP_DIR}/backup.log"
    fi
}

# Function to print usage
print_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Options:"
    echo "  --type TYPE           Backup type: full, database, config, data (default: full)"
    echo "  --database-url URL    Database connection URL"
    echo "  --redis-url URL       Redis connection URL"
    echo "  --output-dir DIR      Backup output directory"
    echo "  --no-compress         Skip compression"
    echo "  --encrypt             Encrypt backup files"
    echo "  --gpg-recipient EMAIL GPG recipient for encryption"
    echo "  --password PASS       Password for encryption"
    echo "  --retention-days N    Keep backups for N days (default: 30)"
    echo "  --dry-run            Show what would be backed up"
    echo "  --verbose            Enable verbose logging"
    echo "  --help               Show this help message"
    echo
    echo "Backup Types:"
    echo "  full                 Complete backup (database + config + data)"
    echo "  database             Database only"
    echo "  config               Configuration files only"
    echo "  data                 Application data only"
    echo
    echo "Environment Variables:"
    echo "  DATABASE_URL         Database connection URL"
    echo "  REDIS_URL           Redis connection URL"
    echo "  BACKUP_ROOT         Root directory for backups"
    echo "  GPG_RECIPIENT       GPG recipient for encryption"
    echo "  ENCRYPTION_PASSWORD Password for encryption"
}

# Function to validate input parameters
validate_inputs() {
    # Validate backup type
    case "$BACKUP_TYPE" in
        full|database|config|data) ;;
        *) print_status $RED "Invalid backup type: $BACKUP_TYPE"; exit 1 ;;
    esac

    # Validate database URL format (basic check)
    if [[ -n "$DATABASE_URL" ]] && [[ ! "$DATABASE_URL" =~ ^postgres:// ]]; then
        print_status $YELLOW "Warning: DATABASE_URL does not start with postgres://"
    fi

    # Validate Redis URL format (basic check)
    if [[ -n "$REDIS_URL" ]] && [[ ! "$REDIS_URL" =~ ^redis:// ]]; then
        print_status $YELLOW "Warning: REDIS_URL does not start with redis://"
    fi

    # Validate retention days
    if ! [[ "$RETENTION_DAYS" =~ ^[0-9]+$ ]] || [[ "$RETENTION_DAYS" -lt 1 ]]; then
        print_status $RED "Invalid retention days: $RETENTION_DAYS"
        exit 1
    fi
}

# Function to check prerequisites
check_prerequisites() {
    print_status $BLUE "Checking prerequisites..."

    # Check required tools
    local missing_tools=()

    if ! command -v pg_dump >/dev/null 2>&1; then
        missing_tools+=("pg_dump")
    fi

    if [[ "$COMPRESS" == "true" ]] && ! command -v gzip >/dev/null 2>&1; then
        missing_tools+=("gzip")
    fi

    if [[ "$ENCRYPT" == "true" ]]; then
        if [[ -n "$GPG_RECIPIENT" ]] && ! command -v gpg >/dev/null 2>&1; then
            missing_tools+=("gpg")
        elif [[ -n "$ENCRYPTION_PASSWORD" ]] && ! command -v openssl >/dev/null 2>&1; then
            missing_tools+=("openssl")
        fi
    fi

    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        print_status $RED "Error: Missing required tools: ${missing_tools[*]}"
        exit 1
    fi

    # Create backup directory with secure permissions
    if [[ "$DRY_RUN" != "true" ]]; then
        mkdir -p "$BACKUP_DIR"
        chmod 700 "$BACKUP_DIR"  # Restrict access to owner only
        echo "Backup started at $(date)" > "${BACKUP_DIR}/backup.log"
        chmod 600 "${BACKUP_DIR}/backup.log"  # Secure log file
    fi

    print_status $GREEN "Prerequisites check passed"
}

# Function to test database connection
test_database_connection() {
    print_status $BLUE "Testing database connection..."
    
    if psql "$DATABASE_URL" -c "SELECT 1;" >/dev/null 2>&1; then
        print_status $GREEN "Database connection successful"
        return 0
    else
        print_status $RED "Database connection failed"
        return 1
    fi
}

# Function to backup database
backup_database() {
    print_status $BLUE "Backing up database..."
    
    local db_backup_file="${BACKUP_DIR}/database.sql"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status $YELLOW "DRY RUN: Would backup database to $db_backup_file"
        return 0
    fi
    
    # Create database backup
    if pg_dump "$DATABASE_URL" > "$db_backup_file"; then
        print_status $GREEN "Database backup created: $db_backup_file"
        
        # Get backup size
        local backup_size=$(du -h "$db_backup_file" | cut -f1)
        print_status $BLUE "Database backup size: $backup_size"
        
        # Compress if requested
        if [[ "$COMPRESS" == "true" ]]; then
            print_status $BLUE "Compressing database backup..."
            if gzip "$db_backup_file"; then
                print_status $GREEN "Database backup compressed: ${db_backup_file}.gz"
                db_backup_file="${db_backup_file}.gz"
            fi
        fi
        
        # Encrypt if requested
        if [[ "$ENCRYPT" == "true" ]]; then
            encrypt_file "$db_backup_file"
        fi
        
        return 0
    else
        print_status $RED "Database backup failed"
        return 1
    fi
}

# Function to backup Redis data
backup_redis() {
    print_status $BLUE "Backing up Redis data..."

    local redis_backup_file="${BACKUP_DIR}/redis.rdb"

    if [[ "$DRY_RUN" == "true" ]]; then
        print_status $YELLOW "DRY RUN: Would backup Redis to $redis_backup_file"
        return 0
    fi

    # Check if Redis is available
    if ! command -v redis-cli >/dev/null 2>&1; then
        print_status $YELLOW "redis-cli not available, skipping Redis backup"
        return 0
    fi

    # Extract Redis connection details
    local redis_host=$(echo "$REDIS_URL" | sed -n 's/redis:\/\/\([^:]*\).*/\1/p')
    local redis_port=$(echo "$REDIS_URL" | sed -n 's/redis:\/\/[^:]*:\([0-9]*\).*/\1/p')
    local redis_password=$(echo "$REDIS_URL" | sed -n 's/redis:\/\/[^:]*:[^@]*@\([^:]*\):.*/\1/p' || echo "")

    redis_host=${redis_host:-localhost}
    redis_port=${redis_port:-6379}

    # Test Redis connection
    local redis_cli_cmd="redis-cli -h $redis_host -p $redis_port"
    if [[ -n "$redis_password" ]]; then
        redis_cli_cmd="$redis_cli_cmd -a $redis_password"
    fi

    if ! $redis_cli_cmd PING >/dev/null 2>&1; then
        print_status $YELLOW "Cannot connect to Redis at $redis_host:$redis_port, skipping backup"
        return 0
    fi

    # Create Redis backup using BGSAVE
    if $redis_cli_cmd BGSAVE >/dev/null 2>&1; then
        # Wait for background save to complete with timeout
        local timeout=30
        local count=0
        local lastsave_before=$($redis_cli_cmd LASTSAVE 2>/dev/null || echo "0")

        while [[ $count -lt $timeout ]]; do
            local lastsave_current=$($redis_cli_cmd LASTSAVE 2>/dev/null || echo "0")
            if [[ "$lastsave_current" != "$lastsave_before" ]]; then
                break
            fi
            sleep 1
            ((count++))
        done

        if [[ $count -ge $timeout ]]; then
            print_status $YELLOW "Redis BGSAVE timeout, proceeding with current dump file"
        fi

        # Copy the RDB file
        local redis_data_dir="/var/lib/redis"
        if [[ -f "${redis_data_dir}/dump.rdb" ]]; then
            cp "${redis_data_dir}/dump.rdb" "$redis_backup_file"
            print_status $GREEN "Redis backup created: $redis_backup_file"

            # Compress if requested
            if [[ "$COMPRESS" == "true" ]]; then
                gzip "$redis_backup_file"
                print_status $GREEN "Redis backup compressed: ${redis_backup_file}.gz"
            fi

            # Encrypt if requested
            if [[ "$ENCRYPT" == "true" ]]; then
                encrypt_file "${redis_backup_file}.gz"
            fi
        else
            print_status $YELLOW "Redis RDB file not found at ${redis_data_dir}/dump.rdb, skipping"
        fi
    else
        print_status $YELLOW "Redis BGSAVE failed, skipping backup"
    fi
}

# Function to backup configuration files
backup_config() {
    print_status $BLUE "Backing up configuration files..."
    
    local config_backup_dir="${BACKUP_DIR}/config"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status $YELLOW "DRY RUN: Would backup config to $config_backup_dir"
        return 0
    fi
    
    mkdir -p "$config_backup_dir"
    
    # List of configuration files and directories to backup
    local config_items=(
        ".env"
        ".env.example"
        "Cargo.toml"
        "Cargo.lock"
        "monitoring/"
        "logging/"
        "deployment/"
        "scripts/"
    )
    
    for item in "${config_items[@]}"; do
        local source_path="${PROJECT_ROOT}/${item}"
        if [[ -e "$source_path" ]]; then
            if [[ -d "$source_path" ]]; then
                cp -r "$source_path" "$config_backup_dir/"
                print_status $GREEN "Backed up directory: $item"
            else
                cp "$source_path" "$config_backup_dir/"
                print_status $GREEN "Backed up file: $item"
            fi
        else
            print_status $YELLOW "Config item not found: $item"
        fi
    done
    
    # Create tar archive of config
    local config_archive="${BACKUP_DIR}/config.tar"
    if tar -cf "$config_archive" -C "$BACKUP_DIR" config/; then
        rm -rf "$config_backup_dir"
        print_status $GREEN "Configuration backup created: $config_archive"
        
        # Compress if requested
        if [[ "$COMPRESS" == "true" ]]; then
            gzip "$config_archive"
            print_status $GREEN "Configuration backup compressed: ${config_archive}.gz"
            config_archive="${config_archive}.gz"
        fi
        
        # Encrypt if requested
        if [[ "$ENCRYPT" == "true" ]]; then
            encrypt_file "$config_archive"
        fi
    fi
}

# Function to backup application data
backup_data() {
    print_status $BLUE "Backing up application data..."
    
    local data_backup_dir="${BACKUP_DIR}/data"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status $YELLOW "DRY RUN: Would backup data to $data_backup_dir"
        return 0
    fi
    
    mkdir -p "$data_backup_dir"
    
    # List of data directories to backup
    local data_items=(
        "logs/"
        "storage/"
        "reports/"
        "temp/"
    )
    
    for item in "${data_items[@]}"; do
        local source_path="${PROJECT_ROOT}/${item}"
        if [[ -d "$source_path" ]]; then
            cp -r "$source_path" "$data_backup_dir/"
            print_status $GREEN "Backed up data directory: $item"
        else
            print_status $YELLOW "Data directory not found: $item"
        fi
    done
    
    # Create tar archive of data
    local data_archive="${BACKUP_DIR}/data.tar"
    if tar -cf "$data_archive" -C "$BACKUP_DIR" data/; then
        rm -rf "$data_backup_dir"
        print_status $GREEN "Application data backup created: $data_archive"
        
        # Compress if requested
        if [[ "$COMPRESS" == "true" ]]; then
            gzip "$data_archive"
            print_status $GREEN "Application data backup compressed: ${data_archive}.gz"
            data_archive="${data_archive}.gz"
        fi
        
        # Encrypt if requested
        if [[ "$ENCRYPT" == "true" ]]; then
            encrypt_file "$data_archive"
        fi
    fi
}

# Function to encrypt a file
encrypt_file() {
    local file_path="$1"
    
    if [[ ! -f "$file_path" ]]; then
        print_status $RED "File not found for encryption: $file_path"
        return 1
    fi
    
    print_status $BLUE "Encrypting file: $(basename "$file_path")"
    
    if [[ -n "$GPG_RECIPIENT" ]]; then
        # Use GPG encryption
        if gpg --trust-model always --encrypt --recipient "$GPG_RECIPIENT" --output "${file_path}.gpg" "$file_path"; then
            rm "$file_path"
            print_status $GREEN "File encrypted with GPG: ${file_path}.gpg"
        else
            print_status $RED "GPG encryption failed"
            return 1
        fi
    elif [[ -n "$ENCRYPTION_PASSWORD" ]]; then
        # Use OpenSSL encryption
        if openssl enc -aes-256-cbc -salt -in "$file_path" -out "${file_path}.enc" -pass pass:"$ENCRYPTION_PASSWORD"; then
            rm "$file_path"
            print_status $GREEN "File encrypted with OpenSSL: ${file_path}.enc"
        else
            print_status $RED "OpenSSL encryption failed"
            return 1
        fi
    else
        print_status $YELLOW "No encryption method specified"
    fi
}

# Function to create backup manifest
create_manifest() {
    local manifest_file="${BACKUP_DIR}/manifest.txt"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        return 0
    fi
    
    print_status $BLUE "Creating backup manifest..."
    
    {
        echo "Infinitium Signal Backup Manifest"
        echo "=================================="
        echo "Backup Date: $(date)"
        echo "Backup Type: $BACKUP_TYPE"
        echo "Backup Directory: $BACKUP_DIR"
        echo "Compressed: $COMPRESS"
        echo "Encrypted: $ENCRYPT"
        echo ""
        echo "Files:"
        find "$BACKUP_DIR" -type f -not -name "manifest.txt" -not -name "backup.log" | sort
        echo ""
        echo "Total Size:"
        du -sh "$BACKUP_DIR"
    } > "$manifest_file"
    
    print_status $GREEN "Backup manifest created: $manifest_file"
}

# Function to cleanup old backups
cleanup_old_backups() {
    print_status $BLUE "Cleaning up old backups (older than $RETENTION_DAYS days)..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status $YELLOW "DRY RUN: Would cleanup backups older than $RETENTION_DAYS days"
        find "$BACKUP_ROOT" -maxdepth 1 -type d -mtime +$RETENTION_DAYS -name "20*" | head -10
        return 0
    fi
    
    local deleted_count=0
    while IFS= read -r -d '' backup_dir; do
        if [[ -d "$backup_dir" ]]; then
            print_status $YELLOW "Removing old backup: $(basename "$backup_dir")"
            rm -rf "$backup_dir"
            ((deleted_count++))
        fi
    done < <(find "$BACKUP_ROOT" -maxdepth 1 -type d -mtime +$RETENTION_DAYS -name "20*" -print0)
    
    if [[ $deleted_count -gt 0 ]]; then
        print_status $GREEN "Cleaned up $deleted_count old backups"
    else
        print_status $BLUE "No old backups to clean up"
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --type)
            BACKUP_TYPE="$2"
            shift 2
            ;;
        --database-url)
            DATABASE_URL="$2"
            shift 2
            ;;
        --redis-url)
            REDIS_URL="$2"
            shift 2
            ;;
        --output-dir)
            BACKUP_ROOT="$2"
            BACKUP_DIR="${BACKUP_ROOT}/${TIMESTAMP}"
            shift 2
            ;;
        --no-compress)
            COMPRESS=false
            shift
            ;;
        --encrypt)
            ENCRYPT=true
            shift
            ;;
        --gpg-recipient)
            GPG_RECIPIENT="$2"
            ENCRYPT=true
            shift 2
            ;;
        --password)
            ENCRYPTION_PASSWORD="$2"
            ENCRYPT=true
            shift 2
            ;;
        --retention-days)
            RETENTION_DAYS="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            print_usage
            exit 0
            ;;
        *)
            print_status $RED "Unknown option: $1"
            print_usage
            exit 1
            ;;
    esac
done

# Validate backup type
case "$BACKUP_TYPE" in
    full|database|config|data)
        ;;
    *)
        print_status $RED "Invalid backup type: $BACKUP_TYPE"
        print_usage
        exit 1
        ;;
esac

# Main execution
main() {
    local start_time=$(date +%s)

    print_status $BLUE "Infinitium Signal Backup Tool"
    print_status $BLUE "Backup Type: $BACKUP_TYPE"
    print_status $BLUE "Backup Directory: $BACKUP_DIR"

    validate_inputs
    check_prerequisites
    
    case "$BACKUP_TYPE" in
        full)
            test_database_connection && backup_database
            backup_redis
            backup_config
            backup_data
            ;;
        database)
            test_database_connection && backup_database
            backup_redis
            ;;
        config)
            backup_config
            ;;
        data)
            backup_data
            ;;
    esac
    
    create_manifest
    cleanup_old_backups
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [[ "$DRY_RUN" != "true" ]]; then
        local backup_size=$(du -sh "$BACKUP_DIR" | cut -f1)
        print_status $GREEN "Backup completed successfully!"
        print_status $GREEN "Backup size: $backup_size"
        print_status $GREEN "Duration: ${duration}s"
        print_status $GREEN "Location: $BACKUP_DIR"
    else
        print_status $BLUE "Dry run completed in ${duration}s"
    fi
}

# Run main function
main
