#!/bin/bash

# Infinitium Signal - Enterprise Cyber-Compliance Platform Demo Script
# This script demonstrates the complete functionality of the platform

set -euo pipefail
IFS=$'\n\t'

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Demo configuration
DEMO_DIR="demo-data"
API_BASE_URL="http://localhost:8080"
CLI_BINARY="./target/release/infinitum-signal-cli"

# Logging functions
log_header() {
    echo -e "\n${PURPLE}========================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}========================================${NC}\n"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_header "Checking Prerequisites"

    # Check if binary exists and is executable
    if [[ ! -f "$CLI_BINARY" ]]; then
        log_error "CLI binary not found at $CLI_BINARY. Please run 'make build' first."
        exit 1
    fi

    if [[ ! -x "$CLI_BINARY" ]]; then
        log_error "CLI binary is not executable. Please check permissions."
        exit 1
    fi

    # Check if demo data exists
    if [[ ! -d "$DEMO_DIR" ]]; then
        log_warning "Demo data directory not found. Creating sample data..."
        create_demo_data
    fi

    # Check required tools
    local missing_tools=()
    if ! command -v curl >/dev/null 2>&1; then
        missing_tools+=("curl")
    fi
    if ! command -v jq >/dev/null 2>&1; then
        missing_tools+=("jq")
    fi

    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_info "Please install them using your package manager"
        exit 1
    fi

    # Check if API server is running with timeout
    if ! timeout 10 curl -s --max-time 5 "$API_BASE_URL/health" > /dev/null 2>&1; then
        log_warning "API server not running at $API_BASE_URL. Starting server..."
        start_api_server
    else
        log_success "API server is running"
    fi

    log_success "Prerequisites check completed"
}

# Create demo data
create_demo_data() {
    log_step "Creating demo data structure"
    
    mkdir -p "$DEMO_DIR"/{sample_projects,sample_firmware,expected_reports}
    
    # Create sample Rust project
    mkdir -p "$DEMO_DIR/sample_projects/rust_web_app"
    cat > "$DEMO_DIR/sample_projects/rust_web_app/Cargo.toml" << 'EOF'
[package]
name = "sample-web-app"
version = "0.1.0"
edition = "2021"

[dependencies]
axum = "0.7"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tracing = "0.1"
uuid = { version = "1.0", features = ["v4"] }
# Intentionally vulnerable dependency for demo
openssl = "0.10.45"  # Known vulnerable version
EOF

    # Create sample Node.js project
    mkdir -p "$DEMO_DIR/sample_projects/node_express_api"
    cat > "$DEMO_DIR/sample_projects/node_express_api/package.json" << 'EOF'
{
  "name": "sample-express-api",
  "version": "1.0.0",
  "description": "Sample Express.js API for demo",
  "main": "index.js",
  "dependencies": {
    "express": "4.18.2",
    "lodash": "4.17.20",
    "moment": "2.29.1",
    "axios": "0.21.1"
  },
  "devDependencies": {
    "nodemon": "2.0.20"
  }
}
EOF

    # Create sample Python project
    mkdir -p "$DEMO_DIR/sample_projects/python_ml_service"
    cat > "$DEMO_DIR/sample_projects/python_ml_service/requirements.txt" << 'EOF'
flask==2.2.2
numpy==1.21.0
pandas==1.3.0
scikit-learn==1.0.2
requests==2.25.1
pillow==8.3.2
tensorflow==2.8.0
EOF

    # Create sample firmware files
    echo "Sample firmware binary data" > "$DEMO_DIR/sample_firmware/esp32_firmware.bin"
    echo "Arduino sketch binary" > "$DEMO_DIR/sample_firmware/arduino_sketch.elf"
    echo "Linux embedded image" > "$DEMO_DIR/sample_firmware/linux_embedded.img"
    
    log_success "Demo data created"
}

# Start API server
start_api_server() {
    log_step "Starting API server"
    
    # Start server in background
    $CLI_BINARY server --port 8080 --host 0.0.0.0 > /tmp/infinitum-signal-server.log 2>&1 &
    SERVER_PID=$!
    
    # Wait for server to start
    for i in {1..30}; do
        if curl -s "$API_BASE_URL/health" > /dev/null 2>&1; then
            log_success "API server started (PID: $SERVER_PID)"
            return 0
        fi
        sleep 1
    done
    
    log_error "Failed to start API server"
    exit 1
}

# Demo 1: SBOM Generation
demo_sbom_generation() {
    log_header "Demo 1: Software Bill of Materials (SBOM) Generation"
    
    log_step "Scanning Rust project"
    $CLI_BINARY scan sbom \
        --target "$DEMO_DIR/sample_projects/rust_web_app" \
        --output-format cyclonedx \
        --output-format spdx \
        --output-format json \
        --output-dir "./output/sbom"
    
    log_step "Scanning Node.js project"
    $CLI_BINARY scan sbom \
        --target "$DEMO_DIR/sample_projects/node_express_api" \
        --output-format cyclonedx \
        --output-dir "./output/sbom"
    
    log_step "Scanning Python project"
    $CLI_BINARY scan sbom \
        --target "$DEMO_DIR/sample_projects/python_ml_service" \
        --output-format spdx \
        --output-dir "./output/sbom"
    
    log_success "SBOM generation completed"
    log_info "Generated SBOMs available in ./output/sbom/"
}

# Demo 2: HBOM Generation
demo_hbom_generation() {
    log_header "Demo 2: Hardware Bill of Materials (HBOM) Generation"
    
    log_step "Scanning firmware binaries"
    $CLI_BINARY scan hbom \
        --target "$DEMO_DIR/sample_firmware" \
        --output-format json \
        --output-dir "./output/hbom" \
        --enable-security-analysis
    
    log_success "HBOM generation completed"
    log_info "Generated HBOMs available in ./output/hbom/"
}

# Demo 3: Vulnerability Assessment
demo_vulnerability_assessment() {
    log_header "Demo 3: Vulnerability Assessment"
    
    log_step "Running vulnerability scan on all projects"
    $CLI_BINARY vuln assess \
        --sbom-dir "./output/sbom" \
        --sources nvd,github,osv \
        --severity-threshold medium \
        --include-epss \
        --output-dir "./output/vulnerabilities"
    
    log_step "Generating vulnerability report"
    $CLI_BINARY vuln report \
        --input-dir "./output/vulnerabilities" \
        --format json \
        --format pdf \
        --output-dir "./output/reports"
    
    log_success "Vulnerability assessment completed"
    log_info "Vulnerability reports available in ./output/reports/"
}

# Demo 4: Compliance Report Generation
demo_compliance_reports() {
    log_header "Demo 4: Compliance Report Generation"
    
    log_step "Generating CERT-In compliance report"
    $CLI_BINARY compliance generate \
        --framework cert-in \
        --scan-results "./output/sbom,./output/hbom,./output/vulnerabilities" \
        --organization "Demo Corporation" \
        --output-format pdf \
        --output-format json \
        --output-dir "./output/compliance"
    
    log_step "Generating SEBI CSCRF compliance report"
    $CLI_BINARY compliance generate \
        --framework sebi \
        --scan-results "./output/sbom,./output/hbom,./output/vulnerabilities" \
        --organization "Demo Financial Services" \
        --output-format pdf \
        --output-dir "./output/compliance"
    
    log_step "Generating ISO 27001 compliance report"
    $CLI_BINARY compliance generate \
        --framework iso27001 \
        --scan-results "./output/sbom,./output/hbom,./output/vulnerabilities" \
        --organization "Demo Enterprise" \
        --output-format pdf \
        --output-dir "./output/compliance"
    
    log_success "Compliance reports generated"
    log_info "Compliance reports available in ./output/compliance/"
}

# Demo 5: Blockchain Audit Trail
demo_blockchain_audit() {
    log_header "Demo 5: Blockchain Audit Trail & Verifiable Credentials"
    
    log_step "Committing scan results to blockchain"
    $CLI_BINARY blockchain commit \
        --type scan-result \
        --data "./output/sbom" \
        --output-dir "./output/blockchain"
    
    log_step "Generating Merkle proofs"
    $CLI_BINARY blockchain proof \
        --data-dir "./output" \
        --output-dir "./output/blockchain/proofs"
    
    log_step "Issuing verifiable credentials"
    $CLI_BINARY blockchain credential \
        --type compliance \
        --subject "Demo Corporation IT Infrastructure" \
        --claims "./output/compliance" \
        --output-dir "./output/blockchain/credentials"
    
    log_success "Blockchain audit trail created"
    log_info "Blockchain records available in ./output/blockchain/"
}

# Demo 6: API Integration
demo_api_integration() {
    log_header "Demo 6: REST API Integration"
    
    log_step "Testing API health"
    curl -s "$API_BASE_URL/health" | jq '.'
    
    log_step "Uploading SBOM via API"
    curl -X POST "$API_BASE_URL/api/v1/sbom/upload" \
        -H "Content-Type: application/json" \
        -d @"./output/sbom/rust_web_app_sbom.json" | jq '.'
    
    log_step "Querying vulnerabilities via API"
    curl -s "$API_BASE_URL/api/v1/vulnerabilities?severity=high&limit=10" | jq '.'
    
    log_step "Generating compliance report via API"
    curl -X POST "$API_BASE_URL/api/v1/compliance/generate" \
        -H "Content-Type: application/json" \
        -d '{
            "framework": "cert-in",
            "organization": "API Demo Corp",
            "scan_results": ["./output/sbom", "./output/vulnerabilities"]
        }' | jq '.'
    
    log_success "API integration demo completed"
}

# Demo 7: Monitoring and Metrics
demo_monitoring() {
    log_header "Demo 7: Monitoring and Metrics"
    
    log_step "Checking system metrics"
    curl -s "$API_BASE_URL/metrics" | jq '.'
    
    log_step "Viewing Prometheus metrics"
    curl -s "$API_BASE_URL/metrics/prometheus"
    
    log_step "Database statistics"
    $CLI_BINARY db stats
    
    log_success "Monitoring demo completed"
}

# Demo 8: Security Features
demo_security_features() {
    log_header "Demo 8: Security Features Demonstration"
    
    log_step "Digital signature verification"
    $CLI_BINARY security verify \
        --file "./output/compliance/cert_in_report.pdf" \
        --signature "./output/blockchain/signatures/"
    
    log_step "Integrity checking"
    $CLI_BINARY security integrity \
        --directory "./output" \
        --generate-checksums
    
    log_step "Encryption demonstration"
    $CLI_BINARY security encrypt \
        --file "./output/compliance/sebi_report.pdf" \
        --output "./output/encrypted/"
    
    log_success "Security features demo completed"
}

# Generate demo report
generate_demo_report() {
    log_header "Generating Demo Summary Report"
    
    cat > "./output/demo_summary.md" << EOF
# Infinitium Signal Platform Demo Summary

**Demo Date:** $(date)
**Platform Version:** $(cat Cargo.toml | grep version | head -1 | cut -d'"' -f2)

## Demo Results

### 1. SBOM Generation
- ✅ Rust project scanned (CycloneDX, SPDX, JSON formats)
- ✅ Node.js project scanned (CycloneDX format)
- ✅ Python project scanned (SPDX format)

### 2. HBOM Generation
- ✅ Firmware binaries analyzed
- ✅ Security analysis performed
- ✅ Hardware components identified

### 3. Vulnerability Assessment
- ✅ Multi-source vulnerability scanning (NVD, GitHub, OSV)
- ✅ EPSS scoring included
- ✅ Risk assessment completed

### 4. Compliance Reports
- ✅ CERT-In compliance report generated
- ✅ SEBI CSCRF compliance report generated
- ✅ ISO 27001 compliance report generated

### 5. Blockchain Audit Trail
- ✅ Scan results committed to blockchain
- ✅ Merkle proofs generated
- ✅ Verifiable credentials issued

### 6. API Integration
- ✅ REST API endpoints tested
- ✅ SBOM upload functionality verified
- ✅ Vulnerability querying tested

### 7. Monitoring & Metrics
- ✅ System metrics collected
- ✅ Prometheus integration verified
- ✅ Database statistics generated

### 8. Security Features
- ✅ Digital signatures verified
- ✅ Integrity checking performed
- ✅ Encryption capabilities demonstrated

## Generated Artifacts

\`\`\`
output/
├── sbom/                    # Software Bills of Materials
├── hbom/                    # Hardware Bills of Materials
├── vulnerabilities/         # Vulnerability assessments
├── compliance/              # Compliance reports (PDF, JSON)
├── blockchain/              # Blockchain records and proofs
├── reports/                 # Summary reports
└── encrypted/               # Encrypted sensitive data
\`\`\`

## Next Steps

1. Review generated compliance reports
2. Integrate with existing CI/CD pipelines
3. Configure automated scanning schedules
4. Set up monitoring dashboards
5. Implement custom compliance frameworks

---
*Generated by Infinitium Signal Enterprise Cyber-Compliance Platform*
EOF

    log_success "Demo summary report generated: ./output/demo_summary.md"
}

# Cleanup function
cleanup() {
    log_header "Demo Cleanup"
    
    if [[ -n "${SERVER_PID:-}" ]]; then
        log_step "Stopping API server (PID: $SERVER_PID)"
        kill $SERVER_PID 2>/dev/null || true
    fi
    
    log_info "Demo artifacts preserved in ./output/ directory"
    log_success "Demo cleanup completed"
}

# Main demo execution
main() {
    log_header "Infinitium Signal Enterprise Cyber-Compliance Platform Demo"
    log_info "This demo showcases the complete functionality of the platform"
    
    # Set up cleanup trap
    trap cleanup EXIT
    
    # Create output directory
    mkdir -p output/{sbom,hbom,vulnerabilities,compliance,blockchain,reports,encrypted}
    
    # Run demo steps
    check_prerequisites
    demo_sbom_generation
    demo_hbom_generation
    demo_vulnerability_assessment
    demo_compliance_reports
    demo_blockchain_audit
    demo_api_integration
    demo_monitoring
    demo_security_features
    generate_demo_report
    
    log_header "Demo Completed Successfully!"
    log_success "All platform features demonstrated"
    log_info "Check ./output/ directory for generated artifacts"
    log_info "View demo summary: ./output/demo_summary.md"
    
    # Open demo summary if possible
    if command -v xdg-open &> /dev/null; then
        xdg-open "./output/demo_summary.md" 2>/dev/null || true
    elif command -v open &> /dev/null; then
        open "./output/demo_summary.md" 2>/dev/null || true
    fi
}

# Run main function
main "$@"
