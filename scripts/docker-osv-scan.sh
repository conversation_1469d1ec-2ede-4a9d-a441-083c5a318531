#!/bin/bash

# Docker-based OSV Scan Automation Script
# Runs OSV vulnerability scans using Docker containers

set -euo pipefail
IFS=$'\n\t'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
DOCKER_DIR="${PROJECT_ROOT}/docker"
RESULTS_DIR="${PROJECT_ROOT}/docker_scan_results"

# Default configuration
: "${DOCKER_IMAGE:=infinitum-osv-scanner}"
: "${SCAN_TARGET:=${PROJECT_ROOT}}"
: "${SCAN_FORMAT:=json}"
: "${CONTAINER_NAME:=osv-scan-$(date +%s)}"
: "${DOCKER_NETWORK:=osv-network}"
: "${TIMEOUT:=1800}"
: "${MEMORY_LIMIT:=2g}"
: "${CPU_LIMIT:=1.0}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "${LOG_FILE}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a "${LOG_FILE}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "${LOG_FILE}"
}

# Setup function
setup() {
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    SCAN_ID="docker_scan_${timestamp}"

    # Create directories
    mkdir -p "${RESULTS_DIR}/${SCAN_ID}"
    mkdir -p "${RESULTS_DIR}/logs"

    # Set log file
    LOG_FILE="${RESULTS_DIR}/logs/docker_scan_${timestamp}.log"

    log "Starting Docker-based OSV scan"
    log "Scan ID: ${SCAN_ID}"
    log "Target: ${SCAN_TARGET}"
    log "Results directory: ${RESULTS_DIR}/${SCAN_ID}"
}

# Check Docker availability
check_docker() {
    log "Checking Docker availability..."

    if ! command -v docker &> /dev/null; then
        error "Docker is not installed or not in PATH"
        exit 1
    fi

    if ! docker info &> /dev/null; then
        error "Docker daemon is not running or not accessible"
        exit 1
    fi

    log "Docker is available"
}

# Build Docker image
build_image() {
    log "Building OSV scanner Docker image..."

    if ! docker build -f "${DOCKER_DIR}/Dockerfile.osv" -t "${DOCKER_IMAGE}" "${PROJECT_ROOT}" >> "${LOG_FILE}" 2>&1; then
        error "Failed to build Docker image"
        exit 1
    fi

    log "Docker image built successfully: ${DOCKER_IMAGE}"
}

# Create Docker network
create_network() {
    log "Setting up Docker network..."

    if ! docker network ls | grep -q "${DOCKER_NETWORK}"; then
        if ! docker network create "${DOCKER_NETWORK}" >> "${LOG_FILE}" 2>&1; then
            warn "Failed to create Docker network, using default"
            DOCKER_NETWORK="bridge"
        fi
    fi

    log "Using Docker network: ${DOCKER_NETWORK}"
}

# Run OSV scan in container
run_scan() {
    log "Running OSV scan in Docker container..."

    # Prepare volumes
    local volumes=""
    volumes="${volumes} -v ${SCAN_TARGET}:/scan-target:ro"
    volumes="${volumes} -v ${RESULTS_DIR}/${SCAN_ID}:/app/results"

    # Prepare environment variables
    local env_vars=""
    env_vars="${env_vars} -e SCAN_FORMAT=${SCAN_FORMAT}"
    env_vars="${env_vars} -e RUST_LOG=info"

    # Prepare resource limits
    local resource_limits=""
    resource_limits="${resource_limits} --memory=${MEMORY_LIMIT}"
    resource_limits="${resource_limits} --cpus=${CPU_LIMIT}"

    # Build Docker run command
    local docker_cmd="docker run --rm"
    docker_cmd="${docker_cmd} --name ${CONTAINER_NAME}"
    docker_cmd="${docker_cmd} --network ${DOCKER_NETWORK}"
    docker_cmd="${docker_cmd} ${volumes}"
    docker_cmd="${docker_cmd} ${env_vars}"
    docker_cmd="${docker_cmd} ${resource_limits}"
    docker_cmd="${docker_cmd} --timeout ${TIMEOUT}s"
    docker_cmd="${docker_cmd} ${DOCKER_IMAGE}"
    docker_cmd="${docker_cmd} scan /scan-target --format ${SCAN_FORMAT}"

    log "Executing: ${docker_cmd}"

    # Run the scan
    if eval "${docker_cmd}" > "${RESULTS_DIR}/${SCAN_ID}/scan_output.log" 2>&1; then
        log "OSV scan completed successfully"

        # Copy results from container
        if [ -f "${RESULTS_DIR}/${SCAN_ID}/osv_scan_results.${SCAN_FORMAT}" ]; then
            cp "${RESULTS_DIR}/${SCAN_ID}/osv_scan_results.${SCAN_FORMAT}" "${RESULTS_DIR}/${SCAN_ID}/"
        fi

        return 0
    else
        error "OSV scan failed"
        return 1
    fi
}

# Analyze results
analyze_results() {
    log "Analyzing scan results..."

    local results_file="${RESULTS_DIR}/${SCAN_ID}/osv_scan_results.${SCAN_FORMAT}"

    if [ ! -f "$results_file" ]; then
        error "Scan results file not found: $results_file"
        return 1
    fi

    # Parse results based on format
    if [ "$SCAN_FORMAT" = "json" ]; then
        VULN_COUNT=$(jq '.vulnerabilities | length' "$results_file" 2>/dev/null || echo "0")
        HIGH_SEVERITY=$(jq '[.vulnerabilities[] | select(.severity == "HIGH" or (.cvss_score // 0) >= 7.0)] | length' "$results_file" 2>/dev/null || echo "0")
        MEDIUM_SEVERITY=$(jq '[.vulnerabilities[] | select(.severity == "MEDIUM" or ((.cvss_score // 0) >= 4.0 and (.cvss_score // 0) < 7.0))] | length' "$results_file" 2>/dev/null || echo "0")
        SCAN_DURATION=$(jq '.scan_duration // 0' "$results_file" 2>/dev/null || echo "0")
    else
        # For other formats, we can't easily parse
        VULN_COUNT="unknown"
        HIGH_SEVERITY="unknown"
        MEDIUM_SEVERITY="unknown"
        SCAN_DURATION="unknown"
    fi

    log "Scan Results Summary:"
    log "  Total vulnerabilities: ${VULN_COUNT}"
    log "  High severity: ${HIGH_SEVERITY}"
    log "  Medium severity: ${MEDIUM_SEVERITY}"
    log "  Scan duration: ${SCAN_DURATION}ms"

    # Create summary file
    cat > "${RESULTS_DIR}/${SCAN_ID}/scan_summary.txt" << EOF
OSV Docker Scan Summary
========================
Scan ID: ${SCAN_ID}
Timestamp: $(date)
Target: ${SCAN_TARGET}
Format: ${SCAN_FORMAT}
Container: ${CONTAINER_NAME}

Results:
- Total vulnerabilities: ${VULN_COUNT}
- High severity: ${HIGH_SEVERITY}
- Medium severity: ${MEDIUM_SEVERITY}
- Scan duration: ${SCAN_DURATION}ms

Docker Configuration:
- Image: ${DOCKER_IMAGE}
- Network: ${DOCKER_NETWORK}
- Memory limit: ${MEMORY_LIMIT}
- CPU limit: ${CPU_LIMIT}
- Timeout: ${TIMEOUT}s
EOF
}

# Generate HTML report
generate_html_report() {
    log "Generating HTML report..."

    local html_file="${RESULTS_DIR}/${SCAN_ID}/scan_report.html"
    local results_file="${RESULTS_DIR}/${SCAN_ID}/osv_scan_results.${SCAN_FORMAT}"

    cat > "$html_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>OSV Docker Scan Report - ${SCAN_ID}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .value { font-size: 2em; font-weight: bold; color: #667eea; }
        .docker-info { background: #e9ecef; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .footer { margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: center; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 OSV Docker Scan Report</h1>
            <p>Scan ID: ${SCAN_ID}</p>
            <p>Generated: $(date)</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <h3>Total Vulnerabilities</h3>
                <div class="value">${VULN_COUNT}</div>
            </div>
            <div class="summary-card">
                <h3>High Severity</h3>
                <div class="value" style="color: #dc3545;">${HIGH_SEVERITY}</div>
            </div>
            <div class="summary-card">
                <h3>Medium Severity</h3>
                <div class="value" style="color: #ffc107;">${MEDIUM_SEVERITY}</div>
            </div>
            <div class="summary-card">
                <h3>Scan Duration</h3>
                <div class="value">${SCAN_DURATION}ms</div>
            </div>
        </div>

        <div class="docker-info">
            <h2>🐳 Docker Configuration</h2>
            <p><strong>Image:</strong> ${DOCKER_IMAGE}</p>
            <p><strong>Container:</strong> ${CONTAINER_NAME}</p>
            <p><strong>Network:</strong> ${DOCKER_NETWORK}</p>
            <p><strong>Memory Limit:</strong> ${MEMORY_LIMIT}</p>
            <p><strong>CPU Limit:</strong> ${CPU_LIMIT}</p>
            <p><strong>Timeout:</strong> ${TIMEOUT}s</p>
            <p><strong>Target:</strong> ${SCAN_TARGET}</p>
        </div>
EOF

    # Add vulnerability details if we have JSON results
    if [ "$SCAN_FORMAT" = "json" ] && [ -f "$results_file" ]; then
        echo '<h2>🚨 Vulnerabilities Found</h2>' >> "$html_file"
        echo '<div class="vulnerabilities">' >> "$html_file"

        jq -r '.vulnerabilities[] | "<div class=\"vulnerability\"><h3>\(.id)</h3><p><strong>Severity:</strong> \(.severity // "Unknown")</p><p><strong>CVSS Score:</strong> \(.cvss_score // "N/A")</p><p><strong>Package:</strong> \(.package.name)@\(.package.version)</p><p><strong>Summary:</strong> \(.summary)</p></div>"' "$results_file" >> "$html_file" 2>/dev/null || true

        echo '</div>' >> "$html_file"
    fi

    cat >> "$html_file" << EOF
        <div class="footer">
            <p>Report generated by Docker OSV Scan Automation Script</p>
            <p>Infinitium Signal - Enterprise Cyber-Compliance Platform</p>
        </div>
    </div>
</body>
</html>
EOF

    log "HTML report generated: $html_file"
}

# Cleanup function
cleanup() {
    log "Cleaning up Docker resources..."

    # Remove container if it still exists
    if docker ps -a | grep -q "${CONTAINER_NAME}"; then
        docker rm -f "${CONTAINER_NAME}" >> "${LOG_FILE}" 2>&1 || true
    fi

    # Remove dangling images
    docker image prune -f >> "${LOG_FILE}" 2>&1 || true
}

# Main execution
main() {
    setup
    check_docker
    build_image
    create_network

    if run_scan; then
        analyze_results
        generate_html_report
        log "Docker-based OSV scan completed successfully"
        log "Results: ${RESULTS_DIR}/${SCAN_ID}"
        exit 0
    else
        error "Docker-based OSV scan failed"
        cleanup
        exit 1
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --image)
            DOCKER_IMAGE="$2"
            shift 2
            ;;
        --target)
            SCAN_TARGET="$2"
            shift 2
            ;;
        --format)
            SCAN_FORMAT="$2"
            shift 2
            ;;
        --memory)
            MEMORY_LIMIT="$2"
            shift 2
            ;;
        --cpu)
            CPU_LIMIT="$2"
            shift 2
            ;;
        --timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        --network)
            DOCKER_NETWORK="$2"
            shift 2
            ;;
        --help)
            echo "Docker OSV Scan Automation Script"
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --image IMAGE       Docker image to use (default: infinitum-osv-scanner)"
            echo "  --target PATH       Path to scan (default: project root)"
            echo "  --format FORMAT     Output format (json, default: json)"
            echo "  --memory LIMIT      Memory limit (default: 2g)"
            echo "  --cpu LIMIT         CPU limit (default: 1.0)"
            echo "  --timeout SECONDS   Scan timeout in seconds (default: 1800)"
            echo "  --network NETWORK   Docker network to use (default: osv-network)"
            echo "  --help              Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 --target /path/to/project --memory 4g --cpu 2.0"
            echo "  $0 --image my-custom-scanner --format json"
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Trap for cleanup on exit
trap cleanup EXIT INT TERM

# Run main function
main