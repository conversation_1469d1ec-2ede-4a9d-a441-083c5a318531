#!/bin/bash

# Infinitium Signal Load Testing Script
# Comprehensive API load testing with concurrent users and stress scenarios

set -euo pipefail
IFS=$'\n\t'

# Configuration
API_BASE_URL=${API_BASE_URL:-"http://localhost:8080"}
CONCURRENT_USERS=${CONCURRENT_USERS:-10}
TEST_DURATION=${TEST_DURATION:-60}
RAMP_UP_TIME=${RAMP_UP_TIME:-10}
OUTPUT_DIR="./load_test_results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RESULTS_DIR="${OUTPUT_DIR}/${TIMESTAMP}"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Setup test environment
setup_load_testing() {
    log "Setting up load testing environment..."
    mkdir -p "$RESULTS_DIR"
    
    # Check if API is accessible
    if ! curl -s "$API_BASE_URL/health" > /dev/null; then
        error "API not accessible at $API_BASE_URL"
        error "Please ensure Infinitium Signal is running"
        exit 1
    fi
    
    log "API is accessible at $API_BASE_URL"
    log "Results will be saved to: $RESULTS_DIR"
}

# Generate test data
generate_test_data() {
    log "Generating test data..."
    
    # Create test SBOM
    cat > "$RESULTS_DIR/test_sbom.json" << 'EOF'
{
  "bomFormat": "CycloneDX",
  "specVersion": "1.4",
  "serialNumber": "urn:uuid:test-load-test-sbom",
  "version": 1,
  "metadata": {
    "timestamp": "2024-01-01T00:00:00Z",
    "tools": [
      {
        "vendor": "Infinitium Signal",
        "name": "Load Test Generator",
        "version": "1.0.0"
      }
    ]
  },
  "components": [
    {
      "type": "library",
      "bom-ref": "pkg:cargo/serde@1.0.0",
      "name": "serde",
      "version": "1.0.0",
      "purl": "pkg:cargo/serde@1.0.0"
    },
    {
      "type": "library",
      "bom-ref": "pkg:cargo/tokio@1.0.0",
      "name": "tokio",
      "version": "1.0.0",
      "purl": "pkg:cargo/tokio@1.0.0"
    }
  ]
}
EOF

    # Create test scan request
    cat > "$RESULTS_DIR/test_scan_request.json" << 'EOF'
{
  "project_path": "/tmp/test-project",
  "scan_type": "sbom",
  "output_format": "cyclonedx"
}
EOF

    # Create test compliance request
    cat > "$RESULTS_DIR/test_compliance_request.json" << 'EOF'
{
  "framework": "cert-in",
  "organization": "Load Test Corp",
  "scan_results": ["test-scan-id"]
}
EOF
}

# Basic API health check load test
load_test_health_check() {
    log "Running health check load test..."
    
    local output_file="$RESULTS_DIR/health_check_results.txt"
    
    {
        echo "=== HEALTH CHECK LOAD TEST ==="
        echo "Concurrent Users: $CONCURRENT_USERS"
        echo "Duration: $TEST_DURATION seconds"
        echo "Start Time: $(date)"
        echo ""
    } > "$output_file"
    
    # Use Apache Bench for simple load testing
    if command -v ab &> /dev/null; then
        ab -n $((CONCURRENT_USERS * 10)) -c "$CONCURRENT_USERS" \
           "$API_BASE_URL/health" >> "$output_file" 2>&1
    else
        # Fallback to curl-based testing
        log "Apache Bench not found, using curl-based testing"
        curl_load_test_health_check "$output_file"
    fi
}

# Curl-based load testing fallback
curl_load_test_health_check() {
    local output_file="$1"
    local total_requests=$((CONCURRENT_USERS * 10))
    local success_count=0
    local error_count=0
    local total_time=0
    
    echo "Using curl for load testing..." >> "$output_file"
    
    for ((i=1; i<=total_requests; i++)); do
        start_time=$(date +%s.%N)
        
        if curl -s -o /dev/null -w "%{http_code}" "$API_BASE_URL/health" | grep -q "200"; then
            ((success_count++))
        else
            ((error_count++))
        fi
        
        end_time=$(date +%s.%N)
        request_time=$(echo "$end_time - $start_time" | bc -l)
        total_time=$(echo "$total_time + $request_time" | bc -l)
        
        # Add small delay to simulate concurrent users
        if ((i % CONCURRENT_USERS == 0)); then
            sleep 0.1
        fi
    done
    
    # Calculate average time using bash arithmetic
    if [[ $total_requests -gt 0 ]]; then
        avg_time=$(( total_time * 1000 / total_requests ))  # Convert to milliseconds for precision
        avg_time_seconds=$(( avg_time / 1000 )).$(( (avg_time % 1000) / 100 ))
        success_rate=$(( success_count * 100 / total_requests ))
    else
        avg_time_seconds="0.000"
        success_rate=0
    fi

    {
        echo ""
        echo "=== RESULTS ==="
        echo "Total Requests: $total_requests"
        echo "Successful Requests: $success_count"
        echo "Failed Requests: $error_count"
        echo "Average Response Time: ${avg_time_seconds}s"
        echo "Success Rate: ${success_rate}%"
    } >> "$output_file"
}

# SBOM upload load test
load_test_sbom_upload() {
    log "Running SBOM upload load test..."
    
    local output_file="$RESULTS_DIR/sbom_upload_results.txt"
    local success_count=0
    local error_count=0
    
    {
        echo "=== SBOM UPLOAD LOAD TEST ==="
        echo "Concurrent Users: $CONCURRENT_USERS"
        echo "Duration: $TEST_DURATION seconds"
        echo "Start Time: $(date)"
        echo ""
    } > "$output_file"
    
    local end_time=$(($(date +%s) + TEST_DURATION))
    
    while [ $(date +%s) -lt $end_time ]; do
        for ((i=1; i<=CONCURRENT_USERS; i++)); do
            {
                start_time=$(date +%s.%N)
                
                response=$(curl -s -w "%{http_code}" -X POST \
                    "$API_BASE_URL/api/v1/sbom/upload" \
                    -H "Content-Type: application/json" \
                    -d @"$RESULTS_DIR/test_sbom.json" \
                    -o /dev/null)
                
                end_time_req=$(date +%s.%N)
                request_time=$(echo "$end_time_req - $start_time" | bc -l)
                
                if [[ "$response" =~ ^2[0-9][0-9]$ ]]; then
                    echo "$(date +%s.%N),SUCCESS,$request_time,$response" >> "$RESULTS_DIR/sbom_upload_times.csv"
                    ((success_count++))
                else
                    echo "$(date +%s.%N),ERROR,$request_time,$response" >> "$RESULTS_DIR/sbom_upload_times.csv"
                    ((error_count++))
                fi
            } &
        done
        
        # Wait for batch to complete
        wait
        sleep 1
    done
    
    {
        echo ""
        echo "=== SBOM UPLOAD RESULTS ==="
        echo "Successful Uploads: $success_count"
        echo "Failed Uploads: $error_count"
        echo "Total Requests: $((success_count + error_count))"
        echo "Success Rate: $(echo "scale=2; $success_count * 100 / ($success_count + $error_count)" | bc -l)%"
    } >> "$output_file"
}

# Vulnerability query load test
load_test_vulnerability_queries() {
    log "Running vulnerability query load test..."
    
    local output_file="$RESULTS_DIR/vulnerability_query_results.txt"
    local success_count=0
    local error_count=0
    
    {
        echo "=== VULNERABILITY QUERY LOAD TEST ==="
        echo "Concurrent Users: $CONCURRENT_USERS"
        echo "Duration: $TEST_DURATION seconds"
        echo "Start Time: $(date)"
        echo ""
    } > "$output_file"
    
    local end_time=$(($(date +%s) + TEST_DURATION))
    
    while [ $(date +%s) -lt $end_time ]; do
        for ((i=1; i<=CONCURRENT_USERS; i++)); do
            {
                start_time=$(date +%s.%N)
                
                # Test different query parameters
                local severity_levels=("low" "medium" "high" "critical")
                local severity=${severity_levels[$((RANDOM % 4))]}
                local limit=$((RANDOM % 50 + 10))
                
                response=$(curl -s -w "%{http_code}" \
                    "$API_BASE_URL/api/v1/vulnerabilities?severity=$severity&limit=$limit" \
                    -o /dev/null)
                
                end_time_req=$(date +%s.%N)
                request_time=$(echo "$end_time_req - $start_time" | bc -l)
                
                if [[ "$response" =~ ^2[0-9][0-9]$ ]]; then
                    echo "$(date +%s.%N),SUCCESS,$request_time,$response,$severity,$limit" >> "$RESULTS_DIR/vuln_query_times.csv"
                    ((success_count++))
                else
                    echo "$(date +%s.%N),ERROR,$request_time,$response,$severity,$limit" >> "$RESULTS_DIR/vuln_query_times.csv"
                    ((error_count++))
                fi
            } &
        done
        
        wait
        sleep 1
    done
    
    {
        echo ""
        echo "=== VULNERABILITY QUERY RESULTS ==="
        echo "Successful Queries: $success_count"
        echo "Failed Queries: $error_count"
        echo "Total Requests: $((success_count + error_count))"
        echo "Success Rate: $(echo "scale=2; $success_count * 100 / ($success_count + $error_count)" | bc -l)%"
    } >> "$output_file"
}

# Compliance report generation load test
load_test_compliance_reports() {
    log "Running compliance report generation load test..."
    
    local output_file="$RESULTS_DIR/compliance_report_results.txt"
    local success_count=0
    local error_count=0
    
    {
        echo "=== COMPLIANCE REPORT LOAD TEST ==="
        echo "Concurrent Users: $CONCURRENT_USERS"
        echo "Duration: $TEST_DURATION seconds"
        echo "Start Time: $(date)"
        echo ""
    } > "$output_file"
    
    # Reduced concurrency for resource-intensive operations
    local compliance_concurrency=$((CONCURRENT_USERS / 2))
    if [ $compliance_concurrency -lt 1 ]; then
        compliance_concurrency=1
    fi
    
    local end_time=$(($(date +%s) + TEST_DURATION))
    
    while [ $(date +%s) -lt $end_time ]; do
        for ((i=1; i<=compliance_concurrency; i++)); do
            {
                start_time=$(date +%s.%N)
                
                response=$(curl -s -w "%{http_code}" -X POST \
                    "$API_BASE_URL/api/v1/compliance/generate" \
                    -H "Content-Type: application/json" \
                    -d @"$RESULTS_DIR/test_compliance_request.json" \
                    -o /dev/null)
                
                end_time_req=$(date +%s.%N)
                request_time=$(echo "$end_time_req - $start_time" | bc -l)
                
                if [[ "$response" =~ ^2[0-9][0-9]$ ]]; then
                    echo "$(date +%s.%N),SUCCESS,$request_time,$response" >> "$RESULTS_DIR/compliance_times.csv"
                    ((success_count++))
                else
                    echo "$(date +%s.%N),ERROR,$request_time,$response" >> "$RESULTS_DIR/compliance_times.csv"
                    ((error_count++))
                fi
            } &
        done
        
        wait
        sleep 2  # Longer delay for compliance reports
    done
    
    {
        echo ""
        echo "=== COMPLIANCE REPORT RESULTS ==="
        echo "Successful Reports: $success_count"
        echo "Failed Reports: $error_count"
        echo "Total Requests: $((success_count + error_count))"
        echo "Success Rate: $(echo "scale=2; $success_count * 100 / ($success_count + $error_count)" | bc -l)%"
    } >> "$output_file"
}

# Stress test with increasing load
stress_test() {
    log "Running stress test with increasing load..."
    
    local output_file="$RESULTS_DIR/stress_test_results.txt"
    
    {
        echo "=== STRESS TEST ==="
        echo "Starting with 1 user, increasing to $((CONCURRENT_USERS * 2))"
        echo "Start Time: $(date)"
        echo ""
    } > "$output_file"
    
    for ((users=1; users<=CONCURRENT_USERS*2; users+=2)); do
        log "Testing with $users concurrent users..."
        
        local success_count=0
        local error_count=0
        local test_duration=30  # Shorter duration for stress test
        
        echo "=== Testing with $users users ===" >> "$output_file"
        
        local end_time=$(($(date +%s) + test_duration))
        
        while [ $(date +%s) -lt $end_time ]; do
            for ((i=1; i<=users; i++)); do
                {
                    response=$(curl -s -w "%{http_code}" "$API_BASE_URL/health" -o /dev/null)
                    if [[ "$response" =~ ^2[0-9][0-9]$ ]]; then
                        ((success_count++))
                    else
                        ((error_count++))
                    fi
                } &
            done
            wait
            sleep 0.5
        done
        
        local total_requests=$((success_count + error_count))
        local success_rate=0
        if [ $total_requests -gt 0 ]; then
            success_rate=$(( success_count * 100 / total_requests ))
        fi
        
        {
            echo "Users: $users"
            echo "Total Requests: $total_requests"
            echo "Successful: $success_count"
            echo "Failed: $error_count"
            echo "Success Rate: ${success_rate}%"
            echo ""
        } >> "$output_file"
        
        # Break if success rate drops below 90%
        if (( $(echo "$success_rate < 90" | bc -l) )); then
            warn "Success rate dropped below 90% at $users users"
            echo "BREAKING: Success rate dropped below 90% at $users users" >> "$output_file"
            break
        fi
    done
}

# Generate summary report
generate_summary() {
    log "Generating load test summary..."
    
    local summary_file="$RESULTS_DIR/load_test_summary.txt"
    
    {
        echo "========================================"
        echo "INFINITIUM SIGNAL LOAD TEST SUMMARY"
        echo "========================================"
        echo ""
        echo "Test Configuration:"
        echo "  API Base URL: $API_BASE_URL"
        echo "  Concurrent Users: $CONCURRENT_USERS"
        echo "  Test Duration: $TEST_DURATION seconds"
        echo "  Test Date: $(date)"
        echo ""
        echo "Test Results Files:"
        echo "  - health_check_results.txt"
        echo "  - sbom_upload_results.txt"
        echo "  - vulnerability_query_results.txt"
        echo "  - compliance_report_results.txt"
        echo "  - stress_test_results.txt"
        echo ""
        echo "CSV Data Files:"
        echo "  - sbom_upload_times.csv"
        echo "  - vuln_query_times.csv"
        echo "  - compliance_times.csv"
        echo ""
        echo "========================================"
    } > "$summary_file"
    
    log "Load test summary saved to: $summary_file"
}

# Main execution
main() {
    log "Starting Infinitium Signal Load Testing"
    log "Configuration: $CONCURRENT_USERS users, ${TEST_DURATION}s duration"
    
    setup_load_testing
    generate_test_data
    
    # Run all load tests
    load_test_health_check
    load_test_sbom_upload
    load_test_vulnerability_queries
    load_test_compliance_reports
    stress_test
    
    generate_summary
    
    log "Load testing completed. Results saved to: $RESULTS_DIR"
}

# Check dependencies
check_dependencies() {
    if ! command -v curl &> /dev/null; then
        error "curl is required but not installed"
        exit 1
    fi
    
    if ! command -v bc &> /dev/null; then
        warn "bc not found, some calculations may not work"
    fi
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    check_dependencies
    main "$@"
fi
