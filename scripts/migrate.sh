#!/bin/bash

# Database migration script for Infinitium Signal
# This script handles database schema migrations and data migrations

set -euo pipefail
IFS=$'\n\t'

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
MIGRATION_LOG="${PROJECT_ROOT}/logs/migration.log"

# Default values
COMMAND=""
DATABASE_URL="${DATABASE_URL:-postgres://infinitium:password@localhost:5432/infinitium_signal}"
DRY_RUN=false
FORCE=false
BACKUP_BEFORE_MIGRATION=true
VERBOSE=false

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
    if [[ "$VERBOSE" == "true" ]]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') - $message" >> "$MIGRATION_LOG"
    fi
}

# Function to print usage
print_usage() {
    echo "Usage: $0 COMMAND [OPTIONS]"
    echo
    echo "Commands:"
    echo "  up              Run all pending migrations"
    echo "  down            Rollback the last migration"
    echo "  reset           Reset database (drop all tables and re-run migrations)"
    echo "  status          Show migration status"
    echo "  create NAME     Create a new migration file"
    echo "  validate        Validate migration files"
    echo
    echo "Options:"
    echo "  --database-url URL    Database connection URL"
    echo "  --dry-run            Show what would be done without executing"
    echo "  --force              Force operation without confirmation"
    echo "  --no-backup          Skip backup before migration"
    echo "  --verbose            Enable verbose logging"
    echo "  --help               Show this help message"
    echo
    echo "Environment Variables:"
    echo "  DATABASE_URL         Database connection URL"
    echo "  RUST_LOG            Log level (debug, info, warn, error)"
}

# Function to check prerequisites
check_prerequisites() {
    print_status $BLUE "Checking prerequisites..."
    
    # Check if cargo is available
    if ! command -v cargo >/dev/null 2>&1; then
        print_status $RED "Error: cargo not found. Please install Rust."
        exit 1
    fi
    
    # Check if psql is available for PostgreSQL operations
    if ! command -v psql >/dev/null 2>&1; then
        print_status $YELLOW "Warning: psql not found. Some operations may not work."
    fi
    
    # Create logs directory
    mkdir -p "$(dirname "$MIGRATION_LOG")"
    
    # Test database connection
    if ! test_database_connection; then
        print_status $RED "Error: Cannot connect to database"
        print_status $RED "Database URL: $(mask_database_url "$DATABASE_URL")"
        exit 1
    fi
    
    print_status $GREEN "Prerequisites check passed"
}

# Function to test database connection
test_database_connection() {
    if command -v psql >/dev/null 2>&1; then
        psql "$DATABASE_URL" -c "SELECT 1;" >/dev/null 2>&1
    else
        # Fallback: use cargo to test connection
        cd "$PROJECT_ROOT"
        cargo run --bin infinitium-signal -- --check-db >/dev/null 2>&1
    fi
}

# Function to mask sensitive information in database URL
mask_database_url() {
    local url="$1"
    echo "$url" | sed 's/:\/\/[^:]*:[^@]*@/:\/\/***:***@/'
}

# Function to create database backup
create_backup() {
    if [[ "$BACKUP_BEFORE_MIGRATION" != "true" ]]; then
        return 0
    fi
    
    print_status $BLUE "Creating database backup..."
    
    local backup_dir="${PROJECT_ROOT}/backups"
    local backup_file="${backup_dir}/backup_$(date +%Y%m%d_%H%M%S).sql"
    
    mkdir -p "$backup_dir"
    
    if command -v pg_dump >/dev/null 2>&1; then
        if pg_dump "$DATABASE_URL" > "$backup_file"; then
            print_status $GREEN "Backup created: $backup_file"
            return 0
        else
            print_status $RED "Backup failed"
            return 1
        fi
    else
        print_status $YELLOW "pg_dump not available, skipping backup"
        return 0
    fi
}

# Function to run migrations up
migrate_up() {
    print_status $BLUE "Running pending migrations..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status $YELLOW "DRY RUN: Would run pending migrations"
        cd "$PROJECT_ROOT"
        cargo run --bin infinitium-signal -- migrate --dry-run
        return $?
    fi
    
    if [[ "$FORCE" != "true" ]]; then
        print_status $YELLOW "This will run all pending migrations."
        read -p "Continue? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status $BLUE "Migration cancelled"
            return 0
        fi
    fi
    
    # Create backup before migration
    if ! create_backup; then
        if [[ "$FORCE" != "true" ]]; then
            print_status $RED "Backup failed. Continue anyway? (y/N): "
            read -p "" -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                return 1
            fi
        fi
    fi
    
    cd "$PROJECT_ROOT"
    if cargo run --bin infinitium-signal -- migrate up; then
        print_status $GREEN "Migrations completed successfully"
        return 0
    else
        print_status $RED "Migration failed"
        return 1
    fi
}

# Function to rollback migrations
migrate_down() {
    print_status $BLUE "Rolling back last migration..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status $YELLOW "DRY RUN: Would rollback last migration"
        cd "$PROJECT_ROOT"
        cargo run --bin infinitium-signal -- migrate down --dry-run
        return $?
    fi
    
    if [[ "$FORCE" != "true" ]]; then
        print_status $YELLOW "This will rollback the last migration."
        print_status $RED "WARNING: This may result in data loss!"
        read -p "Continue? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status $BLUE "Rollback cancelled"
            return 0
        fi
    fi
    
    # Create backup before rollback
    if ! create_backup; then
        if [[ "$FORCE" != "true" ]]; then
            print_status $RED "Backup failed. Continue anyway? (y/N): "
            read -p "" -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                return 1
            fi
        fi
    fi
    
    cd "$PROJECT_ROOT"
    if cargo run --bin infinitium-signal -- migrate down; then
        print_status $GREEN "Rollback completed successfully"
        return 0
    else
        print_status $RED "Rollback failed"
        return 1
    fi
}

# Function to reset database
reset_database() {
    print_status $BLUE "Resetting database..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status $YELLOW "DRY RUN: Would reset database"
        return 0
    fi
    
    if [[ "$FORCE" != "true" ]]; then
        print_status $RED "WARNING: This will drop all tables and data!"
        print_status $YELLOW "This action cannot be undone."
        read -p "Type 'RESET' to confirm: " -r
        echo
        if [[ "$REPLY" != "RESET" ]]; then
            print_status $BLUE "Reset cancelled"
            return 0
        fi
    fi
    
    # Create backup before reset
    if ! create_backup; then
        if [[ "$FORCE" != "true" ]]; then
            print_status $RED "Backup failed. Continue anyway? (y/N): "
            read -p "" -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                return 1
            fi
        fi
    fi
    
    cd "$PROJECT_ROOT"
    if cargo run --bin infinitium-signal -- migrate reset; then
        print_status $GREEN "Database reset completed successfully"
        return 0
    else
        print_status $RED "Database reset failed"
        return 1
    fi
}

# Function to show migration status
show_status() {
    print_status $BLUE "Checking migration status..."
    
    cd "$PROJECT_ROOT"
    cargo run --bin infinitium-signal -- migrate status
}

# Function to create new migration
create_migration() {
    local migration_name="$1"
    
    if [[ -z "$migration_name" ]]; then
        print_status $RED "Error: Migration name is required"
        return 1
    fi
    
    print_status $BLUE "Creating new migration: $migration_name"
    
    cd "$PROJECT_ROOT"
    if cargo run --bin infinitium-signal -- migrate create "$migration_name"; then
        print_status $GREEN "Migration created successfully"
        return 0
    else
        print_status $RED "Failed to create migration"
        return 1
    fi
}

# Function to validate migrations
validate_migrations() {
    print_status $BLUE "Validating migration files..."
    
    cd "$PROJECT_ROOT"
    if cargo run --bin infinitium-signal -- migrate validate; then
        print_status $GREEN "All migrations are valid"
        return 0
    else
        print_status $RED "Migration validation failed"
        return 1
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        up|down|reset|status|validate)
            COMMAND="$1"
            shift
            ;;
        create)
            COMMAND="create"
            shift
            if [[ $# -gt 0 ]]; then
                MIGRATION_NAME="$1"
                shift
            fi
            ;;
        --database-url)
            DATABASE_URL="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --no-backup)
            BACKUP_BEFORE_MIGRATION=false
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            print_usage
            exit 0
            ;;
        *)
            print_status $RED "Unknown option: $1"
            print_usage
            exit 1
            ;;
    esac
done

# Validate command
if [[ -z "$COMMAND" ]]; then
    print_status $RED "Error: Command is required"
    print_usage
    exit 1
fi

# Main execution
main() {
    print_status $BLUE "Infinitium Signal Database Migration Tool"
    print_status $BLUE "Database: $(mask_database_url "$DATABASE_URL")"
    
    if [[ "$VERBOSE" == "true" ]]; then
        echo "Migration log: $MIGRATION_LOG"
        echo "$(date '+%Y-%m-%d %H:%M:%S') - Starting migration: $COMMAND" >> "$MIGRATION_LOG"
    fi
    
    check_prerequisites
    
    case "$COMMAND" in
        up)
            migrate_up
            ;;
        down)
            migrate_down
            ;;
        reset)
            reset_database
            ;;
        status)
            show_status
            ;;
        create)
            create_migration "$MIGRATION_NAME"
            ;;
        validate)
            validate_migrations
            ;;
        *)
            print_status $RED "Unknown command: $COMMAND"
            exit 1
            ;;
    esac
    
    local exit_code=$?
    
    if [[ "$VERBOSE" == "true" ]]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') - Migration completed with exit code: $exit_code" >> "$MIGRATION_LOG"
    fi
    
    exit $exit_code
}

# Run main function
main
