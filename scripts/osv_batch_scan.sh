#!/bin/bash

# OSV Batch Processing Scan Script
# This script performs OSV vulnerability scanning on multiple projects/repositories
# Useful for scanning multiple components, microservices, or external dependencies

set -euo pipefail
IFS=$'\n\t'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
BATCH_RESULTS_DIR="${PROJECT_ROOT}/batch_scan_results"
LOG_DIR="${BATCH_RESULTS_DIR}/logs"
REPORTS_DIR="${BATCH_RESULTS_DIR}/reports"

# Default configuration
: "${BATCH_CONFIG_FILE:=batch_config.json}"
: "${MAX_CONCURRENT_SCANS:=3}"
: "${SCAN_TIMEOUT:=1800}"  # 30 minutes
: "${RETRY_FAILED:=true}"
: "${GENERATE_SUMMARY_REPORT:=true}"
: "${NOTIFICATION_WEBHOOK_URL:=}"
: "${CLEANUP_ON_SUCCESS:=false}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Global variables
BATCH_ID=""
LOG_FILE=""
TOTAL_PROJECTS=0
COMPLETED_SCANS=0
FAILED_SCANS=0
TOTAL_VULNERABILITIES=0
HIGH_SEVERITY_COUNT=0

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "${LOG_FILE}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a "${LOG_FILE}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "${LOG_FILE}"
}

# Setup function
setup() {
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    BATCH_ID="osv_batch_${timestamp}"

    # Create directories
    mkdir -p "${BATCH_RESULTS_DIR}/${BATCH_ID}"
    mkdir -p "${LOG_DIR}"
    mkdir -p "${REPORTS_DIR}"

    # Set log file
    LOG_FILE="${LOG_DIR}/osv_batch_${timestamp}.log"

    log "Starting OSV batch scan"
    log "Batch ID: ${BATCH_ID}"
    log "Results directory: ${BATCH_RESULTS_DIR}/${BATCH_ID}"
}

# Validate batch configuration
validate_config() {
    log "Validating batch configuration..."

    if [ ! -f "$BATCH_CONFIG_FILE" ]; then
        error "Batch configuration file not found: $BATCH_CONFIG_FILE"
        echo "Please create a batch configuration file with the following format:"
        echo '{
  "projects": [
    {
      "name": "project1",
      "repository": "https://github.com/org/project1.git",
      "branch": "main",
      "scan_paths": ["."],
      "build_commands": ["cargo build --release"],
      "scan_command": "./target/release/my-binary scan . --format json"
    }
  ]
}'
        exit 1
    fi

    # Validate JSON format
    if ! jq empty "$BATCH_CONFIG_FILE" 2>/dev/null; then
        error "Invalid JSON in batch configuration file: $BATCH_CONFIG_FILE"
        exit 1
    fi

    # Check required fields
    local project_count=$(jq '.projects | length' "$BATCH_CONFIG_FILE")
    if [ "$project_count" -eq 0 ]; then
        error "No projects found in batch configuration"
        exit 1
    fi

    TOTAL_PROJECTS=$project_count
    log "Found $TOTAL_PROJECTS projects to scan"
}

# Check dependencies
check_dependencies() {
    log "Checking dependencies..."

    local missing_deps=()

    for cmd in jq git cargo parallel timeout; do
        if ! command -v "$cmd" &> /dev/null; then
            missing_deps+=("$cmd")
        fi
    done

    if [ ${#missing_deps[@]} -gt 0 ]; then
        error "Missing required dependencies: ${missing_deps[*]}"
        echo "Please install the missing dependencies:"
        echo "  Ubuntu/Debian: sudo apt-get install jq git parallel"
        echo "  Rust: curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh"
        exit 1
    fi

    log "Dependencies check passed"
}

# Scan single project
scan_project() {
    local project_json="$1"
    local project_name=$(echo "$project_json" | jq -r '.name')
    local repository=$(echo "$project_json" | jq -r '.repository')
    local branch=$(echo "$project_json" | jq -r '.branch // "main"')
    local scan_paths=$(echo "$project_json" | jq -r '.scan_paths // ["."] | join(" ")')
    local build_commands=$(echo "$project_json" | jq -r '.build_commands // [] | join(" && ")')
    local scan_command=$(echo "$project_json" | jq -r '.scan_command // "./target/release/infinitum-signal scan . --format json"')

    local project_dir="${BATCH_RESULTS_DIR}/${BATCH_ID}/projects/${project_name}"
    local project_log="${project_dir}/scan.log"
    local start_time=$(date +%s)

    log "Starting scan for project: $project_name"

    # Create project directory
    mkdir -p "$project_dir"

    # Clone repository
    if ! git clone --branch "$branch" --depth 1 "$repository" "$project_dir/repo" >> "$project_log" 2>&1; then
        error "Failed to clone repository for $project_name"
        echo "{\"project\": \"$project_name\", \"status\": \"failed\", \"error\": \"Clone failed\"}" > "${project_dir}/result.json"
        return 1
    fi

    cd "$project_dir/repo"

    # Run build commands if specified
    if [ -n "$build_commands" ] && [ "$build_commands" != "null" ]; then
        log "Building project: $project_name"
        if ! eval "$build_commands" >> "$project_log" 2>&1; then
            error "Build failed for $project_name"
            echo "{\"project\": \"$project_name\", \"status\": \"failed\", \"error\": \"Build failed\"}" > "${project_dir}/result.json"
            return 1
        fi
    fi

    # Run scan for each path
    local all_vulns=0
    local all_high=0
    local scan_results="[]"

    for scan_path in $scan_paths; do
        log "Scanning path: $scan_path for project: $project_name"

        # Run scan with timeout
        if timeout "$SCAN_TIMEOUT" bash -c "cd '$scan_path' && $scan_command" > "${project_dir}/scan_output.json" 2>> "$project_log"; then
            # Parse results
            if [ -f "${project_dir}/scan_output.json" ] && jq empty "${project_dir}/scan_output.json" 2>/dev/null; then
                local vuln_count=$(jq '.vulnerabilities | length' "${project_dir}/scan_output.json" 2>/dev/null || echo "0")
                local high_count=$(jq '[.vulnerabilities[] | select(.severity == "HIGH" or (.cvss_score // 0) >= 7.0)] | length' "${project_dir}/scan_output.json" 2>/dev/null || echo "0")

                all_vulns=$((all_vulns + vuln_count))
                all_high=$((all_high + high_count))

                # Merge results
                scan_results=$(jq --argjson new "$(cat "${project_dir}/scan_output.json")" '. + [$new]' <<< "$scan_results")
            fi
        else
            warn "Scan timed out for $project_name path: $scan_path"
        fi
    done

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    # Create result summary
    local result="{
        \"project\": \"$project_name\",
        \"repository\": \"$repository\",
        \"branch\": \"$branch\",
        \"status\": \"completed\",
        \"scan_duration\": $duration,
        \"total_vulnerabilities\": $all_vulns,
        \"high_severity_vulnerabilities\": $all_high,
        \"scan_timestamp\": \"$(date -Iseconds)\"
    }"

    echo "$result" > "${project_dir}/result.json"

    # Update global counters
    ((COMPLETED_SCANS++))
    TOTAL_VULNERABILITIES=$((TOTAL_VULNERABILITIES + all_vulns))
    HIGH_SEVERITY_COUNT=$((HIGH_SEVERITY_COUNT + all_high))

    log "Completed scan for $project_name: ${all_vulns} vulnerabilities, ${all_high} high severity (${duration}s)"

    return 0
}

# Run batch scan
run_batch_scan() {
    log "Running batch scan with $MAX_CONCURRENT_SCANS concurrent processes..."

    # Export functions for parallel execution
    export -f scan_project
    export -f log
    export -f warn
    export -f error
    export LOG_FILE
    export BATCH_RESULTS_DIR
    export BATCH_ID
    export SCAN_TIMEOUT
    export COMPLETED_SCANS
    export TOTAL_VULNERABILITIES
    export HIGH_SEVERITY_COUNT

    # Run scans in parallel
    jq -r '.projects[] | @json' "$BATCH_CONFIG_FILE" | \
        parallel --no-notice --max-procs "$MAX_CONCURRENT_SCANS" --pipe scan_project {}

    log "Batch scan completed"
}

# Retry failed scans
retry_failed_scans() {
    if [ "$RETRY_FAILED" != "true" ] || [ $FAILED_SCANS -eq 0 ]; then
        return 0
    fi

    log "Retrying failed scans..."

    # Find failed projects and retry
    find "${BATCH_RESULTS_DIR}/${BATCH_ID}/projects" -name "result.json" -exec jq -r 'select(.status == "failed") | .project' {} \; | \
        while read -r failed_project; do
            log "Retrying scan for: $failed_project"
            # Retry logic would go here
        done
}

# Generate summary report
generate_summary_report() {
    if [ "$GENERATE_SUMMARY_REPORT" != "true" ]; then
        return 0
    fi

    log "Generating summary report..."

    local summary_file="${REPORTS_DIR}/osv_batch_report_${BATCH_ID}.html"
    local summary_json="${BATCH_RESULTS_DIR}/${BATCH_ID}/batch_summary.json"

    # Collect all results
    local results="[]"
    find "${BATCH_RESULTS_DIR}/${BATCH_ID}/projects" -name "result.json" -exec cat {} \; | \
        jq -s '.' > "$summary_json"

    # Generate HTML report
    cat > "$summary_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>OSV Batch Scan Report - ${BATCH_ID}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .value { font-size: 2em; font-weight: bold; }
        .projects { margin-top: 20px; }
        .project { border: 1px solid #dee2e6; margin: 10px 0; padding: 15px; border-radius: 8px; display: grid; grid-template-columns: 2fr 1fr 1fr 1fr 1fr; gap: 10px; align-items: center; }
        .project.success { border-left: 5px solid #28a745; background-color: #f8fff8; }
        .project.failed { border-left: 5px solid #dc3545; background-color: #fff5f5; }
        .status-badge { padding: 4px 8px; border-radius: 4px; color: white; font-size: 12px; font-weight: bold; text-align: center; }
        .success-badge { background-color: #28a745; }
        .failed-badge { background-color: #dc3545; }
        .footer { margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: center; color: #666; }
        .progress-bar { width: 100%; height: 20px; background-color: #f0f0f0; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 OSV Batch Vulnerability Scan Report</h1>
            <p>Batch ID: ${BATCH_ID}</p>
            <p>Generated: $(date)</p>
        </div>

        <div class="summary-grid">
            <div class="summary-card">
                <h3>Total Projects</h3>
                <div class="value">${TOTAL_PROJECTS}</div>
            </div>
            <div class="summary-card">
                <h3>Completed Scans</h3>
                <div class="value" style="color: #28a745;">${COMPLETED_SCANS}</div>
            </div>
            <div class="summary-card">
                <h3>Failed Scans</h3>
                <div class="value" style="color: #dc3545;">${FAILED_SCANS}</div>
            </div>
            <div class="summary-card">
                <h3>Total Vulnerabilities</h3>
                <div class="value" style="color: #ffc107;">${TOTAL_VULNERABILITIES}</div>
            </div>
            <div class="summary-card">
                <h3>High Severity</h3>
                <div class="value" style="color: #dc3545;">${HIGH_SEVERITY_COUNT}</div>
            </div>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" style="width: $((COMPLETED_SCANS * 100 / TOTAL_PROJECTS))%"></div>
        </div>

        <div class="projects">
            <h2>📋 Project Results</h2>
EOF

    # Add project results
    jq -r '.[] | "<div class=\"project \(.status)\"><div><strong>\(.project)</strong><br><small>\(.repository)</small></div><div class=\"status-badge \(.status)-badge\">\(.status | ascii_upcase)</div><div>\(.total_vulnerabilities // 0) vulns</div><div>\(.high_severity_vulnerabilities // 0) high</div><div>\(.scan_duration // 0)s</div></div>"' "$summary_json" >> "$summary_file"

    cat >> "$summary_file" << EOF
        </div>

        <div class="footer">
            <p>Report generated by OSV Batch Scan Script</p>
            <p>Configuration: ${BATCH_CONFIG_FILE}</p>
        </div>
    </div>
</body>
</html>
EOF

    log "Summary report generated: $summary_file"
}

# Send notifications
send_notifications() {
    log "Sending notifications..."

    if [ -n "$NOTIFICATION_WEBHOOK_URL" ]; then
        log "Sending webhook notification"

        local webhook_payload="{
            \"batch_id\": \"$BATCH_ID\",
            \"total_projects\": $TOTAL_PROJECTS,
            \"completed_scans\": $COMPLETED_SCANS,
            \"failed_scans\": $FAILED_SCANS,
            \"total_vulnerabilities\": $TOTAL_VULNERABILITIES,
            \"high_severity_count\": $HIGH_SEVERITY_COUNT,
            \"timestamp\": \"$(date -Iseconds)\",
            \"report_url\": \"${REPORTS_DIR}/osv_batch_report_${BATCH_ID}.html\"
        }"

        curl -X POST -H 'Content-type: application/json' --data "$webhook_payload" "$NOTIFICATION_WEBHOOK_URL" || \
            warn "Failed to send webhook notification"
    fi
}

# Cleanup function
cleanup() {
    if [ "$CLEANUP_ON_SUCCESS" = "true" ] && [ $FAILED_SCANS -eq 0 ]; then
        log "Cleaning up temporary files..."
        rm -rf "${BATCH_RESULTS_DIR}/${BATCH_ID}/projects"
    fi
}

# Main execution
main() {
    setup
    validate_config
    check_dependencies
    run_batch_scan
    retry_failed_scans
    generate_summary_report
    send_notifications
    cleanup

    log "OSV batch scan completed"
    log "Batch ID: ${BATCH_ID}"
    log "Results: ${BATCH_RESULTS_DIR}/${BATCH_ID}"
    if [ "$GENERATE_SUMMARY_REPORT" = "true" ]; then
        log "Summary Report: ${REPORTS_DIR}/osv_batch_report_${BATCH_ID}.html"
    fi

    # Exit with error if any scans failed
    if [ $FAILED_SCANS -gt 0 ]; then
        error "Batch scan completed with $FAILED_SCANS failed scans"
        exit 1
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --config)
            BATCH_CONFIG_FILE="$2"
            shift 2
            ;;
        --max-concurrent)
            MAX_CONCURRENT_SCANS="$2"
            shift 2
            ;;
        --timeout)
            SCAN_TIMEOUT="$2"
            shift 2
            ;;
        --no-retry)
            RETRY_FAILED=false
            shift
            ;;
        --no-report)
            GENERATE_SUMMARY_REPORT=false
            shift
            ;;
        --cleanup)
            CLEANUP_ON_SUCCESS=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --config FILE          Batch configuration file (default: batch_config.json)"
            echo "  --max-concurrent N     Maximum concurrent scans (default: 3)"
            echo "  --timeout SECONDS      Scan timeout in seconds (default: 1800)"
            echo "  --no-retry            Don't retry failed scans"
            echo "  --no-report           Don't generate summary report"
            echo "  --cleanup             Clean up temporary files on success"
            echo "  --help                Show this help message"
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Trap for cleanup on exit
trap 'error "Script interrupted by user"; exit 1' INT TERM

# Run main function
main