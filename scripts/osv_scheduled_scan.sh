#!/bin/bash

# OSV Scheduled Vulnerability Scan Script
# This script performs automated OSV vulnerability scanning on a schedule
# Can be run via cron, systemd timer, or other scheduling systems

set -euo pipefail
IFS=$'\n\t'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
SCAN_RESULTS_DIR="${PROJECT_ROOT}/scan_results"
LOG_DIR="${SCAN_RESULTS_DIR}/logs"
REPORTS_DIR="${SCAN_RESULTS_DIR}/reports"

# Default configuration (can be overridden by environment variables)
: "${SCAN_BRANCH:=main}"
: "${SCAN_FORMAT:=json}"
: "${NOTIFICATION_EMAIL:=}"
: "${SLACK_WEBHOOK_URL:=}"
: "${MAX_RETRIES:=3}"
: "${RETRY_DELAY:=30}"
: "${FAIL_ON_VULNERABILITIES:=false}"
: "${GENERATE_HTML_REPORT:=true}"
: "${CLEANUP_OLD_RESULTS:=true}"
: "${RETENTION_DAYS:=30}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "${LOG_FILE}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a "${LOG_FILE}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "${LOG_FILE}"
}

# Setup function
setup() {
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    SCAN_ID="osv_scan_${timestamp}"

    # Create directories
    mkdir -p "${SCAN_RESULTS_DIR}/${SCAN_ID}"
    mkdir -p "${LOG_DIR}"
    mkdir -p "${REPORTS_DIR}"

    # Set log file
    LOG_FILE="${LOG_DIR}/osv_scan_${timestamp}.log"

    log "Starting OSV scheduled scan"
    log "Scan ID: ${SCAN_ID}"
    log "Branch: ${SCAN_BRANCH}"
    log "Results directory: ${SCAN_RESULTS_DIR}/${SCAN_ID}"
}

# Check dependencies
check_dependencies() {
    log "Checking dependencies..."

    local missing_deps=()

    # Check for required commands
    for cmd in git cargo jq curl; do
        if ! command -v "$cmd" &> /dev/null; then
            missing_deps+=("$cmd")
        fi
    done

    if [ ${#missing_deps[@]} -gt 0 ]; then
        error "Missing required dependencies: ${missing_deps[*]}"
        error "Please install the missing dependencies and try again"
        exit 1
    fi

    # Check if we're in a git repository
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        error "Not in a git repository"
        exit 1
    fi

    log "Dependencies check passed"
}

# Clone or update repository
prepare_repository() {
    log "Preparing repository..."

    # If we're not on the target branch, checkout
    local current_branch=$(git rev-parse --abbrev-ref HEAD)
    if [ "$current_branch" != "$SCAN_BRANCH" ]; then
        log "Switching to branch: ${SCAN_BRANCH}"
        git checkout "$SCAN_BRANCH"
        git pull origin "$SCAN_BRANCH"
    else
        log "Already on branch: ${SCAN_BRANCH}"
        git pull origin "$SCAN_BRANCH"
    fi

    # Get commit info
    COMMIT_SHA=$(git rev-parse HEAD)
    COMMIT_SHORT=$(git rev-parse --short HEAD)
    COMMIT_MESSAGE=$(git log -1 --pretty=%B | head -1)

    log "Current commit: ${COMMIT_SHORT}"
    log "Commit message: ${COMMIT_MESSAGE}"
}

# Build the project
build_project() {
    log "Building project..."

    # Clean previous build
    cargo clean

    # Build in release mode
    if ! cargo build --release; then
        error "Failed to build project"
        exit 1
    fi

    log "Project built successfully"
}

# Extract dependencies for scanning
extract_dependencies() {
    log "Extracting dependencies..."

    # Get cargo metadata
    if ! cargo metadata --format-version 1 > "${SCAN_RESULTS_DIR}/${SCAN_ID}/cargo_metadata.json"; then
        error "Failed to get cargo metadata"
        exit 1
    fi

    # Extract dependencies
    jq -r '.packages[] | select(.name == "infinitium-signal") | .dependencies[] | "\(.name) \(.version)"' \
        "${SCAN_RESULTS_DIR}/${SCAN_ID}/cargo_metadata.json" > "${SCAN_RESULTS_DIR}/${SCAN_ID}/dependencies.txt"

    DEP_COUNT=$(wc -l < "${SCAN_RESULTS_DIR}/${SCAN_ID}/dependencies.txt")
    log "Found ${DEP_COUNT} dependencies to scan"
}

# Run OSV scan with retry logic
run_osv_scan() {
    local attempt=1
    local scan_success=false

    while [ $attempt -le $MAX_RETRIES ] && [ "$scan_success" = false ]; do
        log "Running OSV scan (attempt ${attempt}/${MAX_RETRIES})..."

        # Run the scan
        if ./target/release/infinitum-signal scan . --format "${SCAN_FORMAT}" \
            > "${SCAN_RESULTS_DIR}/${SCAN_ID}/osv_scan_results.${SCAN_FORMAT}" 2>&1; then

            scan_success=true
            log "OSV scan completed successfully"
        else
            warn "OSV scan attempt ${attempt} failed"

            if [ $attempt -lt $MAX_RETRIES ]; then
                log "Retrying in ${RETRY_DELAY} seconds..."
                sleep $RETRY_DELAY
            fi
        fi

        ((attempt++))
    done

    if [ "$scan_success" = false ]; then
        error "OSV scan failed after ${MAX_RETRIES} attempts"
        exit 1
    fi
}

# Analyze scan results
analyze_results() {
    log "Analyzing scan results..."

    local results_file="${SCAN_RESULTS_DIR}/${SCAN_ID}/osv_scan_results.${SCAN_FORMAT}"

    if [ ! -f "$results_file" ]; then
        error "Scan results file not found: $results_file"
        exit 1
    fi

    # Parse JSON results
    if [ "$SCAN_FORMAT" = "json" ]; then
        VULN_COUNT=$(jq '.vulnerabilities | length' "$results_file" 2>/dev/null || echo "0")
        HIGH_SEVERITY=$(jq '[.vulnerabilities[] | select(.severity == "HIGH" or (.cvss_score // 0) >= 7.0)] | length' "$results_file" 2>/dev/null || echo "0")
        MEDIUM_SEVERITY=$(jq '[.vulnerabilities[] | select(.severity == "MEDIUM" or ((.cvss_score // 0) >= 4.0 and (.cvss_score // 0) < 7.0))] | length' "$results_file" 2>/dev/null || echo "0")
        SCAN_DURATION=$(jq '.scan_duration // 0' "$results_file" 2>/dev/null || echo "0")
    else
        # For non-JSON formats, we can't easily parse
        VULN_COUNT="unknown"
        HIGH_SEVERITY="unknown"
        MEDIUM_SEVERITY="unknown"
        SCAN_DURATION="unknown"
    fi

    log "Scan Results Summary:"
    log "  Total vulnerabilities: ${VULN_COUNT}"
    log "  High severity: ${HIGH_SEVERITY}"
    log "  Medium severity: ${MEDIUM_SEVERITY}"
    log "  Scan duration: ${SCAN_DURATION}ms"
}

# Generate HTML report
generate_html_report() {
    if [ "$GENERATE_HTML_REPORT" != "true" ]; then
        return 0
    fi

    log "Generating HTML report..."

    local html_file="${REPORTS_DIR}/osv_scan_report_${SCAN_ID}.html"
    local results_file="${SCAN_RESULTS_DIR}/${SCAN_ID}/osv_scan_results.${SCAN_FORMAT}"

    cat > "$html_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>OSV Vulnerability Scan Report - ${SCAN_ID}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .value { font-size: 2em; font-weight: bold; color: #667eea; }
        .vulnerabilities { margin-top: 20px; }
        .vulnerability { border: 1px solid #dee2e6; margin: 10px 0; padding: 15px; border-radius: 8px; }
        .high-severity { border-left: 5px solid #dc3545; background-color: #fff5f5; }
        .medium-severity { border-left: 5px solid #ffc107; background-color: #fffbf0; }
        .low-severity { border-left: 5px solid #28a745; background-color: #f8fff8; }
        .severity-badge { display: inline-block; padding: 4px 8px; border-radius: 4px; color: white; font-size: 12px; font-weight: bold; }
        .high-badge { background-color: #dc3545; }
        .medium-badge { background-color: #ffc107; color: #000; }
        .low-badge { background-color: #28a745; }
        .footer { margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: center; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 OSV Vulnerability Scan Report</h1>
            <p>Scheduled Scan ID: ${SCAN_ID}</p>
            <p>Branch: ${SCAN_BRANCH} | Commit: ${COMMIT_SHORT}</p>
            <p>Generated: $(date)</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <h3>Total Dependencies</h3>
                <div class="value">${DEP_COUNT}</div>
            </div>
            <div class="summary-card">
                <h3>Total Vulnerabilities</h3>
                <div class="value">${VULN_COUNT}</div>
            </div>
            <div class="summary-card">
                <h3>High Severity</h3>
                <div class="value" style="color: #dc3545;">${HIGH_SEVERITY}</div>
            </div>
            <div class="summary-card">
                <h3>Medium Severity</h3>
                <div class="value" style="color: #ffc107;">${MEDIUM_SEVERITY}</div>
            </div>
        </div>
EOF

    # Add vulnerability details if we have JSON results
    if [ "$SCAN_FORMAT" = "json" ] && [ -f "$results_file" ]; then
        echo '<div class="vulnerabilities">' >> "$html_file"
        echo '<h2>🚨 Vulnerabilities Found</h2>' >> "$html_file"

        jq -r '.vulnerabilities[] | "<div class=\"vulnerability \(.severity | ascii_downcase // "unknown")-severity\"><h3>\(.id)</h3><div class=\"severity-badge \(.severity | ascii_downcase // "unknown")-badge\">\(.severity // "Unknown")</div><p><strong>CVSS Score:</strong> \(.cvss_score // "N/A")</p><p><strong>Package:</strong> \(.package.name)@\(.package.version)</p><p><strong>Summary:</strong> \(.summary)</p><p><strong>Description:</strong> \(.details // "No description available")</p><p><strong>Affected Versions:</strong> \(.affected_versions | join(", ") // "N/A")</p><p><strong>Fixed Versions:</strong> \(.fixed_versions | join(", ") // "N/A")</p></div>"' "$results_file" >> "$html_file"

        echo '</div>' >> "$html_file"
    fi

    cat >> "$html_file" << EOF
        <div class="footer">
            <p>Report generated by OSV Scheduled Scan Script</p>
            <p>Project: Infinitium Signal | Repository: $(git remote get-url origin)</p>
        </div>
    </div>
</body>
</html>
EOF

    log "HTML report generated: $html_file"
}

# Send notifications
send_notifications() {
    log "Sending notifications..."

    local subject="OSV Scan Results: ${SCAN_ID}"
    local message="OSV vulnerability scan completed for branch ${SCAN_BRANCH}

Scan ID: ${SCAN_ID}
Commit: ${COMMIT_SHORT}
Dependencies scanned: ${DEP_COUNT}
Vulnerabilities found: ${VULN_COUNT}
High severity: ${HIGH_SEVERITY}
Medium severity: ${MEDIUM_SEVERITY}

View full report: ${REPORTS_DIR}/osv_scan_report_${SCAN_ID}.html
Scan results: ${SCAN_RESULTS_DIR}/${SCAN_ID}/"

    # Email notification
    if [ -n "$NOTIFICATION_EMAIL" ]; then
        log "Sending email notification to: ${NOTIFICATION_EMAIL}"

        # Attach HTML report if it exists
        local attachments=""
        if [ -f "${REPORTS_DIR}/osv_scan_report_${SCAN_ID}.html" ]; then
            attachments="-a ${REPORTS_DIR}/osv_scan_report_${SCAN_ID}.html"
        fi

        echo "$message" | mail -s "$subject" $attachments "$NOTIFICATION_EMAIL" || warn "Failed to send email notification"
    fi

    # Slack notification
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        log "Sending Slack notification"

        local slack_color="good"
        if [ "$HIGH_SEVERITY" != "0" ] && [ "$HIGH_SEVERITY" != "unknown" ]; then
            slack_color="danger"
        elif [ "$VULN_COUNT" != "0" ] && [ "$VULN_COUNT" != "unknown" ]; then
            slack_color="warning"
        fi

        local slack_payload="{
            \"attachments\": [{
                \"color\": \"$slack_color\",
                \"title\": \"OSV Vulnerability Scan Results\",
                \"text\": \"Branch: ${SCAN_BRANCH}\nCommit: ${COMMIT_SHORT}\nVulnerabilities: ${VULN_COUNT}\nHigh Severity: ${HIGH_SEVERITY}\",
                \"fields\": [
                    {\"title\": \"Scan ID\", \"value\": \"${SCAN_ID}\", \"short\": true},
                    {\"title\": \"Dependencies\", \"value\": \"${DEP_COUNT}\", \"short\": true}
                ]
            }]
        }"

        curl -X POST -H 'Content-type: application/json' --data "$slack_payload" "$SLACK_WEBHOOK_URL" || warn "Failed to send Slack notification"
    fi
}

# Cleanup old results
cleanup_old_results() {
    if [ "$CLEANUP_OLD_RESULTS" != "true" ]; then
        return 0
    fi

    log "Cleaning up old scan results (older than ${RETENTION_DAYS} days)..."

    # Remove old scan directories
    find "$SCAN_RESULTS_DIR" -maxdepth 1 -type d -name "osv_scan_*" -mtime +$RETENTION_DAYS -exec rm -rf {} \; 2>/dev/null || true

    # Remove old log files
    find "$LOG_DIR" -name "osv_scan_*.log" -mtime +$RETENTION_DAYS -delete 2>/dev/null || true

    # Remove old reports
    find "$REPORTS_DIR" -name "osv_scan_report_*.html" -mtime +$RETENTION_DAYS -delete 2>/dev/null || true

    log "Cleanup completed"
}

# Main execution
main() {
    setup
    check_dependencies
    prepare_repository
    build_project
    extract_dependencies
    run_osv_scan
    analyze_results
    generate_html_report
    send_notifications
    cleanup_old_results

    log "OSV scheduled scan completed successfully"
    log "Scan ID: ${SCAN_ID}"
    log "Results: ${SCAN_RESULTS_DIR}/${SCAN_ID}"
    if [ "$GENERATE_HTML_REPORT" = "true" ]; then
        log "Report: ${REPORTS_DIR}/osv_scan_report_${SCAN_ID}.html"
    fi
}

# Trap for cleanup on exit
trap 'error "Script interrupted by user"; exit 1' INT TERM

# Run main function
main "$@"