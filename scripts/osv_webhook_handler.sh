#!/bin/bash

# OSV Webhook Handler Script
# This script handles webhook events from repository hosts (GitHub, GitLab, etc.)
# and triggers OSV vulnerability scans based on repository changes

set -euo pipefail
IFS=$'\n\t'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
WEBHOOK_RESULTS_DIR="${PROJECT_ROOT}/webhook_scan_results"
LOG_DIR="${WEBHOOK_RESULTS_DIR}/logs"

# Default configuration
: "${WEBHOOK_SECRET:=}"
: "${ALLOWED_REPOS:=}"
: "${TRIGGER_EVENTS:=push,pull_request,release}"
: "${SCAN_ON_PUSH:=true}"
: "${SCAN_ON_PR:=true}"
: "${SCAN_ON_RELEASE:=true}"
: "${NOTIFICATION_URL:=}"
: "${MAX_SCAN_QUEUE:=5}"
: "${SCAN_TIMEOUT:=3600}"  # 1 hour

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Global variables
WEBHOOK_ID=""
LOG_FILE=""
SCAN_QUEUE=()

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "${LOG_FILE}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a "${LOG_FILE}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "${LOG_FILE}"
}

# Setup function
setup() {
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    WEBHOOK_ID="webhook_${timestamp}"

    # Create directories
    mkdir -p "${WEBHOOK_RESULTS_DIR}/${WEBHOOK_ID}"
    mkdir -p "${LOG_DIR}"

    # Set log file
    LOG_FILE="${LOG_DIR}/webhook_${timestamp}.log"

    log "OSV Webhook Handler started"
    log "Webhook ID: ${WEBHOOK_ID}"
}

# Verify webhook signature (GitHub)
verify_github_signature() {
    local payload="$1"
    local signature="$2"

    if [ -z "$WEBHOOK_SECRET" ]; then
        warn "No webhook secret configured, skipping signature verification"
        return 0
    fi

    if [ -z "$signature" ]; then
        error "No signature provided in webhook"
        return 1
    fi

    # Remove 'sha256=' prefix if present
    signature="${signature#sha256=}"

    # Calculate expected signature
    local expected_signature=$(echo -n "$payload" | openssl dgst -sha256 -hmac "$WEBHOOK_SECRET" -binary | xxd -p -c 256)

    if [ "$signature" != "$expected_signature" ]; then
        error "Webhook signature verification failed"
        return 1
    fi

    log "Webhook signature verified successfully"
    return 0
}

# Verify webhook signature (GitLab)
verify_gitlab_signature() {
    local payload="$1"
    local signature="$2"

    if [ -z "$WEBHOOK_SECRET" ]; then
        warn "No webhook secret configured, skipping signature verification"
        return 0
    fi

    # GitLab uses X-Gitlab-Token header
    if [ -z "$signature" ]; then
        error "No GitLab token provided in webhook"
        return 1
    fi

    if [ "$signature" != "$WEBHOOK_SECRET" ]; then
        error "GitLab token verification failed"
        return 1
    fi

    log "GitLab token verified successfully"
    return 0
}

# Check if repository is allowed
check_repository_allowed() {
    local repo_full_name="$1"

    if [ -z "$ALLOWED_REPOS" ]; then
        log "No repository restrictions configured, allowing all"
        return 0
    fi

    # Check if repository is in allowed list
    for allowed_repo in ${ALLOWED_REPOS//,/ }; do
        if [[ "$repo_full_name" == *"$allowed_repo"* ]]; then
            log "Repository $repo_full_name is allowed"
            return 0
        fi
    done

    error "Repository $repo_full_name is not in allowed list"
    return 1
}

# Parse GitHub webhook payload
parse_github_webhook() {
    local payload="$1"

    # Extract event information
    EVENT_TYPE=$(echo "$payload" | jq -r '.action // "push"')
    REPO_FULL_NAME=$(echo "$payload" | jq -r '.repository.full_name')
    REPO_URL=$(echo "$payload" | jq -r '.repository.clone_url')
    BRANCH=$(echo "$payload" | jq -r '.ref // empty' | sed 's|refs/heads/||')
    COMMIT_SHA=$(echo "$payload" | jq -r '.after // .head_commit.id // empty')
    PR_NUMBER=$(echo "$payload" | jq -r '.number // empty')

    # For pull requests, get the head branch and SHA
    if [ "$EVENT_TYPE" = "opened" ] || [ "$EVENT_TYPE" = "synchronize" ] || [ "$EVENT_TYPE" = "reopened" ]; then
        BRANCH=$(echo "$payload" | jq -r '.pull_request.head.ref')
        COMMIT_SHA=$(echo "$payload" | jq -r '.pull_request.head.sha')
        PR_NUMBER=$(echo "$payload" | jq -r '.number')
    fi

    # For releases
    if [ "$EVENT_TYPE" = "published" ]; then
        BRANCH=$(echo "$payload" | jq -r '.release.target_commitish')
        COMMIT_SHA=$(echo "$payload" | jq -r '.release.tag_name')
    fi

    WEBHOOK_PROVIDER="github"
}

# Parse GitLab webhook payload
parse_gitlab_webhook() {
    local payload="$1"

    EVENT_TYPE=$(echo "$payload" | jq -r '.object_kind // "push"')
    REPO_FULL_NAME=$(echo "$payload" | jq -r '.project.path_with_namespace')
    REPO_URL=$(echo "$payload" | jq -r '.project.git_http_url')
    BRANCH=$(echo "$payload" | jq -r '.ref // empty' | sed 's|refs/heads/||')
    COMMIT_SHA=$(echo "$payload" | jq -r '.after // .checkout_sha // empty')

    # For merge requests
    if [ "$EVENT_TYPE" = "merge_request" ]; then
        BRANCH=$(echo "$payload" | jq -r '.object_attributes.source_branch')
        COMMIT_SHA=$(echo "$payload" | jq -r '.object_attributes.last_commit.id')
        PR_NUMBER=$(echo "$payload" | jq -r '.object_attributes.iid')
    fi

    WEBHOOK_PROVIDER="gitlab"
}

# Determine if scan should be triggered
should_trigger_scan() {
    local event_type="$1"
    local branch="$2"

    # Check event type
    case "$event_type" in
        "push")
            if [ "$SCAN_ON_PUSH" != "true" ]; then
                log "Push events disabled, skipping scan"
                return 1
            fi
            ;;
        "pull_request"|"merge_request"|"opened"|"synchronize"|"reopened")
            if [ "$SCAN_ON_PR" != "true" ]; then
                log "PR events disabled, skipping scan"
                return 1
            fi
            ;;
        "release"|"published")
            if [ "$SCAN_ON_RELEASE" != "true" ]; then
                log "Release events disabled, skipping scan"
                return 1
            fi
            ;;
        *)
            log "Event type '$event_type' not configured for scanning"
            return 1
            ;;
    esac

    # Check if branch is specified and matches
    if [ -n "$branch" ]; then
        log "Scan triggered for event: $event_type, branch: $branch, commit: ${COMMIT_SHA:0:8}"
    else
        log "Scan triggered for event: $event_type, commit: ${COMMIT_SHA:0:8}"
    fi

    return 0
}

# Queue scan for execution
queue_scan() {
    local scan_config="$1"

    # Check queue size
    if [ ${#SCAN_QUEUE[@]} -ge $MAX_SCAN_QUEUE ]; then
        warn "Scan queue is full ($MAX_SCAN_QUEUE), dropping oldest scan"
        SCAN_QUEUE=("${SCAN_QUEUE[@]:1}")
    fi

    SCAN_QUEUE+=("$scan_config")
    log "Scan queued. Queue size: ${#SCAN_QUEUE[@]}"
}

# Execute queued scans
execute_queued_scans() {
    while [ ${#SCAN_QUEUE[@]} -gt 0 ]; do
        local scan_config="${SCAN_QUEUE[0]}"
        SCAN_QUEUE=("${SCAN_QUEUE[@]:1}")

        execute_scan "$scan_config"
    done
}

# Execute single scan
execute_scan() {
    local scan_config="$1"

    # Parse scan configuration
    local repo_url=$(echo "$scan_config" | jq -r '.repo_url')
    local branch=$(echo "$scan_config" | jq -r '.branch')
    local commit_sha=$(echo "$scan_config" | jq -r '.commit_sha')
    local event_type=$(echo "$scan_config" | jq -r '.event_type')
    local pr_number=$(echo "$scan_config" | jq -r '.pr_number // empty')

    local scan_id="${WEBHOOK_ID}_scan_$(date +%s)"
    local scan_dir="${WEBHOOK_RESULTS_DIR}/${WEBHOOK_ID}/scans/${scan_id}"

    log "Executing scan: $scan_id"

    # Create scan directory
    mkdir -p "$scan_dir"

    # Clone repository
    if ! git clone --branch "$branch" --depth 1 "$repo_url" "$scan_dir/repo" >> "${scan_dir}/scan.log" 2>&1; then
        error "Failed to clone repository: $repo_url"
        echo "{\"scan_id\": \"$scan_id\", \"status\": \"failed\", \"error\": \"Clone failed\"}" > "${scan_dir}/result.json"
        return 1
    fi

    cd "$scan_dir/repo"

    # Checkout specific commit if provided
    if [ -n "$commit_sha" ] && [ "$commit_sha" != "null" ]; then
        git checkout "$commit_sha" >> "${scan_dir}/scan.log" 2>&1 || true
    fi

    # Build project
    log "Building project for scan: $scan_id"
    if ! cargo build --release >> "${scan_dir}/scan.log" 2>&1; then
        error "Build failed for scan: $scan_id"
        echo "{\"scan_id\": \"$scan_id\", \"status\": \"failed\", \"error\": \"Build failed\"}" > "${scan_dir}/result.json"
        return 1
    fi

    # Run OSV scan with timeout
    log "Running OSV scan: $scan_id"
    if timeout "$SCAN_TIMEOUT" ./target/release/infinitum-signal scan . --format json > "${scan_dir}/osv_results.json" 2>> "${scan_dir}/scan.log"; then
        # Parse results
        local vuln_count=$(jq '.vulnerabilities | length' "${scan_dir}/osv_results.json" 2>/dev/null || echo "0")
        local high_severity=$(jq '[.vulnerabilities[] | select(.severity == "HIGH" or (.cvss_score // 0) >= 7.0)] | length' "${scan_dir}/osv_results.json" 2>/dev/null || echo "0")

        log "Scan completed: $scan_id - $vuln_count vulnerabilities, $high_severity high severity"

        # Create result summary
        local result="{
            \"scan_id\": \"$scan_id\",
            \"repo_url\": \"$repo_url\",
            \"branch\": \"$branch\",
            \"commit_sha\": \"$commit_sha\",
            \"event_type\": \"$event_type\",
            \"pr_number\": \"$pr_number\",
            \"status\": \"completed\",
            \"vulnerabilities_found\": $vuln_count,
            \"high_severity_count\": $high_severity,
            \"scan_timestamp\": \"$(date -Iseconds)\",
            \"webhook_provider\": \"$WEBHOOK_PROVIDER\"
        }"

        echo "$result" > "${scan_dir}/result.json"

        # Send notification if high severity vulnerabilities found
        if [ "$high_severity" -gt 0 ] && [ -n "$NOTIFICATION_URL" ]; then
            send_notification "$result" "${scan_dir}/osv_results.json"
        fi

    else
        error "Scan timed out or failed: $scan_id"
        echo "{\"scan_id\": \"$scan_id\", \"status\": \"failed\", \"error\": \"Scan timeout\"}" > "${scan_dir}/result.json"
        return 1
    fi
}

# Send notification
send_notification() {
    local result_json="$1"
    local osv_results_file="$2"

    log "Sending notification for high severity vulnerabilities"

    local notification_payload="{
        \"webhook_id\": \"$WEBHOOK_ID\",
        \"scan_result\": $result_json,
        \"osv_results\": $(cat "$osv_results_file"),
        \"notification_timestamp\": \"$(date -Iseconds)\"
    }"

    curl -X POST -H 'Content-type: application/json' --data "$notification_payload" "$NOTIFICATION_URL" || \
        warn "Failed to send notification"
}

# Handle webhook
handle_webhook() {
    local method="$1"
    local payload="$2"
    local signature="$3"
    local provider="${4:-github}"

    log "Handling webhook from $provider"

    # Verify signature/token
    case "$provider" in
        "github")
            if ! verify_github_signature "$payload" "$signature"; then
                return 1
            fi
            parse_github_webhook "$payload"
            ;;
        "gitlab")
            if ! verify_gitlab_signature "$payload" "$signature"; then
                return 1
            fi
            parse_gitlab_webhook "$payload"
            ;;
        *)
            error "Unsupported webhook provider: $provider"
            return 1
            ;;
    esac

    # Check if repository is allowed
    if ! check_repository_allowed "$REPO_FULL_NAME"; then
        return 1
    fi

    # Check if scan should be triggered
    if ! should_trigger_scan "$EVENT_TYPE" "$BRANCH"; then
        log "Scan not triggered for this event"
        return 0
    fi

    # Create scan configuration
    local scan_config="{
        \"repo_url\": \"$REPO_URL\",
        \"branch\": \"$BRANCH\",
        \"commit_sha\": \"$COMMIT_SHA\",
        \"event_type\": \"$EVENT_TYPE\",
        \"pr_number\": \"$PR_NUMBER\",
        \"webhook_provider\": \"$WEBHOOK_PROVIDER\"
    }"

    # Queue scan for execution
    queue_scan "$scan_config"

    log "Webhook processed successfully"
}

# Main execution
main() {
    setup

    # Check if running as CGI script or standalone
    if [ -n "${REQUEST_METHOD:-}" ]; then
        # Running as CGI script
        handle_cgi_webhook
    else
        # Running standalone with arguments
        handle_standalone "$@"
    fi

    # Execute queued scans
    execute_queued_scans

    log "Webhook handler completed"
}

# Handle CGI webhook (for web server integration)
handle_cgi_webhook() {
    # Read payload from stdin
    local payload=""
    if [ "$REQUEST_METHOD" = "POST" ]; then
        payload=$(cat)
    fi

    # Get headers
    local signature="${HTTP_X_HUB_SIGNATURE_256:-${HTTP_X_GITLAB_TOKEN:-}}"
    local event="${HTTP_X_GITHUB_EVENT:-${HTTP_X_GITLAB_EVENT:-}}"

    # Determine provider
    local provider="github"
    if [ -n "${HTTP_X_GITLAB_EVENT:-}" ]; then
        provider="gitlab"
    fi

    handle_webhook "$REQUEST_METHOD" "$payload" "$signature" "$provider"
}

# Handle standalone execution
handle_standalone() {
    if [ $# -lt 2 ]; then
        echo "Usage: $0 <provider> <payload_file> [signature]"
        echo ""
        echo "Providers: github, gitlab"
        echo "Example: $0 github webhook_payload.json 'sha256=...'"
        exit 1
    fi

    local provider="$1"
    local payload_file="$2"
    local signature="${3:-}"

    if [ ! -f "$payload_file" ]; then
        error "Payload file not found: $payload_file"
        exit 1
    fi

    local payload=$(cat "$payload_file")

    handle_webhook "POST" "$payload" "$signature" "$provider"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --provider)
            WEBHOOK_PROVIDER="$2"
            shift 2
            ;;
        --secret)
            WEBHOOK_SECRET="$2"
            shift 2
            ;;
        --allowed-repos)
            ALLOWED_REPOS="$2"
            shift 2
            ;;
        --trigger-events)
            TRIGGER_EVENTS="$2"
            shift 2
            ;;
        --notification-url)
            NOTIFICATION_URL="$2"
            shift 2
            ;;
        --max-queue)
            MAX_SCAN_QUEUE="$2"
            shift 2
            ;;
        --timeout)
            SCAN_TIMEOUT="$2"
            shift 2
            ;;
        --help)
            echo "OSV Webhook Handler"
            echo ""
            echo "Usage: $0 [OPTIONS] <provider> <payload_file> [signature]"
            echo ""
            echo "Options:"
            echo "  --provider PROVIDER     Webhook provider (github, gitlab)"
            echo "  --secret SECRET         Webhook secret for signature verification"
            echo "  --allowed-repos REPOS   Comma-separated list of allowed repositories"
            echo "  --trigger-events EVENTS Comma-separated list of events to trigger scans"
            echo "  --notification-url URL  URL to send notifications for high severity findings"
            echo "  --max-queue N          Maximum number of queued scans"
            echo "  --timeout SECONDS      Scan timeout in seconds"
            echo "  --help                 Show this help message"
            echo ""
            echo "Standalone usage example:"
            echo "  $0 --provider github --secret mysecret github webhook.json 'sha256=...'"
            echo ""
            echo "CGI usage (configure web server to call this script):"
            echo "  Set REQUEST_METHOD, read payload from stdin, set headers as environment variables"
            exit 0
            ;;
        *)
            break
            ;;
    esac
done

# Trap for cleanup on exit
trap 'error "Script interrupted by user"; exit 1' INT TERM

# Run main function
main "$@"