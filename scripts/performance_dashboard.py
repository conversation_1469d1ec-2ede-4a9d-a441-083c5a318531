#!/usr/bin/env python3
"""
Infinitium Signal Performance Dashboard
Generates performance visualizations from monitoring data
"""

import os
import sys
import csv
import json
import argparse
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

class PerformanceDashboard:
    def __init__(self, results_dir: str) -> None:
        self.results_dir = Path(results_dir)
        self.output_dir = self.results_dir / "visualizations"
        self.output_dir.mkdir(exist_ok=True)

    def load_csv_data(self, file_path: Path, columns: List[str]) -> Optional[pd.DataFrame]:
        """Load CSV data with specified column names"""
        try:
            df = pd.read_csv(file_path, header=None, names=columns)
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
            return df
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            return None
    
    def plot_cpu_metrics(self) -> None:
        """Generate CPU performance plots"""
        load_file = self.results_dir / "cpu" / "load_average.csv"
        if not load_file.exists():
            print("CPU load data not found")
            return

        df = self.load_csv_data(load_file, ['timestamp', 'load_1m', 'load_5m', 'load_15m'])
        if df is None:
            return

        plt.figure(figsize=(12, 6))
        plt.plot(df['timestamp'], df['load_1m'], label='1-minute load', linewidth=2)
        plt.plot(df['timestamp'], df['load_5m'], label='5-minute load', linewidth=2)
        plt.plot(df['timestamp'], df['load_15m'], label='15-minute load', linewidth=2)

        plt.title('CPU Load Average Over Time')
        plt.xlabel('Time')
        plt.ylabel('Load Average')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()

        output_file = self.output_dir / "cpu_load.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"CPU load plot saved to {output_file}")
    
    def plot_memory_metrics(self):
        """Generate memory performance plots"""
        memory_file = self.results_dir / "memory" / "memory_usage.csv"
        if not memory_file.exists():
            print("Memory usage data not found")
            return
            
        df = self.load_csv_data(memory_file, ['timestamp', 'total', 'used', 'free', 'available'])
        if df is None:
            return
            
        # Convert bytes to GB
        for col in ['total', 'used', 'free', 'available']:
            df[col] = df[col] / (1024**3)
        
        plt.figure(figsize=(12, 6))
        plt.plot(df['timestamp'], df['used'], label='Used Memory', linewidth=2, color='red')
        plt.plot(df['timestamp'], df['available'], label='Available Memory', linewidth=2, color='green')
        plt.fill_between(df['timestamp'], 0, df['used'], alpha=0.3, color='red')
        plt.fill_between(df['timestamp'], df['used'], df['total'], alpha=0.3, color='green')
        
        plt.title('Memory Usage Over Time')
        plt.xlabel('Time')
        plt.ylabel('Memory (GB)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        output_file = self.output_dir / "memory_usage.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"Memory usage plot saved to {output_file}")
    
    def generate_summary_stats(self) -> Dict[str, Dict[str, float]]:
        """Generate summary statistics"""
        stats: Dict[str, Dict[str, float]] = {}
        
        # CPU stats
        load_file = self.results_dir / "cpu" / "load_average.csv"
        if load_file.exists():
            df = self.load_csv_data(load_file, ['timestamp', 'load_1m', 'load_5m', 'load_15m'])
            if df is not None:
                stats['cpu'] = {
                    'avg_load_1m': df['load_1m'].mean(),
                    'max_load_1m': df['load_1m'].max(),
                    'avg_load_5m': df['load_5m'].mean(),
                    'max_load_5m': df['load_5m'].max(),
                    'avg_load_15m': df['load_15m'].mean(),
                    'max_load_15m': df['load_15m'].max(),
                }
        
        # Memory stats
        memory_file = self.results_dir / "memory" / "memory_usage.csv"
        if memory_file.exists():
            df = self.load_csv_data(memory_file, ['timestamp', 'total', 'used', 'free', 'available'])
            if df is not None:
                # Convert to GB
                for col in ['total', 'used', 'free', 'available']:
                    df[col] = df[col] / (1024**3)
                
                stats['memory'] = {
                    'total_gb': df['total'].iloc[0],
                    'avg_used_gb': df['used'].mean(),
                    'max_used_gb': df['used'].max(),
                    'min_available_gb': df['available'].min(),
                    'avg_utilization_percent': (df['used'] / df['total'] * 100).mean(),
                    'max_utilization_percent': (df['used'] / df['total'] * 100).max(),
                }
        
        # Save stats to JSON
        stats_file = self.output_dir / "performance_stats.json"
        try:
            with open(stats_file, 'w') as f:
                json.dump(stats, f, indent=2)
            print(f"Performance statistics saved to {stats_file}")
        except Exception as e:
            print(f"Error saving statistics: {e}")
        
        return stats
    
    def generate_html_report(self, stats):
        """Generate HTML performance report"""
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Infinitium Signal Performance Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        .header {{ background-color: #2c3e50; color: white; padding: 20px; border-radius: 5px; }}
        .metric-card {{ background-color: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }}
        .chart {{ text-align: center; margin: 20px 0; }}
        .stats {{ display: flex; flex-wrap: wrap; gap: 20px; }}
        .stat-item {{ flex: 1; min-width: 200px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Infinitium Signal Performance Report</h1>
        <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <h2>Performance Overview</h2>
    <div class="stats">
"""
        
        if 'cpu' in stats:
            cpu_stats = stats['cpu']
            html_content += f"""
        <div class="stat-item">
            <div class="metric-card">
                <h3>CPU Performance</h3>
                <p><strong>Average 1m Load:</strong> {cpu_stats['avg_load_1m']:.2f}</p>
                <p><strong>Maximum 1m Load:</strong> {cpu_stats['max_load_1m']:.2f}</p>
                <p><strong>Average 5m Load:</strong> {cpu_stats['avg_load_5m']:.2f}</p>
                <p><strong>Maximum 5m Load:</strong> {cpu_stats['max_load_5m']:.2f}</p>
            </div>
        </div>
"""
        
        if 'memory' in stats:
            mem_stats = stats['memory']
            html_content += f"""
        <div class="stat-item">
            <div class="metric-card">
                <h3>Memory Performance</h3>
                <p><strong>Total Memory:</strong> {mem_stats['total_gb']:.1f} GB</p>
                <p><strong>Average Used:</strong> {mem_stats['avg_used_gb']:.1f} GB</p>
                <p><strong>Maximum Used:</strong> {mem_stats['max_used_gb']:.1f} GB</p>
                <p><strong>Average Utilization:</strong> {mem_stats['avg_utilization_percent']:.1f}%</p>
                <p><strong>Peak Utilization:</strong> {mem_stats['max_utilization_percent']:.1f}%</p>
            </div>
        </div>
"""
        
        html_content += """
    </div>
    
    <h2>Performance Charts</h2>
    <div class="chart">
        <h3>CPU Load Average</h3>
        <img src="cpu_load.png" alt="CPU Load Chart" style="max-width: 100%; height: auto;">
    </div>
    
    <div class="chart">
        <h3>Memory Usage</h3>
        <img src="memory_usage.png" alt="Memory Usage Chart" style="max-width: 100%; height: auto;">
    </div>
    
    <h2>System Information</h2>
    <div class="metric-card">
        <p>For detailed system information, see the system_info.txt file in the results directory.</p>
    </div>
    
</body>
</html>
"""
        
        html_file = self.output_dir / "performance_report.html"
        with open(html_file, 'w') as f:
            f.write(html_content)
        print(f"HTML report saved to {html_file}")
    
    def generate_dashboard(self):
        """Generate complete performance dashboard"""
        print(f"Generating performance dashboard for {self.results_dir}")
        
        # Generate plots
        self.plot_cpu_metrics()
        self.plot_memory_metrics()
        
        # Generate statistics
        stats = self.generate_summary_stats()
        
        # Generate HTML report
        self.generate_html_report(stats)
        
        print(f"\nPerformance dashboard generated in {self.output_dir}")
        print("Files created:")
        for file in self.output_dir.iterdir():
            print(f"  - {file.name}")

def main():
    parser = argparse.ArgumentParser(description='Generate Infinitium Signal Performance Dashboard')
    parser.add_argument('results_dir', help='Path to performance results directory')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.results_dir):
        print(f"Error: Results directory {args.results_dir} does not exist")
        sys.exit(1)
    
    try:
        dashboard = PerformanceDashboard(args.results_dir)
        dashboard.generate_dashboard()
    except Exception as e:
        print(f"Error generating dashboard: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
