#!/bin/bash

# Infinitium Signal Performance Monitoring Script
# Captures comprehensive system metrics during benchmark execution

set -euo pipefail
IFS=$'\n\t'

# Configuration
MONITOR_DURATION=${1:-300}  # Default 5 minutes
OUTPUT_DIR="./performance_results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RESULTS_DIR="${OUTPUT_DIR}/${TIMESTAMP}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Create results directory
setup_monitoring() {
    log "Setting up performance monitoring..."
    mkdir -p "$RESULTS_DIR"
    
    # Create subdirectories for different metrics
    mkdir -p "$RESULTS_DIR"/{cpu,memory,disk,network,system,docker}
    
    log "Results will be saved to: $RESULTS_DIR"
}

# Get system information
collect_system_info() {
    log "Collecting system information..."
    
    {
        echo "=== SYSTEM INFORMATION ==="
        echo "Timestamp: $(date)"
        echo "Hostname: $(hostname)"
        echo "Kernel: $(uname -r)"
        echo "OS: $(lsb_release -d 2>/dev/null | cut -f2 || echo "Unknown")"
        echo ""
        
        echo "=== CPU INFORMATION ==="
        lscpu
        echo ""
        
        echo "=== MEMORY INFORMATION ==="
        free -h
        echo ""
        
        echo "=== DISK INFORMATION ==="
        df -h
        echo ""
        
        echo "=== NETWORK INTERFACES ==="
        ip addr show
        echo ""
        
        echo "=== DOCKER INFORMATION ==="
        if command -v docker &> /dev/null; then
            docker version 2>/dev/null || echo "Docker not running"
            docker system df 2>/dev/null || echo "Docker system info unavailable"
        else
            echo "Docker not installed"
        fi
    } > "$RESULTS_DIR/system_info.txt"
}

# Monitor CPU usage
monitor_cpu() {
    log "Starting CPU monitoring..."
    
    # Overall CPU usage
    iostat -c 1 "$MONITOR_DURATION" > "$RESULTS_DIR/cpu/iostat_cpu.log" &
    
    # Per-core CPU usage
    mpstat -P ALL 1 "$MONITOR_DURATION" > "$RESULTS_DIR/cpu/mpstat_cores.log" &
    
    # CPU load average
    while true; do
        echo "$(date +%s),$(uptime | awk -F'load average:' '{print $2}' | tr -d ' ')" >> "$RESULTS_DIR/cpu/load_average.csv"
        sleep 1
    done &
    CPU_LOAD_PID=$!
    
    # Top processes by CPU
    while true; do
        echo "=== $(date) ===" >> "$RESULTS_DIR/cpu/top_processes.log"
        ps aux --sort=-%cpu | head -20 >> "$RESULTS_DIR/cpu/top_processes.log"
        echo "" >> "$RESULTS_DIR/cpu/top_processes.log"
        sleep 5
    done &
    CPU_TOP_PID=$!
}

# Monitor memory usage
monitor_memory() {
    log "Starting memory monitoring..."
    
    # Memory usage over time
    while true; do
        timestamp=$(date +%s)
        memory_info=$(free -b | grep '^Mem:')
        total=$(echo $memory_info | awk '{print $2}')
        used=$(echo $memory_info | awk '{print $3}')
        free=$(echo $memory_info | awk '{print $4}')
        available=$(echo $memory_info | awk '{print $7}')
        
        echo "$timestamp,$total,$used,$free,$available" >> "$RESULTS_DIR/memory/memory_usage.csv"
        sleep 1
    done &
    MEMORY_PID=$!
    
    # Memory by process
    while true; do
        echo "=== $(date) ===" >> "$RESULTS_DIR/memory/process_memory.log"
        ps aux --sort=-%mem | head -20 >> "$RESULTS_DIR/memory/process_memory.log"
        echo "" >> "$RESULTS_DIR/memory/process_memory.log"
        sleep 5
    done &
    MEMORY_PROC_PID=$!
    
    # Swap usage
    while true; do
        timestamp=$(date +%s)
        swap_info=$(free -b | grep '^Swap:')
        if [ -n "$swap_info" ]; then
            swap_total=$(echo $swap_info | awk '{print $2}')
            swap_used=$(echo $swap_info | awk '{print $3}')
            echo "$timestamp,$swap_total,$swap_used" >> "$RESULTS_DIR/memory/swap_usage.csv"
        fi
        sleep 1
    done &
    SWAP_PID=$!
}

# Monitor disk I/O
monitor_disk() {
    log "Starting disk I/O monitoring..."
    
    # Disk I/O statistics
    iostat -d 1 "$MONITOR_DURATION" > "$RESULTS_DIR/disk/iostat_disk.log" &
    
    # Disk usage by mount point
    while true; do
        timestamp=$(date +%s)
        df -B1 | grep -E '^/dev/' | while read line; do
            device=$(echo $line | awk '{print $1}')
            total=$(echo $line | awk '{print $2}')
            used=$(echo $line | awk '{print $3}')
            available=$(echo $line | awk '{print $4}')
            mount=$(echo $line | awk '{print $6}')
            echo "$timestamp,$device,$total,$used,$available,$mount" >> "$RESULTS_DIR/disk/disk_usage.csv"
        done
        sleep 5
    done &
    DISK_USAGE_PID=$!
    
    # I/O wait and queue depth
    while true; do
        timestamp=$(date +%s)
        iowait=$(iostat -c 1 1 | grep -A1 "avg-cpu" | tail -1 | awk '{print $4}')
        echo "$timestamp,$iowait" >> "$RESULTS_DIR/disk/iowait.csv"
        sleep 1
    done &
    IOWAIT_PID=$!
}

# Monitor network usage
monitor_network() {
    log "Starting network monitoring..."
    
    # Network interface statistics
    while true; do
        timestamp=$(date +%s)
        cat /proc/net/dev | grep -E '(eth|ens|enp|wlan)' | while read line; do
            interface=$(echo $line | cut -d: -f1 | tr -d ' ')
            stats=$(echo $line | cut -d: -f2)
            rx_bytes=$(echo $stats | awk '{print $1}')
            tx_bytes=$(echo $stats | awk '{print $9}')
            echo "$timestamp,$interface,$rx_bytes,$tx_bytes" >> "$RESULTS_DIR/network/interface_stats.csv"
        done
        sleep 1
    done &
    NETWORK_PID=$!
    
    # Network connections
    while true; do
        echo "=== $(date) ===" >> "$RESULTS_DIR/network/connections.log"
        netstat -tuln >> "$RESULTS_DIR/network/connections.log"
        echo "" >> "$RESULTS_DIR/network/connections.log"
        sleep 10
    done &
    NETSTAT_PID=$!
}

# Monitor Docker containers (if applicable)
monitor_docker() {
    if ! command -v docker &> /dev/null; then
        warn "Docker not found, skipping container monitoring"
        return
    fi
    
    log "Starting Docker container monitoring..."
    
    # Container resource usage
    while true; do
        timestamp=$(date +%s)
        docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}" 2>/dev/null | tail -n +2 | while read line; do
            echo "$timestamp,$line" >> "$RESULTS_DIR/docker/container_stats.csv"
        done
        sleep 2
    done &
    DOCKER_PID=$!
}

# Monitor OSV vulnerability scanning
monitor_osv_scan() {
    if [ "$RUN_OSV_SCAN" != "true" ]; then
        warn "OSV scanning disabled, skipping"
        return
    fi

    log "Starting OSV vulnerability scanning..."

    # Create OSV results directory
    mkdir -p "$RESULTS_DIR/osv"

    # Initial scan
    run_osv_scan

    # Schedule periodic scans
    while true; do
        sleep "$OSV_SCAN_INTERVAL"
        run_osv_scan
    done &
    OSV_PID=$!
}

# Run OSV scan
run_osv_scan() {
    local scan_timestamp=$(date +%s)
    local scan_file="$RESULTS_DIR/osv/scan_${scan_timestamp}.json"

    log "Running OSV scan at $(date)"

    # Check if binary exists
    if [ ! -f "./target/release/infinitum-signal" ]; then
        warn "Infinitium Signal binary not found, building..."
        if ! cargo build --release >> "$RESULTS_DIR/osv/build.log" 2>&1; then
            error "Failed to build project for OSV scanning"
            return 1
        fi
    fi
    # Kill all background monitoring processes
    for pid in $CPU_LOAD_PID $CPU_TOP_PID $MEMORY_PID $MEMORY_PROC_PID $SWAP_PID \
               $DISK_USAGE_PID $IOWAIT_PID $NETWORK_PID $NETSTAT_PID $DOCKER_PID \
               $APP_PID $POSTGRES_PID $OSV_PID; do

    # Run OSV scan
    if ./target/release/infinitum-signal scan . --format json > "$scan_file" 2>> "$RESULTS_DIR/osv/scan.log"; then
        # Parse results
        local vuln_count=$(jq '.vulnerabilities | length' "$scan_file" 2>/dev/null || echo "0")
        local high_severity=$(jq '[.vulnerabilities[] | select(.severity == "HIGH" or (.cvss_score // 0) >= 7.0)] | length' "$scan_file" 2>/dev/null || echo "0")
        local scan_duration=$(jq '.scan_duration // 0' "$scan_file" 2>/dev/null || echo "0")

        log "OSV scan completed: $vuln_count vulnerabilities found, $high_severity high severity"

        # Log to CSV for performance tracking
        echo "$scan_timestamp,$vuln_count,$high_severity,$scan_duration" >> "$RESULTS_DIR/osv/scan_performance.csv"

        # Alert on high severity vulnerabilities
        if [ "$high_severity" -gt 0 ]; then
            warn "High severity vulnerabilities detected: $high_severity"
            # Could add notification logic here
        fi
    else
        error "OSV scan failed"
        echo "$scan_timestamp,failed,0,0" >> "$RESULTS_DIR/osv/scan_performance.csv"
    fi
}
# Monitor Infinitium Signal specific metrics
monitor_application() {
    log "Starting application-specific monitoring..."
    
    # Monitor Infinitium Signal processes
    while true; do
        timestamp=$(date +%s)
    # Start all monitoring functions
    monitor_cpu
    monitor_memory
    monitor_disk
    monitor_network
    monitor_docker
    monitor_application
    monitor_osv_scan
        pgrep -f "infinitum-signal" | while read pid; do
            if [ -n "$pid" ]; then
                cpu_usage=$(ps -p $pid -o %cpu --no-headers 2>/dev/null || echo "0")
                mem_usage=$(ps -p $pid -o %mem --no-headers 2>/dev/null || echo "0")
                rss=$(ps -p $pid -o rss --no-headers 2>/dev/null || echo "0")
                echo "$timestamp,$pid,$cpu_usage,$mem_usage,$rss" >> "$RESULTS_DIR/system/infinitum_signal_processes.csv"
            fi
        done
        sleep 1
    done &
    APP_PID=$!
    
    # Monitor PostgreSQL if running
    if pgrep postgres > /dev/null; then
        while true; do
            timestamp=$(date +%s)
            postgres_pid=$(pgrep postgres | head -1)
            if [ -n "$postgres_pid" ]; then
                cpu_usage=$(ps -p $postgres_pid -o %cpu --no-headers 2>/dev/null || echo "0")
                mem_usage=$(ps -p $postgres_pid -o %mem --no-headers 2>/dev/null || echo "0")
                echo "$timestamp,$postgres_pid,$cpu_usage,$mem_usage" >> "$RESULTS_DIR/system/postgres_stats.csv"
            fi
            sleep 1
# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    if [[ "${1:-}" == "--help" || "${1:-}" == "-h" ]]; then
        echo "Infinitium Signal Performance Monitoring Script"
        echo ""
        echo "Usage: $0 [MONITOR_DURATION] [RUN_OSV_SCAN] [OSV_SCAN_INTERVAL]"
        echo ""
        echo "Arguments:"
        echo "  MONITOR_DURATION   Duration to monitor in seconds (default: 300)"
        echo "  RUN_OSV_SCAN       Enable OSV scanning (true/false, default: false)"
        echo "  OSV_SCAN_INTERVAL  OSV scan interval in seconds (default: 300)"
        echo ""
        echo "Examples:"
        echo "  $0                    # Monitor for 5 minutes without OSV scanning"
        echo "  $0 600 true 180      # Monitor for 10 minutes with OSV scanning every 3 minutes"
        echo "  $0 300 true           # Monitor for 5 minutes with OSV scanning every 5 minutes"
        exit 0
    fi

    check_dependencies
    main "$@"
fi
        done &
        POSTGRES_PID=$!
    fi
}

# Cleanup function
cleanup() {
    log "Stopping monitoring processes..."
    
    # Kill all background monitoring processes
    for pid in $CPU_LOAD_PID $CPU_TOP_PID $MEMORY_PID $MEMORY_PROC_PID $SWAP_PID \
               $DISK_USAGE_PID $IOWAIT_PID $NETWORK_PID $NETSTAT_PID $DOCKER_PID \
               $APP_PID $POSTGRES_PID; do
        if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
            kill "$pid" 2>/dev/null || true
        fi
    done
    
    # Wait a moment for processes to terminate
    sleep 2
    
    # Force kill any remaining processes
    pkill -f "iostat|mpstat" 2>/dev/null || true
    
    log "Monitoring stopped. Results saved to: $RESULTS_DIR"
}

# Signal handlers
trap cleanup EXIT INT TERM

# Main execution
main() {
    log "Starting Infinitium Signal Performance Monitoring"
    log "Duration: ${MONITOR_DURATION} seconds"
    
    setup_monitoring
    collect_system_info
    
    # Start all monitoring functions
    monitor_cpu
    monitor_memory
    monitor_disk
    monitor_network
    monitor_docker
    monitor_application
    
    log "All monitoring processes started. Monitoring for ${MONITOR_DURATION} seconds..."
    
    # Wait for the specified duration
    sleep "$MONITOR_DURATION"
    
    log "Monitoring period completed"
}

# Check dependencies
check_dependencies() {
    local missing_deps=()
    
    for cmd in iostat mpstat ps free df netstat; do
        if ! command -v "$cmd" &> /dev/null; then
            missing_deps+=("$cmd")
        fi
    done
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        error "Missing required dependencies: ${missing_deps[*]}"
        echo "Please install the following packages:"
        echo "  Ubuntu/Debian: sudo apt-get install sysstat net-tools procps"
        echo "  CentOS/RHEL: sudo yum install sysstat net-tools procps"
        exit 1
    fi
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    check_dependencies
    main "$@"
fi
