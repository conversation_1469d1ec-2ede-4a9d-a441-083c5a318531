#!/bin/bash

# Test runner script for Infinitium Signal
# This script runs all tests including unit, integration, and security tests

set -euo pipefail
IFS=$'\n\t'

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TEST_RESULTS_DIR="${PROJECT_ROOT}/target/test-results"
COVERAGE_DIR="${PROJECT_ROOT}/target/coverage"
LOG_FILE="${TEST_RESULTS_DIR}/test.log"

# Test configuration
RUN_UNIT_TESTS=true
RUN_INTEGRATION_TESTS=true
RUN_SECURITY_TESTS=true
RUN_PERFORMANCE_TESTS=false
GENERATE_COVERAGE=true
PARALLEL_TESTS=true

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --unit-only)
            RUN_INTEGRATION_TESTS=false
            RUN_SECURITY_TESTS=false
            RUN_PERFORMANCE_TESTS=false
            shift
            ;;
        --integration-only)
            RUN_UNIT_TESTS=false
            RUN_SECURITY_TESTS=false
            RUN_PERFORMANCE_TESTS=false
            shift
            ;;
        --security-only)
            RUN_UNIT_TESTS=false
            RUN_INTEGRATION_TESTS=false
            RUN_PERFORMANCE_TESTS=false
            shift
            ;;
        --with-performance)
            RUN_PERFORMANCE_TESTS=true
            shift
            ;;
        --no-coverage)
            GENERATE_COVERAGE=false
            shift
            ;;
        --sequential)
            PARALLEL_TESTS=false
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --unit-only         Run only unit tests"
            echo "  --integration-only  Run only integration tests"
            echo "  --security-only     Run only security tests"
            echo "  --with-performance  Include performance tests"
             echo "  --security-only     Run only security tests (including OSV scanning)"
             shift
             ;;
            echo "  --no-coverage       Skip coverage generation"
            echo "  --sequential        Run tests sequentially"
            echo "  --help              Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print section headers
print_section() {
    echo
    print_status $BLUE "=================================================="
    print_status $BLUE "$1"
    print_status $BLUE "=================================================="
    echo
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to setup test environment
setup_test_environment() {
    print_section "Setting up test environment"
    
    # Create test directories
    mkdir -p "$TEST_RESULTS_DIR"
    mkdir -p "$COVERAGE_DIR"
    
    # Initialize log file
    echo "Test run started at $(date)" > "$LOG_FILE"
    
    # Check required tools
    if ! command_exists cargo; then
        print_status $RED "Error: cargo not found. Please install Rust."
        exit 1
    fi
    
    if $GENERATE_COVERAGE && ! command_exists cargo-tarpaulin; then
        print_status $YELLOW "Installing cargo-tarpaulin for coverage..."
        cargo install cargo-tarpaulin
    fi
    
    if $RUN_SECURITY_TESTS && ! command_exists cargo-audit; then
        print_status $YELLOW "Installing cargo-audit for security tests..."
        cargo install cargo-audit
    fi
    
    # Set environment variables for testing
    if $PARALLEL_TESTS; then
        unset RUST_TEST_THREADS  # Let Rust determine optimal thread count
    else
        export RUST_TEST_THREADS=1
    fi
    export RUST_BACKTRACE=1
    export DATABASE_URL="postgres://test:test@localhost:5432/infinitium_signal_test"
    export REDIS_URL="redis://localhost:6379/1"
    
    print_status $GREEN "Test environment setup complete"
}

# Function to run unit tests
run_unit_tests() {
    if ! $RUN_UNIT_TESTS; then
        return 0
    fi

    print_section "Running Unit Tests"

    if cargo test --lib 2>&1 | tee -a "$LOG_FILE"; then
        print_status $GREEN "✓ Unit tests passed"
        return 0
    else
        print_status $RED "✗ Unit tests failed"
        return 1
    fi
}

# Function to run integration tests
run_integration_tests() {
    if ! $RUN_INTEGRATION_TESTS; then
        return 0
    fi

    print_section "Running Integration Tests"

    # Start test database if needed
    if command_exists docker && command_exists pg_isready; then
        if ! pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
            print_status $YELLOW "Starting test database..."
            docker run -d --name infinitium-test-db \
                -e POSTGRES_DB=infinitium_signal_test \
                -e POSTGRES_USER=test \
                -e POSTGRES_PASSWORD=test \
                -p 5432:5432 \
                postgres:15

            # Wait for database to be ready
            for i in {1..30}; do
                if pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
                    break
                fi
                sleep 1
            done
        fi
    else
        print_status $YELLOW "Docker or pg_isready not available, skipping database setup"
    fi

    # Run unit test files as integration tests
    if cargo test --test compliance_tests --test scanner_tests 2>&1 | tee -a "$LOG_FILE"; then
        print_status $GREEN "✓ Integration tests passed"
        return 0
    else
        print_status $RED "✗ Integration tests failed"
        return 1
    fi
}

# Function to run security tests
run_security_tests() {
    if ! $RUN_SECURITY_TESTS; then
        return 0
    fi

    print_section "Running Security Tests"

    local security_passed=true

    # Run cargo audit
    print_status $BLUE "Running cargo audit..."
    if cargo audit 2>&1 | tee -a "$LOG_FILE"; then
        print_status $GREEN "✓ Cargo audit passed"
    else
        print_status $RED "✗ Cargo audit found vulnerabilities"
        security_passed=false
    fi

    # Run cargo deny (if available)
    if command_exists cargo-deny; then
        print_status $BLUE "Running cargo deny..."
        if cargo deny check 2>&1 | tee -a "$LOG_FILE"; then
            print_status $GREEN "✓ Cargo deny passed"
        else
            print_status $RED "✗ Cargo deny found issues"
            security_passed=false
        fi
    fi

    # Run all tests (including security-related ones)
    print_status $BLUE "Running test suite..."
    if cargo test 2>&1 | tee -a "$LOG_FILE"; then
        print_status $GREEN "✓ Tests passed"
    else
        print_status $RED "✗ Tests failed"
        security_passed=false
    fi

    if $security_passed; then
        return 0
    else
        return 1
    fi
# Function to run OSV vulnerability scan
run_osv_scan() {
    if ! $RUN_SECURITY_TESTS; then
        return 0
    fi

    print_section "Running OSV Vulnerability Scan"

    local osv_passed=true

    # Check if the binary exists
    if ! cargo build --release >> "$LOG_FILE" 2>&1; then
        print_status $RED "✗ Failed to build project for OSV scanning"
        osv_passed=false
    else
        # Run OSV scan
        print_status $BLUE "Running OSV scan..."
        if ./target/release/infinitum-signal scan . --format json > "${TEST_RESULTS_DIR}/osv_scan_results.json" 2>> "$LOG_FILE"; then
            # Parse results
            local vuln_count=$(jq '.vulnerabilities | length' "${TEST_RESULTS_DIR}/osv_scan_results.json" 2>/dev/null || echo "0")
            local high_severity=$(jq '[.vulnerabilities[] | select(.severity == "HIGH" or (.cvss_score // 0) >= 7.0)] | length' "${TEST_RESULTS_DIR}/osv_scan_results.json" 2>/dev/null || echo "0")
            local medium_severity=$(jq '[.vulnerabilities[] | select(.severity == "MEDIUM" or ((.cvss_score // 0) >= 4.0 and (.cvss_score // 0) < 7.0))] | length' "${TEST_RESULTS_DIR}/osv_scan_results.json" 2>/dev/null || echo "0")

            print_status $GREEN "✓ OSV scan completed"
            echo "  Total vulnerabilities: $vuln_count"
            echo "  High severity: $high_severity"
            echo "  Medium severity: $medium_severity"

            # Save results for reporting
            echo "OSV_SCAN_VULNERABILITIES=$vuln_count" >> "${TEST_RESULTS_DIR}/test_metrics.txt"
            echo "OSV_SCAN_HIGH_SEVERITY=$high_severity" >> "${TEST_RESULTS_DIR}/test_metrics.txt"
            echo "OSV_SCAN_MEDIUM_SEVERITY=$medium_severity" >> "${TEST_RESULTS_DIR}/test_metrics.txt"

            # Check for high severity vulnerabilities
            if [ "$high_severity" -gt 0 ]; then
                print_status $RED "✗ High severity vulnerabilities found: $high_severity"
                osv_passed=false
            fi

            # Generate summary of findings
            if [ "$vuln_count" -gt 0 ]; then
                echo "" >> "$LOG_FILE"
                echo "=== OSV VULNERABILITY FINDINGS ===" >> "$LOG_FILE"
                jq -r '.vulnerabilities[] | "• \(.id): \(.summary) (Severity: \(.severity // "Unknown"))"' "${TEST_RESULTS_DIR}/osv_scan_results.json" >> "$LOG_FILE" 2>/dev/null || true
                echo "" >> "$LOG_FILE"
            fi

        else
            print_status $RED "✗ OSV scan failed"
            osv_passed=false
        fi
    fi

    if $osv_passed; then
        return 0
    else
        return 1
    fi
}
}

# Function to run performance tests
run_performance_tests() {
    if ! $RUN_PERFORMANCE_TESTS; then
        return 0
    fi
    
    print_section "Running Performance Tests"
    
    if cargo test --release performance 2>&1 | tee -a "$LOG_FILE"; then
        print_status $GREEN "✓ Performance tests passed"
        return 0
    else
        print_status $RED "✗ Performance tests failed"
        return 1
    fi
}

# Function to generate coverage report
generate_coverage() {
    if ! $GENERATE_COVERAGE; then
        return 0
    fi

    print_section "Generating Coverage Report"

    if cargo tarpaulin --out Html --output-dir "$COVERAGE_DIR" 2>&1 | tee -a "$LOG_FILE"; then
        print_status $GREEN "✓ Coverage report generated at $COVERAGE_DIR"

        # Extract coverage percentage
        local coverage_pct=$(grep -o '[0-9]*\.[0-9]*%' "$LOG_FILE" | tail -1)
        if [[ -n "$coverage_pct" ]]; then
            print_status $BLUE "Coverage: $coverage_pct"
        fi

        return 0
    else
        print_status $RED "✗ Coverage generation failed"
        return 1
    fi
    if ! run_security_tests; then
        exit_code=1
    fi

    if ! run_osv_scan; then
        exit_code=1
    fi
}

# Function to cleanup test environment
cleanup_test_environment() {
    print_section "Cleaning up test environment"
    
    # Stop test database if we started it
    if docker ps | grep -q infinitium-test-db; then
        print_status $BLUE "Stopping test database..."
        docker stop infinitium-test-db >/dev/null 2>&1
        docker rm infinitium-test-db >/dev/null 2>&1
    fi
    
    print_status $GREEN "Cleanup complete"
}

# Function to generate test report
generate_test_report() {
    print_section "Test Summary"
    
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    
    # Count test results from log
    if [[ -f "$LOG_FILE" ]]; then
        total_tests=$(grep -c "test result:" "$LOG_FILE" || echo "0")
        passed_tests=$(grep "test result: ok" "$LOG_FILE" | wc -l || echo "0")
        failed_tests=$(grep "test result: FAILED" "$LOG_FILE" | wc -l || echo "0")
    fi
    
    echo "Test Results:"
    echo "  Total test suites: $total_tests"
    echo "  Passed: $passed_tests"
    echo "  Failed: $failed_tests"
    echo
    echo "Logs available at: $LOG_FILE"
    
    if $GENERATE_COVERAGE && [[ -d "$COVERAGE_DIR" ]]; then
        echo "Coverage report: $COVERAGE_DIR/index.html"
    fi
    
    echo "Test results: $TEST_RESULTS_DIR"
}

# Main execution
main() {
    local start_time=$(date +%s)
    local exit_code=0
    
    print_status $BLUE "Starting Infinitium Signal Test Suite"
    print_status $BLUE "Project root: $PROJECT_ROOT"
    
    # Setup
    setup_test_environment
    
    # Run tests
    if ! run_unit_tests; then
        exit_code=1
    fi
    
    if ! run_integration_tests; then
        exit_code=1
    fi
    
    if ! run_security_tests; then
        exit_code=1
    fi
    
    if ! run_performance_tests; then
        exit_code=1
    fi
    
    # Generate coverage
    if ! generate_coverage; then
        print_status $YELLOW "Warning: Coverage generation failed"
    fi
    
    # Cleanup
    cleanup_test_environment
    
    # Generate report
    generate_test_report
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo
    if [[ $exit_code -eq 0 ]]; then
        print_status $GREEN "✓ All tests passed! (${duration}s)"
    else
        print_status $RED "✗ Some tests failed! (${duration}s)"
    fi
    
    exit $exit_code
}

# Trap to ensure cleanup on exit
trap cleanup_test_environment EXIT

# Run main function
main "$@"
