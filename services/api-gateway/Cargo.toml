[package]
name = "api-gateway"
version = "0.1.0"
edition = "2021"

[dependencies]
actix-web = "4.0"
tokio = { version = "1.0", features = ["full"] }
tonic = "0.8"
prost = "0.11"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
reqwest = { version = "0.11", default-features = false, features = ["json", "rustls-tls"] }
governor = "0.5"
jsonwebtoken = "8.0"
prometheus = "0.13"
clap = { version = "4.0", features = ["derive"] }
env_logger = "0.10"
log = "0.4"
futures = "0.3"
futures-util = "0.3"
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"
thiserror = "1.0"
lazy_static = "1.4"

[build-dependencies]
tonic-build = "0.8"