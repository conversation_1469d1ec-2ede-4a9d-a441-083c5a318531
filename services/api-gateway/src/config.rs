use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Config {
    pub host: String,
    pub port: u16,
    pub jwt_secret: String,
    pub rate_limit_requests: u32,
    pub rate_limit_window_secs: u64,
    pub scanning_service_addr: String,
    pub compliance_service_addr: String,
    pub blockchain_service_addr: String,
}

impl Config {
    pub fn from_env() -> Self {
        Self {
            host: env::var("HOST").unwrap_or_else(|_| "127.0.0.1".to_string()),
            port: env::var("PORT")
                .unwrap_or_else(|_| "8080".to_string())
                .parse()
                .expect("Invalid PORT"),
            jwt_secret: env::var("JWT_SECRET")
                .unwrap_or_else(|_| "your-secret-key".to_string()),
            rate_limit_requests: env::var("RATE_LIMIT_REQUESTS")
                .unwrap_or_else(|_| "100".to_string())
                .parse()
                .expect("Invalid RATE_LIMIT_REQUESTS"),
            rate_limit_window_secs: env::var("RATE_LIMIT_WINDOW_SECS")
                .unwrap_or_else(|_| "60".to_string())
                .parse()
                .expect("Invalid RATE_LIMIT_WINDOW_SECS"),
            scanning_service_addr: env::var("SCANNING_SERVICE_ADDR")
                .unwrap_or_else(|_| "http://localhost:8081".to_string()),
            compliance_service_addr: env::var("COMPLIANCE_SERVICE_ADDR")
                .unwrap_or_else(|_| "http://localhost:8082".to_string()),
            blockchain_service_addr: env::var("BLOCKCHAIN_SERVICE_ADDR")
                .unwrap_or_else(|_| "http://localhost:8083".to_string()),
        }
    }
}