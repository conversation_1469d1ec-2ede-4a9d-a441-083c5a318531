use actix_web::{HttpResponse, Result};
use serde_json::json;

pub async fn health_check() -> Result<HttpResponse> {
    let health_response = json!({
        "status": "healthy",
        "version": env!("CARGO_PKG_VERSION"),
        "timestamp": chrono::Utc::now(),
        "uptime": std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs(),
        "services": {
            "scanning": "unknown",  // TODO: Check actual service health
            "compliance": "unknown",
            "blockchain": "unknown"
        }
    });

    Ok(HttpResponse::Ok().json(health_response))
}

pub async fn liveness_probe() -> Result<HttpResponse> {
    // Simple liveness check - if we can respond, we're alive
    Ok(HttpResponse::Ok().json(json!({"status": "alive"})))
}

pub async fn readiness_probe() -> Result<HttpResponse> {
    // TODO: Check if all dependencies are ready
    // For now, assume ready
    Ok(HttpResponse::Ok().json(json!({"status": "ready"})))
}