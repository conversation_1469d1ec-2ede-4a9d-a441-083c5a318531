mod handlers;
mod middleware;
mod grpc_clients;
mod health;
mod metrics;
mod config;

use actix_web::{web, App, HttpServer, middleware as actix_middleware};
use std::sync::Arc;
use tokio::sync::Mutex;
use crate::config::Config;
use crate::metrics::MetricsCollector;

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    env_logger::init();

    let config = Config::from_env();

    let metrics = Arc::new(Mutex::new(MetricsCollector::new()));

    log::info!("Starting API Gateway on {}:{}", config.host, config.port);

    let config_for_app = config.clone();
    let server = HttpServer::new(move || {
        App::new()
            .app_data(web::Data::new(metrics.clone()))
            .app_data(web::Data::new(config_for_app.clone()))
            .wrap(actix_middleware::Logger::default())
            .wrap(crate::middleware::auth::AuthMiddleware {
                jwt_secret: config.jwt_secret.clone(),
            })
            .wrap(crate::middleware::rate_limit::RateLimitMiddleware::new(
                config.rate_limit_requests,
                config.rate_limit_window_secs,
            ))
            .wrap(crate::middleware::transform::TransformMiddleware)
            .service(
                web::scope("/api")
                    .configure(crate::handlers::configure_routes)
            )
            .route("/health", web::get().to(crate::health::health_check))
            .route("/metrics", web::get().to(crate::metrics::metrics_endpoint))
            // Auth endpoints
            .route("/auth/login", web::post().to(crate::handlers::auth_login))
            .route("/auth/refresh", web::post().to(crate::handlers::auth_refresh))
    })
    .bind(format!("{}:{}", config.host, config.port))?
    .shutdown_timeout(30); // 30 seconds graceful shutdown

    log::info!("Server starting with graceful shutdown enabled");
    server.run().await
}