use prometheus::{Encoder, TextEncoder, register_counter, register_histogram, register_gauge, Counter, Histogram, Gauge};
use std::collections::HashMap;
use lazy_static::lazy_static;

lazy_static! {
    static ref HTTP_REQUESTS_TOTAL: Counter = register_counter!(
        "api_gateway_http_requests_total",
        "Total number of HTTP requests"
    ).expect("Can't create metric");

    static ref HTTP_REQUEST_DURATION: Histogram = register_histogram!(
        "api_gateway_http_request_duration_seconds",
        "HTTP request duration in seconds"
    ).expect("Can't create metric");

    static ref ACTIVE_CONNECTIONS: Gauge = register_gauge!(
        "api_gateway_active_connections",
        "Number of active connections"
    ).expect("Can't create metric");
}

pub struct MetricsCollector {
    custom_metrics: HashMap<String, String>,
}

impl MetricsCollector {
    pub fn new() -> Self {
        Self {
            custom_metrics: HashMap::new(),
        }
    }

    pub fn increment_requests(&self) {
        HTTP_REQUESTS_TOTAL.inc();
    }

    pub fn record_request_duration(&self, duration: f64) {
        HTTP_REQUEST_DURATION.observe(duration);
    }

    pub fn set_active_connections(&self, count: u64) {
        ACTIVE_CONNECTIONS.set(count as f64);
    }

    pub fn add_custom_metric(&mut self, name: String, value: String) {
        self.custom_metrics.insert(name, value);
    }
}

pub async fn metrics_endpoint() -> actix_web::Result<actix_web::HttpResponse> {
    let encoder = TextEncoder::new();
    let metric_families = prometheus::gather();
    let mut buffer = Vec::new();

    encoder.encode(&metric_families, &mut buffer)
        .map_err(|e| {
            log::error!("Failed to encode metrics: {}", e);
            actix_web::error::ErrorInternalServerError("Metrics encoding failed")
        })?;

    let output = String::from_utf8(buffer)
        .map_err(|e| {
            log::error!("Failed to convert metrics to string: {}", e);
            actix_web::error::ErrorInternalServerError("Metrics conversion failed")
        })?;

    Ok(actix_web::HttpResponse::Ok()
        .content_type("text/plain; charset=utf-8")
        .body(output))
}