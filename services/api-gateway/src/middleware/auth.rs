use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    Error, HttpMessage, HttpResponse,
};
use futures_util::future::LocalBoxFuture;
use jsonwebtoken::{decode, DecodingKey, Validation};
use serde::{Deserialize, Serialize};
use std::{
    future::{ready, Ready},
    rc::Rc,
};

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,
    pub exp: usize,
    pub iat: usize,
    pub roles: Vec<String>,
    pub scopes: Vec<String>,
}

pub struct AuthMiddleware {
    pub jwt_secret: String,
}

impl<S, B> Transform<S, ServiceRequest> for AuthMiddleware
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type InitError = ();
    type Transform = AuthMiddlewareService<S>;
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(AuthMiddlewareService {
            service: Rc::new(service),
            jwt_secret: self.jwt_secret.clone(),
        }))
    }
}

pub struct AuthMiddlewareService<S> {
    service: Rc<S>,
    jwt_secret: String,
}

impl<S, B> Service<ServiceRequest> for AuthMiddlewareService<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let service = Rc::clone(&self.service);
        let jwt_secret = self.jwt_secret.clone();

        Box::pin(async move {
            // Skip auth for public endpoints
            let path = req.path();
            if is_public_endpoint(path) {
                return service.call(req).await;
            }

            // Extract token from Authorization header
            let token = match extract_token(&req) {
                Some(token) => token,
                None => {
                    return Err(actix_web::error::ErrorUnauthorized(
                        serde_json::json!({
                            "error": "Missing authorization token"
                        })
                    ));
                }
            };

            // Validate JWT token
            let claims = match validate_token(&token, &jwt_secret) {
                Ok(claims) => claims,
                Err(_) => {
                    return Err(actix_web::error::ErrorUnauthorized(
                        serde_json::json!({
                            "error": "Invalid authorization token"
                        })
                    ));
                }
            };

            // Store claims in request extensions
            req.extensions_mut().insert(claims);

            service.call(req).await
        })
    }
}

fn is_public_endpoint(path: &str) -> bool {
    let public_paths = vec![
        "/health",
        "/health/live",
        "/health/ready",
        "/metrics",
        "/docs",
        "/api/v1/health",
        "/api/v1/status",
        "/api/v1/sbom/upload",
        "/api/v1/scan/sbom",
        "/api/v1/scan/hbom",
        "/api/v1/scan/",
        "/api/v1/scans",
        "/api/v1/vulnerability/assess",
        "/api/v1/compliance/generate",
        "/api/v1/blockchain/verify",
        "/api/v1/stats",
        "/api/v1/metrics/summary",
        "/api/v1/metrics/custom",
        "/auth/login",
        "/auth/refresh",
    ];

    public_paths.iter().any(|public_path| {
        if public_path.ends_with('/') {
            path.starts_with(public_path)
        } else {
            path == *public_path
        }
    })
}

fn extract_token(req: &ServiceRequest) -> Option<String> {
    req.headers()
        .get("authorization")
        .and_then(|header| header.to_str().ok())
        .and_then(|auth_header| {
            if auth_header.starts_with("Bearer ") {
                Some(auth_header[7..].to_string())
            } else {
                None
            }
        })
}

fn validate_token(token: &str, secret: &str) -> Result<Claims, jsonwebtoken::errors::Error> {
    let decoding_key = DecodingKey::from_secret(secret.as_ref());
    let validation = Validation::default();

    let token_data = decode::<Claims>(token, &decoding_key, &validation)?;
    Ok(token_data.claims)
}