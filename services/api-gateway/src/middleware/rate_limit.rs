use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    Error, HttpResponse,
};
use futures_util::future::LocalBoxFuture;
use governor::{clock::DefaultClock, middleware::NoOpMiddleware, state::keyed::DefaultKeyedStateStore, RateLimiter};
use std::{
    future::{ready, Ready},
    num::NonZeroU32,
    rc::Rc,
    sync::Arc,
};

pub struct RateLimitMiddleware {
    pub limiter: Arc<RateLimiter<String, DefaultKeyedStateStore<String>, DefaultClock, NoOpMiddleware>>,
}

impl RateLimitMiddleware {
    pub fn new(requests: u32, window_secs: u64) -> Self {
        let quota = governor::Quota::per_second(NonZeroU32::new(requests).unwrap())
            .allow_burst(NonZeroU32::new((requests * window_secs as u32) / 60).unwrap());

        let limiter = Arc::new(
            RateLimiter::keyed(quota)
        );

        Self { limiter }
    }
}

impl<S, B> Transform<S, ServiceRequest> for RateLimitMiddleware
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type InitError = ();
    type Transform = RateLimitMiddlewareService<S>;
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(RateLimitMiddlewareService {
            service: Rc::new(service),
            limiter: Arc::clone(&self.limiter),
        }))
    }
}

pub struct RateLimitMiddlewareService<S> {
    service: Rc<S>,
    limiter: Arc<RateLimiter<String, DefaultKeyedStateStore<String>, DefaultClock, NoOpMiddleware>>,
}

impl<S, B> Service<ServiceRequest> for RateLimitMiddlewareService<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let service = Rc::clone(&self.service);
        let limiter = Arc::clone(&self.limiter);

        Box::pin(async move {
            // Use client IP as key for rate limiting
            let client_ip = req
                .connection_info()
                .peer_addr()
                .unwrap_or("unknown")
                .to_string();

            // Check rate limit
            match limiter.check_key(&client_ip) {
                Ok(_) => {
                    // Rate limit not exceeded, proceed
                    service.call(req).await
                }
                Err(_) => {
                    // Rate limit exceeded
                    Err(actix_web::error::ErrorTooManyRequests(
                        serde_json::json!({
                            "error": "Rate limit exceeded",
                            "retry_after": 60
                        })
                    ))
                }
            }
        })
    }
}