use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    http::header::{HeaderName, HeaderValue},
    Error,
};
use futures_util::future::LocalBoxFuture;
use std::{
    future::{ready, Ready},
    rc::Rc,
};

pub struct TransformMiddleware;

impl<S, B> Transform<S, ServiceRequest> for TransformMiddleware
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type InitError = ();
    type Transform = TransformMiddlewareService<S>;
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(TransformMiddlewareService {
            service: Rc::new(service),
        }))
    }
}

pub struct TransformMiddlewareService<S> {
    service: Rc<S>,
}

impl<S, B> Service<ServiceRequest> for TransformMiddlewareService<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, mut req: ServiceRequest) -> Self::Future {
        let service = Rc::clone(&self.service);

        Box::pin(async move {
            // Add request ID if not present
            if req.headers().get("x-request-id").is_none() {
                let request_id = uuid::Uuid::new_v4().to_string();
                req.headers_mut().insert(
                    HeaderName::from_static("x-request-id"),
                    HeaderValue::from_str(&request_id).unwrap(),
                );
            }

            // Log request details
            let method = req.method().clone();
            let path = req.path().to_string();
            let request_id = req
                .headers()
                .get("x-request-id")
                .and_then(|h| h.to_str().ok())
                .unwrap_or("unknown");

            log::info!(
                "Request: {} {} [ID: {}]",
                method,
                path,
                request_id
            );

            // Transform request body if needed (e.g., normalize JSON)
            // For now, just pass through

            let res = service.call(req).await?;

            // Transform response if needed
            // Add common headers
            let (req, mut response) = res.into_parts();
            response.headers_mut().insert(
                HeaderName::from_static("x-api-gateway"),
                HeaderValue::from_static("infinitium-signal"),
            );

            Ok(ServiceResponse::new(req, response))
        })
    }
}