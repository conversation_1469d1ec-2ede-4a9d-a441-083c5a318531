//! API documentation and OpenAPI specification

use utoipa::OpenApi;

/// OpenAPI documentation for Infinitum Signal API
#[derive(OpenApi)]
#[openapi(
    info(
        title = "Infinitum Signal API",
        version = "1.0.0",
        description = "Enterprise Cybersecurity Platform API",
        contact(
            name = "Infinitum Signal Team",
            email = "<EMAIL>"
        )
    ),
    paths(
        crate::api::health::health_check,
        crate::api::health::liveness_probe,
        crate::api::health::readiness_probe,
    ),
    components(
        schemas(
            crate::api::health::HealthResponse,
            crate::api::health::HealthStatus,
            crate::api::health::ServiceHealth,
            crate::api::health::SystemMetrics,

        )
    ),
    tags(
        (name = "Health", description = "Health check endpoints"),
        (name = "Vulnerability", description = "Vulnerability scanning endpoints"),
        (name = "Compliance", description = "Compliance reporting endpoints"),
        (name = "Blockchain", description = "Blockchain and verifiable credentials"),
    )
)]
pub struct ApiDocumentation;

impl ApiDocumentation {
    /// Create new API documentation
    pub fn openapi_spec() -> utoipa::openapi::OpenApi {
        Self::openapi()
    }
}

impl Default for ApiDocumentation {
    fn default() -> Self {
        Self
    }
}
