use crate::{
    api::{AppState, Claims},
    error::{InfinitumError, Result},
};
use axum::{
    extract::{Request, State},
    http::{HeaderMap, HeaderValue, StatusCode},
    middleware::Next,
    response::Response,
};
use jsonwebtoken::{decode, Decoding<PERSON>ey, Validation};
use std::time::Instant;
use tracing::{info, warn};
use uuid::Uuid;

/// Request ID header name
const REQUEST_ID_HEADER: &str = "x-request-id";

/// Authorization header name
const AUTHORIZATION_HEADER: &str = "authorization";

/// Bearer token prefix
const BEARER_PREFIX: &str = "Bearer ";

/// Request ID middleware
pub async fn request_id(
    State(_state): State<AppState>,
    mut request: Request,
    next: Next,
) -> Response {
    // Generate or extract request ID
    let request_id = request
        .headers()
        .get(REQUEST_ID_HEADER)
        .and_then(|h| h.to_str().ok())
        .map(|s| s.to_string())
        .unwrap_or_else(|| Uuid::new_v4().to_string());

    // Add request ID to headers
    request.headers_mut().insert(
        REQUEST_ID_HEADER,
        HeaderValue::from_str(&request_id).unwrap_or_else(|_| HeaderValue::from_static("invalid")),
    );

    // Process request
    let mut response = next.run(request).await;

    // Add request ID to response headers
    response.headers_mut().insert(
        REQUEST_ID_HEADER,
        HeaderValue::from_str(&request_id).unwrap_or_else(|_| HeaderValue::from_static("invalid")),
    );

    response
}

/// Rate limiting middleware
pub async fn rate_limit(
    State(state): State<AppState>,
    request: Request,
    next: Next,
) -> std::result::Result<Response, StatusCode> {
    // Check rate limit
    match state.rate_limiter.check() {
        Ok(_) => {
            // Request allowed, proceed
            Ok(next.run(request).await)
        }
        Err(_) => {
            // Rate limit exceeded
            warn!("Rate limit exceeded for request");
            Err(StatusCode::TOO_MANY_REQUESTS)
        }
    }
}

/// Authentication middleware
pub async fn auth(
    State(state): State<AppState>,
    request: Request,
    next: Next,
) -> std::result::Result<Response, StatusCode> {
    let uri = request.uri().clone();

    // Skip authentication for public endpoints
    if is_public_endpoint(uri.path()) {
        return Ok(next.run(request).await);
    }

    // Extract authorization header
    let auth_header = request
        .headers()
        .get(AUTHORIZATION_HEADER)
        .and_then(|h| h.to_str().ok())
        .ok_or(StatusCode::UNAUTHORIZED)?;

    // Extract token from Bearer header
    let token = auth_header
        .strip_prefix(BEARER_PREFIX)
        .ok_or(StatusCode::UNAUTHORIZED)?;

    // Validate JWT token
    match validate_jwt_token(token, &state.jwt_secret) {
        Ok(claims) => {
            // Check if user has required permissions for this endpoint
            if !has_required_permissions(&claims, uri.path()) {
                warn!("User {} lacks permissions for {}", claims.sub, uri.path());
                return Err(StatusCode::FORBIDDEN);
            }

            // Token is valid, proceed with request
            Ok(next.run(request).await)
        }
        Err(e) => {
            warn!("JWT validation failed: {}", e);
            Err(StatusCode::UNAUTHORIZED)
        }
    }
}

/// Logging middleware
pub async fn logging(request: Request, next: Next) -> Response {
    let start_time = Instant::now();
    let method = request.method().clone();
    let uri = request.uri().clone();
    let request_id = request
        .headers()
        .get(REQUEST_ID_HEADER)
        .and_then(|h| h.to_str().ok())
        .unwrap_or("unknown")
        .to_string();

    info!(
        request_id = request_id,
        method = %method,
        uri = %uri,
        "Request started"
    );

    // Process request
    let response = next.run(request).await;

    let duration = start_time.elapsed();
    let status = response.status();

    info!(
        request_id = request_id,
        method = %method,
        uri = %uri,
        status = %status,
        duration_ms = duration.as_millis(),
        "Request completed"
    );

    response
}

/// CORS middleware
pub async fn cors(request: Request, next: Next) -> Response {
    let mut response = next.run(request).await;

    // Add CORS headers
    let headers = response.headers_mut();
    headers.insert("access-control-allow-origin", HeaderValue::from_static("*"));
    headers.insert(
        "access-control-allow-methods",
        HeaderValue::from_static("GET, POST, PUT, DELETE, OPTIONS"),
    );
    headers.insert(
        "access-control-allow-headers",
        HeaderValue::from_static("content-type, authorization, x-request-id"),
    );
    headers.insert("access-control-max-age", HeaderValue::from_static("86400"));

    response
}

/// Security headers middleware
pub async fn security_headers(request: Request, next: Next) -> Response {
    let mut response = next.run(request).await;

    // Add security headers
    let headers = response.headers_mut();
    headers.insert(
        "x-content-type-options",
        HeaderValue::from_static("nosniff"),
    );
    headers.insert("x-frame-options", HeaderValue::from_static("DENY"));
    headers.insert(
        "x-xss-protection",
        HeaderValue::from_static("1; mode=block"),
    );
    headers.insert(
        "strict-transport-security",
        HeaderValue::from_static("max-age=31536000; includeSubDomains"),
    );
    headers.insert(
        "referrer-policy",
        HeaderValue::from_static("strict-origin-when-cross-origin"),
    );

    response
}

/// Generate JWT token
pub fn generate_jwt_token(claims: &Claims, secret: &str) -> Result<String> {
    let encoding_key = jsonwebtoken::EncodingKey::from_secret(secret.as_ref());
    let header = jsonwebtoken::Header::default();

    jsonwebtoken::encode(&header, claims, &encoding_key)
        .map_err(|e| InfinitumError::Authentication {
            message: format!("JWT generation failed: {}", e),
        })
}

/// Create claims for a user
pub fn create_user_claims(user_id: &str, roles: Vec<String>, scopes: Vec<String>) -> Claims {
    let iat = chrono::Utc::now().timestamp() as usize;
    let exp = (chrono::Utc::now() + chrono::Duration::hours(24)).timestamp() as usize;

    Claims {
        sub: user_id.to_string(),
        iat: iat as i64,
        exp: exp as i64,
        roles,
        scopes,
    }
}

/// Validate JWT token
fn validate_jwt_token(token: &str, secret: &str) -> Result<Claims> {
    let decoding_key = DecodingKey::from_secret(secret.as_ref());
    let validation = Validation::default();

    match decode::<Claims>(token, &decoding_key, &validation) {
        Ok(token_data) => Ok(token_data.claims),
        Err(e) => Err(InfinitumError::Authentication {
            message: format!("JWT validation failed: {}", e),
        }),
    }
}

/// Check if endpoint is public (doesn't require authentication)
fn is_public_endpoint(path: &str) -> bool {
    let public_paths = [
        "/health",
        "/metrics",
        "/docs",
        "/docs/openapi.json",
        "/auth/login",
        "/auth/refresh",
        "/api/v1/health",
    ];

    public_paths
        .iter()
        .any(|&public_path| path == public_path || path.starts_with(&format!("{}/", public_path)))
}

/// Check if user has required permissions for endpoint
fn has_required_permissions(claims: &Claims, path: &str) -> bool {
    // Define endpoint permissions with required roles
    let endpoint_permissions = [
        (
            &["/internal/", "/api/v1/admin/", "/api/v1/system/"][..],
            &["admin"][..],
        ),
        (
            &[
                "/api/v1/scan/",
                "/api/v1/vulnerability/",
                "/api/v1/compliance/",
            ][..],
            &["scanner", "admin"][..],
        ),
        (&["/api/v1/blockchain/"][..], &["blockchain", "admin"][..]),
    ];

    // Check if path requires specific permissions
    for (endpoints, required_roles) in &endpoint_permissions {
        if endpoints.iter().any(|&endpoint| path.starts_with(endpoint)) {
            return required_roles
                .iter()
                .any(|role| claims.roles.contains(&role.to_string()));
        }
    }

    // Default: allow access for authenticated users
    true
}

/// Extract client IP address
pub fn extract_client_ip(headers: &HeaderMap) -> Option<String> {
    // Try various headers in order of preference
    let ip_headers = [
        "x-forwarded-for",
        "x-real-ip",
        "cf-connecting-ip",
        "x-client-ip",
    ];

    for header_name in &ip_headers {
        if let Some(header_value) = headers.get(*header_name) {
            if let Ok(ip_str) = header_value.to_str() {
                // X-Forwarded-For can contain multiple IPs, take the first one
                let ip = ip_str.split(',').next().unwrap_or(ip_str).trim();
                if !ip.is_empty() {
                    return Some(ip.to_string());
                }
            }
        }
    }

    None
}

/// Extract user agent
pub fn extract_user_agent(headers: &HeaderMap) -> Option<String> {
    headers
        .get("user-agent")
        .and_then(|h| h.to_str().ok())
        .map(|s| s.to_string())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_is_public_endpoint() {
        assert!(is_public_endpoint("/health"));
        assert!(is_public_endpoint("/metrics"));
        assert!(is_public_endpoint("/docs"));
        assert!(is_public_endpoint("/auth/login"));
        assert!(!is_public_endpoint("/api/v1/scan/sbom"));
        assert!(!is_public_endpoint("/internal/admin"));
    }

    #[test]
    fn test_has_required_permissions() {
        let admin_claims = Claims {
            sub: "admin_user".to_string(),
            iat: 0,
            exp: **********,
            roles: vec!["admin".to_string()],
            scopes: vec!["read".to_string(), "write".to_string()],
        };

        let scanner_claims = Claims {
            sub: "scanner_user".to_string(),
            iat: 0,
            exp: **********,
            roles: vec!["scanner".to_string()],
            scopes: vec!["read".to_string()],
        };

        let user_claims = Claims {
            sub: "regular_user".to_string(),
            iat: 0,
            exp: **********,
            roles: vec!["user".to_string()],
            scopes: vec!["read".to_string()],
        };

        // Admin should have access to everything
        assert!(has_required_permissions(&admin_claims, "/internal/admin"));
        assert!(has_required_permissions(&admin_claims, "/api/v1/scan/sbom"));
        assert!(has_required_permissions(
            &admin_claims,
            "/api/v1/blockchain/commit"
        ));

        // Scanner should have access to scanning endpoints
        assert!(!has_required_permissions(
            &scanner_claims,
            "/internal/admin"
        ));
        assert!(has_required_permissions(
            &scanner_claims,
            "/api/v1/scan/sbom"
        ));
        assert!(!has_required_permissions(
            &scanner_claims,
            "/api/v1/blockchain/commit"
        ));

        // Regular user should not have access to restricted endpoints
        assert!(!has_required_permissions(&user_claims, "/internal/admin"));
        assert!(!has_required_permissions(&user_claims, "/api/v1/scan/sbom"));
        assert!(!has_required_permissions(
            &user_claims,
            "/api/v1/blockchain/commit"
        ));
    }

    #[test]
    fn test_extract_client_ip() {
        let mut headers = HeaderMap::new();

        // Test X-Forwarded-For header
        headers.insert(
            "x-forwarded-for",
            HeaderValue::from_static("***********, ********"),
        );
        assert_eq!(extract_client_ip(&headers), Some("***********".to_string()));

        // Test X-Real-IP header
        headers.clear();
        headers.insert("x-real-ip", HeaderValue::from_static("***********"));
        assert_eq!(extract_client_ip(&headers), Some("***********".to_string()));

        // Test no headers
        headers.clear();
        assert_eq!(extract_client_ip(&headers), None);
    }

    #[test]
    fn test_extract_user_agent() {
        let mut headers = HeaderMap::new();

        headers.insert(
            "user-agent",
            HeaderValue::from_static("Mozilla/5.0 (Test Browser)"),
        );
        assert_eq!(
            extract_user_agent(&headers),
            Some("Mozilla/5.0 (Test Browser)".to_string())
        );

        headers.clear();
        assert_eq!(extract_user_agent(&headers), None);
    }
}
