//! Input sanitization middleware for enhanced security

use crate::error::{InfinitumError, Result};
use axum::{
    extract::{Request, State},
    http::StatusCode,
    middleware::Next,
    response::Response,
};
use regex::Regex;
use tracing::{debug, warn};

/// Input sanitization configuration
#[derive(Debug, <PERSON>lone)]
pub struct SanitizationConfig {
    /// Maximum input length
    pub max_length: usize,
    /// Allowed characters pattern
    pub allowed_pattern: Option<String>,
    /// Blocked patterns (SQL injection, XSS, etc.)
    pub blocked_patterns: Vec<String>,
    /// HTML sanitization enabled
    pub sanitize_html: bool,
    /// SQL injection protection
    pub prevent_sql_injection: bool,
    /// XSS protection
    pub prevent_xss: bool,
}

impl Default for SanitizationConfig {
    fn default() -> Self {
        Self {
            max_length: 10000,
            allowed_pattern: None,
            blocked_patterns: vec![
                r"<script[^>]*>.*?</script>".to_string(),
                r"javascript:".to_string(),
                r"on\w+\s*=".to_string(),
                r"SELECT.*FROM".to_string(),
                r"INSERT.*INTO".to_string(),
                r"UPDATE.*SET".to_string(),
                r"DELETE.*FROM".to_string(),
                r"DROP.*TABLE".to_string(),
                r"UNION.*SELECT".to_string(),
            ],
            sanitize_html: true,
            prevent_sql_injection: true,
            prevent_xss: true,
        }
    }
}

/// Input sanitization middleware
pub async fn sanitization_middleware(
    config: SanitizationConfig,
    mut request: Request,
    next: Next,
) -> std::result::Result<Response, StatusCode> {
    // Sanitize query parameters
    if let Some(query) = request.uri().query() {
        let _sanitized_query = sanitize_input(query, &config).map_err(|_| StatusCode::BAD_REQUEST)?;
        // Note: In a real implementation, you'd reconstruct the URI with sanitized query
        debug!("Sanitized query parameters");
    }

    // Sanitize headers that might contain user input
    let headers_to_check = ["user-agent", "referer", "x-forwarded-for"];
    for header_name in &headers_to_check {
        if let Some(header_value) = request.headers().get(*header_name) {
            if let Ok(value_str) = header_value.to_str() {
                let sanitized = sanitize_input(value_str, &config)
                    .map_err(|_| StatusCode::BAD_REQUEST)?;
                // Update header with sanitized value
                request.headers_mut().insert(
                    *header_name,
                    sanitized.parse().map_err(|_| StatusCode::BAD_REQUEST)?,
                );
            }
        }
    }

    // For POST/PUT requests, we would need to sanitize the body
    // This is a simplified version - in practice, you'd need to buffer and sanitize the body

    Ok(next.run(request).await)
}

/// Sanitize input string based on configuration
pub fn sanitize_input(input: &str, config: &SanitizationConfig) -> Result<String> {
    let mut sanitized = input.to_string();

    // Check length limit
    if sanitized.len() > config.max_length {
        warn!("Input exceeds maximum length: {} > {}", sanitized.len(), config.max_length);
        return Err(InfinitumError::Validation {
            field: "input".to_string(),
            message: format!("Input exceeds maximum length of {}", config.max_length),
        });
    }

    // Check blocked patterns
    for pattern in &config.blocked_patterns {
        if let Ok(regex) = Regex::new(&format!("(?i){}", pattern)) {
            if regex.is_match(&sanitized) {
                warn!("Blocked pattern detected in input: {}", pattern);
                return Err(InfinitumError::Validation {
                    field: "input".to_string(),
                    message: "Input contains blocked content".to_string(),
                });
            }
        }
    }

    // Apply allowed pattern if specified
    if let Some(pattern) = &config.allowed_pattern {
        if let Ok(regex) = Regex::new(pattern) {
            sanitized = regex.replace_all(&sanitized, "").to_string();
        }
    }

    // HTML sanitization
    if config.sanitize_html {
        sanitized = sanitize_html(&sanitized);
    }

    Ok(sanitized)
}

/// Basic HTML sanitization
fn sanitize_html(input: &str) -> String {
    // Remove potentially dangerous HTML tags and attributes
    let dangerous_tags = ["script", "iframe", "object", "embed", "form", "input", "button"];
    let mut result = input.to_string();

    for tag in &dangerous_tags {
        let tag_pattern = format!(r"<{}[^>]*>.*?</{}>", tag, tag);
        if let Ok(regex) = Regex::new(&format!("(?i){}", tag_pattern)) {
            result = regex.replace_all(&result, "").to_string();
        }

        // Also remove self-closing tags
        let self_closing_pattern = format!(r"<{}/?>", tag);
        if let Ok(regex) = Regex::new(&format!("(?i){}", self_closing_pattern)) {
            result = regex.replace_all(&result, "").to_string();
        }
    }

    result
}

/// Content Security Policy (CSP) middleware
pub async fn csp_middleware(request: Request, next: Next) -> Response {
    let mut response = next.run(request).await;

    // Add CSP header
    let csp_header = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:; media-src 'self'; object-src 'none'; frame-ancestors 'none'; base-uri 'self'; form-action 'self';";

    response.headers_mut().insert(
        "content-security-policy",
        csp_header.parse().unwrap(),
    );

    response
}

/// CSRF protection middleware
pub async fn csrf_middleware(
    State(_state): axum::extract::State<crate::api::AppState>,
    request: Request,
    next: Next,
) -> std::result::Result<Response, StatusCode> {
    // Skip CSRF check for safe methods
    let method = request.method();
    if matches!(method, &axum::http::Method::GET | &axum::http::Method::HEAD | &axum::http::Method::OPTIONS) {
        return Ok(next.run(request).await);
    }

    // Check for CSRF token in headers
    let csrf_token = request.headers()
        .get("x-csrf-token")
        .or_else(|| request.headers().get("x-xsrf-token"))
        .and_then(|h| h.to_str().ok());

    if let Some(token) = csrf_token {
        // In a real implementation, you'd validate the token against a session store
        // For now, we'll just check if it's present and properly formatted
        if token.len() < 32 {
            warn!("Invalid CSRF token length");
            return Err(StatusCode::FORBIDDEN);
        }
        debug!("CSRF token validated");
    } else {
        warn!("Missing CSRF token for non-safe method");
        return Err(StatusCode::FORBIDDEN);
    }

    Ok(next.run(request).await)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sanitize_html() {
        let input = r#"<p>Hello <script>alert('xss')</script> world</p>"#;
        let result = sanitize_html(input);
        assert!(!result.contains("<script>"));
        assert!(result.contains("Hello"));
        assert!(result.contains("world"));
    }

    #[test]
    fn test_sanitize_input_with_blocked_patterns() {
        let config = SanitizationConfig {
            blocked_patterns: vec![r"<script[^>]*>.*?</script>".to_string()],
            ..Default::default()
        };

        let input = r#"<script>alert('xss')</script>"#;
        let result = sanitize_input(input, &config);
        assert!(result.is_err());
    }

    #[test]
    fn test_sanitize_input_length_limit() {
        let config = SanitizationConfig {
            max_length: 10,
            ..Default::default()
        };

        let input = "this is a very long string that exceeds the limit";
        let result = sanitize_input(input, &config);
        assert!(result.is_err());
    }
}