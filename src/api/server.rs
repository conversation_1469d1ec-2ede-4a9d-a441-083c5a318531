use crate::{
    api::{create_router, AppState},
    config::Config,
    database::DatabaseService,
    error::Result,
    observability::ObservabilityManager,
};
use axum::serve;
use governor::{Quota, RateLimiter};
use std::{net::SocketAddr, sync::Arc};
use tokio::net::TcpListener;
use tracing::{error, info, instrument};

/// API server for the Infinitum Signal platform
pub struct ApiServer {
    config: Arc<Config>,
    listener: TcpListener,
    app_state: AppState,
    observability_manager: Arc<ObservabilityManager>,
}

impl ApiServer {
    /// Create a new API server instance
    #[instrument(skip(config, observability_manager))]
    pub async fn new(config: Arc<Config>, observability_manager: Arc<ObservabilityManager>) -> Result<Self> {
        info!("Initializing API server");

        // Create database connection (optional)
        let database = if config.database.enabled {
            info!("Database enabled, attempting connection...");
            match DatabaseService::new(config.database.clone()).await {
                Ok(db) => {
                    info!("Database connection established successfully");
                    Some(Arc::new(db))
                }
                Err(e) => {
                    error!("Failed to connect to database: {}", e);
                    info!("Continuing in demo mode without database");
                    None
                }
            }
        } else {
            info!("Database disabled, running in demo mode");
            None
        };

        // Create rate limiter
        let quota = Quota::per_minute(
            std::num::NonZeroU32::new(config.server.timeout as u32)
                .unwrap_or(std::num::NonZeroU32::new(1000).unwrap()),
        );
        let rate_limiter = Arc::new(RateLimiter::direct(quota));

        // Create application state
        let app_state = AppState {
            database_service: database,
            scanner_orchestrator: Arc::new(crate::scanners::ScannerOrchestrator::new(config.scanning.clone())),
            vulnerability_orchestrator: Arc::new(crate::vulnerability::VulnerabilityOrchestrator::new(config.vulnerability.clone())),
            compliance_orchestrator: Arc::new(crate::compliance::ComplianceOrchestrator::new(config.compliance.clone())),
            blockchain_orchestrator: Arc::new(crate::blockchain::BlockchainOrchestrator::new(config.blockchain.clone()).unwrap()),
            observability_manager: Some(observability_manager.clone()),
            config: config.server.clone(),
            jwt_secret: config.security.jwt_secret.clone(),
            rate_limiter,
        };

        // Create TCP listener
        let addr = SocketAddr::new(config.server.host.parse()?, config.server.port);

        let listener = TcpListener::bind(addr).await?;
        info!("API server bound to {}", addr);

        Ok(Self {
            config,
            listener,
            app_state,
            observability_manager,
        })
    }

    /// Run the API server
    #[instrument(skip(self))]
    pub async fn run(self) -> Result<()> {
        info!("Starting API server");

        // Create the router with all routes and middleware
        let app = create_router(self.app_state);

        // Start the server
        info!(
            "API server listening on http://{}:{}",
            self.config.server.host, self.config.server.port
        );
        info!(
            "API documentation available at http://{}:{}/docs",
            self.config.server.host, self.config.server.port
        );

        serve(self.listener, app).await.map_err(|e| {
            error!("Server error: {}", e);
            e.into()
        })
    }

    /// Get the local address the server is bound to
    pub fn local_addr(&self) -> Result<SocketAddr> {
        Ok(self.listener.local_addr()?)
    }

    /// Gracefully shutdown the server
    pub async fn shutdown(self) -> Result<()> {
        info!("Shutting down API server gracefully");
        // The server will shutdown when dropped
        Ok(())
    }
}

/// Server configuration
#[derive(Debug, Clone)]
pub struct ServerConfig {
    /// Server host
    pub host: String,
    /// Server port
    pub port: u16,
    /// Maximum number of concurrent connections
    pub max_connections: usize,
    /// Request timeout in seconds
    pub request_timeout_seconds: u64,
    /// Enable CORS
    pub cors_enabled: bool,
    /// Rate limiting configuration
    pub rate_limit: RateLimitConfig,
}

/// Rate limiting configuration
#[derive(Debug, Clone)]
pub struct RateLimitConfig {
    /// Requests per minute
    pub requests_per_minute: u32,
    /// Burst size
    pub burst_size: u32,
    /// Enable rate limiting
    pub enabled: bool,
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            host: "0.0.0.0".to_string(),
            port: 8080,
            max_connections: 1000,
            request_timeout_seconds: 30,
            cors_enabled: true,
            rate_limit: RateLimitConfig {
                requests_per_minute: 1000,
                burst_size: 100,
                enabled: true,
            },
        }
    }
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        Self {
            requests_per_minute: 1000,
            burst_size: 100,
            enabled: true,
        }
    }
}

/// Server metrics
#[derive(Debug, Clone)]
pub struct ServerMetrics {
    /// Total requests handled
    pub total_requests: u64,
    /// Active connections
    pub active_connections: u32,
    /// Average response time in milliseconds
    pub avg_response_time_ms: f64,
    /// Error rate (0.0 - 1.0)
    pub error_rate: f64,
    /// Uptime in seconds
    pub uptime_seconds: u64,
}

impl Default for ServerMetrics {
    fn default() -> Self {
        Self {
            total_requests: 0,
            active_connections: 0,
            avg_response_time_ms: 0.0,
            error_rate: 0.0,
            uptime_seconds: 0,
        }
    }
}

/// Health check status
#[derive(Debug, Clone)]
pub enum HealthStatus {
    /// Server is healthy
    Healthy,
    /// Server is degraded but functional
    Degraded,
    /// Server is unhealthy
    Unhealthy,
}

impl std::fmt::Display for HealthStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            HealthStatus::Healthy => write!(f, "healthy"),
            HealthStatus::Degraded => write!(f, "degraded"),
            HealthStatus::Unhealthy => write!(f, "unhealthy"),
        }
    }
}

/// Server health information
#[derive(Debug, Clone)]
pub struct ServerHealth {
    /// Overall health status
    pub status: HealthStatus,
    /// Database connectivity
    pub database_connected: bool,
    /// Redis connectivity
    pub redis_connected: bool,
    /// External API connectivity
    pub external_apis_available: bool,
    /// Memory usage percentage
    pub memory_usage_percent: f64,
    /// CPU usage percentage
    pub cpu_usage_percent: f64,
    /// Disk usage percentage
    pub disk_usage_percent: f64,
}

impl ServerHealth {
    /// Create a new server health instance
    pub fn new() -> Self {
        Self {
            status: HealthStatus::Healthy,
            database_connected: false,
            redis_connected: false,
            external_apis_available: false,
            memory_usage_percent: 0.0,
            cpu_usage_percent: 0.0,
            disk_usage_percent: 0.0,
        }
    }

    /// Update health status based on component status
    pub fn update_status(&mut self) {
        if !self.database_connected {
            self.status = HealthStatus::Unhealthy;
        } else if !self.redis_connected
            || !self.external_apis_available
            || self.memory_usage_percent > 90.0
            || self.cpu_usage_percent > 90.0
        {
            self.status = HealthStatus::Degraded;
        } else {
            self.status = HealthStatus::Healthy;
        }
    }
}

impl Default for ServerHealth {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::{DatabaseConfig, SecurityConfig, ServerConfig};

    #[tokio::test]
    async fn test_server_creation() {
        let _config = Arc::new(Config {
            server: ServerConfig {
                host: "127.0.0.1".to_string(),
                port: 0, // Use port 0 for testing
                workers: 1,
                timeout: 30,
                keep_alive: 60,
                max_request_size: 1024 * 1024,
                cors_origins: vec!["*".to_string()],
            },
            database: DatabaseConfig::default(),
            security: SecurityConfig {
                jwt_secret: "test_secret".to_string(),
                jwt_expiration: 3600,
                jwt_refresh_expiration: 86400,
                encryption_key: "test_key".to_string(),
                hash_rounds: 12,
                api_rate_limit: 100,
                api_rate_window: 60,
            },
            ..Default::default()
        });

        // This test would fail without a database, but demonstrates the API
        // In a real test environment, you'd use a test database
        // let server = ApiServer::new(config).await;
        // assert!(server.is_ok());
    }

    #[test]
    fn test_server_config_default() {
        let config = ServerConfig::default();
        assert_eq!(config.host, "0.0.0.0");
        assert_eq!(config.port, 8080);
        assert_eq!(config.workers, 4);
        assert!(!config.cors_origins.is_empty());
    }

    #[test]
    fn test_server_health() {
        let mut health = ServerHealth::new();
        assert!(matches!(health.status, HealthStatus::Healthy));

        health.database_connected = false;
        health.update_status();
        assert!(matches!(health.status, HealthStatus::Unhealthy));

        health.database_connected = true;
        health.redis_connected = false;
        health.update_status();
        assert!(matches!(health.status, HealthStatus::Degraded));

        health.redis_connected = true;
        health.external_apis_available = true;
        health.update_status();
        assert!(matches!(health.status, HealthStatus::Healthy));
    }

    #[test]
    fn test_rate_limit_config() {
        let config = RateLimitConfig::default();
        assert_eq!(config.requests_per_minute, 1000);
        assert_eq!(config.burst_size, 100);
        assert!(config.enabled);
    }
}
