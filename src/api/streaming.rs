//! Streaming utilities for handling large data responses efficiently

use crate::error::{InfinitumError, Result};
use axum::{
    body::Body,
    extract::{Query, State},
    http::{HeaderMap, StatusCode},
    response::{IntoResponse, Response},
};
use futures::stream::{self, Stream};
use serde::{Deserialize, Serialize};
use tokio::sync::mpsc;
use tokio_stream::{wrappers::ReceiverStream, StreamExt};

/// Streaming configuration
#[derive(Debug, Clone)]
pub struct StreamingConfig {
    /// Maximum chunk size in bytes
    pub max_chunk_size: usize,
    /// Buffer size for streaming
    pub buffer_size: usize,
    /// Enable compression for large responses
    pub enable_compression: bool,
    /// Timeout for streaming operations
    pub timeout_seconds: u64,
}

impl Default for StreamingConfig {
    fn default() -> Self {
        Self {
            max_chunk_size: 64 * 1024, // 64KB
            buffer_size: 1000,
            enable_compression: true,
            timeout_seconds: 300, // 5 minutes
        }
    }
}

/// Streaming query parameters
#[derive(Debug, Deserialize)]
pub struct StreamingParams {
    /// Enable streaming mode
    pub stream: Option<bool>,
    /// Chunk size preference
    pub chunk_size: Option<usize>,
    /// Format preference (json, csv, etc.)
    pub format: Option<String>,
}

/// Stream response metadata
#[derive(Debug, Serialize)]
pub struct StreamMetadata {
    /// Total number of items
    pub total_items: u64,
    /// Items per chunk
    pub chunk_size: usize,
    /// Estimated total size in bytes
    pub estimated_size_bytes: Option<u64>,
    /// Content type
    pub content_type: String,
    /// Supports seeking
    pub seekable: bool,
}

/// Create a streaming response for large datasets
pub async fn create_streaming_response<T>(
    data_stream: impl Stream<Item = Result<T>> + Send + 'static,
    metadata: StreamMetadata,
    config: &StreamingConfig,
) -> Response
where
    T: Serialize + Send + 'static,
{
    let (tx, rx) = mpsc::channel(config.buffer_size);

    // Clone config to avoid lifetime issues
    let config_clone = config.clone();

    // Spawn a task to process the stream
    tokio::spawn(async move {
        let mut data_stream = std::pin::pin!(data_stream);
        let mut chunk = Vec::new();
        let mut chunk_count = 0;
        let mut should_break = false;

        while let Some(item) = data_stream.next().await {
            if should_break {
                break;
            }

            match item {
                Ok(data) => {
                    chunk.push(data);
                    chunk_count += 1;

                    // Send chunk when it reaches the maximum size
                    if chunk_count >= config_clone.max_chunk_size / std::mem::size_of::<T>() || chunk.len() >= 100 {
                        if tx.send(Ok(std::mem::take(&mut chunk))).await.is_err() {
                            should_break = true; // Receiver disconnected
                        }
                        chunk_count = 0;
                    }
                }
                Err(e) => {
                    let _ = tx.send(Err(e)).await;
                    should_break = true;
                }
            }
        }

        // Send remaining items
        if !chunk.is_empty() {
            let _ = tx.send(Ok(chunk)).await;
        }
    });

    let stream = ReceiverStream::new(rx)
        .map(move |result| match result {
            Ok(chunk) => {
                match serde_json::to_string(&chunk) {
                    Ok(json) => Ok(json.into_bytes()),
                    Err(e) => Err(InfinitumError::SerializationError(
                        format!("Failed to serialize chunk: {}", e),
                    )),
                }
            }
            Err(e) => Err(e),
        })
        .map(|result| match result {
            Ok(bytes) => Ok::<axum::body::Bytes, InfinitumError>(axum::body::Bytes::from(bytes)),
            Err(e) => {
                let error_json = serde_json::json!({
                    "error": "streaming_error",
                    "message": e.to_string()
                });
                Ok(axum::body::Bytes::from(error_json.to_string()))
            }
        });

    let body = Body::from_stream(stream);

    let mut headers = HeaderMap::new();
    headers.insert("content-type", "application/json".parse().unwrap());
    headers.insert("transfer-encoding", "chunked".parse().unwrap());
    headers.insert("x-stream-total-items", metadata.total_items.to_string().parse().unwrap());
    headers.insert("x-stream-chunk-size", metadata.chunk_size.to_string().parse().unwrap());
    headers.insert("x-stream-content-type", metadata.content_type.parse().unwrap());

    if let Some(size) = metadata.estimated_size_bytes {
        headers.insert("x-estimated-size", size.to_string().parse().unwrap());
    }

    (StatusCode::OK, headers, body).into_response()
}

/// Stream large scan results
pub async fn stream_scan_results(
    State(_state): State<crate::api::AppState>,
    Query(params): Query<StreamingParams>,
) -> Result<Response> {
    // Check if streaming is requested
    if !params.stream.unwrap_or(false) {
        return Err(InfinitumError::Validation {
            field: "stream".to_string(),
            message: "Streaming must be explicitly enabled".to_string(),
        });
    }

    let config = StreamingConfig::default();
    let chunk_size = params.chunk_size.unwrap_or(config.max_chunk_size);

    // Create a mock data stream for demonstration
    // In a real implementation, this would stream from the database
    let data_stream = stream::iter(0..10000)
        .map(|i| {
            Ok(serde_json::json!({
                "id": i,
                "scan_type": "vulnerability",
                "severity": if i % 10 == 0 { "critical" } else { "medium" },
                "description": format!("Mock vulnerability {}", i),
                "component": format!("component-{}", i % 100)
            }))
        });

    let metadata = StreamMetadata {
        total_items: 10000,
        chunk_size,
        estimated_size_bytes: Some(2 * 1024 * 1024), // ~2MB
        content_type: "application/json".to_string(),
        seekable: false,
    };

    Ok(create_streaming_response(data_stream, metadata, &config).await)
}

/// Stream compliance reports
pub async fn stream_compliance_reports(
    State(_state): State<crate::api::AppState>,
    Query(params): Query<StreamingParams>,
) -> Result<Response> {
    if !params.stream.unwrap_or(false) {
        return Err(InfinitumError::Validation {
            field: "stream".to_string(),
            message: "Streaming must be explicitly enabled".to_string(),
        });
    }

    let config = StreamingConfig::default();

    // Mock compliance report stream
    let data_stream = stream::iter(0..5000)
        .map(|i| {
            Ok(serde_json::json!({
                "id": i,
                "framework": "CERT-In",
                "status": if i % 5 == 0 { "compliant" } else { "non-compliant" },
                "severity": if i % 20 == 0 { "high" } else { "medium" },
                "description": format!("Compliance check {}", i),
                "remediation": format!("Fix issue {}", i)
            }))
        });

    let metadata = StreamMetadata {
        total_items: 5000,
        chunk_size: config.max_chunk_size,
        estimated_size_bytes: Some(1024 * 1024), // ~1MB
        content_type: "application/json".to_string(),
        seekable: false,
    };

    Ok(create_streaming_response(data_stream, metadata, &config).await)
}

/// Stream blockchain records
pub async fn stream_blockchain_records(
    State(_state): State<crate::api::AppState>,
    Query(params): Query<StreamingParams>,
) -> Result<Response> {
    if !params.stream.unwrap_or(false) {
        return Err(InfinitumError::Validation {
            field: "stream".to_string(),
            message: "Streaming must be explicitly enabled".to_string(),
        });
    }

    let config = StreamingConfig::default();

    // Mock blockchain record stream
    let data_stream = stream::iter(0..2000)
        .map(|i| {
            Ok(serde_json::json!({
                "id": i,
                "transaction_hash": format!("0x{:064x}", i),
                "block_number": 1000000 + i,
                "timestamp": chrono::Utc::now().timestamp(),
                "data_type": "scan_result",
                "data_hash": format!("{:064x}", i * 2),
                "verifiable": true
            }))
        });

    let metadata = StreamMetadata {
        total_items: 2000,
        chunk_size: config.max_chunk_size,
        estimated_size_bytes: Some(500 * 1024), // ~500KB
        content_type: "application/json".to_string(),
        seekable: true,
    };

    Ok(create_streaming_response(data_stream, metadata, &config).await)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_streaming_config_default() {
        let config = StreamingConfig::default();
        assert_eq!(config.max_chunk_size, 64 * 1024);
        assert_eq!(config.buffer_size, 1000);
        assert!(config.enable_compression);
    }

    #[test]
    fn test_stream_metadata() {
        let metadata = StreamMetadata {
            total_items: 1000,
            chunk_size: 100,
            estimated_size_bytes: Some(1024000),
            content_type: "application/json".to_string(),
            seekable: true,
        };

        assert_eq!(metadata.total_items, 1000);
        assert_eq!(metadata.chunk_size, 100);
        assert!(metadata.seekable);
    }

    #[tokio::test]
    async fn test_streaming_params() {
        let params = StreamingParams {
            stream: Some(true),
            chunk_size: Some(1024),
            format: Some("json".to_string()),
        };

        assert!(params.stream.unwrap());
        assert_eq!(params.chunk_size.unwrap(), 1024);
        assert_eq!(params.format.unwrap(), "json");
    }
}