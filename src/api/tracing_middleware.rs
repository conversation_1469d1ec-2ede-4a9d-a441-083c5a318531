//! # Tracing Middleware
//!
//! This module provides middleware for comprehensive HTTP request tracing
//! including trace context propagation, span creation, and error tracking.

use crate::{
    api::AppState,
    observability::{tracing::{self, propagation}, ObservabilityManager},
};
use axum::{
    extract::Request,
    http::{HeaderMap, StatusCode},
    middleware::Next,
    response::Response,
};
use opentelemetry::trace::{Span, Status};
use std::{collections::HashMap, sync::Arc, time::Instant};
use ::tracing::{info, warn};

/// Tracing middleware for HTTP requests
pub async fn tracing_middleware(
    app_state: AppState,
    mut request: Request,
    next: Next,
) -> Response {
    let start_time = Instant::now();

    // Extract trace context from request headers
    let trace_context = extract_trace_context(&request.headers());

    // Get or create request ID
    let request_id = get_or_create_request_id(&request.headers());

    // Create advanced tracing instance
    let advanced_tracing = if let Some(ref obs_manager) = app_state.observability_manager {
        Some(tracing::AdvancedTracing::new(Arc::clone(obs_manager)))
    } else {
        None
    };

    // Create request span
    let mut span = if let Some(ref tracing) = advanced_tracing {
        let method = request.method().as_str();
        let path = request.uri().path();
        tracing.create_request_span(method, path, Some(&request_id))
    } else {
        // Fallback to basic span creation
        let tracer = opentelemetry::global::tracer("infinitium-signal");
        tracer.start("http_request")
    };

    // Set span attributes
    set_request_attributes(&mut span, &request, &request_id);

    // Inject trace context into request extensions for downstream handlers
    if let Some(ref tracing) = advanced_tracing {
        let trace_headers = tracing.inject_trace_context(&span);
        request.extensions_mut().insert(trace_headers);
    }

    // Execute the request with span context
    let response = if let Some(context) = &trace_context {
        // Use existing trace context
        let _guard = context.attach();
        next.run(request).await
    } else {
        // Create new trace context
        let context = opentelemetry::Context::current_with_span(span.clone());
        let _guard = context.attach();
        next.run(request).await
    };

    let duration = start_time.elapsed();

    // Set response attributes
    set_response_attributes(&mut span, &response, duration);

    // Handle errors
    if response.status().is_server_error() {
        if let Some(ref tracing) = advanced_tracing {
            tracing.record_error(&mut span, "Server error occurred", "http_error");
            tracing.set_span_status(&mut span, Status::error("HTTP server error"));
        }
    } else if response.status().is_client_error() {
        if let Some(ref tracing) = advanced_tracing {
            tracing.set_span_status(&mut span, Status::error("HTTP client error"));
        }
    } else {
        if let Some(ref tracing) = advanced_tracing {
            tracing.set_span_status(&mut span, Status::ok());
        }
    }

    // Add span events for significant operations
    if let Some(ref tracing) = advanced_tracing {
        tracing.add_span_event(
            &mut span,
            "request_completed",
            vec![
                opentelemetry::KeyValue::new("duration_ms", duration.as_millis() as f64),
                opentelemetry::KeyValue::new("status_code", response.status().as_u16() as i64),
            ],
        );
    }

    // Log request completion
    info!(
        request_id = %request_id,
        method = %response.extensions().get::<axum::http::Method>().unwrap_or(&axum::http::Method::GET),
        path = %response.extensions().get::<axum::http::Uri>().unwrap_or(&axum::http::Uri::from_static("/")).path(),
        status = %response.status(),
        duration_ms = %duration.as_millis(),
        "Request completed"
    );

    response
}

/// Extract trace context from HTTP headers
fn extract_trace_context(headers: &HeaderMap) -> Option<opentelemetry::Context> {
    let mut header_map = HashMap::new();

    for (name, value) in headers {
        if let Ok(value_str) = value.to_str() {
            header_map.insert(name.as_str().to_string(), value_str.to_string());
        }
    }

    propagation::HttpHeaderPropagator::extract(&header_map)
}

/// Get or create request ID from headers
fn get_or_create_request_id(headers: &HeaderMap) -> String {
    if let Some(request_id) = headers.get("x-request-id") {
        if let Ok(id) = request_id.to_str() {
            return id.to_string();
        }
    }

    // Generate new request ID
    uuid::Uuid::new_v4().to_string()
}

/// Set request attributes on span
fn set_request_attributes(span: &mut Span, request: &Request, request_id: &str) {
    span.set_attribute(opentelemetry::KeyValue::new("http.method", request.method().as_str().to_string()));
    span.set_attribute(opentelemetry::KeyValue::new("http.url", request.uri().to_string()));
    span.set_attribute(opentelemetry::KeyValue::new("http.scheme", request.uri().scheme_str().unwrap_or("http").to_string()));
    span.set_attribute(opentelemetry::KeyValue::new("http.host", request.uri().host().unwrap_or("unknown").to_string()));
    span.set_attribute(opentelemetry::KeyValue::new("http.target", request.uri().path().to_string()));
    span.set_attribute(opentelemetry::KeyValue::new("request.id", request_id.to_string()));

    // Add user agent if present
    if let Some(user_agent) = request.headers().get("user-agent") {
        if let Ok(ua) = user_agent.to_str() {
            span.set_attribute(opentelemetry::KeyValue::new("http.user_agent", ua.to_string()));
        }
    }

    // Add content length if present
    if let Some(content_length) = request.headers().get("content-length") {
        if let Ok(cl) = content_length.to_str() {
            if let Ok(length) = cl.parse::<i64>() {
                span.set_attribute(opentelemetry::KeyValue::new("http.request_content_length", length));
            }
        }
    }
}

/// Set response attributes on span
fn set_response_attributes(span: &mut Span, response: &Response, duration: std::time::Duration) {
    span.set_attribute(opentelemetry::KeyValue::new("http.status_code", response.status().as_u16() as i64));
    span.set_attribute(opentelemetry::KeyValue::new("http.status_text", response.status().canonical_reason().unwrap_or("Unknown").to_string()));
    span.set_attribute(opentelemetry::KeyValue::new("http.duration_ms", duration.as_millis() as f64));

    // Add response content length if present
    if let Some(content_length) = response.headers().get("content-length") {
        if let Ok(cl) = content_length.to_str() {
            if let Ok(length) = cl.parse::<i64>() {
                span.set_attribute(opentelemetry::KeyValue::new("http.response_content_length", length));
            }
        }
    }
}

/// Database operation tracing middleware
pub async fn database_tracing_middleware(
    operation: &str,
    table: &str,
    observability_manager: Option<Arc<ObservabilityManager>>,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    if let Some(manager) = observability_manager {
        let advanced_tracing = tracing::AdvancedTracing::new(manager);
        let mut span = advanced_tracing.create_database_span(operation, table, None);

        // Set additional database attributes
        span.set_attribute(opentelemetry::KeyValue::new("db.system", "postgresql"));
        span.set_attribute(opentelemetry::KeyValue::new("db.operation", operation.to_string()));
        span.set_attribute(opentelemetry::KeyValue::new("db.table", table.to_string()));

        // The span will be automatically ended when it goes out of scope
    }

    Ok(())
}

/// External service call tracing
pub async fn external_service_tracing_middleware(
    service: &str,
    operation: &str,
    observability_manager: Option<Arc<ObservabilityManager>>,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    if let Some(manager) = observability_manager {
        let advanced_tracing = tracing::AdvancedTracing::new(manager);
        let mut span = advanced_tracing.create_external_service_span(service, operation, None);

        // Set additional service attributes
        span.set_attribute(opentelemetry::KeyValue::new("external.service", service.to_string()));
        span.set_attribute(opentelemetry::KeyValue::new("external.operation", operation.to_string()));

        // The span will be automatically ended when it goes out of scope
    }

    Ok(())
}

/// Error tracing helper
pub fn trace_error(
    error: &str,
    error_type: &str,
    observability_manager: Option<Arc<ObservabilityManager>>,
) {
    if let Some(manager) = observability_manager {
        let advanced_tracing = tracing::AdvancedTracing::new(manager);
        let context = opentelemetry::Context::current();

        if let Ok(mut span) = context.span().try_clone() {
            advanced_tracing.record_error(&mut span, error, error_type);
        }
    }
}

/// Exception tracing helper
pub fn trace_exception(
    exception: &str,
    stack_trace: Option<&str>,
    observability_manager: Option<Arc<ObservabilityManager>>,
) {
    if let Some(manager) = observability_manager {
        let advanced_tracing = tracing::AdvancedTracing::new(manager);
        let context = opentelemetry::Context::current();

        if let Ok(mut span) = context.span().try_clone() {
            advanced_tracing.record_exception(&mut span, exception, stack_trace);
        }
    }
}