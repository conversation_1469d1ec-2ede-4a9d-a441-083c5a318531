use anyhow::Result;
use clap::{Parser, Subcommand};
use infinitium_signal::demo::InfinitiumDemo;
use std::path::PathBuf;
use tracing::info;

#[derive(Parser)]
#[command(name = "infinitium-demo")]
#[command(about = "Infinitium Signal Demo - Showcase cybersecurity capabilities")]
#[command(version = env!("CARGO_PKG_VERSION"))]
struct Cli {
    /// Log level
    #[arg(long, env = "RUST_LOG")]
    log_level: Option<String>,

    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// Run interactive demo
    Run {
        /// Target path to scan (optional)
        #[arg(value_name = "PATH")]
        target: Option<PathBuf>,
    },
    /// Generate SBOM for a project
    Sbom {
        /// Target path to scan
        #[arg(value_name = "PATH")]
        target: PathBuf,

        /// Output format
        #[arg(short, long, default_value = "cyclonedx-json")]
        format: String,
    },
    /// Scan for vulnerabilities
    Scan {
        /// Target path to scan
        #[arg(value_name = "PATH")]
        target: PathBuf,

        /// Output format
        #[arg(short, long, default_value = "json")]
        format: String,
    },
}

#[tokio::main]
async fn main() -> Result<()> {
    // Parse command line arguments
    let cli = Cli::parse();

    // Setup basic logging
    if std::env::var("RUST_LOG").is_err() {
        std::env::set_var("RUST_LOG", "info");
    }
    tracing_subscriber::fmt::init();

    info!(
        "Starting Infinitium Signal Demo v{}",
        env!("CARGO_PKG_VERSION")
    );

    match cli.command {
        Commands::Run { target } => {
            let demo = InfinitiumDemo::new();
            demo.run_demo(target.as_deref()).await?;
        }
        Commands::Sbom { target, format } => {
            println!("🔍 Generating SBOM for: {}", target.display());
            println!("📄 Format: {}", format);

            let output = std::process::Command::new("syft")
                .arg(target.to_string_lossy().as_ref())
                .arg("-o")
                .arg(&format)
                .output();

            match output {
                Ok(output) if output.status.success() => {
                    let sbom = String::from_utf8_lossy(&output.stdout);
                    println!("{}", sbom);
                }
                Ok(output) => {
                    let error = String::from_utf8_lossy(&output.stderr);
                    eprintln!("❌ SBOM generation failed: {}", error);
                    std::process::exit(1);
                }
                Err(e) => {
                    eprintln!("❌ Failed to run Syft: {}", e);
                    std::process::exit(1);
                }
            }
        }
        Commands::Scan { target, format } => {
            println!("🔍 Scanning for vulnerabilities: {}", target.display());
            println!("📄 Format: {}", format);

            let output = std::process::Command::new("trivy")
                .arg("fs")
                .arg("--format")
                .arg(&format)
                .arg(target.to_string_lossy().as_ref())
                .output();

            match output {
                Ok(output) if output.status.success() => {
                    let scan_result = String::from_utf8_lossy(&output.stdout);
                    println!("{}", scan_result);
                }
                Ok(output) => {
                    let error = String::from_utf8_lossy(&output.stderr);
                    eprintln!("❌ Vulnerability scan failed: {}", error);
                    std::process::exit(1);
                }
                Err(e) => {
                    eprintln!("❌ Failed to run Trivy: {}", e);
                    std::process::exit(1);
                }
            }
        }
    }

    Ok(())
}
