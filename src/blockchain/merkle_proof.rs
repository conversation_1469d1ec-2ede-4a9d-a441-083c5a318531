use crate::{
    config::BlockchainConfig,
    error::{InfinitumError, Result},
};
use serde::{Deserialize, Serialize};
use sha2::{Digest, Sha256};
use tracing::{debug, info, instrument};

/// Merkle tree implementation for blockchain integrity
pub struct MerkleService {
    #[allow(dead_code)]
    config: BlockchainConfig,
}

/// Merkle proof structure
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub struct MerkleProof {
    /// Root hash of the Merkle tree
    pub root_hash: String,
    /// Leaf hash being proven
    pub leaf_hash: String,
    /// Proof path (sibling hashes)
    pub proof_path: Vec<MerkleProofNode>,
    /// Tree depth
    pub depth: usize,
    /// Index of the leaf in the tree
    pub leaf_index: usize,
}

/// Merkle proof node
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq, Eq)]
pub struct MerkleProofNode {
    /// Hash value
    pub hash: String,
    /// Position (left or right)
    pub position: MerklePosition,
}

/// Position in Merkle tree
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq, Eq)]
pub enum MerklePosition {
    /// Left child
    Left,
    /// Right child
    Right,
}

/// Merkle tree structure
#[derive(Debug, Clone)]
pub struct MerkleTree {
    /// Tree nodes organized by levels
    pub levels: Vec<Vec<String>>,
    /// Root hash
    pub root_hash: String,
    /// Leaf count
    pub leaf_count: usize,
}

/// Merkle tree builder
pub struct MerkleTreeBuilder {
    /// Leaf hashes
    leaves: Vec<String>,
    /// Hash function to use
    hash_function: HashFunction,
}

/// Supported hash functions
#[derive(Debug, Clone)]
pub enum HashFunction {
    /// SHA-256
    Sha256,
    /// Blake3
    Blake3,
}

impl MerkleService {
    /// Create new Merkle service
    pub fn new(config: &BlockchainConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }

    /// Generate Merkle proof for data
    #[instrument(skip(self, data))]
    pub async fn generate_proof(&self, data: &[u8], tree_data: &[Vec<u8>]) -> Result<MerkleProof> {
        info!("Generating Merkle proof for data");

        // Build Merkle tree from all data
        let tree = self.build_merkle_tree(tree_data)?;

        // Find the leaf hash for our data
        let leaf_hash = self.hash_data(data);
        let leaf_index = tree.levels[0]
            .iter()
            .position(|h| h == &leaf_hash)
            .ok_or_else(|| InfinitumError::Internal {
                message: "Data not found in Merkle tree".to_string(),
            })?;

        // Generate proof path
        let proof_path = self.generate_proof_path(&tree, leaf_index)?;

        let proof = MerkleProof {
            root_hash: tree.root_hash.clone(),
            leaf_hash,
            proof_path,
            depth: tree.levels.len() - 1,
            leaf_index,
        };

        debug!(
            "Generated Merkle proof with {} nodes",
            proof.proof_path.len()
        );
        Ok(proof)
    }

    /// Verify Merkle proof
    #[instrument(skip(self, proof))]
    pub async fn verify_proof(&self, proof: &MerkleProof, data_hash: &str) -> Result<bool> {
        info!("Verifying Merkle proof");

        // Check if the provided data hash matches the leaf hash in proof
        if proof.leaf_hash != data_hash {
            debug!("Data hash mismatch in proof verification");
            return Ok(false);
        }

        // Reconstruct root hash from proof path
        let reconstructed_root = self.reconstruct_root_hash(proof)?;

        let is_valid = reconstructed_root == proof.root_hash;
        debug!("Merkle proof verification result: {}", is_valid);

        Ok(is_valid)
    }

    /// Build Merkle tree from data
    pub fn build_merkle_tree(&self, data: &[Vec<u8>]) -> Result<MerkleTree> {
        if data.is_empty() {
            return Err(InfinitumError::Internal {
                message: "Cannot build Merkle tree from empty data".to_string(),
            });
        }

        let mut builder = MerkleTreeBuilder::new(HashFunction::Sha256);

        // Add all data as leaves
        for item in data {
            builder.add_leaf(item);
        }

        builder.build()
    }

    /// Generate proof path for a leaf
    fn generate_proof_path(
        &self,
        tree: &MerkleTree,
        leaf_index: usize,
    ) -> Result<Vec<MerkleProofNode>> {
        let mut proof_path = Vec::new();
        let mut current_index = leaf_index;

        // Traverse from leaf to root
        for level in 0..tree.levels.len() - 1 {
            let current_level = &tree.levels[level];

            // Find sibling
            let sibling_index = if current_index % 2 == 0 {
                current_index + 1
            } else {
                current_index - 1
            };

            // Add sibling to proof path if it exists
            if sibling_index < current_level.len() {
                let position = if current_index % 2 == 0 {
                    MerklePosition::Right
                } else {
                    MerklePosition::Left
                };

                proof_path.push(MerkleProofNode {
                    hash: current_level[sibling_index].clone(),
                    position,
                });
            }

            // Move to parent index
            current_index /= 2;
        }

        Ok(proof_path)
    }

    /// Reconstruct root hash from proof
    fn reconstruct_root_hash(&self, proof: &MerkleProof) -> Result<String> {
        let mut current_hash = proof.leaf_hash.clone();

        for node in &proof.proof_path {
            current_hash = match node.position {
                MerklePosition::Left => self.hash_pair(&node.hash, &current_hash),
                MerklePosition::Right => self.hash_pair(&current_hash, &node.hash),
            };
        }

        Ok(current_hash)
    }

    /// Hash data using configured hash function
    fn hash_data(&self, data: &[u8]) -> String {
        let mut hasher = Sha256::new();
        hasher.update(data);
        hex::encode(hasher.finalize())
    }

    /// Hash a pair of values
    fn hash_pair(&self, left: &str, right: &str) -> String {
        let combined = format!("{}{}", left, right);
        self.hash_data(combined.as_bytes())
    }
}

impl MerkleTreeBuilder {
    /// Create new Merkle tree builder
    pub fn new(hash_function: HashFunction) -> Self {
        Self {
            leaves: Vec::new(),
            hash_function,
        }
    }

    /// Add leaf data
    pub fn add_leaf(&mut self, data: &[u8]) {
        let hash = self.hash_data(data);
        self.leaves.push(hash);
    }

    /// Build the Merkle tree
    pub fn build(self) -> Result<MerkleTree> {
        if self.leaves.is_empty() {
            return Err(InfinitumError::Internal {
                message: "Cannot build tree with no leaves".to_string(),
            });
        }

        let leaf_count = self.leaves.len();
        let leaves_clone = self.leaves.clone();
        let mut levels = vec![leaves_clone.clone()];
        let mut current_level = leaves_clone;

        // Build tree level by level
        while current_level.len() > 1 {
            let mut next_level = Vec::new();

            // Process pairs
            for chunk in current_level.chunks(2) {
                let hash = if chunk.len() == 2 {
                    self.hash_pair(&chunk[0], &chunk[1])
                } else {
                    // Odd number of nodes, duplicate the last one
                    self.hash_pair(&chunk[0], &chunk[0])
                };
                next_level.push(hash);
            }

            levels.push(next_level.clone());
            current_level = next_level;
        }

        let root_hash = current_level[0].clone();

        Ok(MerkleTree {
            levels,
            root_hash,
            leaf_count,
        })
    }

    /// Hash data using the configured hash function
    fn hash_data(&self, data: &[u8]) -> String {
        match self.hash_function {
            HashFunction::Sha256 => {
                let mut hasher = Sha256::new();
                hasher.update(data);
                hex::encode(hasher.finalize())
            }
            HashFunction::Blake3 => {
                let hash = blake3::hash(data);
                hex::encode(hash.as_bytes())
            }
        }
    }

    /// Hash a pair of values
    fn hash_pair(&self, left: &str, right: &str) -> String {
        let combined = format!("{}{}", left, right);
        self.hash_data(combined.as_bytes())
    }
}

impl MerkleTree {
    /// Get tree statistics
    pub fn stats(&self) -> MerkleTreeStats {
        MerkleTreeStats {
            leaf_count: self.leaf_count,
            depth: self.levels.len() - 1,
            total_nodes: self.levels.iter().map(|level| level.len()).sum(),
            root_hash: self.root_hash.clone(),
        }
    }

    /// Validate tree structure
    pub fn validate(&self) -> Result<()> {
        if self.levels.is_empty() {
            return Err(InfinitumError::Internal {
                message: "Merkle tree has no levels".to_string(),
            });
        }

        if self.levels.last().unwrap().len() != 1 {
            return Err(InfinitumError::Internal {
                message: "Merkle tree root level should have exactly one node".to_string(),
            });
        }

        // Validate each level has correct number of nodes
        for i in 1..self.levels.len() {
            let prev_level_size = self.levels[i - 1].len();
            let current_level_size = self.levels[i].len();
            let expected_size = prev_level_size.div_ceil(2);

            if current_level_size != expected_size {
                return Err(InfinitumError::Internal {
                    message: format!(
                        "Invalid level size at level {}: expected {}, got {}",
                        i, expected_size, current_level_size
                    ),
                });
            }
        }

        Ok(())
    }
}

/// Merkle tree statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MerkleTreeStats {
    /// Number of leaves
    pub leaf_count: usize,
    /// Tree depth
    pub depth: usize,
    /// Total number of nodes
    pub total_nodes: usize,
    /// Root hash
    pub root_hash: String,
}

/// Merkle proof verification result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MerkleVerificationResult {
    /// Whether the proof is valid
    pub valid: bool,
    /// Root hash used for verification
    pub root_hash: String,
    /// Leaf hash that was verified
    pub leaf_hash: String,
    /// Verification timestamp
    pub verified_at: chrono::DateTime<chrono::Utc>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_merkle_tree_builder() {
        let mut builder = MerkleTreeBuilder::new(HashFunction::Sha256);
        builder.add_leaf(b"data1");
        builder.add_leaf(b"data2");
        builder.add_leaf(b"data3");
        builder.add_leaf(b"data4");

        let tree = builder.build().unwrap();
        assert_eq!(tree.leaf_count, 4);
        assert_eq!(tree.levels.len(), 3); // leaves, intermediate, root
        assert!(tree.validate().is_ok());
    }

    #[test]
    fn test_merkle_tree_single_leaf() {
        let mut builder = MerkleTreeBuilder::new(HashFunction::Sha256);
        builder.add_leaf(b"single_data");

        let tree = builder.build().unwrap();
        assert_eq!(tree.leaf_count, 1);
        assert_eq!(tree.levels.len(), 1); // only root
        assert!(tree.validate().is_ok());
    }

    #[test]
    fn test_merkle_tree_odd_leaves() {
        let mut builder = MerkleTreeBuilder::new(HashFunction::Sha256);
        builder.add_leaf(b"data1");
        builder.add_leaf(b"data2");
        builder.add_leaf(b"data3");

        let tree = builder.build().unwrap();
        assert_eq!(tree.leaf_count, 3);
        assert!(tree.validate().is_ok());
    }

    #[test]
    fn test_hash_functions() {
        let data = b"test_data";

        let sha256_builder = MerkleTreeBuilder::new(HashFunction::Sha256);
        let sha256_hash = sha256_builder.hash_data(data);

        let blake3_builder = MerkleTreeBuilder::new(HashFunction::Blake3);
        let blake3_hash = blake3_builder.hash_data(data);

        assert_ne!(sha256_hash, blake3_hash);
        assert_eq!(sha256_hash.len(), 64); // SHA-256 produces 32 bytes = 64 hex chars
        assert_eq!(blake3_hash.len(), 64); // Blake3 produces 32 bytes = 64 hex chars
    }

    #[tokio::test]
    async fn test_merkle_service() {
        let config = BlockchainConfig::default();
        let service = MerkleService::new(&config);

        let data = vec![
            b"data1".to_vec(),
            b"data2".to_vec(),
            b"data3".to_vec(),
            b"data4".to_vec(),
        ];

        let proof = service.generate_proof(b"data2", &data).await.unwrap();
        assert_eq!(proof.leaf_index, 1);
        assert!(!proof.proof_path.is_empty());

        let is_valid = service
            .verify_proof(&proof, &proof.leaf_hash)
            .await
            .unwrap();
        assert!(is_valid);
    }
}
