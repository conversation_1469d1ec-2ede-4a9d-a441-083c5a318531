use crate::{
    compliance::ComplianceReport,
    config::BlockchainConfig,
    error::{InfinitumError, Result},
};
use serde::{Deserialize, Serialize};
use sha2::Digest;
use std::collections::HashMap;
use tracing::{debug, info, instrument};

/// Verifiable credentials service
pub struct CredentialService {
    #[allow(dead_code)]
    config: BlockchainConfig,
    issuer_did: String,
    signing_key: ed25519_dalek::SigningKey,
    verifying_key: ed25519_dalek::VerifyingKey,
}

/// W3C Verifiable Credential
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VerifiableCredential {
    /// JSON-LD context
    #[serde(rename = "@context")]
    pub context: Vec<String>,
    /// Credential ID
    pub id: String,
    /// Credential type
    #[serde(rename = "type")]
    pub credential_type: Vec<String>,
    /// Issuer information
    pub issuer: CredentialIssuer,
    /// Issuance date
    #[serde(rename = "issuanceDate")]
    pub issuance_date: chrono::DateTime<chrono::Utc>,
    /// Expiration date
    #[serde(rename = "expirationDate")]
    pub expiration_date: Option<chrono::DateTime<chrono::Utc>>,
    /// Credential subject
    #[serde(rename = "credentialSubject")]
    pub credential_subject: CredentialSubject,
    /// Proof
    pub proof: CredentialProof,
}

/// Credential issuer
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CredentialIssuer {
    /// Issuer DID
    pub id: String,
    /// Issuer name
    pub name: String,
    /// Issuer type
    #[serde(rename = "type")]
    pub issuer_type: String,
    /// Issuer description
    pub description: Option<String>,
    /// Issuer URL
    pub url: Option<String>,
}

/// Credential subject
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CredentialSubject {
    /// Subject ID
    pub id: String,
    /// Subject type
    #[serde(rename = "type")]
    pub subject_type: String,
    /// Subject claims
    pub claims: HashMap<String, serde_json::Value>,
    /// Evidence
    pub evidence: Vec<CredentialEvidence>,
}

/// Credential evidence
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CredentialEvidence {
    /// Evidence ID
    pub id: String,
    /// Evidence type
    #[serde(rename = "type")]
    pub evidence_type: String,
    /// Evidence description
    pub description: String,
    /// Evidence URL or reference
    pub reference: String,
    /// Evidence hash for integrity
    pub hash: String,
    /// Evidence timestamp
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Credential proof
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CredentialProof {
    /// Proof type
    #[serde(rename = "type")]
    pub proof_type: String,
    /// Creation date
    pub created: chrono::DateTime<chrono::Utc>,
    /// Verification method
    #[serde(rename = "verificationMethod")]
    pub verification_method: String,
    /// Proof purpose
    #[serde(rename = "proofPurpose")]
    pub proof_purpose: String,
    /// Signature value
    #[serde(rename = "proofValue")]
    pub proof_value: String,
}

/// Verifiable presentation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VerifiablePresentation {
    /// JSON-LD context
    #[serde(rename = "@context")]
    pub context: Vec<String>,
    /// Presentation ID
    pub id: String,
    /// Presentation type
    #[serde(rename = "type")]
    pub presentation_type: Vec<String>,
    /// Holder DID
    pub holder: String,
    /// Verifiable credentials
    #[serde(rename = "verifiableCredential")]
    pub verifiable_credential: Vec<VerifiableCredential>,
    /// Proof
    pub proof: CredentialProof,
}

/// Credential status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CredentialStatus {
    /// Status ID
    pub id: String,
    /// Status type
    #[serde(rename = "type")]
    pub status_type: String,
    /// Status list index
    #[serde(rename = "statusListIndex")]
    pub status_list_index: u32,
    /// Status list credential
    #[serde(rename = "statusListCredential")]
    pub status_list_credential: String,
}

/// Credential schema
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CredentialSchema {
    /// Schema ID
    pub id: String,
    /// Schema type
    #[serde(rename = "type")]
    pub schema_type: String,
    /// Schema definition
    pub schema: serde_json::Value,
}

impl CredentialService {
    /// Create new credential service
    pub fn new(config: &BlockchainConfig) -> Self {
        // Generate or load signing keys
        let mut csprng = rand::rngs::OsRng;
        let secret_key_bytes: [u8; 32] = rand::Rng::gen(&mut csprng);
        let signing_key = ed25519_dalek::SigningKey::from_bytes(&secret_key_bytes);
        let verifying_key = signing_key.verifying_key();

        // Generate issuer DID
        let issuer_did = format!("did:infinitum:{}", hex::encode(verifying_key.as_bytes()));

        Self {
            config: config.clone(),
            issuer_did,
            signing_key,
            verifying_key,
        }
    }

    /// Issue compliance credential
    #[instrument(skip(self, report))]
    pub async fn issue_compliance_credential(
        &self,
        report: &ComplianceReport,
    ) -> Result<VerifiableCredential> {
        info!(
            "Issuing compliance credential for report: {}",
            report.request.id
        );

        let credential_id = format!("urn:uuid:{}", uuid::Uuid::new_v4());
        let now = chrono::Utc::now();

        // Create credential subject
        let mut claims = HashMap::new();
        claims.insert(
            "complianceFramework".to_string(),
            serde_json::Value::String(format!("{:?}", report.request.framework)),
        );
        claims.insert(
            "complianceScore".to_string(),
            serde_json::Value::Number(
                serde_json::Number::from_f64(report.summary.compliance_score).unwrap(),
            ),
        );
        claims.insert(
            "organization".to_string(),
            serde_json::Value::String(report.request.config.organization.clone()),
        );
        claims.insert(
            "assessmentDate".to_string(),
            serde_json::Value::String(report.generated_at.to_rfc3339()),
        );

        let evidence = vec![CredentialEvidence {
            id: format!("evidence:{}", report.request.id),
            evidence_type: "ComplianceReport".to_string(),
            description: "Automated compliance assessment report".to_string(),
            reference: format!("report:{}", report.request.id),
            hash: self.calculate_hash(&serde_json::to_string(report)?)?,
            timestamp: report.generated_at,
        }];

        let credential_subject = CredentialSubject {
            id: format!(
                "did:infinitum:org:{}",
                self.sanitize_org_name(&report.request.config.organization)
            ),
            subject_type: "Organization".to_string(),
            claims,
            evidence,
        };

        // Create issuer
        let issuer = CredentialIssuer {
            id: self.issuer_did.clone(),
            name: "Infinitium Signal Platform".to_string(),
            issuer_type: "CyberSecurityPlatform".to_string(),
            description: Some("Enterprise Cyber-Compliance Platform".to_string()),
            url: Some("https://infinitum-signal.com".to_string()),
        };

        // Create credential without proof first
        let mut credential = VerifiableCredential {
            context: vec![
                "https://www.w3.org/2018/credentials/v1".to_string(),
                "https://infinitum-signal.com/contexts/compliance/v1".to_string(),
            ],
            id: credential_id,
            credential_type: vec![
                "VerifiableCredential".to_string(),
                "ComplianceCredential".to_string(),
            ],
            issuer,
            issuance_date: now,
            expiration_date: Some(now + chrono::Duration::days(365)),
            credential_subject,
            proof: CredentialProof {
                proof_type: "Ed25519Signature2020".to_string(),
                created: now,
                verification_method: format!("{}#key-1", self.issuer_did),
                proof_purpose: "assertionMethod".to_string(),
                proof_value: String::new(), // Will be filled by signing
            },
        };

        // Sign the credential
        let proof_value = self.sign_credential(&credential).await?;
        credential.proof.proof_value = proof_value;

        debug!("Issued compliance credential: {}", credential.id);
        Ok(credential)
    }

    /// Issue security assessment credential
    pub async fn issue_security_credential(
        &self,
        subject_id: &str,
        assessment_data: &serde_json::Value,
    ) -> Result<VerifiableCredential> {
        info!("Issuing security assessment credential for: {}", subject_id);

        let credential_id = format!("urn:uuid:{}", uuid::Uuid::new_v4());
        let now = chrono::Utc::now();

        let mut claims = HashMap::new();
        claims.insert(
            "assessmentType".to_string(),
            serde_json::Value::String("SecurityAssessment".to_string()),
        );
        claims.insert("assessmentData".to_string(), assessment_data.clone());

        let credential_subject = CredentialSubject {
            id: subject_id.to_string(),
            subject_type: "SecurityAssessment".to_string(),
            claims,
            evidence: Vec::new(),
        };

        let issuer = CredentialIssuer {
            id: self.issuer_did.clone(),
            name: "Infinitium Signal Platform".to_string(),
            issuer_type: "CyberSecurityPlatform".to_string(),
            description: Some("Enterprise Cyber-Compliance Platform".to_string()),
            url: Some("https://infinitum-signal.com".to_string()),
        };

        let mut credential = VerifiableCredential {
            context: vec![
                "https://www.w3.org/2018/credentials/v1".to_string(),
                "https://infinitum-signal.com/contexts/security/v1".to_string(),
            ],
            id: credential_id,
            credential_type: vec![
                "VerifiableCredential".to_string(),
                "SecurityCredential".to_string(),
            ],
            issuer,
            issuance_date: now,
            expiration_date: Some(now + chrono::Duration::days(90)),
            credential_subject,
            proof: CredentialProof {
                proof_type: "Ed25519Signature2020".to_string(),
                created: now,
                verification_method: format!("{}#key-1", self.issuer_did),
                proof_purpose: "assertionMethod".to_string(),
                proof_value: String::new(),
            },
        };

        let proof_value = self.sign_credential(&credential).await?;
        credential.proof.proof_value = proof_value;

        Ok(credential)
    }

    /// Verify verifiable credential
    #[instrument(skip(self, credential))]
    pub async fn verify_credential(&self, credential: &VerifiableCredential) -> Result<bool> {
        info!("Verifying credential: {}", credential.id);

        // Check expiration
        if let Some(expiration) = credential.expiration_date {
            if chrono::Utc::now() > expiration {
                debug!("Credential has expired");
                return Ok(false);
            }
        }

        // Verify signature
        let is_valid = self.verify_credential_signature(credential).await?;

        debug!("Credential verification result: {}", is_valid);
        Ok(is_valid)
    }

    /// Create verifiable presentation
    pub async fn create_presentation(
        &self,
        holder_did: &str,
        credentials: Vec<VerifiableCredential>,
    ) -> Result<VerifiablePresentation> {
        info!(
            "Creating verifiable presentation for holder: {}",
            holder_did
        );

        let presentation_id = format!("urn:uuid:{}", uuid::Uuid::new_v4());
        let now = chrono::Utc::now();

        let mut presentation = VerifiablePresentation {
            context: vec![
                "https://www.w3.org/2018/credentials/v1".to_string(),
                "https://www.w3.org/2018/presentations/v1".to_string(),
            ],
            id: presentation_id,
            presentation_type: vec!["VerifiablePresentation".to_string()],
            holder: holder_did.to_string(),
            verifiable_credential: credentials,
            proof: CredentialProof {
                proof_type: "Ed25519Signature2020".to_string(),
                created: now,
                verification_method: format!("{}#key-1", holder_did),
                proof_purpose: "authentication".to_string(),
                proof_value: String::new(),
            },
        };

        // Sign the presentation
        let proof_value = self.sign_presentation(&presentation).await?;
        presentation.proof.proof_value = proof_value;

        Ok(presentation)
    }

    /// Sign credential
    async fn sign_credential(&self, credential: &VerifiableCredential) -> Result<String> {
        // Create canonical representation for signing
        let mut credential_copy = credential.clone();
        credential_copy.proof.proof_value = String::new();

        let canonical_data = serde_json::to_string(&credential_copy)?;

        // Sign with Ed25519
        use ed25519_dalek::Signer;
        let signature = self.signing_key.sign(canonical_data.as_bytes());

        Ok(hex::encode(signature.to_bytes()))
    }

    /// Sign presentation
    async fn sign_presentation(&self, presentation: &VerifiablePresentation) -> Result<String> {
        let mut presentation_copy = presentation.clone();
        presentation_copy.proof.proof_value = String::new();

        let canonical_data = serde_json::to_string(&presentation_copy)?;

        use ed25519_dalek::Signer;
        let signature = self.signing_key.sign(canonical_data.as_bytes());

        Ok(hex::encode(signature.to_bytes()))
    }

    /// Verify credential signature
    async fn verify_credential_signature(&self, credential: &VerifiableCredential) -> Result<bool> {
        // Recreate canonical representation
        let mut credential_copy = credential.clone();
        credential_copy.proof.proof_value = String::new();

        let canonical_data = serde_json::to_string(&credential_copy)?;

        // Decode signature
        let signature_bytes =
            hex::decode(&credential.proof.proof_value).map_err(|e| InfinitumError::Internal {
                message: format!("Invalid signature hex: {}", e),
            })?;

        let signature =
            ed25519_dalek::Signature::from_bytes(&signature_bytes.try_into().map_err(|_| {
                InfinitumError::Internal {
                    message: "Invalid signature length".to_string(),
                }
            })?);

        // Verify with public key
        use ed25519_dalek::Verifier;
        Ok(self
            .verifying_key
            .verify(canonical_data.as_bytes(), &signature)
            .is_ok())
    }

    /// Calculate hash of data
    fn calculate_hash(&self, data: &str) -> Result<String> {
        let hash = sha2::Sha256::digest(data.as_bytes());
        Ok(hex::encode(hash))
    }

    /// Sanitize organization name for DID
    fn sanitize_org_name(&self, org_name: &str) -> String {
        org_name
            .chars()
            .map(|c| if c.is_whitespace() { '-' } else { c })
            .filter(|c| c.is_alphanumeric() || *c == '-' || *c == '_')
            .collect::<String>()
            .to_lowercase()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::compliance::{
        ComplianceFramework, ComplianceReport, ComplianceRequest, ReportStatus, ReportSummary,
        RiskLevel,
    };

    #[tokio::test]
    async fn test_credential_service_creation() {
        let config = BlockchainConfig::default();
        let service = CredentialService::new(&config);

        assert!(service.issuer_did.starts_with("did:infinitum:"));
    }

    #[tokio::test]
    async fn test_compliance_credential_issuance() {
        let config = BlockchainConfig::default();
        let service = CredentialService::new(&config);

        let report = ComplianceReport {
            request: ComplianceRequest {
                id: uuid::Uuid::new_v4(),
                framework: ComplianceFramework::CertIn,
                scan_results: vec![],
                config: Default::default(),
                metadata: std::collections::HashMap::new(),
            },
            status: ReportStatus::Completed,
            generated_at: chrono::Utc::now(),
            summary: ReportSummary {
                total_components: 100,
                total_vulnerabilities: 15,
                high_severity_issues: 5,
                medium_severity_issues: 6,
                low_severity_issues: 4,
                compliance_score: 85.5,
                risk_level: RiskLevel::Medium,
            },
            findings: vec![],
            risk_assessment: crate::compliance::RiskAssessment {
                overall_risk_score: 85.5,
                risk_level: RiskLevel::Medium,
                risk_factors: vec![],
                mitigation_strategies: vec![],
            },
            recommendations: vec![],
            output_files: std::collections::HashMap::new(),
            metadata: std::collections::HashMap::new(),
        };

        let credential = service.issue_compliance_credential(&report).await.unwrap();

        assert_eq!(credential.credential_type.len(), 2);
        assert!(credential
            .credential_type
            .contains(&"ComplianceCredential".to_string()));
        assert!(!credential.proof.proof_value.is_empty());
    }

    #[tokio::test]
    async fn test_credential_verification() {
        let config = BlockchainConfig::default();
        let service = CredentialService::new(&config);

        let assessment_data = serde_json::json!({
            "vulnerabilities": 5,
            "severity": "medium"
        });

        let credential = service
            .issue_security_credential("did:example:subject", &assessment_data)
            .await
            .unwrap();

        let is_valid = service.verify_credential(&credential).await.unwrap();
        assert!(is_valid);
    }

    #[test]
    fn test_sanitize_org_name() {
        let config = BlockchainConfig::default();
        let service = CredentialService::new(&config);

        assert_eq!(service.sanitize_org_name("Test Corp!"), "test-corp");
        assert_eq!(service.sanitize_org_name("ABC_123"), "abc_123");
        assert_eq!(service.sanitize_org_name("Special@#$Chars"), "specialchars");
    }
}
