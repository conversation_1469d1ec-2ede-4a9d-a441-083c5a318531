//! # Adaptive Pattern Learner
//!
//! This module provides online learning capabilities for adaptive pattern recognition,
//! including continuous model updates, pattern discovery from detection results,
//! and feedback loop integration for improving license detection accuracy.

use crate::{
    compliance::{
        ml_feature_extractor::{ExtractedFeatures, MLFeatureExtractor},
        ml_model_manager::{MLModelManager, ModelType, ModelMetrics},
        license_pattern_classifier::{LicensePatternClassifier, ClassificationResult},
    },
    error::{InfinitumError, Result},
};
use serde::{Deserialize, Serialize};
use std::{
    collections::{HashMap, VecDeque},
    sync::Arc,
};
use tokio::sync::RwLock;
use tracing::{debug, info, instrument, warn};
use chrono::{DateTime, Utc};

/// Learning configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LearningConfig {
    /// Enable online learning
    pub enable_online_learning: bool,
    /// Learning batch size
    pub batch_size: usize,
    /// Learning interval in seconds
    pub learning_interval_secs: u64,
    /// Minimum samples for retraining
    pub min_samples_for_retraining: usize,
    /// Maximum samples to keep in buffer
    pub max_buffer_size: usize,
    /// Performance improvement threshold for retraining
    pub performance_threshold: f64,
    /// Enable pattern discovery
    pub enable_pattern_discovery: bool,
    /// Pattern discovery confidence threshold
    pub pattern_discovery_threshold: f64,
    /// Feedback loop enabled
    pub enable_feedback_loop: bool,
}

/// Learning sample with metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LearningSample {
    /// License text
    pub text: String,
    /// True license label
    pub true_label: String,
    /// Predicted label
    pub predicted_label: String,
    /// Prediction confidence
    pub confidence: f64,
    /// Feature vector
    pub features: Vec<f64>,
    /// Timestamp of sample
    pub timestamp: DateTime<Utc>,
    /// Source of the sample
    pub source: String,
    /// Additional metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Pattern discovery result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiscoveredPattern {
    /// Pattern identifier
    pub pattern_id: String,
    /// Pattern description
    pub description: String,
    /// Pattern confidence
    pub confidence: f64,
    /// Supporting samples
    pub supporting_samples: Vec<String>,
    /// Pattern features
    pub features: HashMap<String, f64>,
    /// Discovery timestamp
    pub discovered_at: DateTime<Utc>,
    /// Pattern category
    pub category: String,
}

/// Learning statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LearningStats {
    /// Total samples processed
    pub total_samples: usize,
    /// Samples in current buffer
    pub buffer_size: usize,
    /// Retraining events
    pub retraining_events: usize,
    /// Patterns discovered
    pub patterns_discovered: usize,
    /// Average prediction accuracy
    pub avg_accuracy: f64,
    /// Learning rate
    pub learning_rate: f64,
    /// Last learning timestamp
    pub last_learning_at: Option<DateTime<Utc>>,
}

/// Feedback data for continuous improvement
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FeedbackData {
    /// Feedback type
    pub feedback_type: String,
    /// Original prediction
    pub original_prediction: String,
    /// Corrected prediction
    pub corrected_prediction: String,
    /// Feedback confidence
    pub confidence: f64,
    /// Feedback source
    pub source: String,
    /// Timestamp
    pub timestamp: DateTime<Utc>,
    /// Additional context
    pub context: HashMap<String, serde_json::Value>,
}

/// Adaptive Pattern Learner
pub struct AdaptivePatternLearner {
    config: LearningConfig,
    feature_extractor: Arc<MLFeatureExtractor>,
    model_manager: Arc<MLModelManager>,
    classifier: Arc<LicensePatternClassifier>,
    learning_buffer: Arc<RwLock<VecDeque<LearningSample>>>,
    discovered_patterns: Arc<RwLock<HashMap<String, DiscoveredPattern>>>,
    feedback_buffer: Arc<RwLock<VecDeque<FeedbackData>>>,
    learning_stats: Arc<RwLock<LearningStats>>,
}

impl AdaptivePatternLearner {
    /// Create new Adaptive Pattern Learner
    pub fn new(
        config: LearningConfig,
        feature_extractor: Arc<MLFeatureExtractor>,
        model_manager: Arc<MLModelManager>,
        classifier: Arc<LicensePatternClassifier>,
    ) -> Self {
        Self {
            config,
            feature_extractor,
            model_manager,
            classifier,
            learning_buffer: Arc::new(RwLock::new(VecDeque::new())),
            discovered_patterns: Arc::new(RwLock::new(HashMap::new())),
            feedback_buffer: Arc::new(RwLock::new(VecDeque::new())),
            learning_stats: Arc::new(RwLock::new(LearningStats::default())),
        }
    }

    /// Add sample to learning buffer
    #[instrument(skip(self, sample), fields(sample_text_len = sample.text.len()))]
    pub async fn add_learning_sample(&self, sample: LearningSample) -> Result<()> {
        debug!("Adding learning sample: {} -> {}", sample.predicted_label, sample.true_label);

        let mut buffer = self.learning_buffer.write().await;

        // Add sample to buffer
        buffer.push_back(sample);

        // Maintain buffer size
        while buffer.len() > self.config.max_buffer_size {
            buffer.pop_front();
        }

        // Update statistics
        let mut stats = self.learning_stats.write().await;
        stats.total_samples += 1;
        stats.buffer_size = buffer.len();

        // Check if we should trigger learning
        if self.should_trigger_learning(&stats).await {
            drop(stats);
            drop(buffer);
            self.trigger_learning().await?;
        }

        Ok(())
    }

    /// Add feedback data for continuous improvement
    #[instrument(skip(self, feedback))]
    pub async fn add_feedback(&self, feedback: FeedbackData) -> Result<()> {
        debug!("Adding feedback: {} -> {}", feedback.original_prediction, feedback.corrected_prediction);

        let mut feedback_buffer = self.feedback_buffer.write().await;
        feedback_buffer.push_back(feedback);

        // Maintain feedback buffer size
        while feedback_buffer.len() > 100 {
            feedback_buffer.pop_front();
        }

        Ok(())
    }

    /// Process detection result for learning
    #[instrument(skip(self, text, result))]
    pub async fn process_detection_result(&self, text: &str, result: &ClassificationResult) -> Result<()> {
        // Extract features
        let features = self.feature_extractor.extract_features(text, Some(&result.predicted_license)).await?;
        let feature_vector = features.to_feature_vector();

        // Create learning sample
        let sample = LearningSample {
            text: text.to_string(),
            true_label: result.predicted_license.clone(), // Assume prediction is correct for now
            predicted_label: result.predicted_license.clone(),
            confidence: result.confidence,
            features: feature_vector,
            timestamp: Utc::now(),
            source: "detection_result".to_string(),
            metadata: HashMap::from([
                ("confidence".to_string(), serde_json::json!(result.confidence)),
                ("alternatives_count".to_string(), serde_json::json!(result.alternatives.len())),
            ]),
        };

        self.add_learning_sample(sample).await?;

        // Check for pattern discovery
        if self.config.enable_pattern_discovery {
            self.discover_patterns(text, result, &features).await?;
        }

        Ok(())
    }

    /// Trigger online learning process
    #[instrument(skip(self))]
    pub async fn trigger_learning(&self) -> Result<()> {
        info!("Triggering online learning process");

        let buffer = self.learning_buffer.read().await;

        if buffer.len() < self.config.min_samples_for_retraining {
            debug!("Not enough samples for retraining: {} < {}", buffer.len(), self.config.min_samples_for_retraining);
            return Ok(());
        }

        // Prepare training data
        let training_data: Vec<(String, String)> = buffer.iter()
            .map(|sample| (sample.text.clone(), sample.true_label.clone()))
            .collect();

        // Retrain classifier
        let new_version = self.classifier.train_classifier(training_data).await?;

        // Update statistics
        let mut stats = self.learning_stats.write().await;
        stats.retraining_events += 1;
        stats.last_learning_at = Some(Utc::now());

        info!("Online learning completed: new model version {}", new_version);
        Ok(())
    }

    /// Discover new patterns from detection results
    #[instrument(skip(self, text, result, features))]
    async fn discover_patterns(
        &self,
        text: &str,
        result: &ClassificationResult,
        features: &ExtractedFeatures,
    ) -> Result<()> {
        // Simple pattern discovery based on high-confidence predictions
        if result.confidence >= self.config.pattern_discovery_threshold {
            let pattern_id = format!("pattern_{}", Utc::now().timestamp());

            let pattern = DiscoveredPattern {
                pattern_id: pattern_id.clone(),
                description: format!("High-confidence pattern for {} license", result.predicted_license),
                confidence: result.confidence,
                supporting_samples: vec![text.to_string()],
                features: features.license_specific_features.clone(),
                discovered_at: Utc::now(),
                category: "license_pattern".to_string(),
            };

            let mut patterns = self.discovered_patterns.write().await;
            patterns.insert(pattern_id, pattern);

            // Update statistics
            let mut stats = self.learning_stats.write().await;
            stats.patterns_discovered = patterns.len();

            debug!("Discovered new pattern: {}", pattern_id);
        }

        Ok(())
    }

    /// Get learning statistics
    pub async fn get_learning_stats(&self) -> LearningStats {
        self.learning_stats.read().await.clone()
    }

    /// Get discovered patterns
    pub async fn get_discovered_patterns(&self) -> HashMap<String, DiscoveredPattern> {
        self.discovered_patterns.read().await.clone()
    }

    /// Get samples from learning buffer
    pub async fn get_learning_samples(&self, limit: usize) -> Vec<LearningSample> {
        let buffer = self.learning_buffer.read().await;
        buffer.iter().rev().take(limit).cloned().collect()
    }

    /// Clear learning buffer
    #[instrument(skip(self))]
    pub async fn clear_learning_buffer(&self) -> Result<usize> {
        let mut buffer = self.learning_buffer.write().await;
        let cleared_count = buffer.len();
        buffer.clear();

        let mut stats = self.learning_stats.write().await;
        stats.buffer_size = 0;

        info!("Cleared {} samples from learning buffer", cleared_count);
        Ok(cleared_count)
    }

    /// Evaluate learning performance
    #[instrument(skip(self))]
    pub async fn evaluate_learning_performance(&self) -> Result<HashMap<String, f64>> {
        let buffer = self.learning_buffer.read().await;

        if buffer.is_empty() {
            return Ok(HashMap::new());
        }

        let mut correct_predictions = 0;
        let mut total_predictions = 0;
        let mut confidence_sum = 0.0;

        for sample in buffer.iter() {
            total_predictions += 1;
            confidence_sum += sample.confidence;

            if sample.true_label == sample.predicted_label {
                correct_predictions += 1;
            }
        }

        let accuracy = correct_predictions as f64 / total_predictions as f64;
        let avg_confidence = confidence_sum / total_predictions as f64;

        let metrics = HashMap::from([
            ("accuracy".to_string(), accuracy),
            ("avg_confidence".to_string(), avg_confidence),
            ("total_predictions".to_string(), total_predictions as f64),
            ("correct_predictions".to_string(), correct_predictions as f64),
        ]);

        // Update learning stats
        let mut stats = self.learning_stats.write().await;
        stats.avg_accuracy = accuracy;
        stats.learning_rate = if stats.last_learning_at.is_some() {
            let time_since_last_learning = Utc::now().signed_duration_since(stats.last_learning_at.unwrap());
            1.0 / (time_since_last_learning.num_seconds() as f64 / 3600.0) // Learning rate per hour
        } else {
            0.0
        };

        Ok(metrics)
    }

    /// Process feedback loop
    #[instrument(skip(self))]
    pub async fn process_feedback_loop(&self) -> Result<()> {
        if !self.config.enable_feedback_loop {
            return Ok(());
        }

        let feedback_buffer = self.feedback_buffer.read().await;

        if feedback_buffer.is_empty() {
            return Ok(());
        }

        // Process feedback to improve learning
        let mut correction_samples = Vec::new();

        for feedback in feedback_buffer.iter() {
            if feedback.original_prediction != feedback.corrected_prediction {
                // Create correction sample
                let sample = LearningSample {
                    text: "".to_string(), // Would need original text
                    true_label: feedback.corrected_prediction.clone(),
                    predicted_label: feedback.original_prediction.clone(),
                    confidence: feedback.confidence,
                    features: Vec::new(), // Would need to extract features
                    timestamp: feedback.timestamp,
                    source: format!("feedback_{}", feedback.source),
                    metadata: feedback.context.clone(),
                };
                correction_samples.push(sample);
            }
        }

        // Add correction samples to learning buffer
        for sample in correction_samples {
            self.add_learning_sample(sample).await?;
        }

        info!("Processed {} feedback items", feedback_buffer.len());
        Ok(())
    }

    /// Check if learning should be triggered
    async fn should_trigger_learning(&self, stats: &LearningStats) -> bool {
        if !self.config.enable_online_learning {
            return false;
        }

        // Check buffer size
        if stats.buffer_size < self.config.min_samples_for_retraining {
            return false;
        }

        // Check time since last learning
        if let Some(last_learning) = stats.last_learning_at {
            let time_since_last = Utc::now().signed_duration_since(last_learning);
            if time_since_last.num_seconds() < self.config.learning_interval_secs as i64 {
                return false;
            }
        } else {
            // First learning
            return true;
        }

        // Check performance improvement potential
        stats.avg_accuracy < self.config.performance_threshold
    }

    /// Export learning data for analysis
    #[instrument(skip(self))]
    pub async fn export_learning_data(&self) -> Result<serde_json::Value> {
        let stats = self.get_learning_stats().await;
        let patterns = self.get_discovered_patterns().await;
        let samples = self.get_learning_samples(100).await;

        let export_data = serde_json::json!({
            "learning_stats": stats,
            "discovered_patterns": patterns,
            "recent_samples": samples.iter().take(10).collect::<Vec<_>>(),
            "export_timestamp": Utc::now(),
        });

        Ok(export_data)
    }
}

impl Default for LearningConfig {
    fn default() -> Self {
        Self {
            enable_online_learning: true,
            batch_size: 32,
            learning_interval_secs: 3600, // 1 hour
            min_samples_for_retraining: 100,
            max_buffer_size: 1000,
            performance_threshold: 0.8,
            enable_pattern_discovery: true,
            pattern_discovery_threshold: 0.9,
            enable_feedback_loop: true,
        }
    }
}

impl Default for LearningStats {
    fn default() -> Self {
        Self {
            total_samples: 0,
            buffer_size: 0,
            retraining_events: 0,
            patterns_discovered: 0,
            avg_accuracy: 0.0,
            learning_rate: 0.0,
            last_learning_at: None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Arc;

    #[tokio::test]
    async fn test_adaptive_learner_creation() {
        let feature_config = crate::compliance::ml_feature_extractor::FeatureExtractorConfig::default();
        let feature_extractor = Arc::new(MLFeatureExtractor::new(feature_config));

        let model_config = crate::compliance::ml_model_manager::MLModelConfig::default();
        let model_manager = Arc::new(MLModelManager::new(model_config));

        let classifier_config = crate::compliance::license_pattern_classifier::ClassifierConfig::default();
        let classifier = Arc::new(LicensePatternClassifier::new(
            classifier_config,
            Arc::clone(&feature_extractor),
            Arc::clone(&model_manager),
        ));

        let learning_config = LearningConfig::default();
        let learner = AdaptivePatternLearner::new(
            learning_config,
            feature_extractor,
            model_manager,
            classifier,
        );

        let stats = learner.get_learning_stats().await;
        assert_eq!(stats.total_samples, 0);
    }

    #[tokio::test]
    async fn test_learning_sample_addition() {
        let feature_config = crate::compliance::ml_feature_extractor::FeatureExtractorConfig::default();
        let feature_extractor = Arc::new(MLFeatureExtractor::new(feature_config));

        let model_config = crate::compliance::ml_model_manager::MLModelConfig::default();
        let model_manager = Arc::new(MLModelManager::new(model_config));

        let classifier_config = crate::compliance::license_pattern_classifier::ClassifierConfig::default();
        let classifier = Arc::new(LicensePatternClassifier::new(
            classifier_config,
            Arc::clone(&feature_extractor),
            Arc::clone(&model_manager),
        ));

        let learning_config = LearningConfig::default();
        let learner = AdaptivePatternLearner::new(
            learning_config,
            feature_extractor,
            model_manager,
            classifier,
        );

        let sample = LearningSample {
            text: "MIT License text".to_string(),
            true_label: "MIT".to_string(),
            predicted_label: "MIT".to_string(),
            confidence: 0.9,
            features: vec![1.0, 2.0, 3.0],
            timestamp: Utc::now(),
            source: "test".to_string(),
            metadata: HashMap::new(),
        };

        learner.add_learning_sample(sample).await.unwrap();

        let stats = learner.get_learning_stats().await;
        assert_eq!(stats.total_samples, 1);
        assert_eq!(stats.buffer_size, 1);
    }

    #[tokio::test]
    async fn test_feedback_processing() {
        let feature_config = crate::compliance::ml_feature_extractor::FeatureExtractorConfig::default();
        let feature_extractor = Arc::new(MLFeatureExtractor::new(feature_config));

        let model_config = crate::compliance::ml_model_manager::MLModelConfig::default();
        let model_manager = Arc::new(MLModelManager::new(model_config));

        let classifier_config = crate::compliance::license_pattern_classifier::ClassifierConfig::default();
        let classifier = Arc::new(LicensePatternClassifier::new(
            classifier_config,
            Arc::clone(&feature_extractor),
            Arc::clone(&model_manager),
        ));

        let learning_config = LearningConfig::default();
        let learner = AdaptivePatternLearner::new(
            learning_config,
            feature_extractor,
            model_manager,
            classifier,
        );

        let feedback = FeedbackData {
            feedback_type: "correction".to_string(),
            original_prediction: "MIT".to_string(),
            corrected_prediction: "Apache-2.0".to_string(),
            confidence: 0.8,
            source: "user".to_string(),
            timestamp: Utc::now(),
            context: HashMap::new(),
        };

        learner.add_feedback(feedback).await.unwrap();
        assert!(learner.process_feedback_loop().await.is_ok());
    }
}