//! # CI/CD Configuration Management
//!
//! Centralized configuration management for all CI/CD integrations with
//! validation, optimization, and environment-specific overrides.

use crate::{
    compliance::{
        ci_cd_scanner::{CICDPlaformConfig, CIOutputFormat, ScanMode, RetryConfig},
        github_actions_integration::GitHubActionsConfig,
        gitlab_ci_integration::GitLabCIConfig,
        jenkins_integration::JenkinsConfig,
        docker_integration::DockerIntegrationConfig,
        pipeline_orchestrator::PipelineOrchestratorConfig,
        ComplianceConfig,
    },
    config::ScanningConfig,
    error::{InfinitumError, Result},
};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    env,
    fs,
    path::{Path, PathBuf},
};
use tracing::{debug, info, warn};

/// Main CI/CD configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CICDPlaformMasterConfig {
    /// Version of the configuration
    pub version: String,
    /// Global settings
    pub global: GlobalConfig,
    /// Platform-specific configurations
    pub platforms: PlatformConfigs,
    /// Pipeline orchestrator configuration
    pub orchestrator: PipelineOrchestratorConfig,
    /// Environment-specific overrides
    pub environments: HashMap<String, EnvironmentOverride>,
}

/// Global CI/CD configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlobalConfig {
    /// Default scan mode
    pub default_scan_mode: ScanMode,
    /// Default output format
    pub default_output_format: CIOutputFormat,
    /// Default compliance threshold
    pub default_compliance_threshold: f64,
    /// Default timeout
    pub default_timeout_seconds: u64,
    /// Enable caching by default
    pub enable_caching: bool,
    /// Default retry configuration
    pub default_retry_config: RetryConfig,
    /// Global environment variables
    pub global_env: HashMap<String, String>,
    /// Default include patterns
    pub default_include_patterns: Vec<String>,
    /// Default exclude patterns
    pub default_exclude_patterns: Vec<String>,
}

/// Platform-specific configurations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlatformConfigs {
    /// GitHub Actions configuration
    pub github_actions: Option<GitHubActionsConfig>,
    /// GitLab CI configuration
    pub gitlab_ci: Option<GitLabCIConfig>,
    /// Jenkins configuration
    pub jenkins: Option<JenkinsConfig>,
    /// Docker configuration
    pub docker: Option<DockerIntegrationConfig>,
}

/// Environment-specific overrides
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnvironmentOverride {
    /// Environment name
    pub name: String,
    /// Override values
    pub overrides: HashMap<String, serde_json::Value>,
    /// Enabled platforms for this environment
    pub enabled_platforms: Vec<String>,
    /// Environment-specific variables
    pub env_variables: HashMap<String, String>,
}

/// Configuration validation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigValidationResult {
    /// Whether configuration is valid
    pub is_valid: bool,
    /// Validation errors
    pub errors: Vec<String>,
    /// Validation warnings
    pub warnings: Vec<String>,
    /// Configuration suggestions
    pub suggestions: Vec<String>,
}

/// CI/CD configuration manager
pub struct CICDPlaformConfigManager {
    config: CICDPlaformMasterConfig,
    config_path: Option<PathBuf>,
    environment: String,
}

impl CICDPlaformConfigManager {
    /// Create new configuration manager
    pub fn new() -> Self {
        Self {
            config: CICDPlaformMasterConfig::default(),
            config_path: None,
            environment: env::var("CI_CD_ENV").unwrap_or_else(|_| "development".to_string()),
        }
    }

    /// Load configuration from file
    pub fn load_from_file<P: AsRef<Path>>(&mut self, path: P) -> Result<()> {
        let path = path.as_ref();
        info!("Loading CI/CD configuration from: {}", path.display());

        let content = fs::read_to_string(path)
            .map_err(|e| InfinitumError::Io {
                message: format!("Failed to read config file {}: {}", path.display(), e),
            })?;

        self.config = serde_yaml::from_str(&content)
            .map_err(|e| InfinitumError::Serialization {
                message: format!("Failed to parse config file: {}", e),
            })?;

        self.config_path = Some(path.to_path_buf());

        // Apply environment overrides
        self.apply_environment_overrides()?;

        info!("CI/CD configuration loaded successfully");
        Ok(())
    }

    /// Save configuration to file
    pub fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let path = path.as_ref();
        info!("Saving CI/CD configuration to: {}", path.display());

        let content = serde_yaml::to_string(&self.config)
            .map_err(|e| InfinitumError::Serialization {
                message: format!("Failed to serialize config: {}", e),
            })?;

        fs::write(path, content)
            .map_err(|e| InfinitumError::Io {
                message: format!("Failed to write config file {}: {}", path.display(), e),
            })?;

        info!("CI/CD configuration saved successfully");
        Ok(())
    }

    /// Validate configuration
    pub fn validate(&self) -> Result<ConfigValidationResult> {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        let mut suggestions = Vec::new();

        // Validate global configuration
        self.validate_global_config(&mut errors, &mut warnings, &mut suggestions);

        // Validate platform configurations
        self.validate_platform_configs(&mut errors, &mut warnings, &mut suggestions);

        // Validate orchestrator configuration
        self.validate_orchestrator_config(&mut errors, &mut warnings, &mut suggestions);

        // Validate environment overrides
        self.validate_environment_overrides(&mut errors, &mut warnings, &mut suggestions);

        let is_valid = errors.is_empty();

        Ok(ConfigValidationResult {
            is_valid,
            errors,
            warnings,
            suggestions,
        })
    }

    /// Validate global configuration
    fn validate_global_config(&self, errors: &mut Vec<String>, warnings: &mut Vec<String>, suggestions: &mut Vec<String>) {
        let global = &self.config.global;

        if global.default_compliance_threshold < 0.0 || global.default_compliance_threshold > 100.0 {
            errors.push("Default compliance threshold must be between 0 and 100".to_string());
        }

        if global.default_timeout_seconds == 0 {
            errors.push("Default timeout must be greater than 0".to_string());
        }

        if global.default_include_patterns.is_empty() {
            warnings.push("No default include patterns specified".to_string());
            suggestions.push("Consider adding default include patterns like '**/*'".to_string());
        }

        if global.default_exclude_patterns.is_empty() {
            suggestions.push("Consider adding default exclude patterns for common directories".to_string());
        }
    }

    /// Validate platform configurations
    fn validate_platform_configs(&self, errors: &mut Vec<String>, warnings: &mut Vec<String>, suggestions: &mut Vec<String>) {
        let platforms = &self.config.platforms;

        // Check if at least one platform is configured
        let configured_platforms = vec![
            platforms.github_actions.is_some(),
            platforms.gitlab_ci.is_some(),
            platforms.jenkins.is_some(),
            platforms.docker.is_some(),
        ].iter().filter(|&&x| x).count();

        if configured_platforms == 0 {
            warnings.push("No CI/CD platforms configured".to_string());
            suggestions.push("Configure at least one CI/CD platform (GitHub Actions, GitLab CI, Jenkins, or Docker)".to_string());
        }

        // Validate individual platform configurations
        if let Some(github_config) = &platforms.github_actions {
            if let Err(e) = github_config.validate_config() {
                errors.extend(e);
            }
        }

        if let Some(gitlab_config) = &platforms.gitlab_ci {
            if let Err(e) = gitlab_config.validate_config() {
                errors.extend(e);
            }
        }

        if let Some(jenkins_config) = &platforms.jenkins {
            if let Err(e) = jenkins_config.validate_config() {
                errors.extend(e);
            }
        }

        if let Some(docker_config) = &platforms.docker {
            if let Err(e) = docker_config.validate_config() {
                errors.extend(e);
            }
        }
    }

    /// Validate orchestrator configuration
    fn validate_orchestrator_config(&self, errors: &mut Vec<String>, _warnings: &mut Vec<String>, _suggestions: &mut Vec<String>) {
        let orchestrator = &self.config.orchestrator;

        if orchestrator.max_concurrent_pipelines == 0 {
            errors.push("Maximum concurrent pipelines must be greater than 0".to_string());
        }

        if orchestrator.pipeline_timeout_seconds == 0 {
            errors.push("Pipeline timeout must be greater than 0".to_string());
        }
    }

    /// Validate environment overrides
    fn validate_environment_overrides(&self, _errors: &mut Vec<String>, _warnings: &mut Vec<String>, _suggestions: &mut Vec<String>) {
        // Environment validation logic would go here
        // For now, this is a placeholder
    }

    /// Apply environment-specific overrides
    fn apply_environment_overrides(&mut self) -> Result<()> {
        if let Some(env_override) = self.config.environments.get(&self.environment) {
            debug!("Applying environment overrides for: {}", self.environment);

            // Apply global overrides
            for (key, value) in &env_override.overrides {
                match key.as_str() {
                    "default_scan_mode" => {
                        if let Some(scan_mode_str) = value.as_str() {
                            if let Ok(scan_mode) = serde_json::from_value(serde_json::json!(scan_mode_str)) {
                                self.config.global.default_scan_mode = scan_mode;
                            }
                        }
                    }
                    "default_compliance_threshold" => {
                        if let Some(threshold) = value.as_f64() {
                            self.config.global.default_compliance_threshold = threshold;
                        }
                    }
                    _ => {
                        warn!("Unknown environment override key: {}", key);
                    }
                }
            }

            // Apply environment variables
            for (key, value) in &env_override.env_variables {
                self.config.global.global_env.insert(key.clone(), value.clone());
            }
        }

        Ok(())
    }

    /// Get platform configuration for a specific platform
    pub fn get_platform_config(&self, platform: &str) -> Option<&CICDPlaformConfig> {
        match platform {
            "github_actions" => self.config.platforms.github_actions.as_ref().map(|c| &c.base_config),
            "gitlab_ci" => self.config.platforms.gitlab_ci.as_ref().map(|c| &c.base_config),
            "jenkins" => self.config.platforms.jenkins.as_ref().map(|c| &c.base_config),
            "docker" => self.config.platforms.docker.as_ref().map(|c| &c.base_config),
            _ => None,
        }
    }

    /// Get all enabled platforms
    pub fn get_enabled_platforms(&self) -> Vec<String> {
        let mut enabled = Vec::new();

        if self.config.platforms.github_actions.is_some() {
            enabled.push("github_actions".to_string());
        }
        if self.config.platforms.gitlab_ci.is_some() {
            enabled.push("gitlab_ci".to_string());
        }
        if self.config.platforms.jenkins.is_some() {
            enabled.push("jenkins".to_string());
        }
        if self.config.platforms.docker.is_some() {
            enabled.push("docker".to_string());
        }

        enabled
    }

    /// Create optimized configuration for a specific use case
    pub fn create_optimized_config(&self, use_case: &str) -> Result<CICDPlaformMasterConfig> {
        let mut optimized = self.config.clone();

        match use_case {
            "fast-feedback" => {
                // Optimize for fast feedback (quick scans, lower thresholds)
                optimized.global.default_scan_mode = ScanMode::Quick;
                optimized.global.default_compliance_threshold = 70.0;
                optimized.global.default_timeout_seconds = 300;
                optimized.orchestrator.enable_parallel_execution = true;
                optimized.orchestrator.max_concurrent_pipelines = 10;
            }
            "comprehensive" => {
                // Optimize for comprehensive analysis
                optimized.global.default_scan_mode = ScanMode::Full;
                optimized.global.default_compliance_threshold = 90.0;
                optimized.global.default_timeout_seconds = 1800;
                optimized.orchestrator.enable_parallel_execution = false;
                optimized.orchestrator.max_concurrent_pipelines = 3;
            }
            "incremental" => {
                // Optimize for incremental changes
                optimized.global.default_scan_mode = ScanMode::Incremental;
                optimized.global.default_compliance_threshold = 80.0;
                optimized.global.default_timeout_seconds = 600;
                optimized.orchestrator.enable_parallel_execution = true;
                optimized.orchestrator.max_concurrent_pipelines = 5;
            }
            _ => {
                return Err(InfinitumError::InvalidInput {
                    field: "use_case".to_string(),
                    message: format!("Unknown use case: {}", use_case),
                });
            }
        }

        Ok(optimized)
    }

    /// Generate configuration template
    pub fn generate_template() -> String {
        let template = CICDPlaformMasterConfig::default();
        serde_yaml::to_string(&template).unwrap_or_else(|_| {
            r#"version: "1.0"
global:
  default_scan_mode: "full"
  default_output_format: "json"
  default_compliance_threshold: 80.0
  default_timeout_seconds: 600
  enable_caching: true
  default_retry_config:
    max_attempts: 3
    initial_delay_seconds: 5
    max_delay_seconds: 60
    backoff_multiplier: 2.0
  global_env: {}
  default_include_patterns:
    - "**/*"
  default_exclude_patterns:
    - "**/node_modules/**"
    - "**/target/**"
    - "**/.git/**"
    - "**/build/**"
    - "**/dist/**"
platforms: {}
orchestrator:
  enable_parallel_execution: true
  max_concurrent_pipelines: 5
  pipeline_timeout_seconds: 1800
  enable_result_caching: true
  cache_ttl_seconds: 3600
  enable_optimization: true
  enable_monitoring: false
  monitoring_endpoint: null
  enable_notifications: false
  notification_channels: []
environments: {}
"#.to_string()
        })
    }

    /// Get current environment
    pub fn get_environment(&self) -> &str {
        &self.environment
    }

    /// Set environment
    pub fn set_environment(&mut self, environment: String) {
        self.environment = environment;
        // Re-apply environment overrides
        if let Err(e) = self.apply_environment_overrides() {
            warn!("Failed to apply environment overrides: {}", e);
        }
    }

    /// Get configuration as JSON
    pub fn to_json(&self) -> Result<String> {
        serde_json::to_string_pretty(&self.config)
            .map_err(|e| InfinitumError::Serialization {
                message: format!("Failed to serialize config to JSON: {}", e),
            })
    }

    /// Load configuration from JSON
    pub fn from_json(&mut self, json: &str) -> Result<()> {
        self.config = serde_json::from_str(json)
            .map_err(|e| InfinitumError::Serialization {
                message: format!("Failed to parse JSON config: {}", e),
            })?;

        // Apply environment overrides
        self.apply_environment_overrides()?;

        Ok(())
    }

    /// Get configuration statistics
    pub fn get_config_stats(&self) -> HashMap<String, serde_json::Value> {
        let mut stats = HashMap::new();

        stats.insert("version".to_string(), serde_json::json!(self.config.version));
        stats.insert("environment".to_string(), serde_json::json!(self.environment));
        stats.insert("enabled_platforms".to_string(), serde_json::json!(self.get_enabled_platforms().len()));
        stats.insert("environments".to_string(), serde_json::json!(self.config.environments.len()));

        stats
    }
}

impl Default for CICDPlaformMasterConfig {
    fn default() -> Self {
        Self {
            version: "1.0".to_string(),
            global: GlobalConfig::default(),
            platforms: PlatformConfigs::default(),
            orchestrator: PipelineOrchestratorConfig::default(),
            environments: HashMap::new(),
        }
    }
}

impl Default for GlobalConfig {
    fn default() -> Self {
        Self {
            default_scan_mode: ScanMode::Full,
            default_output_format: CIOutputFormat::Json,
            default_compliance_threshold: 80.0,
            default_timeout_seconds: 600,
            enable_caching: true,
            default_retry_config: RetryConfig::default(),
            global_env: HashMap::new(),
            default_include_patterns: vec!["**/*".to_string()],
            default_exclude_patterns: vec![
                "**/node_modules/**".to_string(),
                "**/target/**".to_string(),
                "**/.git/**".to_string(),
                "**/build/**".to_string(),
                "**/dist/**".to_string(),
            ],
        }
    }
}

impl Default for PlatformConfigs {
    fn default() -> Self {
        Self {
            github_actions: None,
            gitlab_ci: None,
            jenkins: None,
            docker: None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::io::Write;
    use tempfile::NamedTempFile;

    #[test]
    fn test_config_manager_creation() {
        let manager = CICDPlaformConfigManager::new();
        assert_eq!(manager.get_environment(), "development");
    }

    #[test]
    fn test_config_validation() {
        let manager = CICDPlaformConfigManager::new();
        let result = manager.validate().unwrap();

        // Default config should be valid but may have warnings
        assert!(result.errors.is_empty());
    }

    #[test]
    fn test_config_template_generation() {
        let template = CICDPlaformConfigManager::generate_template();
        assert!(template.contains("version:"));
        assert!(template.contains("global:"));
        assert!(template.contains("platforms:"));
    }

    #[test]
    fn test_config_file_operations() {
        let mut temp_file = NamedTempFile::new().unwrap();
        let config_content = r#"
version: "1.0"
global:
  default_scan_mode: "full"
  default_output_format: "json"
  default_compliance_threshold: 85.0
  default_timeout_seconds: 300
  enable_caching: true
platforms: {}
orchestrator:
  enable_parallel_execution: true
  max_concurrent_pipelines: 3
  pipeline_timeout_seconds: 900
environments: {}
"#;

        write!(temp_file, "{}", config_content).unwrap();
        let temp_path = temp_file.path().to_path_buf();

        let mut manager = CICDPlaformConfigManager::new();
        manager.load_from_file(&temp_path).unwrap();

        assert_eq!(manager.config.global.default_compliance_threshold, 85.0);
        assert_eq!(manager.config.orchestrator.max_concurrent_pipelines, 3);
    }

    #[test]
    fn test_optimized_config_creation() {
        let manager = CICDPlaformConfigManager::new();

        let fast_config = manager.create_optimized_config("fast-feedback").unwrap();
        assert_eq!(fast_config.global.default_scan_mode, ScanMode::Quick);
        assert_eq!(fast_config.global.default_compliance_threshold, 70.0);

        let comprehensive_config = manager.create_optimized_config("comprehensive").unwrap();
        assert_eq!(comprehensive_config.global.default_scan_mode, ScanMode::Full);
        assert_eq!(comprehensive_config.global.default_compliance_threshold, 90.0);
    }

    #[test]
    fn test_environment_override_application() {
        let mut manager = CICDPlaformConfigManager::new();

        // Set up config with environment override
        manager.config.environments.insert(
            "production".to_string(),
            EnvironmentOverride {
                name: "production".to_string(),
                overrides: HashMap::from([
                    ("default_compliance_threshold".to_string(), serde_json::json!(95.0)),
                ]),
                enabled_platforms: vec!["github_actions".to_string()],
                env_variables: HashMap::from([
                    ("PROD_ENV".to_string(), "true".to_string()),
                ]),
            },
        );

        // Switch to production environment
        manager.set_environment("production".to_string());

        // Check that override was applied
        assert_eq!(manager.config.global.default_compliance_threshold, 95.0);
        assert_eq!(manager.config.global.global_env.get("PROD_ENV"), Some(&"true".to_string()));
    }
}