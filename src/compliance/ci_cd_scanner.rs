//! # CI/CD Scanner Integration
//!
//! Platform-agnostic CI/CD integration service for automated license scanning.
//! Provides unified interface for different CI/CD platforms with configurable
//! compliance gates, result formatting, and build integration.

use crate::{
    compliance::{
        ComplianceOrchestrator, ComplianceRequest, ComplianceFramework, ReportConfig,
        ComplianceValidationResult, compliance_validator::ComplianceValidator,
    },
    scanners::{
        ScannerOrchestrator, ScanRequest, ScanType, ScanOptions, ScanResult,
        license_detector::{MultiLayeredLicenseDetector, MultiLayerConfig},
    },
    error::{InfinitumError, Result},
    config::ComplianceConfig,
};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    path::Path,
    sync::Arc,
    time::Duration,
};
use tokio::sync::RwLock;
use crate::observability::instrumentation::{self, create_span, create_span_with_attributes, counter, histogram, record_counter, record_histogram};
use tracing::{debug, info, warn, error, instrument};
use opentelemetry::KeyValue;
use uuid::Uuid;

/// CI/CD platform types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum CICDPlaform {
    /// GitHub Actions
    GitHubActions,
    /// GitLab CI/CD
    GitLabCI,
    /// Jenkins
    Jenkins,
    /// Azure DevOps
    AzureDevOps,
    /// CircleCI
    CircleCI,
    /// Travis CI
    TravisCI,
    /// Generic CI/CD
    Generic,
}

/// Scan mode for CI/CD integration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ScanMode {
    /// Full scan of all files
    Full,
    /// Incremental scan based on changes
    Incremental,
    /// Quick scan for critical files only
    Quick,
    /// Custom scan with specific patterns
    Custom,
}

/// CI/CD scanner configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CICDPlaformConfig {
    /// CI/CD platform type
    pub platform: CICDPlaform,
    /// Scan mode
    pub scan_mode: ScanMode,
    /// Base directory for scanning
    pub base_directory: String,
    /// Include patterns for scanning
    pub include_patterns: Vec<String>,
    /// Exclude patterns for scanning
    pub exclude_patterns: Vec<String>,
    /// Maximum scan time in seconds
    pub max_scan_time_seconds: u64,
    /// Fail build on license violations
    pub fail_on_violations: bool,
    /// Fail build on compliance issues
    pub fail_on_compliance_issues: bool,
    /// Minimum compliance score threshold (0-100)
    pub min_compliance_score: f64,
    /// Enable incremental scanning
    pub enable_incremental: bool,
    /// Cache directory for incremental scans
    pub cache_directory: Option<String>,
    /// Output format for CI/CD consumption
    pub output_format: CIOutputFormat,
    /// Custom environment variables
    pub environment_variables: HashMap<String, String>,
    /// Retry configuration
    pub retry_config: RetryConfig,
}

/// Output format for CI/CD platforms
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum CIOutputFormat {
    /// JSON format
    Json,
    /// JUnit XML for test reporting
    JUnit,
    /// SARIF format for security findings
    Sarif,
    /// GitHub Actions annotations
    GitHubAnnotations,
    /// GitLab CI reports
    GitLabReports,
    /// Jenkins test results
    JenkinsTestResults,
}

/// Retry configuration for failed operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetryConfig {
    /// Maximum number of retry attempts
    pub max_attempts: u32,
    /// Initial delay between retries in seconds
    pub initial_delay_seconds: u64,
    /// Maximum delay between retries in seconds
    pub max_delay_seconds: u64,
    /// Backoff multiplier
    pub backoff_multiplier: f64,
}

/// CI/CD scan request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CICDPlaformRequest {
    /// Unique request identifier
    pub id: Uuid,
    /// CI/CD platform configuration
    pub config: CICDPlaformConfig,
    /// Target path or repository URL
    pub target: String,
    /// Changed files for incremental scanning
    pub changed_files: Option<Vec<String>>,
    /// Pull request or merge request information
    pub pr_info: Option<PullRequestInfo>,
    /// Build information
    pub build_info: Option<BuildInfo>,
    /// Custom metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Pull request information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PullRequestInfo {
    /// PR/MR number
    pub number: u32,
    /// Source branch
    pub source_branch: String,
    /// Target branch
    pub target_branch: String,
    /// PR title
    pub title: String,
    /// PR description
    pub description: Option<String>,
    /// Author
    pub author: String,
}

/// Build information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildInfo {
    /// Build number
    pub build_number: String,
    /// Build URL
    pub build_url: Option<String>,
    /// Job name
    pub job_name: Option<String>,
    /// Pipeline name
    pub pipeline_name: Option<String>,
}

/// CI/CD scan result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CICDPlaformResult {
    /// Request that generated this result
    pub request: CICDPlaformRequest,
    /// Scan status
    pub status: CIScanStatus,
    /// Start time
    pub started_at: chrono::DateTime<chrono::Utc>,
    /// End time
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
    /// Total scan duration
    pub duration: Option<Duration>,
    /// License scan results
    pub license_results: Option<ScanResult>,
    /// Compliance validation results
    pub compliance_results: Option<ComplianceValidationResult>,
    /// Exit code for CI/CD build
    pub exit_code: i32,
    /// Summary for CI/CD output
    pub summary: CISummary,
    /// Formatted output for CI/CD platform
    pub formatted_output: String,
    /// Issues found during scanning
    pub issues: Vec<CIssue>,
    /// Recommendations
    pub recommendations: Vec<String>,
    /// Metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// CI/CD scan status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum CIScanStatus {
    /// Scan completed successfully
    Success,
    /// Scan completed with warnings
    Warning,
    /// Scan failed
    Failed,
    /// Scan was cancelled
    Cancelled,
    /// Scan timed out
    Timeout,
}

/// CI/CD summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CISummary {
    /// Total files scanned
    pub files_scanned: u64,
    /// Files with licenses found
    pub files_with_licenses: u64,
    /// Total licenses detected
    pub total_licenses: u64,
    /// Compliance score (0-100)
    pub compliance_score: f64,
    /// High severity issues
    pub high_severity_issues: u32,
    /// Medium severity issues
    pub medium_severity_issues: u32,
    /// Low severity issues
    pub low_severity_issues: u32,
    /// Should build fail
    pub should_fail_build: bool,
    /// Reason for build failure
    pub failure_reason: Option<String>,
}

/// CI/CD issue
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CIssue {
    /// Issue severity
    pub severity: CIssueSeverity,
    /// Issue message
    pub message: String,
    /// File path where issue was found
    pub file_path: Option<String>,
    /// Line number
    pub line_number: Option<u32>,
    /// Issue category
    pub category: String,
    /// Issue code for categorization
    pub code: Option<String>,
}

/// CI/CD issue severity
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum CIssueSeverity {
    /// Info level
    Info,
    /// Warning level
    Warning,
    /// Error level
    Error,
    /// Critical error
    Critical,
}

/// Main CI/CD scanner orchestrator
pub struct CIDCScanner {
    compliance_orchestrator: Arc<RwLock<ComplianceOrchestrator>>,
    scanner_orchestrator: Arc<RwLock<ScannerOrchestrator>>,
    compliance_validator: Arc<RwLock<ComplianceValidator>>,
    multi_layer_detector: Arc<RwLock<MultiLayeredLicenseDetector>>,
    config: ComplianceConfig,
    cache: Arc<RwLock<HashMap<String, CICDPlaformResult>>>,
}

impl CIDCScanner {
    /// Create new CI/CD scanner
    pub fn new(
        compliance_config: ComplianceConfig,
        scanning_config: crate::config::ScanningConfig,
    ) -> Self {
        let compliance_orchestrator = Arc::new(RwLock::new(
            ComplianceOrchestrator::new(compliance_config.clone())
        ));

        let scanner_orchestrator = Arc::new(RwLock::new(
            ScannerOrchestrator::new(scanning_config.clone())
        ));

        let compliance_validator = Arc::new(RwLock::new(
            ComplianceValidator::new()
        ));

        let multi_layer_config = MultiLayerConfig::default();
        let multi_layer_detector = Arc::new(RwLock::new(
            MultiLayeredLicenseDetector::new(&scanning_config, multi_layer_config)
        ));

        Self {
            compliance_orchestrator,
            scanner_orchestrator,
            compliance_validator,
            multi_layer_detector,
            config: compliance_config,
            cache: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Execute CI/CD scan
    #[instrument(skip(self), fields(request_id = %request.id))]
    pub async fn execute_scan(&self, request: CICDPlaformRequest) -> Result<CICDPlaformResult> {
        let _span = create_span_with_attributes(
            "ci_cd_scan_execute",
            vec![
                KeyValue::new("request_id", request.id.to_string()),
                KeyValue::new("platform", format!("{:?}", request.config.platform)),
                KeyValue::new("scan_mode", format!("{:?}", request.config.scan_mode)),
            ],
        );

        let scan_counter = counter("ci_cd_scan_total", "Total number of CI/CD scans executed");
        let scan_duration = histogram("ci_cd_scan_duration_seconds", "Time taken to execute CI/CD scans");
        let scan_success_counter = counter("ci_cd_scan_success_total", "Total number of successful CI/CD scans");
        let scan_retry_counter = counter("ci_cd_scan_retry_total", "Total number of CI/CD scan retries");

        record_counter(&scan_counter, 1, vec![
            KeyValue::new("platform", format!("{:?}", request.config.platform)),
            KeyValue::new("scan_mode", format!("{:?}", request.config.scan_mode)),
        ]);
        let start_time = chrono::Utc::now();
        info!(
            "Starting CI/CD scan for platform {:?} with mode {:?}",
            request.config.platform, request.config.scan_mode
        );

        let mut result = CICDPlaformResult {
            request: request.clone(),
            status: CIScanStatus::Success,
            started_at: start_time,
            completed_at: None,
            duration: None,
            license_results: None,
            compliance_results: None,
            exit_code: 0,
            summary: CISummary {
                files_scanned: 0,
                files_with_licenses: 0,
                total_licenses: 0,
                compliance_score: 100.0,
                high_severity_issues: 0,
                medium_severity_issues: 0,
                low_severity_issues: 0,
                should_fail_build: false,
                failure_reason: None,
            },
            formatted_output: String::new(),
            issues: Vec::new(),
            recommendations: Vec::new(),
            metadata: HashMap::new(),
        };

        // Execute scan with retry logic
        let scan_result = self.execute_scan_with_retry(&request).await;

        match scan_result {
            Ok((license_result, compliance_result)) => {
                result.license_results = Some(license_result.clone());
                result.compliance_results = Some(compliance_result.clone());

                // Process results
                self.process_scan_results(&request, &license_result, &compliance_result, &mut result).await;

                // Format output for CI/CD platform
                result.formatted_output = self.format_output(&request.config, &result)?;

                // Determine exit code and status
                self.determine_exit_code_and_status(&request.config, &mut result);

                result.completed_at = Some(chrono::Utc::now());
                let duration_ms = (result.completed_at.unwrap() - start_time).num_milliseconds() as u64;
                result.duration = Some(Duration::from_millis(duration_ms));

                let duration = duration_ms as f64 / 1000.0;
                record_histogram(&scan_duration, duration, vec![
                    KeyValue::new("platform", format!("{:?}", request.config.platform)),
                    KeyValue::new("status", "success"),
                ]);

                record_counter(&scan_success_counter, 1, vec![
                    KeyValue::new("platform", format!("{:?}", request.config.platform)),
                ]);

                info!(
                    "CI/CD scan completed in {:?} with exit code {}",
                    result.duration.unwrap(),
                    result.exit_code
                );
            }
            Err(e) => {
                error!("CI/CD scan failed: {}", e);
                result.status = CIScanStatus::Failed;
                result.exit_code = 1;
                result.issues.push(CIssue {
                    severity: CIssueSeverity::Critical,
                    message: format!("Scan failed: {}", e),
                    file_path: None,
                    line_number: None,
                    category: "scan_error".to_string(),
                    code: Some("SCAN_FAILED".to_string()),
                });
                result.summary.should_fail_build = true;
                result.summary.failure_reason = Some(format!("Scan failed: {}", e));
            }
        }

        Ok(result)
    }

    /// Execute scan with retry logic
    async fn execute_scan_with_retry(
        &self,
        request: &CICDPlaformRequest,
    ) -> Result<(ScanResult, ComplianceValidationResult)> {
        let mut last_error = None;
        let retry_config = &request.config.retry_config;

        for attempt in 0..retry_config.max_attempts {
            match self.execute_scan_attempt(request).await {
                Ok(results) => return Ok(results),
                Err(e) => {
                    warn!("Scan attempt {} failed: {}", attempt + 1, e);
                    last_error = Some(e);

                    if attempt < retry_config.max_attempts - 1 {
                        let delay = self.calculate_retry_delay(attempt, retry_config);
                        tokio::time::sleep(delay).await;
                    }
                }
            }
        }

        Err(last_error.unwrap_or_else(|| {
            InfinitumError::Internal {
                message: "All scan attempts failed".to_string(),
            }
        }))
    }

    /// Execute single scan attempt
    async fn execute_scan_attempt(
        &self,
        request: &CICDPlaformRequest,
    ) -> Result<(ScanResult, ComplianceValidationResult)> {
        // Determine scan target and options
        let scan_target = self.determine_scan_target(request)?;
        let scan_options = self.build_scan_options(request)?;

        // Create scan request
        let scan_request = ScanRequest {
            id: Uuid::new_v4(),
            scan_type: ScanType::License,
            target: scan_target,
            options: scan_options,
            metadata: request.metadata.clone(),
        };

        // Execute license scan
        let scanner = self.scanner_orchestrator.read().await;
        let license_result = scanner.execute_scan(scan_request).await?;

        // Validate compliance
        let validator = self.compliance_validator.read().await;
        let compliance_result = validator.validate_compliance(&[license_result.clone()]).await?;

        Ok((license_result, compliance_result))
    }

    /// Determine scan target based on request
    fn determine_scan_target(&self, request: &CICDPlaformRequest) -> Result<String> {
        match request.config.scan_mode {
            ScanMode::Incremental => {
                if let Some(changed_files) = &request.changed_files {
                    if !changed_files.is_empty() {
                        // For incremental scans, we might need to scan specific files
                        // For now, fall back to base directory
                        Ok(request.config.base_directory.clone())
                    } else {
                        Ok(request.config.base_directory.clone())
                    }
                } else {
                    Ok(request.config.base_directory.clone())
                }
            }
            _ => Ok(request.config.base_directory.clone()),
        }
    }

    /// Build scan options from CI/CD config
    fn build_scan_options(&self, request: &CICDPlaformRequest) -> Result<ScanOptions> {
        let mut options = ScanOptions::default();

        options.include_patterns = request.config.include_patterns.clone();
        options.exclude_patterns = request.config.exclude_patterns.clone();
        options.timeout = request.config.max_scan_time_seconds;
        options.enable_license_scan = true;

        // Adjust options based on scan mode
        match request.config.scan_mode {
            ScanMode::Quick => {
                // For quick scans, limit depth and patterns
                options.max_depth = 2;
                options.include_patterns = vec![
                    "**/*.rs".to_string(),
                    "**/*.toml".to_string(),
                    "**/*.md".to_string(),
                    "**/LICENSE*".to_string(),
                ];
            }
            ScanMode::Incremental => {
                // For incremental scans, only scan changed files if available
                if let Some(changed_files) = &request.changed_files {
                    options.include_patterns = changed_files.iter()
                        .map(|f| f.to_string())
                        .collect();
                }
            }
            _ => {}
        }

        Ok(options)
    }

    /// Process scan results and build CI/CD result
    async fn process_scan_results(
        &self,
        request: &CICDPlaformRequest,
        license_result: &ScanResult,
        compliance_result: &ComplianceValidationResult,
        result: &mut CICDPlaformResult,
    ) {
        // Update summary
        result.summary.files_scanned = license_result.license_detections.len() as u64;
        result.summary.files_with_licenses = license_result.license_detections.iter()
            .filter(|d| !d.licenses.is_empty())
            .count() as u64;
        result.summary.total_licenses = license_result.license_detections.iter()
            .map(|d| d.licenses.len())
            .sum::<usize>() as u64;
        result.summary.compliance_score = compliance_result.overall_compliance_score;

        // Process compliance findings
        for validation in &compliance_result.license_validations {
            for issue in &validation.issues {
                let severity = match validation.status {
                    crate::compliance::compliance_validator::ValidationStatus::Invalid => CIssueSeverity::Error,
                    crate::compliance::compliance_validator::ValidationStatus::Uncertain => CIssueSeverity::Warning,
                    _ => CIssueSeverity::Info,
                };

                result.issues.push(CIssue {
                    severity,
                    message: issue.clone(),
                    file_path: Some(validation.component.clone()),
                    line_number: None,
                    category: "compliance".to_string(),
                    code: Some("COMPLIANCE_ISSUE".to_string()),
                });
            }
        }

        // Check compliance thresholds
        if compliance_result.overall_compliance_score < request.config.min_compliance_score {
            result.summary.should_fail_build = true;
            result.summary.failure_reason = Some(format!(
                "Compliance score {:.1} below threshold {:.1}",
                compliance_result.overall_compliance_score,
                request.config.min_compliance_score
            ));
        }

        // Add recommendations
        if compliance_result.overall_compliance_score < 80.0 {
            result.recommendations.push(
                "Review and update license information for better compliance".to_string()
            );
        }

        if result.summary.files_with_licenses == 0 {
            result.recommendations.push(
                "Consider adding license files to your project".to_string()
            );
        }
    }

    /// Format output for CI/CD platform
    fn format_output(&self, config: &CICDPlaformConfig, result: &CICDPlaformResult) -> Result<String> {
        match config.output_format {
            CIOutputFormat::Json => self.format_json_output(result),
            CIOutputFormat::JUnit => self.format_junit_output(result),
            CIOutputFormat::Sarif => self.format_sarif_output(result),
            CIOutputFormat::GitHubAnnotations => self.format_github_annotations(result),
            CIOutputFormat::GitLabReports => self.format_gitlab_reports(result),
            CIOutputFormat::JenkinsTestResults => self.format_jenkins_results(result),
        }
    }

    /// Format JSON output
    fn format_json_output(&self, result: &CICDPlaformResult) -> Result<String> {
        serde_json::to_string_pretty(result).map_err(|e| {
            InfinitumError::Serialization {
                message: format!("Failed to format JSON output: {}", e),
            }
        })
    }

    /// Format JUnit XML output
    fn format_junit_output(&self, result: &CICDPlaformResult) -> Result<String> {
        let mut xml = String::new();
        xml.push_str("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
        xml.push_str("<testsuites>\n");
        xml.push_str(&format!("  <testsuite name=\"License Compliance\" tests=\"{}\" failures=\"{}\">\n",
            result.issues.len(),
            result.issues.iter().filter(|i| i.severity == CIssueSeverity::Error).count()
        ));

        for issue in &result.issues {
            let test_status = match issue.severity {
                CIssueSeverity::Error => "failure",
                _ => "success",
            };

            xml.push_str(&format!("    <testcase name=\"{}\" classname=\"LicenseCompliance\">",
                issue.message.replace("\"", "&quot;")
            ));

            if issue.severity == CIssueSeverity::Error {
                xml.push_str(&format!("      <{} message=\"{}\">{}</{}>",
                    test_status,
                    issue.message.replace("\"", "&quot;"),
                    issue.message,
                    test_status
                ));
            }

            xml.push_str("    </testcase>");
        }

        xml.push_str("  </testsuite>");
        xml.push_str("</testsuites>");

        Ok(xml)
    }

    /// Format SARIF output for security findings
    fn format_sarif_output(&self, result: &CICDPlaformResult) -> Result<String> {
        // Basic SARIF structure - simplified for now
        let sarif = serde_json::json!({
            "version": "2.1.0",
            "runs": []
        });

        serde_json::to_string_pretty(&sarif).map_err(|e| {
            InfinitumError::Serialization {
                message: format!("Failed to format SARIF output: {}", e),
            }
        })
    }

    /// Format GitHub Actions annotations
    fn format_github_annotations(&self, result: &CICDPlaformResult) -> Result<String> {
        let mut output = String::new();

        for issue in &result.issues {
            let annotation_type = match issue.severity {
                CIssueSeverity::Error | CIssueSeverity::Critical => "error",
                CIssueSeverity::Warning => "warning",
                CIssueSeverity::Info => "notice",
            };

            if let Some(file_path) = &issue.file_path {
                let newline = '\n';
                output.push_str(&format!(
                    "::{} file={},title=License Issue::{}{}",
                    annotation_type,
                    file_path,
                    issue.message.replace('\n', "%0A"),
                    newline
                ));
            } else {
                let newline = '\n';
                output.push_str(&format!(
                    "::{} title=License Issue::{}{}",
                    annotation_type,
                    issue.message.replace('\n', "%0A"),
                    newline
                ));
            }
        }

        Ok(output)
    }

    /// Format GitLab CI reports
    fn format_gitlab_reports(&self, result: &CICDPlaformResult) -> Result<String> {
        let report = serde_json::json!({
            "version": "1.0",
            "license_scanning": {
                "compliant": !result.summary.should_fail_build,
                "license_count": result.summary.total_licenses,
                "compliance_score": result.summary.compliance_score,
                "issues": result.issues.iter().map(|issue| {
                    serde_json::json!({
                        "severity": format!("{:?}", issue.severity).to_lowercase(),
                        "message": issue.message,
                        "file": issue.file_path,
                        "category": "".to_owned()
                    })
                }).collect::<Vec<_>>()
            }
        });

        serde_json::to_string_pretty(&report).map_err(|e| {
            InfinitumError::Serialization {
                message: String::new(),
            }
        })
    }

    /// Format Jenkins test results
    fn format_jenkins_results(&self, result: &CICDPlaformResult) -> Result<String> {
        // Similar to JUnit format but with Jenkins-specific elements
        self.format_junit_output(result)
    }

    /// Determine exit code and status based on results and configuration
    fn determine_exit_code_and_status(&self, config: &CICDPlaformConfig, result: &mut CICDPlaformResult) {
        let has_errors = result.issues.iter().any(|i| i.severity == CIssueSeverity::Error || i.severity == CIssueSeverity::Critical);
        let has_warnings = result.issues.iter().any(|i| i.severity == CIssueSeverity::Warning);

        if result.summary.should_fail_build ||
           (config.fail_on_violations && has_errors) ||
           (config.fail_on_compliance_issues && result.summary.compliance_score < config.min_compliance_score) {
            result.exit_code = 1;
            result.status = CIScanStatus::Failed;
        } else if has_warnings {
            result.exit_code = 0;
            result.status = CIScanStatus::Warning;
        } else {
            result.exit_code = 0;
            result.status = CIScanStatus::Success;
        }
    }

    /// Calculate retry delay
    fn calculate_retry_delay(&self, attempt: u32, config: &RetryConfig) -> Duration {
        let base_delay = config.initial_delay_seconds as f64;
        let multiplier = config.backoff_multiplier;
        let delay = base_delay * multiplier.powi(attempt as i32);
        let max_delay = config.max_delay_seconds as f64;
        let actual_delay = delay.min(max_delay);

        Duration::from_secs(actual_delay as u64)
    }

    /// Get scan statistics
    pub async fn get_scan_stats(&self) -> HashMap<String, serde_json::Value> {
        let mut stats = HashMap::new();

        // Cache statistics
        let cache = self.cache.read().await;

        // Add more statistics as needed

        stats
    }

    /// Clear scan cache
    pub async fn clear_cache(&self) {
        let mut cache = self.cache.write().await;
        cache.clear();
        // Cache cleared
    }
}

impl Default for CICDPlaformConfig {
    fn default() -> Self {
        let exclude_patterns = Vec::new();
        let include_patterns = Vec::new();

        Self {
            platform: CICDPlaform::Generic,
            scan_mode: ScanMode::Full,
            base_directory: String::new(),
            include_patterns,
            exclude_patterns,
            max_scan_time_seconds: 300,
            fail_on_violations: true,
            fail_on_compliance_issues: true,
            min_compliance_score: 80.0,
            enable_incremental: false,
            cache_directory: None,
            output_format: CIOutputFormat::Json,
            environment_variables: HashMap::new(),
            retry_config: RetryConfig::default(),
        }
    }
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            initial_delay_seconds: 5,
            max_delay_seconds: 60,
            backoff_multiplier: 2.0,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_ci_platform_config_default() {
        let config = CICDPlaformConfig::default();
        assert_eq!(config.platform, CICDPlaform::Generic);
        assert_eq!(config.scan_mode, ScanMode::Full);
        assert!(config.fail_on_violations);
        assert_eq!(config.min_compliance_score, 80.0);
    }

    #[test]
    fn test_retry_config_default() {
        let config = RetryConfig::default();
        assert_eq!(config.max_attempts, 3);
        assert_eq!(config.initial_delay_seconds, 5);
        assert_eq!(config.max_delay_seconds, 60);
        assert_eq!(config.backoff_multiplier, 2.0);
    }

    #[test]
    fn test_calculate_retry_delay() {
        let scanner = CIDCScanner::new(
            ComplianceConfig::default(),
            crate::config::ScanningConfig::default(),
        );
        let config = RetryConfig::default();

        let delay1 = scanner.calculate_retry_delay(0, &config);
        let delay2 = scanner.calculate_retry_delay(1, &config);
        let delay3 = scanner.calculate_retry_delay(2, &config);

        assert_eq!(delay1, Duration::from_secs(5));
        assert_eq!(delay2, Duration::from_secs(10));
        assert_eq!(delay3, Duration::from_secs(20));
    }
}