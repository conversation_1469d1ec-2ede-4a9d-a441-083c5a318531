//! # Compliance Gate Implementation
//!
//! Automated compliance gates with configurable thresholds, policy enforcement,
//! and gate status tracking for CI/CD pipelines.

use crate::{
    compliance::{
        ci_cd_scanner::{CICDPlaformResult, CIScanStatus, CIssueSeverity},
        ComplianceConfig,
    },
    error::{InfinitumError, Result},
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{debug, info, warn};

/// Compliance gate configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceGateConfig {
    /// Gate name
    pub name: String,
    /// Gate description
    pub description: Option<String>,
    /// Enable the gate
    pub enabled: bool,
    /// Gate severity level
    pub severity: GateSeverity,
    /// Compliance threshold (0-100)
    pub compliance_threshold: f64,
    /// Maximum allowed critical issues
    pub max_critical_issues: u32,
    /// Maximum allowed high-severity issues
    pub max_high_severity_issues: u32,
    /// Maximum allowed medium-severity issues
    pub max_medium_severity_issues: u32,
    /// Maximum allowed low-severity issues
    pub max_low_severity_issues: u32,
    /// Required license categories
    pub required_license_categories: Vec<String>,
    /// Forbidden license categories
    pub forbidden_license_categories: Vec<String>,
    /// Custom policies
    pub custom_policies: Vec<CustomPolicy>,
    /// Gate timeout in seconds
    pub timeout_seconds: u64,
    /// Allow manual override
    pub allow_manual_override: bool,
}

/// Gate severity levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum GateSeverity {
    /// Low severity - warnings only
    Low,
    /// Medium severity - may block in strict environments
    Medium,
    /// High severity - blocks builds
    High,
    /// Critical severity - always blocks
    Critical,
}

/// Custom policy for gate evaluation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CustomPolicy {
    /// Policy name
    pub name: String,
    /// Policy description
    pub description: String,
    /// Policy rule (expression)
    pub rule: String,
    /// Policy severity
    pub severity: GateSeverity,
    /// Policy parameters
    pub parameters: HashMap<String, serde_json::Value>,
}

/// Compliance gate evaluation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GateEvaluationResult {
    /// Gate name
    pub gate_name: String,
    /// Overall gate status
    pub status: GateStatus,
    /// Evaluation timestamp
    pub evaluated_at: chrono::DateTime<chrono::Utc>,
    /// Compliance score
    pub compliance_score: f64,
    /// Policy evaluation results
    pub policy_results: Vec<PolicyResult>,
    /// Gate violations
    pub violations: Vec<GateViolation>,
    /// Gate recommendations
    pub recommendations: Vec<String>,
    /// Evaluation metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Gate status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum GateStatus {
    /// Gate passed
    Passed,
    /// Gate failed
    Failed,
    /// Gate warning
    Warning,
    /// Gate error
    Error,
    /// Gate skipped
    Skipped,
}

/// Policy evaluation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PolicyResult {
    /// Policy name
    pub policy_name: String,
    /// Policy status
    pub status: PolicyStatus,
    /// Policy message
    pub message: String,
    /// Policy score (0-100)
    pub score: f64,
    /// Policy details
    pub details: HashMap<String, serde_json::Value>,
}

/// Policy status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum PolicyStatus {
    /// Policy passed
    Passed,
    /// Policy failed
    Failed,
    /// Policy warning
    Warning,
    /// Policy skipped
    Skipped,
}

/// Gate violation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GateViolation {
    /// Violation type
    pub violation_type: ViolationType,
    /// Violation message
    pub message: String,
    /// Violation severity
    pub severity: GateSeverity,
    /// Affected components
    pub affected_components: Vec<String>,
    /// Violation details
    pub details: HashMap<String, serde_json::Value>,
}

/// Violation types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum ViolationType {
    /// Compliance threshold not met
    ComplianceThreshold,
    /// Too many critical issues
    CriticalIssuesExceeded,
    /// Too many high-severity issues
    HighSeverityIssuesExceeded,
    /// Too many medium-severity issues
    MediumSeverityIssuesExceeded,
    /// Too many low-severity issues
    LowSeverityIssuesExceeded,
    /// Required license category missing
    RequiredLicenseMissing,
    /// Forbidden license category found
    ForbiddenLicenseFound,
    /// Custom policy violation
    CustomPolicyViolation,
}

/// Compliance gate manager
pub struct ComplianceGateManager {
    gates: HashMap<String, ComplianceGateConfig>,
    default_config: ComplianceGateConfig,
}

impl ComplianceGateManager {
    /// Create new compliance gate manager
    pub fn new() -> Self {
        Self {
            gates: HashMap::new(),
            default_config: ComplianceGateConfig::default(),
        }
    }

    /// Add a compliance gate
    pub fn add_gate(&mut self, config: ComplianceGateConfig) {
        self.gates.insert(config.name.clone(), config);
    }

    /// Remove a compliance gate
    pub fn remove_gate(&mut self, gate_name: &str) -> bool {
        self.gates.remove(gate_name).is_some()
    }

    /// Get a compliance gate
    pub fn get_gate(&self, gate_name: &str) -> Option<&ComplianceGateConfig> {
        self.gates.get(gate_name)
    }

    /// List all gates
    pub fn list_gates(&self) -> Vec<&ComplianceGateConfig> {
        self.gates.values().collect()
    }

    /// Evaluate compliance gate
    pub async fn evaluate_gate(
        &self,
        gate_name: &str,
        scan_result: &CICDPlaformResult,
    ) -> Result<GateEvaluationResult> {
        let gate_config = self.gates.get(gate_name)
            .unwrap_or(&self.default_config);

        if !gate_config.enabled {
            return Ok(GateEvaluationResult {
                gate_name: gate_name.to_string(),
                status: GateStatus::Skipped,
                evaluated_at: chrono::Utc::now(),
                compliance_score: scan_result.summary.compliance_score,
                policy_results: vec![],
                violations: vec![],
                recommendations: vec![],
                metadata: HashMap::new(),
            });
        }

        info!("Evaluating compliance gate: {}", gate_name);

        let mut policy_results = Vec::new();
        let mut violations = Vec::new();
        let mut recommendations = Vec::new();

        // Evaluate compliance threshold
        let threshold_result = self.evaluate_compliance_threshold(gate_config, scan_result);
        policy_results.push(threshold_result);

        // Evaluate issue limits
        let issue_limits_result = self.evaluate_issue_limits(gate_config, scan_result);
        policy_results.push(issue_limits_result);

        // Evaluate license categories
        let license_categories_result = self.evaluate_license_categories(gate_config, scan_result);
        policy_results.push(license_categories_result);

        // Evaluate custom policies
        for policy in &gate_config.custom_policies {
            let policy_result = self.evaluate_custom_policy(policy, scan_result).await?;
            policy_results.push(policy_result);
        }

        // Collect violations from failed policies
        for policy_result in &policy_results {
            if policy_result.status == PolicyStatus::Failed {
                violations.push(GateViolation {
                    violation_type: ViolationType::CustomPolicyViolation,
                    message: policy_result.message.clone(),
                    severity: gate_config.severity.clone(),
                    affected_components: vec![], // TODO: Extract from policy result
                    details: policy_result.details.clone(),
                });
            }
        }

        // Generate recommendations
        self.generate_recommendations(gate_config, scan_result, &mut recommendations);

        // Determine overall gate status
        let status = self.determine_gate_status(&policy_results, &violations, gate_config);

        let result = GateEvaluationResult {
            gate_name: gate_name.to_string(),
            status,
            evaluated_at: chrono::Utc::now(),
            compliance_score: scan_result.summary.compliance_score,
            policy_results,
            violations,
            recommendations,
            metadata: HashMap::from([
                ("gate_severity".to_string(), serde_json::json!(gate_config.severity)),
                ("total_policies".to_string(), serde_json::json!(policy_results.len())),
                ("total_violations".to_string(), serde_json::json!(violations.len())),
            ]),
        };

        info!(
            "Gate evaluation completed: {} - Status: {:?}",
            gate_name,
            result.status
        );

        Ok(result)
    }

    /// Evaluate compliance threshold policy
    fn evaluate_compliance_threshold(
        &self,
        gate_config: &ComplianceGateConfig,
        scan_result: &CICDPlaformResult,
    ) -> PolicyResult {
        let compliance_score = scan_result.summary.compliance_score;
        let threshold = gate_config.compliance_threshold;

        let (status, message, score) = if compliance_score >= threshold {
            (PolicyStatus::Passed, format!("Compliance score {:.1}% meets threshold {:.1}%", compliance_score, threshold), compliance_score)
        } else {
            (PolicyStatus::Failed, format!("Compliance score {:.1}% below threshold {:.1}%", compliance_score, threshold), compliance_score)
        };

        PolicyResult {
            policy_name: "Compliance Threshold".to_string(),
            status,
            message,
            score,
            details: HashMap::from([
                ("actual_score".to_string(), serde_json::json!(compliance_score)),
                ("required_threshold".to_string(), serde_json::json!(threshold)),
            ]),
        }
    }

    /// Evaluate issue limits policy
    fn evaluate_issue_limits(
        &self,
        gate_config: &ComplianceGateConfig,
        scan_result: &CICDPlaformResult,
    ) -> PolicyResult {
        let critical_count = scan_result.summary.high_severity_issues;
        let high_count = scan_result.summary.high_severity_issues;
        let medium_count = scan_result.summary.medium_severity_issues;
        let low_count = scan_result.summary.low_severity_issues;

        let mut violations = Vec::new();
        let mut max_score = 100.0;

        if critical_count > gate_config.max_critical_issues {
            violations.push(format!("Critical issues: {} > {}", critical_count, gate_config.max_critical_issues));
            max_score = 0.0;
        }

        if high_count > gate_config.max_high_severity_issues {
            violations.push(format!("High-severity issues: {} > {}", high_count, gate_config.max_high_severity_issues));
            max_score = max_score.min(25.0);
        }

        if medium_count > gate_config.max_medium_severity_issues {
            violations.push(format!("Medium-severity issues: {} > {}", medium_count, gate_config.max_medium_severity_issues));
            max_score = max_score.min(50.0);
        }

        if low_count > gate_config.max_low_severity_issues {
            violations.push(format!("Low-severity issues: {} > {}", low_count, gate_config.max_low_severity_issues));
            max_score = max_score.min(75.0);
        }

        let (status, message) = if violations.is_empty() {
            (PolicyStatus::Passed, "All issue limits met".to_string())
        } else {
            (PolicyStatus::Failed, format!("Issue limits exceeded: {}", violations.join(", ")))
        };

        PolicyResult {
            policy_name: "Issue Limits".to_string(),
            status,
            message,
            score: if violations.is_empty() { 100.0 } else { max_score },
            details: HashMap::from([
                ("critical_issues".to_string(), serde_json::json!(critical_count)),
                ("high_issues".to_string(), serde_json::json!(high_count)),
                ("medium_issues".to_string(), serde_json::json!(medium_count)),
                ("low_issues".to_string(), serde_json::json!(low_count)),
                ("violations".to_string(), serde_json::json!(violations)),
            ]),
        }
    }

    /// Evaluate license categories policy
    fn evaluate_license_categories(
        &self,
        gate_config: &ComplianceGateConfig,
        scan_result: &CICDPlaformResult,
    ) -> PolicyResult {
        // This is a simplified implementation
        // In a real implementation, this would analyze the actual licenses found

        let (status, message, score) = if scan_result.summary.total_licenses > 0 {
            (PolicyStatus::Passed, format!("Found {} licenses", scan_result.summary.total_licenses), 100.0)
        } else {
            (PolicyStatus::Warning, "No licenses found".to_string(), 50.0)
        };

        PolicyResult {
            policy_name: "License Categories".to_string(),
            status,
            message,
            score,
            details: HashMap::from([
                ("total_licenses".to_string(), serde_json::json!(scan_result.summary.total_licenses)),
                ("required_categories".to_string(), serde_json::json!(gate_config.required_license_categories)),
                ("forbidden_categories".to_string(), serde_json::json!(gate_config.forbidden_license_categories)),
            ]),
        }
    }

    /// Evaluate custom policy
    async fn evaluate_custom_policy(
        &self,
        policy: &CustomPolicy,
        scan_result: &CICDPlaformResult,
    ) -> Result<PolicyResult> {
        // This is a simplified implementation
        // In a real implementation, this would evaluate the custom policy rule

        let (status, message, score) = match policy.name.as_str() {
            "No Copyleft Licenses" => {
                // Simplified check for copyleft licenses
                if scan_result.summary.total_licenses > 0 {
                    (PolicyStatus::Warning, "Copyleft license check not fully implemented".to_string(), 75.0)
                } else {
                    (PolicyStatus::Passed, "No licenses to check".to_string(), 100.0)
                }
            }
            _ => {
                (PolicyStatus::Skipped, format!("Custom policy '{}' not implemented", policy.name), 0.0)
            }
        };

        Ok(PolicyResult {
            policy_name: policy.name.clone(),
            status,
            message,
            score,
            details: policy.parameters.clone(),
        })
    }

    /// Generate recommendations
    fn generate_recommendations(
        &self,
        gate_config: &ComplianceGateConfig,
        scan_result: &CICDPlaformResult,
        recommendations: &mut Vec<String>,
    ) {
        if scan_result.summary.compliance_score < gate_config.compliance_threshold {
            recommendations.push(format!(
                "Improve compliance score from {:.1}% to meet {:.1}% threshold",
                scan_result.summary.compliance_score,
                gate_config.compliance_threshold
            ));
        }

        if scan_result.summary.high_severity_issues > gate_config.max_high_severity_issues {
            recommendations.push(format!(
                "Reduce high-severity issues from {} to {} or fewer",
                scan_result.summary.high_severity_issues,
                gate_config.max_high_severity_issues
            ));
        }

        if scan_result.summary.medium_severity_issues > gate_config.max_medium_severity_issues {
            recommendations.push(format!(
                "Reduce medium-severity issues from {} to {} or fewer",
                scan_result.summary.medium_severity_issues,
                gate_config.max_medium_severity_issues
            ));
        }

        if scan_result.summary.low_severity_issues > gate_config.max_low_severity_issues {
            recommendations.push(format!(
                "Reduce low-severity issues from {} to {} or fewer",
                scan_result.summary.low_severity_issues,
                gate_config.max_low_severity_issues
            ));
        }
    }

    /// Determine overall gate status
    fn determine_gate_status(
        &self,
        policy_results: &[PolicyResult],
        violations: &[GateViolation],
        gate_config: &ComplianceGateConfig,
    ) -> GateStatus {
        let has_failures = policy_results.iter().any(|p| p.status == PolicyStatus::Failed);
        let has_critical_violations = violations.iter().any(|v| v.severity == GateSeverity::Critical);

        match gate_config.severity {
            GateSeverity::Low => {
                if has_critical_violations {
                    GateStatus::Warning
                } else {
                    GateStatus::Passed
                }
            }
            GateSeverity::Medium => {
                if has_failures {
                    GateStatus::Warning
                } else {
                    GateStatus::Passed
                }
            }
            GateSeverity::High | GateSeverity::Critical => {
                if has_failures || has_critical_violations {
                    GateStatus::Failed
                } else {
                    GateStatus::Passed
                }
            }
        }
    }

    /// Get gate statistics
    pub fn get_gate_stats(&self) -> HashMap<String, serde_json::Value> {
        let mut stats = HashMap::new();

        stats.insert("total_gates".to_string(), serde_json::json!(self.gates.len()));
        stats.insert("enabled_gates".to_string(), serde_json::json!(
            self.gates.values().filter(|g| g.enabled).count()
        ));

        let severity_counts = self.gates.values()
            .fold(HashMap::new(), |mut acc, gate| {
                let count = acc.entry(format!("{:?}", gate.severity)).or_insert(0);
                *count += 1;
                acc
            });

        stats.insert("severity_distribution".to_string(), serde_json::json!(severity_counts));

        stats
    }

    /// Validate gate configuration
    pub fn validate_gate_config(&self, config: &ComplianceGateConfig) -> Vec<String> {
        let mut issues = Vec::new();

        if config.name.is_empty() {
            issues.push("Gate name cannot be empty".to_string());
        }

        if config.compliance_threshold < 0.0 || config.compliance_threshold > 100.0 {
            issues.push("Compliance threshold must be between 0 and 100".to_string());
        }

        if config.timeout_seconds == 0 {
            issues.push("Timeout must be greater than 0".to_string());
        }

        issues
    }
}

impl Default for ComplianceGateConfig {
    fn default() -> Self {
        Self {
            name: "default-gate".to_string(),
            description: Some("Default compliance gate".to_string()),
            enabled: true,
            severity: GateSeverity::High,
            compliance_threshold: 80.0,
            max_critical_issues: 0,
            max_high_severity_issues: 5,
            max_medium_severity_issues: 10,
            max_low_severity_issues: 20,
            required_license_categories: vec![],
            forbidden_license_categories: vec![],
            custom_policies: vec![],
            timeout_seconds: 300,
            allow_manual_override: false,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::compliance::ci_cd_scanner::ScanSummary;

    #[test]
    fn test_gate_config_default() {
        let config = ComplianceGateConfig::default();
        assert_eq!(config.name, "default-gate");
        assert!(config.enabled);
        assert_eq!(config.severity, GateSeverity::High);
        assert_eq!(config.compliance_threshold, 80.0);
    }

    #[test]
    fn test_gate_manager_operations() {
        let mut manager = ComplianceGateManager::new();

        let gate_config = ComplianceGateConfig {
            name: "test-gate".to_string(),
            description: Some("Test gate".to_string()),
            enabled: true,
            severity: GateSeverity::Medium,
            compliance_threshold: 75.0,
            max_critical_issues: 0,
            max_high_severity_issues: 3,
            max_medium_severity_issues: 8,
            max_low_severity_issues: 15,
            required_license_categories: vec![],
            forbidden_license_categories: vec![],
            custom_policies: vec![],
            timeout_seconds: 300,
            allow_manual_override: true,
        };

        manager.add_gate(gate_config.clone());
        assert!(manager.get_gate("test-gate").is_some());
        assert_eq!(manager.list_gates().len(), 1);

        assert!(manager.remove_gate("test-gate"));
        assert!(manager.get_gate("test-gate").is_none());
    }

    #[test]
    fn test_compliance_threshold_evaluation() {
        let manager = ComplianceGateManager::new();
        let gate_config = ComplianceGateConfig {
            name: "test-gate".to_string(),
            compliance_threshold: 80.0,
            ..Default::default()
        };

        let scan_result = CICDPlaformResult {
            request: crate::compliance::ci_cd_scanner::CICDPlaformRequest {
                id: uuid::Uuid::new_v4(),
                config: crate::compliance::ci_cd_scanner::CICDPlaformConfig::default(),
                target: "/test".to_string(),
                changed_files: None,
                pr_info: None,
                build_info: None,
                metadata: HashMap::new(),
            },
            status: CIScanStatus::Completed,
            started_at: chrono::Utc::now(),
            completed_at: Some(chrono::Utc::now()),
            duration: Some(std::time::Duration::from_secs(10)),
            license_results: None,
            compliance_results: None,
            exit_code: 0,
            summary: ScanSummary {
                compliance_score: 85.0,
                files_scanned: 100,
                total_licenses: 10,
                high_severity_issues: 2,
                medium_severity_issues: 5,
                low_severity_issues: 8,
                failure_reason: None,
                should_fail_build: false,
            },
            formatted_output: String::new(),
            issues: vec![],
            recommendations: vec![],
            metadata: HashMap::new(),
        };

        let result = manager.evaluate_compliance_threshold(&gate_config, &scan_result);
        assert_eq!(result.status, PolicyStatus::Passed);
        assert!(result.message.contains("85.0%"));
        assert!(result.message.contains("80.0%"));
    }

    #[test]
    fn test_gate_config_validation() {
        let manager = ComplianceGateManager::new();

        let valid_config = ComplianceGateConfig::default();
        let issues = manager.validate_gate_config(&valid_config);
        assert!(issues.is_empty());

        let invalid_config = ComplianceGateConfig {
            name: "".to_string(),
            compliance_threshold: 150.0,
            timeout_seconds: 0,
            ..Default::default()
        };

        let issues = manager.validate_gate_config(&invalid_config);
        assert!(!issues.is_empty());
        assert!(issues.len() >= 3); // name, threshold, timeout
    }
}