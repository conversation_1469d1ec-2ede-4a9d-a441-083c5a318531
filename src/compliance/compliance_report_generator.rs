//! # Compliance Report Generator
//!
//! Generates detailed compliance reports with license conflict analysis,
//! compliance status assessment, risk assessment, and actionable recommendations.

use crate::{
    compliance::{
        report_generator::{BaseReportGenerator, GeneratedReport, ReportGenerator, ReportGeneratorConfig, ReportType},
        ComplianceFinding, ComplianceReport, ComplianceRequest, OutputFormat, ReportSummary,
        RiskAssessment, RiskFactor, RiskLevel, Severity,
    },
    config::ComplianceConfig,
    error::{InfinitumError, Result},
    scanners::{ScanResult, SoftwareComponent},
};
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, path::Path};
use tracing::{info, instrument};
use uuid::Uuid;

/// License conflict information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LicenseConflict {
    /// Conflict identifier
    pub id: String,
    /// Conflicting licenses
    pub licenses: Vec<String>,
    /// Conflict description
    pub description: String,
    /// Severity of the conflict
    pub severity: Severity,
    /// Affected components
    pub affected_components: Vec<String>,
    /// Resolution suggestions
    pub resolution_suggestions: Vec<String>,
}

/// Compliance assessment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceAssessment {
    /// Overall compliance score (0-100)
    pub overall_score: f64,
    /// Compliance level
    pub compliance_level: ComplianceLevel,
    /// License compliance score
    pub license_compliance_score: f64,
    /// Security compliance score
    pub security_compliance_score: f64,
    /// Risk compliance score
    pub risk_compliance_score: f64,
    /// Critical issues count
    pub critical_issues: u32,
    /// High severity issues count
    pub high_issues: u32,
    /// Medium severity issues count
    pub medium_issues: u32,
    /// Low severity issues count
    pub low_issues: u32,
}

/// Compliance levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ComplianceLevel {
    /// Excellent compliance
    Excellent,
    /// Good compliance
    Good,
    /// Satisfactory compliance
    Satisfactory,
    /// Needs improvement
    NeedsImprovement,
    /// Critical issues present
    Critical,
}

/// Executive summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutiveSummary {
    /// Key findings
    pub key_findings: Vec<String>,
    /// Critical actions required
    pub critical_actions: Vec<String>,
    /// Risk overview
    pub risk_overview: String,
    /// Compliance status
    pub compliance_status: String,
    /// Next steps
    pub next_steps: Vec<String>,
}

/// Detailed compliance report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetailedComplianceReport {
    /// Report metadata
    pub metadata: ReportMetadata,
    /// Executive summary
    pub executive_summary: ExecutiveSummary,
    /// Compliance assessment
    pub assessment: ComplianceAssessment,
    /// License conflicts
    pub license_conflicts: Vec<LicenseConflict>,
    /// Component analysis
    pub component_analysis: Vec<ComponentAnalysis>,
    /// Risk assessment
    pub risk_assessment: RiskAssessment,
    /// Recommendations
    pub recommendations: Vec<Recommendation>,
    /// Compliance findings
    pub findings: Vec<ComplianceFinding>,
}

/// Report metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReportMetadata {
    /// Report title
    pub title: String,
    /// Organization
    pub organization: String,
    /// Author
    pub author: String,
    /// Generation date
    pub generated_at: chrono::DateTime<chrono::Utc>,
    /// Report version
    pub version: String,
    /// Compliance framework
    pub framework: String,
}

/// Component analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentAnalysis {
    /// Component name
    pub name: String,
    /// Component version
    pub version: String,
    /// License information
    pub license: String,
    /// Security vulnerabilities
    pub vulnerabilities: Vec<String>,
    /// Compliance status
    pub compliance_status: ComplianceStatus,
    /// Risk score
    pub risk_score: f64,
}

/// Compliance status for components
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ComplianceStatus {
    /// Compliant
    Compliant,
    /// Non-compliant
    NonCompliant,
    /// Needs review
    NeedsReview,
    /// Unknown
    Unknown,
}

/// Recommendation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Recommendation {
    /// Recommendation ID
    pub id: String,
    /// Title
    pub title: String,
    /// Description
    pub description: String,
    /// Priority
    pub priority: Priority,
    /// Category
    pub category: RecommendationCategory,
    /// Implementation effort
    pub effort: ImplementationEffort,
    /// Expected impact
    pub expected_impact: String,
    /// Implementation steps
    pub implementation_steps: Vec<String>,
}

/// Recommendation categories
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum RecommendationCategory {
    /// License related
    License,
    /// Security related
    Security,
    /// Risk management
    Risk,
    /// Process improvement
    Process,
    /// Documentation
    Documentation,
}

/// Priority levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
#[serde(rename_all = "lowercase")]
pub enum Priority {
    /// Critical priority
    Critical,
    /// High priority
    High,
    /// Medium priority
    Medium,
    /// Low priority
    Low,
}

/// Implementation effort levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ImplementationEffort {
    /// Low effort (< 1 day)
    Low,
    /// Medium effort (1-5 days)
    Medium,
    /// High effort (1-4 weeks)
    High,
    /// Very high effort (> 1 month)
    VeryHigh,
}

/// Compliance Report Generator
pub struct ComplianceReportGenerator {
    base: BaseReportGenerator,
}

impl ComplianceReportGenerator {
    /// Create new compliance report generator
    pub fn new(config: ReportGeneratorConfig, compliance_config: ComplianceConfig) -> Self {
        Self {
            base: BaseReportGenerator::new(config, compliance_config),
        }
    }

    /// Generate detailed compliance report
    async fn generate_detailed_report(
        &self,
        request: &ComplianceRequest,
        scan_results: &[ScanResult],
    ) -> Result<DetailedComplianceReport> {
        let metadata = ReportMetadata {
            title: request.config.title.clone(),
            organization: request.config.organization.clone(),
            author: request.config.author.clone(),
            generated_at: chrono::Utc::now(),
            version: "1.0".to_string(),
            framework: format!("{:?}", request.framework),
        };

        // Analyze components
        let component_analysis = self.analyze_components(scan_results).await?;

        // Assess compliance
        let assessment = self.assess_compliance(&component_analysis).await?;

        // Analyze license conflicts
        let license_conflicts = self.analyze_license_conflicts(&component_analysis).await?;

        // Generate risk assessment
        let risk_assessment = self.generate_risk_assessment(&component_analysis, &license_conflicts).await?;

        // Generate recommendations
        let recommendations = self.generate_recommendations(&assessment, &license_conflicts, &risk_assessment).await?;

        // Generate findings
        let findings = self.generate_findings(&component_analysis, &license_conflicts).await?;

        // Generate executive summary
        let executive_summary = self.generate_executive_summary(&assessment, &license_conflicts, &recommendations).await?;

        Ok(DetailedComplianceReport {
            metadata,
            executive_summary,
            assessment,
            license_conflicts,
            component_analysis,
            risk_assessment,
            recommendations,
            findings,
        })
    }

    /// Analyze software components
    async fn analyze_components(&self, scan_results: &[ScanResult]) -> Result<Vec<ComponentAnalysis>> {
        let mut analyses = Vec::new();

        for scan_result in scan_results {
            for component in &scan_result.software_components {
                let analysis = ComponentAnalysis {
                    name: component.name.clone(),
                    version: component.version.clone(),
                    license: component.license.clone().unwrap_or_else(|| "Unknown".to_string()),
                    vulnerabilities: scan_result.vulnerabilities.iter()
                        .filter(|v| v.component == component.name)
                        .map(|v| v.cve_id.clone())
                        .collect(),
                    compliance_status: self.assess_component_compliance(component).await?,
                    risk_score: self.calculate_component_risk(component, scan_result).await?,
                };
                analyses.push(analysis);
            }
        }

        Ok(analyses)
    }

    /// Assess component compliance
    async fn assess_component_compliance(&self, component: &SoftwareComponent) -> Result<ComplianceStatus> {
        // Simple compliance assessment based on license and vulnerabilities
        if component.license.is_none() {
            return Ok(ComplianceStatus::Unknown);
        }

        let license = component.license.as_ref().unwrap();

        // Check for problematic licenses
        if license.contains("GPL") || license.contains("LGPL") {
            return Ok(ComplianceStatus::NeedsReview);
        }

        // Check for permissive licenses
        if license.contains("MIT") || license.contains("Apache") || license.contains("BSD") {
            return Ok(ComplianceStatus::Compliant);
        }

        Ok(ComplianceStatus::NeedsReview)
    }

    /// Calculate component risk score
    async fn calculate_component_risk(&self, component: &SoftwareComponent, scan_result: &ScanResult) -> Result<f64> {
        let mut risk_score = 0.0;

        // License risk
        if let Some(license) = &component.license {
            if license.contains("GPL") {
                risk_score += 30.0;
            } else if license.contains("MIT") || license.contains("Apache") {
                risk_score += 10.0;
            } else {
                risk_score += 20.0;
            }
        } else {
            risk_score += 50.0; // Unknown license is high risk
        }

        // Vulnerability risk
        let vuln_count = scan_result.vulnerabilities.iter()
            .filter(|v| v.component == component.name)
            .count();
        risk_score += (vuln_count as f64) * 15.0;

        // Cap at 100
        Ok(risk_score.min(100.0))
    }

    /// Assess overall compliance
    async fn assess_compliance(&self, component_analysis: &[ComponentAnalysis]) -> Result<ComplianceAssessment> {
        let total_components = component_analysis.len() as f64;

        if total_components == 0.0 {
            return Ok(ComplianceAssessment {
                overall_score: 0.0,
                compliance_level: ComplianceLevel::Critical,
                license_compliance_score: 0.0,
                security_compliance_score: 0.0,
                risk_compliance_score: 0.0,
                critical_issues: 0,
                high_issues: 0,
                medium_issues: 0,
                low_issues: 0,
            });
        }

        // Calculate license compliance
        let license_compliant = component_analysis.iter()
            .filter(|c| c.compliance_status == ComplianceStatus::Compliant)
            .count() as f64;
        let license_compliance_score = (license_compliant / total_components) * 100.0;

        // Calculate security compliance (inverse of vulnerabilities)
        let avg_vulnerabilities = component_analysis.iter()
            .map(|c| c.vulnerabilities.len() as f64)
            .sum::<f64>() / total_components;
        let security_compliance_score = (1.0 - (avg_vulnerabilities / 10.0).min(1.0)) * 100.0;

        // Calculate risk compliance
        let avg_risk = component_analysis.iter()
            .map(|c| c.risk_score)
            .sum::<f64>() / total_components;
        let risk_compliance_score = 100.0 - avg_risk;

        // Overall score (weighted average)
        let overall_score = (license_compliance_score * 0.4) +
                           (security_compliance_score * 0.4) +
                           (risk_compliance_score * 0.2);

        // Count issues by severity
        let critical_issues = component_analysis.iter()
            .filter(|c| c.risk_score > 80.0)
            .count() as u32;
        let high_issues = component_analysis.iter()
            .filter(|c| c.risk_score > 60.0 && c.risk_score <= 80.0)
            .count() as u32;
        let medium_issues = component_analysis.iter()
            .filter(|c| c.risk_score > 40.0 && c.risk_score <= 60.0)
            .count() as u32;
        let low_issues = component_analysis.iter()
            .filter(|c| c.risk_score <= 40.0)
            .count() as u32;

        let compliance_level = if overall_score >= 90.0 {
            ComplianceLevel::Excellent
        } else if overall_score >= 80.0 {
            ComplianceLevel::Good
        } else if overall_score >= 70.0 {
            ComplianceLevel::Satisfactory
        } else if overall_score >= 60.0 {
            ComplianceLevel::NeedsImprovement
        } else {
            ComplianceLevel::Critical
        };

        Ok(ComplianceAssessment {
            overall_score,
            compliance_level,
            license_compliance_score,
            security_compliance_score,
            risk_compliance_score,
            critical_issues,
            high_issues,
            medium_issues,
            low_issues,
        })
    }

    /// Analyze license conflicts
    async fn analyze_license_conflicts(&self, component_analysis: &[ComponentAnalysis]) -> Result<Vec<LicenseConflict>> {
        let mut conflicts = Vec::new();
        let mut license_groups: HashMap<String, Vec<String>> = HashMap::new();

        // Group components by license
        for analysis in component_analysis {
            license_groups.entry(analysis.license.clone())
                .or_insert_with(Vec::new)
                .push(analysis.name.clone());
        }

        // Check for incompatible license combinations
        let licenses: Vec<String> = license_groups.keys().cloned().collect();

        for i in 0..licenses.len() {
            for j in (i + 1)..licenses.len() {
                let license1 = &licenses[i];
                let license2 = &licenses[j];

                if self.licenses_conflict(license1, license2) {
                    let components1 = license_groups.get(license1).unwrap();
                    let components2 = license_groups.get(license2).unwrap();

                    let mut affected_components = components1.clone();
                    affected_components.extend(components2.clone());

                    conflicts.push(LicenseConflict {
                        id: format!("conflict-{}-{}", i, j),
                        licenses: vec![license1.clone(), license2.clone()],
                        description: format!("License conflict between {} and {}", license1, license2),
                        severity: Severity::High,
                        affected_components,
                        resolution_suggestions: vec![
                            "Review license compatibility".to_string(),
                            "Consider replacing one of the conflicting licenses".to_string(),
                            "Consult legal counsel for guidance".to_string(),
                        ],
                    });
                }
            }
        }

        Ok(conflicts)
    }

    /// Check if two licenses conflict
    fn licenses_conflict(&self, license1: &str, license2: &str) -> bool {
        // Simple conflict detection
        let copyleft_licenses = ["GPL", "LGPL", "CDDL", "EPL"];
        let proprietary_licenses = ["Proprietary", "Commercial"];

        let is_copyleft1 = copyleft_licenses.iter().any(|l| license1.contains(l));
        let is_copyleft2 = copyleft_licenses.iter().any(|l| license2.contains(l));
        let is_proprietary1 = proprietary_licenses.iter().any(|l| license1.contains(l));
        let is_proprietary2 = proprietary_licenses.iter().any(|l| license2.contains(l));

        // Copyleft licenses generally conflict with proprietary licenses
        (is_copyleft1 && is_proprietary2) || (is_copyleft2 && is_proprietary1)
    }

    /// Generate risk assessment
    async fn generate_risk_assessment(
        &self,
        component_analysis: &[ComponentAnalysis],
        license_conflicts: &[LicenseConflict],
    ) -> Result<RiskAssessment> {
        let mut risk_factors = Vec::new();
        let mut mitigation_strategies = Vec::new();

        // License risk factors
        let unknown_licenses = component_analysis.iter()
            .filter(|c| c.license == "Unknown")
            .count();
        if unknown_licenses > 0 {
            risk_factors.push(RiskFactor {
                name: "Unknown Licenses".to_string(),
                description: format!("{} components have unknown licenses", unknown_licenses),
                impact_score: 8.0,
                likelihood_score: 7.0,
                risk_score: 56.0,
            });
            mitigation_strategies.push("Identify and document all component licenses".to_string());
        }

        // License conflict risk
        if !license_conflicts.is_empty() {
            risk_factors.push(RiskFactor {
                name: "License Conflicts".to_string(),
                description: format!("{} license conflicts detected", license_conflicts.len()),
                impact_score: 9.0,
                likelihood_score: 6.0,
                risk_score: 54.0,
            });
            mitigation_strategies.push("Resolve license conflicts through legal review".to_string());
        }

        // Vulnerability risk
        let total_vulnerabilities = component_analysis.iter()
            .map(|c| c.vulnerabilities.len())
            .sum::<usize>();
        if total_vulnerabilities > 0 {
            risk_factors.push(RiskFactor {
                name: "Security Vulnerabilities".to_string(),
                description: format!("{} vulnerabilities found across components", total_vulnerabilities),
                impact_score: 9.0,
                likelihood_score: 8.0,
                risk_score: 72.0,
            });
            mitigation_strategies.push("Update vulnerable components to patched versions".to_string());
        }

        // Calculate overall risk score
        let overall_risk_score = if risk_factors.is_empty() {
            0.0
        } else {
            risk_factors.iter().map(|f| f.risk_score).sum::<f64>() / risk_factors.len() as f64
        };

        let risk_level = if overall_risk_score >= 70.0 {
            RiskLevel::VeryHigh
        } else if overall_risk_score >= 50.0 {
            RiskLevel::High
        } else if overall_risk_score >= 30.0 {
            RiskLevel::Medium
        } else if overall_risk_score >= 10.0 {
            RiskLevel::Low
        } else {
            RiskLevel::VeryLow
        };

        Ok(RiskAssessment {
            overall_risk_score,
            risk_level,
            risk_factors,
            mitigation_strategies,
        })
    }

    /// Generate recommendations
    async fn generate_recommendations(
        &self,
        assessment: &ComplianceAssessment,
        license_conflicts: &[LicenseConflict],
        risk_assessment: &RiskAssessment,
    ) -> Result<Vec<Recommendation>> {
        let mut recommendations = Vec::new();

        // License-related recommendations
        if assessment.license_compliance_score < 80.0 {
            recommendations.push(Recommendation {
                id: "license-audit".to_string(),
                title: "Conduct License Audit".to_string(),
                description: "Perform comprehensive audit of all component licenses".to_string(),
                priority: Priority::High,
                category: RecommendationCategory::License,
                effort: ImplementationEffort::Medium,
                expected_impact: "Improve license compliance score by identifying and resolving license issues".to_string(),
                implementation_steps: vec![
                    "Inventory all software components and their licenses".to_string(),
                    "Review license compatibility and conflicts".to_string(),
                    "Document license obligations and requirements".to_string(),
                    "Implement license approval process for new components".to_string(),
                ],
            });
        }

        // Security recommendations
        if assessment.security_compliance_score < 80.0 {
            recommendations.push(Recommendation {
                id: "vulnerability-management".to_string(),
                title: "Implement Vulnerability Management Process".to_string(),
                description: "Establish process for identifying and remediating security vulnerabilities".to_string(),
                priority: Priority::Critical,
                category: RecommendationCategory::Security,
                effort: ImplementationEffort::High,
                expected_impact: "Reduce security risks and improve compliance posture".to_string(),
                implementation_steps: vec![
                    "Set up automated vulnerability scanning".to_string(),
                    "Establish vulnerability triage and remediation process".to_string(),
                    "Implement security patch management".to_string(),
                    "Regular security assessments and penetration testing".to_string(),
                ],
            });
        }

        // Risk management recommendations
        if assessment.risk_compliance_score < 80.0 {
            recommendations.push(Recommendation {
                id: "risk-assessment".to_string(),
                title: "Regular Risk Assessments".to_string(),
                description: "Conduct regular risk assessments of the software supply chain".to_string(),
                priority: Priority::High,
                category: RecommendationCategory::Risk,
                effort: ImplementationEffort::Medium,
                expected_impact: "Better understanding and mitigation of compliance risks".to_string(),
                implementation_steps: vec![
                    "Define risk assessment methodology".to_string(),
                    "Identify critical components and suppliers".to_string(),
                    "Assess license and security risks".to_string(),
                    "Develop risk mitigation strategies".to_string(),
                ],
            });
        }

        // License conflict recommendations
        if !license_conflicts.is_empty() {
            recommendations.push(Recommendation {
                id: "license-conflict-resolution".to_string(),
                title: "Resolve License Conflicts".to_string(),
                description: format!("Address {} identified license conflicts", license_conflicts.len()),
                priority: Priority::High,
                category: RecommendationCategory::License,
                effort: ImplementationEffort::High,
                expected_impact: "Eliminate legal risks from license incompatibilities".to_string(),
                implementation_steps: vec![
                    "Review each license conflict in detail".to_string(),
                    "Consult legal counsel for guidance".to_string(),
                    "Replace conflicting components where possible".to_string(),
                    "Document conflict resolutions".to_string(),
                ],
            });
        }

        Ok(recommendations)
    }

    /// Generate compliance findings
    async fn generate_findings(
        &self,
        component_analysis: &[ComponentAnalysis],
        license_conflicts: &[LicenseConflict],
    ) -> Result<Vec<ComplianceFinding>> {
        let mut findings = Vec::new();

        // Generate findings from component analysis
        for analysis in component_analysis {
            if analysis.compliance_status == ComplianceStatus::NonCompliant {
                findings.push(ComplianceFinding {
                    id: format!("finding-{}", analysis.name),
                    title: format!("Non-compliant component: {}", analysis.name),
                    description: format!("Component {} has compliance issues", analysis.name),
                    severity: Severity::High,
                    control_reference: "License Compliance".to_string(),
                    affected_components: vec![analysis.name.clone()],
                    evidence: vec![format!("License: {}", analysis.license)],
                    status: crate::compliance::FindingStatus::Open,
                });
            }

            if !analysis.vulnerabilities.is_empty() {
                findings.push(ComplianceFinding {
                    id: format!("security-finding-{}", analysis.name),
                    title: format!("Security vulnerabilities in {}", analysis.name),
                    description: format!("Component has {} security vulnerabilities", analysis.vulnerabilities.len()),
                    severity: Severity::Critical,
                    control_reference: "Security Compliance".to_string(),
                    affected_components: vec![analysis.name.clone()],
                    evidence: analysis.vulnerabilities.clone(),
                    status: crate::compliance::FindingStatus::Open,
                });
            }
        }

        // Generate findings from license conflicts
        for conflict in license_conflicts {
            findings.push(ComplianceFinding {
                id: conflict.id.clone(),
                title: "License Conflict Detected".to_string(),
                description: conflict.description.clone(),
                severity: conflict.severity,
                control_reference: "License Compatibility".to_string(),
                affected_components: conflict.affected_components.clone(),
                evidence: vec![format!("Conflicting licenses: {}", conflict.licenses.join(", "))],
                status: crate::compliance::FindingStatus::Open,
            });
        }

        Ok(findings)
    }

    /// Generate executive summary
    async fn generate_executive_summary(
        &self,
        assessment: &ComplianceAssessment,
        license_conflicts: &[LicenseConflict],
        recommendations: &[Recommendation],
    ) -> Result<ExecutiveSummary> {
        let key_findings = vec![
            format!("Overall compliance score: {:.1}%", assessment.overall_score),
            format!("License compliance: {:.1}%", assessment.license_compliance_score),
            format!("Security compliance: {:.1}%", assessment.security_compliance_score),
            format!("{} license conflicts detected", license_conflicts.len()),
            format!("{} critical recommendations", recommendations.iter().filter(|r| r.priority == Priority::Critical).count()),
        ];

        let critical_actions = recommendations.iter()
            .filter(|r| r.priority == Priority::Critical)
            .map(|r| r.title.clone())
            .collect();

        let risk_overview = format!(
            "Current risk level: {:?} (Score: {:.1})",
            assessment.compliance_level, assessment.overall_score
        );

        let compliance_status = match assessment.compliance_level {
            ComplianceLevel::Excellent => "Excellent - All compliance requirements met",
            ComplianceLevel::Good => "Good - Minor improvements needed",
            ComplianceLevel::Satisfactory => "Satisfactory - Moderate improvements required",
            ComplianceLevel::NeedsImprovement => "Needs Improvement - Significant issues identified",
            ComplianceLevel::Critical => "Critical - Immediate action required",
        }.to_string();

        let next_steps = recommendations.iter()
            .take(3)
            .map(|r| format!("{}: {}", r.priority, r.title))
            .collect();

        Ok(ExecutiveSummary {
            key_findings,
            critical_actions,
            risk_overview,
            compliance_status,
            next_steps,
        })
    }

    /// Format report as JSON
    fn format_as_json(&self, report: &DetailedComplianceReport) -> String {
        serde_json::to_string_pretty(report).unwrap_or_else(|_| "{}".to_string())
    }

    /// Format report as HTML
    fn format_as_html(&self, report: &DetailedComplianceReport) -> String {
        let mut html = String::from("<!DOCTYPE html>\n<html>\n<head>\n");
        html.push_str("<title>Compliance Report</title>\n");
        html.push_str("<style>\n");
        html.push_str("body { font-family: Arial, sans-serif; margin: 40px; }\n");
        html.push_str("h1, h2 { color: #333; }\n");
        html.push_str(".score { font-size: 2em; font-weight: bold; }\n");
        html.push_str(".excellent { color: #27ae60; }\n");
        html.push_str(".good { color: #2ecc71; }\n");
        html.push_str(".satisfactory { color: #f39c12; }\n");
        html.push_str(".needs-improvement { color: #e67e22; }\n");
        html.push_str(".critical { color: #e74c3c; }\n");
        html.push_str("</style>\n");
        html.push_str("</head>\n<body>\n");

        html.push_str(&format!("<h1>{}</h1>\n", report.metadata.title));
        html.push_str(&format!("<p><strong>Organization:</strong> {}</p>\n", report.metadata.organization));
        html.push_str(&format!("<p><strong>Generated:</strong> {}</p>\n", report.metadata.generated_at.format("%Y-%m-%d %H:%M:%S UTC")));

        // Executive Summary
        html.push_str("<h2>Executive Summary</h2>\n");
        html.push_str(&format!("<p><strong>Compliance Score:</strong> <span class=\"score {}\">{:.1}%</span></p>\n",
            match report.assessment.compliance_level {
                ComplianceLevel::Excellent => "excellent",
                ComplianceLevel::Good => "good",
                ComplianceLevel::Satisfactory => "satisfactory",
                ComplianceLevel::NeedsImprovement => "needs-improvement",
                ComplianceLevel::Critical => "critical",
            },
            report.assessment.overall_score));

        html.push_str("<h3>Key Findings</h3>\n<ul>\n");
        for finding in &report.executive_summary.key_findings {
            html.push_str(&format!("<li>{}</li>\n", finding));
        }
        html.push_str("</ul>\n");

        html.push_str("</body>\n</html>\n");
        html
    }
}

#[async_trait]
impl ReportGenerator for ComplianceReportGenerator {
    async fn generate_report(
        &self,
        request: &ComplianceRequest,
        scan_results: &[ScanResult],
    ) -> Result<GeneratedReport> {
        let start_time = chrono::Utc::now();

        info!("Generating detailed compliance report for request {}", request.id);

        // Validate output directory
        self.base.validate_output_dir().await?;

        // Generate detailed report
        let detailed_report = self.generate_detailed_report(request, scan_results).await?;

        // Generate outputs in requested formats
        let mut files = HashMap::new();
        for format in &request.config.output_formats {
            if self.supported_formats().contains(format) {
                let filename = self.base.generate_filename("compliance", format, request.id);
                let file_path = self.base.create_output_path(&filename);

                let content = match format {
                    OutputFormat::Json => self.format_as_json(&detailed_report),
                    OutputFormat::Html => self.format_as_html(&detailed_report),
                    _ => self.format_as_json(&detailed_report), // Default to JSON
                };

                self.base.write_file(&file_path, &content).await?;
                files.insert(format.clone(), file_path);
            }
        }

        let end_time = chrono::Utc::now();
        let duration = end_time - start_time;

        let metadata = self.base.generate_metadata(request);
        let mut enhanced_metadata = metadata;
        enhanced_metadata.insert("compliance_score".to_string(), serde_json::json!(detailed_report.assessment.overall_score));
        enhanced_metadata.insert("license_conflicts".to_string(), serde_json::json!(detailed_report.license_conflicts.len()));
        enhanced_metadata.insert("recommendations".to_string(), serde_json::json!(detailed_report.recommendations.len()));

        Ok(GeneratedReport {
            id: Uuid::new_v4(),
            report_type: ReportType::Compliance,
            files,
            metadata: enhanced_metadata,
            generated_at: end_time,
            duration_ms: duration.num_milliseconds() as u64,
        })
    }

    fn supported_formats(&self) -> Vec<OutputFormat> {
        vec![OutputFormat::Json, OutputFormat::Html]
    }

    fn name(&self) -> &'static str {
        "Compliance Report Generator"
    }

    fn description(&self) -> &'static str {
        "Generates detailed compliance reports with license conflict analysis, risk assessment, and actionable recommendations"
    }

    fn can_handle(&self, request: &ComplianceRequest) -> bool {
        request.config.output_formats.iter().any(|f| self.supported_formats().contains(f))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_compliance_report_generator_creation() {
        let config = ReportGeneratorConfig::default();
        let compliance_config = ComplianceConfig::default();
        let generator = ComplianceReportGenerator::new(config, compliance_config);
        assert_eq!(generator.name(), "Compliance Report Generator");
    }

    #[test]
    fn test_supported_formats() {
        let config = ReportGeneratorConfig::default();
        let compliance_config = ComplianceConfig::default();
        let generator = ComplianceReportGenerator::new(config, compliance_config);
        let formats = generator.supported_formats();
        assert!(formats.contains(&OutputFormat::Json));
        assert!(formats.contains(&OutputFormat::Html));
    }

    #[test]
    fn test_compliance_level_serialization() {
        let level = ComplianceLevel::Excellent;
        let serialized = serde_json::to_string(&level).unwrap();
        assert_eq!(serialized, "\"excellent\"");
    }

    #[test]
    fn test_priority_ordering() {
        assert!(Priority::Critical > Priority::High);
        assert!(Priority::High > Priority::Medium);
        assert!(Priority::Medium > Priority::Low);
    }

    #[tokio::test]
    async fn test_license_conflict_detection() {
        let config = ReportGeneratorConfig::default();
        let compliance_config = ComplianceConfig::default();
        let generator = ComplianceReportGenerator::new(config, compliance_config);

        // Test GPL vs Proprietary conflict
        assert!(generator.licenses_conflict("GPL-3.0", "Proprietary"));
        assert!(generator.licenses_conflict("Proprietary", "LGPL-2.1"));

        // Test compatible licenses
        assert!(!generator.licenses_conflict("MIT", "Apache-2.0"));
        assert!(!generator.licenses_conflict("BSD-3-Clause", "MIT"));
    }
}