//! # Compliance Validator
//!
//! Cross-validation system for license compliance with multi-source verification,
//! confidence score aggregation, and evidence-based validation.

use crate::{
    compliance::{
        dual_licensing_handler::DualLicensingHandler,
        edge_case_detector::{EdgeCaseDetector, EdgeCaseDetectionResult},
        internationalization_engine::InternationalizationEngine,
        license_conflict_resolver::{LicenseConflictResolver, ResolutionStrategy},
        license_expression_parser::LicenseExpressionParser,
        i18n::Language,
    },
    error::{InfinitumError, Result},
    scanners::{ScanResult, SoftwareComponent},
};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use tracing::{debug, info, instrument, warn};

/// Validation source
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ValidationSource {
    /// SPDX license database
    SpdxDatabase,
    /// License scanning tool
    LicenseScanner,
    /// Manual verification
    ManualVerification,
    /// Third-party database
    ThirdPartyDatabase,
    /// File header analysis
    FileHeaderAnalysis,
    /// Package metadata
    PackageMetadata,
}

/// Validation evidence
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationEvidence {
    /// Source of the evidence
    pub source: ValidationSource,
    /// License detected
    pub license: String,
    /// Confidence score (0-100)
    pub confidence: f64,
    /// Evidence details
    pub details: String,
    /// Timestamp of validation
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// Additional metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Validation result for a single license
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LicenseValidationResult {
    /// Component name
    pub component: String,
    /// Detected license expression
    pub detected_license: String,
    /// Validation evidence
    pub evidence: Vec<ValidationEvidence>,
    /// Aggregated confidence score
    pub aggregated_confidence: f64,
    /// Validation status
    pub status: ValidationStatus,
    /// Validation issues
    pub issues: Vec<String>,
    /// Recommendations
    pub recommendations: Vec<String>,
}

/// Overall validation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceValidationResult {
    /// Individual license validations
    pub license_validations: Vec<LicenseValidationResult>,
    /// Overall compliance score
    pub overall_compliance_score: f64,
    /// Validation summary
    pub summary: ValidationSummary,
    /// Risk assessment
    pub risk_assessment: ValidationRiskAssessment,
    /// Processing statistics
    pub statistics: ValidationStatistics,
}

/// Validation status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ValidationStatus {
    /// Validation passed
    Valid,
    /// Validation failed
    Invalid,
    /// Validation uncertain (requires manual review)
    Uncertain,
    /// Validation not possible
    NotApplicable,
}

/// Validation summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationSummary {
    /// Total components validated
    pub total_components: usize,
    /// Valid components
    pub valid_components: usize,
    /// Invalid components
    pub invalid_components: usize,
    /// Uncertain components
    pub uncertain_components: usize,
    /// Components requiring manual review
    pub manual_review_required: usize,
}

/// Risk assessment for validation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationRiskAssessment {
    /// Risk score (0-100)
    pub risk_score: f64,
    /// Risk factors
    pub risk_factors: Vec<String>,
    /// Mitigation strategies
    pub mitigation_strategies: Vec<String>,
    /// Critical issues
    pub critical_issues: Vec<String>,
}

/// Validation statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationStatistics {
    /// Total validation time in milliseconds
    pub total_validation_time_ms: u64,
    /// Average confidence score
    pub average_confidence: f64,
    /// Sources used
    pub sources_used: HashSet<ValidationSource>,
    /// Validation rules applied
    pub rules_applied: usize,
}

/// Compliance validator
pub struct ComplianceValidator {
    dual_handler: DualLicensingHandler,
    edge_detector: EdgeCaseDetector,
    i18n_engine: InternationalizationEngine,
    conflict_resolver: LicenseConflictResolver,
    validation_rules: Vec<ValidationRule>,
}

impl ComplianceValidator {
    /// Create new compliance validator
    pub fn new() -> Self {
        let mut validator = Self {
            dual_handler: DualLicensingHandler::new(),
            edge_detector: EdgeCaseDetector::new(),
            i18n_engine: InternationalizationEngine::new("translations".to_string()),
            conflict_resolver: LicenseConflictResolver::new(),
            validation_rules: Vec::new(),
        };
        validator.initialize_validation_rules();
        validator
    }

    /// Initialize validation rules
    fn initialize_validation_rules(&mut self) {
        self.validation_rules.push(ValidationRule {
            name: "SPDX License Format".to_string(),
            description: "Validate SPDX license identifier format".to_string(),
            rule_type: ValidationRuleType::Format,
            severity: ValidationSeverity::Medium,
            validator: Box::new(|license, _| Self::validate_spdx_format(license)),
        });

        self.validation_rules.push(ValidationRule {
            name: "License Expression Syntax".to_string(),
            description: "Validate license expression syntax".to_string(),
            rule_type: ValidationRuleType::Syntax,
            severity: ValidationSeverity::High,
            validator: Box::new(|license, _| Self::validate_expression_syntax(license)),
        });

        self.validation_rules.push(ValidationRule {
            name: "Confidence Threshold".to_string(),
            description: "Check minimum confidence threshold".to_string(),
            rule_type: ValidationRuleType::Confidence,
            severity: ValidationSeverity::Medium,
            validator: Box::new(|_, evidence| Self::validate_confidence_threshold(evidence)),
        });

        self.validation_rules.push(ValidationRule {
            name: "Source Consistency".to_string(),
            description: "Check consistency across validation sources".to_string(),
            rule_type: ValidationRuleType::Consistency,
            severity: ValidationSeverity::High,
            validator: Box::new(|license, evidence| Self::validate_source_consistency(license, evidence)),
        });
    }

    /// Validate compliance for scan results
    #[instrument(skip(self, scan_results))]
    pub async fn validate_compliance(&mut self, scan_results: &[ScanResult]) -> Result<ComplianceValidationResult> {
        let start_time = std::time::Instant::now();
        info!("Starting compliance validation for {} scan results", scan_results.len());

        let mut license_validations = Vec::new();
        let mut sources_used = HashSet::new();

        // First pass: detect edge cases
        let edge_case_result = self.edge_detector.detect_edge_cases(scan_results).await?;
        debug!("Edge case detection completed with {} cases", edge_case_result.edge_cases.len());

        // Second pass: validate each component
        for scan_result in scan_results {
            for component in &scan_result.software_components {
                let validation = self.validate_component_license(component, scan_result).await?;
                sources_used.extend(validation.evidence.iter().map(|e| e.source.clone()));
                license_validations.push(validation);
            }
        }

        // Calculate overall compliance score
        let overall_compliance_score = self.calculate_overall_compliance_score(&license_validations);

        // Generate validation summary
        let summary = self.generate_validation_summary(&license_validations);

        // Assess validation risk
        let risk_assessment = self.assess_validation_risk(&license_validations, &edge_case_result);

        let validation_time = start_time.elapsed().as_millis() as u64;
        let average_confidence = if !license_validations.is_empty() {
            license_validations.iter().map(|v| v.aggregated_confidence).sum::<f64>() / license_validations.len() as f64
        } else {
            0.0
        };

        let statistics = ValidationStatistics {
            total_validation_time_ms: validation_time,
            average_confidence,
            sources_used,
            rules_applied: self.validation_rules.len(),
        };

        info!("Compliance validation completed in {}ms with score {:.1}", validation_time, overall_compliance_score);

        Ok(ComplianceValidationResult {
            license_validations,
            overall_compliance_score,
            summary,
            risk_assessment,
            statistics,
        })
    }

    /// Validate license for a single component
    async fn validate_component_license(
        &mut self,
        component: &SoftwareComponent,
        scan_result: &ScanResult,
    ) -> Result<LicenseValidationResult> {
        let mut evidence = Vec::new();
        let mut issues = Vec::new();
        let mut recommendations = Vec::new();

        // Collect evidence from scan result
        if let Some(license) = &component.license {
            // Add evidence from component metadata
            evidence.push(ValidationEvidence {
                source: ValidationSource::PackageMetadata,
                license: license.clone(),
                confidence: 80.0, // High confidence from package metadata
                details: "License from package metadata".to_string(),
                timestamp: chrono::Utc::now(),
                metadata: HashMap::from([
                    ("component".to_string(), serde_json::json!(component.name)),
                    ("version".to_string(), serde_json::json!(component.version)),
                ]),
            });
        }

        // Add evidence from license detections
        for detection in &scan_result.license_detections {
            for license in &detection.licenses {
                evidence.push(ValidationEvidence {
                    source: ValidationSource::LicenseScanner,
                    license: license.spdx_id.clone(),
                    confidence: (license.confidence * 100.0).round(),
                    details: format!("Detected in file: {}", detection.file_path),
                    timestamp: chrono::Utc::now(),
                    metadata: HashMap::from([
                        ("file_path".to_string(), serde_json::json!(detection.file_path)),
                        ("license_name".to_string(), serde_json::json!(license.name)),
                        ("line_number".to_string(), serde_json::json!(license.start_line)),
                    ]),
                });
            }
        }

        // Apply validation rules
        let detected_license = component.license.clone().unwrap_or_else(|| "NOASSERTION".to_string());
        for rule in &self.validation_rules {
            match (rule.validator)(&detected_license, &evidence) {
                Ok(validation_result) => {
                    if !validation_result.is_valid {
                        issues.push(format!("{}: {}", rule.name, validation_result.message));
                        recommendations.extend(validation_result.recommendations);
                    }
                }
                Err(e) => {
                    warn!("Validation rule '{}' failed: {}", rule.name, e);
                }
            }
        }

        // Calculate aggregated confidence
        let aggregated_confidence = self.calculate_aggregated_confidence(&evidence);

        // Determine validation status
        let status = self.determine_validation_status(&issues, aggregated_confidence);

        Ok(LicenseValidationResult {
            component: component.name.clone(),
            detected_license,
            evidence,
            aggregated_confidence,
            status,
            issues,
            recommendations,
        })
    }

    /// Calculate aggregated confidence from evidence
    fn calculate_aggregated_confidence(&self, evidence: &[ValidationEvidence]) -> f64 {
        if evidence.is_empty() {
            return 0.0;
        }

        // Weight evidence by source reliability
        let mut weighted_sum = 0.0;
        let mut total_weight = 0.0;

        for ev in evidence {
            let weight = match ev.source {
                ValidationSource::ManualVerification => 1.0,
                ValidationSource::SpdxDatabase => 0.9,
                ValidationSource::PackageMetadata => 0.8,
                ValidationSource::LicenseScanner => 0.7,
                ValidationSource::FileHeaderAnalysis => 0.6,
                ValidationSource::ThirdPartyDatabase => 0.5,
            };

            weighted_sum += ev.confidence * weight;
            total_weight += weight;
        }

        if total_weight > 0.0 {
            (weighted_sum / total_weight).min(100.0)
        } else {
            0.0
        }
    }

    /// Determine validation status
    fn determine_validation_status(&self, issues: &[String], confidence: f64) -> ValidationStatus {
        if issues.is_empty() && confidence >= 80.0 {
            ValidationStatus::Valid
        } else if confidence < 50.0 {
            ValidationStatus::Invalid
        } else if !issues.is_empty() || confidence < 70.0 {
            ValidationStatus::Uncertain
        } else {
            ValidationStatus::NotApplicable
        }
    }

    /// Calculate overall compliance score
    fn calculate_overall_compliance_score(&self, validations: &[LicenseValidationResult]) -> f64 {
        if validations.is_empty() {
            return 100.0;
        }

        let mut total_score = 0.0;
        let mut weights = 0.0;

        for validation in validations {
            let weight = match validation.status {
                ValidationStatus::Valid => 1.0,
                ValidationStatus::Uncertain => 0.7,
                ValidationStatus::Invalid => 0.3,
                ValidationStatus::NotApplicable => 0.5,
            };

            total_score += validation.aggregated_confidence * weight;
            weights += weight;
        }

        if weights > 0.0 {
            total_score / weights
        } else {
            0.0
        }
    }

    /// Generate validation summary
    fn generate_validation_summary(&self, validations: &[LicenseValidationResult]) -> ValidationSummary {
        let mut valid = 0;
        let mut invalid = 0;
        let mut uncertain = 0;
        let mut manual_review = 0;

        for validation in validations {
            match validation.status {
                ValidationStatus::Valid => valid += 1,
                ValidationStatus::Invalid => invalid += 1,
                ValidationStatus::Uncertain => {
                    uncertain += 1;
                    if validation.aggregated_confidence < 60.0 {
                        manual_review += 1;
                    }
                }
                ValidationStatus::NotApplicable => {}
            }
        }

        ValidationSummary {
            total_components: validations.len(),
            valid_components: valid,
            invalid_components: invalid,
            uncertain_components: uncertain,
            manual_review_required: manual_review,
        }
    }

    /// Assess validation risk
    fn assess_validation_risk(
        &self,
        validations: &[LicenseValidationResult],
        edge_cases: &EdgeCaseDetectionResult,
    ) -> ValidationRiskAssessment {
        let mut risk_score = 0.0;
        let mut risk_factors = Vec::new();
        let mut mitigation_strategies = Vec::new();
        let mut critical_issues = Vec::new();

        // Risk from validation issues
        let invalid_count = validations.iter().filter(|v| v.status == ValidationStatus::Invalid).count();
        if invalid_count > 0 {
            risk_score += (invalid_count as f64 / validations.len() as f64) * 50.0;
            risk_factors.push(format!("{} components have invalid license validations", invalid_count));
            mitigation_strategies.push("Review and correct invalid license information".to_string());
        }

        // Risk from low confidence validations
        let low_confidence_count = validations.iter().filter(|v| v.aggregated_confidence < 60.0).count();
        if low_confidence_count > 0 {
            risk_score += (low_confidence_count as f64 / validations.len() as f64) * 30.0;
            risk_factors.push(format!("{} components have low confidence validations", low_confidence_count));
            mitigation_strategies.push("Improve license detection accuracy".to_string());
        }

        // Risk from edge cases
        risk_score += edge_cases.overall_risk * 0.3;
        if edge_cases.overall_risk > 50.0 {
            risk_factors.push("High number of license edge cases detected".to_string());
            mitigation_strategies.push("Address detected edge cases".to_string());
        }

        // Critical issues
        for validation in validations {
            if validation.status == ValidationStatus::Invalid && validation.aggregated_confidence < 30.0 {
                critical_issues.push(format!("Critical: {} has invalid license with very low confidence", validation.component));
            }
        }

        ValidationRiskAssessment {
            risk_score: risk_score.min(100.0),
            risk_factors,
            mitigation_strategies,
            critical_issues,
        }
    }

    /// Validate SPDX license format
    fn validate_spdx_format(license: &str) -> Result<ValidationResult> {
        // Basic SPDX identifier validation
        if license == "NOASSERTION" || license == "NONE" {
            return Ok(ValidationResult {
                is_valid: true,
                message: "Valid SPDX special identifier".to_string(),
                recommendations: vec![],
            });
        }

        // Check for valid characters
        for ch in license.chars() {
            if !ch.is_alphanumeric() && ch != '-' && ch != '.' && ch != '+' {
                return Ok(ValidationResult {
                    is_valid: false,
                    message: format!("Invalid character '{}' in SPDX identifier", ch),
                    recommendations: vec!["Use only alphanumeric characters, hyphens, dots, and plus signs".to_string()],
                });
            }
        }

        Ok(ValidationResult {
            is_valid: true,
            message: "Valid SPDX identifier format".to_string(),
            recommendations: vec![],
        })
    }

    /// Validate license expression syntax
    fn validate_expression_syntax(license: &str) -> Result<ValidationResult> {
        match LicenseExpressionParser::parse(license) {
            Ok(_) => Ok(ValidationResult {
                is_valid: true,
                message: "Valid license expression syntax".to_string(),
                recommendations: vec![],
            }),
            Err(e) => Ok(ValidationResult {
                is_valid: false,
                message: format!("Invalid license expression syntax: {}", e),
                recommendations: vec![
                    "Check for proper operator usage (AND, OR, WITH)".to_string(),
                    "Ensure balanced parentheses".to_string(),
                    "Verify SPDX license identifier format".to_string(),
                ],
            }),
        }
    }

    /// Validate confidence threshold
    fn validate_confidence_threshold(evidence: &[ValidationEvidence]) -> Result<ValidationResult> {
        let avg_confidence = if !evidence.is_empty() {
            evidence.iter().map(|e| e.confidence).sum::<f64>() / evidence.len() as f64
        } else {
            0.0
        };

        if avg_confidence >= 60.0 {
            Ok(ValidationResult {
                is_valid: true,
                message: format!("Confidence threshold met: {:.1}%", avg_confidence),
                recommendations: vec![],
            })
        } else {
            Ok(ValidationResult {
                is_valid: false,
                message: format!("Confidence below threshold: {:.1}% (minimum 60%)", avg_confidence),
                recommendations: vec![
                    "Consider manual verification".to_string(),
                    "Use multiple license scanning tools".to_string(),
                    "Review license detection parameters".to_string(),
                ],
            })
        }
    }

    /// Validate source consistency
    fn validate_source_consistency(license: &str, evidence: &[ValidationEvidence]) -> Result<ValidationResult> {
        if evidence.len() < 2 {
            return Ok(ValidationResult {
                is_valid: true,
                message: "Insufficient evidence for consistency check".to_string(),
                recommendations: vec!["Gather evidence from multiple sources".to_string()],
            });
        }

        let mut license_counts = HashMap::new();
        for ev in evidence {
            *license_counts.entry(ev.license.clone()).or_insert(0) += 1;
        }

        let most_common = license_counts.iter().max_by_key(|&(_, count)| count);
        let consistency_ratio = if let Some((_, count)) = most_common {
            *count as f64 / evidence.len() as f64
        } else {
            0.0
        };

        if consistency_ratio >= 0.7 {
            Ok(ValidationResult {
                is_valid: true,
                message: format!("Source consistency: {:.1}%", consistency_ratio * 100.0),
                recommendations: vec![],
            })
        } else {
            Ok(ValidationResult {
                is_valid: false,
                message: format!("Low source consistency: {:.1}%", consistency_ratio * 100.0),
                recommendations: vec![
                    "Review conflicting license detections".to_string(),
                    "Consider manual verification of license information".to_string(),
                ],
            })
        }
    }

    /// Add custom validation rule
    pub fn add_validation_rule(&mut self, rule: ValidationRule) {
        self.validation_rules.push(rule);
    }

    /// Get validation rules
    pub fn get_validation_rules(&self) -> &[ValidationRule] {
        &self.validation_rules
    }
}

/// Validation rule
#[derive(Debug)]
pub struct ValidationRule {
    /// Rule name
    pub name: String,
    /// Rule description
    pub description: String,
    /// Rule type
    pub rule_type: ValidationRuleType,
    /// Rule severity
    pub severity: ValidationSeverity,
    /// Validation function
    pub validator: Box<dyn Fn(&str, &[ValidationEvidence]) -> Result<ValidationResult> + Send + Sync>,
}

/// Validation rule type
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ValidationRuleType {
    /// Format validation
    Format,
    /// Syntax validation
    Syntax,
    /// Confidence validation
    Confidence,
    /// Consistency validation
    Consistency,
    /// Custom validation
    Custom,
}

/// Validation severity
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ValidationSeverity {
    /// Low severity
    Low,
    /// Medium severity
    Medium,
    /// High severity
    High,
    /// Critical severity
    Critical,
}

/// Validation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationResult {
    /// Whether validation passed
    pub is_valid: bool,
    /// Validation message
    pub message: String,
    /// Recommendations for improvement
    pub recommendations: Vec<String>,
}

impl Default for ComplianceValidator {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::scanners::LicenseDetection;

    #[test]
    fn test_spdx_format_validation() {
        let result = ComplianceValidator::validate_spdx_format("MIT").unwrap();
        assert!(result.is_valid);

        let result = ComplianceValidator::validate_spdx_format("INVALID@LICENSE").unwrap();
        assert!(!result.is_valid);
    }

    #[test]
    fn test_expression_syntax_validation() {
        let result = ComplianceValidator::validate_expression_syntax("MIT OR Apache-2.0").unwrap();
        assert!(result.is_valid);

        let result = ComplianceValidator::validate_expression_syntax("MIT AND").unwrap();
        assert!(!result.is_valid);
    }

    #[test]
    fn test_confidence_threshold_validation() {
        let evidence = vec![
            ValidationEvidence {
                source: ValidationSource::LicenseScanner,
                license: "MIT".to_string(),
                confidence: 80.0,
                details: "Test".to_string(),
                timestamp: chrono::Utc::now(),
                metadata: HashMap::new(),
            },
        ];

        let result = ComplianceValidator::validate_confidence_threshold(&evidence).unwrap();
        assert!(result.is_valid);

        let low_evidence = vec![
            ValidationEvidence {
                source: ValidationSource::LicenseScanner,
                license: "MIT".to_string(),
                confidence: 40.0,
                details: "Test".to_string(),
                timestamp: chrono::Utc::now(),
                metadata: HashMap::new(),
            },
        ];

        let result = ComplianceValidator::validate_confidence_threshold(&low_evidence).unwrap();
        assert!(!result.is_valid);
    }

    #[test]
    fn test_aggregated_confidence_calculation() {
        let validator = ComplianceValidator::new();

        let evidence = vec![
            ValidationEvidence {
                source: ValidationSource::PackageMetadata,
                license: "MIT".to_string(),
                confidence: 80.0,
                details: "Test".to_string(),
                timestamp: chrono::Utc::now(),
                metadata: HashMap::new(),
            },
            ValidationEvidence {
                source: ValidationSource::LicenseScanner,
                license: "MIT".to_string(),
                confidence: 70.0,
                details: "Test".to_string(),
                timestamp: chrono::Utc::now(),
                metadata: HashMap::new(),
            },
        ];

        let confidence = validator.calculate_aggregated_confidence(&evidence);
        assert!(confidence > 70.0 && confidence < 85.0);
    }

    #[test]
    fn test_validation_status_determination() {
        let validator = ComplianceValidator::new();

        assert_eq!(validator.determine_validation_status(&[], 85.0), ValidationStatus::Valid);
        assert_eq!(validator.determine_validation_status(&["issue".to_string()], 45.0), ValidationStatus::Invalid);
        assert_eq!(validator.determine_validation_status(&[], 65.0), ValidationStatus::Uncertain);
    }
}