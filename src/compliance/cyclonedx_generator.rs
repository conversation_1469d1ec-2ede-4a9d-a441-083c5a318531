use crate::{config::ComplianceConfig, error::Result, scanners::ScanResult};
use serde::{Deserialize, Serialize};

use tracing::{info, instrument};
use uuid::Uuid;

/// CycloneDX SBOM generator
pub struct CycloneDxGenerator {
    config: ComplianceConfig,
}

/// CycloneDX BOM document
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CycloneDxBom {
    /// BOM format (always "CycloneDX")
    #[serde(rename = "bomFormat")]
    pub bom_format: String,

    /// Spec version
    #[serde(rename = "specVersion")]
    pub spec_version: String,

    /// Serial number (UUID)
    #[serde(rename = "serialNumber")]
    pub serial_number: String,

    /// BOM version
    pub version: u32,

    /// Metadata
    pub metadata: CycloneDxMetadata,

    /// Components
    pub components: Vec<CycloneDxComponent>,

    /// Services
    #[serde(skip_serializing_if = "Vec::is_empty")]
    pub services: Vec<CycloneDxService>,

    /// Dependencies
    #[serde(skip_serializing_if = "Vec::is_empty")]
    pub dependencies: Vec<CycloneDxDependency>,

    /// Vulnerabilities
    #[serde(skip_serializing_if = "Vec::is_empty")]
    pub vulnerabilities: Vec<CycloneDxVulnerability>,
}

/// CycloneDX metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CycloneDxMetadata {
    /// Timestamp
    pub timestamp: chrono::DateTime<chrono::Utc>,

    /// Tools used to create the BOM
    pub tools: Vec<CycloneDxTool>,

    /// Authors
    #[serde(skip_serializing_if = "Vec::is_empty")]
    pub authors: Vec<CycloneDxContact>,

    /// Component (main component being described)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub component: Option<CycloneDxComponent>,

    /// Supplier
    #[serde(skip_serializing_if = "Option::is_none")]
    pub supplier: Option<CycloneDxOrganization>,
}

/// CycloneDX tool information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CycloneDxTool {
    /// Tool vendor
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vendor: Option<String>,

    /// Tool name
    pub name: String,

    /// Tool version
    #[serde(skip_serializing_if = "Option::is_none")]
    pub version: Option<String>,

    /// Tool hashes
    #[serde(skip_serializing_if = "Vec::is_empty")]
    pub hashes: Vec<CycloneDxHash>,
}

/// CycloneDX contact information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CycloneDxContact {
    /// Name
    #[serde(skip_serializing_if = "Option::is_none")]
    pub name: Option<String>,

    /// Email
    #[serde(skip_serializing_if = "Option::is_none")]
    pub email: Option<String>,

    /// Phone
    #[serde(skip_serializing_if = "Option::is_none")]
    pub phone: Option<String>,
}

/// CycloneDX organization
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CycloneDxOrganization {
    /// Organization name
    pub name: String,

    /// URLs
    #[serde(skip_serializing_if = "Vec::is_empty")]
    pub url: Vec<String>,

    /// Contact information
    #[serde(skip_serializing_if = "Vec::is_empty")]
    pub contact: Vec<CycloneDxContact>,
}

/// CycloneDX component
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CycloneDxComponent {
    /// Component type
    #[serde(rename = "type")]
    pub component_type: ComponentType,

    /// MIME type
    #[serde(rename = "mime-type", skip_serializing_if = "Option::is_none")]
    pub mime_type: Option<String>,

    /// BOM reference
    #[serde(rename = "bom-ref")]
    pub bom_ref: String,

    /// Supplier
    #[serde(skip_serializing_if = "Option::is_none")]
    pub supplier: Option<CycloneDxOrganization>,

    /// Author
    #[serde(skip_serializing_if = "Option::is_none")]
    pub author: Option<String>,

    /// Publisher
    #[serde(skip_serializing_if = "Option::is_none")]
    pub publisher: Option<String>,

    /// Group (namespace)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub group: Option<String>,

    /// Name
    pub name: String,

    /// Version
    pub version: String,

    /// Description
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,

    /// Scope
    #[serde(skip_serializing_if = "Option::is_none")]
    pub scope: Option<ComponentScope>,

    /// Hashes
    #[serde(skip_serializing_if = "Vec::is_empty")]
    pub hashes: Vec<CycloneDxHash>,

    /// Licenses
    #[serde(skip_serializing_if = "Vec::is_empty")]
    pub licenses: Vec<CycloneDxLicense>,

    /// Copyright
    #[serde(skip_serializing_if = "Option::is_none")]
    pub copyright: Option<String>,

    /// CPE (Common Platform Enumeration)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub cpe: Option<String>,

    /// PURL (Package URL)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub purl: Option<String>,

    /// External references
    #[serde(rename = "externalReferences", skip_serializing_if = "Vec::is_empty")]
    pub external_references: Vec<CycloneDxExternalReference>,
}

/// Component types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ComponentType {
    Application,
    Framework,
    Library,
    Container,
    #[serde(rename = "operating-system")]
    OperatingSystem,
    Device,
    Firmware,
    File,
}

/// Component scope
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ComponentScope {
    Required,
    Optional,
    Excluded,
}

/// CycloneDX hash
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CycloneDxHash {
    /// Hash algorithm
    #[serde(rename = "alg")]
    pub algorithm: HashAlgorithm,

    /// Hash content
    pub content: String,
}

/// Hash algorithms
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "kebab-case")]
pub enum HashAlgorithm {
    #[serde(rename = "MD5")]
    Md5,
    #[serde(rename = "SHA-1")]
    Sha1,
    #[serde(rename = "SHA-256")]
    Sha256,
    #[serde(rename = "SHA-384")]
    Sha384,
    #[serde(rename = "SHA-512")]
    Sha512,
    #[serde(rename = "SHA3-256")]
    Sha3_256,
    #[serde(rename = "SHA3-384")]
    Sha3_384,
    #[serde(rename = "SHA3-512")]
    Sha3_512,
    #[serde(rename = "BLAKE2b-256")]
    Blake2b256,
    #[serde(rename = "BLAKE2b-384")]
    Blake2b384,
    #[serde(rename = "BLAKE2b-512")]
    Blake2b512,
    #[serde(rename = "BLAKE3")]
    Blake3,
}

/// CycloneDX license
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CycloneDxLicense {
    /// License choice (either id or name)
    #[serde(flatten)]
    pub license: LicenseChoice,

    /// License text
    #[serde(skip_serializing_if = "Option::is_none")]
    pub text: Option<CycloneDxAttachment>,

    /// License URL
    #[serde(skip_serializing_if = "Option::is_none")]
    pub url: Option<String>,
}

/// License choice (SPDX ID or name)
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum LicenseChoice {
    /// SPDX license identifier
    Id { id: String },
    /// License name
    Name { name: String },
}

/// CycloneDX attachment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CycloneDxAttachment {
    /// Content type
    #[serde(rename = "contentType", skip_serializing_if = "Option::is_none")]
    pub content_type: Option<String>,

    /// Encoding
    #[serde(skip_serializing_if = "Option::is_none")]
    pub encoding: Option<String>,

    /// Content
    pub content: String,
}

/// CycloneDX external reference
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CycloneDxExternalReference {
    /// Reference type
    #[serde(rename = "type")]
    pub reference_type: ExternalReferenceType,

    /// URL
    pub url: String,

    /// Comment
    #[serde(skip_serializing_if = "Option::is_none")]
    pub comment: Option<String>,

    /// Hashes
    #[serde(skip_serializing_if = "Vec::is_empty")]
    pub hashes: Vec<CycloneDxHash>,
}

/// External reference types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "kebab-case")]
pub enum ExternalReferenceType {
    Vcs,
    Issue,
    Website,
    Advisories,
    Bom,
    #[serde(rename = "mailing-list")]
    MailingList,
    Social,
    Chat,
    Documentation,
    Support,
    Distribution,
    License,
    #[serde(rename = "build-meta")]
    BuildMeta,
    #[serde(rename = "build-system")]
    BuildSystem,
    Other,
}

/// CycloneDX service
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CycloneDxService {
    /// BOM reference
    #[serde(rename = "bom-ref")]
    pub bom_ref: String,

    /// Provider
    #[serde(skip_serializing_if = "Option::is_none")]
    pub provider: Option<CycloneDxOrganization>,

    /// Group
    #[serde(skip_serializing_if = "Option::is_none")]
    pub group: Option<String>,

    /// Name
    pub name: String,

    /// Version
    #[serde(skip_serializing_if = "Option::is_none")]
    pub version: Option<String>,

    /// Description
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,

    /// Endpoints
    #[serde(skip_serializing_if = "Vec::is_empty")]
    pub endpoints: Vec<String>,

    /// Authenticated
    #[serde(skip_serializing_if = "Option::is_none")]
    pub authenticated: Option<bool>,

    /// Cross-domain
    #[serde(rename = "x-trust-boundary", skip_serializing_if = "Option::is_none")]
    pub x_trust_boundary: Option<bool>,

    /// External references
    #[serde(rename = "externalReferences", skip_serializing_if = "Vec::is_empty")]
    pub external_references: Vec<CycloneDxExternalReference>,
}

/// CycloneDX dependency
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CycloneDxDependency {
    /// Reference to component
    #[serde(rename = "ref")]
    pub reference: String,

    /// Dependencies
    #[serde(rename = "dependsOn", skip_serializing_if = "Vec::is_empty")]
    pub depends_on: Vec<String>,
}

/// CycloneDX vulnerability
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CycloneDxVulnerability {
    /// BOM reference
    #[serde(rename = "bom-ref", skip_serializing_if = "Option::is_none")]
    pub bom_ref: Option<String>,

    /// Vulnerability ID
    pub id: String,

    /// Source
    #[serde(skip_serializing_if = "Option::is_none")]
    pub source: Option<CycloneDxVulnerabilitySource>,

    /// References
    #[serde(skip_serializing_if = "Vec::is_empty")]
    pub references: Vec<CycloneDxVulnerabilityReference>,

    /// Ratings
    #[serde(skip_serializing_if = "Vec::is_empty")]
    pub ratings: Vec<CycloneDxVulnerabilityRating>,

    /// CWEs
    #[serde(skip_serializing_if = "Vec::is_empty")]
    pub cwes: Vec<u32>,

    /// Description
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,

    /// Detail
    #[serde(skip_serializing_if = "Option::is_none")]
    pub detail: Option<String>,

    /// Recommendation
    #[serde(skip_serializing_if = "Option::is_none")]
    pub recommendation: Option<String>,

    /// Affects
    #[serde(skip_serializing_if = "Vec::is_empty")]
    pub affects: Vec<CycloneDxVulnerabilityAffect>,
}

/// Vulnerability source
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CycloneDxVulnerabilitySource {
    /// Source name
    pub name: String,

    /// Source URL
    #[serde(skip_serializing_if = "Option::is_none")]
    pub url: Option<String>,
}

/// Vulnerability reference
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CycloneDxVulnerabilityReference {
    /// Reference ID
    pub id: String,

    /// Source
    pub source: CycloneDxVulnerabilitySource,
}

/// Vulnerability rating
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CycloneDxVulnerabilityRating {
    /// Source
    #[serde(skip_serializing_if = "Option::is_none")]
    pub source: Option<CycloneDxVulnerabilitySource>,

    /// Score
    #[serde(skip_serializing_if = "Option::is_none")]
    pub score: Option<f64>,

    /// Severity
    #[serde(skip_serializing_if = "Option::is_none")]
    pub severity: Option<VulnerabilitySeverity>,

    /// Method
    #[serde(skip_serializing_if = "Option::is_none")]
    pub method: Option<ScoringMethod>,

    /// Vector
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vector: Option<String>,
}

/// Vulnerability severity
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum VulnerabilitySeverity {
    Critical,
    High,
    Medium,
    Low,
    Info,
    None,
    Unknown,
}

/// Scoring method
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "UPPERCASE")]
pub enum ScoringMethod {
    #[serde(rename = "CVSSv2")]
    CvssV2,
    #[serde(rename = "CVSSv3")]
    CvssV3,
    #[serde(rename = "CVSSv31")]
    CvssV31,
    #[serde(rename = "OWASP")]
    Owasp,
    Other,
}

/// Vulnerability affect
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CycloneDxVulnerabilityAffect {
    /// Reference to affected component
    #[serde(rename = "ref")]
    pub reference: String,

    /// Versions
    #[serde(skip_serializing_if = "Vec::is_empty")]
    pub versions: Vec<CycloneDxAffectedVersion>,
}

/// Affected version
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CycloneDxAffectedVersion {
    /// Version
    pub version: String,

    /// Status
    pub status: AffectedStatus,
}

/// Affected status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum AffectedStatus {
    Affected,
    Unaffected,
    Unknown,
}

impl CycloneDxGenerator {
    /// Create new CycloneDX generator
    pub fn new(config: &ComplianceConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }

    /// Generate CycloneDX BOM from scan results
    #[instrument(skip(self, scan_results))]
    pub async fn generate_bom(&self, scan_results: &[ScanResult]) -> Result<String> {
        info!("Generating CycloneDX BOM");

        let bom = self.create_bom(scan_results).await?;
        let json_content = serde_json::to_string_pretty(&bom)?;

        // Save to file
        let output_dir = &self.config.report_output_dir;
        let filename = format!("cyclonedx_bom_{}.json", Uuid::new_v4());
        let file_path = std::path::Path::new(output_dir).join(&filename);

        tokio::fs::create_dir_all(output_dir).await?;
        tokio::fs::write(&file_path, &json_content).await?;

        info!(
            file_path = %file_path.display(),
            components_count = bom.components.len(),
            "CycloneDX BOM generated"
        );

        Ok(file_path.to_string_lossy().to_string())
    }

    /// Create CycloneDX BOM from scan results
    async fn create_bom(&self, scan_results: &[ScanResult]) -> Result<CycloneDxBom> {
        let mut components = Vec::new();
        let dependencies = Vec::new();
        let mut vulnerabilities = Vec::new();

        // Process each scan result
        for scan_result in scan_results {
            // Convert software components
            for component in &scan_result.software_components {
                let cyclone_component = CycloneDxComponent {
                    component_type: self.map_component_type(&component.package_type),
                    mime_type: None,
                    bom_ref: format!("{}@{}", component.name, component.version),
                    supplier: None,
                    author: None,
                    publisher: None,
                    group: None,
                    name: component.name.clone(),
                    version: component.version.clone(),
                    description: component.description.clone(),
                    scope: Some(self.map_component_scope(&component.scope)),
                    hashes: Vec::new(),
                    licenses: component
                        .license
                        .as_ref()
                        .map(|l| {
                            vec![CycloneDxLicense {
                                license: LicenseChoice::Id { id: l.clone() },
                                text: None,
                                url: None,
                            }]
                        })
                        .unwrap_or_default(),
                    copyright: None,
                    cpe: None,
                    purl: self.generate_purl(component),
                    external_references: Vec::new(),
                };
                components.push(cyclone_component);
            }

            // Convert vulnerabilities
            for vuln in &scan_result.vulnerabilities {
                let cyclone_vuln = CycloneDxVulnerability {
                    bom_ref: Some(vuln.cve_id.clone()),
                    id: vuln.cve_id.clone(),
                    source: Some(CycloneDxVulnerabilitySource {
                        name: "NVD".to_string(),
                        url: Some("https://nvd.nist.gov/".to_string()),
                    }),
                    references: Vec::new(),
                    ratings: vuln
                        .cvss_score
                        .map(|score| {
                            vec![CycloneDxVulnerabilityRating {
                                source: None,
                                score: Some(score),
                                severity: Some(self.map_vulnerability_severity(&vuln.severity)),
                                method: Some(ScoringMethod::CvssV31),
                                vector: None,
                            }]
                        })
                        .unwrap_or_default(),
                    cwes: Vec::new(),
                    description: Some(vuln.description.clone()),
                    detail: None,
                    recommendation: vuln
                        .fixed_version
                        .as_ref()
                        .map(|v| format!("Upgrade to version {}", v)),
                    affects: vec![CycloneDxVulnerabilityAffect {
                        reference: format!("{}@unknown", vuln.component),
                        versions: vec![CycloneDxAffectedVersion {
                            version: "unknown".to_string(),
                            status: AffectedStatus::Affected,
                        }],
                    }],
                };
                vulnerabilities.push(cyclone_vuln);
            }
        }

        Ok(CycloneDxBom {
            bom_format: "CycloneDX".to_string(),
            spec_version: "1.4".to_string(),
            serial_number: format!("urn:uuid:{}", Uuid::new_v4()),
            version: 1,
            metadata: CycloneDxMetadata {
                timestamp: chrono::Utc::now(),
                tools: vec![CycloneDxTool {
                    vendor: Some("Infinitium Signal".to_string()),
                    name: "infinitum-signal".to_string(),
                    version: Some(env!("CARGO_PKG_VERSION").to_string()),
                    hashes: Vec::new(),
                }],
                authors: vec![CycloneDxContact {
                    name: Some("Infinitium Signal".to_string()),
                    email: Some("<EMAIL>".to_string()),
                    phone: None,
                }],
                component: None,
                supplier: None,
            },
            components,
            services: Vec::new(),
            dependencies,
            vulnerabilities,
        })
    }

    /// Map package type to CycloneDX component type
    fn map_component_type(&self, package_type: &str) -> ComponentType {
        match package_type.to_lowercase().as_str() {
            "application" => ComponentType::Application,
            "framework" => ComponentType::Framework,
            "library" | "crate" | "npm" | "pypi" => ComponentType::Library,
            "container" => ComponentType::Container,
            "firmware" => ComponentType::Firmware,
            _ => ComponentType::Library,
        }
    }

    /// Map component scope
    fn map_component_scope(&self, scope: &crate::scanners::ComponentScope) -> ComponentScope {
        match scope {
            crate::scanners::ComponentScope::Runtime => ComponentScope::Required,
            crate::scanners::ComponentScope::Development => ComponentScope::Optional,
            crate::scanners::ComponentScope::Test => ComponentScope::Optional,
            crate::scanners::ComponentScope::Build => ComponentScope::Optional,
            crate::scanners::ComponentScope::Optional => ComponentScope::Optional,
            crate::scanners::ComponentScope::Unknown => ComponentScope::Required,
        }
    }

    /// Generate Package URL (PURL)
    fn generate_purl(&self, component: &crate::scanners::SoftwareComponent) -> Option<String> {
        match component.package_manager.as_str() {
            "cargo" => Some(format!(
                "pkg:cargo/{}@{}",
                component.name, component.version
            )),
            "npm" => Some(format!("pkg:npm/{}@{}", component.name, component.version)),
            "pip" => Some(format!("pkg:pypi/{}@{}", component.name, component.version)),
            _ => None,
        }
    }

    /// Map vulnerability severity
    fn map_vulnerability_severity(&self, severity: &str) -> VulnerabilitySeverity {
        match severity.to_lowercase().as_str() {
            "critical" => VulnerabilitySeverity::Critical,
            "high" => VulnerabilitySeverity::High,
            "medium" => VulnerabilitySeverity::Medium,
            "low" => VulnerabilitySeverity::Low,
            "info" => VulnerabilitySeverity::Info,
            _ => VulnerabilitySeverity::Unknown,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_component_type_serialization() {
        let comp_type = ComponentType::Library;
        let serialized = serde_json::to_string(&comp_type).unwrap();
        assert_eq!(serialized, "\"library\"");
    }

    #[test]
    fn test_hash_algorithm_serialization() {
        let hash_alg = HashAlgorithm::Sha256;
        let serialized = serde_json::to_string(&hash_alg).unwrap();
        assert_eq!(serialized, "\"SHA-256\"");
    }

    #[test]
    fn test_vulnerability_severity() {
        assert_eq!(
            VulnerabilitySeverity::Critical,
            VulnerabilitySeverity::Critical
        );
        assert_ne!(VulnerabilitySeverity::Critical, VulnerabilitySeverity::High);
    }
}
