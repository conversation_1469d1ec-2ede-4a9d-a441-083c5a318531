//! # Docker Integration
//!
//! Containerized license scanning with Docker image integration,
//! multi-stage build support, and volume mounting for CI/CD pipelines.

use crate::{
    compliance::{
        ci_cd_scanner::{CIDCScanner, CICDPlaformConfig, CICDPlaformRequest, CICDPlaformResult, CIOutputFormat, ScanMode},
        ComplianceConfig,
    },
    config::ScanningConfig,
    error::{InfinitumError, Result},
};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    path::{Path, PathBuf},
    sync::Arc,
};
use tokio::sync::RwLock;
use tracing::{debug, info, instrument, warn};

/// Docker image configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DockerImageConfig {
    /// Base image
    pub base_image: String,
    /// Image tag
    pub tag: String,
    /// Image labels
    pub labels: HashMap<String, String>,
    /// Environment variables
    pub env: HashMap<String, String>,
    /// Working directory
    pub workdir: String,
    /// Exposed ports
    pub exposed_ports: Vec<u16>,
    /// Volumes
    pub volumes: Vec<String>,
}

/// Docker build configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DockerBuildConfig {
    /// Build context
    pub context: String,
    /// Dockerfile path
    pub dockerfile: String,
    /// Build arguments
    pub build_args: HashMap<String, String>,
    /// Target stage for multi-stage builds
    pub target: Option<String>,
    /// Build platforms
    pub platforms: Vec<String>,
    /// Cache configuration
    pub cache_from: Vec<String>,
}

/// Docker run configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DockerRunConfig {
    /// Container name
    pub name: Option<String>,
    /// Hostname
    pub hostname: Option<String>,
    /// Network mode
    pub network: Option<String>,
    /// Restart policy
    pub restart: Option<String>,
    /// Memory limit
    pub memory: Option<String>,
    /// CPU limit
    pub cpu: Option<String>,
    /// User
    pub user: Option<String>,
    /// Working directory
    pub workdir: Option<String>,
    /// Environment variables
    pub env: HashMap<String, String>,
    /// Volume mounts
    pub volumes: Vec<DockerVolumeMount>,
    /// Port mappings
    pub ports: Vec<DockerPortMapping>,
}

/// Docker volume mount
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DockerVolumeMount {
    /// Host path
    pub host_path: String,
    /// Container path
    pub container_path: String,
    /// Mount options
    pub options: Option<String>,
}

/// Docker port mapping
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DockerPortMapping {
    /// Host port
    pub host_port: u16,
    /// Container port
    pub container_port: u16,
    /// Protocol
    pub protocol: Option<String>,
}

/// Docker integration configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DockerIntegrationConfig {
    /// Base CI/CD configuration
    pub base_config: CICDPlaformConfig,
    /// Docker image configuration
    pub image_config: DockerImageConfig,
    /// Docker build configuration
    pub build_config: DockerBuildConfig,
    /// Docker run configuration
    pub run_config: DockerRunConfig,
    /// Enable multi-stage builds
    pub enable_multi_stage: bool,
    /// Enable volume caching
    pub enable_volume_cache: bool,
    /// Cache volume name
    pub cache_volume: String,
    /// Enable bind mounts
    pub enable_bind_mounts: bool,
    /// Custom Dockerfiles
    pub custom_dockerfiles: Vec<DockerfileTemplate>,
}

/// Dockerfile template
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DockerfileTemplate {
    /// Template name
    pub name: String,
    /// Template description
    pub description: String,
    /// Template content
    pub content: String,
    /// Applicable stages
    pub stages: Vec<String>,
    /// Required build arguments
    pub build_args: HashMap<String, String>,
}

/// Docker integration service
pub struct DockerIntegration {
    scanner: Arc<RwLock<CIDCScanner>>,
    config: DockerIntegrationConfig,
    dockerfile_templates: HashMap<String, DockerfileTemplate>,
}

impl DockerIntegration {
    /// Create new Docker integration
    pub fn new(
        compliance_config: ComplianceConfig,
        scanning_config: ScanningConfig,
        config: DockerIntegrationConfig,
    ) -> Self {
        let scanner = Arc::new(RwLock::new(CIDCScanner::new(
            compliance_config,
            scanning_config,
        )));

        let mut dockerfile_templates = HashMap::new();

        // Initialize default Dockerfile templates
        dockerfile_templates.insert(
            "license-scanner".to_string(),
            Self::create_license_scanner_dockerfile(),
        );
        dockerfile_templates.insert(
            "multi-stage".to_string(),
            Self::create_multi_stage_dockerfile(),
        );
        dockerfile_templates.insert(
            "ci-integration".to_string(),
            Self::create_ci_integration_dockerfile(),
        );

        // Add custom templates
        for template in &config.custom_dockerfiles {
            dockerfile_templates.insert(template.name.clone(), template.clone());
        }

        Self {
            scanner,
            config,
            dockerfile_templates,
        }
    }

    /// Execute Docker-based scan
    #[instrument(skip(self), fields(image = %self.config.image_config.base_image))]
    pub async fn execute_scan(&self, request: DockerScanRequest) -> Result<DockerScanResult> {
        info!("Executing Docker-based license scan");

        // Build Docker command
        let docker_command = self.build_docker_command(&request)?;

        // Execute scan in Docker container
        let scan_result = self.execute_docker_scan(docker_command, &request).await?;

        // Convert to Docker-specific result
        let docker_result = self.convert_to_docker_result(scan_result).await?;

        Ok(docker_result)
    }

    /// Generate Dockerfile
    pub fn generate_dockerfile(&self, template_name: &str, custom_config: Option<HashMap<String, String>>) -> Result<String> {
        let template = self.dockerfile_templates.get(template_name)
            .ok_or_else(|| InfinitumError::InvalidInput {
                field: "template_name".to_string(),
                message: format!("Dockerfile template '{}' not found", template_name),
            })?;

        let mut content = template.content.clone();

        // Replace placeholders with configuration values
        content = content.replace("{{BASE_IMAGE}}", &self.config.image_config.base_image);
        content = content.replace("{{TAG}}", &self.config.image_config.tag);
        content = content.replace("{{WORKDIR}}", &self.config.image_config.workdir);

        // Apply custom configuration
        if let Some(custom) = custom_config {
            for (key, value) in custom {
                let placeholder = format!("{{{{{}}}}}", key.to_uppercase());
                content = content.replace(&placeholder, &value);
            }
        }

        Ok(content)
    }

    /// Generate Docker Compose file
    pub fn generate_docker_compose(&self, services: Vec<DockerService>) -> Result<String> {
        let mut compose = String::new();
        compose.push_str("version: '3.8'\n\n");
        compose.push_str("services:\n");

        for service in services {
            compose.push_str(&format!("  {}:\n", service.name));
            compose.push_str(&format!("    image: {}\n", service.image));

            if let Some(build) = &service.build {
                compose.push_str("    build:\n");
                compose.push_str(&format!("      context: {}\n", build.context));
                if let Some(dockerfile) = &build.dockerfile {
                    compose.push_str(&format!("      dockerfile: {}\n", dockerfile));
                }
            }

            if !service.environment.is_empty() {
                compose.push_str("    environment:\n");
                for (key, value) in &service.environment {
                    compose.push_str(&format!("      {}: {}\n", key, value));
                }
            }

            if !service.volumes.is_empty() {
                compose.push_str("    volumes:\n");
                for volume in &service.volumes {
                    compose.push_str(&format!("      - {}\n", volume));
                }
            }

            if !service.ports.is_empty() {
                compose.push_str("    ports:\n");
                for port in &service.ports {
                    compose.push_str(&format!("      - \"{}\"\n", port));
                }
            }

            compose.push_str("\n");
        }

        Ok(compose)
    }

    /// Get available Dockerfile templates
    pub fn get_dockerfile_templates(&self) -> Vec<&DockerfileTemplate> {
        self.dockerfile_templates.values().collect()
    }

    /// Validate Docker configuration
    pub fn validate_config(&self) -> Result<Vec<String>> {
        let mut issues = Vec::new();

        if self.config.image_config.base_image.is_empty() {
            issues.push("Base image is required".to_string());
        }

        if self.config.image_config.workdir.is_empty() {
            issues.push("Working directory is required".to_string());
        }

        if self.config.build_config.context.is_empty() {
            issues.push("Build context is required".to_string());
        }

        if self.config.base_config.min_compliance_score < 0.0 || self.config.base_config.min_compliance_score > 100.0 {
            issues.push("Minimum compliance score must be between 0 and 100".to_string());
        }

        Ok(issues)
    }

    /// Build Docker command for scan execution
    fn build_docker_command(&self, request: &DockerScanRequest) -> Result<String> {
        let mut command = String::new();

        // Docker run command
        command.push_str("docker run --rm");

        // Add container name if specified
        if let Some(name) = &self.config.run_config.name {
            command.push_str(&format!(" --name {}", name));
        }

        // Add environment variables
        for (key, value) in &self.config.run_config.env {
            command.push_str(&format!(" -e {}=\"{}\"", key, value));
        }

        // Add volume mounts
        for volume in &self.config.run_config.volumes {
            let options = volume.options.as_deref().unwrap_or("rw");
            command.push_str(&format!(" -v {}:{}:{}", volume.host_path, volume.container_path, options));
        }

        // Add working directory
        if let Some(workdir) = &self.config.run_config.workdir {
            command.push_str(&format!(" -w {}", workdir));
        }

        // Add user
        if let Some(user) = &self.config.run_config.user {
            command.push_str(&format!(" -u {}", user));
        }

        // Add memory limit
        if let Some(memory) = &self.config.run_config.memory {
            command.push_str(&format!(" -m {}", memory));
        }

        // Add CPU limit
        if let Some(cpu) = &self.config.run_config.cpu {
            command.push_str(&format!(" --cpus {}", cpu));
        }

        // Add image
        command.push_str(&format!(" {}:{}", self.config.image_config.base_image, self.config.image_config.tag));

        // Add scan command
        command.push_str(&format!(" infinitium-signal ci-scan --platform docker --output-format {} --scan-mode {} --fail-on-violations {} --min-compliance-score {} --enable-artifacts true",
            match self.config.base_config.output_format {
                CIOutputFormat::Json => "json",
                CIOutputFormat::JUnit => "junit",
                CIOutputFormat::Sarif => "sarif",
                CIOutputFormat::GitHubAnnotations => "github-annotations",
                CIOutputFormat::GitLabReports => "gitlab-reports",
                CIOutputFormat::JenkinsTestResults => "jenkins-test-results",
            },
            match request.scan_mode {
                ScanMode::Full => "full",
                ScanMode::Incremental => "incremental",
                ScanMode::Quick => "quick",
                ScanMode::Custom => "custom",
            },
            self.config.base_config.fail_on_violations,
            self.config.base_config.min_compliance_score
        ));

        // Add target path
        command.push_str(&format!(" --target {}", request.target_path));

        Ok(command)
    }

    /// Execute scan in Docker container
    async fn execute_docker_scan(&self, docker_command: String, request: &DockerScanRequest) -> Result<CICDPlaformResult> {
        info!("Executing Docker command: {}", docker_command);

        // In a real implementation, this would execute the Docker command
        // For now, we'll simulate the execution by calling the scanner directly
        let base_request = self.convert_to_base_request(request)?;

        let scanner = self.scanner.read().await;
        let result = scanner.execute_scan(base_request).await?;

        debug!("Docker scan completed with exit code: {}", result.exit_code);

        Ok(result)
    }

    /// Convert Docker request to base CI/CD request
    fn convert_to_base_request(&self, request: &DockerScanRequest) -> Result<CICDPlaformRequest> {
        let mut base_config = self.config.base_config.clone();

        // Set up environment variables for Docker
        let mut env_vars = base_config.environment_variables.clone();
        env_vars.insert("DOCKER_CONTAINER".to_string(), "true".to_string());
        env_vars.insert("SCAN_MODE".to_string(), format!("{:?}", request.scan_mode).to_lowercase());

        base_config.environment_variables = env_vars;

        Ok(CICDPlaformRequest {
            id: request.id,
            config: base_config,
            target: request.target_path.clone(),
            changed_files: request.changed_files.clone(),
            pr_info: None,
            build_info: Some(crate::compliance::ci_cd_scanner::BuildInfo {
                build_number: "docker-build".to_string(),
                build_url: None,
                job_name: Some("docker-license-scan".to_string()),
                pipeline_name: Some("docker-pipeline".to_string()),
            }),
            metadata: request.metadata.clone(),
        })
    }

    /// Convert base result to Docker result
    async fn convert_to_docker_result(&self, base_result: CICDPlaformResult) -> Result<DockerScanResult> {
        let container_logs = format!("License scan completed with exit code: {}", base_result.exit_code);
        let container_stats = DockerContainerStats {
            execution_time_ms: base_result.duration.unwrap_or_default().as_millis() as u64,
            memory_usage_mb: 256, // Simulated
            cpu_usage_percent: 45.0, // Simulated
            exit_code: base_result.exit_code,
        };

        Ok(DockerScanResult {
            base_result,
            container_logs,
            container_stats,
            image_info: DockerImageInfo {
                name: format!("{}:{}", self.config.image_config.base_image, self.config.image_config.tag),
                size_mb: 512, // Simulated
                layers: 15, // Simulated
                created: chrono::Utc::now(),
            },
            volumes_mounted: self.config.run_config.volumes.clone(),
        })
    }

    /// Create license scanner Dockerfile template
    fn create_license_scanner_dockerfile() -> DockerfileTemplate {
        let content = r#"# License Scanner Docker Image
FROM {{BASE_IMAGE}}:{{TAG}}

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    build-essential \
    pkg-config \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR {{WORKDIR}}

# Copy source code
COPY . .

# Build the application
RUN cargo build --release

# Create non-root user
RUN useradd -r -s /bin/false scanner

# Set permissions
RUN chown -R scanner:scanner {{WORKDIR}}

# Switch to non-root user
USER scanner

# Set environment variables
ENV RUST_LOG=info
ENV SCAN_MODE=full

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD cargo run --release --bin infinitium-signal -- --version || exit 1

# Default command
CMD ["cargo", "run", "--release", "--bin", "infinitium-signal", "--", "ci-scan"]

# Labels
LABEL maintainer="infinitum-signal"
LABEL version="1.0"
LABEL description="License compliance scanning container"
"#.to_string();

        let mut build_args = HashMap::new();
        build_args.insert("BASE_IMAGE".to_string(), "rust".to_string());
        build_args.insert("TAG".to_string(), "1.80".to_string());
        build_args.insert("WORKDIR".to_string(), "/app".to_string());

        DockerfileTemplate {
            name: "license-scanner".to_string(),
            description: "Standalone license scanner container".to_string(),
            content,
            stages: vec!["build".to_string(), "runtime".to_string()],
            build_args,
        }
    }

    /// Create multi-stage Dockerfile template
    fn create_multi_stage_dockerfile() -> DockerfileTemplate {
        let content = r#"# Multi-stage License Scanner Build

# Build stage
FROM {{BASE_IMAGE}}:{{TAG}} AS builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    build-essential \
    pkg-config \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR {{WORKDIR}}

# Copy dependency files first for better caching
COPY Cargo.toml Cargo.lock ./

# Create dummy main.rs for dependency caching
RUN mkdir src && echo "fn main() {}" > src/main.rs

# Build dependencies
RUN cargo build --release && rm -rf src target/release/deps/infitium*

# Copy source code
COPY src ./src

# Build the application
RUN cargo build --release

# Runtime stage
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd -r -s /bin/false scanner

# Copy binary from builder
COPY --from=builder {{WORKDIR}}/target/release/infitium-signal /usr/local/bin/

# Set working directory
WORKDIR /scan

# Set permissions
RUN chown -R scanner:scanner /scan

# Switch to non-root user
USER scanner

# Set environment variables
ENV RUST_LOG=info
ENV SCAN_MODE=full

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD infinitium-signal --version || exit 1

# Default command
CMD ["infinitium-signal", "ci-scan"]

# Labels
LABEL maintainer="infinitum-signal"
LABEL version="1.0"
LABEL description="Multi-stage license compliance scanning container"
"#.to_string();

        let mut build_args = HashMap::new();
        build_args.insert("BASE_IMAGE".to_string(), "rust".to_string());
        build_args.insert("TAG".to_string(), "1.80".to_string());
        build_args.insert("WORKDIR".to_string(), "/app".to_string());

        DockerfileTemplate {
            name: "multi-stage".to_string(),
            description: "Multi-stage build for optimized container size".to_string(),
            content,
            stages: vec!["builder".to_string(), "runtime".to_string()],
            build_args,
        }
    }

    /// Create CI integration Dockerfile template
    fn create_ci_integration_dockerfile() -> DockerfileTemplate {
        let content = r#"# CI Integration License Scanner

FROM {{BASE_IMAGE}}:{{TAG}}

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    jq \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# Install additional tools for CI integration
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs

# Set working directory
WORKDIR {{WORKDIR}}

# Copy pre-built binary (assumes binary is built separately)
COPY infinitium-signal /usr/local/bin/
RUN chmod +x /usr/local/bin/infitium-signal

# Copy CI integration scripts
COPY scripts/ci-integration/ ./scripts/ci-integration/

# Create workspace directory
RUN mkdir -p /workspace && chmod 777 /workspace

# Create non-root user
RUN useradd -r -s /bin/bash scanner && \
    usermod -aG scanner scanner

# Set permissions
RUN chown -R scanner:scanner {{WORKDIR}} /workspace

# Switch to non-root user
USER scanner

# Set environment variables
ENV RUST_LOG=info
ENV SCAN_MODE=incremental
ENV CI=true
ENV DOCKER_CONTAINER=true

# Working directory for scans
WORKDIR /workspace

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD infinitium-signal --version || exit 1

# Default command
CMD ["infinitium-signal", "ci-scan", "--platform", "docker", "--output-format", "json"]

# Labels
LABEL maintainer="infinitum-signal"
LABEL version="1.0"
LABEL description="CI/CD integration license scanning container"
LABEL ci.integration="true"
"#.to_string();

        let mut build_args = HashMap::new();
        build_args.insert("BASE_IMAGE".to_string(), "debian".to_string());
        build_args.insert("TAG".to_string(), "bookworm-slim".to_string());
        build_args.insert("WORKDIR".to_string(), "/app".to_string());

        DockerfileTemplate {
            name: "ci-integration".to_string(),
            description: "CI/CD optimized container with integration scripts".to_string(),
            content,
            stages: vec!["runtime".to_string()],
            build_args,
        }
    }
}

/// Docker scan request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DockerScanRequest {
    /// Request ID
    pub id: uuid::Uuid,
    /// Target path to scan
    pub target_path: String,
    /// Scan mode
    pub scan_mode: ScanMode,
    /// Changed files (for incremental scans)
    pub changed_files: Option<Vec<String>>,
    /// Additional metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Docker scan result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DockerScanResult {
    /// Base CI/CD result
    pub base_result: CICDPlaformResult,
    /// Container logs
    pub container_logs: String,
    /// Container execution statistics
    pub container_stats: DockerContainerStats,
    /// Docker image information
    pub image_info: DockerImageInfo,
    /// Volumes mounted during scan
    pub volumes_mounted: Vec<DockerVolumeMount>,
}

/// Docker container statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DockerContainerStats {
    /// Execution time in milliseconds
    pub execution_time_ms: u64,
    /// Memory usage in MB
    pub memory_usage_mb: u64,
    /// CPU usage percentage
    pub cpu_usage_percent: f64,
    /// Container exit code
    pub exit_code: i32,
}

/// Docker image information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DockerImageInfo {
    /// Image name with tag
    pub name: String,
    /// Image size in MB
    pub size_mb: u64,
    /// Number of layers
    pub layers: u32,
    /// Creation timestamp
    pub created: chrono::DateTime<chrono::Utc>,
}

/// Docker service for Docker Compose
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DockerService {
    /// Service name
    pub name: String,
    /// Docker image
    pub image: String,
    /// Build configuration
    pub build: Option<DockerBuild>,
    /// Environment variables
    pub environment: HashMap<String, String>,
    /// Volume mounts
    pub volumes: Vec<String>,
    /// Port mappings
    pub ports: Vec<String>,
}

/// Docker build configuration for Compose
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DockerBuild {
    /// Build context
    pub context: String,
    /// Dockerfile path
    pub dockerfile: Option<String>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_docker_config_validation() {
        let config = DockerIntegrationConfig {
            base_config: CICDPlaformConfig::default(),
            image_config: DockerImageConfig {
                base_image: "rust".to_string(),
                tag: "1.80".to_string(),
                labels: HashMap::new(),
                env: HashMap::new(),
                workdir: "/app".to_string(),
                exposed_ports: vec![],
                volumes: vec![],
            },
            build_config: DockerBuildConfig {
                context: ".".to_string(),
                dockerfile: "Dockerfile".to_string(),
                build_args: HashMap::new(),
                target: None,
                platforms: vec![],
                cache_from: vec![],
            },
            run_config: DockerRunConfig {
                name: None,
                hostname: None,
                network: None,
                restart: None,
                memory: None,
                cpu: None,
                user: None,
                workdir: Some("/app".to_string()),
                env: HashMap::new(),
                volumes: vec![],
                ports: vec![],
            },
            enable_multi_stage: true,
            enable_volume_cache: true,
            cache_volume: "license-scan-cache".to_string(),
            enable_bind_mounts: true,
            custom_dockerfiles: vec![],
        };

        let integration = DockerIntegration::new(
            ComplianceConfig::default(),
            ScanningConfig::default(),
            config,
        );

        let issues = integration.validate_config().unwrap();
        assert!(issues.is_empty());
    }

    #[test]
    fn test_dockerfile_generation() {
        let config = DockerIntegrationConfig {
            base_config: CICDPlaformConfig::default(),
            image_config: DockerImageConfig {
                base_image: "rust".to_string(),
                tag: "1.80".to_string(),
                labels: HashMap::new(),
                env: HashMap::new(),
                workdir: "/app".to_string(),
                exposed_ports: vec![],
                volumes: vec![],
            },
            build_config: DockerBuildConfig {
                context: ".".to_string(),
                dockerfile: "Dockerfile".to_string(),
                build_args: HashMap::new(),
                target: None,
                platforms: vec![],
                cache_from: vec![],
            },
            run_config: DockerRunConfig {
                name: None,
                hostname: None,
                network: None,
                restart: None,
                memory: None,
                cpu: None,
                user: None,
                workdir: Some("/app".to_string()),
                env: HashMap::new(),
                volumes: vec![],
                ports: vec![],
            },
            enable_multi_stage: true,
            enable_volume_cache: true,
            cache_volume: "license-scan-cache".to_string(),
            enable_bind_mounts: true,
            custom_dockerfiles: vec![],
        };

        let integration = DockerIntegration::new(
            ComplianceConfig::default(),
            ScanningConfig::default(),
            config,
        );

        let dockerfile = integration.generate_dockerfile("license-scanner", None).unwrap();
        assert!(dockerfile.contains("FROM rust:1.80"));
        assert!(dockerfile.contains("WORKDIR /app"));
    }

    #[test]
    fn test_docker_compose_generation() {
        let integration = DockerIntegration::new(
            ComplianceConfig::default(),
            ScanningConfig::default(),
            DockerIntegrationConfig {
                base_config: CICDPlaformConfig::default(),
                image_config: DockerImageConfig {
                    base_image: "rust".to_string(),
                    tag: "1.80".to_string(),
                    labels: HashMap::new(),
                    env: HashMap::new(),
                    workdir: "/app".to_string(),
                    exposed_ports: vec![],
                    volumes: vec![],
                },
                build_config: DockerBuildConfig {
                    context: ".".to_string(),
                    dockerfile: "Dockerfile".to_string(),
                    build_args: HashMap::new(),
                    target: None,
                    platforms: vec![],
                    cache_from: vec![],
                },
                run_config: DockerRunConfig {
                    name: None,
                    hostname: None,
                    network: None,
                    restart: None,
                    memory: None,
                    cpu: None,
                    user: None,
                    workdir: Some("/app".to_string()),
                    env: HashMap::new(),
                    volumes: vec![],
                    ports: vec![],
                },
                enable_multi_stage: true,
                enable_volume_cache: true,
                cache_volume: "license-scan-cache".to_string(),
                enable_bind_mounts: true,
                custom_dockerfiles: vec![],
            },
        );

        let services = vec![
            DockerService {
                name: "license-scanner".to_string(),
                image: "infinitum-signal:latest".to_string(),
                build: Some(DockerBuild {
                    context: ".".to_string(),
                    dockerfile: Some("Dockerfile".to_string()),
                }),
                environment: HashMap::from([
                    ("RUST_LOG".to_string(), "info".to_string()),
                ]),
                volumes: vec!["./src:/app/src".to_string()],
                ports: vec!["8080:8080".to_string()],
            },
        ];

        let compose = integration.generate_docker_compose(services).unwrap();
        assert!(compose.contains("version: '3.8'"));
        assert!(compose.contains("license-scanner:"));
        assert!(compose.contains("image: infinitum-signal:latest"));
    }
}