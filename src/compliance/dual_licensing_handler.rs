//! # Dual Licensing Handler
//!
//! Specialized handling for dual and multi-licensing scenarios with SPDX expression parsing,
//! license combination analysis, and attribution requirement aggregation.

use crate::{
    compliance::{
        license_conflict_resolver::{LicenseConflictResolver, ResolutionStrategy},
        license_expression_parser::{LicenseExpressionParser, LicenseNode},
    },
    error::{InfinitumError, Result},
};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use tracing::{debug, info, instrument};

/// License combination type
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum LicenseCombination {
    /// Dual licensing (OR combination)
    Dual,
    /// Multi-licensing (complex OR combinations)
    Multi,
    /// Conjunctive licensing (AND combination)
    Conjunctive,
    /// Exception-based licensing (WITH operator)
    WithException,
}

/// Attribution requirement
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AttributionRequirement {
    /// License identifier
    pub license: String,
    /// Attribution text required
    pub attribution_text: String,
    /// Whether attribution is mandatory
    pub mandatory: bool,
    /// Attribution format (copyright notice, license text, etc.)
    pub format: AttributionFormat,
}

/// Attribution format types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AttributionFormat {
    /// Full license text
    FullText,
    /// Short license identifier
    ShortIdentifier,
    /// Copyright notice only
    CopyrightNotice,
    /// SPDX license identifier
    SpdxIdentifier,
}

/// License combination analysis result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CombinationAnalysis {
    /// Combination type detected
    pub combination_type: LicenseCombination,
    /// Individual licenses in the combination
    pub licenses: Vec<String>,
    /// Compatibility assessment
    pub compatibility: CompatibilityAssessment,
    /// Attribution requirements
    pub attribution_requirements: Vec<AttributionRequirement>,
    /// Risk assessment
    pub risk_assessment: RiskAssessment,
}

/// Compatibility assessment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompatibilityAssessment {
    /// Overall compatibility score (0-100)
    pub overall_score: f64,
    /// Compatible license pairs
    pub compatible_pairs: Vec<(String, String)>,
    /// Incompatible license pairs
    pub incompatible_pairs: Vec<(String, String)>,
    /// Conditional compatibilities
    pub conditional_pairs: Vec<(String, String)>,
}

/// Risk assessment for dual licensing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAssessment {
    /// Risk score (0-100)
    pub risk_score: f64,
    /// Risk factors
    pub risk_factors: Vec<String>,
    /// Mitigation strategies
    pub mitigation_strategies: Vec<String>,
}

/// Dual licensing handler
pub struct DualLicensingHandler {
    conflict_resolver: LicenseConflictResolver,
    attribution_templates: HashMap<String, AttributionRequirement>,
}

impl DualLicensingHandler {
    /// Create new dual licensing handler
    pub fn new() -> Self {
        let mut handler = Self {
            conflict_resolver: LicenseConflictResolver::new(),
            attribution_templates: HashMap::new(),
        };
        handler.initialize_attribution_templates();
        handler
    }

    /// Initialize attribution templates for common licenses
    fn initialize_attribution_templates(&mut self) {
        // MIT License
        self.attribution_templates.insert(
            "MIT".to_string(),
            AttributionRequirement {
                license: "MIT".to_string(),
                attribution_text: "This software includes components licensed under the MIT License.".to_string(),
                mandatory: true,
                format: AttributionFormat::CopyrightNotice,
            },
        );

        // Apache-2.0
        self.attribution_templates.insert(
            "Apache-2.0".to_string(),
            AttributionRequirement {
                license: "Apache-2.0".to_string(),
                attribution_text: "This software includes components licensed under the Apache License 2.0.".to_string(),
                mandatory: true,
                format: AttributionFormat::CopyrightNotice,
            },
        );

        // BSD-3-Clause
        self.attribution_templates.insert(
            "BSD-3-Clause".to_string(),
            AttributionRequirement {
                license: "BSD-3-Clause".to_string(),
                attribution_text: "This software includes components licensed under the BSD 3-Clause License.".to_string(),
                mandatory: true,
                format: AttributionFormat::CopyrightNotice,
            },
        );

        // GPL-3.0
        self.attribution_templates.insert(
            "GPL-3.0".to_string(),
            AttributionRequirement {
                license: "GPL-3.0".to_string(),
                attribution_text: "This software includes components licensed under the GNU General Public License v3.0.".to_string(),
                mandatory: true,
                format: AttributionFormat::FullText,
            },
        );
    }

    /// Analyze license combination expression
    #[instrument(skip(expression))]
    pub fn analyze_combination(&self, expression: &str) -> Result<CombinationAnalysis> {
        info!("Analyzing license combination: {}", expression);

        // Parse the expression
        let parsed = LicenseExpressionParser::parse(expression)?;
        debug!("Parsed expression successfully");

        // Extract licenses
        let licenses = LicenseExpressionParser::extract_licenses(&parsed);
        let licenses_vec: Vec<String> = licenses.into_iter().collect();

        // Determine combination type
        let combination_type = self.determine_combination_type(&parsed);

        // Analyze compatibility
        let compatibility = self.analyze_compatibility(&licenses_vec)?;

        // Generate attribution requirements
        let attribution_requirements = self.generate_attribution_requirements(&licenses_vec)?;

        // Assess risk
        let risk_assessment = self.assess_risk(&parsed, &compatibility)?;

        Ok(CombinationAnalysis {
            combination_type,
            licenses: licenses_vec,
            compatibility,
            attribution_requirements,
            risk_assessment,
        })
    }

    /// Determine the type of license combination
    fn determine_combination_type(&self, node: &LicenseNode) -> LicenseCombination {
        match node {
            LicenseNode::License(_) => LicenseCombination::Dual, // Single license, but could be part of dual
            LicenseNode::Or(left, right) => {
                // Check if this is a simple dual license or complex multi-license
                if self.is_simple_dual(left) && self.is_simple_dual(right) {
                    LicenseCombination::Dual
                } else {
                    LicenseCombination::Multi
                }
            }
            LicenseNode::And(_, _) => LicenseCombination::Conjunctive,
            LicenseNode::With(_, _) => LicenseCombination::WithException,
        }
    }

    /// Check if node is a simple license (not a complex expression)
    fn is_simple_dual(&self, node: &LicenseNode) -> bool {
        matches!(node, LicenseNode::License(_))
    }

    /// Analyze compatibility between licenses in the combination
    fn analyze_compatibility(&self, licenses: &[String]) -> Result<CompatibilityAssessment> {
        let mut compatible_pairs = Vec::new();
        let mut incompatible_pairs = Vec::new();
        let mut conditional_pairs = Vec::new();
        let mut total_score = 0.0;
        let mut pair_count = 0;

        for i in 0..licenses.len() {
            for j in (i + 1)..licenses.len() {
                let license_a = &licenses[i];
                let license_b = &licenses[j];
                pair_count += 1;

                let compatibility = self.conflict_resolver.get_compatibility(license_a, license_b);

                match compatibility {
                    crate::compliance::license_conflict_resolver::CompatibilityLevel::Compatible => {
                        compatible_pairs.push((license_a.clone(), license_b.clone()));
                        total_score += 100.0;
                    }
                    crate::compliance::license_conflict_resolver::CompatibilityLevel::Conditional => {
                        conditional_pairs.push((license_a.clone(), license_b.clone()));
                        total_score += 75.0;
                    }
                    crate::compliance::license_conflict_resolver::CompatibilityLevel::Resolvable => {
                        conditional_pairs.push((license_a.clone(), license_b.clone()));
                        total_score += 50.0;
                    }
                    crate::compliance::license_conflict_resolver::CompatibilityLevel::Incompatible => {
                        incompatible_pairs.push((license_a.clone(), license_b.clone()));
                        total_score += 0.0;
                    }
                }
            }
        }

        let overall_score = if pair_count > 0 {
            total_score / pair_count as f64
        } else {
            100.0 // Single license is fully compatible
        };

        Ok(CompatibilityAssessment {
            overall_score,
            compatible_pairs,
            incompatible_pairs,
            conditional_pairs,
        })
    }

    /// Generate attribution requirements for licenses
    fn generate_attribution_requirements(&self, licenses: &[String]) -> Result<Vec<AttributionRequirement>> {
        let mut requirements = Vec::new();

        for license in licenses {
            if let Some(template) = self.attribution_templates.get(license) {
                requirements.push(template.clone());
            } else {
                // Generate generic attribution for unknown licenses
                requirements.push(AttributionRequirement {
                    license: license.clone(),
                    attribution_text: format!("This software includes components licensed under the {} license.", license),
                    mandatory: true,
                    format: AttributionFormat::SpdxIdentifier,
                });
            }
        }

        Ok(requirements)
    }

    /// Assess risk for the license combination
    fn assess_risk(
        &self,
        node: &LicenseNode,
        compatibility: &CompatibilityAssessment,
    ) -> Result<RiskAssessment> {
        let mut risk_score = 0.0;
        let mut risk_factors = Vec::new();
        let mut mitigation_strategies = Vec::new();

        // Base risk from compatibility
        risk_score += (100.0 - compatibility.overall_score) * 0.7;

        // Risk from incompatible pairs
        if !compatibility.incompatible_pairs.is_empty() {
            risk_score += 20.0;
            risk_factors.push(format!(
                "Found {} incompatible license pairs",
                compatibility.incompatible_pairs.len()
            ));
            mitigation_strategies.push("Review incompatible license combinations".to_string());
            mitigation_strategies.push("Consider replacing incompatible licenses".to_string());
        }

        // Risk from combination complexity
        let complexity = self.calculate_complexity(node);
        risk_score += complexity * 10.0;

        if complexity > 3.0 {
            risk_factors.push("High complexity license expression".to_string());
            mitigation_strategies.push("Simplify license expression where possible".to_string());
        }

        // Risk from conditional compatibilities
        if !compatibility.conditional_pairs.is_empty() {
            risk_score += 10.0;
            risk_factors.push(format!(
                "Found {} conditionally compatible pairs",
                compatibility.conditional_pairs.len()
            ));
            mitigation_strategies.push("Review conditions for license compatibility".to_string());
        }

        // Ensure risk score is within bounds
        risk_score = risk_score.min(100.0);

        if risk_factors.is_empty() {
            risk_factors.push("No significant risk factors identified".to_string());
        }

        if mitigation_strategies.is_empty() {
            mitigation_strategies.push("Maintain current license combination".to_string());
        }

        Ok(RiskAssessment {
            risk_score,
            risk_factors,
            mitigation_strategies,
        })
    }

    /// Calculate complexity score for license expression
    fn calculate_complexity(&self, node: &LicenseNode) -> f64 {
        match node {
            LicenseNode::License(_) => 1.0,
            LicenseNode::Or(left, right) | LicenseNode::And(left, right) => {
                1.0 + self.calculate_complexity(left) + self.calculate_complexity(right)
            }
            LicenseNode::With(license, _) => 2.0 + self.calculate_complexity(license),
        }
    }

    /// Validate SPDX license expression
    pub fn validate_expression(&self, expression: &str) -> Result<()> {
        // Parse to check syntax
        let _ = LicenseExpressionParser::parse(expression)?;

        // Additional semantic validation
        let parsed = LicenseExpressionParser::parse(expression)?;
        LicenseExpressionParser::validate_semantics(&parsed)?;

        Ok(())
    }

    /// Optimize license expression for clarity
    pub fn optimize_expression(&self, expression: &str) -> Result<String> {
        let parsed = LicenseExpressionParser::parse(expression)?;

        // For now, return normalized expression
        // Could be enhanced with more sophisticated optimization
        Ok(LicenseExpressionParser::to_string(&parsed))
    }

    /// Check if combination is dual licensing
    pub fn is_dual_licensing(&self, expression: &str) -> Result<bool> {
        let parsed = LicenseExpressionParser::parse(expression)?;
        Ok(matches!(self.determine_combination_type(&parsed), LicenseCombination::Dual))
    }

    /// Get all license families in the combination
    pub fn get_license_families(&self, expression: &str) -> Result<HashSet<String>> {
        let parsed = LicenseExpressionParser::parse(expression)?;
        let licenses = LicenseExpressionParser::extract_licenses(&parsed);

        let mut families = HashSet::new();
        for license in licenses {
            if let Some(family) = self.conflict_resolver.get_license_family(&license) {
                families.insert(family.clone());
            } else {
                families.insert(license); // Use license itself if no family
            }
        }

        Ok(families)
    }

    /// Resolve dual licensing conflicts
    pub fn resolve_dual_conflicts(
        &self,
        expressions: &[String],
        strategy: ResolutionStrategy,
    ) -> Result<crate::compliance::license_conflict_resolver::ConflictResolution> {
        // Use default confidence scores
        let confidence_scores = vec![1.0; expressions.len()];
        self.conflict_resolver.resolve_conflicts(expressions, &confidence_scores, strategy)
    }
}

impl Default for DualLicensingHandler {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_dual_license_analysis() {
        let handler = DualLicensingHandler::new();
        let result = handler.analyze_combination("MIT OR Apache-2.0").unwrap();

        assert_eq!(result.combination_type, LicenseCombination::Dual);
        assert_eq!(result.licenses.len(), 2);
        assert!(result.licenses.contains(&"MIT".to_string()));
        assert!(result.licenses.contains(&"Apache-2.0".to_string()));
    }

    #[test]
    fn test_conjunctive_license_analysis() {
        let handler = DualLicensingHandler::new();
        let result = handler.analyze_combination("MIT AND Apache-2.0").unwrap();

        assert_eq!(result.combination_type, LicenseCombination::Conjunctive);
        assert_eq!(result.licenses.len(), 2);
    }

    #[test]
    fn test_with_exception_analysis() {
        let handler = DualLicensingHandler::new();
        let result = handler.analyze_combination("GPL-2.0 WITH Classpath-exception").unwrap();

        assert_eq!(result.combination_type, LicenseCombination::WithException);
        assert_eq!(result.licenses.len(), 1); // Only GPL-2.0, exception is separate
    }

    #[test]
    fn test_expression_validation() {
        let handler = DualLicensingHandler::new();

        assert!(handler.validate_expression("MIT OR Apache-2.0").is_ok());
        assert!(handler.validate_expression("MIT AND (Apache-2.0 OR BSD-3-Clause)").is_ok());
        assert!(handler.validate_expression("INVALID").is_err());
    }

    #[test]
    fn test_dual_licensing_detection() {
        let handler = DualLicensingHandler::new();

        assert!(handler.is_dual_licensing("MIT OR Apache-2.0").unwrap());
        assert!(!handler.is_dual_licensing("MIT AND Apache-2.0").unwrap());
        assert!(!handler.is_dual_licensing("MIT").unwrap());
    }

    #[test]
    fn test_license_families_extraction() {
        let handler = DualLicensingHandler::new();
        let families = handler.get_license_families("MIT OR BSD-3-Clause").unwrap();

        assert!(families.contains("MIT"));
        assert!(families.contains("BSD"));
    }

    #[test]
    fn test_attribution_requirements() {
        let handler = DualLicensingHandler::new();
        let analysis = handler.analyze_combination("MIT OR Apache-2.0").unwrap();

        assert!(!analysis.attribution_requirements.is_empty());
        let mit_req = analysis.attribution_requirements.iter()
            .find(|r| r.license == "MIT").unwrap();
        assert!(mit_req.mandatory);
        assert_eq!(mit_req.format, AttributionFormat::CopyrightNotice);
    }
}