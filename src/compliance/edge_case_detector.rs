//! # Edge Case Detector
//!
//! Proactive identification of complex license scenarios including dual-licensing patterns,
//! conflict detection algorithms, and internationalization requirements.

use crate::{
    compliance::{
        dual_licensing_handler::{DualLicensingHandler, CombinationAnalysis},
        internationalization_engine::InternationalizationEngine,
        license_conflict_resolver::{LicenseConflictResolver, LicenseConflict},
        license_expression_parser::{LicenseExpressionParser, LicenseNode},
        i18n::Language,
    },
    error::{InfinitumError, Result},
    scanners::ScanResult,
};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use tracing::{debug, info, instrument, warn};

/// Edge case type
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum EdgeCaseType {
    /// Dual licensing scenario
    DualLicensing,
    /// Multi-licensing with complex expressions
    MultiLicensing,
    /// License conflicts
    LicenseConflicts,
    /// Internationalization requirements
    Internationalization,
    /// Complex SPDX expressions
    ComplexExpressions,
    /// Proprietary license combinations
    ProprietaryCombinations,
    /// Regional compliance issues
    RegionalCompliance,
}

/// Detected edge case
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DetectedEdgeCase {
    /// Edge case type
    pub case_type: EdgeCaseType,
    /// Description of the edge case
    pub description: String,
    /// Affected components
    pub affected_components: Vec<String>,
    /// Risk score (0-100)
    pub risk_score: f64,
    /// Confidence in detection (0-100)
    pub confidence: f64,
    /// Recommended actions
    pub recommendations: Vec<String>,
    /// Additional metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Edge case detection result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EdgeCaseDetectionResult {
    /// Detected edge cases
    pub edge_cases: Vec<DetectedEdgeCase>,
    /// Overall risk assessment
    pub overall_risk: f64,
    /// Detection summary
    pub summary: String,
    /// Processing statistics
    pub statistics: DetectionStatistics,
}

/// Detection statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetectionStatistics {
    /// Total components analyzed
    pub total_components: usize,
    /// Components with edge cases
    pub components_with_edge_cases: usize,
    /// Total edge cases detected
    pub total_edge_cases: usize,
    /// Detection time in milliseconds
    pub detection_time_ms: u64,
}

/// Edge case detector
pub struct EdgeCaseDetector {
    dual_handler: DualLicensingHandler,
    conflict_resolver: LicenseConflictResolver,
    i18n_engine: InternationalizationEngine,
    risk_thresholds: HashMap<EdgeCaseType, f64>,
}

impl EdgeCaseDetector {
    /// Create new edge case detector
    pub fn new() -> Self {
        let mut detector = Self {
            dual_handler: DualLicensingHandler::new(),
            conflict_resolver: LicenseConflictResolver::new(),
            i18n_engine: InternationalizationEngine::new("translations".to_string()),
            risk_thresholds: HashMap::new(),
        };
        detector.initialize_risk_thresholds();
        detector
    }

    /// Initialize risk thresholds for different edge case types
    fn initialize_risk_thresholds(&mut self) {
        self.risk_thresholds.insert(EdgeCaseType::DualLicensing, 20.0);
        self.risk_thresholds.insert(EdgeCaseType::MultiLicensing, 30.0);
        self.risk_thresholds.insert(EdgeCaseType::LicenseConflicts, 70.0);
        self.risk_thresholds.insert(EdgeCaseType::Internationalization, 40.0);
        self.risk_thresholds.insert(EdgeCaseType::ComplexExpressions, 25.0);
        self.risk_thresholds.insert(EdgeCaseType::ProprietaryCombinations, 80.0);
        self.risk_thresholds.insert(EdgeCaseType::RegionalCompliance, 60.0);
    }

    /// Detect edge cases in scan results
    #[instrument(skip(self, scan_results))]
    pub async fn detect_edge_cases(&mut self, scan_results: &[ScanResult]) -> Result<EdgeCaseDetectionResult> {
        let start_time = std::time::Instant::now();
        info!("Starting edge case detection for {} scan results", scan_results.len());

        let mut all_edge_cases = Vec::new();
        let mut total_components = 0;
        let mut components_with_edge_cases = 0;

        for scan_result in scan_results {
            for component in &scan_result.software_components {
                total_components += 1;

                let component_edge_cases = self.detect_component_edge_cases(component).await?;

                if !component_edge_cases.is_empty() {
                    components_with_edge_cases += 1;
                    all_edge_cases.extend(component_edge_cases);
                }
            }

            // Check for license detection patterns
            if let Some(component_edge_cases) = self.detect_license_detection_edge_cases(scan_result).await? {
                all_edge_cases.extend(component_edge_cases);
            }
        }

        // Global analysis
        let global_edge_cases = self.detect_global_edge_cases(scan_results).await?;
        all_edge_cases.extend(global_edge_cases);

        // Calculate overall risk
        let overall_risk = self.calculate_overall_risk(&all_edge_cases);

        // Generate summary
        let summary = self.generate_detection_summary(&all_edge_cases, overall_risk);

        let detection_time = start_time.elapsed().as_millis() as u64;

        let statistics = DetectionStatistics {
            total_components,
            components_with_edge_cases,
            total_edge_cases: all_edge_cases.len(),
            detection_time_ms: detection_time,
        };

        debug!("Edge case detection completed in {}ms", detection_time);

        Ok(EdgeCaseDetectionResult {
            edge_cases: all_edge_cases,
            overall_risk,
            summary,
            statistics,
        })
    }

    /// Detect edge cases for a single component
    async fn detect_component_edge_cases(&mut self, component: &crate::scanners::SoftwareComponent) -> Result<Vec<DetectedEdgeCase>> {
        let mut edge_cases = Vec::new();

        // Check license expression complexity
        if let Some(license) = &component.license {
            if let Ok(analysis) = self.dual_handler.analyze_combination(license) {
                // Dual licensing detection
                if matches!(analysis.combination_type, crate::compliance::dual_licensing_handler::LicenseCombination::Dual) {
                    edge_cases.push(DetectedEdgeCase {
                        case_type: EdgeCaseType::DualLicensing,
                        description: format!("Dual licensing detected: {}", license),
                        affected_components: vec![component.name.clone()],
                        risk_score: analysis.risk_assessment.risk_score,
                        confidence: 90.0,
                        recommendations: vec![
                            "Review dual licensing terms".to_string(),
                            "Ensure both licenses are acceptable for your use case".to_string(),
                        ],
                        metadata: HashMap::from([
                            ("licenses".to_string(), serde_json::json!(analysis.licenses)),
                            ("compatibility_score".to_string(), serde_json::json!(analysis.compatibility.overall_score)),
                        ]),
                    });
                }

                // Complex expressions
                if self.is_complex_expression(license) {
                    edge_cases.push(DetectedEdgeCase {
                        case_type: EdgeCaseType::ComplexExpressions,
                        description: format!("Complex license expression: {}", license),
                        affected_components: vec![component.name.clone()],
                        risk_score: 25.0,
                        confidence: 85.0,
                        recommendations: vec![
                            "Consider simplifying the license expression".to_string(),
                            "Document the reasoning behind the complex licensing".to_string(),
                        ],
                        metadata: HashMap::from([
                            ("expression".to_string(), serde_json::json!(license)),
                        ]),
                    });
                }

                // Proprietary combinations
                if analysis.licenses.iter().any(|l| l == "Proprietary") {
                    edge_cases.push(DetectedEdgeCase {
                        case_type: EdgeCaseType::ProprietaryCombinations,
                        description: "Proprietary license in combination detected".to_string(),
                        affected_components: vec![component.name.clone()],
                        risk_score: 80.0,
                        confidence: 95.0,
                        recommendations: vec![
                            "Review proprietary license terms carefully".to_string(),
                            "Consider open source alternatives".to_string(),
                            "Ensure compliance with proprietary license restrictions".to_string(),
                        ],
                        metadata: HashMap::from([
                            ("proprietary_licenses".to_string(), serde_json::json!(analysis.licenses.iter().filter(|l| *l == "Proprietary").collect::<Vec<_>>())),
                        ]),
                    });
                }
            }
        }

        // Internationalization requirements
        if let Some(license) = &component.license {
            let i18n_cases = self.detect_internationalization_edge_cases(license, &component.name).await?;
            edge_cases.extend(i18n_cases);
        }

        Ok(edge_cases)
    }

    /// Detect edge cases from license detections
    async fn detect_license_detection_edge_cases(&mut self, scan_result: &ScanResult) -> Result<Option<Vec<DetectedEdgeCase>>> {
        let mut edge_cases = Vec::new();

        for detection in &scan_result.license_detections {
            // Multiple license detections with different confidence
            if detection.licenses.len() > 1 {
                let confidences: Vec<f64> = detection.licenses.iter().map(|l| l.confidence).collect();
                let max_confidence = confidences.iter().cloned().fold(0.0, f64::max);
                let min_confidence = confidences.iter().cloned().fold(1.0, f64::min);
                let confidence_spread = max_confidence - min_confidence;

                if confidence_spread > 0.3 {
                    edge_cases.push(DetectedEdgeCase {
                        case_type: EdgeCaseType::LicenseConflicts,
                        description: "Multiple license detections with varying confidence".to_string(),
                        affected_components: vec![detection.file_path.clone()],
                        risk_score: 40.0,
                        confidence: 75.0,
                        recommendations: vec![
                            "Review license detection results manually".to_string(),
                            "Consider using higher confidence threshold".to_string(),
                        ],
                        metadata: HashMap::from([
                            ("confidence_spread".to_string(), serde_json::json!(confidence_spread)),
                            ("max_confidence".to_string(), serde_json::json!(max_confidence)),
                            ("min_confidence".to_string(), serde_json::json!(min_confidence)),
                        ]),
                    });
                }
            }

            // Low confidence detections
            for license in &detection.licenses {
                if license.confidence < 0.5 {
                    edge_cases.push(DetectedEdgeCase {
                        case_type: EdgeCaseType::LicenseConflicts,
                        description: format!("Low confidence license detection: {} ({:.2})", license.name, license.confidence),
                        affected_components: vec![detection.file_path.clone()],
                        risk_score: 30.0,
                        confidence: 80.0,
                        recommendations: vec![
                            "Manually verify the license detection".to_string(),
                            "Consider using alternative license scanning tools".to_string(),
                        ],
                        metadata: HashMap::from([
                            ("detected_license".to_string(), serde_json::json!(license.name)),
                            ("confidence".to_string(), serde_json::json!(license.confidence)),
                        ]),
                    });
                }
            }
        }

        if edge_cases.is_empty() {
            Ok(None)
        } else {
            Ok(Some(edge_cases))
        }
    }

    /// Detect internationalization edge cases
    async fn detect_internationalization_edge_cases(&mut self, license: &str, component_name: &str) -> Result<Vec<DetectedEdgeCase>> {
        let mut edge_cases = Vec::new();

        // Check for non-ASCII characters in license text
        if license.chars().any(|c| !c.is_ascii()) {
            edge_cases.push(DetectedEdgeCase {
                case_type: EdgeCaseType::Internationalization,
                description: "License contains non-ASCII characters".to_string(),
                affected_components: vec![component_name.to_string()],
                risk_score: 20.0,
                confidence: 100.0,
                recommendations: vec![
                    "Ensure proper character encoding handling".to_string(),
                    "Verify license text integrity".to_string(),
                ],
                metadata: HashMap::from([
                    ("license_text".to_string(), serde_json::json!(license)),
                ]),
            });
        }

        // Check regional requirements
        for region in &["EU", "DE", "FR", "JP", "CN"] {
            if self.i18n_engine.is_translation_required(region, license)? {
                edge_cases.push(DetectedEdgeCase {
                    case_type: EdgeCaseType::RegionalCompliance,
                    description: format!("Translation required for region: {}", region),
                    affected_components: vec![component_name.to_string()],
                    risk_score: 60.0,
                    confidence: 90.0,
                    recommendations: vec![
                        format!("Provide {} translation for {}", region, license),
                        "Ensure compliance with regional requirements".to_string(),
                    ],
                    metadata: HashMap::from([
                        ("region".to_string(), serde_json::json!(region)),
                        ("license".to_string(), serde_json::json!(license)),
                    ]),
                });
            }
        }

        Ok(edge_cases)
    }

    /// Detect global edge cases across all components
    async fn detect_global_edge_cases(&mut self, scan_results: &[ScanResult]) -> Result<Vec<DetectedEdgeCase>> {
        let mut edge_cases = Vec::new();

        // Collect all licenses across components
        let mut all_licenses = HashSet::new();
        for scan_result in scan_results {
            for component in &scan_result.software_components {
                if let Some(license) = &component.license {
                    if let Ok(licenses) = LicenseExpressionParser::parse(license) {
                        all_licenses.extend(LicenseExpressionParser::extract_licenses(&licenses));
                    }
                }
            }
        }

        // Check for license family conflicts
        let license_list: Vec<String> = all_licenses.into_iter().collect();
        for i in 0..license_list.len() {
            for j in (i + 1)..license_list.len() {
                let license_a = &license_list[i];
                let license_b = &license_list[j];

                if let (Some(family_a), Some(family_b)) = (
                    self.conflict_resolver.get_license_family(license_a),
                    self.conflict_resolver.get_license_family(license_b),
                ) {
                    if family_a != family_b && self.conflict_resolver.get_compatibility(license_a, license_b)
                        == crate::compliance::license_conflict_resolver::CompatibilityLevel::Incompatible {
                        edge_cases.push(DetectedEdgeCase {
                            case_type: EdgeCaseType::LicenseConflicts,
                            description: format!("Incompatible license families: {} ({}) vs {} ({})", license_a, family_a, license_b, family_b),
                            affected_components: vec!["Multiple components".to_string()],
                            risk_score: 90.0,
                            confidence: 95.0,
                            recommendations: vec![
                                "Resolve license family conflicts".to_string(),
                                "Consider replacing incompatible licenses".to_string(),
                                "Review component dependencies".to_string(),
                            ],
                            metadata: HashMap::from([
                                ("license_a".to_string(), serde_json::json!(license_a)),
                                ("family_a".to_string(), serde_json::json!(family_a)),
                                ("license_b".to_string(), serde_json::json!(license_b)),
                                ("family_b".to_string(), serde_json::json!(family_b)),
                            ]),
                        });
                    }
                }
            }
        }

        Ok(edge_cases)
    }

    /// Check if license expression is complex
    fn is_complex_expression(&self, expression: &str) -> bool {
        // Count operators
        let operators = [" AND ", " OR ", " WITH "]
            .iter()
            .map(|op| expression.matches(op).count())
            .sum::<usize>();

        // Count parentheses
        let paren_count = expression.chars().filter(|&c| c == '(' || c == ')').count();

        // Consider complex if multiple operators or nested parentheses
        operators > 2 || paren_count > 4
    }

    /// Calculate overall risk from all edge cases
    fn calculate_overall_risk(&self, edge_cases: &[DetectedEdgeCase]) -> f64 {
        if edge_cases.is_empty() {
            return 0.0;
        }

        let total_risk: f64 = edge_cases.iter().map(|ec| ec.risk_score).sum();
        let avg_risk = total_risk / edge_cases.len() as f64;

        // Weight by number of high-risk cases
        let high_risk_count = edge_cases.iter().filter(|ec| ec.risk_score > 70.0).count();
        let high_risk_weight = high_risk_count as f64 * 10.0;

        (avg_risk + high_risk_weight).min(100.0)
    }

    /// Generate detection summary
    fn generate_detection_summary(&self, edge_cases: &[DetectedEdgeCase], overall_risk: f64) -> String {
        let case_counts: HashMap<EdgeCaseType, usize> = edge_cases.iter()
            .fold(HashMap::new(), |mut acc, ec| {
                *acc.entry(ec.case_type.clone()).or_insert(0) += 1;
                acc
            });

        let mut summary = format!(
            "Detected {} edge cases with overall risk score of {:.1}",
            edge_cases.len(),
            overall_risk
        );

        if !case_counts.is_empty() {
            summary.push_str(". Breakdown: ");
            for (case_type, count) in case_counts {
                summary.push_str(&format!("{}: {}, ", self.case_type_name(&case_type), count));
            }
            summary.truncate(summary.len() - 2); // Remove last comma and space
        }

        summary
    }

    /// Get human-readable name for edge case type
    fn case_type_name(&self, case_type: &EdgeCaseType) -> &str {
        match case_type {
            EdgeCaseType::DualLicensing => "Dual Licensing",
            EdgeCaseType::MultiLicensing => "Multi-Licensing",
            EdgeCaseType::LicenseConflicts => "License Conflicts",
            EdgeCaseType::Internationalization => "Internationalization",
            EdgeCaseType::ComplexExpressions => "Complex Expressions",
            EdgeCaseType::ProprietaryCombinations => "Proprietary Combinations",
            EdgeCaseType::RegionalCompliance => "Regional Compliance",
        }
    }

    /// Get risk threshold for edge case type
    pub fn get_risk_threshold(&self, case_type: &EdgeCaseType) -> f64 {
        self.risk_thresholds.get(case_type).cloned().unwrap_or(50.0)
    }

    /// Set risk threshold for edge case type
    pub fn set_risk_threshold(&mut self, case_type: EdgeCaseType, threshold: f64) {
        self.risk_thresholds.insert(case_type, threshold);
    }
}

impl Default for EdgeCaseDetector {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::scanners::{SoftwareComponent, LicenseDetection, DetectedLicense};

    #[test]
    fn test_complex_expression_detection() {
        let detector = EdgeCaseDetector::new();
        assert!(detector.is_complex_expression("MIT AND (Apache-2.0 OR BSD-3-Clause)"));
        assert!(!detector.is_complex_expression("MIT"));
    }

    #[test]
    fn test_overall_risk_calculation() {
        let detector = EdgeCaseDetector::new();

        let edge_cases = vec![
            DetectedEdgeCase {
                case_type: EdgeCaseType::DualLicensing,
                description: "Test case".to_string(),
                affected_components: vec![],
                risk_score: 20.0,
                confidence: 80.0,
                recommendations: vec![],
                metadata: HashMap::new(),
            },
            DetectedEdgeCase {
                case_type: EdgeCaseType::LicenseConflicts,
                description: "Test case 2".to_string(),
                affected_components: vec![],
                risk_score: 80.0,
                confidence: 90.0,
                recommendations: vec![],
                metadata: HashMap::new(),
            },
        ];

        let overall_risk = detector.calculate_overall_risk(&edge_cases);
        assert!(overall_risk > 40.0); // Should be weighted by high-risk case
    }

    #[test]
    fn test_case_type_naming() {
        let detector = EdgeCaseDetector::new();
        assert_eq!(detector.case_type_name(&EdgeCaseType::DualLicensing), "Dual Licensing");
        assert_eq!(detector.case_type_name(&EdgeCaseType::LicenseConflicts), "License Conflicts");
    }

    #[tokio::test]
    async fn test_component_edge_case_detection() {
        let mut detector = EdgeCaseDetector::new();

        let component = SoftwareComponent {
            name: "test-component".to_string(),
            version: "1.0.0".to_string(),
            license: Some("MIT OR Apache-2.0".to_string()),
            purl: None,
            cpe: None,
            download_url: None,
            homepage: None,
            description: None,
            hash: None,
            copyright: None,
            dependencies: Vec::new(),
            vulnerabilities: Vec::new(),
        };

        let edge_cases = detector.detect_component_edge_cases(&component).await.unwrap();
        assert!(!edge_cases.is_empty());
        assert!(edge_cases.iter().any(|ec| ec.case_type == EdgeCaseType::DualLicensing));
    }
}