//! # Error Recovery Manager
//!
//! Intelligent error recovery system with context-aware recovery strategies,
//! progressive retry mechanisms, alternative processing paths, and recovery
//! success rate tracking for the license compliance platform.

use crate::{
    error::{InfinitumError, Result},
    compliance::{
        ErrorContext, ErrorClassification, ErrorSeverity, RecoveryAction, RecoveryStrategy,
        CircuitBreakerState, CircuitBreakerStateEnum,
    },
};
use serde::{Deserialize, Serialize};
use std::{
    collections::{HashMap, HashSet, VecDeque},
    sync::Arc,
    time::{Duration, Instant},
};
use tokio::sync::RwLock;
use tracing::{debug, error, info, instrument, warn};
use chrono::{DateTime, Utc};

/// Recovery configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecoveryConfig {
    /// Enable intelligent recovery
    pub enable_intelligent_recovery: bool,
    /// Maximum retry attempts
    pub max_retry_attempts: u32,
    /// Base retry delay (milliseconds)
    pub base_retry_delay_ms: u64,
    /// Maximum retry delay (milliseconds)
    pub max_retry_delay_ms: u64,
    /// Exponential backoff multiplier
    pub backoff_multiplier: f64,
    /// Enable jitter in retry delays
    pub enable_jitter: bool,
    /// Recovery timeout (seconds)
    pub recovery_timeout_secs: u64,
    /// Success rate tracking window (minutes)
    pub success_rate_window_minutes: u64,
    /// Minimum success rate for strategy promotion
    pub min_success_rate_for_promotion: f64,
    /// Enable alternative processing paths
    pub enable_alternative_paths: bool,
    /// Context awareness enabled
    pub enable_context_awareness: bool,
}

/// Recovery attempt information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecoveryAttempt {
    /// Attempt ID
    pub attempt_id: String,
    /// Error context
    pub error_context: ErrorContext,
    /// Recovery strategy used
    pub strategy: RecoveryStrategy,
    /// Attempt number
    pub attempt_number: u32,
    /// Start time
    pub start_time: DateTime<Utc>,
    /// End time
    pub end_time: Option<DateTime<Utc>>,
    /// Success status
    pub success: Option<bool>,
    /// Recovery time (milliseconds)
    pub recovery_time_ms: Option<u64>,
    /// Alternative path used
    pub alternative_path: Option<String>,
    /// Context factors considered
    pub context_factors: HashMap<String, serde_json::Value>,
}

/// Recovery strategy performance
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecoveryPerformance {
    /// Strategy type
    pub strategy: RecoveryStrategy,
    /// Total attempts
    pub total_attempts: u64,
    /// Successful recoveries
    pub successful_recoveries: u64,
    /// Failed recoveries
    pub failed_recoveries: u64,
    /// Average recovery time (milliseconds)
    pub avg_recovery_time_ms: f64,
    /// Success rate (0-1)
    pub success_rate: f64,
    /// Last updated
    pub last_updated: DateTime<Utc>,
}

/// Alternative processing path
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlternativePath {
    /// Path identifier
    pub path_id: String,
    /// Path name
    pub name: String,
    /// Applicable error classifications
    pub applicable_errors: HashSet<ErrorClassification>,
    /// Resource requirements
    pub resource_requirements: HashMap<String, serde_json::Value>,
    /// Expected performance impact
    pub performance_impact: f64,
    /// Reliability score (0-1)
    pub reliability_score: f64,
    /// Last used timestamp
    pub last_used: Option<DateTime<Utc>>,
    /// Usage count
    pub usage_count: u64,
}

/// Context-aware recovery decision
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecoveryDecision {
    /// Recommended strategy
    pub recommended_strategy: RecoveryStrategy,
    /// Alternative paths to consider
    pub alternative_paths: Vec<String>,
    /// Expected success probability
    pub expected_success_probability: f64,
    /// Expected recovery time
    pub expected_recovery_time: Duration,
    /// Context factors influencing decision
    pub context_factors: HashMap<String, String>,
    /// Decision confidence
    pub decision_confidence: f64,
}

/// Error recovery manager
pub struct ErrorRecoveryManager {
    config: RecoveryConfig,
    recovery_history: Arc<RwLock<VecDeque<RecoveryAttempt>>>,
    strategy_performance: Arc<RwLock<HashMap<RecoveryStrategy, RecoveryPerformance>>>,
    alternative_paths: Vec<AlternativePath>,
    context_analyzer: ContextAnalyzer,
}

/// Context analyzer for intelligent recovery decisions
#[derive(Debug)]
struct ContextAnalyzer {
    error_patterns: HashMap<String, Vec<ErrorContext>>,
    system_state: HashMap<String, serde_json::Value>,
}

impl ErrorRecoveryManager {
    /// Create new error recovery manager
    pub fn new(config: RecoveryConfig) -> Self {
        let mut manager = Self {
            config,
            recovery_history: Arc::new(RwLock::new(VecDeque::with_capacity(10000))),
            strategy_performance: Arc::new(RwLock::new(HashMap::new())),
            alternative_paths: Vec::new(),
            context_analyzer: ContextAnalyzer {
                error_patterns: HashMap::new(),
                system_state: HashMap::new(),
            },
        };

        manager.initialize_alternative_paths();
        manager.initialize_default_strategies();
        manager
    }

    /// Initialize default alternative processing paths
    fn initialize_alternative_paths(&mut self) {
        // Cached response path
        self.alternative_paths.push(AlternativePath {
            path_id: "cached_response".to_string(),
            name: "Cached Response Fallback".to_string(),
            applicable_errors: HashSet::from([
                ErrorClassification::Network,
                ErrorClassification::External,
                ErrorClassification::Transient,
            ]),
            resource_requirements: HashMap::from([
                ("memory".to_string(), serde_json::json!("low")),
                ("cpu".to_string(), serde_json::json!("low")),
            ]),
            performance_impact: 0.1, // 10% faster
            reliability_score: 0.9,
            last_used: None,
            usage_count: 0,
        });

        // Simplified processing path
        self.alternative_paths.push(AlternativePath {
            path_id: "simplified_processing".to_string(),
            name: "Simplified Processing".to_string(),
            applicable_errors: HashSet::from([
                ErrorClassification::ResourceExhaustion,
                ErrorClassification::Internal,
            ]),
            resource_requirements: HashMap::from([
                ("memory".to_string(), serde_json::json!("medium")),
                ("cpu".to_string(), serde_json::json!("medium")),
            ]),
            performance_impact: -0.2, // 20% slower
            reliability_score: 0.95,
            last_used: None,
            usage_count: 0,
        });

        // Backup service path
        self.alternative_paths.push(AlternativePath {
            path_id: "backup_service".to_string(),
            name: "Backup Service".to_string(),
            applicable_errors: HashSet::from([
                ErrorClassification::External,
                ErrorClassification::ServiceUnavailable,
            ]),
            resource_requirements: HashMap::from([
                ("network".to_string(), serde_json::json!("high")),
                ("cpu".to_string(), serde_json::json!("medium")),
            ]),
            performance_impact: -0.3, // 30% slower
            reliability_score: 0.85,
            last_used: None,
            usage_count: 0,
        });

        // Local processing path
        self.alternative_paths.push(AlternativePath {
            path_id: "local_processing".to_string(),
            name: "Local Processing Fallback".to_string(),
            applicable_errors: HashSet::from([
                ErrorClassification::Network,
                ErrorClassification::External,
            ]),
            resource_requirements: HashMap::from([
                ("cpu".to_string(), serde_json::json!("high")),
                ("memory".to_string(), serde_json::json!("high")),
            ]),
            performance_impact: -0.5, // 50% slower
            reliability_score: 0.98,
            last_used: None,
            usage_count: 0,
        });
    }

    /// Initialize default recovery strategies
    fn initialize_default_strategies(&mut self) {
        let strategies = vec![
            RecoveryStrategy::Retry,
            RecoveryStrategy::FallbackToCache,
            RecoveryStrategy::GracefulDegradation,
            RecoveryStrategy::AlternativeService,
            RecoveryStrategy::ManualIntervention,
            RecoveryStrategy::Ignore,
            RecoveryStrategy::FailFast,
        ];

        let mut strategy_performance = self.strategy_performance.try_write().unwrap();
        for strategy in strategies {
            strategy_performance.insert(strategy, RecoveryPerformance {
                strategy: strategy.clone(),
                total_attempts: 0,
                successful_recoveries: 0,
                failed_recoveries: 0,
                avg_recovery_time_ms: 0.0,
                success_rate: 0.0,
                last_updated: Utc::now(),
            });
        }
    }

    /// Execute intelligent error recovery
    #[instrument(skip(self, error, context))]
    pub async fn execute_recovery(
        &self,
        error: &InfinitumError,
        context: ErrorContext,
    ) -> Result<RecoveryResult> {
        let start_time = Instant::now();
        info!("Executing intelligent recovery for error: {}", error);

        // Analyze context for intelligent decision making
        let decision = self.analyze_context_and_decide(&context).await?;

        // Select best recovery strategy
        let strategy = self.select_optimal_strategy(&decision, &context).await?;

        // Execute recovery with progressive retry
        let result = self.execute_progressive_recovery(error, &context, &strategy, &decision).await;

        let recovery_time = start_time.elapsed().as_millis() as u64;

        // Record recovery attempt
        self.record_recovery_attempt(&context, &strategy, result.is_ok(), recovery_time, &decision).await?;

        match result {
            Ok(recovery_result) => {
                info!("Recovery successful in {}ms using strategy {:?}", recovery_time, strategy.strategy);
                Ok(recovery_result)
            }
            Err(e) => {
                warn!("Recovery failed after {}ms: {}", recovery_time, e);
                Err(e)
            }
        }
    }

    /// Analyze context and make intelligent recovery decision
    async fn analyze_context_and_decide(&self, context: &ErrorContext) -> Result<RecoveryDecision> {
        let mut context_factors = HashMap::new();
        let mut expected_success_probability = 0.5;
        let mut alternative_paths = Vec::new();

        // Analyze error classification
        match context.classification {
            ErrorClassification::Transient => {
                expected_success_probability = 0.8;
                context_factors.insert("error_type".to_string(), "transient".to_string());
            }
            ErrorClassification::Network => {
                expected_success_probability = 0.6;
                context_factors.insert("error_type".to_string(), "network".to_string());
            }
            ErrorClassification::External => {
                expected_success_probability = 0.4;
                context_factors.insert("error_type".to_string(), "external".to_string());
            }
            ErrorClassification::Configuration => {
                expected_success_probability = 0.2;
                context_factors.insert("error_type".to_string(), "configuration".to_string());
            }
            _ => {
                expected_success_probability = 0.3;
                context_factors.insert("error_type".to_string(), "other".to_string());
            }
        }

        // Analyze error severity
        match context.severity {
            ErrorSeverity::Critical => {
                expected_success_probability *= 0.7;
                context_factors.insert("severity".to_string(), "critical".to_string());
            }
            ErrorSeverity::High => {
                expected_success_probability *= 0.8;
                context_factors.insert("severity".to_string(), "high".to_string());
            }
            ErrorSeverity::Medium => {
                expected_success_probability *= 0.9;
                context_factors.insert("severity".to_string(), "medium".to_string());
            }
            _ => {
                context_factors.insert("severity".to_string(), "low".to_string());
            }
        }

        // Find applicable alternative paths
        for path in &self.alternative_paths {
            if path.applicable_errors.contains(&context.classification) {
                alternative_paths.push(path.path_id.clone());
            }
        }

        // Analyze historical success rates
        let historical_success = self.analyze_historical_success(&context).await?;
        expected_success_probability = (expected_success_probability + historical_success) / 2.0;

        let decision = RecoveryDecision {
            recommended_strategy: RecoveryStrategy::Retry, // Will be overridden by select_optimal_strategy
            alternative_paths,
            expected_success_probability,
            expected_recovery_time: Duration::from_secs(30),
            context_factors,
            decision_confidence: 0.8,
        };

        Ok(decision)
    }

    /// Select optimal recovery strategy based on context and performance
    async fn select_optimal_strategy(
        &self,
        decision: &RecoveryDecision,
        context: &ErrorContext,
    ) -> Result<RecoveryAction> {
        let strategy_performance = self.strategy_performance.read().await;

        // Find best performing strategy for this error type
        let mut best_strategy = RecoveryStrategy::Retry;
        let mut best_success_rate = 0.0;

        for (strategy, performance) in strategy_performance.iter() {
            if performance.total_attempts > 0 && performance.success_rate > best_success_rate {
                best_strategy = strategy.clone();
                best_success_rate = performance.success_rate;
            }
        }

        // Override based on error classification
        let final_strategy = match context.classification {
            ErrorClassification::Transient => RecoveryStrategy::Retry,
            ErrorClassification::Network => RecoveryStrategy::Retry,
            ErrorClassification::External => RecoveryStrategy::AlternativeService,
            ErrorClassification::Configuration => RecoveryStrategy::ManualIntervention,
            ErrorClassification::ResourceExhaustion => RecoveryStrategy::GracefulDegradation,
            _ => best_strategy,
        };

        let action = RecoveryAction {
            strategy: final_strategy,
            max_retries: self.config.max_retry_attempts,
            retry_delay: Duration::from_millis(self.config.base_retry_delay_ms),
            timeout: Duration::from_secs(self.config.recovery_timeout_secs),
            alternatives: decision.alternative_paths.clone(),
            fallback_data: None,
        };

        Ok(action)
    }

    /// Execute progressive recovery with retry logic
    async fn execute_progressive_recovery(
        &self,
        error: &InfinitumError,
        context: &ErrorContext,
        action: &RecoveryAction,
        decision: &RecoveryDecision,
    ) -> Result<RecoveryResult> {
        let mut attempt_number = 0;
        let mut last_error = error.clone();

        while attempt_number < action.max_retries {
            attempt_number += 1;

            // Calculate delay with exponential backoff and jitter
            let base_delay = self.config.base_retry_delay_ms as f64 * self.config.backoff_multiplier.powf(attempt_number as f64 - 1.0);
            let max_delay = self.config.max_retry_delay_ms as f64;
            let delay = base_delay.min(max_delay);

            let jitter = if self.config.enable_jitter {
                (rand::random::<f64>() - 0.5) * 0.1 * delay // ±5% jitter
            } else {
                0.0
            };

            let final_delay = Duration::from_millis((delay + jitter).max(0.0) as u64);

            if attempt_number > 1 {
                debug!("Waiting {:?} before retry attempt {}", final_delay, attempt_number);
                tokio::time::sleep(final_delay).await;
            }

            // Try recovery
            match self.attempt_recovery_action(error, context, action, attempt_number).await {
                Ok(result) => {
                    return Ok(RecoveryResult {
                        success: true,
                        strategy_used: action.strategy.clone(),
                        attempts_made: attempt_number,
                        total_time_ms: 0, // Will be calculated by caller
                        alternative_path_used: result.alternative_path_used,
                        recovery_metadata: result.recovery_metadata,
                    });
                }
                Err(e) => {
                    last_error = e;
                    debug!("Recovery attempt {} failed: {:?}", attempt_number, last_error);
                }
            }
        }

        // All attempts failed
        Err(last_error)
    }

    /// Attempt a specific recovery action
    async fn attempt_recovery_action(
        &self,
        error: &InfinitumError,
        context: &ErrorContext,
        action: &RecoveryAction,
        attempt_number: u32,
    ) -> Result<RecoveryAttemptResult> {
        match action.strategy {
            RecoveryStrategy::Retry => {
                // For retry, we would typically re-execute the original operation
                // This is a placeholder - actual implementation would depend on the operation
                Ok(RecoveryAttemptResult {
                    alternative_path_used: None,
                    recovery_metadata: HashMap::from([
                        ("attempt".to_string(), serde_json::json!(attempt_number)),
                        ("strategy".to_string(), serde_json::json!("retry")),
                    ]),
                })
            }
            RecoveryStrategy::FallbackToCache => {
                // Use cached data
                Ok(RecoveryAttemptResult {
                    alternative_path_used: Some("cached_response".to_string()),
                    recovery_metadata: HashMap::from([
                        ("attempt".to_string(), serde_json::json!(attempt_number)),
                        ("strategy".to_string(), serde_json::json!("cache_fallback")),
                    ]),
                })
            }
            RecoveryStrategy::AlternativeService => {
                // Try alternative service
                if let Some(alternative) = action.alternatives.first() {
                    Ok(RecoveryAttemptResult {
                        alternative_path_used: Some(alternative.clone()),
                        recovery_metadata: HashMap::from([
                            ("attempt".to_string(), serde_json::json!(attempt_number)),
                            ("strategy".to_string(), serde_json::json!("alternative_service")),
                            ("service".to_string(), serde_json::json!(alternative)),
                        ]),
                    })
                } else {
                    Err(InfinitumError::Internal {
                        message: "No alternative service available".to_string(),
                    })
                }
            }
            RecoveryStrategy::GracefulDegradation => {
                // Degrade functionality
                Ok(RecoveryAttemptResult {
                    alternative_path_used: Some("simplified_processing".to_string()),
                    recovery_metadata: HashMap::from([
                        ("attempt".to_string(), serde_json::json!(attempt_number)),
                        ("strategy".to_string(), serde_json::json!("graceful_degradation")),
                    ]),
                })
            }
            _ => {
                Err(InfinitumError::Internal {
                    message: format!("Unsupported recovery strategy: {:?}", action.strategy),
                })
            }
        }
    }

    /// Analyze historical success rates for similar errors
    async fn analyze_historical_success(&self, context: &ErrorContext) -> Result<f64> {
        let history = self.recovery_history.read().await;

        let similar_attempts: Vec<_> = history.iter()
            .filter(|attempt| {
                attempt.error_context.classification == context.classification &&
                attempt.error_context.component == context.component
            })
            .collect();

        if similar_attempts.is_empty() {
            return Ok(0.5); // Default success rate
        }

        let successful_attempts = similar_attempts.iter()
            .filter(|attempt| attempt.success.unwrap_or(false))
            .count();

        Ok(successful_attempts as f64 / similar_attempts.len() as f64)
    }

    /// Record recovery attempt for learning
    async fn record_recovery_attempt(
        &self,
        context: &ErrorContext,
        action: &RecoveryAction,
        success: bool,
        recovery_time_ms: u64,
        decision: &RecoveryDecision,
    ) -> Result<()> {
        let attempt = RecoveryAttempt {
            attempt_id: format!("recovery_{}", Utc::now().timestamp_millis()),
            error_context: context.clone(),
            strategy: action.strategy.clone(),
            attempt_number: 1, // This would be tracked per recovery session
            start_time: Utc::now() - chrono::Duration::milliseconds(recovery_time_ms as i64),
            end_time: Some(Utc::now()),
            success: Some(success),
            recovery_time_ms: Some(recovery_time_ms),
            alternative_path: None,
            context_factors: decision.context_factors.iter()
                .map(|(k, v)| (k.clone(), serde_json::json!(v)))
                .collect(),
        };

        let mut history = self.recovery_history.write().await;

        // Keep only recent history
        let cutoff_time = Utc::now() - chrono::Duration::minutes(self.config.success_rate_window_minutes as i64);
        while let Some(oldest) = history.front() {
            if oldest.start_time < cutoff_time {
                history.pop_front();
            } else {
                break;
            }
        }

        history.push_back(attempt);

        // Update strategy performance
        self.update_strategy_performance(&action.strategy, success, recovery_time_ms).await?;

        Ok(())
    }

    /// Update strategy performance metrics
    async fn update_strategy_performance(
        &self,
        strategy: &RecoveryStrategy,
        success: bool,
        recovery_time_ms: u64,
    ) -> Result<()> {
        let mut strategy_performance = self.strategy_performance.write().await;

        let performance = strategy_performance.entry(strategy.clone()).or_insert(RecoveryPerformance {
            strategy: strategy.clone(),
            total_attempts: 0,
            successful_recoveries: 0,
            failed_recoveries: 0,
            avg_recovery_time_ms: 0.0,
            success_rate: 0.0,
            last_updated: Utc::now(),
        });

        performance.total_attempts += 1;

        if success {
            performance.successful_recoveries += 1;
        } else {
            performance.failed_recoveries += 1;
        }

        // Update average recovery time
        let total_time = performance.avg_recovery_time_ms * (performance.total_attempts - 1) as f64 + recovery_time_ms as f64;
        performance.avg_recovery_time_ms = total_time / performance.total_attempts as f64;

        // Update success rate
        performance.success_rate = performance.successful_recoveries as f64 / performance.total_attempts as f64;
        performance.last_updated = Utc::now();

        Ok(())
    }

    /// Get recovery performance statistics
    pub async fn get_recovery_performance(&self) -> HashMap<RecoveryStrategy, RecoveryPerformance> {
        let strategy_performance = self.strategy_performance.read().await;
        strategy_performance.clone()
    }

    /// Get alternative paths
    pub fn get_alternative_paths(&self) -> &[AlternativePath] {
        &self.alternative_paths
    }

    /// Add custom alternative path
    pub fn add_alternative_path(&mut self, path: AlternativePath) {
        self.alternative_paths.push(path);
    }

    /// Get recovery success rate for error type
    pub async fn get_success_rate_for_error(&self, classification: &ErrorClassification, component: &str) -> f64 {
        let history = self.recovery_history.read().await;

        let relevant_attempts: Vec<_> = history.iter()
            .filter(|attempt| {
                attempt.error_context.classification == *classification &&
                attempt.error_context.component == component &&
                attempt.success.is_some()
            })
            .collect();

        if relevant_attempts.is_empty() {
            return 0.5; // Default
        }

        let successful = relevant_attempts.iter()
            .filter(|attempt| attempt.success.unwrap())
            .count();

        successful as f64 / relevant_attempts.len() as f64
    }

    /// Update system context for better decision making
    pub async fn update_system_context(&self, key: String, value: serde_json::Value) {
        // This would be used by the context analyzer
        // Implementation depends on the specific context analysis needed
    }
}

/// Recovery attempt result
#[derive(Debug)]
struct RecoveryAttemptResult {
    alternative_path_used: Option<String>,
    recovery_metadata: HashMap<String, serde_json::Value>,
}

/// Recovery result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecoveryResult {
    /// Whether recovery was successful
    pub success: bool,
    /// Strategy that was used
    pub strategy_used: RecoveryStrategy,
    /// Number of attempts made
    pub attempts_made: u32,
    /// Total recovery time in milliseconds
    pub total_time_ms: u64,
    /// Alternative path that was used
    pub alternative_path_used: Option<String>,
    /// Additional recovery metadata
    pub recovery_metadata: HashMap<String, serde_json::Value>,
}

impl Default for RecoveryConfig {
    fn default() -> Self {
        Self {
            enable_intelligent_recovery: true,
            max_retry_attempts: 3,
            base_retry_delay_ms: 1000,
            max_retry_delay_ms: 30000,
            backoff_multiplier: 2.0,
            enable_jitter: true,
            recovery_timeout_secs: 300,
            success_rate_window_minutes: 60,
            min_success_rate_for_promotion: 0.8,
            enable_alternative_paths: true,
            enable_context_awareness: true,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::compliance::ErrorSeverity;

    #[tokio::test]
    async fn test_recovery_manager_creation() {
        let config = RecoveryConfig::default();
        let manager = ErrorRecoveryManager::new(config);

        let performance = manager.get_recovery_performance().await;
        assert!(!performance.is_empty());
    }

    #[tokio::test]
    async fn test_success_rate_calculation() {
        let config = RecoveryConfig::default();
        let manager = ErrorRecoveryManager::new(config);

        let rate = manager.get_success_rate_for_error(&ErrorClassification::Transient, "test_component").await;
        assert!(rate >= 0.0 && rate <= 1.0);
    }

    #[tokio::test]
    async fn test_alternative_paths() {
        let config = RecoveryConfig::default();
        let manager = ErrorRecoveryManager::new(config);

        let paths = manager.get_alternative_paths();
        assert!(!paths.is_empty());

        // Check that we have expected paths
        let path_ids: HashSet<_> = paths.iter().map(|p| &p.path_id).collect();
        assert!(path_ids.contains("cached_response"));
        assert!(path_ids.contains("simplified_processing"));
    }
}