//! # Fault Tolerance Engine
//!
//! Advanced fault tolerance system for the license compliance platform.
//! Provides circuit breaker patterns, graceful degradation, resource management,
//! and service health monitoring to ensure system stability under adverse conditions.

use crate::{
    error::{InfinitumError, Result},
    observability::custom_metrics::{CustomMetricsManager as MetricsCollector, HealthMetrics},
};
use serde::{Deserialize, Serialize};
use std::{
    collections::{HashMap, HashSet, VecDeque},
    sync::Arc,
    time::{Duration, Instant},
};
use tokio::sync::RwLock;
use tracing::{debug, error, info, instrument, warn};
use chrono::{DateTime, Utc};

/// Fault tolerance configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FaultToleranceConfig {
    /// Enable circuit breaker pattern
    pub enable_circuit_breaker: bool,
    /// Circuit breaker failure threshold
    pub circuit_breaker_threshold: u32,
    /// Circuit breaker recovery timeout (seconds)
    pub circuit_breaker_recovery_timeout_secs: u64,
    /// Circuit breaker success threshold for recovery
    pub circuit_breaker_success_threshold: u32,
    /// Enable graceful degradation
    pub enable_graceful_degradation: bool,
    /// Degradation trigger thresholds
    pub degradation_thresholds: DegradationThresholds,
    /// Enable resource exhaustion handling
    pub enable_resource_exhaustion_handling: bool,
    /// Resource limits
    pub resource_limits: ResourceLimits,
    /// Enable service health monitoring
    pub enable_health_monitoring: bool,
    /// Health check intervals (seconds)
    pub health_check_intervals: HashMap<String, u64>,
    /// Enable automatic recovery
    pub enable_auto_recovery: bool,
    /// Recovery retry attempts
    pub recovery_retry_attempts: u32,
    /// Recovery backoff multiplier
    pub recovery_backoff_multiplier: f64,
}

/// Degradation trigger thresholds
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DegradationThresholds {
    /// CPU usage threshold for degradation (0-1)
    pub cpu_threshold: f64,
    /// Memory usage threshold for degradation (0-1)
    pub memory_threshold: f64,
    /// Error rate threshold for degradation (0-1)
    pub error_rate_threshold: f64,
    /// Response time threshold for degradation (seconds)
    pub response_time_threshold: f64,
    /// Queue size threshold for degradation
    pub queue_size_threshold: usize,
}

/// Resource limits
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceLimits {
    /// Maximum concurrent requests
    pub max_concurrent_requests: usize,
    /// Maximum memory usage (MB)
    pub max_memory_mb: usize,
    /// Maximum CPU usage (0-1)
    pub max_cpu_usage: f64,
    /// Maximum queue size
    pub max_queue_size: usize,
    /// Request timeout (seconds)
    pub request_timeout_secs: u64,
}

/// Service health status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ServiceHealthStatus {
    /// Service is healthy
    Healthy,
    /// Service is degraded
    Degraded,
    /// Service is unhealthy
    Unhealthy,
    /// Service is down
    Down,
    /// Service status unknown
    Unknown,
}

/// Circuit breaker state
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircuitBreakerState {
    /// Service name
    pub service: String,
    /// Current state
    pub state: CircuitBreakerStateEnum,
    /// Failure count
    pub failure_count: u32,
    /// Success count (for recovery)
    pub success_count: u32,
    /// Last failure time
    pub last_failure_time: Option<DateTime<Utc>>,
    /// Last success time
    pub last_success_time: Option<DateTime<Utc>>,
    /// Next retry time
    pub next_retry_time: Option<DateTime<Utc>>,
}

/// Circuit breaker states
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum CircuitBreakerStateEnum {
    /// Circuit is closed (normal operation)
    Closed,
    /// Circuit is open (failing fast)
    Open,
    /// Circuit is half-open (testing recovery)
    HalfOpen,
}

/// Degradation level
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
#[serde(rename_all = "lowercase")]
pub enum DegradationLevel {
    /// No degradation
    None,
    /// Minor degradation
    Minor,
    /// Moderate degradation
    Moderate,
    /// Severe degradation
    Severe,
    /// Critical degradation
    Critical,
}

/// Service health information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceHealth {
    /// Service name
    pub service_name: String,
    /// Health status
    pub status: ServiceHealthStatus,
    /// Response time (milliseconds)
    pub response_time_ms: Option<u64>,
    /// Error rate (0-1)
    pub error_rate: Option<f64>,
    /// Last health check time
    pub last_check: DateTime<Utc>,
    /// Next health check time
    pub next_check: DateTime<Utc>,
    /// Health check details
    pub details: HashMap<String, serde_json::Value>,
}

/// Resource usage information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceUsage {
    /// CPU usage (0-1)
    pub cpu_usage: f64,
    /// Memory usage (MB)
    pub memory_usage_mb: usize,
    /// Available memory (MB)
    pub available_memory_mb: usize,
    /// Disk usage (0-1)
    pub disk_usage: f64,
    /// Network I/O (bytes/sec)
    pub network_io_bps: u64,
    /// Active connections
    pub active_connections: usize,
    /// Queue size
    pub queue_size: usize,
    /// Timestamp
    pub timestamp: DateTime<Utc>,
}

/// Graceful degradation strategy
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DegradationStrategy {
    /// Strategy name
    pub name: String,
    /// Degradation level that triggers this strategy
    pub trigger_level: DegradationLevel,
    /// Actions to take when degrading
    pub degradation_actions: Vec<DegradationAction>,
    /// Actions to take when recovering
    pub recovery_actions: Vec<RecoveryAction>,
    /// Priority of this strategy
    pub priority: u32,
}

/// Degradation action
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DegradationAction {
    /// Action type
    pub action_type: DegradationActionType,
    /// Target service/component
    pub target: String,
    /// Action parameters
    pub parameters: HashMap<String, serde_json::Value>,
}

/// Degradation action types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum DegradationActionType {
    /// Reduce request rate
    ReduceRequestRate,
    /// Disable non-critical features
    DisableFeatures,
    /// Use cached responses
    UseCache,
    /// Switch to backup service
    SwitchToBackup,
    /// Reduce response quality
    ReduceQuality,
    /// Reject new requests
    RejectRequests,
}

/// Recovery action
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecoveryAction {
    /// Action type
    pub action_type: RecoveryActionType,
    /// Target service/component
    pub target: String,
    /// Action parameters
    pub parameters: HashMap<String, serde_json::Value>,
}

/// Recovery action types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum RecoveryActionType {
    /// Restore full functionality
    RestoreFull,
    /// Gradually increase capacity
    GradualIncrease,
    /// Enable previously disabled features
    EnableFeatures,
    /// Switch back to primary service
    SwitchToPrimary,
    /// Clear caches
    ClearCaches,
}

/// Fault tolerance engine
pub struct FaultToleranceEngine {
    config: FaultToleranceConfig,
    circuit_breakers: Arc<RwLock<HashMap<String, CircuitBreakerState>>>,
    service_health: Arc<RwLock<HashMap<String, ServiceHealth>>>,
    resource_usage: Arc<RwLock<VecDeque<ResourceUsage>>>,
    degradation_strategies: Vec<DegradationStrategy>,
    current_degradation_level: Arc<RwLock<DegradationLevel>>,
    metrics_collector: Arc<MetricsCollector>,
}

/// Recovery action (different from DegradationAction)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecoveryActionInternal {
    /// Action type
    pub action_type: RecoveryActionType,
    /// Target service/component
    pub target: String,
    /// Action parameters
    pub parameters: HashMap<String, serde_json::Value>,
}

impl FaultToleranceEngine {
    /// Create new fault tolerance engine
    pub fn new(config: FaultToleranceConfig, metrics_collector: Arc<MetricsCollector>) -> Self {
        let mut engine = Self {
            config,
            circuit_breakers: Arc::new(RwLock::new(HashMap::new())),
            service_health: Arc::new(RwLock::new(HashMap::new())),
            resource_usage: Arc::new(RwLock::new(VecDeque::with_capacity(1000))),
            degradation_strategies: Vec::new(),
            current_degradation_level: Arc::new(RwLock::new(DegradationLevel::None)),
            metrics_collector,
        };

        engine.initialize_degradation_strategies();
        engine
    }

    /// Initialize default degradation strategies
    fn initialize_degradation_strategies(&mut self) {
        // Minor degradation strategy
        self.degradation_strategies.push(DegradationStrategy {
            name: "Minor Degradation".to_string(),
            trigger_level: DegradationLevel::Minor,
            degradation_actions: vec![
                DegradationAction {
                    action_type: DegradationActionType::ReduceRequestRate,
                    target: "api".to_string(),
                    parameters: HashMap::from([
                        ("rate_limit".to_string(), serde_json::json!(0.8)),
                    ]),
                },
            ],
            recovery_actions: vec![
                RecoveryAction {
                    action_type: RecoveryActionType::GradualIncrease,
                    target: "api".to_string(),
                    parameters: HashMap::from([
                        ("increment".to_string(), serde_json::json!(0.1)),
                    ]),
                },
            ],
            priority: 1,
        });

        // Moderate degradation strategy
        self.degradation_strategies.push(DegradationStrategy {
            name: "Moderate Degradation".to_string(),
            trigger_level: DegradationLevel::Moderate,
            degradation_actions: vec![
                DegradationAction {
                    action_type: DegradationActionType::DisableFeatures,
                    target: "advanced_features".to_string(),
                    parameters: HashMap::from([
                        ("features".to_string(), serde_json::json!(["ml_processing", "deep_analysis"])),
                    ]),
                },
                DegradationAction {
                    action_type: DegradationActionType::UseCache,
                    target: "license_validation".to_string(),
                    parameters: HashMap::from([
                        ("cache_ttl".to_string(), serde_json::json!(3600)),
                    ]),
                },
            ],
            recovery_actions: vec![
                RecoveryAction {
                    action_type: RecoveryActionType::EnableFeatures,
                    target: "advanced_features".to_string(),
                    parameters: HashMap::new(),
                },
            ],
            priority: 2,
        });

        // Severe degradation strategy
        self.degradation_strategies.push(DegradationStrategy {
            name: "Severe Degradation".to_string(),
            trigger_level: DegradationLevel::Severe,
            degradation_actions: vec![
                DegradationAction {
                    action_type: DegradationActionType::SwitchToBackup,
                    target: "license_database".to_string(),
                    parameters: HashMap::from([
                        ("backup_service".to_string(), serde_json::json!("local_cache")),
                    ]),
                },
                DegradationAction {
                    action_type: DegradationActionType::ReduceQuality,
                    target: "report_generation".to_string(),
                    parameters: HashMap::from([
                        ("quality_level".to_string(), serde_json::json!("basic")),
                    ]),
                },
            ],
            recovery_actions: vec![
                RecoveryAction {
                    action_type: RecoveryActionType::SwitchToPrimary,
                    target: "license_database".to_string(),
                    parameters: HashMap::new(),
                },
                RecoveryAction {
                    action_type: RecoveryActionType::RestoreFull,
                    target: "report_generation".to_string(),
                    parameters: HashMap::new(),
                },
            ],
            priority: 3,
        });

        // Critical degradation strategy
        self.degradation_strategies.push(DegradationStrategy {
            name: "Critical Degradation".to_string(),
            trigger_level: DegradationLevel::Critical,
            degradation_actions: vec![
                DegradationAction {
                    action_type: DegradationActionType::RejectRequests,
                    target: "api".to_string(),
                    parameters: HashMap::from([
                        ("reject_message".to_string(), serde_json::json!("Service temporarily unavailable")),
                    ]),
                },
            ],
            recovery_actions: vec![
                RecoveryAction {
                    action_type: RecoveryActionType::GradualIncrease,
                    target: "api".to_string(),
                    parameters: HashMap::from([
                        ("start_rate".to_string(), serde_json::json!(0.1)),
                    ]),
                },
            ],
            priority: 4,
        });
    }

    /// Check if a service call should proceed through circuit breaker
    #[instrument(skip(self))]
    pub async fn check_circuit_breaker(&self, service: &str) -> Result<bool> {
        if !self.config.enable_circuit_breaker {
            return Ok(true);
        }

        let mut circuit_breakers = self.circuit_breakers.write().await;
        let state = circuit_breakers
            .entry(service.to_string())
            .or_insert_with(|| CircuitBreakerState {
                service: service.to_string(),
                state: CircuitBreakerStateEnum::Closed,
                failure_count: 0,
                success_count: 0,
                last_failure_time: None,
                last_success_time: None,
                next_retry_time: None,
            });

        match state.state {
            CircuitBreakerStateEnum::Closed => Ok(true),
            CircuitBreakerStateEnum::Open => {
                // Check if it's time to try again
                if let Some(next_retry) = state.next_retry_time {
                    if Utc::now() >= next_retry {
                        state.state = CircuitBreakerStateEnum::HalfOpen;
                        state.success_count = 0;
                        info!("Circuit breaker for {} moving to half-open", service);
                        Ok(true)
                    } else {
                        Ok(false)
                    }
                } else {
                    Ok(false)
                }
            }
            CircuitBreakerStateEnum::HalfOpen => Ok(true),
        }
    }

    /// Record circuit breaker success
    #[instrument(skip(self))]
    pub async fn record_circuit_breaker_success(&self, service: &str) -> Result<()> {
        if !self.config.enable_circuit_breaker {
            return Ok(());
        }

        let mut circuit_breakers = self.circuit_breakers.write().await;
        if let Some(state) = circuit_breakers.get_mut(service) {
            state.success_count += 1;
            state.last_success_time = Some(Utc::now());

            // If in half-open state and success threshold reached, close circuit
            if state.state == CircuitBreakerStateEnum::HalfOpen &&
               state.success_count >= self.config.circuit_breaker_success_threshold {
                state.state = CircuitBreakerStateEnum::Closed;
                state.failure_count = 0;
                info!("Circuit breaker for {} closed after successful recovery", service);
            }
        }

        Ok(())
    }

    /// Record circuit breaker failure
    #[instrument(skip(self))]
    pub async fn record_circuit_breaker_failure(&self, service: &str) -> Result<()> {
        if !self.config.enable_circuit_breaker {
            return Ok(());
        }

        let mut circuit_breakers = self.circuit_breakers.write().await;
        let state = circuit_breakers
            .entry(service.to_string())
            .or_insert_with(|| CircuitBreakerState {
                service: service.to_string(),
                state: CircuitBreakerStateEnum::Closed,
                failure_count: 0,
                success_count: 0,
                last_failure_time: None,
                last_success_time: None,
                next_retry_time: None,
            });

        state.failure_count += 1;
        state.last_failure_time = Some(Utc::now());

        // Check if failure threshold exceeded
        if state.failure_count >= self.config.circuit_breaker_threshold {
            state.state = CircuitBreakerStateEnum::Open;
            state.next_retry_time = Some(Utc::now() + chrono::Duration::seconds(self.config.circuit_breaker_recovery_timeout_secs as i64));
            warn!("Circuit breaker for {} opened after {} failures", service, state.failure_count);
        }

        Ok(())
    }

    /// Assess current system health and determine degradation level
    #[instrument(skip(self))]
    pub async fn assess_system_health(&self) -> Result<DegradationLevel> {
        if !self.config.enable_graceful_degradation {
            return Ok(DegradationLevel::None);
        }

        let resource_usage = self.get_current_resource_usage().await?;
        let error_rate = self.get_current_error_rate().await?;
        let response_time = self.get_current_response_time().await?;

        let mut degradation_score = 0.0;

        // CPU usage assessment
        if resource_usage.cpu_usage > self.config.degradation_thresholds.cpu_threshold {
            let excess = resource_usage.cpu_usage - self.config.degradation_thresholds.cpu_threshold;
            degradation_score += excess * 2.0;
        }

        // Memory usage assessment
        let memory_usage_ratio = resource_usage.memory_usage_mb as f64 / self.config.resource_limits.max_memory_mb as f64;
        if memory_usage_ratio > self.config.degradation_thresholds.memory_threshold {
            let excess = memory_usage_ratio - self.config.degradation_thresholds.memory_threshold;
            degradation_score += excess * 2.0;
        }

        // Error rate assessment
        if error_rate > self.config.degradation_thresholds.error_rate_threshold {
            let excess = error_rate - self.config.degradation_thresholds.error_rate_threshold;
            degradation_score += excess * 3.0;
        }

        // Response time assessment
        if let Some(avg_response_time) = response_time {
            if avg_response_time as f64 / 1000.0 > self.config.degradation_thresholds.response_time_threshold {
                let excess = (avg_response_time as f64 / 1000.0) - self.config.degradation_thresholds.response_time_threshold;
                degradation_score += excess * 1.5;
            }
        }

        // Queue size assessment
        if resource_usage.queue_size > self.config.degradation_thresholds.queue_size_threshold {
            let excess = resource_usage.queue_size as f64 / self.config.degradation_thresholds.queue_size_threshold as f64;
            degradation_score += (excess - 1.0) * 1.0;
        }

        // Determine degradation level based on score
        let degradation_level = if degradation_score >= 4.0 {
            DegradationLevel::Critical
        } else if degradation_score >= 3.0 {
            DegradationLevel::Severe
        } else if degradation_score >= 2.0 {
            DegradationLevel::Moderate
        } else if degradation_score >= 1.0 {
            DegradationLevel::Minor
        } else {
            DegradationLevel::None
        };

        // Update current degradation level
        let mut current_level = self.current_degradation_level.write().await;
        if *current_level != degradation_level {
            info!("System degradation level changed from {:?} to {:?}", *current_level, degradation_level);
            *current_level = degradation_level.clone();
        }

        Ok(degradation_level)
    }

    /// Apply graceful degradation based on current level
    #[instrument(skip(self))]
    pub async fn apply_graceful_degradation(&self, level: DegradationLevel) -> Result<()> {
        if !self.config.enable_graceful_degradation || level == DegradationLevel::None {
            return Ok(());
        }

        info!("Applying graceful degradation to level {:?}", level);

        // Find applicable strategies
        let applicable_strategies: Vec<_> = self.degradation_strategies.iter()
            .filter(|s| s.trigger_level <= level)
            .collect();

        // Sort by priority
        let mut applicable_strategies = applicable_strategies.clone();
        applicable_strategies.sort_by_key(|s| s.priority);

        // Apply degradation actions
        for strategy in applicable_strategies {
            for action in &strategy.degradation_actions {
                self.execute_degradation_action(action).await?;
            }
        }

        Ok(())
    }

    /// Execute a degradation action
    async fn execute_degradation_action(&self, action: &DegradationAction) -> Result<()> {
        match action.action_type {
            DegradationActionType::ReduceRequestRate => {
                if let Some(rate_limit) = action.parameters.get("rate_limit") {
                    if let Some(rate) = rate_limit.as_f64() {
                        info!("Reducing request rate for {} to {:.1}%", action.target, rate * 100.0);
                        // Implementation would integrate with rate limiter
                    }
                }
            }
            DegradationActionType::DisableFeatures => {
                if let Some(features) = action.parameters.get("features") {
                    if let Some(features_array) = features.as_array() {
                        let feature_list: Vec<String> = features_array.iter()
                            .filter_map(|f| f.as_str())
                            .map(|s| s.to_string())
                            .collect();
                        info!("Disabling features for {}: {:?}", action.target, feature_list);
                        // Implementation would disable specified features
                    }
                }
            }
            DegradationActionType::UseCache => {
                if let Some(ttl) = action.parameters.get("cache_ttl") {
                    if let Some(ttl_secs) = ttl.as_u64() {
                        info!("Enabling extended caching for {} with TTL {}s", action.target, ttl_secs);
                        // Implementation would adjust cache TTL
                    }
                }
            }
            DegradationActionType::SwitchToBackup => {
                if let Some(backup) = action.parameters.get("backup_service") {
                    if let Some(backup_name) = backup.as_str() {
                        info!("Switching {} to backup service: {}", action.target, backup_name);
                        // Implementation would switch to backup service
                    }
                }
            }
            DegradationActionType::ReduceQuality => {
                if let Some(quality) = action.parameters.get("quality_level") {
                    if let Some(level) = quality.as_str() {
                        info!("Reducing quality for {} to level: {}", action.target, level);
                        // Implementation would reduce response quality
                    }
                }
            }
            DegradationActionType::RejectRequests => {
                if let Some(message) = action.parameters.get("reject_message") {
                    if let Some(msg) = message.as_str() {
                        info!("Rejecting requests for {} with message: {}", action.target, msg);
                        // Implementation would start rejecting requests
                    }
                }
            }
        }

        Ok(())
    }

    /// Attempt recovery from degradation
    #[instrument(skip(self))]
    pub async fn attempt_recovery(&self) -> Result<()> {
        if !self.config.enable_auto_recovery {
            return Ok(());
        }

        let current_level = self.current_degradation_level.read().await.clone();

        if current_level == DegradationLevel::None {
            return Ok(());
        }

        // Check if system health has improved
        let new_level = self.assess_system_health().await?;

        if new_level < current_level {
            info!("System health improved, attempting recovery from {:?} to {:?}", current_level, new_level);

            // Apply recovery actions
            let applicable_strategies: Vec<_> = self.degradation_strategies.iter()
                .filter(|s| s.trigger_level >= new_level && s.trigger_level <= current_level)
                .collect();

            for strategy in applicable_strategies {
                for action in &strategy.recovery_actions {
                    self.execute_recovery_action(action).await?;
                }
            }
        }

        Ok(())
    }

    /// Execute a recovery action
    async fn execute_recovery_action(&self, action: &RecoveryAction) -> Result<()> {
        match action.action_type {
            RecoveryActionType::RestoreFull => {
                info!("Restoring full functionality for {}", action.target);
                // Implementation would restore full functionality
            }
            RecoveryActionType::GradualIncrease => {
                if let Some(increment) = action.parameters.get("increment") {
                    if let Some(inc) = increment.as_f64() {
                        info!("Gradually increasing capacity for {} by {:.1}%", action.target, inc * 100.0);
                        // Implementation would gradually increase capacity
                    }
                }
                if let Some(start_rate) = action.parameters.get("start_rate") {
                    if let Some(rate) = start_rate.as_f64() {
                        info!("Starting recovery for {} at {:.1}% capacity", action.target, rate * 100.0);
                        // Implementation would start at specified rate
                    }
                }
            }
            RecoveryActionType::EnableFeatures => {
                info!("Re-enabling features for {}", action.target);
                // Implementation would re-enable previously disabled features
            }
            RecoveryActionType::SwitchToPrimary => {
                info!("Switching {} back to primary service", action.target);
                // Implementation would switch back to primary service
            }
            RecoveryActionType::ClearCaches => {
                info!("Clearing caches for {}", action.target);
                // Implementation would clear caches
            }
        }

        Ok(())
    }

    /// Update service health information
    #[instrument(skip(self))]
    pub async fn update_service_health(&self, service_name: &str, health: ServiceHealth) -> Result<()> {
        if !self.config.enable_health_monitoring {
            return Ok(());
        }

        let mut service_health = self.service_health.write().await;
        service_health.insert(service_name.to_string(), health);

        // Update metrics
        let health_metrics = HealthMetrics {
            service_name: service_name.to_string(),
            status: health.status.clone(),
            response_time_ms: health.response_time_ms,
            error_rate: health.error_rate,
            timestamp: Utc::now(),
        };

        self.metrics_collector.record_health_metrics(health_metrics).await;

        Ok(())
    }

    /// Get service health status
    pub async fn get_service_health(&self, service_name: &str) -> Option<ServiceHealth> {
        let service_health = self.service_health.read().await;
        service_health.get(service_name).cloned()
    }

    /// Update resource usage information
    #[instrument(skip(self))]
    pub async fn update_resource_usage(&self, usage: ResourceUsage) -> Result<()> {
        let mut resource_usage = self.resource_usage.write().await;

        // Keep only recent resource usage data
        resource_usage.push_back(usage.clone());

        // Remove old entries (keep last 24 hours of data)
        let cutoff_time = Utc::now() - chrono::Duration::hours(24);
        while let Some(oldest) = resource_usage.front() {
            if oldest.timestamp < cutoff_time {
                resource_usage.pop_front();
            } else {
                break;
            }
        }

        // Check resource limits
        if usage.memory_usage_mb > self.config.resource_limits.max_memory_mb {
            warn!("Memory usage {}MB exceeds limit {}MB", usage.memory_usage_mb, self.config.resource_limits.max_memory_mb);
        }

        if usage.cpu_usage > self.config.resource_limits.max_cpu_usage {
            warn!("CPU usage {:.1}% exceeds limit {:.1}%", usage.cpu_usage * 100.0, self.config.resource_limits.max_cpu_usage * 100.0);
        }

        if usage.queue_size > self.config.resource_limits.max_queue_size {
            warn!("Queue size {} exceeds limit {}", usage.queue_size, self.config.resource_limits.max_queue_size);
        }

        Ok(())
    }

    /// Get current resource usage
    pub async fn get_current_resource_usage(&self) -> Result<ResourceUsage> {
        let resource_usage = self.resource_usage.read().await;

        resource_usage.back().cloned().ok_or_else(|| {
            InfinitumError::Internal {
                message: "No resource usage data available".to_string(),
            }
        })
    }

    /// Get current error rate
    async fn get_current_error_rate(&self) -> Result<f64> {
        // This would integrate with the metrics collector to get current error rate
        // For now, return a placeholder
        Ok(0.05) // 5% error rate
    }

    /// Get current average response time
    async fn get_current_response_time(&self) -> Result<Option<u64>> {
        // This would integrate with the metrics collector to get current response time
        // For now, return a placeholder
        Ok(Some(500)) // 500ms average response time
    }

    /// Get circuit breaker state for service
    pub async fn get_circuit_breaker_state(&self, service: &str) -> Option<CircuitBreakerState> {
        let circuit_breakers = self.circuit_breakers.read().await;
        circuit_breakers.get(service).cloned()
    }

    /// Get all circuit breaker states
    pub async fn get_all_circuit_breaker_states(&self) -> HashMap<String, CircuitBreakerState> {
        let circuit_breakers = self.circuit_breakers.read().await;
        circuit_breakers.clone()
    }

    /// Get all service health statuses
    pub async fn get_all_service_health(&self) -> HashMap<String, ServiceHealth> {
        let service_health = self.service_health.read().await;
        service_health.clone()
    }

    /// Get current degradation level
    pub async fn get_current_degradation_level(&self) -> DegradationLevel {
        self.current_degradation_level.read().await.clone()
    }

    /// Force recovery to a specific degradation level
    #[instrument(skip(self))]
    pub async fn force_recovery_to_level(&self, target_level: DegradationLevel) -> Result<()> {
        let current_level = self.current_degradation_level.read().await.clone();

        if target_level >= current_level {
            return Ok(());
        }

        info!("Forcing recovery from {:?} to {:?}", current_level, target_level);

        // Apply recovery actions for levels between target and current
        let applicable_strategies: Vec<_> = self.degradation_strategies.iter()
            .filter(|s| s.trigger_level > target_level && s.trigger_level <= current_level)
            .collect();

        for strategy in applicable_strategies {
            for action in &strategy.recovery_actions {
                self.execute_recovery_action(action).await?;
            }
        }

        // Update current level
        let mut current_level_ref = self.current_degradation_level.write().await;
        *current_level_ref = target_level;

        Ok(())
    }

    /// Add custom degradation strategy
    pub fn add_degradation_strategy(&mut self, strategy: DegradationStrategy) {
        self.degradation_strategies.push(strategy);
        // Sort by priority
        self.degradation_strategies.sort_by_key(|s| s.priority);
    }

    /// Get degradation strategies
    pub fn get_degradation_strategies(&self) -> &[DegradationStrategy] {
        &self.degradation_strategies
    }
}

impl Default for FaultToleranceConfig {
    fn default() -> Self {
        Self {
            enable_circuit_breaker: true,
            circuit_breaker_threshold: 5,
            circuit_breaker_recovery_timeout_secs: 60,
            circuit_breaker_success_threshold: 3,
            enable_graceful_degradation: true,
            degradation_thresholds: DegradationThresholds {
                cpu_threshold: 0.8,
                memory_threshold: 0.8,
                error_rate_threshold: 0.1,
                response_time_threshold: 2.0,
                queue_size_threshold: 1000,
            },
            enable_resource_exhaustion_handling: true,
            resource_limits: ResourceLimits {
                max_concurrent_requests: 1000,
                max_memory_mb: 4096,
                max_cpu_usage: 0.9,
                max_queue_size: 5000,
                request_timeout_secs: 30,
            },
            enable_health_monitoring: true,
            health_check_intervals: HashMap::from([
                ("license_database".to_string(), 30),
                ("api_gateway".to_string(), 15),
                ("ml_service".to_string(), 60),
            ]),
            enable_auto_recovery: true,
            recovery_retry_attempts: 3,
            recovery_backoff_multiplier: 2.0,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Arc;

    #[tokio::test]
    async fn test_circuit_breaker_closed_state() {
        let config = FaultToleranceConfig::default();
        let metrics_collector = Arc::new(MetricsCollector::default());
        let engine = FaultToleranceEngine::new(config, metrics_collector);

        let should_proceed = engine.check_circuit_breaker("test_service").await.unwrap();
        assert!(should_proceed);
    }

    #[tokio::test]
    async fn test_circuit_breaker_failure_handling() {
        let config = FaultToleranceConfig::default();
        let metrics_collector = Arc::new(MetricsCollector::default());
        let engine = FaultToleranceEngine::new(config, metrics_collector);

        // Record failures
        for _ in 0..5 {
            engine.record_circuit_breaker_failure("test_service").await.unwrap();
        }

        let should_proceed = engine.check_circuit_breaker("test_service").await.unwrap();
        assert!(!should_proceed); // Circuit should be open
    }

    #[tokio::test]
    async fn test_resource_usage_tracking() {
        let config = FaultToleranceConfig::default();
        let metrics_collector = Arc::new(MetricsCollector::default());
        let engine = FaultToleranceEngine::new(config, metrics_collector);

        let usage = ResourceUsage {
            cpu_usage: 0.5,
            memory_usage_mb: 2048,
            available_memory_mb: 2048,
            disk_usage: 0.3,
            network_io_bps: 1000000,
            active_connections: 50,
            queue_size: 100,
            timestamp: Utc::now(),
        };

        engine.update_resource_usage(usage.clone()).await.unwrap();

        let current_usage = engine.get_current_resource_usage().await.unwrap();
        assert_eq!(current_usage.cpu_usage, usage.cpu_usage);
    }

    #[tokio::test]
    async fn test_service_health_tracking() {
        let config = FaultToleranceConfig::default();
        let metrics_collector = Arc::new(MetricsCollector::default());
        let engine = FaultToleranceEngine::new(config, metrics_collector);

        let health = ServiceHealth {
            service_name: "test_service".to_string(),
            status: ServiceHealthStatus::Healthy,
            response_time_ms: Some(100),
            error_rate: Some(0.01),
            last_check: Utc::now(),
            next_check: Utc::now() + chrono::Duration::seconds(30),
            details: HashMap::new(),
        };

        engine.update_service_health("test_service", health.clone()).await.unwrap();

        let retrieved_health = engine.get_service_health("test_service").await.unwrap();
        assert_eq!(retrieved_health.status, ServiceHealthStatus::Healthy);
    }
}