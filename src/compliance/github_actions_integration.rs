//! # GitHub Actions Integration
//!
//! Specialized integration for GitHub Actions CI/CD platform with workflow templates,
//! artifact management, pull request integration, and security event reporting.

use crate::{
    compliance::{
        ci_cd_scanner::{CIDCScanner, CICDPlaformConfig, CICDPlaformRequest, CICDPlaformResult, CIOutputFormat, ScanMode},
        ComplianceConfig,
    },
    config::ScanningConfig,
    error::{InfinitumError, Result},
};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    path::Path,
    sync::Arc,
};
use tokio::sync::RwLock;
use tracing::{debug, info, instrument, warn};

/// GitHub Actions workflow configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitHubWorkflowConfig {
    /// Workflow name
    pub name: String,
    /// Trigger events
    pub triggers: Vec<GitHubTrigger>,
    /// Job configurations
    pub jobs: Vec<GitHubJob>,
    /// Environment variables
    pub env: HashMap<String, String>,
    /// Permissions
    pub permissions: HashMap<String, String>,
}

/// GitHub Actions trigger events
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum GitHubTrigger {
    /// Push to branches
    Push { branches: Vec<String> },
    /// Pull request events
    PullRequest { types: Vec<String> },
    /// Schedule (cron)
    Schedule { cron: String },
    /// Manual trigger
    WorkflowDispatch,
    /// Release events
    Release { types: Vec<String> },
}

/// GitHub Actions job configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitHubJob {
    /// Job name
    pub name: String,
    /// Runner type
    pub runs_on: String,
    /// Job steps
    pub steps: Vec<GitHubStep>,
    /// Job environment
    pub env: HashMap<String, String>,
    /// Job permissions
    pub permissions: Option<HashMap<String, String>>,
}

/// GitHub Actions step configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitHubStep {
    /// Step name
    pub name: String,
    /// Step uses (action reference)
    pub uses: Option<String>,
    /// Step run command
    pub run: Option<String>,
    /// Step with parameters
    pub with: Option<HashMap<String, serde_json::Value>>,
    /// Step environment
    pub env: Option<HashMap<String, String>>,
    /// Step ID
    pub id: Option<String>,
}

/// GitHub Actions integration configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitHubActionsConfig {
    /// Base CI/CD configuration
    pub base_config: CICDPlaformConfig,
    /// GitHub repository information
    pub repository: String,
    /// GitHub token (from secrets)
    pub token: Option<String>,
    /// Enable pull request comments
    pub enable_pr_comments: bool,
    /// Enable status checks
    pub enable_status_checks: bool,
    /// Enable security events
    pub enable_security_events: bool,
    /// Enable artifact upload
    pub enable_artifacts: bool,
    /// Artifact retention days
    pub artifact_retention_days: u32,
    /// Custom workflow templates
    pub workflow_templates: Vec<GitHubWorkflowTemplate>,
}

/// GitHub workflow template
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitHubWorkflowTemplate {
    /// Template name
    pub name: String,
    /// Template description
    pub description: String,
    /// Template content
    pub content: String,
    /// Applicable triggers
    pub triggers: Vec<String>,
    /// Required permissions
    pub permissions: HashMap<String, String>,
}

/// GitHub Actions integration service
pub struct GitHubActionsIntegration {
    scanner: Arc<RwLock<CIDCScanner>>,
    config: GitHubActionsConfig,
    workflow_templates: HashMap<String, GitHubWorkflowTemplate>,
}

impl GitHubActionsIntegration {
    /// Create new GitHub Actions integration
    pub fn new(
        compliance_config: ComplianceConfig,
        scanning_config: ScanningConfig,
        config: GitHubActionsConfig,
    ) -> Self {
        let scanner = Arc::new(RwLock::new(CIDCScanner::new(
            compliance_config,
            scanning_config,
        )));

        let mut workflow_templates = HashMap::new();

        // Initialize default workflow templates
        workflow_templates.insert(
            "license-scan".to_string(),
            Self::create_license_scan_template(),
        );
        workflow_templates.insert(
            "compliance-gate".to_string(),
            Self::create_compliance_gate_template(),
        );
        workflow_templates.insert(
            "security-scan".to_string(),
            Self::create_security_scan_template(),
        );

        // Add custom templates
        for template in &config.workflow_templates {
            workflow_templates.insert(template.name.clone(), template.clone());
        }

        Self {
            scanner,
            config,
            workflow_templates,
        }
    }

    /// Execute GitHub Actions scan
    #[instrument(skip(self), fields(repo = %self.config.repository))]
    pub async fn execute_scan(&self, request: GitHubActionsRequest) -> Result<GitHubActionsResult> {
        info!("Executing GitHub Actions license scan for {}", self.config.repository);

        // Convert to base CI/CD request
        let base_request = self.convert_to_base_request(request)?;

        // Execute scan using base scanner
        let scanner = self.scanner.read().await;
        let scan_result = scanner.execute_scan(base_request).await?;

        // Convert to GitHub-specific result
        let github_result = self.convert_to_github_result(scan_result).await?;

        // Handle GitHub-specific features
        if self.config.enable_pr_comments {
            self.post_pr_comment(&github_result).await?;
        }

        if self.config.enable_status_checks {
            self.update_status_check(&github_result).await?;
        }

        if self.config.enable_security_events {
            self.report_security_events(&github_result).await?;
        }

        if self.config.enable_artifacts {
            self.upload_artifacts(&github_result).await?;
        }

        Ok(github_result)
    }

    /// Generate GitHub Actions workflow file
    pub fn generate_workflow(&self, template_name: &str, custom_config: Option<HashMap<String, String>>) -> Result<String> {
        let template = self.workflow_templates.get(template_name)
            .ok_or_else(|| InfinitumError::InvalidInput {
                field: "template_name".to_string(),
                message: format!("Workflow template '{}' not found", template_name),
            })?;

        let mut content = template.content.clone();

        // Replace placeholders with configuration values
        content = content.replace("{{REPOSITORY}}", &self.config.repository);
        content = content.replace("{{ARTIFACT_RETENTION}}", &self.config.artifact_retention_days.to_string());

        // Apply custom configuration
        if let Some(custom) = custom_config {
            for (key, value) in custom {
                let placeholder = format!("{{{{{}}}}}", key.to_uppercase());
                content = content.replace(&placeholder, &value);
            }
        }

        Ok(content)
    }

    /// Get available workflow templates
    pub fn get_workflow_templates(&self) -> Vec<&GitHubWorkflowTemplate> {
        self.workflow_templates.values().collect()
    }

    /// Validate GitHub Actions configuration
    pub fn validate_config(&self) -> Result<Vec<String>> {
        let mut issues = Vec::new();

        if self.config.repository.is_empty() {
            issues.push("Repository name is required".to_string());
        }

        if self.config.base_config.min_compliance_score < 0.0 || self.config.base_config.min_compliance_score > 100.0 {
            issues.push("Minimum compliance score must be between 0 and 100".to_string());
        }

        if self.config.artifact_retention_days == 0 {
            issues.push("Artifact retention days must be greater than 0".to_string());
        }

        Ok(issues)
    }

    /// Convert GitHub Actions request to base CI/CD request
    fn convert_to_base_request(&self, request: GitHubActionsRequest) -> Result<CICDPlaformRequest> {
        let mut base_config = self.config.base_config.clone();
        base_config.output_format = CIOutputFormat::GitHubAnnotations;

        // Set up environment variables for GitHub Actions
        let mut env_vars = base_config.environment_variables.clone();
        env_vars.insert("GITHUB_REPOSITORY".to_string(), self.config.repository.clone());
        env_vars.insert("GITHUB_SHA".to_string(), request.commit_sha.clone());
        env_vars.insert("GITHUB_REF".to_string(), request.ref_name.clone());

        if let Some(pr) = &request.pull_request {
            env_vars.insert("GITHUB_HEAD_REF".to_string(), pr.head_ref.clone());
            env_vars.insert("GITHUB_BASE_REF".to_string(), pr.base_ref.clone());
        }

        base_config.environment_variables = env_vars;

        // Determine scan mode based on trigger
        base_config.scan_mode = match request.trigger_type {
            GitHubTriggerType::PullRequest => ScanMode::Incremental,
            GitHubTriggerType::Push => ScanMode::Full,
            GitHubTriggerType::Schedule => ScanMode::Full,
            GitHubTriggerType::Manual => ScanMode::Custom,
        };

        Ok(CICDPlaformRequest {
            id: request.id,
            config: base_config,
            target: request.target,
            changed_files: request.changed_files,
            pr_info: request.pull_request.map(|pr| crate::compliance::ci_cd_scanner::PullRequestInfo {
                number: pr.number,
                source_branch: pr.head_ref,
                target_branch: pr.base_ref,
                title: pr.title,
                description: pr.body,
                author: pr.user.login,
            }),
            build_info: Some(crate::compliance::ci_cd_scanner::BuildInfo {
                build_number: request.run_number.to_string(),
                build_url: Some(format!(
                    "https://github.com/{}/actions/runs/{}",
                    self.config.repository, request.run_id
                )),
                job_name: Some(request.job_name),
                pipeline_name: Some(request.workflow_name),
            }),
            metadata: request.metadata,
        })
    }

    /// Convert base result to GitHub Actions result
    async fn convert_to_github_result(&self, base_result: CICDPlaformResult) -> Result<GitHubActionsResult> {
        let mut annotations = Vec::new();
        let mut summary_comment = String::new();

        // Generate summary comment
        summary_comment.push_str("## 🔍 License Compliance Scan Results\n\n");
        summary_comment.push_str(&format!("**Status:** {}\n", match base_result.status {
            crate::compliance::ci_cd_scanner::CIScanStatus::Success => "✅ Passed",
            crate::compliance::ci_cd_scanner::CIScanStatus::Warning => "⚠️ Warning",
            crate::compliance::ci_cd_scanner::CIScanStatus::Failed => "❌ Failed",
            crate::compliance::ci_cd_scanner::CIScanStatus::Cancelled => "🚫 Cancelled",
            crate::compliance::ci_cd_scanner::CIScanStatus::Timeout => "⏰ Timeout",
        }));

        summary_comment.push_str(&format!("**Compliance Score:** {:.1}%\n", base_result.summary.compliance_score));
        summary_comment.push_str(&format!("**Files Scanned:** {}\n", base_result.summary.files_scanned));
        summary_comment.push_str(&format!("**Licenses Found:** {}\n", base_result.summary.total_licenses));

        if !base_result.issues.is_empty() {
            summary_comment.push_str(&format!("\n### Issues Found ({})\n", base_result.issues.len()));
            for issue in &base_result.issues {
                let icon = match issue.severity {
                    crate::compliance::ci_cd_scanner::CIssueSeverity::Critical => "🔴",
                    crate::compliance::ci_cd_scanner::CIssueSeverity::Error => "🔴",
                    crate::compliance::ci_cd_scanner::CIssueSeverity::Warning => "🟡",
                    crate::compliance::ci_cd_scanner::CIssueSeverity::Info => "🔵",
                };
                summary_comment.push_str(&format!("{} {}\n", icon, issue.message));
            }
        }

        if !base_result.recommendations.is_empty() {
            summary_comment.push_str(&format!("\n### Recommendations\n"));
            for rec in &base_result.recommendations {
                summary_comment.push_str(&format!("• {}\n", rec));
            }
        }

        // Convert issues to GitHub annotations
        for issue in &base_result.issues {
            annotations.push(GitHubAnnotation {
                path: issue.file_path.clone(),
                start_line: issue.line_number,
                end_line: issue.line_number,
                start_column: None,
                end_column: None,
                annotation_level: match issue.severity {
                    crate::compliance::ci_cd_scanner::CIssueSeverity::Critical => "failure",
                    crate::compliance::ci_cd_scanner::CIssueSeverity::Error => "failure",
                    crate::compliance::ci_cd_scanner::CIssueSeverity::Warning => "warning",
                    crate::compliance::ci_cd_scanner::CIssueSeverity::Info => "notice",
                }.to_string(),
                message: issue.message.clone(),
                title: Some(format!("License Issue: {}", issue.category)),
                raw_details: None,
            });
        }

        Ok(GitHubActionsResult {
            base_result,
            summary_comment,
            annotations,
            check_run_id: None,
            artifacts: Vec::new(),
        })
    }

    /// Post comment on pull request
    async fn post_pr_comment(&self, result: &GitHubActionsResult) -> Result<()> {
        if let Some(pr_info) = &result.base_result.request.pr_info {
            info!("Posting PR comment for PR #{}", pr_info.number);

            // In a real implementation, this would use the GitHub API
            // For now, we'll just log the comment that would be posted
            debug!("PR Comment:\n{}", result.summary_comment);
        }

        Ok(())
    }

    /// Update GitHub status check
    async fn update_status_check(&self, result: &GitHubActionsResult) -> Result<()> {
        let status = match result.base_result.status {
            crate::compliance::ci_cd_scanner::CIScanStatus::Success => "completed",
            crate::compliance::ci_cd_scanner::CIScanStatus::Warning => "completed",
            crate::compliance::ci_cd_scanner::CIScanStatus::Failed => "completed",
            crate::compliance::ci_cd_scanner::CIScanStatus::Cancelled => "completed",
            crate::compliance::ci_cd_scanner::CIScanStatus::Timeout => "completed",
        };

        let conclusion = match result.base_result.status {
            crate::compliance::ci_cd_scanner::CIScanStatus::Success => "success",
            crate::compliance::ci_cd_scanner::CIScanStatus::Warning => "neutral",
            crate::compliance::ci_cd_scanner::CIScanStatus::Failed => "failure",
            crate::compliance::ci_cd_scanner::CIScanStatus::Cancelled => "cancelled",
            crate::compliance::ci_cd_scanner::CIScanStatus::Timeout => "timed_out",
        };

        info!("Updating status check: {} - {}", status, conclusion);

        // In a real implementation, this would use the GitHub API
        debug!("Status check update would be sent to GitHub API");

        Ok(())
    }

    /// Report security events to GitHub
    async fn report_security_events(&self, result: &GitHubActionsResult) -> Result<()> {
        // Convert high-severity issues to security events
        let security_issues: Vec<_> = result.base_result.issues.iter()
            .filter(|issue| matches!(issue.severity,
                crate::compliance::ci_cd_scanner::CIssueSeverity::Critical |
                crate::compliance::ci_cd_scanner::CIssueSeverity::Error
            ))
            .collect();

        if !security_issues.is_empty() {
            info!("Reporting {} security events to GitHub", security_issues.len());

            // In a real implementation, this would use the GitHub Security API
            for issue in security_issues {
                debug!("Security Event: {} - {}", issue.category, issue.message);
            }
        }

        Ok(())
    }

    /// Upload artifacts to GitHub
    async fn upload_artifacts(&self, result: &GitHubActionsResult) -> Result<()> {
        info!("Uploading scan artifacts to GitHub");

        // In a real implementation, this would upload files using GitHub API
        // For now, we'll just indicate what would be uploaded
        let mut artifacts = Vec::new();

        artifacts.push(GitHubArtifact {
            name: "license-scan-results.json".to_string(),
            path: "scan-results.json".to_string(),
            content_type: "application/json".to_string(),
        });

        artifacts.push(GitHubArtifact {
            name: "compliance-report.pdf".to_string(),
            path: "compliance-report.pdf".to_string(),
            content_type: "application/pdf".to_string(),
        });

        debug!("Artifacts that would be uploaded: {:?}", artifacts);

        Ok(())
    }

    /// Create license scan workflow template
    fn create_license_scan_template() -> GitHubWorkflowTemplate {
        let content = r#"name: License Compliance Scan

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    - cron: '0 2 * * 1'  # Weekly on Monday at 2 AM UTC

jobs:
  license-scan:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
      checks: write
      security-events: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Rust
      uses: actions-rust-lang/setup-rust-toolchain@v1

    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}

    - name: Run license compliance scan
      id: license-scan
      run: |
        cargo run --release --bin infinitium-signal -- ci-scan \
          --platform github-actions \
          --output-format github-annotations \
          --fail-on-violations true \
          --min-compliance-score 80.0 \
          --enable-pr-comments true \
          --enable-status-checks true \
          --enable-artifacts true

    - name: Upload scan results
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: license-scan-results
        path: |
          scan-results.json
          compliance-report.pdf
        retention-days: {{ARTIFACT_RETENTION}}

    - name: Update pull request
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const results = JSON.parse(fs.readFileSync('scan-results.json', 'utf8'));

          const body = `## 🔍 License Compliance Scan Results

**Status:** ${results.status === 'success' ? '✅ Passed' : results.status === 'warning' ? '⚠️ Warning' : '❌ Failed'}
**Compliance Score:** ${results.summary.compliance_score}%
**Files Scanned:** ${results.summary.files_scanned}
**Licenses Found:** ${results.summary.total_licenses}

### Issues Found
${results.issues.map(issue => `- ${issue.severity.toUpperCase()}: ${issue.message}`).join('\n')}

### Recommendations
${results.recommendations.map(rec => `- ${rec}`).join('\n')}
`;

          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: body
          });
"#.to_string();

        let mut permissions = HashMap::new();
        permissions.insert("contents".to_string(), "read".to_string());
        permissions.insert("pull-requests".to_string(), "write".to_string());
        permissions.insert("checks".to_string(), "write".to_string());
        permissions.insert("security-events".to_string(), "write".to_string());

        GitHubWorkflowTemplate {
            name: "license-scan".to_string(),
            description: "Comprehensive license compliance scanning workflow".to_string(),
            content,
            triggers: vec!["push".to_string(), "pull_request".to_string(), "schedule".to_string()],
            permissions,
        }
    }

    /// Create compliance gate workflow template
    fn create_compliance_gate_template() -> GitHubWorkflowTemplate {
        let content = r#"name: Compliance Gate

on:
  pull_request:
    branches: [ main, develop ]
    types: [opened, synchronize, reopened]

jobs:
  compliance-gate:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
      checks: write
      statuses: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Rust
      uses: actions-rust-lang/setup-rust-toolchain@v1

    - name: Run compliance gate
      id: compliance-gate
      run: |
        cargo run --release --bin infinitium-signal -- ci-scan \
          --platform github-actions \
          --scan-mode incremental \
          --fail-on-violations true \
          --fail-on-compliance-issues true \
          --min-compliance-score 90.0 \
          --enable-status-checks true

    - name: Set commit status
      if: always()
      uses: actions/github-script@v7
      with:
        script: |
          const status = '${{ steps.compliance-gate.outcome }}' === 'success' ? 'success' : 'failure';
          const description = status === 'success'
            ? 'License compliance requirements met'
            : 'License compliance issues found - review required';

          github.rest.repos.createCommitStatus({
            owner: context.repo.owner,
            repo: context.repo.repo,
            sha: context.sha,
            state: status,
            description: description,
            context: 'license-compliance-gate'
          });
"#.to_string();

        let mut permissions = HashMap::new();
        permissions.insert("contents".to_string(), "read".to_string());
        permissions.insert("pull-requests".to_string(), "write".to_string());
        permissions.insert("checks".to_string(), "write".to_string());
        permissions.insert("statuses".to_string(), "write".to_string());

        GitHubWorkflowTemplate {
            name: "compliance-gate".to_string(),
            description: "Compliance gate that blocks merges on license issues".to_string(),
            content,
            triggers: vec!["pull_request".to_string()],
            permissions,
        }
    }

    /// Create security scan workflow template
    fn create_security_scan_template() -> GitHubWorkflowTemplate {
        let content = r#"name: Security License Scan

on:
  push:
    branches: [ main ]
  schedule:
    - cron: '0 3 * * *'  # Daily at 3 AM UTC

jobs:
  security-scan:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
      actions: read

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Rust
      uses: actions-rust-lang/setup-rust-toolchain@v1

    - name: Run security license scan
      id: security-scan
      run: |
        cargo run --release --bin infinitium-signal -- ci-scan \
          --platform github-actions \
          --scan-mode full \
          --enable-security-events true \
          --output-format sarif \
          --fail-on-violations true

    - name: Upload SARIF file
      if: always()
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: scan-results.sarif
        category: license-security

    - name: Create security advisory
      if: failure()
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const results = JSON.parse(fs.readFileSync('scan-results.json', 'utf8'));

          if (results.summary.high_severity_issues > 0) {
            // Create security advisory for high-severity license issues
            github.rest.securityAdvisories.createPrivateVulnerability({
              owner: context.repo.owner,
              repo: context.repo.repo,
              summary: 'License Compliance Security Issues',
              description: `Found ${results.summary.high_severity_issues} high-severity license compliance issues`,
              severity: 'high',
              identifiers: [{
                type: 'OTHER',
                value: 'LICENSE-COMPLIANCE-SECURITY'
              }]
            });
          }
"#.to_string();

        let mut permissions = HashMap::new();
        permissions.insert("contents".to_string(), "read".to_string());
        permissions.insert("security-events".to_string(), "write".to_string());
        permissions.insert("actions".to_string(), "read".to_string());

        GitHubWorkflowTemplate {
            name: "security-scan".to_string(),
            description: "Security-focused license scanning with SARIF reporting".to_string(),
            content,
            triggers: vec!["push".to_string(), "schedule".to_string()],
            permissions,
        }
    }
}

/// GitHub Actions specific request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitHubActionsRequest {
    /// Request ID
    pub id: uuid::Uuid,
    /// Target path or repository
    pub target: String,
    /// GitHub trigger type
    pub trigger_type: GitHubTriggerType,
    /// Commit SHA
    pub commit_sha: String,
    /// Reference name (branch/tag)
    pub ref_name: String,
    /// Run ID
    pub run_id: u64,
    /// Run number
    pub run_number: u64,
    /// Job name
    pub job_name: String,
    /// Workflow name
    pub workflow_name: String,
    /// Pull request information
    pub pull_request: Option<GitHubPullRequest>,
    /// Changed files (for incremental scans)
    pub changed_files: Option<Vec<String>>,
    /// Additional metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// GitHub trigger types
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum GitHubTriggerType {
    /// Push event
    Push,
    /// Pull request event
    PullRequest,
    /// Scheduled run
    Schedule,
    /// Manual trigger
    Manual,
}

/// GitHub pull request information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitHubPullRequest {
    /// PR number
    pub number: u32,
    /// Head branch
    pub head_ref: String,
    /// Base branch
    pub base_ref: String,
    /// PR title
    pub title: String,
    /// PR body
    pub body: Option<String>,
    /// PR author
    pub user: GitHubUser,
}

/// GitHub user information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitHubUser {
    /// User login
    pub login: String,
    /// User ID
    pub id: u64,
    /// User type
    pub user_type: String,
}

/// GitHub Actions result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitHubActionsResult {
    /// Base CI/CD result
    pub base_result: CICDPlaformResult,
    /// Summary comment for PR
    pub summary_comment: String,
    /// GitHub annotations
    pub annotations: Vec<GitHubAnnotation>,
    /// Check run ID
    pub check_run_id: Option<u64>,
    /// Uploaded artifacts
    pub artifacts: Vec<GitHubArtifact>,
}

/// GitHub annotation for code issues
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitHubAnnotation {
    /// File path
    pub path: Option<String>,
    /// Start line
    pub start_line: Option<u32>,
    /// End line
    pub end_line: Option<u32>,
    /// Start column
    pub start_column: Option<u32>,
    /// End column
    pub end_column: Option<u32>,
    /// Annotation level
    pub annotation_level: String,
    /// Message
    pub message: String,
    /// Title
    pub title: Option<String>,
    /// Raw details
    pub raw_details: Option<String>,
}

/// GitHub artifact information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitHubArtifact {
    /// Artifact name
    pub name: String,
    /// File path
    pub path: String,
    /// Content type
    pub content_type: String,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_github_actions_config_validation() {
        let config = GitHubActionsConfig {
            base_config: CICDPlaformConfig::default(),
            repository: "owner/repo".to_string(),
            token: None,
            enable_pr_comments: true,
            enable_status_checks: true,
            enable_security_events: true,
            enable_artifacts: true,
            artifact_retention_days: 30,
            workflow_templates: vec![],
        };

        let integration = GitHubActionsIntegration::new(
            ComplianceConfig::default(),
            ScanningConfig::default(),
            config,
        );

        let issues = integration.validate_config().unwrap();
        assert!(issues.is_empty());
    }

    #[test]
    fn test_workflow_generation() {
        let config = GitHubActionsConfig {
            base_config: CICDPlaformConfig::default(),
            repository: "test/repo".to_string(),
            token: None,
            enable_pr_comments: true,
            enable_status_checks: true,
            enable_security_events: true,
            enable_artifacts: true,
            artifact_retention_days: 30,
            workflow_templates: vec![],
        };

        let integration = GitHubActionsIntegration::new(
            ComplianceConfig::default(),
            ScanningConfig::default(),
            config,
        );

        let workflow = integration.generate_workflow("license-scan", None).unwrap();
        assert!(workflow.contains("name: License Compliance Scan"));
        assert!(workflow.contains("test/repo"));
        assert!(workflow.contains("30"));
    }
}