//! # GitLab CI Integration
//!
//! Specialized integration for GitLab CI/CD platform with pipeline templates,
//! merge request integration, artifact management, and compliance gates.

use crate::{
    compliance::{
        ci_cd_scanner::{CIDCScanner, CICDPlaformConfig, CICDPlaformRequest, CICDPlaformResult, CIOutputFormat, ScanMode},
        ComplianceConfig,
    },
    config::ScanningConfig,
    error::{InfinitumError, Result},
};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    sync::Arc,
};
use tokio::sync::RwLock;
use tracing::{debug, info, instrument, warn};

/// GitLab CI pipeline configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitLabPipelineConfig {
    /// Pipeline stages
    pub stages: Vec<String>,
    /// Job configurations
    pub jobs: Vec<GitLabJob>,
    /// Global variables
    pub variables: HashMap<String, String>,
    /// Included templates
    pub include: Option<Vec<GitLabInclude>>,
    /// Workflow rules
    pub workflow: Option<GitLabWorkflow>,
}

/// GitLab CI job configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitLabJob {
    /// Job name
    pub name: String,
    /// Job stage
    pub stage: String,
    /// Job script
    pub script: Vec<String>,
    /// Job image
    pub image: Option<String>,
    /// Job variables
    pub variables: Option<HashMap<String, String>>,
    /// Job artifacts
    pub artifacts: Option<GitLabArtifacts>,
    /// Job rules
    pub rules: Option<Vec<GitLabRule>>,
    /// Job dependencies
    pub dependencies: Option<Vec<String>>,
    /// Job needs
    pub needs: Option<Vec<String>>,
    /// Job environment
    pub environment: Option<GitLabEnvironment>,
}

/// GitLab CI artifacts configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitLabArtifacts {
    /// Artifact paths
    pub paths: Vec<String>,
    /// Expose artifacts in merge requests
    pub expose_as: Option<String>,
    /// Artifact expiration
    pub expire_in: Option<String>,
    /// When to create artifacts
    pub when: Option<String>,
    /// Reports
    pub reports: Option<GitLabReports>,
}

/// GitLab CI reports
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitLabReports {
    /// JUnit test reports
    pub junit: Option<String>,
    /// Coverage reports
    pub coverage_report: Option<GitLabCoverageReport>,
    /// Code quality reports
    pub codequality: Option<String>,
}

/// GitLab CI coverage report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitLabCoverageReport {
    /// Coverage format
    pub coverage_format: String,
    /// Path to coverage file
    pub path: String,
}

/// GitLab CI rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitLabRule {
    /// If condition
    pub if_condition: Option<String>,
    /// Changes condition
    pub changes: Option<Vec<String>>,
    /// When to run
    pub when: Option<String>,
    /// Allow failure
    pub allow_failure: Option<bool>,
}

/// GitLab CI environment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitLabEnvironment {
    /// Environment name
    pub name: String,
    /// Environment URL
    pub url: Option<String>,
    /// On stop action
    pub on_stop: Option<String>,
}

/// GitLab CI include
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitLabInclude {
    /// Template path
    pub template: Option<String>,
    /// Local file
    pub local: Option<String>,
    /// Remote file
    pub remote: Option<String>,
}

/// GitLab CI workflow
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitLabWorkflow {
    /// Workflow rules
    pub rules: Vec<GitLabRule>,
}

/// GitLab CI integration configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitLabCIConfig {
    /// Base CI/CD configuration
    pub base_config: CICDPlaformConfig,
    /// GitLab project ID
    pub project_id: String,
    /// GitLab API URL
    pub api_url: String,
    /// GitLab token
    pub token: Option<String>,
    /// Enable merge request integration
    pub enable_mr_integration: bool,
    /// Enable pipeline artifacts
    pub enable_artifacts: bool,
    /// Enable compliance gates
    pub enable_compliance_gates: bool,
    /// Artifact expiration
    pub artifact_expiration: String,
    /// Custom pipeline templates
    pub pipeline_templates: Vec<GitLabPipelineTemplate>,
}

/// GitLab pipeline template
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitLabPipelineTemplate {
    /// Template name
    pub name: String,
    /// Template description
    pub description: String,
    /// Template content
    pub content: String,
    /// Applicable stages
    pub stages: Vec<String>,
    /// Required variables
    pub variables: HashMap<String, String>,
}

/// GitLab CI integration service
pub struct GitLabCIIntegration {
    scanner: Arc<RwLock<CIDCScanner>>,
    config: GitLabCIConfig,
    pipeline_templates: HashMap<String, GitLabPipelineTemplate>,
}

impl GitLabCIIntegration {
    /// Create new GitLab CI integration
    pub fn new(
        compliance_config: ComplianceConfig,
        scanning_config: ScanningConfig,
        config: GitLabCIConfig,
    ) -> Self {
        let scanner = Arc::new(RwLock::new(CIDCScanner::new(
            compliance_config,
            scanning_config,
        )));

        let mut pipeline_templates = HashMap::new();

        // Initialize default pipeline templates
        pipeline_templates.insert(
            "license-scan".to_string(),
            Self::create_license_scan_template(),
        );
        pipeline_templates.insert(
            "compliance-gate".to_string(),
            Self::create_compliance_gate_template(),
        );
        pipeline_templates.insert(
            "security-scan".to_string(),
            Self::create_security_scan_template(),
        );

        // Add custom templates
        for template in &config.pipeline_templates {
            pipeline_templates.insert(template.name.clone(), template.clone());
        }

        Self {
            scanner,
            config,
            pipeline_templates,
        }
    }

    /// Execute GitLab CI scan
    #[instrument(skip(self), fields(project_id = %self.config.project_id))]
    pub async fn execute_scan(&self, request: GitLabCIRequest) -> Result<GitLabCIResult> {
        info!("Executing GitLab CI license scan for project {}", self.config.project_id);

        // Convert to base CI/CD request
        let base_request = self.convert_to_base_request(request)?;

        // Execute scan using base scanner
        let scanner = self.scanner.read().await;
        let scan_result = scanner.execute_scan(base_request).await?;

        // Convert to GitLab-specific result
        let gitlab_result = self.convert_to_gitlab_result(scan_result).await?;

        // Handle GitLab-specific features
        if self.config.enable_mr_integration {
            self.update_merge_request(&gitlab_result).await?;
        }

        if self.config.enable_artifacts {
            self.upload_artifacts(&gitlab_result).await?;
        }

        if self.config.enable_compliance_gates {
            self.enforce_compliance_gate(&gitlab_result).await?;
        }

        Ok(gitlab_result)
    }

    /// Generate GitLab CI pipeline file
    pub fn generate_pipeline(&self, template_name: &str, custom_config: Option<HashMap<String, String>>) -> Result<String> {
        let template = self.pipeline_templates.get(template_name)
            .ok_or_else(|| InfinitumError::InvalidInput {
                field: "template_name".to_string(),
                message: format!("Pipeline template '{}' not found", template_name),
            })?;

        let mut content = template.content.clone();

        // Replace placeholders with configuration values
        content = content.replace("{{PROJECT_ID}}", &self.config.project_id);
        content = content.replace("{{ARTIFACT_EXPIRATION}}", &self.config.artifact_expiration);

        // Apply custom configuration
        if let Some(custom) = custom_config {
            for (key, value) in custom {
                let placeholder = format!("{{{{{}}}}}", key.to_uppercase());
                content = content.replace(&placeholder, &value);
            }
        }

        Ok(content)
    }

    /// Get available pipeline templates
    pub fn get_pipeline_templates(&self) -> Vec<&GitLabPipelineTemplate> {
        self.pipeline_templates.values().collect()
    }

    /// Validate GitLab CI configuration
    pub fn validate_config(&self) -> Result<Vec<String>> {
        let mut issues = Vec::new();

        if self.config.project_id.is_empty() {
            issues.push("Project ID is required".to_string());
        }

        if self.config.api_url.is_empty() {
            issues.push("API URL is required".to_string());
        }

        if self.config.base_config.min_compliance_score < 0.0 || self.config.base_config.min_compliance_score > 100.0 {
            issues.push("Minimum compliance score must be between 0 and 100".to_string());
        }

        Ok(issues)
    }

    /// Convert GitLab CI request to base CI/CD request
    fn convert_to_base_request(&self, request: GitLabCIRequest) -> Result<CICDPlaformRequest> {
        let mut base_config = self.config.base_config.clone();
        base_config.output_format = CIOutputFormat::GitLabReports;

        // Set up environment variables for GitLab CI
        let mut env_vars = base_config.environment_variables.clone();
        env_vars.insert("CI_PROJECT_ID".to_string(), self.config.project_id.clone());
        env_vars.insert("CI_COMMIT_SHA".to_string(), request.commit_sha.clone());
        env_vars.insert("CI_COMMIT_REF_NAME".to_string(), request.ref_name.clone());
        env_vars.insert("CI_PIPELINE_ID".to_string(), request.pipeline_id.to_string());

        if let Some(mr) = &request.merge_request {
            env_vars.insert("CI_MERGE_REQUEST_TARGET_BRANCH_NAME".to_string(), mr.target_branch.clone());
            env_vars.insert("CI_MERGE_REQUEST_SOURCE_BRANCH_NAME".to_string(), mr.source_branch.clone());
        }

        base_config.environment_variables = env_vars;

        // Determine scan mode based on trigger
        base_config.scan_mode = match request.trigger_type {
            GitLabTriggerType::MergeRequest => ScanMode::Incremental,
            GitLabTriggerType::Push => ScanMode::Full,
            GitLabTriggerType::Schedule => ScanMode::Full,
            GitLabTriggerType::Manual => ScanMode::Custom,
        };

        Ok(CICDPlaformRequest {
            id: request.id,
            config: base_config,
            target: request.target,
            changed_files: request.changed_files,
            pr_info: request.merge_request.map(|mr| crate::compliance::ci_cd_scanner::PullRequestInfo {
                number: mr.iid,
                source_branch: mr.source_branch,
                target_branch: mr.target_branch,
                title: mr.title,
                description: mr.description,
                author: mr.author.username,
            }),
            build_info: Some(crate::compliance::ci_cd_scanner::BuildInfo {
                build_number: request.pipeline_id.to_string(),
                build_url: Some(format!(
                    "{}/pipelines/{}",
                    self.config.api_url.trim_end_matches("/api/v4"),
                    request.pipeline_id
                )),
                job_name: Some(request.job_name),
                pipeline_name: Some(request.pipeline_name),
            }),
            metadata: request.metadata,
        })
    }

    /// Convert base result to GitLab CI result
    async fn convert_to_gitlab_result(&self, base_result: CICDPlaformResult) -> Result<GitLabCIResult> {
        let mut junit_report = String::new();
        let mut codequality_report = Vec::new();

        // Generate JUnit report
        junit_report.push_str("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
        junit_report.push_str("<testsuites>\n");
        junit_report.push_str(&format!("  <testsuite name=\"License Compliance\" tests=\"{}\" failures=\"{}\">\n",
            base_result.issues.len(),
            base_result.issues.iter().filter(|i| i.severity == crate::compliance::ci_cd_scanner::CIssueSeverity::Error).count()
        ));

        // Generate code quality report
        for issue in &base_result.issues {
            // JUnit test case
            junit_report.push_str(&format!("    <testcase name=\"{}\" classname=\"LicenseCompliance\">\n",
                issue.message.replace("\"", "&quot;")
            ));

            if issue.severity == crate::compliance::ci_cd_scanner::CIssueSeverity::Error {
                junit_report.push_str(&format!("      <failure message=\"{}\">{}</failure>\n",
                    issue.message.replace("\"", "&quot;"),
                    issue.message
                ));
            }

            junit_report.push_str("    </testcase>\n");

            // Code quality issue
            if let Some(file_path) = &issue.file_path {
                codequality_report.push(GitLabCodeQualityIssue {
                    description: issue.message.clone(),
                    check_name: "License Compliance".to_string(),
                    fingerprint: format!("{:x}", md5::compute(format!("{}:{}", file_path, issue.message))),
                    severity: match issue.severity {
                        crate::compliance::ci_cd_scanner::CIssueSeverity::Critical => "critical",
                        crate::compliance::ci_cd_scanner::CIssueSeverity::Error => "major",
                        crate::compliance::ci_cd_scanner::CIssueSeverity::Warning => "minor",
                        crate::compliance::ci_cd_scanner::CIssueSeverity::Info => "info",
                    }.to_string(),
                    location: GitLabLocation {
                        path: file_path.clone(),
                        lines: issue.line_number.map(|l| GitLabLines {
                            begin: l,
                        }),
                    },
                });
            }
        }

        junit_report.push_str("  </testsuite>\n");
        junit_report.push_str("</testsuites>\n");

        Ok(GitLabCIResult {
            base_result,
            junit_report,
            codequality_report,
            test_results: vec![],
            artifacts: vec![],
        })
    }

    /// Update merge request with scan results
    async fn update_merge_request(&self, result: &GitLabCIResult) -> Result<()> {
        if let Some(mr_info) = &result.base_result.request.pr_info {
            info!("Updating merge request !{} with scan results", mr_info.number);

            // Create discussion note with scan results
            let discussion_body = self.format_mr_discussion(&result.base_result);

            // In a real implementation, this would use the GitLab API
            debug!("MR discussion that would be posted:\n{}", discussion_body);
        }

        Ok(())
    }

    /// Upload artifacts to GitLab
    async fn upload_artifacts(&self, result: &GitLabCIResult) -> Result<()> {
        info!("Uploading scan artifacts to GitLab");

        // In a real implementation, this would upload files using GitLab API
        let mut artifacts = Vec::new();

        artifacts.push(GitLabArtifact {
            name: "license-scan-results.json".to_string(),
            path: "scan-results.json".to_string(),
            content_type: "application/json".to_string(),
        });

        artifacts.push(GitLabArtifact {
            name: "junit-report.xml".to_string(),
            path: "junit-report.xml".to_string(),
            content_type: "application/xml".to_string(),
        });

        artifacts.push(GitLabArtifact {
            name: "codequality-report.json".to_string(),
            path: "codequality-report.json".to_string(),
            content_type: "application/json".to_string(),
        });

        debug!("Artifacts that would be uploaded: {:?}", artifacts);

        Ok(())
    }

    /// Enforce compliance gate
    async fn enforce_compliance_gate(&self, result: &GitLabCIResult) -> Result<()> {
        if result.base_result.summary.should_fail_build {
            info!("Compliance gate failed - blocking pipeline");

            // In a real implementation, this would fail the GitLab CI job
            return Err(InfinitumError::ComplianceGateFailed {
                reason: result.base_result.summary.failure_reason.clone()
                    .unwrap_or_else(|| "Compliance requirements not met".to_string()),
            });
        }

        info!("Compliance gate passed");
        Ok(())
    }

    /// Format merge request discussion
    fn format_mr_discussion(&self, result: &CICDPlaformResult) -> String {
        let mut discussion = String::new();

        discussion.push_str("## 🔍 License Compliance Scan Results\n\n");
        discussion.push_str(&format!("**Status:** {}\n", match result.status {
            crate::compliance::ci_cd_scanner::CIScanStatus::Success => "✅ Passed",
            crate::compliance::ci_cd_scanner::CIScanStatus::Warning => "⚠️ Warning",
            crate::compliance::ci_cd_scanner::CIScanStatus::Failed => "❌ Failed",
            crate::compliance::ci_cd_scanner::CIScanStatus::Cancelled => "🚫 Cancelled",
            crate::compliance::ci_cd_scanner::CIScanStatus::Timeout => "⏰ Timeout",
        }));

        discussion.push_str(&format!("**Compliance Score:** {:.1}%\n", result.summary.compliance_score));
        discussion.push_str(&format!("**Files Scanned:** {}\n", result.summary.files_scanned));
        discussion.push_str(&format!("**Licenses Found:** {}\n", result.summary.total_licenses));

        if !result.issues.is_empty() {
            discussion.push_str(&format!("\n### Issues Found ({})\n", result.issues.len()));
            for issue in &result.issues {
                let icon = match issue.severity {
                    crate::compliance::ci_cd_scanner::CIssueSeverity::Critical => "🔴",
                    crate::compliance::ci_cd_scanner::CIssueSeverity::Error => "🔴",
                    crate::compliance::ci_cd_scanner::CIssueSeverity::Warning => "🟡",
                    crate::compliance::ci_cd_scanner::CIssueSeverity::Info => "🔵",
                };
                discussion.push_str(&format!("{} **{}**: {}\n", icon, issue.category, issue.message));
            }
        }

        if !result.recommendations.is_empty() {
            discussion.push_str(&format!("\n### Recommendations\n"));
            for rec in &result.recommendations {
                discussion.push_str(&format!("• {}\n", rec));
            }
        }

        discussion
    }

    /// Create license scan pipeline template
    fn create_license_scan_template() -> GitLabPipelineTemplate {
        let content = r#"stages:
  - test
  - compliance

variables:
  RUST_VERSION: "1.80"
  CARGO_INCREMENTAL: "0"
  CARGO_PROFILE_RELEASE_LTO: "true"

license_scan:
  stage: test
  image: rust:${RUST_VERSION}
  before_script:
    - apt-get update && apt-get install -y git
    - rustup component add rustfmt clippy
  script:
    - echo "Running license compliance scan..."
    - cargo run --release --bin infinitium-signal -- ci-scan --platform gitlab-ci --output-format gitlab-reports --fail-on-violations true --min-compliance-score 80.0 --enable-mr-integration true --enable-artifacts true
  artifacts:
    reports:
      junit: junit-report.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
    paths:
      - scan-results.json
      - compliance-report.pdf
    expire_in: {{ARTIFACT_EXPIRATION}}
    expose_as: "License Scan Results"
  only:
    - merge_requests
    - main
    - develop
  except:
    - tags

license_compliance_gate:
  stage: compliance
  image: rust:${RUST_VERSION}
  script:
    - echo "Enforcing license compliance gate..."
    - cargo run --release --bin infinitium-signal -- ci-scan --platform gitlab-ci --scan-mode incremental --fail-on-violations true --fail-on-compliance-issues true --min-compliance-score 90.0 --enable-compliance-gates true
  dependencies:
    - license_scan
  only:
    - merge_requests
  except:
    - tags
"#.to_string();

        let mut variables = HashMap::new();
        variables.insert("RUST_VERSION".to_string(), "1.80".to_string());

        GitLabPipelineTemplate {
            name: "license-scan".to_string(),
            description: "Comprehensive license compliance scanning pipeline".to_string(),
            content,
            stages: vec!["test".to_string(), "compliance".to_string()],
            variables,
        }
    }

    /// Create compliance gate pipeline template
    fn create_compliance_gate_template() -> GitLabPipelineTemplate {
        let content = r#"stages:
  - compliance

license_compliance_gate:
  stage: compliance
  image: rust:1.80
  script:
    - echo "Running compliance gate check..."
    - cargo run --release --bin infinitium-signal -- ci-scan --platform gitlab-ci --scan-mode incremental --fail-on-violations true --fail-on-compliance-issues true --min-compliance-score 90.0 --enable-compliance-gates true
  rules:
    - if: "$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == \"main\" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == \"develop\""
      when: always
    - when: manual
  allow_failure: false
  timeout: 10m
"#.to_string();

        let variables = HashMap::new();

        GitLabPipelineTemplate {
            name: "compliance-gate".to_string(),
            description: "Compliance gate that blocks merges on license issues".to_string(),
            content,
            stages: vec!["compliance".to_string()],
            variables,
        }
    }

    /// Create security scan pipeline template
    fn create_security_scan_template() -> GitLabPipelineTemplate {
        let content = r#"stages:
  - security

license_security_scan:
  stage: security
  image: rust:1.80
  script:
    - echo "Running license security scan..."
    - cargo run --release --bin infinitium-signal -- ci-scan --platform gitlab-ci --scan-mode full --enable-security-events true --output-format sarif --fail-on-violations true
  artifacts:
    reports:
      codequality: codequality-report.json
    paths:
      - scan-results.sarif
      - security-report.json
    expire_in: 1 week
  rules:
    - if: "$CI_COMMIT_REF_NAME == \"main\""
      when: always
    - if: "$CI_PIPELINE_SOURCE == \"schedule\""
      when: always
    - when: manual
  timeout: 15m
"#.to_string();

        let variables = HashMap::new();

        GitLabPipelineTemplate {
            name: "security-scan".to_string(),
            description: "Security-focused license scanning with code quality reporting".to_string(),
            content,
            stages: vec!["security".to_string()],
            variables,
        }
    }
}

/// GitLab CI specific request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitLabCIRequest {
    /// Request ID
    pub id: uuid::Uuid,
    /// Target path or repository
    pub target: String,
    /// GitLab trigger type
    pub trigger_type: GitLabTriggerType,
    /// Commit SHA
    pub commit_sha: String,
    /// Reference name (branch/tag)
    pub ref_name: String,
    /// Pipeline ID
    pub pipeline_id: u64,
    /// Job name
    pub job_name: String,
    /// Pipeline name
    pub pipeline_name: String,
    /// Merge request information
    pub merge_request: Option<GitLabMergeRequest>,
    /// Changed files (for incremental scans)
    pub changed_files: Option<Vec<String>>,
    /// Additional metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// GitLab trigger types
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum GitLabTriggerType {
    /// Push event
    Push,
    /// Merge request event
    MergeRequest,
    /// Scheduled pipeline
    Schedule,
    /// Manual trigger
    Manual,
}

/// GitLab merge request information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitLabMergeRequest {
    /// MR IID
    pub iid: u32,
    /// Source branch
    pub source_branch: String,
    /// Target branch
    pub target_branch: String,
    /// MR title
    pub title: String,
    /// MR description
    pub description: Option<String>,
    /// MR author
    pub author: GitLabUser,
}

/// GitLab user information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitLabUser {
    /// User username
    pub username: String,
    /// User ID
    pub id: u64,
    /// User name
    pub name: String,
}

/// GitLab CI result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitLabCIResult {
    /// Base CI/CD result
    pub base_result: CICDPlaformResult,
    /// JUnit test report XML
    pub junit_report: String,
    /// Code quality report
    pub codequality_report: Vec<GitLabCodeQualityIssue>,
    /// Test results
    pub test_results: Vec<GitLabTestResult>,
    /// Uploaded artifacts
    pub artifacts: Vec<GitLabArtifact>,
}

/// GitLab code quality issue
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitLabCodeQualityIssue {
    /// Issue description
    pub description: String,
    /// Check name
    pub check_name: String,
    /// Issue fingerprint
    pub fingerprint: String,
    /// Issue severity
    pub severity: String,
    /// Issue location
    pub location: GitLabLocation,
}

/// GitLab location
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitLabLocation {
    /// File path
    pub path: String,
    /// Line information
    pub lines: Option<GitLabLines>,
}

/// GitLab lines
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitLabLines {
    /// Beginning line
    pub begin: u32,
}

/// GitLab test result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitLabTestResult {
    /// Test name
    pub name: String,
    /// Test status
    pub status: String,
    /// Test duration
    pub duration: Option<f64>,
    /// Test message
    pub message: Option<String>,
}

/// GitLab artifact information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitLabArtifact {
    /// Artifact name
    pub name: String,
    /// File path
    pub path: String,
    /// Content type
    pub content_type: String,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_gitlab_ci_config_validation() {
        let config = GitLabCIConfig {
            base_config: CICDPlaformConfig::default(),
            project_id: "123".to_string(),
            api_url: "https://gitlab.com/api/v4".to_string(),
            token: None,
            enable_mr_integration: true,
            enable_artifacts: true,
            enable_compliance_gates: true,
            artifact_expiration: "1 week".to_string(),
            pipeline_templates: vec![],
        };

        let integration = GitLabCIIntegration::new(
            ComplianceConfig::default(),
            ScanningConfig::default(),
            config,
        );

        let issues = integration.validate_config().unwrap();
        assert!(issues.is_empty());
    }

    #[test]
    fn test_pipeline_generation() {
        let config = GitLabCIConfig {
            base_config: CICDPlaformConfig::default(),
            project_id: "123".to_string(),
            api_url: "https://gitlab.com/api/v4".to_string(),
            token: None,
            enable_mr_integration: true,
            enable_artifacts: true,
            enable_compliance_gates: true,
            artifact_expiration: "1 week".to_string(),
            pipeline_templates: vec![],
        };

        let integration = GitLabCIIntegration::new(
            ComplianceConfig::default(),
            ScanningConfig::default(),
            config,
        );

        let pipeline = integration.generate_pipeline("license-scan", None).unwrap();
        assert!(pipeline.contains("stages:"));
        assert!(pipeline.contains("license_scan"));
        assert!(pipeline.contains("123"));
    }
}