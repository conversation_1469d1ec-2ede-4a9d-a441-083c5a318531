//! # Global License Registry
//!
//! Centralized license data management with multiple sources.
//! Provides unified access, metadata enrichment, and search capabilities.

use crate::{
    compliance::license_update_processor::{LicenseData, UpdateSource},
    error::{InfinitumError, Result},
};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::{
    collections::{HashMap, HashSet},
    sync::Arc,
};
use tokio::sync::RwLock;
use tracing::{error, info, warn};

/// Registry entry for a license
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegistryEntry {
    /// Primary license data
    pub license: LicenseData,
    /// Alternative identifiers
    pub alternative_ids: HashSet<String>,
    /// Cross-references to other sources
    pub cross_references: HashMap<UpdateSource, String>,
    /// Enriched metadata
    pub enriched_metadata: HashMap<String, serde_json::Value>,
    /// Tags for categorization
    pub tags: HashSet<String>,
    /// Last synchronized timestamp
    pub last_synchronized: DateTime<Utc>,
    /// Data quality score (0.0-1.0)
    pub quality_score: f64,
}

/// Search query for licenses
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LicenseSearchQuery {
    /// Search text (name, ID, or keywords)
    pub text: Option<String>,
    /// Filter by OSI approval status
    pub osi_approved: Option<bool>,
    /// Filter by deprecated status
    pub deprecated: Option<bool>,
    /// Filter by tags
    pub tags: Option<HashSet<String>>,
    /// Filter by sources
    pub sources: Option<HashSet<UpdateSource>>,
    /// Minimum quality score
    pub min_quality_score: Option<f64>,
    /// Limit results
    pub limit: Option<usize>,
    /// Offset for pagination
    pub offset: Option<usize>,
}

/// Search result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    /// Matching license entries
    pub entries: Vec<RegistryEntry>,
    /// Total number of matches
    pub total_count: usize,
    /// Search query used
    pub query: LicenseSearchQuery,
    /// Search execution time in milliseconds
    pub execution_time_ms: u64,
}

/// Registry statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegistryStats {
    /// Total licenses in registry
    pub total_licenses: usize,
    /// OSI approved licenses
    pub osi_approved_count: usize,
    /// Deprecated licenses
    pub deprecated_count: usize,
    /// Licenses by source
    pub licenses_by_source: HashMap<UpdateSource, usize>,
    /// Average quality score
    pub average_quality_score: f64,
    /// Last updated timestamp
    pub last_updated: Option<DateTime<Utc>>,
    /// Total cross-references
    pub total_cross_references: usize,
    /// Total tags
    pub total_tags: usize,
}

/// Enrichment rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnrichmentRule {
    /// Rule name
    pub name: String,
    /// Rule description
    pub description: String,
    /// Source to enrich from
    pub source: UpdateSource,
    /// Target field to enrich
    pub target_field: String,
    /// Enrichment logic (could be a script or function)
    pub enrichment_logic: String,
    /// Priority (higher numbers = higher priority)
    pub priority: u32,
    /// Enabled status
    pub enabled: bool,
}

/// Registry configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegistryConfig {
    /// Enable automatic enrichment
    pub enable_auto_enrichment: bool,
    /// Enable cross-referencing
    pub enable_cross_referencing: bool,
    /// Enable quality scoring
    pub enable_quality_scoring: bool,
    /// Minimum quality score threshold
    pub min_quality_threshold: f64,
    /// Enable caching
    pub enable_caching: bool,
    /// Cache TTL in seconds
    pub cache_ttl_seconds: u64,
    /// Maximum search results
    pub max_search_results: usize,
    /// Enable detailed logging
    pub enable_detailed_logging: bool,
}

/// Global License Registry
pub struct GlobalLicenseRegistry {
    /// Configuration
    config: RegistryConfig,
    /// License registry storage
    registry: Arc<RwLock<HashMap<String, RegistryEntry>>>,
    /// Search index for fast lookups
    search_index: Arc<RwLock<HashMap<String, HashSet<String>>>>,
    /// Enrichment rules
    enrichment_rules: Arc<RwLock<Vec<EnrichmentRule>>>,
    /// Cache for search results
    search_cache: Arc<RwLock<HashMap<String, (SearchResult, DateTime<Utc>)>>>,
    /// Registry statistics
    stats: Arc<RwLock<RegistryStats>>,
}

impl GlobalLicenseRegistry {
    /// Create new global license registry
    pub fn new(config: RegistryConfig) -> Self {
        Self {
            config,
            registry: Arc::new(RwLock::new(HashMap::new())),
            search_index: Arc::new(RwLock::new(HashMap::new())),
            enrichment_rules: Arc::new(RwLock::new(Vec::new())),
            search_cache: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(RwLock::new(RegistryStats {
                total_licenses: 0,
                osi_approved_count: 0,
                deprecated_count: 0,
                licenses_by_source: HashMap::new(),
                average_quality_score: 0.0,
                last_updated: None,
                total_cross_references: 0,
                total_tags: 0,
            })),
        }
    }

    /// Add or update license in registry
    pub async fn add_or_update_license(
        &self,
        license_data: LicenseData,
        source: UpdateSource,
    ) -> Result<()> {
        let mut registry = self.registry.write().await;
        let mut search_index = self.search_index.write().await;

        let license_id = license_data.id.clone();
        let entry_exists = registry.contains_key(&license_id);

        // Create or update registry entry
        let entry = if let Some(existing_entry) = registry.get_mut(&license_id) {
            // Update existing entry
            self.update_existing_entry(existing_entry, license_data, source).await?;
            existing_entry.clone()
        } else {
            // Create new entry
            self.create_new_entry(license_data, source).await?
        };

        registry.insert(license_id.clone(), entry.clone());

        // Update search index
        self.update_search_index(&license_id, &entry, &mut search_index).await;

        // Update statistics
        self.update_statistics(&registry).await;

        if entry_exists {
            info!("Updated license {} in registry", license_id);
        } else {
            info!("Added new license {} to registry", license_id);
        }

        Ok(())
    }

    /// Get license by ID
    pub async fn get_license(&self, license_id: &str) -> Result<Option<RegistryEntry>> {
        let registry = self.registry.read().await;
        Ok(registry.get(license_id).cloned())
    }

    /// Search licenses
    pub async fn search_licenses(&self, query: LicenseSearchQuery) -> Result<SearchResult> {
        let start_time = Utc::now();

        // Check cache first
        if self.config.enable_caching {
            let cache_key = self.generate_cache_key(&query);
            if let Some((cached_result, cached_at)) = self.search_cache.read().await.get(&cache_key) {
                let age = Utc::now().signed_duration_since(*cached_at);
                if age.num_seconds() < self.config.cache_ttl_seconds as i64 {
                    return Ok(cached_result.clone());
                }
            }
        }

        let registry = self.registry.read().await;
        let mut matching_entries = Vec::new();

        // Apply filters
        for entry in registry.values() {
            if self.matches_query(entry, &query) {
                matching_entries.push(entry.clone());
            }
        }

        // Sort by quality score (highest first)
        matching_entries.sort_by(|a, b| {
            b.quality_score.partial_cmp(&a.quality_score).unwrap_or(std::cmp::Ordering::Equal)
        });

        // Apply pagination
        let total_count = matching_entries.len();
        let offset = query.offset.unwrap_or(0);
        let limit = query.limit.unwrap_or(self.config.max_search_results).min(self.config.max_search_results);
        let end = (offset + limit).min(matching_entries.len());
        let paginated_entries = matching_entries[offset..end].to_vec();

        let execution_time = Utc::now().signed_duration_since(start_time).num_milliseconds() as u64;

        let result = SearchResult {
            entries: paginated_entries,
            total_count,
            query: query.clone(),
            execution_time_ms: execution_time,
        };

        // Cache result
        if self.config.enable_caching {
            let cache_key = self.generate_cache_key(&query);
            self.search_cache.write().await.insert(cache_key, (result.clone(), Utc::now()));
        }

        Ok(result)
    }

    /// Add enrichment rule
    pub async fn add_enrichment_rule(&self, rule: EnrichmentRule) -> Result<()> {
        let mut rules = self.enrichment_rules.write().await;
        rules.push(rule);
        // Sort by priority (highest first)
        rules.sort_by(|a, b| b.priority.cmp(&a.priority));
        Ok(())
    }

    /// Apply enrichment rules to a license
    pub async fn enrich_license(&self, license_id: &str) -> Result<()> {
        if !self.config.enable_auto_enrichment {
            return Ok(());
        }

        let mut registry = self.registry.write().await;
        if let Some(entry) = registry.get_mut(license_id) {
            let rules = self.enrichment_rules.read().await;

            for rule in rules.iter().filter(|r| r.enabled) {
                self.apply_enrichment_rule(entry, rule).await?;
            }

            // Recalculate quality score
            entry.quality_score = self.calculate_quality_score(entry);
        }

        Ok(())
    }

    /// Add cross-reference between licenses
    pub async fn add_cross_reference(
        &self,
        license_id: &str,
        source: UpdateSource,
        reference_id: &str,
    ) -> Result<()> {
        if !self.config.enable_cross_referencing {
            return Ok(());
        }

        let mut registry = self.registry.write().await;
        if let Some(entry) = registry.get_mut(license_id) {
            entry.cross_references.insert(source, reference_id.to_string());
            entry.last_synchronized = Utc::now();
        }

        Ok(())
    }

    /// Get registry statistics
    pub async fn get_stats(&self) -> RegistryStats {
        self.stats.read().await.clone()
    }

    /// Get all license IDs
    pub async fn get_all_license_ids(&self) -> Vec<String> {
        let registry = self.registry.read().await;
        registry.keys().cloned().collect()
    }

    /// Remove license from registry
    pub async fn remove_license(&self, license_id: &str) -> Result<bool> {
        let mut registry = self.registry.write().await;
        let mut search_index = self.search_index.write().await;

        if let Some(_) = registry.remove(license_id) {
            // Remove from search index
            self.remove_from_search_index(license_id, &mut search_index).await;

            // Update statistics
            self.update_statistics(&registry).await;

            info!("Removed license {} from registry", license_id);
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// Clear registry
    pub async fn clear(&self) -> Result<()> {
        let mut registry = self.registry.write().await;
        let mut search_index = self.search_index.write().await;
        let mut search_cache = self.search_cache.write().await;

        registry.clear();
        search_index.clear();
        search_cache.clear();

        let mut stats = self.stats.write().await;
        *stats = RegistryStats {
            total_licenses: 0,
            osi_approved_count: 0,
            deprecated_count: 0,
            licenses_by_source: HashMap::new(),
            average_quality_score: 0.0,
            last_updated: Some(Utc::now()),
            total_cross_references: 0,
            total_tags: 0,
        };

        info!("Cleared license registry");
        Ok(())
    }

    /// Create new registry entry
    async fn create_new_entry(&self, license_data: LicenseData, source: UpdateSource) -> Result<RegistryEntry> {
        let mut entry = RegistryEntry {
            license: license_data,
            alternative_ids: HashSet::new(),
            cross_references: HashMap::new(),
            enriched_metadata: HashMap::new(),
            tags: HashSet::new(),
            last_synchronized: Utc::now(),
            quality_score: 0.5, // Default quality score
        };

        // Add initial cross-reference
        entry.cross_references.insert(source, entry.license.id.clone());

        // Calculate initial quality score
        entry.quality_score = self.calculate_quality_score(&entry);

        Ok(entry)
    }

    /// Update existing registry entry
    async fn update_existing_entry(
        &self,
        entry: &mut RegistryEntry,
        license_data: LicenseData,
        source: UpdateSource,
    ) -> Result<()> {
        // Update license data
        entry.license = license_data;
        entry.last_synchronized = Utc::now();

        // Update cross-reference
        entry.cross_references.insert(source, entry.license.id.clone());

        // Recalculate quality score
        entry.quality_score = self.calculate_quality_score(entry);

        Ok(())
    }

    /// Update search index for a license
    async fn update_search_index(
        &self,
        license_id: &str,
        entry: &RegistryEntry,
        search_index: &mut HashMap<String, HashSet<String>>,
    ) {
        // Index license name
        self.add_to_index(&entry.license.name.to_lowercase(), license_id, search_index);

        // Index license ID
        self.add_to_index(&entry.license.id.to_lowercase(), license_id, search_index);

        // Index alternative IDs
        for alt_id in &entry.alternative_ids {
            self.add_to_index(&alt_id.to_lowercase(), license_id, search_index);
        }

        // Index tags
        for tag in &entry.tags {
            self.add_to_index(&tag.to_lowercase(), license_id, search_index);
        }
    }

    /// Remove license from search index
    async fn remove_from_search_index(
        &self,
        license_id: &str,
        search_index: &mut HashMap<String, HashSet<String>>,
    ) {
        for license_ids in search_index.values_mut() {
            license_ids.remove(license_id);
        }

        // Remove empty index entries
        search_index.retain(|_, ids| !ids.is_empty());
    }

    /// Add term to search index
    fn add_to_index(
        &self,
        term: &str,
        license_id: &str,
        search_index: &mut HashMap<String, HashSet<String>>,
    ) {
        search_index
            .entry(term.to_string())
            .or_insert_with(HashSet::new)
            .insert(license_id.to_string());
    }

    /// Check if entry matches search query
    fn matches_query(&self, entry: &RegistryEntry, query: &LicenseSearchQuery) -> bool {
        // Text search
        if let Some(text) = &query.text {
            let search_text = text.to_lowercase();
            let name_match = entry.license.name.to_lowercase().contains(&search_text);
            let id_match = entry.license.id.to_lowercase().contains(&search_text);
            let alt_id_match = entry.alternative_ids.iter().any(|id| id.to_lowercase().contains(&search_text));
            let tag_match = entry.tags.iter().any(|tag| tag.to_lowercase().contains(&search_text));

            if !name_match && !id_match && !alt_id_match && !tag_match {
                return false;
            }
        }

        // OSI approved filter
        if let Some(osi_required) = query.osi_approved {
            if entry.license.osi_approved != Some(osi_required) {
                return false;
            }
        }

        // Deprecated filter
        if let Some(deprecated_required) = query.deprecated {
            if entry.license.deprecated != deprecated_required {
                return false;
            }
        }

        // Tags filter
        if let Some(required_tags) = &query.tags {
            if !required_tags.is_subset(&entry.tags) {
                return false;
            }
        }

        // Sources filter
        if let Some(required_sources) = &query.sources {
            let entry_sources: HashSet<_> = entry.cross_references.keys().cloned().collect();
            if !required_sources.is_subset(&entry_sources) {
                return false;
            }
        }

        // Quality score filter
        if let Some(min_score) = query.min_quality_score {
            if entry.quality_score < min_score {
                return false;
            }
        }

        true
    }

    /// Calculate quality score for an entry
    fn calculate_quality_score(&self, entry: &RegistryEntry) -> f64 {
        if !self.config.enable_quality_scoring {
            return 0.5;
        }

        let mut score = 0.0;
        let mut factors = 0;

        // OSI approval (high weight)
        if let Some(osi_approved) = entry.license.osi_approved {
            score += if osi_approved { 1.0 } else { 0.3 };
            factors += 1;
        }

        // License text availability
        if entry.license.text.is_some() {
            score += 1.0;
            factors += 1;
        }

        // Cross-references (more sources = higher quality)
        let cross_ref_score = (entry.cross_references.len() as f64 / 3.0).min(1.0);
        score += cross_ref_score;
        factors += 1;

        // Enriched metadata
        let metadata_score = (entry.enriched_metadata.len() as f64 / 5.0).min(1.0);
        score += metadata_score;
        factors += 1;

        // Not deprecated
        if !entry.license.deprecated {
            score += 1.0;
            factors += 1;
        }

        if factors > 0 {
            score / factors as f64
        } else {
            0.5
        }
    }

    /// Apply enrichment rule to an entry
    async fn apply_enrichment_rule(&self, _entry: &mut RegistryEntry, _rule: &EnrichmentRule) -> Result<()> {
        // This would implement the actual enrichment logic
        // For now, it's a placeholder
        Ok(())
    }

    /// Update registry statistics
    async fn update_statistics(&self, registry: &HashMap<String, RegistryEntry>) {
        let mut stats = self.stats.write().await;

        stats.total_licenses = registry.len();
        stats.osi_approved_count = registry.values().filter(|e| e.license.osi_approved == Some(true)).count();
        stats.deprecated_count = registry.values().filter(|e| e.license.deprecated).count();
        stats.last_updated = Some(Utc::now());

        // Count by source
        stats.licenses_by_source.clear();
        for entry in registry.values() {
            for source in entry.cross_references.keys() {
                *stats.licenses_by_source.entry(*source).or_insert(0) += 1;
            }
        }

        // Calculate average quality score
        let total_score: f64 = registry.values().map(|e| e.quality_score).sum();
        stats.average_quality_score = if registry.is_empty() {
            0.0
        } else {
            total_score / registry.len() as f64
        };

        // Count cross-references and tags
        stats.total_cross_references = registry.values().map(|e| e.cross_references.len()).sum();
        stats.total_tags = registry.values().map(|e| e.tags.len()).sum();
    }

    /// Generate cache key for search query
    fn generate_cache_key(&self, query: &LicenseSearchQuery) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        query.hash(&mut hasher);
        format!("search_{}", hasher.finish())
    }
}

impl Default for RegistryConfig {
    fn default() -> Self {
        Self {
            enable_auto_enrichment: true,
            enable_cross_referencing: true,
            enable_quality_scoring: true,
            min_quality_threshold: 0.3,
            enable_caching: true,
            cache_ttl_seconds: 300, // 5 minutes
            max_search_results: 1000,
            enable_detailed_logging: true,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_registry_creation() {
        let config = RegistryConfig::default();
        let registry = GlobalLicenseRegistry::new(config);
        assert_eq!(registry.config.max_search_results, 1000);
    }

    #[test]
    fn test_default_config() {
        let config = RegistryConfig::default();
        assert!(config.enable_auto_enrichment);
        assert!(config.enable_cross_referencing);
        assert!(config.enable_quality_scoring);
    }

    #[tokio::test]
    async fn test_empty_search() {
        let config = RegistryConfig::default();
        let registry = GlobalLicenseRegistry::new(config);
        let query = LicenseSearchQuery {
            text: Some("MIT".to_string()),
            ..Default::default()
        };
        let result = registry.search_licenses(query).await.unwrap();
        assert_eq!(result.total_count, 0);
        assert!(result.entries.is_empty());
    }
}