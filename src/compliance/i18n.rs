//! # Internationalization Support
//!
//! Provides internationalization support for global compliance reporting.

use crate::error::{InfinitumError, Result};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, fs, path::Path};
use tracing::info;

/// Supported languages
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
#[serde(rename_all = "lowercase")]
pub enum Language {
    /// English
    En,
    /// Spanish
    Es,
    /// French
    Fr,
    /// German
    De,
    /// Japanese
    Ja,
    /// Chinese (Simplified)
    ZhCn,
    /// Portuguese
    Pt,
    /// Russian
    Ru,
    /// Korean
    Ko,
    /// Italian
    It,
}

impl std::fmt::Display for Language {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Language::En => write!(f, "en"),
            Language::Es => write!(f, "es"),
            Language::Fr => write!(f, "fr"),
            Language::De => write!(f, "de"),
            Language::Ja => write!(f, "ja"),
            Language::ZhCn => write!(f, "zh-cn"),
            Language::Pt => write!(f, "pt"),
            Language::Ru => write!(f, "ru"),
            Language::Ko => write!(f, "ko"),
            Language::It => write!(f, "it"),
        }
    }
}

impl std::str::FromStr for Language {
    type Err = InfinitumError;

    fn from_str(s: &str) -> Result<Self> {
        match s.to_lowercase().as_str() {
            "en" => Ok(Language::En),
            "es" => Ok(Language::Es),
            "fr" => Ok(Language::Fr),
            "de" => Ok(Language::De),
            "ja" => Ok(Language::Ja),
            "zh-cn" => Ok(Language::ZhCn),
            "pt" => Ok(Language::Pt),
            "ru" => Ok(Language::Ru),
            "ko" => Ok(Language::Ko),
            "it" => Ok(Language::It),
            _ => Err(InfinitumError::InvalidLanguage {
                language: s.to_string(),
            }),
        }
    }
}

/// Translation key
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub struct TranslationKey(String);

impl TranslationKey {
    /// Create new translation key
    pub fn new(key: &str) -> Self {
        Self(key.to_string())
    }

    /// Get key as string
    pub fn as_str(&self) -> &str {
        &self.0
    }
}

impl std::fmt::Display for TranslationKey {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

/// Translation dictionary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TranslationDictionary {
    /// Language
    pub language: Language,
    /// Translations
    pub translations: HashMap<TranslationKey, String>,
    /// Fallback language
    pub fallback: Option<Language>,
}

/// Internationalization manager
pub struct I18nManager {
    dictionaries: HashMap<Language, TranslationDictionary>,
    default_language: Language,
    translations_dir: String,
}

impl I18nManager {
    /// Create new i18n manager
    pub fn new(translations_dir: String, default_language: Language) -> Self {
        Self {
            dictionaries: HashMap::new(),
            default_language,
            translations_dir,
        }
    }

    /// Load translations from directory
    pub async fn load_translations(&mut self) -> Result<()> {
        let translations_path = Path::new(&self.translations_dir);

        if !translations_path.exists() {
            info!("Translations directory does not exist, creating default translations");
            self.create_default_translations().await?;
            return Ok(());
        }

        // Load all JSON files in the translations directory
        if let Ok(entries) = fs::read_dir(translations_path) {
            for entry in entries.flatten() {
                if let Some(extension) = entry.path().extension() {
                    if extension == "json" {
                        if let Some(filename) = entry.path().file_stem() {
                            if let Some(lang_str) = filename.to_str() {
                                if let Ok(language) = lang_str.parse::<Language>() {
                                    self.load_language_file(&entry.path(), language).await?;
                                }
                            }
                        }
                    }
                }
            }
        }

        // Ensure default language is loaded
        if !self.dictionaries.contains_key(&self.default_language) {
            self.create_default_dictionary(self.default_language.clone()).await?;
        }

        info!("Loaded {} language dictionaries", self.dictionaries.len());
        Ok(())
    }

    /// Load language file
    async fn load_language_file(&mut self, path: &Path, language: Language) -> Result<()> {
        let content = fs::read_to_string(path)?;
        let mut dictionary: TranslationDictionary = serde_json::from_str(&content)?;

        // Ensure the language in the file matches the filename
        dictionary.language = language.clone();

        // Set fallback to default language if not specified
        if dictionary.fallback.is_none() && language != self.default_language {
            dictionary.fallback = Some(self.default_language.clone());
        }

        self.dictionaries.insert(language, dictionary);
        Ok(())
    }

    /// Create default translations
    async fn create_default_translations(&mut self) -> Result<()> {
        // Create translations directory
        fs::create_dir_all(&self.translations_dir)?;

        // Create default language dictionaries
        for language in &[Language::En, Language::Es, Language::Fr, Language::De] {
            self.create_default_dictionary(language.clone()).await?;
        }

        Ok(())
    }

    /// Create default dictionary for a language
    async fn create_default_dictionary(&mut self, language: Language) -> Result<()> {
        let dictionary = match language {
            Language::En => self.create_english_dictionary(),
            Language::Es => self.create_spanish_dictionary(),
            Language::Fr => self.create_french_dictionary(),
            Language::De => self.create_german_dictionary(),
            _ => self.create_english_dictionary(), // Fallback to English
        };

        // Save to file
        let filename = format!("{}.json", language);
        let filepath = Path::new(&self.translations_dir).join(filename);
        let content = serde_json::to_string_pretty(&dictionary)?;
        fs::write(filepath, content)?;

        self.dictionaries.insert(language, dictionary);
        Ok(())
    }

    /// Create English dictionary
    fn create_english_dictionary(&self) -> TranslationDictionary {
        let mut translations = HashMap::new();

        // Common UI strings
        translations.insert(TranslationKey::new("report.title"), "Compliance Report".to_string());
        translations.insert(TranslationKey::new("report.generated_at"), "Generated at".to_string());
        translations.insert(TranslationKey::new("report.organization"), "Organization".to_string());
        translations.insert(TranslationKey::new("report.author"), "Author".to_string());

        // Assessment strings
        translations.insert(TranslationKey::new("assessment.overall_score"), "Overall Compliance Score".to_string());
        translations.insert(TranslationKey::new("assessment.license_compliance"), "License Compliance".to_string());
        translations.insert(TranslationKey::new("assessment.security_compliance"), "Security Compliance".to_string());
        translations.insert(TranslationKey::new("assessment.risk_level"), "Risk Level".to_string());

        // Severity levels
        translations.insert(TranslationKey::new("severity.critical"), "Critical".to_string());
        translations.insert(TranslationKey::new("severity.high"), "High".to_string());
        translations.insert(TranslationKey::new("severity.medium"), "Medium".to_string());
        translations.insert(TranslationKey::new("severity.low"), "Low".to_string());
        translations.insert(TranslationKey::new("severity.info"), "Info".to_string());

        // Risk levels
        translations.insert(TranslationKey::new("risk.very_high"), "Very High".to_string());
        translations.insert(TranslationKey::new("risk.high"), "High".to_string());
        translations.insert(TranslationKey::new("risk.medium"), "Medium".to_string());
        translations.insert(TranslationKey::new("risk.low"), "Low".to_string());
        translations.insert(TranslationKey::new("risk.very_low"), "Very Low".to_string());

        // Compliance levels
        translations.insert(TranslationKey::new("compliance.excellent"), "Excellent".to_string());
        translations.insert(TranslationKey::new("compliance.good"), "Good".to_string());
        translations.insert(TranslationKey::new("compliance.satisfactory"), "Satisfactory".to_string());
        translations.insert(TranslationKey::new("compliance.needs_improvement"), "Needs Improvement".to_string());
        translations.insert(TranslationKey::new("compliance.critical"), "Critical".to_string());

        // Common actions
        translations.insert(TranslationKey::new("action.view_details"), "View Details".to_string());
        translations.insert(TranslationKey::new("action.download"), "Download".to_string());
        translations.insert(TranslationKey::new("action.export"), "Export".to_string());

        TranslationDictionary {
            language: Language::En,
            translations,
            fallback: None,
        }
    }

    /// Create Spanish dictionary
    fn create_spanish_dictionary(&self) -> TranslationDictionary {
        let mut translations = HashMap::new();

        translations.insert(TranslationKey::new("report.title"), "Informe de Cumplimiento".to_string());
        translations.insert(TranslationKey::new("report.generated_at"), "Generado en".to_string());
        translations.insert(TranslationKey::new("report.organization"), "Organización".to_string());
        translations.insert(TranslationKey::new("report.author"), "Autor".to_string());

        translations.insert(TranslationKey::new("assessment.overall_score"), "Puntuación General de Cumplimiento".to_string());
        translations.insert(TranslationKey::new("assessment.license_compliance"), "Cumplimiento de Licencias".to_string());
        translations.insert(TranslationKey::new("assessment.security_compliance"), "Cumplimiento de Seguridad".to_string());
        translations.insert(TranslationKey::new("assessment.risk_level"), "Nivel de Riesgo".to_string());

        translations.insert(TranslationKey::new("severity.critical"), "Crítico".to_string());
        translations.insert(TranslationKey::new("severity.high"), "Alto".to_string());
        translations.insert(TranslationKey::new("severity.medium"), "Medio".to_string());
        translations.insert(TranslationKey::new("severity.low"), "Bajo".to_string());
        translations.insert(TranslationKey::new("severity.info"), "Información".to_string());

        TranslationDictionary {
            language: Language::Es,
            translations,
            fallback: Some(Language::En),
        }
    }

    /// Create French dictionary
    fn create_french_dictionary(&self) -> TranslationDictionary {
        let mut translations = HashMap::new();

        translations.insert(TranslationKey::new("report.title"), "Rapport de Conformité".to_string());
        translations.insert(TranslationKey::new("report.generated_at"), "Généré le".to_string());
        translations.insert(TranslationKey::new("report.organization"), "Organisation".to_string());
        translations.insert(TranslationKey::new("report.author"), "Auteur".to_string());

        translations.insert(TranslationKey::new("assessment.overall_score"), "Score Global de Conformité".to_string());
        translations.insert(TranslationKey::new("assessment.license_compliance"), "Conformité des Licences".to_string());
        translations.insert(TranslationKey::new("assessment.security_compliance"), "Conformité Sécurité".to_string());
        translations.insert(TranslationKey::new("assessment.risk_level"), "Niveau de Risque".to_string());

        translations.insert(TranslationKey::new("severity.critical"), "Critique".to_string());
        translations.insert(TranslationKey::new("severity.high"), "Élevé".to_string());
        translations.insert(TranslationKey::new("severity.medium"), "Moyen".to_string());
        translations.insert(TranslationKey::new("severity.low"), "Faible".to_string());
        translations.insert(TranslationKey::new("severity.info"), "Info".to_string());

        TranslationDictionary {
            language: Language::Fr,
            translations,
            fallback: Some(Language::En),
        }
    }

    /// Create German dictionary
    fn create_german_dictionary(&self) -> TranslationDictionary {
        let mut translations = HashMap::new();

        translations.insert(TranslationKey::new("report.title"), "Compliance-Bericht".to_string());
        translations.insert(TranslationKey::new("report.generated_at"), "Erstellt am".to_string());
        translations.insert(TranslationKey::new("report.organization"), "Organisation".to_string());
        translations.insert(TranslationKey::new("report.author"), "Autor".to_string());

        translations.insert(TranslationKey::new("assessment.overall_score"), "Gesamt-Compliance-Score".to_string());
        translations.insert(TranslationKey::new("assessment.license_compliance"), "Lizenz-Compliance".to_string());
        translations.insert(TranslationKey::new("assessment.security_compliance"), "Sicherheits-Compliance".to_string());
        translations.insert(TranslationKey::new("assessment.risk_level"), "Risikostufe".to_string());

        translations.insert(TranslationKey::new("severity.critical"), "Kritisch".to_string());
        translations.insert(TranslationKey::new("severity.high"), "Hoch".to_string());
        translations.insert(TranslationKey::new("severity.medium"), "Mittel".to_string());
        translations.insert(TranslationKey::new("severity.low"), "Niedrig".to_string());
        translations.insert(TranslationKey::new("severity.info"), "Info".to_string());

        TranslationDictionary {
            language: Language::De,
            translations,
            fallback: Some(Language::En),
        }
    }

    /// Translate a key
    pub fn translate(&self, key: &TranslationKey, language: &Language) -> String {
        // Try the requested language first
        if let Some(dictionary) = self.dictionaries.get(language) {
            if let Some(translation) = dictionary.translations.get(key) {
                return translation.clone();
            }

            // Try fallback language
            if let Some(fallback_lang) = &dictionary.fallback {
                if let Some(fallback_dict) = self.dictionaries.get(fallback_lang) {
                    if let Some(translation) = fallback_dict.translations.get(key) {
                        return translation.clone();
                    }
                }
            }
        }

        // Try default language
        if let Some(default_dict) = self.dictionaries.get(&self.default_language) {
            if let Some(translation) = default_dict.translations.get(key) {
                return translation.clone();
            }
        }

        // Return key if no translation found
        format!("{{{{{} }}}}", key.as_str())
    }

    /// Get available languages
    pub fn available_languages(&self) -> Vec<Language> {
        self.dictionaries.keys().cloned().collect()
    }

    /// Add translation
    pub fn add_translation(&mut self, language: Language, key: TranslationKey, value: String) {
        let language_clone = language.clone();
        self.dictionaries.entry(language)
            .or_insert_with(|| TranslationDictionary {
                language: language_clone,
                translations: HashMap::new(),
                fallback: Some(self.default_language.clone()),
            })
            .translations.insert(key, value);
    }

    /// Get default language
    pub fn default_language(&self) -> &Language {
        &self.default_language
    }

    /// Set default language
    pub fn set_default_language(&mut self, language: Language) {
        self.default_language = language;
    }
}

impl Default for I18nManager {
    fn default() -> Self {
        Self::new("translations".to_string(), Language::En)
    }
}

/// Translation helper functions
pub mod helpers {
    use super::*;

    /// Get translated severity level
    pub fn translate_severity(severity: &crate::compliance::Severity, i18n: &I18nManager, language: &Language) -> String {
        let key = match severity {
            crate::compliance::Severity::Critical => TranslationKey::new("severity.critical"),
            crate::compliance::Severity::High => TranslationKey::new("severity.high"),
            crate::compliance::Severity::Medium => TranslationKey::new("severity.medium"),
            crate::compliance::Severity::Low => TranslationKey::new("severity.low"),
            crate::compliance::Severity::Info => TranslationKey::new("severity.info"),
        };
        i18n.translate(&key, language)
    }

    /// Get translated risk level
    pub fn translate_risk_level(risk_level: &crate::compliance::RiskLevel, i18n: &I18nManager, language: &Language) -> String {
        let key = match risk_level {
            crate::compliance::RiskLevel::VeryHigh => TranslationKey::new("risk.very_high"),
            crate::compliance::RiskLevel::High => TranslationKey::new("risk.high"),
            crate::compliance::RiskLevel::Medium => TranslationKey::new("risk.medium"),
            crate::compliance::RiskLevel::Low => TranslationKey::new("risk.low"),
            crate::compliance::RiskLevel::VeryLow => TranslationKey::new("risk.very_low"),
        };
        i18n.translate(&key, language)
    }

    /// Get translated compliance level
    pub fn translate_compliance_level(level: &super::super::compliance_report_generator::ComplianceLevel, i18n: &I18nManager, language: &Language) -> String {
        let key = match level {
            super::super::compliance_report_generator::ComplianceLevel::Excellent => TranslationKey::new("compliance.excellent"),
            super::super::compliance_report_generator::ComplianceLevel::Good => TranslationKey::new("compliance.good"),
            super::super::compliance_report_generator::ComplianceLevel::Satisfactory => TranslationKey::new("compliance.satisfactory"),
            super::super::compliance_report_generator::ComplianceLevel::NeedsImprovement => TranslationKey::new("compliance.needs_improvement"),
            super::super::compliance_report_generator::ComplianceLevel::Critical => TranslationKey::new("compliance.critical"),
        };
        i18n.translate(&key, language)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_language_parsing() {
        assert_eq!("en".parse::<Language>().unwrap(), Language::En);
        assert_eq!("es".parse::<Language>().unwrap(), Language::Es);
        assert_eq!("fr".parse::<Language>().unwrap(), Language::Fr);
        assert!("invalid".parse::<Language>().is_err());
    }

    #[test]
    fn test_translation_key() {
        let key = TranslationKey::new("test.key");
        assert_eq!(key.as_str(), "test.key");
        assert_eq!(key.to_string(), "test.key");
    }

    #[test]
    fn test_i18n_manager_creation() {
        let manager = I18nManager::new("test_translations".to_string(), Language::En);
        assert_eq!(manager.default_language(), &Language::En);
        assert_eq!(manager.translations_dir, "test_translations");
    }

    #[test]
    fn test_translation_fallback() {
        let mut manager = I18nManager::new("test".to_string(), Language::En);

        // Add English translation
        manager.add_translation(Language::En, TranslationKey::new("test.key"), "English".to_string());

        // Test translation with existing language
        assert_eq!(manager.translate(&TranslationKey::new("test.key"), &Language::En), "English");

        // Test translation with non-existing language (should fallback to default)
        assert_eq!(manager.translate(&TranslationKey::new("test.key"), &Language::Es), "English");

        // Test non-existing key
        assert_eq!(manager.translate(&TranslationKey::new("non.existing"), &Language::En), "{{non.existing}}");
    }

    #[test]
    fn test_default_translations() {
        let manager = I18nManager::default();
        let english_dict = manager.dictionaries.get(&Language::En).unwrap();
        assert!(english_dict.translations.contains_key(&TranslationKey::new("report.title")));
        assert!(english_dict.translations.contains_key(&TranslationKey::new("severity.critical")));
    }
}