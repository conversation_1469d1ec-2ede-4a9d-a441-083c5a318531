//! # Internationalization Engine
//!
//! Global license text and metadata handling with multi-language support,
//! translation management, and regional compliance requirements.

use crate::{
    compliance::i18n::{I18nManager, Language, TranslationKey},
    error::{InfinitumError, Result},
};
use encoding_rs::{Encoding, UTF_8};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    fs,
    path::Path,
    str,
};
use tracing::{debug, info, instrument, warn};

/// License text in multiple languages
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MultilingualLicenseText {
    /// Original language of the license
    pub original_language: Language,
    /// License text in different languages
    pub translations: HashMap<Language, String>,
    /// Character encoding used
    pub encoding: String,
    /// Whether the text contains non-ASCII characters
    pub has_non_ascii: bool,
}

/// Regional compliance requirement
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegionalRequirement {
    /// Region identifier (ISO 3166-1 alpha-2)
    pub region: String,
    /// Required language for notices
    pub required_language: Language,
    /// Additional compliance requirements
    pub requirements: Vec<String>,
    /// Whether translation is mandatory
    pub translation_mandatory: bool,
}

/// Translation quality assessment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TranslationQuality {
    /// Quality score (0-100)
    pub score: f64,
    /// Issues found
    pub issues: Vec<String>,
    /// Recommendations
    pub recommendations: Vec<String>,
}

/// Internationalization engine for licenses
pub struct InternationalizationEngine {
    i18n_manager: I18nManager,
    license_texts: HashMap<String, MultilingualLicenseText>,
    regional_requirements: HashMap<String, RegionalRequirement>,
    translation_cache: HashMap<(String, Language), String>,
}

impl InternationalizationEngine {
    /// Create new internationalization engine
    pub fn new(translations_dir: String) -> Self {
        let mut engine = Self {
            i18n_manager: I18nManager::new(translations_dir.clone(), Language::En),
            license_texts: HashMap::new(),
            regional_requirements: HashMap::new(),
            translation_cache: HashMap::new(),
        };
        engine.initialize_regional_requirements();
        engine.load_license_texts();
        engine
    }

    /// Initialize regional compliance requirements
    fn initialize_regional_requirements(&mut self) {
        // European Union
        self.regional_requirements.insert(
            "EU".to_string(),
            RegionalRequirement {
                region: "EU".to_string(),
                required_language: Language::En, // English is acceptable in EU
                requirements: vec![
                    "GDPR compliance for personal data".to_string(),
                    "Right to be forgotten".to_string(),
                    "Data portability requirements".to_string(),
                ],
                translation_mandatory: false, // English sufficient
            },
        );

        // Germany
        self.regional_requirements.insert(
            "DE".to_string(),
            RegionalRequirement {
                region: "DE".to_string(),
                required_language: Language::De,
                requirements: vec![
                    "German translation required for consumer software".to_string(),
                    "Imprint requirements".to_string(),
                    "Data protection officer requirements".to_string(),
                ],
                translation_mandatory: true,
            },
        );

        // France
        self.regional_requirements.insert(
            "FR".to_string(),
            RegionalRequirement {
                region: "FR".to_string(),
                required_language: Language::Fr,
                requirements: vec![
                    "French translation required".to_string(),
                    "Consumer protection laws".to_string(),
                    "CNIL compliance for data processing".to_string(),
                ],
                translation_mandatory: true,
            },
        );

        // Japan
        self.regional_requirements.insert(
            "JP".to_string(),
            RegionalRequirement {
                region: "JP".to_string(),
                required_language: Language::Ja,
                requirements: vec![
                    "Japanese translation required".to_string(),
                    "PIPA compliance".to_string(),
                    "Consumer contract laws".to_string(),
                ],
                translation_mandatory: true,
            },
        );

        // China
        self.regional_requirements.insert(
            "CN".to_string(),
            RegionalRequirement {
                region: "CN".to_string(),
                required_language: Language::ZhCn,
                requirements: vec![
                    "Chinese translation required".to_string(),
                    "Cybersecurity law compliance".to_string(),
                    "Data localization requirements".to_string(),
                ],
                translation_mandatory: true,
            },
        );
    }

    /// Load license texts from templates directory
    fn load_license_texts(&mut self) {
        let templates_dir = Path::new("templates/licenses");

        if !templates_dir.exists() {
            info!("License templates directory not found, skipping license text loading");
            return;
        }

        if let Ok(entries) = fs::read_dir(templates_dir) {
            for entry in entries.flatten() {
                if let Some(filename) = entry.path().file_name() {
                    if let Some(filename_str) = filename.to_str() {
                        if filename_str.ends_with(".txt") {
                            let license_id = filename_str.trim_end_matches(".txt");
                            if let Ok(content) = fs::read_to_string(entry.path()) {
                                let multilingual = self.create_multilingual_text(&content, Language::En);
                                self.license_texts.insert(license_id.to_string(), multilingual);
                            }
                        }
                    }
                }
            }
        }

        info!("Loaded {} license texts", self.license_texts.len());
    }

    /// Create multilingual license text from content
    fn create_multilingual_text(&self, content: &str, original_lang: Language) -> MultilingualLicenseText {
        let (encoding, has_non_ascii) = self.detect_encoding_and_ascii(content);

        let original_lang_clone = original_lang.clone();
        MultilingualLicenseText {
            original_language: original_lang,
            translations: HashMap::from([(original_lang_clone, content.to_string())]),
            encoding: encoding.name().to_string(),
            has_non_ascii,
        }
    }

    /// Detect character encoding and check for non-ASCII characters
    fn detect_encoding_and_ascii(&self, text: &str) -> (&'static Encoding, bool) {
        let mut has_non_ascii = false;

        for ch in text.chars() {
            if !ch.is_ascii() {
                has_non_ascii = true;
                break;
            }
        }

        // For now, assume UTF-8
        (UTF_8, has_non_ascii)
    }

    /// Get license text in specified language
    #[instrument(skip(self))]
    pub async fn get_license_text(&mut self, license_id: &str, language: &Language) -> Result<String> {
        // Check cache first
        let cache_key = (license_id.to_string(), language.clone());
        if let Some(cached) = self.translation_cache.get(&cache_key) {
            return Ok(cached.clone());
        }

        // Load translations
        self.i18n_manager.load_translations().await?;

        // Get base license text
        let base_text = if let Some(multilingual) = self.license_texts.get(license_id) {
            if let Some(text) = multilingual.translations.get(language) {
                text.clone()
            } else if let Some(text) = multilingual.translations.get(&multilingual.original_language) {
                // Try to translate
                self.translate_license_text(text, &multilingual.original_language, language)?
            } else {
                return Err(InfinitumError::TranslationNotFound {
                    key: license_id.to_string(),
                    language: language.to_string(),
                });
            }
        } else {
            return Err(InfinitumError::LicenseNotFound {
                license: license_id.to_string(),
            });
        };

        // Cache the result
        self.translation_cache.insert(cache_key, base_text.clone());

        Ok(base_text)
    }

    /// Translate license text to target language
    fn translate_license_text(
        &self,
        text: &str,
        source_lang: &Language,
        target_lang: &Language,
    ) -> Result<String> {
        // For now, return a placeholder translation
        // In a real implementation, this would use a translation service
        warn!("Translation from {:?} to {:?} not implemented, returning original text", source_lang, target_lang);
        Ok(format!("{{{{TRANSLATION NEEDED: {}}}}}", text))
    }

    /// Add translation for license text
    pub fn add_license_translation(
        &mut self,
        license_id: &str,
        language: Language,
        translated_text: String,
    ) -> Result<()> {
        if let Some(multilingual) = self.license_texts.get_mut(license_id) {
            multilingual.translations.insert(language, translated_text);
            Ok(())
        } else {
            Err(InfinitumError::LicenseNotFound {
                license: license_id.to_string(),
            })
        }
    }

    /// Get regional compliance requirements
    pub fn get_regional_requirements(&self, region: &str) -> Option<&RegionalRequirement> {
        self.regional_requirements.get(region)
    }

    /// Check if translation is required for region
    pub fn is_translation_required(&self, region: &str, license_id: &str) -> Result<bool> {
        if let Some(requirement) = self.regional_requirements.get(region) {
            if requirement.translation_mandatory {
                // Check if we have the required language translation
                if let Some(multilingual) = self.license_texts.get(license_id) {
                    Ok(!multilingual.translations.contains_key(&requirement.required_language))
                } else {
                    Ok(true) // No license text found, assume translation needed
                }
            } else {
                Ok(false)
            }
        } else {
            Ok(false) // No regional requirements
        }
    }

    /// Assess translation quality
    pub fn assess_translation_quality(
        &self,
        original_text: &str,
        translated_text: &str,
        source_lang: &Language,
        target_lang: &Language,
    ) -> TranslationQuality {
        let mut score = 100.0;
        let mut issues = Vec::new();
        let mut recommendations = Vec::new();

        // Basic quality checks
        if translated_text.is_empty() {
            score -= 50.0;
            issues.push("Empty translation".to_string());
            recommendations.push("Provide complete translation".to_string());
        }

        // Check for untranslated placeholders
        if translated_text.contains("{{") && translated_text.contains("}}") {
            score -= 20.0;
            issues.push("Contains untranslated placeholders".to_string());
            recommendations.push("Replace all placeholders with translated content".to_string());
        }

        // Check length ratio (rough heuristic)
        let original_words = original_text.split_whitespace().count();
        let translated_words = translated_text.split_whitespace().count();
        let ratio = translated_words as f64 / original_words as f64;

        if ratio < 0.5 || ratio > 2.0 {
            score -= 15.0;
            issues.push(format!("Translation length ratio {:.2} seems unusual", ratio));
            recommendations.push("Review translation for completeness and accuracy".to_string());
        }

        // Check for encoding issues
        if translated_text.is_empty() {
            score -= 30.0;
            issues.push("Contains encoding errors (empty text)".to_string());
            recommendations.push("Fix character encoding issues".to_string());
        }

        TranslationQuality {
            score: score.max(0.0),
            issues,
            recommendations,
        }
    }

    /// Normalize character encoding
    pub fn normalize_encoding(&self, text: &str) -> Result<String> {
        // For now, assume input is valid UTF-8
        // In a real implementation, this would handle various encodings
        Ok(text.to_string())
    }

    /// Detect language of text
    pub fn detect_language(&self, text: &str) -> Language {
        // Simple language detection based on character sets
        // In a real implementation, this would use a proper language detection library

        let has_cyrillic = text.chars().any(|c| matches!(c, '\u{0400}'..='\u{04FF}'));
        if has_cyrillic {
            return Language::Ru;
        }

        let has_hiragana = text.chars().any(|c| matches!(c, '\u{3040}'..='\u{309F}'));
        let has_katakana = text.chars().any(|c| matches!(c, '\u{30A0}'..='\u{30FF}'));
        let has_kanji = text.chars().any(|c| matches!(c, '\u{4E00}'..='\u{9FFF}'));
        if has_hiragana || has_katakana || has_kanji {
            return Language::Ja;
        }

        let has_hangul = text.chars().any(|c| matches!(c, '\u{1100}'..='\u{11FF}') | matches!(c, '\u{3130}'..='\u{318F}') | matches!(c, '\u{AC00}'..='\u{D7AF}'));
        if has_hangul {
            return Language::Ko;
        }

        let has_arabic = text.chars().any(|c| matches!(c, '\u{0600}'..='\u{06FF}'));
        if has_arabic {
            return Language::En; // Default fallback
        }

        // Default to English
        Language::En
    }

    /// Get available languages for license
    pub fn get_available_languages(&self, license_id: &str) -> Vec<Language> {
        if let Some(multilingual) = self.license_texts.get(license_id) {
            multilingual.translations.keys().cloned().collect()
        } else {
            Vec::new()
        }
    }

    /// Export translations to file
    pub async fn export_translations(&self, license_id: &str, output_path: &Path) -> Result<()> {
        if let Some(multilingual) = self.license_texts.get(license_id) {
            let json_content = serde_json::to_string_pretty(multilingual)?;
            fs::write(output_path, json_content)?;
            Ok(())
        } else {
            Err(InfinitumError::LicenseNotFound {
                license: license_id.to_string(),
            })
        }
    }

    /// Import translations from file
    pub async fn import_translations(&mut self, license_id: &str, input_path: &Path) -> Result<()> {
        let content = fs::read_to_string(input_path)?;
        let multilingual: MultilingualLicenseText = serde_json::from_str(&content)?;
        self.license_texts.insert(license_id.to_string(), multilingual);
        Ok(())
    }

    /// Get I18n manager for general translations
    pub fn i18n_manager(&mut self) -> &mut I18nManager {
        &mut self.i18n_manager
    }
}

impl Default for InternationalizationEngine {
    fn default() -> Self {
        Self::new("translations".to_string())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_language_detection() {
        let engine = InternationalizationEngine::default();

        assert_eq!(engine.detect_language("Hello world"), Language::En);
        assert_eq!(engine.detect_language("Привет мир"), Language::Ru);
        assert_eq!(engine.detect_language("こんにちは世界"), Language::Ja);
    }

    #[test]
    fn test_encoding_detection() {
        let engine = InternationalizationEngine::default();

        let (encoding, has_non_ascii) = engine.detect_encoding_and_ascii("Hello");
        assert_eq!(encoding, UTF_8);
        assert!(!has_non_ascii);

        let (encoding, has_non_ascii) = engine.detect_encoding_and_ascii("Привет");
        assert_eq!(encoding, UTF_8);
        assert!(has_non_ascii);
    }

    #[test]
    fn test_regional_requirements() {
        let engine = InternationalizationEngine::default();

        let de_req = engine.get_regional_requirements("DE").unwrap();
        assert_eq!(de_req.required_language, Language::De);
        assert!(de_req.translation_mandatory);

        let eu_req = engine.get_regional_requirements("EU").unwrap();
        assert_eq!(eu_req.required_language, Language::En);
        assert!(!eu_req.translation_mandatory);
    }

    #[test]
    fn test_translation_quality_assessment() {
        let engine = InternationalizationEngine::default();

        let quality = engine.assess_translation_quality(
            "This is a test",
            "Dies ist ein Test",
            &Language::En,
            &Language::De,
        );

        assert!(quality.score > 50.0); // Should have decent score
    }

    #[test]
    fn test_multilingual_text_creation() {
        let engine = InternationalizationEngine::default();
        let text = "Copyright 2023 Example";

        let multilingual = engine.create_multilingual_text(text, Language::En);
        assert_eq!(multilingual.original_language, Language::En);
        assert!(multilingual.translations.contains_key(&Language::En));
        assert!(!multilingual.has_non_ascii);
    }
}