//! # Jenkins Integration
//!
//! Specialized integration for Jenkins CI/CD platform with pipeline DSL templates,
//! build step integration, test result publishing, and dashboard integration.

use crate::{
    compliance::{
        ci_cd_scanner::{CIDCScanner, CICDPlaformConfig, CICDPlaformRequest, CICDPlaformResult, CIOutputFormat, ScanMode},
        ComplianceConfig,
    },
    config::ScanningConfig,
    error::{InfinitumError, Result},
};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    sync::Arc,
};
use tokio::sync::RwLock;
use tracing::{debug, info, instrument, warn};

/// Jenkins pipeline configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JenkinsPipelineConfig {
    /// Pipeline name
    pub name: String,
    /// Pipeline description
    pub description: Option<String>,
    /// Agent configuration
    pub agent: JenkinsAgent,
    /// Pipeline stages
    pub stages: Vec<JenkinsStage>,
    /// Post-build actions
    pub post: Option<JenkinsPost>,
    /// Pipeline parameters
    pub parameters: Option<Vec<JenkinsParameter>>,
    /// Triggers
    pub triggers: Option<Vec<JenkinsTrigger>>,
    /// Environment variables
    pub environment: Option<HashMap<String, String>>,
}

/// Jenkins agent configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JenkinsAgent {
    /// Agent label
    pub label: Option<String>,
    /// Docker image
    pub docker: Option<String>,
    /// Kubernetes pod template
    pub kubernetes: Option<JenkinsKubernetes>,
}

/// Jenkins Kubernetes configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JenkinsKubernetes {
    /// Pod label
    pub label: String,
    /// Container templates
    pub containers: Vec<JenkinsContainer>,
    /// Service account
    pub service_account_name: Option<String>,
}

/// Jenkins container configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JenkinsContainer {
    /// Container name
    pub name: String,
    /// Docker image
    pub image: String,
    /// Commands to run
    pub command: Option<String>,
    /// Arguments
    pub args: Option<String>,
    /// Resource limits
    pub resource_limit_cpu: Option<String>,
    pub resource_limit_memory: Option<String>,
}

/// Jenkins stage configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JenkinsStage {
    /// Stage name
    pub name: String,
    /// Stage steps
    pub steps: Vec<JenkinsStep>,
    /// When conditions
    pub when: Option<JenkinsWhen>,
    /// Agent override
    pub agent: Option<JenkinsAgent>,
}

/// Jenkins step configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JenkinsStep {
    /// Step type
    pub step_type: JenkinsStepType,
    /// Step name
    pub name: Option<String>,
    /// Step configuration
    pub config: HashMap<String, serde_json::Value>,
}

/// Jenkins step types
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum JenkinsStepType {
    /// Shell script
    Sh,
    /// Batch script (Windows)
    Bat,
    /// Archive artifacts
    ArchiveArtifacts,
    /// Publish test results
    PublishTestResults,
    /// Publish HTML reports
    PublishHtml,
    /// Build job
    Build,
    /// Custom step
    Custom,
}

/// Jenkins when conditions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JenkinsWhen {
    /// Branch conditions
    pub branch: Option<Vec<String>>,
    /// Expression conditions
    pub expression: Option<String>,
    /// Any of conditions
    pub any_of: Option<Vec<JenkinsWhen>>,
}

/// Jenkins post-build actions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JenkinsPost {
    /// Always actions
    pub always: Option<Vec<JenkinsStep>>,
    /// Success actions
    pub success: Option<Vec<JenkinsStep>>,
    /// Failure actions
    pub failure: Option<Vec<JenkinsStep>>,
    /// Unstable actions
    pub unstable: Option<Vec<JenkinsStep>>,
}

/// Jenkins parameter
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JenkinsParameter {
    /// Parameter name
    pub name: String,
    /// Parameter type
    pub param_type: JenkinsParameterType,
    /// Default value
    pub default_value: Option<String>,
    /// Description
    pub description: Option<String>,
}

/// Jenkins parameter types
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum JenkinsParameterType {
    /// String parameter
    String,
    /// Boolean parameter
    Boolean,
    /// Choice parameter
    Choice,
    /// Password parameter
    Password,
}

/// Jenkins trigger
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JenkinsTrigger {
    /// Trigger type
    pub trigger_type: JenkinsTriggerType,
    /// Trigger configuration
    pub config: HashMap<String, serde_json::Value>,
}

/// Jenkins trigger types
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum JenkinsTriggerType {
    /// SCM polling
    PollScm,
    /// Cron trigger
    Cron,
    /// Upstream trigger
    Upstream,
}

/// Jenkins integration configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JenkinsConfig {
    /// Base CI/CD configuration
    pub base_config: CICDPlaformConfig,
    /// Jenkins server URL
    pub server_url: String,
    /// Jenkins job name
    pub job_name: String,
    /// Jenkins user
    pub user: Option<String>,
    /// Jenkins API token
    pub api_token: Option<String>,
    /// Enable test result publishing
    pub enable_test_results: bool,
    /// Enable artifact publishing
    pub enable_artifacts: bool,
    /// Enable dashboard integration
    pub enable_dashboard: bool,
    /// Custom pipeline templates
    pub pipeline_templates: Vec<JenkinsPipelineTemplate>,
}

/// Jenkins pipeline template
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JenkinsPipelineTemplate {
    /// Template name
    pub name: String,
    /// Template description
    pub description: String,
    /// Template content (Jenkinsfile)
    pub content: String,
    /// Applicable triggers
    pub triggers: Vec<String>,
    /// Required parameters
    pub parameters: HashMap<String, String>,
}

/// Jenkins integration service
pub struct JenkinsIntegration {
    scanner: Arc<RwLock<CIDCScanner>>,
    config: JenkinsConfig,
    pipeline_templates: HashMap<String, JenkinsPipelineTemplate>,
}

impl JenkinsIntegration {
    /// Create new Jenkins integration
    pub fn new(
        compliance_config: ComplianceConfig,
        scanning_config: ScanningConfig,
        config: JenkinsConfig,
    ) -> Self {
        let scanner = Arc::new(RwLock::new(CIDCScanner::new(
            compliance_config,
            scanning_config,
        )));

        let mut pipeline_templates = HashMap::new();

        // Initialize default pipeline templates
        pipeline_templates.insert(
            "license-scan".to_string(),
            Self::create_license_scan_template(),
        );
        pipeline_templates.insert(
            "compliance-gate".to_string(),
            Self::create_compliance_gate_template(),
        );
        pipeline_templates.insert(
            "security-scan".to_string(),
            Self::create_security_scan_template(),
        );

        // Add custom templates
        for template in &config.pipeline_templates {
            pipeline_templates.insert(template.name.clone(), template.clone());
        }

        Self {
            scanner,
            config,
            pipeline_templates,
        }
    }

    /// Execute Jenkins scan
    #[instrument(skip(self), fields(job = %self.config.job_name))]
    pub async fn execute_scan(&self, request: JenkinsRequest) -> Result<JenkinsResult> {
        info!("Executing Jenkins license scan for job {}", self.config.job_name);

        // Convert to base CI/CD request
        let base_request = self.convert_to_base_request(request)?;

        // Execute scan using base scanner
        let scanner = self.scanner.read().await;
        let scan_result = scanner.execute_scan(base_request).await?;

        // Convert to Jenkins-specific result
        let jenkins_result = self.convert_to_jenkins_result(scan_result).await?;

        // Handle Jenkins-specific features
        if self.config.enable_test_results {
            self.publish_test_results(&jenkins_result).await?;
        }

        if self.config.enable_artifacts {
            self.publish_artifacts(&jenkins_result).await?;
        }

        if self.config.enable_dashboard {
            self.update_dashboard(&jenkins_result).await?;
        }

        Ok(jenkins_result)
    }

    /// Generate Jenkins pipeline (Jenkinsfile)
    pub fn generate_pipeline(&self, template_name: &str, custom_config: Option<HashMap<String, String>>) -> Result<String> {
        let template = self.pipeline_templates.get(template_name)
            .ok_or_else(|| InfinitumError::InvalidInput {
                field: "template_name".to_string(),
                message: format!("Pipeline template '{}' not found", template_name),
            })?;

        let mut content = template.content.clone();

        // Replace placeholders with configuration values
        content = content.replace("{{JOB_NAME}}", &self.config.job_name);
        content = content.replace("{{SERVER_URL}}", &self.config.server_url);

        // Apply custom configuration
        if let Some(custom) = custom_config {
            for (key, value) in custom {
                let placeholder = format!("{{{{{}}}}}", key.to_uppercase());
                content = content.replace(&placeholder, &value);
            }
        }

        Ok(content)
    }

    /// Get available pipeline templates
    pub fn get_pipeline_templates(&self) -> Vec<&JenkinsPipelineTemplate> {
        self.pipeline_templates.values().collect()
    }

    /// Validate Jenkins configuration
    pub fn validate_config(&self) -> Result<Vec<String>> {
        let mut issues = Vec::new();

        if self.config.server_url.is_empty() {
            issues.push("Server URL is required".to_string());
        }

        if self.config.job_name.is_empty() {
            issues.push("Job name is required".to_string());
        }

        if self.config.base_config.min_compliance_score < 0.0 || self.config.base_config.min_compliance_score > 100.0 {
            issues.push("Minimum compliance score must be between 0 and 100".to_string());
        }

        Ok(issues)
    }

    /// Convert Jenkins request to base CI/CD request
    fn convert_to_base_request(&self, request: JenkinsRequest) -> Result<CICDPlaformRequest> {
        let mut base_config = self.config.base_config.clone();
        base_config.output_format = CIOutputFormat::JUnit;

        // Set up environment variables for Jenkins
        let mut env_vars = base_config.environment_variables.clone();
        env_vars.insert("JENKINS_URL".to_string(), self.config.server_url.clone());
        env_vars.insert("JOB_NAME".to_string(), self.config.job_name.clone());
        env_vars.insert("BUILD_NUMBER".to_string(), request.build_number.to_string());
        env_vars.insert("GIT_COMMIT".to_string(), request.git_commit.clone());

        if let Some(branch) = &request.git_branch {
            env_vars.insert("GIT_BRANCH".to_string(), branch.clone());
        }

        base_config.environment_variables = env_vars;

        // Determine scan mode based on build cause
        base_config.scan_mode = match request.build_cause.as_str() {
            "SCMTRIGGER" => ScanMode::Incremental,
            "TIMERTRIGGER" => ScanMode::Full,
            _ => ScanMode::Full,
        };

        Ok(CICDPlaformRequest {
            id: request.id,
            config: base_config,
            target: request.workspace,
            changed_files: request.changed_files,
            pr_info: None, // Jenkins doesn't have native PR support like GitHub/GitLab
            build_info: Some(crate::compliance::ci_cd_scanner::BuildInfo {
                build_number: request.build_number.to_string(),
                build_url: Some(format!("{}/job/{}/{}", self.config.server_url, self.config.job_name, request.build_number)),
                job_name: Some(self.config.job_name.clone()),
                pipeline_name: Some(request.pipeline_name),
            }),
            metadata: request.metadata,
        })
    }

    /// Convert base result to Jenkins result
    async fn convert_to_jenkins_result(&self, base_result: CICDPlaformResult) -> Result<JenkinsResult> {
        // Generate JUnit XML for Jenkins test results
        let junit_xml = self.generate_junit_xml(&base_result);

        // Generate HTML report for Jenkins
        let html_report = self.generate_html_report(&base_result);

        Ok(JenkinsResult {
            base_result,
            junit_xml,
            html_report,
            test_results: vec![],
            artifacts: vec![],
            build_actions: vec![],
        })
    }

    /// Generate JUnit XML for Jenkins
    fn generate_junit_xml(&self, result: &CICDPlaformResult) -> String {
        let mut xml = String::new();
        xml.push_str("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
        xml.push_str("<testsuites>\n");
        xml.push_str(&format!("  <testsuite name=\"License Compliance\" tests=\"{}\" failures=\"{}\">\n",
            result.issues.len(),
            result.issues.iter().filter(|i| i.severity == crate::compliance::ci_cd_scanner::CIssueSeverity::Error).count()
        ));

        for (i, issue) in result.issues.iter().enumerate() {
            xml.push_str(&format!("    <testcase name=\"{}\" classname=\"LicenseCompliance\" time=\"0.001\">\n",
                format!("test_{}", i)
            ));

            if issue.severity == crate::compliance::ci_cd_scanner::CIssueSeverity::Error {
                xml.push_str(&format!("      <failure message=\"{}\">{}</failure>\n",
                    self.escape_xml(&issue.message),
                    self.escape_xml(&issue.message)
                ));
            }

            xml.push_str("    </testcase>\n");
        }

        xml.push_str("  </testsuite>\n");
        xml.push_str("</testsuites>\n");

        xml
    }

    /// Generate HTML report for Jenkins
    fn generate_html_report(&self, result: &CICDPlaformResult) -> String {
        let mut html = String::new();

        html.push_str("<!DOCTYPE html>\n");
        html.push_str("<html>\n");
        html.push_str("<head>\n");
        html.push_str("  <title>License Compliance Report</title>\n");
        html.push_str("  <style>\n");
        html.push_str("    body { font-family: Arial, sans-serif; margin: 20px; }\n");
        html.push_str("    .header { background-color: #f0f0f0; padding: 10px; border-radius: 5px; }\n");
        html.push_str("    .summary { margin: 20px 0; }\n");
        html.push_str("    .issues { margin: 20px 0; }\n");
        html.push_str("    .issue { margin: 10px 0; padding: 10px; border: 1px solid #ccc; border-radius: 5px; }\n");
        html.push_str("    .error { border-color: #ff0000; background-color: #ffe6e6; }\n");
        html.push_str("    .warning { border-color: #ffa500; background-color: #fff5e6; }\n");
        html.push_str("    .info { border-color: #0000ff; background-color: #e6f3ff; }\n");
        html.push_str("  </style>\n");
        html.push_str("</head>\n");
        html.push_str("<body>\n");

        html.push_str("  <div class=\"header\">\n");
        html.push_str("    <h1>License Compliance Report</h1>\n");
        html.push_str(&format!("    <p>Generated: {}</p>\n", chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC")));
        html.push_str("  </div>\n");

        html.push_str("  <div class=\"summary\">\n");
        html.push_str("    <h2>Summary</h2>\n");
        html.push_str(&format!("    <p><strong>Status:</strong> {}</p>\n",
            match result.status {
                crate::compliance::ci_cd_scanner::CIScanStatus::Success => "✅ Passed",
                crate::compliance::ci_cd_scanner::CIScanStatus::Warning => "⚠️ Warning",
                crate::compliance::ci_cd_scanner::CIScanStatus::Failed => "❌ Failed",
                crate::compliance::ci_cd_scanner::CIScanStatus::Cancelled => "🚫 Cancelled",
                crate::compliance::ci_cd_scanner::CIScanStatus::Timeout => "⏰ Timeout",
            }
        ));
        html.push_str(&format!("    <p><strong>Compliance Score:</strong> {:.1}%</p>\n", result.summary.compliance_score));
        html.push_str(&format!("    <p><strong>Files Scanned:</strong> {}</p>\n", result.summary.files_scanned));
        html.push_str(&format!("    <p><strong>Licenses Found:</strong> {}</p>\n", result.summary.total_licenses));
        html.push_str("  </div>\n");

        if !result.issues.is_empty() {
            html.push_str("  <div class=\"issues\">\n");
            html.push_str("    <h2>Issues Found</h2>\n");

            for issue in &result.issues {
                let css_class = match issue.severity {
                    crate::compliance::ci_cd_scanner::CIssueSeverity::Error | crate::compliance::ci_cd_scanner::CIssueSeverity::Critical => "error",
                    crate::compliance::ci_cd_scanner::CIssueSeverity::Warning => "warning",
                    crate::compliance::ci_cd_scanner::CIssueSeverity::Info => "info",
                };

                html.push_str(&format!("    <div class=\"issue {}\">\n", css_class));
                html.push_str(&format!("      <h3>{}: {}</h3>\n", issue.category, issue.severity));
                html.push_str(&format!("      <p>{}</p>\n", self.escape_html(&issue.message)));
                if let Some(file_path) = &issue.file_path {
                    html.push_str(&format!("      <p><strong>File:</strong> {}</p>\n", file_path));
                }
                html.push_str("    </div>\n");
            }

            html.push_str("  </div>\n");
        }

        html.push_str("</body>\n");
        html.push_str("</html>\n");

        html
    }

    /// Publish test results to Jenkins
    async fn publish_test_results(&self, result: &JenkinsResult) -> Result<()> {
        info!("Publishing test results to Jenkins");

        // In a real implementation, this would use the Jenkins API
        // For now, we'll just indicate what would be published
        debug!("JUnit XML that would be published:\n{}", result.junit_xml);

        Ok(())
    }

    /// Publish artifacts to Jenkins
    async fn publish_artifacts(&self, result: &JenkinsResult) -> Result<()> {
        info!("Publishing artifacts to Jenkins");

        // In a real implementation, this would upload files using Jenkins API
        let mut artifacts = Vec::new();

        artifacts.push(JenkinsArtifact {
            name: "license-scan-results.json".to_string(),
            path: "scan-results.json".to_string(),
            content_type: "application/json".to_string(),
        });

        artifacts.push(JenkinsArtifact {
            name: "compliance-report.html".to_string(),
            path: "compliance-report.html".to_string(),
            content_type: "text/html".to_string(),
        });

        debug!("Artifacts that would be published: {:?}", artifacts);

        Ok(())
    }

    /// Update Jenkins dashboard
    async fn update_dashboard(&self, result: &JenkinsResult) -> Result<()> {
        info!("Updating Jenkins dashboard");

        // In a real implementation, this would update Jenkins dashboard
        // with compliance metrics and trends
        debug!("Dashboard update would include compliance score: {:.1}", result.base_result.summary.compliance_score);

        Ok(())
    }

    /// Escape XML characters
    fn escape_xml(&self, text: &str) -> String {
        text.replace("&", "&amp;")
            .replace("<", "&lt;")
            .replace(">", "&gt;")
            .replace("\"", "&quot;")
            .replace("'", "&apos;")
    }

    /// Escape HTML characters
    fn escape_html(&self, text: &str) -> String {
        text.replace("&", "&amp;")
            .replace("<", "&lt;")
            .replace(">", "&gt;")
            .replace("\"", "&quot;")
            .replace("'", "&apos;")
    }

    /// Create license scan pipeline template
    fn create_license_scan_template() -> JenkinsPipelineTemplate {
        let content = r#"pipeline {
    agent {
        docker {
            image "rust:1.80"
            args "-v $HOME/.cargo:/usr/local/cargo"
        }
    }

    parameters {
        string(name: "SCAN_MODE", defaultValue: "full", description: "Scan mode: full or incremental")
        string(name: "COMPLIANCE_THRESHOLD", defaultValue: "80", description: "Minimum compliance score threshold")
        booleanParam(name: "FAIL_ON_VIOLATIONS", defaultValue: true, description: "Fail build on license violations")
    }

    stages {
        stage("Checkout") {
            steps {
                checkout scm
            }
        }

        stage("License Scan") {
            steps {
                sh """
                    echo "Running license compliance scan..."
                    cargo run --release --bin infinitium-signal -- ci-scan \\
                        --platform jenkins \\
                        --output-format junit \\
                        --scan-mode \${SCAN_MODE} \\
                        --fail-on-violations \${FAIL_ON_VIOLATIONS} \\
                        --min-compliance-score \${COMPLIANCE_THRESHOLD} \\
                        --enable-artifacts true
                """
            }
        }

        stage("Publish Results") {
            steps {
                junit "junit-report.xml"
                publishHTML([
                    allowMissing: true,
                    alwaysLinkToLastBuild: true,
                    keepAll: true,
                    reportDir: ".",
                    reportFiles: "compliance-report.html",
                    reportName: "License Compliance Report"
                ])
            }
        }
    }

    post {
        always {
            archiveArtifacts artifacts: "scan-results.json,compliance-report.html", allowEmptyArchive: true
        }
        failure {
            echo "License compliance check failed!"
        }
        success {
            echo "License compliance check passed!"
        }
    }
}"#.to_string();

        let mut parameters = HashMap::new();
        parameters.insert("SCAN_MODE".to_string(), "full".to_string());
        parameters.insert("COMPLIANCE_THRESHOLD".to_string(), "80".to_string());

        JenkinsPipelineTemplate {
            name: "license-scan".to_string(),
            description: "Comprehensive license compliance scanning pipeline".to_string(),
            content,
            triggers: vec!["push".to_string(), "manual".to_string()],
            parameters,
        }
    }

    /// Create compliance gate pipeline template
    fn create_compliance_gate_template() -> JenkinsPipelineTemplate {
        let content = r#"pipeline {
    agent any

    parameters {
        string(name: "BRANCH_PATTERN", defaultValue: "main|develop", description: "Branch pattern for compliance gate")
        string(name: "COMPLIANCE_THRESHOLD", defaultValue: "90", description: "Strict compliance threshold for gate")
    }

    stages {
        stage("Compliance Gate") {
            when {
                anyOf {
                    branch pattern: "${BRANCH_PATTERN}", comparator: "REGEXP"
                }
            }
            steps {
                sh """
                    echo "Running compliance gate check..."
                    cargo run --release --bin infinitium-signal -- ci-scan \\
                        --platform jenkins \\
                        --scan-mode incremental \\
                        --fail-on-violations true \\
                        --fail-on-compliance-issues true \\
                        --min-compliance-score \${COMPLIANCE_THRESHOLD} \\
                        --enable-dashboard true
                """
            }
        }
    }

    post {
        failure {
            script {
                // Send notification about compliance gate failure
                echo "Compliance gate failed - blocking merge/deployment"
            }
        }
        success {
            script {
                // Update compliance dashboard
                echo "Compliance gate passed - proceeding with build"
            }
        }
    }
}"#.to_string();

        let mut parameters = HashMap::new();
        parameters.insert("BRANCH_PATTERN".to_string(), "main|develop".to_string());
        parameters.insert("COMPLIANCE_THRESHOLD".to_string(), "90".to_string());

        JenkinsPipelineTemplate {
            name: "compliance-gate".to_string(),
            description: "Compliance gate that blocks builds on license issues".to_string(),
            content,
            triggers: vec!["branch".to_string()],
            parameters,
        }
    }

    /// Create security scan pipeline template
    fn create_security_scan_template() -> JenkinsPipelineTemplate {
        let content = r#"pipeline {
    agent {
        docker {
            image "rust:1.80"
            args "-v $HOME/.cargo:/usr/local/cargo"
        }
    }

    triggers {
        cron("H 2 * * 1")  // Weekly on Monday at 2 AM
    }

    stages {
        stage("Security License Scan") {
            steps {
                sh """
                    echo "Running security license scan..."
                    cargo run --release --bin infinitium-signal -- ci-scan \\
                        --platform jenkins \\
                        --scan-mode full \\
                        --enable-security-events true \\
                        --output-format sarif \\
                        --fail-on-violations true
                """
            }
        }

        stage("Security Analysis") {
            steps {
                sh """
                    echo "Analyzing security findings..."
                    # Additional security analysis steps would go here
                """
            }
        }
    }

    post {
        always {
            archiveArtifacts artifacts: "scan-results.sarif,security-report.json", allowEmptyArchive: true
        }
        failure {
            script {
                // Create security advisory or alert
                echo "Security license issues found!"
            }
        }
    }
}"#.to_string();

        let parameters = HashMap::new();

        JenkinsPipelineTemplate {
            name: "security-scan".to_string(),
            description: "Security-focused license scanning with SARIF reporting".to_string(),
            content,
            triggers: vec!["schedule".to_string()],
            parameters,
        }
    }
}

/// Jenkins specific request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JenkinsRequest {
    /// Request ID
    pub id: uuid::Uuid,
    /// Workspace path
    pub workspace: String,
    /// Build number
    pub build_number: u64,
    /// Build cause
    pub build_cause: String,
    /// Git commit
    pub git_commit: String,
    /// Git branch
    pub git_branch: Option<String>,
    /// Pipeline name
    pub pipeline_name: String,
    /// Changed files (for incremental scans)
    pub changed_files: Option<Vec<String>>,
    /// Additional metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Jenkins result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JenkinsResult {
    /// Base CI/CD result
    pub base_result: CICDPlaformResult,
    /// JUnit XML for test results
    pub junit_xml: String,
    /// HTML report
    pub html_report: String,
    /// Test results
    pub test_results: Vec<JenkinsTestResult>,
    /// Published artifacts
    pub artifacts: Vec<JenkinsArtifact>,
    /// Build actions
    pub build_actions: Vec<JenkinsBuildAction>,
}

/// Jenkins test result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JenkinsTestResult {
    /// Test name
    pub name: String,
    /// Test status
    pub status: String,
    /// Test duration
    pub duration: Option<f64>,
    /// Test message
    pub message: Option<String>,
}

/// Jenkins artifact
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JenkinsArtifact {
    /// Artifact name
    pub name: String,
    /// File path
    pub path: String,
    /// Content type
    pub content_type: String,
}

/// Jenkins build action
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JenkinsBuildAction {
    /// Action type
    pub action_type: String,
    /// Action parameters
    pub parameters: HashMap<String, serde_json::Value>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_jenkins_config_validation() {
        let config = JenkinsConfig {
            base_config: CICDPlaformConfig::default(),
            server_url: "https://jenkins.example.com".to_string(),
            job_name: "license-scan".to_string(),
            user: None,
            api_token: None,
            enable_test_results: true,
            enable_artifacts: true,
            enable_dashboard: true,
            pipeline_templates: vec![],
        };

        let integration = JenkinsIntegration::new(
            ComplianceConfig::default(),
            ScanningConfig::default(),
            config,
        );

        let issues = integration.validate_config().unwrap();
        assert!(issues.is_empty());
    }

    #[test]
    fn test_pipeline_generation() {
        let config = JenkinsConfig {
            base_config: CICDPlaformConfig::default(),
            server_url: "https://jenkins.example.com".to_string(),
            job_name: "test-job".to_string(),
            user: None,
            api_token: None,
            enable_test_results: true,
            enable_artifacts: true,
            enable_dashboard: true,
            pipeline_templates: vec![],
        };

        let integration = JenkinsIntegration::new(
            ComplianceConfig::default(),
            ScanningConfig::default(),
            config,
        );

        let pipeline = integration.generate_pipeline("license-scan", None).unwrap();
        assert!(pipeline.contains("pipeline {"));
        assert!(pipeline.contains("test-job"));
        assert!(pipeline.contains("https://jenkins.example.com"));
    }
}