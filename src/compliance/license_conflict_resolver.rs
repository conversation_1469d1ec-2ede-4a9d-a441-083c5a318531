//! # License Conflict Resolver
//!
//! Advanced conflict resolution engine for license compliance with multiple resolution strategies,
//! compatibility matrix analysis, and risk assessment for conflicting licenses.

use crate::{
    compliance::license_expression_parser::{LicenseExpressionParser, LicenseNode},
    error::{InfinitumError, Result},
};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use tracing::{debug, info, instrument, warn};

/// Resolution strategy for license conflicts
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ResolutionStrategy {
    /// Select license with highest confidence score
    HighestConfidence,
    /// Combine compatible licenses using AND/OR operators
    CombineCompatible,
    /// Flag conflicts for manual review
    FlagConflicts,
    /// Require manual review for all conflicts
    ManualReview,
}

/// License compatibility level
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, PartialOrd)]
pub enum CompatibilityLevel {
    /// Fully compatible
    Compatible,
    /// Compatible with conditions
    Conditional,
    /// Incompatible but resolvable
    Resolvable,
    /// Completely incompatible
    Incompatible,
}

/// License conflict information
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct LicenseConflict {
    /// Conflicting license identifiers
    pub licenses: Vec<String>,
    /// Conflict description
    pub description: String,
    /// Compatibility level
    pub compatibility: CompatibilityLevel,
    /// Risk score (0-100)
    pub risk_score: f64,
    /// Resolution recommendations
    pub recommendations: Vec<String>,
}

/// Conflict resolution result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConflictResolution {
    /// Resolved license expression
    pub resolved_expression: Option<String>,
    /// Remaining conflicts
    pub remaining_conflicts: Vec<LicenseConflict>,
    /// Resolution strategy used
    pub strategy_used: ResolutionStrategy,
    /// Overall risk score
    pub overall_risk: f64,
    /// Resolution confidence (0-100)
    pub confidence: f64,
}

/// License conflict resolver
pub struct LicenseConflictResolver {
    compatibility_matrix: HashMap<(String, String), CompatibilityLevel>,
    license_families: HashMap<String, String>,
}

impl LicenseConflictResolver {
    /// Create new conflict resolver
    pub fn new() -> Self {
        let mut resolver = Self {
            compatibility_matrix: HashMap::new(),
            license_families: HashMap::new(),
        };
        resolver.initialize_compatibility_matrix();
        resolver.initialize_license_families();
        resolver
    }

    /// Initialize license compatibility matrix
    fn initialize_compatibility_matrix(&mut self) {
        // Permissive licenses (generally compatible with each other)
        let permissive = vec![
            "MIT", "BSD-2-Clause", "BSD-3-Clause", "ISC", "Apache-2.0", "BSD-4-Clause"
        ];

        // Copyleft licenses (may conflict with proprietary)
        let copyleft = vec![
            "GPL-2.0", "GPL-3.0", "LGPL-2.1", "LGPL-3.0", "AGPL-3.0"
        ];

        // Proprietary placeholder
        let proprietary = vec!["Proprietary"];

        // Set compatibility between permissive licenses
        for i in 0..permissive.len() {
            for j in (i + 1)..permissive.len() {
                self.compatibility_matrix.insert(
                    (permissive[i].to_string(), permissive[j].to_string()),
                    CompatibilityLevel::Compatible,
                );
                self.compatibility_matrix.insert(
                    (permissive[j].to_string(), permissive[i].to_string()),
                    CompatibilityLevel::Compatible,
                );
            }
        }

        // Set compatibility between copyleft licenses (same family)
        for i in 0..copyleft.len() {
            for j in (i + 1)..copyleft.len() {
                self.compatibility_matrix.insert(
                    (copyleft[i].to_string(), copyleft[j].to_string()),
                    CompatibilityLevel::Conditional,
                );
                self.compatibility_matrix.insert(
                    (copyleft[j].to_string(), copyleft[i].to_string()),
                    CompatibilityLevel::Conditional,
                );
            }
        }

        // Permissive + Copyleft = Conditional (depends on usage)
        for perm in &permissive {
            for copy in &copyleft {
                self.compatibility_matrix.insert(
                    (perm.clone(), copy.clone()),
                    CompatibilityLevel::Conditional,
                );
                self.compatibility_matrix.insert(
                    (copy.clone(), perm.clone()),
                    CompatibilityLevel::Conditional,
                );
            }
        }

        // Any license + Proprietary = Incompatible
        for license in permissive.iter().chain(copyleft.iter()) {
            for prop in &proprietary {
                self.compatibility_matrix.insert(
                    (license.clone(), prop.clone()),
                    CompatibilityLevel::Incompatible,
                );
                self.compatibility_matrix.insert(
                    (prop.clone(), license.clone()),
                    CompatibilityLevel::Incompatible,
                );
            }
        }
    }

    /// Initialize license family mappings
    fn initialize_license_families(&mut self) {
        // BSD family
        self.license_families.insert("BSD-2-Clause".to_string(), "BSD".to_string());
        self.license_families.insert("BSD-3-Clause".to_string(), "BSD".to_string());
        self.license_families.insert("BSD-4-Clause".to_string(), "BSD".to_string());

        // GPL family
        self.license_families.insert("GPL-2.0".to_string(), "GPL".to_string());
        self.license_families.insert("GPL-3.0".to_string(), "GPL".to_string());
        self.license_families.insert("AGPL-3.0".to_string(), "GPL".to_string());

        // LGPL family
        self.license_families.insert("LGPL-2.1".to_string(), "LGPL".to_string());
        self.license_families.insert("LGPL-3.0".to_string(), "LGPL".to_string());

        // Apache family
        self.license_families.insert("Apache-2.0".to_string(), "Apache".to_string());

        // MIT family
        self.license_families.insert("MIT".to_string(), "MIT".to_string());
    }

    /// Resolve license conflicts using specified strategy
    #[instrument(skip(license_expressions, confidence_scores))]
    pub fn resolve_conflicts(
        &self,
        license_expressions: &[String],
        confidence_scores: &[f64],
        strategy: ResolutionStrategy,
    ) -> Result<ConflictResolution> {
        info!("Resolving conflicts for {} expressions using {:?}", license_expressions.len(), strategy);

        if license_expressions.len() != confidence_scores.len() {
            return Err(InfinitumError::InvalidInput {
                field: "license_expressions/confidence_scores".to_string(),
                reason: "Mismatched array lengths".to_string(),
            });
        }

        // Parse all expressions
        let mut parsed_expressions = Vec::new();
        for expr in license_expressions {
            match LicenseExpressionParser::parse(expr) {
                Ok(node) => parsed_expressions.push(node),
                Err(e) => {
                    warn!("Failed to parse expression '{}': {}", expr, e);
                    continue;
                }
            }
        }

        if parsed_expressions.is_empty() {
            return Err(InfinitumError::InvalidLicenseExpression {
                expression: license_expressions.join(", "),
                reason: "No valid expressions found".to_string(),
            });
        }

        // Extract all licenses
        let mut all_licenses = HashSet::new();
        for expr in &parsed_expressions {
            all_licenses.extend(LicenseExpressionParser::extract_licenses(expr));
        }

        // Detect conflicts
        let conflicts = self.detect_conflicts(&all_licenses)?;

        match strategy {
            ResolutionStrategy::HighestConfidence => {
                self.resolve_highest_confidence(license_expressions, confidence_scores, &conflicts)
            }
            ResolutionStrategy::CombineCompatible => {
                self.resolve_combine_compatible(&parsed_expressions, &conflicts)
            }
            ResolutionStrategy::FlagConflicts => {
                self.resolve_flag_conflicts(license_expressions, &conflicts)
            }
            ResolutionStrategy::ManualReview => {
                self.resolve_manual_review(license_expressions, &conflicts)
            }
        }
    }

    /// Detect conflicts between licenses
    fn detect_conflicts(&self, licenses: &HashSet<String>) -> Result<Vec<LicenseConflict>> {
        let mut conflicts = Vec::new();
        let license_list: Vec<String> = licenses.iter().cloned().collect();

        for i in 0..license_list.len() {
            for j in (i + 1)..license_list.len() {
                let license_a = &license_list[i];
                let license_b = &license_list[j];

                let compatibility = self.get_compatibility(license_a, license_b);

                if compatibility >= CompatibilityLevel::Resolvable {
                    let conflict = LicenseConflict {
                        licenses: vec![license_a.clone(), license_b.clone()],
                        description: format!("Conflict between {} and {}", license_a, license_b),
                        compatibility: compatibility.clone(),
                        risk_score: self.calculate_risk_score(license_a, license_b, compatibility.clone()),
                        recommendations: self.generate_recommendations(license_a, license_b, compatibility),
                    };
                    conflicts.push(conflict);
                }
            }
        }

        debug!("Detected {} conflicts", conflicts.len());
        Ok(conflicts)
    }

    /// Get compatibility between two licenses
    fn get_compatibility(&self, license_a: &str, license_b: &str) -> CompatibilityLevel {
        if license_a == license_b {
            return CompatibilityLevel::Compatible;
        }

        // Check direct compatibility
        if let Some(compat) = self.compatibility_matrix.get(&(license_a.to_string(), license_b.to_string())) {
            return compat.clone();
        }

        // Check family compatibility
        if let (Some(family_a), Some(family_b)) = (
            self.license_families.get(license_a),
            self.license_families.get(license_b),
        ) {
            if family_a == family_b {
                return CompatibilityLevel::Conditional;
            }
        }

        // Default to resolvable for unknown combinations
        CompatibilityLevel::Resolvable
    }

    /// Calculate risk score for conflict
    fn calculate_risk_score(&self, license_a: &str, license_b: &str, compatibility: CompatibilityLevel) -> f64 {
        let base_score = match compatibility {
            CompatibilityLevel::Compatible => 0.0,
            CompatibilityLevel::Conditional => 30.0,
            CompatibilityLevel::Resolvable => 60.0,
            CompatibilityLevel::Incompatible => 100.0,
        };

        // Adjust based on license types
        let mut multiplier = 1.0;
        if license_a.contains("GPL") || license_b.contains("GPL") {
            multiplier = 1.5; // GPL conflicts are more serious
        }
        if license_a == "Proprietary" || license_b == "Proprietary" {
            multiplier = 2.0; // Proprietary conflicts are critical
        }

        (base_score * multiplier).min(100.0)
    }

    /// Generate resolution recommendations
    fn generate_recommendations(&self, license_a: &str, license_b: &str, compatibility: CompatibilityLevel) -> Vec<String> {
        let mut recommendations = Vec::new();

        match compatibility {
            CompatibilityLevel::Compatible => {
                recommendations.push(format!("{} and {} are compatible - no action needed", license_a, license_b));
            }
            CompatibilityLevel::Conditional => {
                recommendations.push(format!("{} and {} are conditionally compatible", license_a, license_b));
                recommendations.push("Review usage context and ensure compliance with both licenses".to_string());
            }
            CompatibilityLevel::Resolvable => {
                recommendations.push(format!("Conflict between {} and {} requires resolution", license_a, license_b));
                recommendations.push("Consider using license with higher confidence score".to_string());
                recommendations.push("Review license compatibility matrix for specific guidance".to_string());
            }
            CompatibilityLevel::Incompatible => {
                recommendations.push(format!("{} and {} are incompatible", license_a, license_b));
                recommendations.push("Manual review required - may need to replace one license".to_string());
                recommendations.push("Consider alternative dependencies with compatible licenses".to_string());
            }
        }

        recommendations
    }

    /// Resolve using highest confidence strategy
    fn resolve_highest_confidence(
        &self,
        expressions: &[String],
        confidence_scores: &[f64],
        conflicts: &[LicenseConflict],
    ) -> Result<ConflictResolution> {
        // Find expression with highest confidence
        let mut max_confidence = 0.0;
        let mut best_expression = None;

        for (i, &confidence) in confidence_scores.iter().enumerate() {
            if confidence > max_confidence {
                max_confidence = confidence;
                best_expression = Some(expressions[i].clone());
            }
        }

        let overall_risk = if conflicts.is_empty() { 0.0 } else { 50.0 };

        Ok(ConflictResolution {
            resolved_expression: best_expression,
            remaining_conflicts: conflicts.to_vec(),
            strategy_used: ResolutionStrategy::HighestConfidence,
            overall_risk,
            confidence: max_confidence,
        })
    }

    /// Resolve by combining compatible licenses
    fn resolve_combine_compatible(
        &self,
        expressions: &[LicenseNode],
        conflicts: &[LicenseConflict],
    ) -> Result<ConflictResolution> {
        if expressions.is_empty() {
            return Ok(ConflictResolution {
                resolved_expression: None,
                remaining_conflicts: conflicts.to_vec(),
                strategy_used: ResolutionStrategy::CombineCompatible,
                overall_risk: 100.0,
                confidence: 0.0,
            });
        }

        // For simplicity, combine all expressions with OR
        let mut combined = expressions[0].clone();
        for expr in &expressions[1..] {
            combined = LicenseNode::Or(Box::new(combined), Box::new(expr.clone()));
        }

        let resolved_expr = LicenseExpressionParser::to_string(&combined);
        let overall_risk = conflicts.iter().map(|c| c.risk_score).sum::<f64>() / conflicts.len() as f64;

        Ok(ConflictResolution {
            resolved_expression: Some(resolved_expr),
            remaining_conflicts: conflicts.to_vec(),
            strategy_used: ResolutionStrategy::CombineCompatible,
            overall_risk: overall_risk.min(100.0),
            confidence: 80.0, // High confidence for combination
        })
    }

    /// Resolve by flagging conflicts
    fn resolve_flag_conflicts(
        &self,
        expressions: &[String],
        conflicts: &[LicenseConflict],
    ) -> Result<ConflictResolution> {
        // Return first expression but flag all conflicts
        let resolved_expression = expressions.first().cloned();

        Ok(ConflictResolution {
            resolved_expression,
            remaining_conflicts: conflicts.to_vec(),
            strategy_used: ResolutionStrategy::FlagConflicts,
            overall_risk: 75.0,
            confidence: 60.0,
        })
    }

    /// Require manual review
    fn resolve_manual_review(
        &self,
        expressions: &[String],
        conflicts: &[LicenseConflict],
    ) -> Result<ConflictResolution> {
        Ok(ConflictResolution {
            resolved_expression: None, // No automatic resolution
            remaining_conflicts: conflicts.to_vec(),
            strategy_used: ResolutionStrategy::ManualReview,
            overall_risk: 100.0, // Maximum risk until reviewed
            confidence: 0.0, // No confidence without review
        })
    }

    /// Get license family
    pub fn get_license_family(&self, license: &str) -> Option<&String> {
        self.license_families.get(license)
    }

    /// Check if two licenses are compatible
    pub fn are_compatible(&self, license_a: &str, license_b: &str) -> bool {
        self.get_compatibility(license_a, license_b) <= CompatibilityLevel::Conditional
    }
}

impl Default for LicenseConflictResolver {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_compatibility_matrix() {
        let resolver = LicenseConflictResolver::new();

        // Test compatible licenses
        assert!(resolver.are_compatible("MIT", "BSD-3-Clause"));
        assert!(resolver.are_compatible("Apache-2.0", "MIT"));

        // Test incompatible licenses
        assert!(!resolver.are_compatible("GPL-3.0", "Proprietary"));
    }

    #[test]
    fn test_conflict_detection() {
        let resolver = LicenseConflictResolver::new();
        let licenses = HashSet::from([
            "MIT".to_string(),
            "GPL-3.0".to_string(),
            "Proprietary".to_string(),
        ]);

        let conflicts = resolver.detect_conflicts(&licenses).unwrap();
        assert!(!conflicts.is_empty());

        // Should detect MIT vs GPL-3.0 and GPL-3.0 vs Proprietary conflicts
        assert!(conflicts.len() >= 2);
    }

    #[test]
    fn test_highest_confidence_resolution() {
        let resolver = LicenseConflictResolver::new();
        let expressions = vec!["MIT".to_string(), "Apache-2.0".to_string()];
        let confidence_scores = vec![0.8, 0.9];

        let result = resolver.resolve_conflicts(
            &expressions,
            &confidence_scores,
            ResolutionStrategy::HighestConfidence,
        ).unwrap();

        assert_eq!(result.resolved_expression, Some("Apache-2.0".to_string()));
        assert_eq!(result.strategy_used, ResolutionStrategy::HighestConfidence);
    }

    #[test]
    fn test_manual_review_resolution() {
        let resolver = LicenseConflictResolver::new();
        let expressions = vec!["MIT".to_string(), "GPL-3.0".to_string()];
        let confidence_scores = vec![0.8, 0.9];

        let result = resolver.resolve_conflicts(
            &expressions,
            &confidence_scores,
            ResolutionStrategy::ManualReview,
        ).unwrap();

        assert_eq!(result.resolved_expression, None);
        assert_eq!(result.strategy_used, ResolutionStrategy::ManualReview);
        assert_eq!(result.confidence, 0.0);
    }

    #[test]
    fn test_license_family_mapping() {
        let resolver = LicenseConflictResolver::new();

        assert_eq!(resolver.get_license_family("MIT"), Some(&"MIT".to_string()));
        assert_eq!(resolver.get_license_family("BSD-3-Clause"), Some(&"BSD".to_string()));
        assert_eq!(resolver.get_license_family("GPL-3.0"), Some(&"GPL".to_string()));
    }
}