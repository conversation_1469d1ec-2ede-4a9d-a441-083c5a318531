//! # License Database Updater
//!
//! Core update management system for license databases. Handles scheduled updates,
//! database synchronization, conflict resolution, and rollback capabilities.

use crate::observability::instrumentation::{self, create_span, create_span_with_attributes, counter, histogram, record_counter, record_histogram};
use crate::{
    error::{InfinitumError, Result},
    logging,
};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    sync::Arc,
    time::Duration,
};
use opentelemetry::KeyValue;
use tokio::sync::RwLock;
use tracing::{error, info, warn};

/// Update operation status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum UpdateStatus {
    /// Update is pending
    Pending,
    /// Update is in progress
    InProgress,
    /// Update completed successfully
    Completed,
    /// Update failed
    Failed,
    /// Update was rolled back
    RolledBack,
}

/// Update operation type
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum UpdateType {
    /// Full database update
    Full,
    /// Incremental update
    Incremental,
    /// Emergency update
    Emergency,
}

/// Update operation record
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateOperation {
    /// Unique operation ID
    pub id: String,
    /// Update type
    pub update_type: UpdateType,
    /// Source database
    pub source: String,
    /// Status
    pub status: UpdateStatus,
    /// Start time
    pub started_at: DateTime<Utc>,
    /// Completion time
    pub completed_at: Option<DateTime<Utc>>,
    /// Records processed
    pub records_processed: u32,
    /// Records updated
    pub records_updated: u32,
    /// Records failed
    pub records_failed: u32,
    /// Error message if failed
    pub error_message: Option<String>,
    /// Rollback information
    pub rollback_info: Option<RollbackInfo>,
}

/// Rollback information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RollbackInfo {
    /// Backup file path
    pub backup_path: String,
    /// Rollback timestamp
    pub rolled_back_at: DateTime<Utc>,
    /// Reason for rollback
    pub reason: String,
}

/// Update conflict resolution strategy
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ConflictResolutionStrategy {
    /// Use source data (overwrite)
    UseSource,
    /// Use existing data (keep)
    UseExisting,
    /// Merge data intelligently
    Merge,
    /// Manual review required
    ManualReview,
}

/// Update configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateConfig {
    /// Maximum concurrent updates
    pub max_concurrent_updates: usize,
    /// Update timeout in seconds
    pub update_timeout_seconds: u64,
    /// Conflict resolution strategy
    pub conflict_resolution: ConflictResolutionStrategy,
    /// Enable automatic rollback on failure
    pub auto_rollback_on_failure: bool,
    /// Backup directory path
    pub backup_directory: String,
    /// Enable detailed logging
    pub enable_detailed_logging: bool,
}

/// Database update statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateStatistics {
    /// Total updates performed
    pub total_updates: u64,
    /// Successful updates
    pub successful_updates: u64,
    /// Failed updates
    pub failed_updates: u64,
    /// Average update duration
    pub average_update_duration: Duration,
    /// Last update timestamp
    pub last_update: Option<DateTime<Utc>>,
    /// Update success rate (0.0-1.0)
    pub success_rate: f64,
}

/// License Database Updater
pub struct LicenseDatabaseUpdater {
    /// Configuration
    config: UpdateConfig,
    /// Active update operations
    active_operations: Arc<RwLock<HashMap<String, UpdateOperation>>>,
    /// Update history
    update_history: Arc<RwLock<Vec<UpdateOperation>>>,
    /// Update statistics
    statistics: Arc<RwLock<UpdateStatistics>>,
    /// Logger
    logger: Arc<tracing::Span>,
}

impl LicenseDatabaseUpdater {
    /// Create new license database updater
    pub fn new(config: UpdateConfig) -> Self {
        Self {
            config,
            active_operations: Arc::new(RwLock::new(HashMap::new())),
            update_history: Arc::new(RwLock::new(Vec::new())),
            statistics: Arc::new(RwLock::new(UpdateStatistics {
                total_updates: 0,
                successful_updates: 0,
                failed_updates: 0,
                average_update_duration: Duration::from_secs(0),
                last_update: None,
                success_rate: 0.0,
            })),
            logger: Arc::new(tracing::span!(tracing::Level::INFO, "license_updater")),
        }
    }

    /// Start database update operation
    pub async fn start_update(
        &self,
        update_type: UpdateType,
        source: &str,
    ) -> Result<String> {
        let _span = create_span_with_attributes(
            "license_db_update_start",
            vec![
                KeyValue::new("update_type", format!("{:?}", update_type)),
                KeyValue::new("source", source.to_string()),
            ],
        );

        let update_start_counter = counter("license_db_update_start_total", "Total number of update operations started");
        let concurrent_updates_gauge = counter("license_db_concurrent_updates", "Number of concurrent update operations");

        record_counter(&update_start_counter, 1, vec![
            KeyValue::new("update_type", format!("{:?}", update_type)),
            KeyValue::new("source", source.to_string()),
        ]);

        let active_count = self.active_operations.read().await.len() as u64;
        record_counter(&concurrent_updates_gauge, active_count, vec![]);
        let operation_id = format!("{}_{}", source, Utc::now().timestamp());

        let operation = UpdateOperation {
            id: operation_id.clone(),
            update_type,
            source: source.to_string(),
            status: UpdateStatus::InProgress,
            started_at: Utc::now(),
            completed_at: None,
            records_processed: 0,
            records_updated: 0,
            records_failed: 0,
            error_message: None,
            rollback_info: None,
        };

        // Check concurrent update limit
        let active_count = self.active_operations.read().await.len();
        if active_count >= self.config.max_concurrent_updates {
            return Err(InfinitumError::ResourceLimitExceeded {
                resource: "concurrent_updates".to_string(),
                limit: self.config.max_concurrent_updates,
                current: active_count,
            });
        }

        // Store active operation
        self.active_operations.write().await.insert(operation_id.clone(), operation);

        info!(
            operation_id = %operation_id,
            source = %source,
            update_type = ?update_type,
            "Started license database update operation"
        );

        Ok(operation_id)
    }

    /// Update operation progress
    pub async fn update_progress(
        &self,
        operation_id: &str,
        records_processed: u32,
        records_updated: u32,
        records_failed: u32,
    ) -> Result<()> {
        let mut operations = self.active_operations.write().await;
        if let Some(operation) = operations.get_mut(operation_id) {
            operation.records_processed = records_processed;
            operation.records_updated = records_updated;
            operation.records_failed = records_failed;

            if self.config.enable_detailed_logging {
                info!(
                    operation_id = %operation_id,
                    processed = records_processed,
                    updated = records_updated,
                    failed = records_failed,
                    "Update progress updated"
                );
            }
        }
        Ok(())
    }
    /// Complete update operation successfully
    pub async fn complete_update(&self, operation_id: &str) -> Result<()> {
        let mut operations = self.active_operations.write().await;
        if let Some(mut operation) = operations.remove(operation_id) {
            operation.status = UpdateStatus::Completed;
            operation.completed_at = Some(Utc::now());

            // Update statistics
            self.update_statistics(&operation).await;

            // Move to history
            self.update_history.write().await.push(operation.clone());
        let _span = create_span_with_attributes(
            "license_db_update_rollback",
            vec![
                KeyValue::new("operation_id", operation_id.to_string()),
            ],
        );

        let rollback_counter = counter("license_db_update_rollback_total", "Total number of update rollbacks");

        record_counter(&rollback_counter, 1, vec![
            KeyValue::new("operation_id", operation_id.to_string()),
        ]);

            info!(
                operation_id = %operation_id,
                processed = operation.records_processed,
                updated = operation.records_updated,
                failed = operation.records_failed,
                duration = ?operation.completed_at.unwrap().signed_duration_since(operation.started_at),
                "License database update completed successfully"
            );
        }
        Ok(())
    }

    /// Fail update operation
    pub async fn fail_update(&self, operation_id: &str, error_message: &str) -> Result<()> {
        let mut operations = self.active_operations.write().await;
        if let Some(mut operation) = operations.remove(operation_id) {
            operation.status = UpdateStatus::Failed;
            operation.completed_at = Some(Utc::now());
            operation.error_message = Some(error_message.to_string());

            // Update statistics
            self.update_statistics(&operation).await;

            // Move to history
            self.update_history.write().await.push(operation.clone());

            error!(
                operation_id = %operation_id,
                error = %error_message,
                "License database update failed"
            );

            // Auto-rollback if enabled
            if self.config.auto_rollback_on_failure {
                self.rollback_update(operation_id).await?;
            }
        }
        Ok(())
    }

    /// Rollback update operation
    pub async fn rollback_update(&self, operation_id: &str) -> Result<()> {
        let history = self.update_history.read().await;
        if let Some(operation) = history.iter().find(|op| op.id == operation_id) {
            if operation.status != UpdateStatus::Completed {
                return Err(InfinitumError::InvalidOperation {
                    operation: "rollback".to_string(),
                    reason: "Can only rollback completed operations".to_string(),
                });
            }

            // Create rollback info
            let rollback_info = RollbackInfo {
                backup_path: format!("{}/backup_{}", self.config.backup_directory, operation_id),
                rolled_back_at: Utc::now(),
                reason: "Automatic rollback due to update failure".to_string(),
            };

            // Update operation status
            let mut operations = self.active_operations.write().await;
            if let Some(op) = operations.get_mut(operation_id) {
                op.status = UpdateStatus::RolledBack;
                op.rollback_info = Some(rollback_info.clone());
            }

            info!(
                operation_id = %operation_id,
                backup_path = %rollback_info.backup_path,
                "License database update rolled back"
            );
        }
        Ok(())
    }

    /// Get active operations
    pub async fn get_active_operations(&self) -> Vec<UpdateOperation> {
        self.active_operations.read().await.values().cloned().collect()
    }

    /// Get update history
    pub async fn get_update_history(&self, limit: Option<usize>) -> Vec<UpdateOperation> {
        let history = self.update_history.read().await;
        match limit {
            Some(l) => history.iter().rev().take(l).cloned().collect(),
            None => history.clone(),
        }
    }

    /// Get update statistics
    pub async fn get_statistics(&self) -> UpdateStatistics {
        self.statistics.read().await.clone()
    }

    /// Get operation by ID
    pub async fn get_operation(&self, operation_id: &str) -> Option<UpdateOperation> {
        // Check active operations first
        if let Some(op) = self.active_operations.read().await.get(operation_id) {
            return Some(op.clone());
        }

        // Check history
        self.update_history.read().await.iter().find(|op| op.id == operation_id).cloned()
    }

    /// Clean up old operations from history
    pub async fn cleanup_history(&self, max_age_days: u32) -> Result<usize> {
        let cutoff = Utc::now() - chrono::Duration::days(max_age_days as i64);
        let mut history = self.update_history.write().await;

        let initial_len = history.len();
        history.retain(|op| {
            op.completed_at.unwrap_or(op.started_at) > cutoff
        });

        let removed = initial_len - history.len();
        info!("Cleaned up {} old update operations", removed);

        Ok(removed)
    }

    /// Update statistics after operation completion
    async fn update_statistics(&self, operation: &UpdateOperation) {
        let mut stats = self.statistics.write().await;

        stats.total_updates += 1;
        stats.last_update = operation.completed_at;

        match operation.status {
            UpdateStatus::Completed => {
                stats.successful_updates += 1;
            }
            UpdateStatus::Failed => {
                stats.failed_updates += 1;
            }
            _ => {}
        }

        // Calculate success rate
        if stats.total_updates > 0 {
            stats.success_rate = stats.successful_updates as f64 / stats.total_updates as f64;
        }

        // Update average duration
        if let Some(completed_at) = operation.completed_at {
            let duration = completed_at.signed_duration_since(operation.started_at);
            let total_duration = stats.average_update_duration * (stats.total_updates - 1) as u32;
            stats.average_update_duration = total_duration + duration.to_std().unwrap_or(Duration::from_secs(0));
            stats.average_update_duration /= stats.total_updates as u32;
        }
    }
}

impl Default for UpdateConfig {
    fn default() -> Self {
        Self {
            max_concurrent_updates: 3,
            update_timeout_seconds: 3600, // 1 hour
            conflict_resolution: ConflictResolutionStrategy::Merge,
            auto_rollback_on_failure: true,
            backup_directory: "/tmp/license_backups".to_string(),
            enable_detailed_logging: true,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_updater_creation() {
        let config = UpdateConfig::default();
        let updater = LicenseDatabaseUpdater::new(config);
        assert_eq!(updater.get_active_operations().await.len(), 0);
    }

    #[tokio::test]
    async fn test_start_update() {
        let config = UpdateConfig::default();
        let updater = LicenseDatabaseUpdater::new(config);

        let operation_id = updater.start_update(UpdateType::Full, "spdx").await.unwrap();
        assert!(!operation_id.is_empty());

        let active_ops = updater.get_active_operations().await;
        assert_eq!(active_ops.len(), 1);
        assert_eq!(active_ops[0].source, "spdx");
    }

    #[tokio::test]
    async fn test_complete_update() {
        let config = UpdateConfig::default();
        let updater = LicenseDatabaseUpdater::new(config);

        let operation_id = updater.start_update(UpdateType::Incremental, "osi").await.unwrap();
        updater.update_progress(&operation_id, 100, 95, 5).await.unwrap();
        updater.complete_update(&operation_id).await.unwrap();

        let active_ops = updater.get_active_operations().await;
        assert_eq!(active_ops.len(), 0);

        let history = updater.get_update_history(Some(1)).await;
        assert_eq!(history.len(), 1);
        assert_eq!(history[0].status, UpdateStatus::Completed);
    }

    #[tokio::test]
    async fn test_concurrent_update_limit() {
        let mut config = UpdateConfig::default();
        config.max_concurrent_updates = 1;
        let updater = LicenseDatabaseUpdater::new(config);

        // Start first update
        let _op1 = updater.start_update(UpdateType::Full, "spdx").await.unwrap();

        // Second update should fail due to limit
        let result = updater.start_update(UpdateType::Full, "osi").await;
        assert!(result.is_err());
    }
}