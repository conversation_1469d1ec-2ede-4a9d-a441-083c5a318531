//! # License Expression Parser
//!
//! Robust SPDX license expression processing with proper operator precedence,
//! syntax validation, and semantic analysis for complex license scenarios.

use crate::error::{InfinitumError, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashSet;
use tracing::{debug, instrument};

/// Parsed license expression node
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum LicenseNode {
    /// Single license identifier
    License(String),
    /// AND operation
    And(Box<LicenseNode>, Box<LicenseNode>),
    /// OR operation
    Or(Box<LicenseNode>, Box<LicenseNode>),
    /// WITH exception
    With(Box<LicenseNode>, String),
}

/// License expression parser
pub struct LicenseExpressionParser;

impl LicenseExpressionParser {
    /// Parse SPDX license expression
    #[instrument(skip(expression))]
    pub fn parse(expression: &str) -> Result<LicenseNode> {
        debug!("Parsing license expression: {}", expression);

        if expression.trim().is_empty() {
            return Err(InfinitumError::InvalidLicenseExpression {
                expression: expression.to_string(),
                reason: "Empty expression".to_string(),
            });
        }

        let tokens = Self::tokenize(expression)?;
        let ast = Self::parse_tokens(&tokens)?;
        let normalized = Self::normalize(&ast)?;

        debug!("Parsed expression successfully");
        Ok(normalized)
    }

    /// Tokenize expression into components
    fn tokenize(expression: &str) -> Result<Vec<String>> {
        let mut tokens = Vec::new();
        let mut current_token = String::new();
        let mut paren_depth = 0;

        for ch in expression.chars() {
            match ch {
                '(' => {
                    if !current_token.is_empty() {
                        tokens.push(current_token.clone());
                        current_token.clear();
                    }
                    tokens.push("(".to_string());
                    paren_depth += 1;
                }
                ')' => {
                    if !current_token.is_empty() {
                        tokens.push(current_token.clone());
                        current_token.clear();
                    }
                    tokens.push(")".to_string());
                    paren_depth -= 1;
                    if paren_depth < 0 {
                        return Err(InfinitumError::InvalidLicenseExpression {
                            expression: expression.to_string(),
                            reason: "Unmatched closing parenthesis".to_string(),
                        });
                    }
                }
                ' ' => {
                    if !current_token.is_empty() {
                        tokens.push(current_token.clone());
                        current_token.clear();
                    }
                }
                '+' => {
                    if !current_token.is_empty() {
                        tokens.push(current_token.clone());
                        current_token.clear();
                    }
                    tokens.push("+".to_string());
                }
                _ => {
                    current_token.push(ch);
                }
            }
        }

        if !current_token.is_empty() {
            tokens.push(current_token);
        }

        if paren_depth != 0 {
            return Err(InfinitumError::InvalidLicenseExpression {
                expression: expression.to_string(),
                reason: "Unmatched opening parenthesis".to_string(),
            });
        }

        debug!("Tokenized: {:?}", tokens);
        Ok(tokens)
    }

    /// Parse tokens into AST
    fn parse_tokens(tokens: &[String]) -> Result<LicenseNode> {
        let mut index = 0;
        Self::parse_or_expression(tokens, &mut index)
    }

    /// Parse OR expressions (lowest precedence)
    fn parse_or_expression(tokens: &[String], index: &mut usize) -> Result<LicenseNode> {
        let mut left = Self::parse_and_expression(tokens, index)?;

        while *index < tokens.len() && tokens[*index] == "OR" {
            *index += 1; // consume OR
            let right = Self::parse_and_expression(tokens, index)?;
            left = LicenseNode::Or(Box::new(left), Box::new(right));
        }

        Ok(left)
    }

    /// Parse AND expressions (higher precedence)
    fn parse_and_expression(tokens: &[String], index: &mut usize) -> Result<LicenseNode> {
        let mut left = Self::parse_with_expression(tokens, index)?;

        while *index < tokens.len() && tokens[*index] == "AND" {
            *index += 1; // consume AND
            let right = Self::parse_with_expression(tokens, index)?;
            left = LicenseNode::And(Box::new(left), Box::new(right));
        }

        Ok(left)
    }

    /// Parse WITH expressions (highest precedence)
    fn parse_with_expression(tokens: &[String], index: &mut usize) -> Result<LicenseNode> {
        let left = Self::parse_primary(tokens, index)?;

        if *index < tokens.len() && tokens[*index] == "WITH" {
            *index += 1; // consume WITH
            if *index >= tokens.len() {
                return Err(InfinitumError::InvalidLicenseExpression {
                    expression: tokens.join(" "),
                    reason: "Missing exception after WITH".to_string(),
                });
            }
            let exception = tokens[*index].clone();
            *index += 1;
            return Ok(LicenseNode::With(Box::new(left), exception));
        }

        Ok(left)
    }

    /// Parse primary expressions (licenses or parenthesized expressions)
    fn parse_primary(tokens: &[String], index: &mut usize) -> Result<LicenseNode> {
        if *index >= tokens.len() {
            return Err(InfinitumError::InvalidLicenseExpression {
                expression: tokens.join(" "),
                reason: "Unexpected end of expression".to_string(),
            });
        }

        let token = &tokens[*index];
        *index += 1;

        match token.as_str() {
            "(" => {
                let expr = Self::parse_or_expression(tokens, index)?;
                if *index >= tokens.len() || tokens[*index] != ")" {
                    return Err(InfinitumError::InvalidLicenseExpression {
                        expression: tokens.join(" "),
                        reason: "Missing closing parenthesis".to_string(),
                    });
                }
                *index += 1; // consume )
                Ok(expr)
            }
            ")" => Err(InfinitumError::InvalidLicenseExpression {
                expression: tokens.join(" "),
                reason: "Unexpected closing parenthesis".to_string(),
            }),
            _ => {
                // Validate license identifier
                Self::validate_license_identifier(token)?;
                Ok(LicenseNode::License(token.clone()))
            }
        }
    }

    /// Validate license identifier format
    fn validate_license_identifier(license: &str) -> Result<()> {
        if license.is_empty() {
            return Err(InfinitumError::InvalidLicenseExpression {
                expression: license.to_string(),
                reason: "Empty license identifier".to_string(),
            });
        }

        // Basic SPDX license ID validation
        // License IDs should contain only alphanumeric characters, hyphens, and dots
        for ch in license.chars() {
            if !ch.is_alphanumeric() && ch != '-' && ch != '.' && ch != '+' {
                return Err(InfinitumError::InvalidLicenseExpression {
                    expression: license.to_string(),
                    reason: format!("Invalid character '{}' in license identifier", ch),
                });
            }
        }

        Ok(())
    }

    /// Normalize license expression AST
    fn normalize(node: &LicenseNode) -> Result<LicenseNode> {
        match node {
            LicenseNode::License(license) => Ok(LicenseNode::License(license.clone())),
            LicenseNode::And(left, right) => {
                let left_norm = Self::normalize(left)?;
                let right_norm = Self::normalize(right)?;
                Ok(LicenseNode::And(Box::new(left_norm), Box::new(right_norm)))
            }
            LicenseNode::Or(left, right) => {
                let left_norm = Self::normalize(left)?;
                let right_norm = Self::normalize(right)?;
                Ok(LicenseNode::Or(Box::new(left_norm), Box::new(right_norm)))
            }
            LicenseNode::With(license, exception) => {
                let license_norm = Self::normalize(license)?;
                Ok(LicenseNode::With(Box::new(license_norm), exception.clone()))
            }
        }
    }

    /// Extract all license identifiers from expression
    pub fn extract_licenses(node: &LicenseNode) -> HashSet<String> {
        let mut licenses = HashSet::new();

        match node {
            LicenseNode::License(license) => {
                licenses.insert(license.clone());
            }
            LicenseNode::And(left, right) | LicenseNode::Or(left, right) => {
                licenses.extend(Self::extract_licenses(left));
                licenses.extend(Self::extract_licenses(right));
            }
            LicenseNode::With(license, _) => {
                licenses.extend(Self::extract_licenses(license));
            }
        }

        licenses
    }

    /// Convert AST back to string expression
    pub fn to_string(node: &LicenseNode) -> String {
        match node {
            LicenseNode::License(license) => license.clone(),
            LicenseNode::And(left, right) => {
                format!("({} AND {})", Self::to_string(left), Self::to_string(right))
            }
            LicenseNode::Or(left, right) => {
                format!("({} OR {})", Self::to_string(left), Self::to_string(right))
            }
            LicenseNode::With(license, exception) => {
                format!("{} WITH {}", Self::to_string(license), exception)
            }
        }
    }

    /// Validate expression semantics
    pub fn validate_semantics(node: &LicenseNode) -> Result<()> {
        match node {
            LicenseNode::License(license) => {
                // Check for known SPDX license IDs (basic validation)
                if license == "NOASSERTION" || license == "NONE" {
                    return Ok(());
                }
                // Additional semantic checks can be added here
                Ok(())
            }
            LicenseNode::And(left, right) | LicenseNode::Or(left, right) => {
                Self::validate_semantics(left)?;
                Self::validate_semantics(right)?;
                Ok(())
            }
            LicenseNode::With(license, exception) => {
                Self::validate_semantics(license)?;
                // Validate exception identifier
                Self::validate_license_identifier(exception)?;
                Ok(())
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_simple_license_parsing() {
        let result = LicenseExpressionParser::parse("MIT");
        assert!(result.is_ok());
        if let LicenseNode::License(license) = result.unwrap() {
            assert_eq!(license, "MIT");
        }
    }

    #[test]
    fn test_and_expression() {
        let result = LicenseExpressionParser::parse("MIT AND Apache-2.0");
        assert!(result.is_ok());
        let node = result.unwrap();
        assert!(matches!(node, LicenseNode::And(_, _)));
    }

    #[test]
    fn test_or_expression() {
        let result = LicenseExpressionParser::parse("MIT OR Apache-2.0");
        assert!(result.is_ok());
        let node = result.unwrap();
        assert!(matches!(node, LicenseNode::Or(_, _)));
    }

    #[test]
    fn test_complex_expression() {
        let result = LicenseExpressionParser::parse("MIT OR (Apache-2.0 AND BSD-3-Clause)");
        assert!(result.is_ok());
    }

    #[test]
    fn test_with_exception() {
        let result = LicenseExpressionParser::parse("GPL-2.0 WITH Classpath-exception");
        assert!(result.is_ok());
        let node = result.unwrap();
        assert!(matches!(node, LicenseNode::With(_, _)));
    }

    #[test]
    fn test_invalid_expression() {
        let result = LicenseExpressionParser::parse("MIT AND");
        assert!(result.is_err());
    }

    #[test]
    fn test_unmatched_parentheses() {
        let result = LicenseExpressionParser::parse("(MIT AND Apache-2.0");
        assert!(result.is_err());
    }

    #[test]
    fn test_extract_licenses() {
        let node = LicenseExpressionParser::parse("MIT OR Apache-2.0 AND BSD-3-Clause").unwrap();
        let licenses = LicenseExpressionParser::extract_licenses(&node);
        assert_eq!(licenses.len(), 3);
        assert!(licenses.contains("MIT"));
        assert!(licenses.contains("Apache-2.0"));
        assert!(licenses.contains("BSD-3-Clause"));
    }

    #[test]
    fn test_to_string() {
        let original = "MIT OR Apache-2.0";
        let node = LicenseExpressionParser::parse(original).unwrap();
        let reconstructed = LicenseExpressionParser::to_string(&node);
        assert_eq!(reconstructed, "(MIT OR Apache-2.0)");
    }
}