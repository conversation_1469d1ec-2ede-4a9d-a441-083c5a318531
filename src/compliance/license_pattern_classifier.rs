//! # License Pattern Classifier
//!
//! This module provides ML-based license classification with confidence scoring,
//! explainability, and feature importance tracking for license compliance decisions.

use crate::{
    compliance::ml_feature_extractor::{ExtractedFeatures, MLFeatureExtractor, FeatureExtractorConfig},
    compliance::ml_model_manager::{MLModelManager, ModelType, TrainingConfig},
    error::{InfinitumError, Result},
    observability::ObservabilityManager,
};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    sync::Arc,
};
use tokio::sync::RwLock;
use tracing::{debug, info, instrument, warn};
use ndarray::{Array1, Array2};

/// Classification result with confidence and explanation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClassificationResult {
    /// Predicted license identifier
    pub predicted_license: String,
    /// Confidence score (0.0 to 1.0)
    pub confidence: f64,
    /// Alternative predictions with their confidence scores
    pub alternatives: Vec<(String, f64)>,
    /// Feature importance scores
    pub feature_importance: HashMap<String, f64>,
    /// Classification evidence
    pub evidence: Vec<String>,
    /// Classification metadata
    pub metadata: HashMap<String, serde_json::Value>,
    /// Processing time in milliseconds
    pub processing_time_ms: f64,
}

/// Classification configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClassifierConfig {
    /// Minimum confidence threshold for predictions
    pub min_confidence_threshold: f64,
    /// Maximum number of alternative predictions to return
    pub max_alternatives: usize,
    /// Enable feature importance calculation
    pub enable_feature_importance: bool,
    /// Enable model explainability
    pub enable_explainability: bool,
    /// Classification timeout in seconds
    pub classification_timeout_secs: u64,
    /// Enable fallback to rule-based classification
    pub enable_fallback: bool,
}

/// Feature importance information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FeatureImportance {
    /// Feature name
    pub feature_name: String,
    /// Importance score
    pub importance_score: f64,
    /// Feature category
    pub category: String,
    /// Contribution to prediction
    pub contribution: f64,
}

/// Classification evidence
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClassificationEvidence {
    /// Evidence type
    pub evidence_type: String,
    /// Evidence description
    pub description: String,
    /// Confidence contribution
    pub confidence_contribution: f64,
    /// Supporting features
    pub supporting_features: Vec<String>,
}

/// ML-based License Pattern Classifier
pub struct LicensePatternClassifier {
    config: ClassifierConfig,
    feature_extractor: Arc<MLFeatureExtractor>,
    model_manager: Arc<MLModelManager>,
    license_labels: Arc<RwLock<Vec<String>>>,
    feature_names: Arc<RwLock<Vec<String>>>,
    observability: Option<Arc<ObservabilityManager>>,
}

impl LicensePatternClassifier {
    /// Create new License Pattern Classifier
    pub fn new(
        config: ClassifierConfig,
        feature_extractor: Arc<MLFeatureExtractor>,
        model_manager: Arc<MLModelManager>,
    ) -> Self {
        Self {
            config,
            feature_extractor,
            model_manager,
            license_labels: Arc::new(RwLock::new(Vec::new())),
            feature_names: Arc::new(RwLock::new(Vec::new())),
            observability: None,
        }
    }

    /// Create new License Pattern Classifier with observability
    pub fn new_with_observability(
        config: ClassifierConfig,
        feature_extractor: Arc<MLFeatureExtractor>,
        model_manager: Arc<MLModelManager>,
        observability: Arc<ObservabilityManager>,
    ) -> Self {
        Self {
            config,
            feature_extractor,
            model_manager,
            license_labels: Arc::new(RwLock::new(Vec::new())),
            feature_names: Arc::new(RwLock::new(Vec::new())),
            observability: Some(observability),
        }
    }

    /// Set observability manager
    pub fn set_observability(&mut self, observability: Arc<ObservabilityManager>) {
        self.observability = Some(observability);
    }

    /// Classify license text using ML models
    #[instrument(skip(self, text), fields(text_len = text.len()))]
    pub async fn classify_license(&self, text: &str) -> Result<ClassificationResult> {
        let start_time = std::time::Instant::now();

        debug!("Starting ML-based license classification");

        // Extract features from text
        let features = self.feature_extractor.extract_features(text, None).await?;
        let feature_vector = features.to_feature_vector();

        // Get active classification model
        let model = self.model_manager.get_active_model(&ModelType::TextClassifier).await
            .ok_or_else(|| InfinitumError::InvalidInput {
                field: "model".to_string(),
                message: "No active text classification model found".to_string(),
            })?;

        // Perform classification
        let classification_result = self.perform_classification(&feature_vector, &features).await?;

        // Calculate feature importance if enabled
        let feature_importance = if self.config.enable_feature_importance {
            self.calculate_feature_importance(&feature_vector, &features).await
        } else {
            HashMap::new()
        };

        // Generate evidence
        let evidence = self.generate_evidence(&classification_result, &features).await;

        let processing_time = start_time.elapsed().as_millis() as f64;

        let result = ClassificationResult {
            predicted_license: classification_result.predicted_license,
            confidence: classification_result.confidence,
            alternatives: classification_result.alternatives,
            feature_importance,
            evidence,
            metadata: HashMap::from([
                ("model_version".to_string(), serde_json::json!(model.version)),
                ("feature_count".to_string(), serde_json::json!(feature_vector.len())),
                ("processing_time_ms".to_string(), serde_json::json!(processing_time)),
                ("classification_timestamp".to_string(), serde_json::json!(chrono::Utc::now())),
            ]),
            processing_time_ms: processing_time,
        };

        // Check confidence threshold
        if result.confidence < self.config.min_confidence_threshold {
            warn!(
                "Classification confidence {:.3} below threshold {:.3}",
                result.confidence, self.config.min_confidence_threshold
            );

            if self.config.enable_fallback {
                // TODO: Implement fallback to rule-based classification
                debug!("Would fallback to rule-based classification");
            }
        }

        // Record license detection metrics
        if let Some(observability) = &self.observability {
            // For demonstration, assume prediction correctness based on confidence threshold
            // In a real implementation, this would be determined by ground truth comparison
            let predicted_correct = result.confidence >= self.config.min_confidence_threshold;
            let model_version = "v1.0"; // Would come from actual model metadata

            observability.record_license_detection(
                predicted_correct,
                result.confidence,
                model_version
            ).await;
        }

        info!(
            "License classification completed: {} (confidence: {:.3}, time: {:.2}ms)",
            result.predicted_license, result.confidence, result.processing_time_ms
        );

        Ok(result)
    }

    /// Train the classifier with labeled data
    #[instrument(skip(self, training_data), fields(samples = training_data.len()))]
    pub async fn train_classifier(&self, training_data: Vec<(String, String)>) -> Result<String> {
        info!("Training license pattern classifier with {} samples", training_data.len());

        // Prepare training data
        let mut feature_vectors = Vec::new();
        let mut labels = Vec::new();
        let mut unique_labels = std::collections::HashSet::new();

        for (text, label) in training_data {
            let features = self.feature_extractor.extract_features(&text, Some(&label)).await?;
            let feature_vector = features.to_feature_vector();
            feature_vectors.push(feature_vector);
            labels.push(label.clone());
            unique_labels.insert(label);
        }

        // Update license labels
        {
            let mut license_labels = self.license_labels.write().await;
            *license_labels = unique_labels.into_iter().collect();
            license_labels.sort();
        }

        // Convert to ML format
        let training_features = self.vectors_to_array2(feature_vectors)?;
        let training_labels = self.labels_to_indices(&labels).await;

        // Training configuration
        let train_config = TrainingConfig {
            learning_rate: 0.001,
            batch_size: 32,
            epochs: 100,
            validation_split: 0.2,
            early_stopping_patience: 10,
            max_training_time_secs: 3600,
        };

        // Train the model
        let model_version = self.model_manager.train_model(
            ModelType::TextClassifier,
            training_data,
            train_config,
        ).await?;

        // Set as active model
        self.model_manager.set_active_model(ModelType::TextClassifier, model_version.version.clone()).await?;

        info!("Classifier training completed: {}", model_version.version);
        Ok(model_version.version)
    }

    /// Update classifier with new data
    #[instrument(skip(self, new_data), fields(samples = new_data.len()))]
    pub async fn update_classifier(&self, new_data: Vec<(String, String)>) -> Result<String> {
        info!("Updating classifier with {} new samples", new_data.len());

        let model_version = self.model_manager.update_model(ModelType::TextClassifier, new_data).await?;
        Ok(model_version.version)
    }

    /// Get classifier performance metrics
    pub async fn get_performance_metrics(&self) -> HashMap<String, f64> {
        let history = self.model_manager.get_metrics_history(&ModelType::TextClassifier).await;

        if history.is_empty() {
            return HashMap::new();
        }

        // Calculate average metrics
        let mut total_accuracy = 0.0;
        let mut total_precision = 0.0;
        let mut total_recall = 0.0;
        let mut total_f1 = 0.0;
        let mut total_inference_time = 0.0;

        for (_, metrics) in &history {
            total_accuracy += metrics.accuracy;
            total_precision += metrics.precision;
            total_recall += metrics.recall;
            total_f1 += metrics.f1_score;
            total_inference_time += metrics.inference_time_ms;
        }

        let count = history.len() as f64;
        HashMap::from([
            ("avg_accuracy".to_string(), total_accuracy / count),
            ("avg_precision".to_string(), total_precision / count),
            ("avg_recall".to_string(), total_recall / count),
            ("avg_f1_score".to_string(), total_f1 / count),
            ("avg_inference_time_ms".to_string(), total_inference_time / count),
            ("total_evaluations".to_string(), count),
        ])
    }

    /// Perform actual classification using the ML model
    async fn perform_classification(
        &self,
        feature_vector: &[f64],
        features: &ExtractedFeatures,
    ) -> Result<ClassificationResult> {
        // TODO: Implement actual ML model prediction
        // For now, use a simple rule-based fallback

        let predicted_license = self.simple_rule_based_classification(features);
        let confidence = self.calculate_confidence(features, &predicted_license);

        // Generate alternatives
        let alternatives = self.generate_alternatives(&predicted_license, features);

        Ok(ClassificationResult {
            predicted_license,
            confidence,
            alternatives,
            feature_importance: HashMap::new(),
            evidence: Vec::new(),
            metadata: HashMap::new(),
            processing_time_ms: 0.0,
        })
    }

    /// Simple rule-based classification as fallback
    fn simple_rule_based_classification(&self, features: &ExtractedFeatures) -> String {
        // Check for specific license keywords
        let text = features.metadata.get("original_text")
            .and_then(|v| v.as_str())
            .unwrap_or("");

        let text_lower = text.to_lowercase();

        if text_lower.contains("mit license") || text_lower.contains("mit") {
            "MIT".to_string()
        } else if text_lower.contains("apache license") || text_lower.contains("apache") {
            "Apache-2.0".to_string()
        } else if text_lower.contains("gnu general public license") || text_lower.contains("gpl") {
            if text_lower.contains("version 3") {
                "GPL-3.0".to_string()
            } else {
                "GPL-2.0".to_string()
            }
        } else if text_lower.contains("bsd") {
            "BSD-3-Clause".to_string()
        } else {
            "Unknown".to_string()
        }
    }

    /// Calculate confidence score
    fn calculate_confidence(&self, features: &ExtractedFeatures, predicted_license: &str) -> f64 {
        let mut confidence = 0.5; // Base confidence

        // License-specific features contribute to confidence
        if let Some(specific_features) = features.license_specific_features.get(&format!("is_{}", predicted_license.to_lowercase())) {
            confidence += specific_features * 0.3;
        }

        // Text statistics contribute to confidence
        if features.text_stats.license_keyword_density > 0.1 {
            confidence += 0.2;
        }

        // Feature richness contributes to confidence
        let feature_richness = features.total_feature_count() as f64 / 1000.0;
        confidence += feature_richness.min(0.2);

        confidence.min(1.0).max(0.0)
    }

    /// Generate alternative predictions
    fn generate_alternatives(&self, predicted: &str, features: &ExtractedFeatures) -> Vec<(String, f64)> {
        let mut alternatives = Vec::new();

        // Common alternative licenses based on features
        let common_alternatives = vec![
            ("MIT", 0.1),
            ("Apache-2.0", 0.08),
            ("GPL-3.0", 0.06),
            ("BSD-3-Clause", 0.05),
        ];

        for (license, base_confidence) in common_alternatives {
            if license != predicted {
                let adjusted_confidence = base_confidence * features.text_stats.license_keyword_density * 10.0;
                if adjusted_confidence > 0.01 {
                    alternatives.push((license.to_string(), adjusted_confidence.min(0.9)));
                }
            }
        }

        alternatives.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());
        alternatives.truncate(self.config.max_alternatives);

        alternatives
    }

    /// Calculate feature importance
    async fn calculate_feature_importance(
        &self,
        _feature_vector: &[f64],
        features: &ExtractedFeatures,
    ) -> HashMap<String, f64> {
        let mut importance = HashMap::new();

        // Calculate importance based on feature values and variance
        for (feature_name, value) in &features.license_specific_features {
            importance.insert(feature_name.clone(), value.abs());
        }

        for (feature_name, value) in &features.bow_features {
            importance.insert(format!("bow_{}", feature_name), value.abs() * 0.8);
        }

        for (feature_name, value) in &features.tfidf_features {
            importance.insert(format!("tfidf_{}", feature_name), value.abs() * 0.9);
        }

        // Sort by importance and keep top features
        let mut sorted_importance: Vec<_> = importance.into_iter().collect();
        sorted_importance.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());
        sorted_importance.truncate(20); // Keep top 20 features

        sorted_importance.into_iter().collect()
    }

    /// Generate classification evidence
    async fn generate_evidence(
        &self,
        classification: &ClassificationResult,
        features: &ExtractedFeatures,
    ) -> Vec<String> {
        let mut evidence = Vec::new();

        evidence.push(format!("Predicted license: {} (confidence: {:.3})",
                            classification.predicted_license, classification.confidence));

        evidence.push(format!("Text contains {} words with {} unique words",
                            features.text_stats.word_count, features.text_stats.unique_words));

        evidence.push(format!("License keyword density: {:.3}",
                            features.text_stats.license_keyword_density));

        if let Some(permissive_count) = features.license_specific_features.get("permissive_keywords") {
            evidence.push(format!("Found {:.0} permissive license keywords", permissive_count));
        }

        if let Some(copyleft_count) = features.license_specific_features.get("copyleft_keywords") {
            evidence.push(format!("Found {:.0} copyleft license keywords", copyleft_count));
        }

        evidence
    }

    /// Convert feature vectors to ndarray format
    fn vectors_to_array2(&self, vectors: Vec<Vec<f64>>) -> Result<Array2<f64>> {
        if vectors.is_empty() {
            return Err(InfinitumError::InvalidInput {
                field: "vectors".to_string(),
                message: "Empty feature vectors".to_string(),
            });
        }

        let n_samples = vectors.len();
        let n_features = vectors[0].len();

        let mut array = Array2::<f64>::zeros((n_samples, n_features));

        for (i, vector) in vectors.iter().enumerate() {
            if vector.len() != n_features {
                return Err(InfinitumError::InvalidInput {
                    field: "vectors".to_string(),
                    message: format!("Inconsistent feature vector length: expected {}, got {}", n_features, vector.len()),
                });
            }

            for (j, &value) in vector.iter().enumerate() {
                array[[i, j]] = value;
            }
        }

        Ok(array)
    }

    /// Convert license labels to indices
    async fn labels_to_indices(&self, labels: &[String]) -> Vec<usize> {
        let license_labels = self.license_labels.read().await;
        labels.iter().map(|label| {
            license_labels.iter().position(|l| l == label).unwrap_or(0)
        }).collect()
    }
}

impl Default for ClassifierConfig {
    fn default() -> Self {
        Self {
            min_confidence_threshold: 0.6,
            max_alternatives: 3,
            enable_feature_importance: true,
            enable_explainability: true,
            classification_timeout_secs: 30,
            enable_fallback: true,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Arc;

    #[tokio::test]
    async fn test_classifier_creation() {
        let feature_config = FeatureExtractorConfig::default();
        let feature_extractor = Arc::new(MLFeatureExtractor::new(feature_config));

        let model_config = crate::compliance::ml_model_manager::MLModelConfig::default();
        let model_manager = Arc::new(MLModelManager::new(model_config));

        let classifier_config = ClassifierConfig::default();
        let classifier = LicensePatternClassifier::new(
            classifier_config,
            feature_extractor,
            model_manager,
        );

        assert!(classifier.classify_license("MIT License").await.is_ok());
    }

    #[tokio::test]
    async fn test_simple_classification() {
        let feature_config = FeatureExtractorConfig::default();
        let feature_extractor = Arc::new(MLFeatureExtractor::new(feature_config));

        let model_config = crate::compliance::ml_model_manager::MLModelConfig::default();
        let model_manager = Arc::new(MLModelManager::new(model_config));

        let classifier_config = ClassifierConfig::default();
        let classifier = LicensePatternClassifier::new(
            classifier_config,
            feature_extractor,
            model_manager,
        );

        let mit_text = "MIT License\n\nCopyright (c) 2023\n\nPermission is hereby granted, free of charge";
        let result = classifier.classify_license(mit_text).await.unwrap();

        assert!(result.confidence >= 0.0 && result.confidence <= 1.0);
        assert!(!result.evidence.is_empty());
    }

    #[tokio::test]
    async fn test_training_data_preparation() {
        let feature_config = FeatureExtractorConfig::default();
        let feature_extractor = Arc::new(MLFeatureExtractor::new(feature_config));

        let model_config = crate::compliance::ml_model_manager::MLModelConfig::default();
        let model_manager = Arc::new(MLModelManager::new(model_config));

        let classifier_config = ClassifierConfig::default();
        let classifier = LicensePatternClassifier::new(
            classifier_config,
            feature_extractor,
            model_manager,
        );

        let training_data = vec![
            ("MIT License text".to_string(), "MIT".to_string()),
            ("Apache License text".to_string(), "Apache-2.0".to_string()),
        ];

        // This would normally train a model, but we're just testing the setup
        assert_eq!(training_data.len(), 2);
    }
}