//! # License Update Configuration
//!
//! Configuration management for license database update system.
//! Provides settings for update frequencies, sources, and system behavior.

use crate::error::{InfinitumError, Result};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, fs, path::Path};

/// Main configuration for license database updates
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct LicenseUpdateConfig {
    /// Whether the license update system is enabled
    pub enabled: bool,
    /// Update sources configuration
    pub sources: HashMap<String, UpdateSourceConfig>,
    /// Scheduler configuration
    pub scheduler: SchedulerConfig,
    /// Processor configuration
    pub processor: ProcessorConfig,
    /// Registry configuration
    pub registry: RegistryConfig,
    /// Notification settings
    pub notifications: NotificationConfig,
    /// Monitoring settings
    pub monitoring: MonitoringConfig,
}

/// Configuration for an update source
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct UpdateSourceConfig {
    /// Source name (e.g., "spdx", "osi", "fossology")
    pub name: String,
    /// Whether this source is enabled
    pub enabled: bool,
    /// Update frequency in seconds
    pub update_frequency_seconds: u64,
    /// API endpoint URL
    pub api_url: String,
    /// API version
    pub api_version: String,
    /// Authentication configuration
    pub auth: Option<AuthConfig>,
    /// Rate limiting configuration
    pub rate_limit: RateLimitConfig,
    /// Retry configuration
    pub retry: RetryConfig,
    /// Custom headers
    pub headers: HashMap<String, String>,
    /// Source-specific settings
    pub settings: HashMap<String, serde_json::Value>,
}

/// Authentication configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthConfig {
    /// Authentication type
    pub auth_type: AuthType,
    /// API key (for API key auth)
    pub api_key: Option<String>,
    /// Username (for basic auth)
    pub username: Option<String>,
    /// Password (for basic auth)
    pub password: Option<String>,
    /// Token endpoint (for OAuth)
    pub token_endpoint: Option<String>,
    /// Client ID (for OAuth)
    pub client_id: Option<String>,
    /// Client secret (for OAuth)
    pub client_secret: Option<String>,
}

/// Authentication type
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum AuthType {
    /// No authentication
    None,
    /// API key authentication
    ApiKey,
    /// Basic authentication
    Basic,
    /// OAuth 2.0
    OAuth2,
    /// Bearer token
    Bearer,
}

/// Rate limiting configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    /// Requests per second
    pub requests_per_second: f64,
    /// Burst size
    pub burst_size: u32,
    /// Timeout in seconds
    pub timeout_seconds: u64,
}

/// Retry configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetryConfig {
    /// Maximum number of retries
    pub max_retries: u32,
    /// Initial backoff in seconds
    pub initial_backoff_seconds: u64,
    /// Maximum backoff in seconds
    pub max_backoff_seconds: u64,
    /// Backoff multiplier
    pub backoff_multiplier: f64,
}

/// Scheduler configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SchedulerConfig {
    /// Maximum concurrent jobs
    pub max_concurrent_jobs: usize,
    /// Job queue size
    pub job_queue_size: usize,
    /// Job timeout in seconds
    pub job_timeout_seconds: u64,
    /// Health check interval in seconds
    pub health_check_interval_seconds: u64,
    /// Enable detailed logging
    pub enable_detailed_logging: bool,
}

/// Processor configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessorConfig {
    /// Enable impact assessment
    pub enable_impact_assessment: bool,
    /// Minimum confidence threshold (0.0-1.0)
    pub min_confidence_threshold: f64,
    /// Enable validation
    pub enable_validation: bool,
    /// Batch size for processing
    pub batch_size: usize,
    /// Quality assurance checks
    pub quality_checks: Vec<String>,
    /// Conflict resolution strategy
    pub conflict_resolution: String,
}

/// Registry configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegistryConfig {
    /// Enable auto-enrichment
    pub enable_auto_enrichment: bool,
    /// Enable cross-referencing
    pub enable_cross_referencing: bool,
    /// Enable quality scoring
    pub enable_quality_scoring: bool,
    /// Minimum quality threshold
    pub min_quality_threshold: f64,
    /// Enable caching
    pub enable_caching: bool,
    /// Cache TTL in seconds
    pub cache_ttl_seconds: u64,
    /// Maximum search results
    pub max_search_results: usize,
}

/// Notification configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationConfig {
    /// Enable notifications
    pub enabled: bool,
    /// Notification channels
    pub channels: Vec<NotificationChannel>,
    /// Notification events
    pub events: Vec<String>,
    /// Email configuration
    pub email: Option<EmailConfig>,
    /// Webhook configuration
    pub webhooks: Vec<WebhookConfig>,
}

/// Notification channel
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum NotificationChannel {
    /// Email notifications
    Email,
    /// Webhook notifications
    Webhook,
    /// Log notifications
    Log,
    /// Database notifications
    Database,
}

/// Email configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailConfig {
    /// SMTP server
    pub smtp_server: String,
    /// SMTP port
    pub smtp_port: u16,
    /// Username
    pub username: String,
    /// Password
    pub password: String,
    /// From address
    pub from_address: String,
    /// To addresses
    pub to_addresses: Vec<String>,
    /// Use TLS
    pub use_tls: bool,
}

/// Webhook configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebhookConfig {
    /// Webhook name
    pub name: String,
    /// Webhook URL
    pub url: String,
    /// HTTP method
    pub method: String,
    /// Headers
    pub headers: HashMap<String, String>,
    /// Retry configuration
    pub retry: RetryConfig,
}

/// Monitoring configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringConfig {
    /// Enable metrics collection
    pub enable_metrics: bool,
    /// Metrics prefix
    pub metrics_prefix: String,
    /// Enable health checks
    pub enable_health_checks: bool,
    /// Health check endpoint
    pub health_check_endpoint: String,
    /// Alert thresholds
    pub alert_thresholds: HashMap<String, f64>,
}

impl LicenseUpdateConfig {
    /// Load configuration from file
    pub fn from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let content = fs::read_to_string(path)?;
        let config: LicenseUpdateConfig = serde_yaml::from_str(&content)?;
        Ok(config)
    }

    /// Save configuration to file
    pub fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let content = serde_yaml::to_string(self)?;
        fs::write(path, content)?;
        Ok(())
    }

    /// Get source configuration by name
    pub fn get_source_config(&self, name: &str) -> Option<&UpdateSourceConfig> {
        self.sources.get(name)
    }

    /// Validate configuration
    pub fn validate(&self) -> Result<()> {
        // Validate sources
        for (name, source) in &self.sources {
            if source.enabled {
                if source.api_url.is_empty() {
                    return Err(InfinitumError::ConfigurationError {
                        message: format!("Source '{}' has empty API URL", name),
                    });
                }

                if source.update_frequency_seconds == 0 {
                    return Err(InfinitumError::ConfigurationError {
                        message: format!("Source '{}' has invalid update frequency", name),
                    });
                }
            }
        }

        // Validate scheduler settings
        if self.scheduler.max_concurrent_jobs == 0 {
            return Err(InfinitumError::ConfigurationError {
                message: "Max concurrent jobs must be greater than 0".to_string(),
            });
        }

        // Validate processor settings
        if self.processor.min_confidence_threshold < 0.0 || self.processor.min_confidence_threshold > 1.0 {
            return Err(InfinitumError::ConfigurationError {
                message: "Confidence threshold must be between 0.0 and 1.0".to_string(),
            });
        }

        Ok(())
    }
}

impl Default for LicenseUpdateConfig {
    fn default() -> Self {
        let mut sources = HashMap::new();

        // Default SPDX configuration
        sources.insert(
            "spdx".to_string(),
            UpdateSourceConfig {
                name: "spdx".to_string(),
                enabled: true,
                update_frequency_seconds: 86400, // 24 hours
                api_url: "https://api.spdx.org".to_string(),
                api_version: "v1".to_string(),
                auth: None,
                rate_limit: RateLimitConfig {
                    requests_per_second: 10.0,
                    burst_size: 50,
                    timeout_seconds: 30,
                },
                retry: RetryConfig {
                    max_retries: 3,
                    initial_backoff_seconds: 1,
                    max_backoff_seconds: 60,
                    backoff_multiplier: 2.0,
                },
                headers: HashMap::new(),
                settings: HashMap::new(),
            },
        );

        // Default OSI configuration
        sources.insert(
            "osi".to_string(),
            UpdateSourceConfig {
                name: "osi".to_string(),
                enabled: true,
                update_frequency_seconds: 86400, // 24 hours
                api_url: "https://api.opensource.org".to_string(),
                api_version: "v1".to_string(),
                auth: None,
                rate_limit: RateLimitConfig {
                    requests_per_second: 5.0,
                    burst_size: 25,
                    timeout_seconds: 30,
                },
                retry: RetryConfig {
                    max_retries: 3,
                    initial_backoff_seconds: 1,
                    max_backoff_seconds: 60,
                    backoff_multiplier: 2.0,
                },
                headers: HashMap::new(),
                settings: HashMap::new(),
            },
        );

        Self {
            enabled: true,
            sources,
            scheduler: SchedulerConfig {
                max_concurrent_jobs: 3,
                job_queue_size: 1000,
                job_timeout_seconds: 3600,
                health_check_interval_seconds: 300,
                enable_detailed_logging: true,
            },
            processor: ProcessorConfig {
                enable_impact_assessment: true,
                min_confidence_threshold: 0.8,
                enable_validation: true,
                batch_size: 100,
                quality_checks: vec![
                    "syntax_validation".to_string(),
                    "duplicate_detection".to_string(),
                    "spdx_validation".to_string(),
                ],
                conflict_resolution: "merge".to_string(),
            },
            registry: RegistryConfig {
                enable_auto_enrichment: true,
                enable_cross_referencing: true,
                enable_quality_scoring: true,
                min_quality_threshold: 0.3,
                enable_caching: true,
                cache_ttl_seconds: 3600,
                max_search_results: 1000,
            },
            notifications: NotificationConfig {
                enabled: false,
                channels: vec![NotificationChannel::Log],
                events: vec![
                    "update_completed".to_string(),
                    "update_failed".to_string(),
                    "conflict_detected".to_string(),
                ],
                email: None,
                webhooks: Vec::new(),
            },
            monitoring: MonitoringConfig {
                enable_metrics: true,
                metrics_prefix: "license_update".to_string(),
                enable_health_checks: true,
                health_check_endpoint: "/health/license-updates".to_string(),
                alert_thresholds: HashMap::new(),
            },
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;

    #[test]
    fn test_default_config() {
        let config = LicenseUpdateConfig::default();
        assert!(config.enabled);
        assert!(config.sources.contains_key("spdx"));
        assert!(config.sources.contains_key("osi"));
    }

    #[test]
    fn test_config_validation() {
        let mut config = LicenseUpdateConfig::default();
        assert!(config.validate().is_ok());

        // Test invalid configuration
        config.sources.get_mut("spdx").unwrap().api_url = "".to_string();
        assert!(config.validate().is_err());
    }

    #[tokio::test]
    async fn test_config_file_operations() {
        let config = LicenseUpdateConfig::default();
        let temp_file = NamedTempFile::new().unwrap();

        // Save config
        config.save_to_file(temp_file.path()).unwrap();

        // Load config
        let loaded_config = LicenseUpdateConfig::from_file(temp_file.path()).unwrap();
        assert_eq!(config.enabled, loaded_config.enabled);
    }
}