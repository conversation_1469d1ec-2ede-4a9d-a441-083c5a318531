//! # License Update Integration Tests
//!
//! Integration tests for the license database update system.
//! Tests the interaction between all components and ensures backward compatibility.

use crate::compliance::{
    global_license_registry::{GlobalLicenseRegistry, RegistryConfig},
    license_database_updater::{LicenseDatabaseUpdater, UpdateConfig},
    license_update_config::LicenseUpdateConfig,
    license_update_processor::{LicenseUpdateProcessor, ProcessorConfig, UpdateSource},
    spdx_license_client::{SpdxLicenseClient, SpdxClientConfig},
    osi_license_client::{OsiLicenseClient, OsiClientConfig},
    update_scheduler::{UpdateScheduler, SchedulerConfig},
    ComplianceOrchestrator,
};
use crate::config::ComplianceConfig;
use std::sync::Arc;
use tokio::sync::RwLock;

/// Integration test for the complete license update system
pub struct LicenseUpdateIntegrationTest {
    orchestrator: ComplianceOrchestrator,
    config: LicenseUpdateConfig,
}

impl LicenseUpdateIntegrationTest {
    /// Create new integration test
    pub fn new() -> Self {
        let compliance_config = ComplianceConfig::default();
        let orchestrator = ComplianceOrchestrator::new(compliance_config);
        let config = LicenseUpdateConfig::default();

        Self {
            orchestrator,
            config,
        }
    }

    /// Test basic component initialization
    pub async fn test_component_initialization(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        println!("Testing component initialization...");

        // Initialize SPDX client
        let spdx_config = SpdxClientConfig::default();
        let spdx_client = SpdxLicenseClient::new(spdx_config);
        self.orchestrator.enable_spdx_client(spdx_client);

        assert!(self.orchestrator.has_spdx_client());
        println!("✓ SPDX client initialized");

        // Initialize OSI client
        let osi_config = OsiClientConfig::default();
        let osi_client = OsiLicenseClient::new(osi_config);
        self.orchestrator.enable_osi_client(osi_client);

        assert!(self.orchestrator.has_osi_client());
        println!("✓ OSI client initialized");

        // Initialize processor
        let processor_config = ProcessorConfig::default();
        let processor = LicenseUpdateProcessor::new(processor_config);
        self.orchestrator.enable_update_processor(processor);

        assert!(self.orchestrator.has_update_processor());
        println!("✓ Update processor initialized");

        // Initialize registry
        let registry_config = RegistryConfig::default();
        let registry = GlobalLicenseRegistry::new(registry_config);
        self.orchestrator.enable_license_registry(registry);

        assert!(self.orchestrator.has_license_registry());
        println!("✓ License registry initialized");

        // Initialize database updater
        let updater_config = UpdateConfig::default();
        let updater = LicenseDatabaseUpdater::new(updater_config);
        self.orchestrator.enable_license_updates(updater);

        assert!(self.orchestrator.has_license_updates());
        println!("✓ Database updater initialized");

        // Initialize scheduler
        let scheduler_config = SchedulerConfig::default();
        let scheduler = UpdateScheduler::new(scheduler_config);
        self.orchestrator.enable_update_scheduler(scheduler);

        assert!(self.orchestrator.has_update_scheduler());
        println!("✓ Update scheduler initialized");

        println!("All components initialized successfully!");
        Ok(())
    }

    /// Test component connectivity
    pub async fn test_connectivity(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        println!("Testing component connectivity...");

        if let Some(spdx_client) = self.orchestrator.spdx_client() {
            // Test SPDX connectivity (this will fail in test environment, but should not panic)
            let connectivity = spdx_client.validate_connectivity().await;
            println!("SPDX connectivity test result: {:?}", connectivity);
        }

        if let Some(osi_client) = self.orchestrator.osi_client() {
            // Test OSI connectivity (this will fail in test environment, but should not panic)
            let connectivity = osi_client.validate_connectivity().await;
            println!("OSI connectivity test result: {:?}", connectivity);
        }

        println!("Connectivity tests completed!");
        Ok(())
    }

    /// Test configuration validation
    pub async fn test_configuration(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        println!("Testing configuration validation...");

        // Test default configuration
        let result = self.config.validate();
        assert!(result.is_ok(), "Default configuration should be valid");
        println!("✓ Default configuration is valid");

        // Test invalid configuration
        let mut invalid_config = self.config.clone();
        invalid_config.sources.get_mut("spdx").unwrap().api_url = "".to_string();
        let result = invalid_config.validate();
        assert!(result.is_err(), "Invalid configuration should fail validation");
        println!("✓ Invalid configuration properly rejected");

        println!("Configuration tests completed!");
        Ok(())
    }

    /// Test backward compatibility
    pub async fn test_backward_compatibility(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        println!("Testing backward compatibility...");

        // Test that existing ComplianceOrchestrator methods still work
        let _edge_case_detector = self.orchestrator.edge_case_detector();
        let _compliance_validator = self.orchestrator.compliance_validator();
        let _dual_licensing_handler = self.orchestrator.dual_licensing_handler();
        let _i18n_engine = self.orchestrator.internationalization_engine();

        println!("✓ Existing orchestrator methods still work");

        // Test that new methods don't interfere with existing functionality
        assert!(!self.orchestrator.has_spdx_client()); // Should be false initially
        assert!(!self.orchestrator.has_osi_client()); // Should be false initially
        assert!(!self.orchestrator.has_update_processor()); // Should be false initially
        assert!(!self.orchestrator.has_license_registry()); // Should be false initially
        assert!(!self.orchestrator.has_license_updates()); // Should be false initially
        assert!(!self.orchestrator.has_update_scheduler()); // Should be false initially

        println!("✓ New methods don't interfere with existing functionality");

        println!("Backward compatibility tests completed!");
        Ok(())
    }

    /// Test error handling
    pub async fn test_error_handling(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        println!("Testing error handling...");

        // Test with uninitialized components
        let result = self.orchestrator.get_license_update_stats().await;
        assert!(result.is_none(), "Should return None for uninitialized processor");
        println!("✓ Properly handles uninitialized components");

        let result = self.orchestrator.get_registry_stats().await;
        assert!(result.is_none(), "Should return None for uninitialized registry");
        println!("✓ Properly handles uninitialized registry");

        let result = self.orchestrator.get_scheduler_stats().await;
        assert!(result.is_none(), "Should return None for uninitialized scheduler");
        println!("✓ Properly handles uninitialized scheduler");

        println!("Error handling tests completed!");
        Ok(())
    }

    /// Run all integration tests
    pub async fn run_all_tests(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        println!("Running license update integration tests...\n");

        self.test_component_initialization().await?;
        println!();

        self.test_connectivity().await?;
        println!();

        self.test_configuration().await?;
        println!();

        self.test_backward_compatibility().await?;
        println!();

        self.test_error_handling().await?;
        println!();

        println!("🎉 All integration tests passed!");
        Ok(())
    }
}

#[cfg(test)]
mod integration_tests {
    use super::*;

    #[tokio::test]
    async fn test_license_update_integration() {
        let mut test = LicenseUpdateIntegrationTest::new();
        let result = test.run_all_tests().await;
        assert!(result.is_ok(), "Integration tests should pass");
    }

    #[tokio::test]
    async fn test_component_initialization() {
        let mut test = LicenseUpdateIntegrationTest::new();
        let result = test.test_component_initialization().await;
        assert!(result.is_ok(), "Component initialization should succeed");
    }

    #[tokio::test]
    async fn test_configuration_validation() {
        let mut test = LicenseUpdateIntegrationTest::new();
        let result = test.test_configuration().await;
        assert!(result.is_ok(), "Configuration validation should succeed");
    }

    #[tokio::test]
    async fn test_backward_compatibility() {
        let mut test = LicenseUpdateIntegrationTest::new();
        let result = test.test_backward_compatibility().await;
        assert!(result.is_ok(), "Backward compatibility should be maintained");
    }
}