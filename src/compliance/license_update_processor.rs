//! # License Update Processor
//!
//! Processes and validates license updates from multiple sources.
//! Handles impact assessment, incremental updates, and quality assurance.

use crate::{
    compliance::{
        spdx_license_client::{SpdxLicense, SpdxLicenseClient},
        osi_license_client::{OsiLicense, OsiLicenseClient},
    },
    error::{InfinitumError, Result},
    scanners::ScanResult,
};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, sync::Arc};
use tokio::sync::RwLock;
use tracing::{error, info, warn};

/// Update source type
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum UpdateSource {
    /// SPDX license database
    Spdx,
    /// OSI approved licenses
    Osi,
    /// Fossology license database
    Fossology,
    /// Custom license database
    Custom,
}

/// License update record
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LicenseUpdate {
    /// License ID
    pub license_id: String,
    /// Source of the update
    pub source: UpdateSource,
    /// Update type
    pub update_type: UpdateType,
    /// Old license data (if any)
    pub old_data: Option<LicenseData>,
    /// New license data
    pub new_data: LicenseData,
    /// Update timestamp
    pub timestamp: DateTime<Utc>,
    /// Confidence score (0.0-1.0)
    pub confidence: f64,
}

/// Update type
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum UpdateType {
    /// New license added
    Added,
    /// License modified
    Modified,
    /// License deprecated
    Deprecated,
    /// License removed
    Removed,
}

/// Unified license data structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LicenseData {
    /// License ID
    pub id: String,
    /// License name
    pub name: String,
    /// OSI approved status
    pub osi_approved: Option<bool>,
    /// SPDX identifier
    pub spdx_id: Option<String>,
    /// License text
    pub text: Option<String>,
    /// Deprecated status
    pub deprecated: bool,
    /// Last updated
    pub last_updated: DateTime<Utc>,
    /// Source URLs
    pub source_urls: Vec<String>,
    /// Additional metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Impact assessment result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImpactAssessment {
    /// Update that caused the impact
    pub update: LicenseUpdate,
    /// Affected scan results
    pub affected_scans: Vec<String>,
    /// Risk level of the impact
    pub risk_level: RiskLevel,
    /// Impact description
    pub description: String,
    /// Recommended actions
    pub recommended_actions: Vec<String>,
    /// Assessment timestamp
    pub assessed_at: DateTime<Utc>,
}

/// Risk level for impact assessment
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
#[serde(rename_all = "lowercase")]
pub enum RiskLevel {
    /// No risk
    None,
    /// Low risk
    Low,
    /// Medium risk
    Medium,
    /// High risk
    High,
    /// Critical risk
    Critical,
}

/// Processing statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessingStats {
    /// Total updates processed
    pub total_processed: u64,
    /// Updates successfully applied
    pub successfully_applied: u64,
    /// Updates with validation errors
    pub validation_errors: u64,
    /// Updates with conflicts
    pub conflicts_detected: u64,
    /// Impact assessments performed
    pub impact_assessments: u64,
    /// Average processing time
    pub average_processing_time_ms: f64,
    /// Last processing timestamp
    pub last_processed: Option<DateTime<Utc>>,
}

/// Processor configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessorConfig {
    /// Enable impact assessment
    pub enable_impact_assessment: bool,
    /// Minimum confidence threshold (0.0-1.0)
    pub min_confidence_threshold: f64,
    /// Enable validation
    pub enable_validation: bool,
    /// Batch size for processing
    pub batch_size: usize,
    /// Enable detailed logging
    pub enable_detailed_logging: bool,
    /// Quality assurance checks
    pub quality_checks: Vec<QualityCheck>,
}

/// Quality assurance check
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum QualityCheck {
    /// Validate license syntax
    SyntaxValidation,
    /// Check for duplicate licenses
    DuplicateDetection,
    /// Validate SPDX format
    SpdxValidation,
    /// Cross-reference validation
    CrossReferenceValidation,
    /// Text consistency check
    TextConsistency,
}

/// License Update Processor
pub struct LicenseUpdateProcessor {
    /// Configuration
    config: ProcessorConfig,
    /// SPDX client
    spdx_client: Option<Arc<RwLock<SpdxLicenseClient>>>,
    /// OSI client
    osi_client: Option<Arc<RwLock<OsiLicenseClient>>>,
    /// Processing statistics
    stats: Arc<RwLock<ProcessingStats>>,
    /// Current license database
    license_database: Arc<RwLock<HashMap<String, LicenseData>>>,
    /// Update history
    update_history: Arc<RwLock<Vec<LicenseUpdate>>>,
}

impl LicenseUpdateProcessor {
    /// Create new license update processor
    pub fn new(config: ProcessorConfig) -> Self {
        Self {
            config,
            spdx_client: None,
            osi_client: None,
            stats: Arc::new(RwLock::new(ProcessingStats {
                total_processed: 0,
                successfully_applied: 0,
                validation_errors: 0,
                conflicts_detected: 0,
                impact_assessments: 0,
                average_processing_time_ms: 0.0,
                last_processed: None,
            })),
            license_database: Arc::new(RwLock::new(HashMap::new())),
            update_history: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// Set SPDX client
    pub fn with_spdx_client(mut self, client: Arc<RwLock<SpdxLicenseClient>>) -> Self {
        self.spdx_client = Some(client);
        self
    }

    /// Set OSI client
    pub fn with_osi_client(mut self, client: Arc<RwLock<OsiLicenseClient>>) -> Self {
        self.osi_client = Some(client);
        self
    }

    /// Process license updates from SPDX
    pub async fn process_spdx_updates(&self) -> Result<Vec<LicenseUpdate>> {
        let start_time = Utc::now();

        if let Some(client) = &self.spdx_client {
            let mut client = client.write().await;
            let spdx_licenses = client.fetch_licenses().await?;

            let mut updates = Vec::new();
            for spdx_license in spdx_licenses {
                let update = self.convert_spdx_to_update(&spdx_license).await?;
                updates.push(update);
            }

            // Process updates in batches
            let processed_updates = self.process_updates_batch(updates).await?;

            self.update_processing_stats(start_time, processed_updates.len()).await;

            Ok(processed_updates)
        } else {
            Err(InfinitumError::ConfigurationError {
                message: "SPDX client not configured".to_string(),
            })
        }
    }

    /// Process license updates from OSI
    pub async fn process_osi_updates(&self) -> Result<Vec<LicenseUpdate>> {
        let start_time = Utc::now();

        if let Some(client) = &self.osi_client {
            let mut client = client.write().await;
            let osi_licenses = client.fetch_licenses().await?;

            let mut updates = Vec::new();
            for osi_license in osi_licenses {
                let update = self.convert_osi_to_update(&osi_license).await?;
                updates.push(update);
            }

            // Process updates in batches
            let processed_updates = self.process_updates_batch(updates).await?;

            self.update_processing_stats(start_time, processed_updates.len()).await;

            Ok(processed_updates)
        } else {
            Err(InfinitumError::ConfigurationError {
                message: "OSI client not configured".to_string(),
            })
        }
    }

    /// Process batch of license updates
    pub async fn process_updates_batch(&self, updates: Vec<LicenseUpdate>) -> Result<Vec<LicenseUpdate>> {
        let mut processed_updates = Vec::new();
        let mut batch = Vec::new();

        for update in updates {
            batch.push(update);

            if batch.len() >= self.config.batch_size {
                let processed = self.process_batch_internal(batch).await?;
                processed_updates.extend(processed);
                batch = Vec::new();
            }
        }

        // Process remaining updates
        if !batch.is_empty() {
            let processed = self.process_batch_internal(batch).await?;
            processed_updates.extend(processed);
        }

        Ok(processed_updates)
    }

    /// Process a batch of updates internally
    async fn process_batch_internal(&self, updates: Vec<LicenseUpdate>) -> Result<Vec<LicenseUpdate>> {
        let mut processed = Vec::new();
        let mut database = self.license_database.write().await;

        for update in updates {
            // Validate update
            if self.config.enable_validation {
                if let Err(e) = self.validate_update(&update).await {
                    warn!("Update validation failed for {}: {}", update.license_id, e);
                    let mut stats = self.stats.write().await;
                    stats.validation_errors += 1;
                    continue;
                }
            }

            // Check confidence threshold
            if update.confidence < self.config.min_confidence_threshold {
                warn!(
                    "Update confidence {} below threshold {} for {}",
                    update.confidence, self.config.min_confidence_threshold, update.license_id
                );
                continue;
            }

            // Check for conflicts
            if let Some(conflict) = self.detect_conflict(&update, &database).await {
                warn!("Conflict detected for {}: {}", update.license_id, conflict);
                let mut stats = self.stats.write().await;
                stats.conflicts_detected += 1;
                // Handle conflict based on strategy (for now, skip)
                continue;
            }

            // Apply update
            let old_data = database.insert(update.license_id.clone(), update.new_data.clone());
            let update_with_old = LicenseUpdate {
                old_data,
                ..update
            };

            processed.push(update_with_old.clone());

            // Add to history
            self.update_history.write().await.push(update_with_old);

            let mut stats = self.stats.write().await;
            stats.successfully_applied += 1;
        }

        Ok(processed)
    }

    /// Assess impact of updates on existing scan results
    pub async fn assess_update_impact(
        &self,
        updates: &[LicenseUpdate],
        scan_results: &[ScanResult],
    ) -> Result<Vec<ImpactAssessment>> {
        if !self.config.enable_impact_assessment {
            return Ok(Vec::new());
        }

        let mut assessments = Vec::new();
        let database = self.license_database.read().await;

        for update in updates {
            let assessment = self.assess_single_update_impact(update, scan_results, &database).await?;
            if let Some(assessment) = assessment {
                assessments.push(assessment);
            }
        }

        let mut stats = self.stats.write().await;
        stats.impact_assessments += assessments.len() as u64;

        Ok(assessments)
    }

    /// Assess impact of a single update
    async fn assess_single_update_impact(
        &self,
        update: &LicenseUpdate,
        scan_results: &[ScanResult],
        database: &HashMap<String, LicenseData>,
    ) -> Result<Option<ImpactAssessment>> {
        let mut affected_scans = Vec::new();
        let mut risk_level = RiskLevel::None;
        let mut description = String::new();
        let mut recommended_actions = Vec::new();

        // Check if this license is used in any scan results
        for scan_result in scan_results {
            if self.scan_uses_license(scan_result, &update.license_id) {
                affected_scans.push(scan_result.id.clone());

                // Determine risk level based on update type and license usage
                match update.update_type {
                    UpdateType::Deprecated => {
                        risk_level = RiskLevel::High;
                        description.push_str(&format!(
                            "License {} used in scan {} has been deprecated. ",
                            update.license_id, scan_result.id
                        ));
                        recommended_actions.push(format!(
                            "Review usage of deprecated license {} in scan {}",
                            update.license_id, scan_result.id
                        ));
                    }
                    UpdateType::Removed => {
                        risk_level = RiskLevel::Critical;
                        description.push_str(&format!(
                            "License {} used in scan {} has been removed. ",
                            update.license_id, scan_result.id
                        ));
                        recommended_actions.push(format!(
                            "Immediately replace removed license {} in scan {}",
                            update.license_id, scan_result.id
                        ));
                    }
                    UpdateType::Modified => {
                        risk_level = RiskLevel::Medium;
                        description.push_str(&format!(
                            "License {} used in scan {} has been modified. ",
                            update.license_id, scan_result.id
                        ));
                        recommended_actions.push(format!(
                            "Verify compatibility of modified license {} in scan {}",
                            update.license_id, scan_result.id
                        ));
                    }
                    _ => {}
                }
            }
        }

        if affected_scans.is_empty() {
            return Ok(None);
        }

        Ok(Some(ImpactAssessment {
            update: update.clone(),
            affected_scans,
            risk_level,
            description,
            recommended_actions,
            assessed_at: Utc::now(),
        }))
    }

    /// Check if a scan result uses a specific license
    fn scan_uses_license(&self, scan_result: &ScanResult, license_id: &str) -> bool {
        // This would need to be implemented based on the actual ScanResult structure
        // For now, return false
        false
    }

    /// Validate a license update
    async fn validate_update(&self, update: &LicenseUpdate) -> Result<()> {
        // Run quality checks
        for check in &self.config.quality_checks {
            match check {
                QualityCheck::SyntaxValidation => {
                    self.validate_syntax(update)?;
                }
                QualityCheck::DuplicateDetection => {
                    self.check_duplicates(update).await?;
                }
                QualityCheck::SpdxValidation => {
                    self.validate_spdx_format(update)?;
                }
                QualityCheck::CrossReferenceValidation => {
                    self.validate_cross_references(update).await?;
                }
                QualityCheck::TextConsistency => {
                    self.validate_text_consistency(update)?;
                }
            }
        }

        Ok(())
    }

    /// Convert SPDX license to update
    async fn convert_spdx_to_update(&self, spdx_license: &SpdxLicense) -> Result<LicenseUpdate> {
        let database = self.license_database.read().await;
        let old_data = database.get(&spdx_license.license_id).cloned();

        let new_data = LicenseData {
            id: spdx_license.license_id.clone(),
            name: spdx_license.name.clone(),
            osi_approved: Some(spdx_license.osi_approved),
            spdx_id: Some(spdx_license.license_id.clone()),
            text: spdx_license.license_text.clone(),
            deprecated: spdx_license.is_deprecated,
            last_updated: spdx_license.last_updated,
            source_urls: spdx_license.cross_refs.clone(),
            metadata: HashMap::new(),
        };

        let update_type = if old_data.is_none() {
            UpdateType::Added
        } else if spdx_license.is_deprecated {
            UpdateType::Deprecated
        } else {
            UpdateType::Modified
        };

        Ok(LicenseUpdate {
            license_id: spdx_license.license_id.clone(),
            source: UpdateSource::Spdx,
            update_type,
            old_data,
            new_data,
            timestamp: Utc::now(),
            confidence: 0.95, // High confidence for SPDX
        })
    }

    /// Convert OSI license to update
    async fn convert_osi_to_update(&self, osi_license: &OsiLicense) -> Result<LicenseUpdate> {
        let database = self.license_database.read().await;
        let old_data = database.get(&osi_license.id).cloned();

        let new_data = LicenseData {
            id: osi_license.id.clone(),
            name: osi_license.name.clone(),
            osi_approved: Some(osi_license.approved),
            spdx_id: None, // Would need cross-referencing
            text: None, // OSI doesn't provide text
            deprecated: osi_license.deprecated,
            last_updated: osi_license.last_updated,
            source_urls: vec![osi_license.osi_url.clone()],
            metadata: HashMap::new(),
        };

        let update_type = if old_data.is_none() {
            UpdateType::Added
        } else if osi_license.deprecated {
            UpdateType::Deprecated
        } else {
            UpdateType::Modified
        };

        Ok(LicenseUpdate {
            license_id: osi_license.id.clone(),
            source: UpdateSource::Osi,
            update_type,
            old_data,
            new_data,
            timestamp: Utc::now(),
            confidence: 0.90, // Slightly lower confidence for OSI
        })
    }

    /// Detect conflicts in updates
    async fn detect_conflict(
        &self,
        update: &LicenseUpdate,
        database: &HashMap<String, LicenseData>,
    ) -> Option<String> {
        if let Some(existing) = database.get(&update.license_id) {
            // Check for version conflicts or incompatible changes
            if existing.last_updated > update.new_data.last_updated {
                return Some("Newer data already exists".to_string());
            }
        }
        None
    }

    /// Get processing statistics
    pub async fn get_stats(&self) -> ProcessingStats {
        self.stats.read().await.clone()
    }

    /// Get current license database
    pub async fn get_license_database(&self) -> HashMap<String, LicenseData> {
        self.license_database.read().await.clone()
    }

    /// Get update history
    pub async fn get_update_history(&self, limit: Option<usize>) -> Vec<LicenseUpdate> {
        let history = self.update_history.read().await;
        match limit {
            Some(l) => history.iter().rev().take(l).cloned().collect(),
            None => history.clone(),
        }
    }

    /// Update processing statistics
    async fn update_processing_stats(&self, start_time: DateTime<Utc>, processed_count: usize) {
        let mut stats = self.stats.write().await;
        stats.total_processed += processed_count as u64;
        stats.last_processed = Some(Utc::now());

        let processing_time = Utc::now().signed_duration_since(start_time).num_milliseconds() as f64;
        if stats.total_processed > 0 {
            stats.average_processing_time_ms = (
                stats.average_processing_time_ms * (stats.total_processed - processed_count as u64) as f64 +
                processing_time
            ) / stats.total_processed as f64;
        }
    }

    // Quality check implementations
    fn validate_syntax(&self, _update: &LicenseUpdate) -> Result<()> {
        // Implement syntax validation
        Ok(())
    }

    async fn check_duplicates(&self, _update: &LicenseUpdate) -> Result<()> {
        // Implement duplicate detection
        Ok(())
    }

    fn validate_spdx_format(&self, _update: &LicenseUpdate) -> Result<()> {
        // Implement SPDX format validation
        Ok(())
    }

    async fn validate_cross_references(&self, _update: &LicenseUpdate) -> Result<()> {
        // Implement cross-reference validation
        Ok(())
    }

    fn validate_text_consistency(&self, _update: &LicenseUpdate) -> Result<()> {
        // Implement text consistency validation
        Ok(())
    }
}

impl Default for ProcessorConfig {
    fn default() -> Self {
        Self {
            enable_impact_assessment: true,
            min_confidence_threshold: 0.8,
            enable_validation: true,
            batch_size: 100,
            enable_detailed_logging: true,
            quality_checks: vec![
                QualityCheck::SyntaxValidation,
                QualityCheck::DuplicateDetection,
                QualityCheck::SpdxValidation,
            ],
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_processor_creation() {
        let config = ProcessorConfig::default();
        let processor = LicenseUpdateProcessor::new(config);
        assert_eq!(processor.config.batch_size, 100);
    }

    #[test]
    fn test_default_config() {
        let config = ProcessorConfig::default();
        assert!(config.enable_impact_assessment);
        assert_eq!(config.min_confidence_threshold, 0.8);
        assert!(config.enable_validation);
    }

    #[tokio::test]
    async fn test_empty_batch_processing() {
        let config = ProcessorConfig::default();
        let processor = LicenseUpdateProcessor::new(config);
        let result = processor.process_updates_batch(Vec::new()).await.unwrap();
        assert!(result.is_empty());
    }
}