//! # ML Feature Extractor
//!
//! This module provides comprehensive feature extraction capabilities for license text analysis,
//! including text preprocessing, N-gram features, semantic embeddings, and license-specific features.

use crate::error::{InfinitumError, Result};
use serde::{Deserialize, Serialize};
use std::{
    collections::{HashMap, HashSet},
    sync::Arc,
};
use tokio::sync::RwLock;
use tracing::{debug, info, instrument};
use regex::Regex;

/// Feature extraction configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FeatureExtractorConfig {
    /// Maximum text length for processing
    pub max_text_length: usize,
    /// N-gram range for feature extraction
    pub ngram_range: (usize, usize),
    /// Minimum term frequency for features
    pub min_term_frequency: usize,
    /// Maximum number of features to extract
    pub max_features: usize,
    /// Enable semantic embeddings
    pub enable_embeddings: bool,
    /// Embedding dimension
    pub embedding_dimension: usize,
    /// Enable license-specific features
    pub enable_license_specific: bool,
    /// Custom stop words
    pub custom_stop_words: Vec<String>,
}

/// Extracted features from license text
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ExtractedFeatures {
    /// Bag-of-words features
    pub bow_features: HashMap<String, f64>,
    /// N-gram features
    pub ngram_features: HashMap<String, f64>,
    /// TF-IDF features
    pub tfidf_features: HashMap<String, f64>,
    /// Semantic embeddings
    pub embeddings: Option<Vec<f64>>,
    /// License-specific features
    pub license_specific_features: HashMap<String, f64>,
    /// Text statistics
    pub text_stats: TextStatistics,
    /// Feature metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Text statistics for analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextStatistics {
    /// Total character count
    pub char_count: usize,
    /// Total word count
    pub word_count: usize,
    /// Average word length
    pub avg_word_length: f64,
    /// Unique word count
    pub unique_words: usize,
    /// Sentence count
    pub sentence_count: usize,
    /// License keyword density
    pub license_keyword_density: f64,
    /// Readability score
    pub readability_score: f64,
}

/// License-specific feature patterns
#[derive(Debug, Clone)]
struct LicensePatterns {
    permissive_keywords: HashSet<String>,
    copyleft_keywords: HashSet<String>,
    proprietary_keywords: HashSet<String>,
    obligation_keywords: HashSet<String>,
    permission_keywords: HashSet<String>,
    restriction_keywords: HashSet<String>,
}

/// ML Feature Extractor
pub struct MLFeatureExtractor {
    config: FeatureExtractorConfig,
    license_patterns: Arc<LicensePatterns>,
    stop_words: Arc<HashSet<String>>,
    term_frequencies: Arc<RwLock<HashMap<String, usize>>>,
    document_frequencies: Arc<RwLock<HashMap<String, usize>>>,
    total_documents: Arc<RwLock<usize>>,
}

impl MLFeatureExtractor {
    /// Create new ML Feature Extractor
    pub fn new(config: FeatureExtractorConfig) -> Self {
        let license_patterns = Arc::new(Self::initialize_license_patterns());
        let stop_words = Arc::new(Self::initialize_stop_words(&config.custom_stop_words));

        Self {
            config,
            license_patterns,
            stop_words,
            term_frequencies: Arc::new(RwLock::new(HashMap::new())),
            document_frequencies: Arc::new(RwLock::new(HashMap::new())),
            total_documents: Arc::new(RwLock::new(0)),
        }
    }

    /// Extract features from license text
    #[instrument(skip(self, text), fields(text_len = text.len()))]
    pub async fn extract_features(&self, text: &str, license_type: Option<&str>) -> Result<ExtractedFeatures> {
        debug!("Extracting features from license text");

        // Preprocess text
        let processed_text = self.preprocess_text(text)?;

        // Extract various feature types
        let bow_features = self.extract_bow_features(&processed_text).await;
        let ngram_features = self.extract_ngram_features(&processed_text).await;
        let tfidf_features = self.extract_tfidf_features(&processed_text).await;
        let embeddings = if self.config.enable_embeddings {
            Some(self.extract_embeddings(&processed_text).await?)
        } else {
            None
        };
        let license_specific_features = if self.config.enable_license_specific {
            self.extract_license_specific_features(&processed_text, license_type)
        } else {
            HashMap::new()
        };
        let text_stats = self.compute_text_statistics(text, &processed_text);

        // Update document frequencies
        self.update_document_frequencies(&processed_text).await;

        let features = ExtractedFeatures {
            bow_features,
            ngram_features,
            tfidf_features,
            embeddings,
            license_specific_features,
            text_stats,
            metadata: HashMap::from([
                ("original_text_length".to_string(), serde_json::json!(text.len())),
                ("processed_text_length".to_string(), serde_json::json!(processed_text.len())),
                ("extraction_timestamp".to_string(), serde_json::json!(chrono::Utc::now())),
            ]),
        };

        debug!("Feature extraction completed: {} features extracted", features.total_feature_count());
        Ok(features)
    }

    /// Preprocess license text
    fn preprocess_text(&self, text: &str) -> Result<String> {
        let mut processed = text.to_lowercase();

        // Remove extra whitespace
        processed = Regex::new(r"\s+")?.replace_all(&processed, " ").to_string();

        // Remove punctuation (keep some license-specific punctuation)
        processed = Regex::new(r"[^\w\s\-/]")?.replace_all(&processed, " ").to_string();

        // Normalize whitespace
        processed = processed.trim().to_string();

        // Limit text length
        if processed.len() > self.config.max_text_length {
            processed = processed[..self.config.max_text_length].to_string();
        }

        Ok(processed)
    }

    /// Extract bag-of-words features
    async fn extract_bow_features(&self, text: &str) -> HashMap<String, f64> {
        let words = self.tokenize(text);
        let mut features = HashMap::new();

        for word in words {
            if !self.stop_words.contains(&word) && word.len() > 2 {
                let count = features.entry(word).or_insert(0.0);
                *count += 1.0;
            }
        }

        // Limit number of features
        if features.len() > self.config.max_features {
            let mut sorted_features: Vec<_> = features.into_iter().collect();
            sorted_features.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());
            sorted_features.truncate(self.config.max_features);
            features = sorted_features.into_iter().collect();
        }

        features
    }

    /// Extract N-gram features
    async fn extract_ngram_features(&self, text: &str) -> HashMap<String, f64> {
        let words = self.tokenize(text);
        let mut ngram_features = HashMap::new();

        for n in self.config.ngram_range.0..=self.config.ngram_range.1 {
            if n > words.len() {
                break;
            }

            for window in words.windows(n) {
                let ngram = window.join(" ");
                let count = ngram_features.entry(ngram).or_insert(0.0);
                *count += 1.0;
            }
        }

        // Limit features
        if ngram_features.len() > self.config.max_features {
            let mut sorted_features: Vec<_> = ngram_features.into_iter().collect();
            sorted_features.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());
            sorted_features.truncate(self.config.max_features);
            ngram_features = sorted_features.into_iter().collect();
        }

        ngram_features
    }

    /// Extract TF-IDF features
    async fn extract_tfidf_features(&self, text: &str) -> HashMap<String, f64> {
        let bow_features = self.extract_bow_features(text).await;
        let mut tfidf_features = HashMap::new();

        let total_docs = *self.total_documents.read().await;
        if total_docs == 0 {
            return bow_features; // Return raw term frequencies if no documents processed yet
        }

        for (term, tf) in bow_features {
            let df = self.document_frequencies.read().await.get(&term).unwrap_or(&1);
            let idf = (total_docs as f64 / *df as f64).ln();
            let tfidf = tf * idf;
            tfidf_features.insert(term, tfidf);
        }

        tfidf_features
    }

    /// Extract semantic embeddings (placeholder for actual embedding model)
    async fn extract_embeddings(&self, text: &str) -> Result<Vec<f64>> {
        // TODO: Implement actual embedding extraction using rust-bert or similar
        // For now, return a simple hash-based embedding
        let mut embedding = Vec::with_capacity(self.config.embedding_dimension);

        for i in 0..self.config.embedding_dimension {
            let hash = self.simple_hash(text, i);
            embedding.push((hash % 1000) as f64 / 500.0 - 1.0); // Normalize to [-1, 1]
        }

        Ok(embedding)
    }

    /// Extract license-specific features
    fn extract_license_specific_features(&self, text: &str, license_type: Option<&str>) -> HashMap<String, f64> {
        let mut features = HashMap::new();
        let words: HashSet<String> = self.tokenize(text).into_iter().collect();

        // Count keyword occurrences
        let permissive_count = words.intersection(&self.license_patterns.permissive_keywords).count();
        let copyleft_count = words.intersection(&self.license_patterns.copyleft_keywords).count();
        let proprietary_count = words.intersection(&self.license_patterns.proprietary_keywords).count();
        let obligation_count = words.intersection(&self.license_patterns.obligation_keywords).count();
        let permission_count = words.intersection(&self.license_patterns.permission_keywords).count();
        let restriction_count = words.intersection(&self.license_patterns.restriction_keywords).count();

        features.insert("permissive_keywords".to_string(), permissive_count as f64);
        features.insert("copyleft_keywords".to_string(), copyleft_count as f64);
        features.insert("proprietary_keywords".to_string(), proprietary_count as f64);
        features.insert("obligation_keywords".to_string(), obligation_count as f64);
        features.insert("permission_keywords".to_string(), permission_count as f64);
        features.insert("restriction_keywords".to_string(), restriction_count as f64);

        // License type specific features
        if let Some(license_type) = license_type {
            features.insert(format!("is_{}", license_type.to_lowercase()), 1.0);
        }

        // Keyword density ratios
        let total_keywords = permissive_count + copyleft_count + proprietary_count +
                           obligation_count + permission_count + restriction_count;
        if total_keywords > 0 {
            features.insert("permissive_ratio".to_string(), permissive_count as f64 / total_keywords as f64);
            features.insert("copyleft_ratio".to_string(), copyleft_count as f64 / total_keywords as f64);
            features.insert("obligation_ratio".to_string(), obligation_count as f64 / total_keywords as f64);
        }

        features
    }

    /// Compute text statistics
    fn compute_text_statistics(&self, original_text: &str, processed_text: &str) -> TextStatistics {
        let words: Vec<&str> = processed_text.split_whitespace().collect();
        let unique_words: HashSet<&str> = words.iter().cloned().collect();

        let avg_word_length = if !words.is_empty() {
            words.iter().map(|w| w.len()).sum::<usize>() as f64 / words.len() as f64
        } else {
            0.0
        };

        let sentence_count = original_text.split('.').count() +
                           original_text.split('!').count() +
                           original_text.split('?').count();

        let license_keywords = self.license_patterns.permissive_keywords.len() +
                             self.license_patterns.copyleft_keywords.len() +
                             self.license_patterns.proprietary_keywords.len();

        let license_keyword_density = if !words.is_empty() {
            license_keywords as f64 / words.len() as f64
        } else {
            0.0
        };

        // Simple readability score (Flesch Reading Ease approximation)
        let readability_score = if !words.is_empty() {
            206.835 - 1.015 * (words.len() as f64 / sentence_count as f64) -
            84.6 * (original_text.chars().filter(|c| "aeiou".contains(*c)).count() as f64 / words.len() as f64)
        } else {
            0.0
        };

        TextStatistics {
            char_count: original_text.len(),
            word_count: words.len(),
            avg_word_length,
            unique_words: unique_words.len(),
            sentence_count,
            license_keyword_density,
            readability_score,
        }
    }

    /// Tokenize text into words
    fn tokenize(&self, text: &str) -> Vec<String> {
        text.split_whitespace()
            .map(|word| word.trim_matches(|c: char| !c.is_alphanumeric()))
            .filter(|word| !word.is_empty())
            .map(|word| word.to_lowercase())
            .collect()
    }

    /// Update document frequencies for TF-IDF calculation
    async fn update_document_frequencies(&self, text: &str) {
        let words = self.tokenize(text);
        let unique_words: HashSet<String> = words.into_iter().collect();

        let mut doc_freqs = self.document_frequencies.write().await;
        for word in unique_words {
            let count = doc_freqs.entry(word).or_insert(0);
            *count += 1;
        }

        let mut total_docs = self.total_documents.write().await;
        *total_docs += 1;
    }

    /// Simple hash function for embedding generation
    fn simple_hash(&self, text: &str, seed: usize) -> u64 {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        text.hash(&mut hasher);
        seed.hash(&mut hasher);
        hasher.finish()
    }

    /// Initialize license-specific patterns
    fn initialize_license_patterns() -> LicensePatterns {
        LicensePatterns {
            permissive_keywords: [
                "mit", "bsd", "apache", "isc", "boost", "zlib", "postgresql",
                "permission", "free", "redistribute", "modify", "sublicense"
            ].iter().map(|s| s.to_string()).collect(),

            copyleft_keywords: [
                "gpl", "gnu", "general public license", "copyleft", "viral",
                "same license", "compatible license", "lgpl", "agpl", "mpl"
            ].iter().map(|s| s.to_string()).collect(),

            proprietary_keywords: [
                "proprietary", "commercial", "confidential", "internal use only",
                "all rights reserved", "copyright", "trademark"
            ].iter().map(|s| s.to_string()).collect(),

            obligation_keywords: [
                "must", "shall", "required", "obliged", "disclose", "attribution",
                "notice", "include", "retain", "preserve"
            ].iter().map(|s| s.to_string()).collect(),

            permission_keywords: [
                "may", "can", "permitted", "allowed", "authorize", "grant",
                "license", "right", "permission"
            ].iter().map(|s| s.to_string()).collect(),

            restriction_keywords: [
                "cannot", "may not", "prohibited", "forbidden", "restrict",
                "limit", "exclude", "without permission"
            ].iter().map(|s| s.to_string()).collect(),
        }
    }

    /// Initialize stop words
    fn initialize_stop_words(custom_stop_words: &[String]) -> HashSet<String> {
        let default_stop_words = [
            "the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for",
            "of", "with", "by", "is", "are", "was", "were", "be", "been", "being",
            "have", "has", "had", "do", "does", "did", "will", "would", "could",
            "should", "may", "might", "must", "can", "shall", "this", "that",
            "these", "those", "i", "you", "he", "she", "it", "we", "they"
        ];

        let mut stop_words: HashSet<String> = default_stop_words.iter()
            .map(|s| s.to_string())
            .collect();

        // Add custom stop words
        for word in custom_stop_words {
            stop_words.insert(word.to_lowercase());
        }

        stop_words
    }
}

impl ExtractedFeatures {
    /// Get total number of features
    pub fn total_feature_count(&self) -> usize {
        self.bow_features.len() +
        self.ngram_features.len() +
        self.tfidf_features.len() +
        self.license_specific_features.len() +
        self.embeddings.as_ref().map(|e| e.len()).unwrap_or(0)
    }

    /// Get feature vector for ML model input
    pub fn to_feature_vector(&self) -> Vec<f64> {
        let mut vector = Vec::new();

        // Add BOW features
        let mut bow_vec: Vec<_> = self.bow_features.values().cloned().collect();
        bow_vec.sort_by(|a, b| a.partial_cmp(b).unwrap());
        vector.extend(bow_vec);

        // Add N-gram features
        let mut ngram_vec: Vec<_> = self.ngram_features.values().cloned().collect();
        ngram_vec.sort_by(|a, b| a.partial_cmp(b).unwrap());
        vector.extend(ngram_vec);

        // Add TF-IDF features
        let mut tfidf_vec: Vec<_> = self.tfidf_features.values().cloned().collect();
        tfidf_vec.sort_by(|a, b| a.partial_cmp(b).unwrap());
        vector.extend(tfidf_vec);

        // Add license-specific features
        let mut license_vec: Vec<_> = self.license_specific_features.values().cloned().collect();
        license_vec.sort_by(|a, b| a.partial_cmp(b).unwrap());
        vector.extend(license_vec);

        // Add embeddings
        if let Some(embeddings) = &self.embeddings {
            vector.extend(embeddings.clone());
        }

        // Add text statistics
        vector.push(self.text_stats.char_count as f64);
        vector.push(self.text_stats.word_count as f64);
        vector.push(self.text_stats.avg_word_length);
        vector.push(self.text_stats.unique_words as f64);
        vector.push(self.text_stats.sentence_count as f64);
        vector.push(self.text_stats.license_keyword_density);
        vector.push(self.text_stats.readability_score);

        vector
    }
}

impl Default for FeatureExtractorConfig {
    fn default() -> Self {
        Self {
            max_text_length: 10000,
            ngram_range: (1, 3),
            min_term_frequency: 2,
            max_features: 1000,
            enable_embeddings: true,
            embedding_dimension: 128,
            enable_license_specific: true,
            custom_stop_words: Vec::new(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_feature_extraction() {
        let config = FeatureExtractorConfig::default();
        let extractor = MLFeatureExtractor::new(config);

        let license_text = "MIT License\n\nCopyright (c) 2023\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software";

        let features = extractor.extract_features(license_text, Some("MIT")).await.unwrap();

        assert!(!features.bow_features.is_empty());
        assert!(!features.license_specific_features.is_empty());
        assert!(features.text_stats.word_count > 0);
        assert!(features.embeddings.is_some());
    }

    #[test]
    fn test_text_preprocessing() {
        let config = FeatureExtractorConfig::default();
        let extractor = MLFeatureExtractor::new(config);

        let text = "Hello, World! This is a TEST.";
        let processed = extractor.preprocess_text(text).unwrap();

        assert_eq!(processed, "hello world this is a test");
    }

    #[test]
    fn test_tokenization() {
        let config = FeatureExtractorConfig::default();
        let extractor = MLFeatureExtractor::new(config);

        let text = "hello world test";
        let tokens = extractor.tokenize(text);

        assert_eq!(tokens, vec!["hello", "world", "test"]);
    }
}