//! # ML Integration Orchestrator
//!
//! This module coordinates ML components with existing license detection systems,
//! providing hybrid detection capabilities, confidence score fusion, fallback mechanisms,
//! and performance optimization for license compliance.

use crate::{
    compliance::{
        ml_model_manager::{MLModelManager, ModelType},
        ml_feature_extractor::{MLFeatureExtractor, FeatureExtractorConfig},
        license_pattern_classifier::{LicensePatternClassifier, ClassifierConfig, ClassificationResult},
        adaptive_pattern_learner::{AdaptivePatternLearner, LearningConfig},
        pattern_recognition_engine::{PatternRecognitionEngine, PatternRecognitionConfig},
    },
    scanners::license_detector::{MultiLayeredLicenseDetector, MultiLayerConfig, AggregatedDetectionResult},
    error::{InfinitumError, Result},
    observability::ObservabilityManager,
};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    sync::Arc,
};
use tokio::sync::RwLock;
use tracing::{debug, info, instrument, warn};
use chrono::{DateTime, Utc};

/// Integration configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct MLIntegrationConfig {
    /// Enable ML integration
    pub enable_ml_integration: bool,
    /// ML confidence weight in hybrid scoring
    pub ml_confidence_weight: f64,
    /// Rule-based confidence weight in hybrid scoring
    pub rule_confidence_weight: f64,
    /// Minimum ML confidence for ML-only results
    pub min_ml_confidence: f64,
    /// Enable fallback to rule-based detection
    pub enable_fallback: bool,
    /// Performance monitoring enabled
    pub enable_performance_monitoring: bool,
    /// Learning trigger threshold
    pub learning_trigger_threshold: usize,
    /// Batch processing size
    pub batch_size: usize,
}

/// Hybrid detection result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HybridDetectionResult {
    /// Final license prediction
    pub final_license: String,
    /// Overall confidence score
    pub overall_confidence: f64,
    /// ML-based result
    pub ml_result: Option<ClassificationResult>,
    /// Rule-based result
    pub rule_result: Option<AggregatedDetectionResult>,
    /// Detection method used
    pub detection_method: DetectionMethod,
    /// Confidence breakdown
    pub confidence_breakdown: ConfidenceBreakdown,
    /// Processing time
    pub processing_time_ms: f64,
    /// Evidence supporting the result
    pub evidence: Vec<String>,
    /// Metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Detection method used
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum DetectionMethod {
    /// ML-based detection
    MLBased,
    /// Rule-based detection
    RuleBased,
    /// Hybrid detection (ML + rules)
    Hybrid,
    /// Fallback detection
    Fallback,
}

/// Confidence breakdown
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfidenceBreakdown {
    /// ML confidence score
    pub ml_confidence: f64,
    /// Rule-based confidence score
    pub rule_confidence: f64,
    /// Fused confidence score
    pub fused_confidence: f64,
    /// Confidence weights used
    pub weights: HashMap<String, f64>,
}

/// Performance metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    /// Total detections processed
    pub total_detections: u64,
    /// ML-based detections
    pub ml_detections: u64,
    /// Rule-based detections
    pub rule_detections: u64,
    /// Hybrid detections
    pub hybrid_detections: u64,
    /// Fallback detections
    pub fallback_detections: u64,
    /// Average processing time
    pub avg_processing_time_ms: f64,
    /// Average confidence score
    pub avg_confidence: f64,
    /// Accuracy rate (if ground truth available)
    pub accuracy_rate: Option<f64>,
    /// False positive rate
    pub false_positive_rate: Option<f64>,
    /// Last updated timestamp
    pub last_updated: DateTime<Utc>,
}

/// ML Integration Orchestrator
pub struct MLIntegrationOrchestrator {
    config: MLIntegrationConfig,
    model_manager: Arc<MLModelManager>,
    feature_extractor: Arc<MLFeatureExtractor>,
    classifier: Arc<LicensePatternClassifier>,
    adaptive_learner: Arc<AdaptivePatternLearner>,
    pattern_engine: Arc<PatternRecognitionEngine>,
    rule_detector: Arc<MultiLayeredLicenseDetector>,
    performance_metrics: Arc<RwLock<PerformanceMetrics>>,
    observability: Option<Arc<ObservabilityManager>>,
}

impl MLIntegrationOrchestrator {
    /// Create new ML Integration Orchestrator
    pub fn new(
        config: MLIntegrationConfig,
        model_manager: Arc<MLModelManager>,
        feature_extractor: Arc<MLFeatureExtractor>,
        classifier: Arc<LicensePatternClassifier>,
        adaptive_learner: Arc<AdaptivePatternLearner>,
        pattern_engine: Arc<PatternRecognitionEngine>,
        rule_detector: Arc<MultiLayeredLicenseDetector>,
    ) -> Self {
        Self {
            config,
            model_manager,
            feature_extractor,
            classifier,
            adaptive_learner,
            pattern_engine,
            rule_detector,
            performance_metrics: Arc::new(RwLock::new(PerformanceMetrics::default())),
            observability: None,
        }
    }

    /// Set observability manager
    pub fn set_observability(&mut self, observability: Arc<ObservabilityManager>) {
        self.observability = Some(observability);
    }

    /// Perform hybrid license detection
    #[instrument(skip(self, text), fields(text_len = text.len()))]
    pub async fn detect_license_hybrid(&self, text: &str) -> Result<HybridDetectionResult> {
        let start_time = std::time::Instant::now();

        debug!("Starting hybrid license detection");

        let mut ml_result = None;
        let mut rule_result = None;
        let mut detection_method = DetectionMethod::Fallback;

        // Try ML-based detection first
        if self.config.enable_ml_integration {
            match self.classifier.classify_license(text).await {
                Ok(result) if result.confidence >= self.config.min_ml_confidence => {
                    ml_result = Some(result);
                    detection_method = DetectionMethod::MLBased;
                }
                Ok(result) => {
                    ml_result = Some(result);
                    // Continue to try rule-based if ML confidence is low
                }
                Err(e) => {
                    warn!("ML classification failed: {}", e);
                }
            }
        }

        // Try rule-based detection
        match self.rule_detector.detect_file(std::path::Path::new("")).await {
            Ok(result) => {
                rule_result = Some(result);
                if detection_method == DetectionMethod::Fallback {
                    detection_method = DetectionMethod::RuleBased;
                }
            }
            Err(e) => {
                warn!("Rule-based detection failed: {}", e);
            }
        }

        // Determine final result based on available results
        let final_result = match (&ml_result, &rule_result) {
            (Some(ml), Some(rule)) => {
                // Both available - use hybrid approach
                detection_method = DetectionMethod::Hybrid;
                self.fuse_results(ml, rule).await
            }
            (Some(ml), None) => {
                // Only ML available
                self.ml_only_result(ml)
            }
            (None, Some(rule)) => {
                // Only rule-based available
                self.rule_only_result(rule)
            }
            (None, None) => {
                // No results available - use fallback
                detection_method = DetectionMethod::Fallback;
                self.fallback_result()
            }
        };

        let processing_time = start_time.elapsed().as_millis() as f64;

        let result = HybridDetectionResult {
            final_license: final_result.final_license,
            overall_confidence: final_result.overall_confidence,
            ml_result,
            rule_result,
            detection_method,
            confidence_breakdown: final_result.confidence_breakdown,
            processing_time_ms: processing_time,
            evidence: final_result.evidence,
            metadata: HashMap::from([
                ("processing_time_ms".to_string(), serde_json::json!(processing_time)),
                ("detection_method".to_string(), serde_json::json!(detection_method)),
                ("timestamp".to_string(), serde_json::json!(Utc::now())),
            ]),
        };

        // Update performance metrics
        self.update_performance_metrics(&result).await;

        // Record observability metrics
        if let Some(observability) = &self.observability {
            // Record performance metrics
            observability.record_performance(
                "license_detection_hybrid",
                result.processing_time_ms,
                0, // queue depth not tracked here
            ).await;

            // Record system health (simplified)
            observability.record_system_health(
                "ml_integration_orchestrator",
                0.95, // Assume good health
                0.01, // Low error rate
            ).await;
        }

        // Add to learning buffer for continuous improvement
        if let Some(ml_result) = &result.ml_result {
            self.adaptive_learner.process_detection_result(text, ml_result).await?;
        }

        info!(
            "Hybrid detection completed: {} (confidence: {:.3}, method: {:?}, time: {:.2}ms)",
            result.final_license, result.overall_confidence, result.detection_method, result.processing_time_ms
        );

        Ok(result)
    }

    /// Batch process multiple license texts
    #[instrument(skip(self, texts), fields(batch_size = texts.len()))]
    pub async fn batch_detect_licenses(&self, texts: Vec<String>) -> Result<Vec<HybridDetectionResult>> {
        info!("Starting batch license detection: {} texts", texts.len());

        let mut results = Vec::new();

        // Process in batches to avoid overwhelming the system
        for chunk in texts.chunks(self.config.batch_size) {
            let mut chunk_results = Vec::new();

            for text in chunk {
                let result = self.detect_license_hybrid(text).await?;
                chunk_results.push(result);
            }

            results.extend(chunk_results);

            // Small delay between batches to prevent resource exhaustion
            tokio::time::sleep(std::time::Duration::from_millis(10)).await;
        }

        info!("Batch detection completed: {} results", results.len());
        Ok(results)
    }

    /// Fuse ML and rule-based results
    async fn fuse_results(&self, ml_result: &ClassificationResult, rule_result: &AggregatedDetectionResult) -> FusedResult {
        // Extract license from rule-based result
        let rule_license = rule_result.final_licenses.first()
            .map(|l| l.spdx_id.clone())
            .unwrap_or_else(|| "Unknown".to_string());

        let rule_confidence = rule_result.overall_confidence;

        // Check if results agree
        let agreement = ml_result.predicted_license == rule_license;

        let (final_license, fused_confidence) = if agreement {
            // Results agree - boost confidence
            let fused = (ml_result.confidence * self.config.ml_confidence_weight +
                        rule_confidence * self.config.rule_confidence_weight) /
                       (self.config.ml_confidence_weight + self.config.rule_confidence_weight);
            (ml_result.predicted_license.clone(), fused.max(ml_result.confidence).max(rule_confidence))
        } else {
            // Results disagree - use higher confidence result
            if ml_result.confidence > rule_confidence {
                (ml_result.predicted_license.clone(), ml_result.confidence * 0.8) // Penalty for disagreement
            } else {
                (rule_license, rule_confidence * 0.8)
            }
        };

        let confidence_breakdown = ConfidenceBreakdown {
            ml_confidence: ml_result.confidence,
            rule_confidence,
            fused_confidence,
            weights: HashMap::from([
                ("ml_weight".to_string(), self.config.ml_confidence_weight),
                ("rule_weight".to_string(), self.config.rule_confidence_weight),
            ]),
        };

        let evidence = vec![
            format!("ML prediction: {} (confidence: {:.3})", ml_result.predicted_license, ml_result.confidence),
            format!("Rule-based prediction: {} (confidence: {:.3})", rule_license, rule_confidence),
            format!("Results agreement: {}", agreement),
        ];

        FusedResult {
            final_license,
            overall_confidence: fused_confidence,
            confidence_breakdown,
            evidence,
        }
    }

    /// Handle ML-only result
    fn ml_only_result(&self, ml_result: &ClassificationResult) -> FusedResult {
        FusedResult {
            final_license: ml_result.predicted_license.clone(),
            overall_confidence: ml_result.confidence,
            confidence_breakdown: ConfidenceBreakdown {
                ml_confidence: ml_result.confidence,
                rule_confidence: 0.0,
                fused_confidence: ml_result.confidence,
                weights: HashMap::from([
                    ("ml_weight".to_string(), 1.0),
                    ("rule_weight".to_string(), 0.0),
                ]),
            },
            evidence: vec![
                format!("ML-only prediction: {} (confidence: {:.3})", ml_result.predicted_license, ml_result.confidence),
            ],
        }
    }

    /// Handle rule-based only result
    fn rule_only_result(&self, rule_result: &AggregatedDetectionResult) -> FusedResult {
        let rule_license = rule_result.final_licenses.first()
            .map(|l| l.spdx_id.clone())
            .unwrap_or_else(|| "Unknown".to_string());

        FusedResult {
            final_license: rule_license.clone(),
            overall_confidence: rule_result.overall_confidence,
            confidence_breakdown: ConfidenceBreakdown {
                ml_confidence: 0.0,
                rule_confidence: rule_result.overall_confidence,
                fused_confidence: rule_result.overall_confidence,
                weights: HashMap::from([
                    ("ml_weight".to_string(), 0.0),
                    ("rule_weight".to_string(), 1.0),
                ]),
            },
            evidence: vec![
                format!("Rule-based only prediction: {} (confidence: {:.3})", rule_license, rule_result.overall_confidence),
            ],
        }
    }

    /// Handle fallback result
    fn fallback_result(&self) -> FusedResult {
        FusedResult {
            final_license: "Unknown".to_string(),
            overall_confidence: 0.0,
            confidence_breakdown: ConfidenceBreakdown {
                ml_confidence: 0.0,
                rule_confidence: 0.0,
                fused_confidence: 0.0,
                weights: HashMap::new(),
            },
            evidence: vec![
                "Fallback: No reliable detection method available".to_string(),
            ],
        }
    }

    /// Update performance metrics
    async fn update_performance_metrics(&self, result: &HybridDetectionResult) {
        let mut metrics = self.performance_metrics.write().await;

        metrics.total_detections += 1;
        metrics.avg_confidence = (metrics.avg_confidence * (metrics.total_detections - 1) as f64 +
                                 result.overall_confidence) / metrics.total_detections as f64;
        metrics.avg_processing_time_ms = (metrics.avg_processing_time_ms * (metrics.total_detections - 1) as f64 +
                                         result.processing_time_ms) / metrics.total_detections as f64;

        match result.detection_method {
            DetectionMethod::MLBased => metrics.ml_detections += 1,
            DetectionMethod::RuleBased => metrics.rule_detections += 1,
            DetectionMethod::Hybrid => metrics.hybrid_detections += 1,
            DetectionMethod::Fallback => metrics.fallback_detections += 1,
        }

        metrics.last_updated = Utc::now();
    }

    /// Get current performance metrics
    pub async fn get_performance_metrics(&self) -> PerformanceMetrics {
        self.performance_metrics.read().await.clone()
    }

    /// Optimize ML model performance
    #[instrument(skip(self))]
    pub async fn optimize_performance(&self) -> Result<()> {
        info!("Starting ML performance optimization");

        // Check if learning should be triggered
        let metrics = self.get_performance_metrics().await;

        if metrics.total_detections >= self.config.learning_trigger_threshold as u64 {
            // Trigger learning to improve performance
            self.adaptive_learner.trigger_learning().await?;
            info!("Performance optimization: triggered learning");
        }

        // Check for anomalies in detection patterns
        let anomalies = self.pattern_engine.detect_anomalies().await?;
        if !anomalies.is_empty() {
            warn!("Detected {} anomalous patterns during optimization", anomalies.len());
            // Could trigger model retraining or pattern investigation
        }

        // Perform clustering for pattern categorization
        let clusters = self.pattern_engine.perform_clustering().await?;
        if !clusters.is_empty() {
            info!("Pattern clustering completed: {} clusters formed", clusters.len());
        }

        Ok(())
    }

    /// Get system health status
    pub async fn get_health_status(&self) -> HashMap<String, serde_json::Value> {
        let mut status = HashMap::new();

        // Check ML model status
        let active_model = self.model_manager.get_active_model(&ModelType::TextClassifier).await;
        status.insert("ml_model_active".to_string(), serde_json::json!(active_model.is_some()));

        // Check feature extractor status
        status.insert("feature_extractor_ready".to_string(), serde_json::json!(true));

        // Check pattern engine status
        let pattern_stats = self.pattern_engine.get_pattern_stats().await;
        status.insert("pattern_database_size".to_string(), pattern_stats.get("total_patterns").cloned().unwrap_or(serde_json::json!(0)));

        // Check performance metrics
        let metrics = self.get_performance_metrics().await;
        status.insert("total_detections".to_string(), serde_json::json!(metrics.total_detections));
        status.insert("avg_confidence".to_string(), serde_json::json!(metrics.avg_confidence));

        // Check learning status
        let learning_stats = self.adaptive_learner.get_learning_stats().await;
        status.insert("learning_samples".to_string(), serde_json::json!(learning_stats.total_samples));

        status.insert("last_updated".to_string(), serde_json::json!(Utc::now()));

        status
    }

    /// Export system configuration and status
    pub async fn export_system_status(&self) -> Result<serde_json::Value> {
        let health_status = self.get_health_status().await;
        let performance_metrics = self.get_performance_metrics().await;
        let learning_stats = self.adaptive_learner.get_learning_stats().await;
        let discovered_patterns = self.adaptive_learner.get_discovered_patterns().await;

        let export_data = serde_json::json!({
            "health_status": health_status,
            "performance_metrics": performance_metrics,
            "learning_stats": learning_stats,
            "discovered_patterns": discovered_patterns.len(),
            "configuration": self.config,
            "export_timestamp": Utc::now(),
        });

        Ok(export_data)
    }

    /// Reset system state (for testing or maintenance)
    #[instrument(skip(self))]
    pub async fn reset_system(&self) -> Result<()> {
        info!("Resetting ML integration system");

        // Clear performance metrics
        let mut metrics = self.performance_metrics.write().await;
        *metrics = PerformanceMetrics::default();

        // Clear learning buffer
        self.adaptive_learner.clear_learning_buffer().await?;

        // Clear pattern database
        // Note: This would need to be implemented in PatternRecognitionEngine

        info!("System reset completed");
        Ok(())
    }
}

/// Internal struct for fused results
struct FusedResult {
    final_license: String,
    overall_confidence: f64,
    confidence_breakdown: ConfidenceBreakdown,
    evidence: Vec<String>,
}

impl Default for MLIntegrationConfig {
    fn default() -> Self {
        Self {
            enable_ml_integration: true,
            ml_confidence_weight: 0.7,
            rule_confidence_weight: 0.3,
            min_ml_confidence: 0.6,
            enable_fallback: true,
            enable_performance_monitoring: true,
            learning_trigger_threshold: 100,
            batch_size: 10,
        }
    }
}

impl Default for PerformanceMetrics {
    fn default() -> Self {
        Self {
            total_detections: 0,
            ml_detections: 0,
            rule_detections: 0,
            hybrid_detections: 0,
            fallback_detections: 0,
            avg_processing_time_ms: 0.0,
            avg_confidence: 0.0,
            accuracy_rate: None,
            false_positive_rate: None,
            last_updated: Utc::now(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Arc;

    #[tokio::test]
    async fn test_orchestrator_creation() {
        let model_config = crate::compliance::ml_model_manager::MLModelConfig::default();
        let model_manager = Arc::new(MLModelManager::new(model_config));

        let feature_config = FeatureExtractorConfig::default();
        let feature_extractor = Arc::new(MLFeatureExtractor::new(feature_config));

        let classifier_config = ClassifierConfig::default();
        let classifier = Arc::new(LicensePatternClassifier::new(
            classifier_config,
            Arc::clone(&feature_extractor),
            Arc::clone(&model_manager),
        ));

        let learning_config = LearningConfig::default();
        let adaptive_learner = Arc::new(AdaptivePatternLearner::new(
            learning_config,
            Arc::clone(&feature_extractor),
            Arc::clone(&model_manager),
            Arc::clone(&classifier),
        ));

        let pattern_config = PatternRecognitionConfig::default();
        let pattern_engine = Arc::new(PatternRecognitionEngine::new(
            pattern_config,
            Arc::clone(&feature_extractor),
        ));

        let rule_config = MultiLayerConfig::default();
        let scanning_config = crate::config::ScanningConfig::default();
        let rule_detector = Arc::new(MultiLayeredLicenseDetector::new(&scanning_config, rule_config));

        let integration_config = MLIntegrationConfig::default();
        let orchestrator = MLIntegrationOrchestrator::new(
            integration_config,
            model_manager,
            feature_extractor,
            classifier,
            adaptive_learner,
            pattern_engine,
            rule_detector,
        );

        let health = orchestrator.get_health_status().await;
        assert!(health.contains_key("ml_model_active"));
    }

    #[tokio::test]
    async fn test_hybrid_detection() {
        let model_config = crate::compliance::ml_model_manager::MLModelConfig::default();
        let model_manager = Arc::new(MLModelManager::new(model_config));

        let feature_config = FeatureExtractorConfig::default();
        let feature_extractor = Arc::new(MLFeatureExtractor::new(feature_config));

        let classifier_config = ClassifierConfig::default();
        let classifier = Arc::new(LicensePatternClassifier::new(
            classifier_config,
            Arc::clone(&feature_extractor),
            Arc::clone(&model_manager),
        ));

        let learning_config = LearningConfig::default();
        let adaptive_learner = Arc::new(AdaptivePatternLearner::new(
            learning_config,
            Arc::clone(&feature_extractor),
            Arc::clone(&model_manager),
            Arc::clone(&classifier),
        ));

        let pattern_config = PatternRecognitionConfig::default();
        let pattern_engine = Arc::new(PatternRecognitionEngine::new(
            pattern_config,
            Arc::clone(&feature_extractor),
        ));

        let rule_config = MultiLayerConfig::default();
        let scanning_config = crate::config::ScanningConfig::default();
        let rule_detector = Arc::new(MultiLayeredLicenseDetector::new(&scanning_config, rule_config));

        let integration_config = MLIntegrationConfig::default();
        let orchestrator = MLIntegrationOrchestrator::new(
            integration_config,
            model_manager,
            feature_extractor,
            classifier,
            adaptive_learner,
            pattern_engine,
            rule_detector,
        );

        let result = orchestrator.detect_license_hybrid("MIT License text").await.unwrap();
        assert!(result.overall_confidence >= 0.0 && result.overall_confidence <= 1.0);
        assert!(!result.evidence.is_empty());
    }

    #[tokio::test]
    async fn test_performance_metrics() {
        let model_config = crate::compliance::ml_model_manager::MLModelConfig::default();
        let model_manager = Arc::new(MLModelManager::new(model_config));

        let feature_config = FeatureExtractorConfig::default();
        let feature_extractor = Arc::new(MLFeatureExtractor::new(feature_config));

        let classifier_config = ClassifierConfig::default();
        let classifier = Arc::new(LicensePatternClassifier::new(
            classifier_config,
            Arc::clone(&feature_extractor),
            Arc::clone(&model_manager),
        ));

        let learning_config = LearningConfig::default();
        let adaptive_learner = Arc::new(AdaptivePatternLearner::new(
            learning_config,
            Arc::clone(&feature_extractor),
            Arc::clone(&model_manager),
            Arc::clone(&classifier),
        ));

        let pattern_config = PatternRecognitionConfig::default();
        let pattern_engine = Arc::new(PatternRecognitionEngine::new(
            pattern_config,
            Arc::clone(&feature_extractor),
        ));

        let rule_config = MultiLayerConfig::default();
        let scanning_config = crate::config::ScanningConfig::default();
        let rule_detector = Arc::new(MultiLayeredLicenseDetector::new(&scanning_config, rule_config));

        let integration_config = MLIntegrationConfig::default();
        let orchestrator = MLIntegrationOrchestrator::new(
            integration_config,
            model_manager,
            feature_extractor,
            classifier,
            adaptive_learner,
            pattern_engine,
            rule_detector,
        );

        let metrics = orchestrator.get_performance_metrics().await;
        assert_eq!(metrics.total_detections, 0);
    }
}