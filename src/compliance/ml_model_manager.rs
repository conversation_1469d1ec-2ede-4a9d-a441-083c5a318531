//! # ML Model Manager
//!
//! This module provides comprehensive ML model management capabilities including
//! model loading, versioning, training, updating, and performance monitoring.

use crate::error::{InfinitumError, Result};
use crate::observability::instrumentation::{self, create_span, create_span_with_attributes, counter, histogram, record_counter, record_histogram};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    path::{Path, PathBuf},
    sync::Arc,
};
use tokio::sync::RwLock;
use tracing::{debug, info, instrument, warn};
use chrono::{DateTime, Utc};
use opentelemetry::KeyValue;

/// Model types supported by the system
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
#[serde(rename_all = "snake_case")]
pub enum ModelType {
    /// Text classification for license identification
    TextClassifier,
    /// Feature extraction for license texts
    FeatureExtractor,
    /// Similarity matching for license texts
    SimilarityMatcher,
    /// Anomaly detection for unusual patterns
    AnomalyDetector,
    /// Clustering for license categorization
    ClusterModel,
}

/// Model version information
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ModelVersion {
    /// Version identifier
    pub version: String,
    /// Model type
    pub model_type: ModelType,
    /// Creation timestamp
    pub created_at: DateTime<Utc>,
    /// Training data size
    pub training_samples: usize,
    /// Model accuracy/performance metrics
    pub metrics: HashMap<String, f64>,
    /// Model file path
    pub model_path: PathBuf,
    /// Model metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Model performance metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelMetrics {
    /// Accuracy score
    pub accuracy: f64,
    /// Precision score
    pub precision: f64,
    /// Recall score
    pub recall: f64,
    /// F1 score
    pub f1_score: f64,
    /// Training loss
    pub training_loss: Option<f64>,
    /// Validation loss
    pub validation_loss: Option<f64>,
    /// Inference time in milliseconds
    pub inference_time_ms: f64,
    /// Memory usage in MB
    pub memory_usage_mb: f64,
}

/// Model training configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrainingConfig {
    /// Learning rate
    pub learning_rate: f64,
    /// Batch size
    pub batch_size: usize,
    /// Number of epochs
    pub epochs: usize,
    /// Validation split ratio
    pub validation_split: f64,
    /// Early stopping patience
    pub early_stopping_patience: usize,
    /// Maximum training time in seconds
    pub max_training_time_secs: u64,
}

/// ML Model Manager configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MLModelConfig {
    /// Model storage directory
    pub model_dir: PathBuf,
    /// Maximum number of model versions to keep
    pub max_versions_per_model: usize,
    /// Enable model versioning
    pub enable_versioning: bool,
    /// Enable automatic model updates
    pub enable_auto_updates: bool,
    /// Minimum performance threshold for model updates
    pub min_performance_threshold: f64,
    /// Model cache size
    pub cache_size: usize,
}

/// Core ML Model Manager
pub struct MLModelManager {
    config: MLModelConfig,
    models: Arc<RwLock<HashMap<ModelType, Vec<ModelVersion>>>>,
    active_models: Arc<RwLock<HashMap<ModelType, ModelVersion>>>,
    metrics_history: Arc<RwLock<HashMap<ModelType, Vec<(DateTime<Utc>, ModelMetrics)>>>>,
}

impl MLModelManager {
    /// Create new ML Model Manager
    pub fn new(config: MLModelConfig) -> Self {
        Self {
            config,
            models: Arc::new(RwLock::new(HashMap::new())),
            active_models: Arc::new(RwLock::new(HashMap::new())),
            metrics_history: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Initialize the model manager
    #[instrument(skip(self), fields(model_dir = %self.config.model_dir.display()))]
    pub async fn initialize(&self) -> Result<()> {
        info!("Initializing ML Model Manager");

        // Create model directory if it doesn't exist
        if !self.config.model_dir.exists() {
            tokio::fs::create_dir_all(&self.config.model_dir).await
                .map_err(|e| InfinitumError::Io {
                    message: format!("Failed to create model directory: {}", e),
                })?;
        }

        // Load existing models
        self.load_existing_models().await?;

        info!("ML Model Manager initialized successfully");
        Ok(())
    }

    /// Load a model from file
    #[instrument(skip(self), fields(model_path = %model_path.display(), model_type = ?model_type))]
    pub async fn load_model(&self, model_path: &Path, model_type: ModelType, version: String) -> Result<ModelVersion> {
        let _span = create_span_with_attributes(
            "ml_model_load",
            vec![
                KeyValue::new("model_type", model_type.as_ref().to_string()),
                KeyValue::new("model_path", model_path.display().to_string()),
                KeyValue::new("version", version.clone()),
            ],
        );

        let model_load_counter = counter("ml_model_load_total", "Total number of model load operations");
        let model_load_duration = histogram("ml_model_load_duration_seconds", "Time taken to load models");
        let memory_usage_histogram = histogram("ml_model_memory_usage_mb", "Memory usage during model operations");

        let start_time = std::time::Instant::now();
        if !model_path.exists() {
            record_counter(&model_load_counter, 1, vec![KeyValue::new("status", "failed")]);
            return Err(InfinitumError::FileNotFound {
                path: model_path.to_string_lossy().to_string(),
            });
        }
        if !model_path.exists() {
            return Err(InfinitumError::FileNotFound {
                path: model_path.to_string_lossy().to_string(),
            });
        }

        // Read model metadata
        let metadata_path = model_path.with_extension("json");
        let metadata = if metadata_path.exists() {
            let metadata_content = tokio::fs::read_to_string(&metadata_path).await
                .map_err(|e| InfinitumError::Io {
                    message: format!("Failed to read model metadata: {}", e),
                })?;
            serde_json::from_str(&metadata_content).unwrap_or_default()
        } else {
            HashMap::new()
        };

        let model_version = ModelVersion {
            version,
            model_type: model_type.clone(),
            created_at: Utc::now(),
            training_samples: 0,
            metrics: HashMap::new(),
            model_path: model_path.to_path_buf(),
            metadata,
        };

        // Store model version
        {
            let mut models = self.models.write().await;
            models.entry(model_type.clone()).or_insert(Vec::new()).push(model_version.clone());
        }

        // Set as active if no active model exists
        {
            let mut active_models = self.active_models.write().await;
            if !active_models.contains_key(&model_type) {
                active_models.insert(model_type, model_version.clone());
            }
        }

        info!("Model loaded successfully: {} v{}", model_type.as_ref(), model_version.version);
        Ok(model_version)
    }

    /// Get active model for a given type
    pub async fn get_active_model(&self, model_type: &ModelType) -> Option<ModelVersion> {
        let active_models = self.active_models.read().await;
        active_models.get(model_type).cloned()
    }

    #[instrument(skip(self), fields(model_type = ?model_type, version = %version))]
    pub async fn set_active_model(&self, model_type: ModelType, version: String) -> Result<()> {
        let models = self.models.read().await;
        if let Some(versions) = models.get(&model_type) {
            if let Some(model_version) = versions.iter().find(|v| v.version == version) {
                let mut active_models = self.active_models.write().await;
                active_models.insert(model_type, model_version.clone());
                info!("Active model set: {} v{}", model_type.as_ref(), version);
                Ok(())
            } else {
                Err(InfinitumError::InvalidInput {
                    field: "version".to_string(),
                    message: format!("Model version {} not found for type {:?}", version, model_type),
                })
            }
        } else {
            Err(InfinitumError::InvalidInput {
                field: "model_type".to_string(),
                message: format!("No models found for type {:?}", model_type),
            })
        }
    }

    /// Train a new model
    #[instrument(skip(self, training_data), fields(model_type = ?model_type))]
    pub async fn train_model(
        &self,
        model_type: ModelType,
        training_data: Vec<(String, String)>, // (text, label)
        config: TrainingConfig,
    ) -> Result<ModelVersion> {
        info!("Starting model training: {:?}", model_type);

        // Generate new version
        let version = self.generate_version().await;

        // Create model file path
        let model_filename = format!("{}_{}.bin", model_type.as_ref(), version);
        let model_path = self.config.model_dir.join(model_filename);

        // TODO: Implement actual model training logic
        // This would involve calling the appropriate ML library functions
        // For now, we'll create a placeholder

        let metrics = ModelMetrics {
            accuracy: 0.85,
            precision: 0.82,
            recall: 0.88,
            f1_score: 0.85,
            training_loss: Some(0.15),
            validation_loss: Some(0.18),
            inference_time_ms: 5.2,
            memory_usage_mb: 45.6,
        };

        let model_version = ModelVersion {
            version,
            model_type: model_type.clone(),
            created_at: Utc::now(),
            training_samples: training_data.len(),
            metrics: HashMap::from([
                ("accuracy".to_string(), metrics.accuracy),
                ("precision".to_string(), metrics.precision),
                ("recall".to_string(), metrics.recall),
                ("f1_score".to_string(), metrics.f1_score),
            ]),
            model_path: model_path.clone(),
            metadata: HashMap::from([
                ("training_config".to_string(), serde_json::to_value(&config).unwrap()),
                ("training_samples".to_string(), serde_json::json!(training_data.len())),
            ]),
        };

        // Save model metadata
        let metadata_path = model_path.with_extension("json");
        let metadata_content = serde_json::to_string_pretty(&model_version.metadata)?;
        tokio::fs::write(&metadata_path, metadata_content).await?;

        // Store model version
        {
            let mut models = self.models.write().await;
            let versions = models.entry(model_type.clone()).or_insert(Vec::new());
            versions.push(model_version.clone());

            // Keep only the latest versions
            if versions.len() > self.config.max_versions_per_model {
                versions.sort_by(|a, b| b.created_at.cmp(&a.created_at));
                versions.truncate(self.config.max_versions_per_model);
            }
        }

        // Record metrics
        self.record_metrics(&model_type, metrics).await;

        info!("Model training completed: {} v{}", model_type.as_ref(), model_version.version);
        Ok(model_version)
    }

    /// Update model with new data
    #[instrument(skip(self, new_data), fields(model_type = ?model_type))]
    pub async fn update_model(
        &self,
        model_type: ModelType,
        new_data: Vec<(String, String)>,
    ) -> Result<ModelVersion> {
        info!("Updating model: {:?}", model_type);

        // Get current active model
        let current_model = self.get_active_model(&model_type).await
            .ok_or_else(|| InfinitumError::InvalidInput {
                field: "model_type".to_string(),
                message: format!("No active model found for type {:?}", model_type),
            })?;

        // Combine existing training data with new data
        // TODO: In a real implementation, you'd need to store or reconstruct the original training data
        let combined_data = new_data;

        // Train new model version
        let config = TrainingConfig {
            learning_rate: 0.001,
            batch_size: 32,
            epochs: 10,
            validation_split: 0.2,
            early_stopping_patience: 3,
            max_training_time_secs: 3600,
        };

        let new_model = self.train_model(model_type, combined_data, config).await?;

        // Compare performance
        let current_accuracy = current_model.metrics.get("accuracy").unwrap_or(&0.0);
        let new_accuracy = new_model.metrics.get("accuracy").unwrap_or(&0.0);

        if new_accuracy > current_accuracy || *new_accuracy >= self.config.min_performance_threshold {
            self.set_active_model(new_model.model_type.clone(), new_model.version.clone()).await?;
            info!("Model updated successfully: improved from {:.3} to {:.3} accuracy",
                  current_accuracy, new_accuracy);
        } else {
            warn!("Model update rejected: accuracy decreased from {:.3} to {:.3}",
                  current_accuracy, new_accuracy);
        }

        Ok(new_model)
    }

    /// Record model performance metrics
    #[instrument(skip(self), fields(model_type = ?model_type))]
    pub async fn record_metrics(&self, model_type: &ModelType, metrics: ModelMetrics) {
        let mut history = self.metrics_history.write().await;
        let model_history = history.entry(model_type.clone()).or_insert(Vec::new());
        model_history.push((Utc::now(), metrics));

        // Keep only recent history (last 100 entries)
        if model_history.len() > 100 {
            model_history.sort_by(|a, b| b.0.cmp(&a.0));
            model_history.truncate(100);
        }
    }

    /// Get model performance history
    pub async fn get_metrics_history(&self, model_type: &ModelType) -> Vec<(DateTime<Utc>, ModelMetrics)> {
        let history = self.metrics_history.read().await;
        history.get(model_type).cloned().unwrap_or_default()
    }

    /// Get all available model versions for a type
    pub async fn get_model_versions(&self, model_type: &ModelType) -> Vec<ModelVersion> {
        let models = self.models.read().await;
        models.get(model_type).cloned().unwrap_or_default()
    }

    /// Delete old model versions
    #[instrument(skip(self), fields(model_type = ?model_type))]
    pub async fn cleanup_old_versions(&self, model_type: &ModelType) -> Result<usize> {
        let mut models = self.models.write().await;
        if let Some(versions) = models.get_mut(model_type) {
            let active_version = {
                let active_models = self.active_models.read().await;
                active_models.get(model_type).map(|v| v.version.clone())
            };

            // Keep active version and recent versions
            versions.retain(|v| {
                Some(&v.version) == active_version.as_ref() ||
                versions.len() <= self.config.max_versions_per_model
            });

            let deleted_count = versions.len().saturating_sub(self.config.max_versions_per_model);
            if deleted_count > 0 {
                versions.sort_by(|a, b| b.created_at.cmp(&a.created_at));
                versions.truncate(self.config.max_versions_per_model);
            }

            info!("Cleaned up {} old versions for model type {:?}", deleted_count, model_type);
            Ok(deleted_count)
        } else {
            Ok(0)
        }
    }

    /// Load existing models from disk
    async fn load_existing_models(&self) -> Result<()> {
        let mut entries = tokio::fs::read_dir(&self.config.model_dir).await
            .map_err(|e| InfinitumError::Io {
                message: format!("Failed to read model directory: {}", e),
            })?;

        while let Some(entry) = entries.next_entry().await? {
            let path = entry.path();
            if path.extension().and_then(|e| e.to_str()) == Some("bin") {
                if let Some(file_stem) = path.file_stem().and_then(|s| s.to_str()) {
                    // Parse model type and version from filename
                    if let Some((model_type_str, version)) = self.parse_model_filename(file_stem) {
                        if let Some(model_type) = self.parse_model_type(&model_type_str) {
                            let _ = self.load_model(&path, model_type, version).await;
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// Generate new version string
    async fn generate_version(&self) -> String {
        format!("v{}", Utc::now().timestamp())
    }

    /// Parse model filename to extract type and version
    fn parse_model_filename(&self, filename: &str) -> Option<(String, String)> {
        let parts: Vec<&str> = filename.split('_').collect();
        if parts.len() >= 2 {
            let model_type = parts[0..parts.len()-1].join("_");
            let version = parts.last()?.to_string();
            Some((model_type, version))
        } else {
            None
        }
    }

    /// Parse model type from string
    fn parse_model_type(&self, type_str: &str) -> Option<ModelType> {
        match type_str {
            "text_classifier" => Some(ModelType::TextClassifier),
            "feature_extractor" => Some(ModelType::FeatureExtractor),
            "similarity_matcher" => Some(ModelType::SimilarityMatcher),
            "anomaly_detector" => Some(ModelType::AnomalyDetector),
            "cluster_model" => Some(ModelType::ClusterModel),
            _ => None,
        }
    }
}

impl Default for MLModelConfig {
    fn default() -> Self {
        Self {
            model_dir: PathBuf::from("./models"),
            max_versions_per_model: 5,
            enable_versioning: true,
            enable_auto_updates: true,
            min_performance_threshold: 0.8,
            cache_size: 10,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_model_manager_initialization() {
        let config = MLModelConfig::default();
        let manager = MLModelManager::new(config);
        assert!(manager.initialize().await.is_ok());
    }

    #[tokio::test]
    async fn test_model_version_creation() {
        let config = MLModelConfig::default();
        let manager = MLModelManager::new(config);

        let training_data = vec![
            ("MIT License text".to_string(), "MIT".to_string()),
            ("Apache License text".to_string(), "Apache-2.0".to_string()),
        ];

        let training_config = TrainingConfig {
            learning_rate: 0.001,
            batch_size: 32,
            epochs: 10,
            validation_split: 0.2,
            early_stopping_patience: 3,
            max_training_time_secs: 3600,
        };

        let result = manager.train_model(ModelType::TextClassifier, training_data, training_config).await;
        assert!(result.is_ok());

        let model_version = result.unwrap();
        assert_eq!(model_version.model_type, ModelType::TextClassifier);
        assert!(model_version.version.starts_with('v'));
    }
}