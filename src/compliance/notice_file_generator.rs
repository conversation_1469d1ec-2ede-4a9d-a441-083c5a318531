//! # Notice File Generator
//!
//! Generates comprehensive notice files for software distribution with license text aggregation,
//! copyright compilation, and attribution requirements.

use crate::{
    compliance::{
        report_generator::{BaseReportGenerator, GeneratedReport, ReportGenerator, ReportGeneratorConfig, ReportType},
        ComplianceRequest, OutputFormat,
    },
    config::ComplianceConfig,
    error::{InfinitumError, Result},
    scanners::{ScanResult, SoftwareComponent},
};
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, path::Path};
use tracing::{info, instrument};
use uuid::Uuid;

/// Notice file configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NoticeFileConfig {
    /// Include full license texts
    pub include_full_licenses: bool,
    /// Include copyright notices
    pub include_copyrights: bool,
    /// Include attribution requirements
    pub include_attributions: bool,
    /// Group by license type
    pub group_by_license: bool,
    /// Include package information
    pub include_package_info: bool,
    /// Custom header text
    pub header_text: Option<String>,
    /// Custom footer text
    pub footer_text: Option<String>,
    /// Language for notices
    pub language: String,
}

/// License notice information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LicenseNotice {
    /// License identifier
    pub license_id: String,
    /// License name
    pub license_name: String,
    /// Full license text
    pub license_text: Option<String>,
    /// License URL
    pub license_url: Option<String>,
    /// Packages using this license
    pub packages: Vec<PackageNotice>,
    /// Attribution requirements
    pub attribution_requirements: Vec<String>,
}

/// Package notice information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageNotice {
    /// Package name
    pub name: String,
    /// Package version
    pub version: String,
    /// Copyright notice
    pub copyright: Option<String>,
    /// Homepage URL
    pub homepage: Option<String>,
    /// Additional attribution text
    pub attribution: Option<String>,
}

/// Copyright notice information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CopyrightNotice {
    /// Copyright holder
    pub holder: String,
    /// Copyright years
    pub years: Vec<String>,
    /// Associated packages
    pub packages: Vec<String>,
}

/// Complete notice file
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NoticeFile {
    /// File header
    pub header: String,
    /// License notices
    pub license_notices: Vec<LicenseNotice>,
    /// Copyright notices
    pub copyright_notices: Vec<CopyrightNotice>,
    /// Attribution requirements
    pub attribution_requirements: Vec<String>,
    /// File footer
    pub footer: String,
    /// Generation metadata
    pub metadata: NoticeMetadata,
}

/// Notice file metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NoticeMetadata {
    /// Generation timestamp
    pub generated_at: chrono::DateTime<chrono::Utc>,
    /// Tool name
    pub tool_name: String,
    /// Tool version
    pub tool_version: String,
    /// Organization
    pub organization: String,
    /// Language used
    pub language: String,
}

/// Notice File Generator
pub struct NoticeFileGenerator {
    base: BaseReportGenerator,
    config: NoticeFileConfig,
}

impl NoticeFileGenerator {
    /// Create new notice file generator
    pub fn new(
        config: ReportGeneratorConfig,
        compliance_config: ComplianceConfig,
        notice_config: NoticeFileConfig,
    ) -> Self {
        Self {
            base: BaseReportGenerator::new(config, compliance_config),
            config: notice_config,
        }
    }

    /// Generate notice file from scan results
    async fn generate_notice_file(
        &self,
        request: &ComplianceRequest,
        scan_results: &[ScanResult],
    ) -> Result<NoticeFile> {
        let mut license_notices = Vec::new();
        let mut copyright_notices = Vec::new();
        let mut attribution_requirements = Vec::new();

        // Collect license information
        let mut license_map: HashMap<String, Vec<PackageNotice>> = HashMap::new();
        let mut copyright_map: HashMap<String, Vec<String>> = HashMap::new();

        for scan_result in scan_results {
            for component in &scan_result.software_components {
                self.process_component(component, &mut license_map, &mut copyright_map).await?;
            }
        }

        // Create license notices
        for (license_id, packages) in license_map {
            let license_notice = self.create_license_notice(&license_id, packages).await?;
            license_notices.push(license_notice);
        }

        // Create copyright notices
        for (holder, packages) in copyright_map {
            if !packages.is_empty() {
                copyright_notices.push(CopyrightNotice {
                    holder,
                    years: vec!["2024".to_string()], // Default year
                    packages,
                });
            }
        }

        // Generate attribution requirements
        if self.config.include_attributions {
            attribution_requirements = self.generate_attribution_requirements(&license_notices).await?;
        }

        // Sort notices
        if self.config.group_by_license {
            license_notices.sort_by(|a, b| a.license_name.cmp(&b.license_name));
        }
        copyright_notices.sort_by(|a, b| a.holder.cmp(&b.holder));

        let header = self.generate_header(request).await?;
        let footer = self.generate_footer().await?;

        let metadata = NoticeMetadata {
            generated_at: chrono::Utc::now(),
            tool_name: self.base.generator_config().creator.tool.clone(),
            tool_version: env!("CARGO_PKG_VERSION").to_string(),
            organization: request.config.organization.clone(),
            language: self.config.language.clone(),
        };

        Ok(NoticeFile {
            header,
            license_notices,
            copyright_notices,
            attribution_requirements,
            footer,
            metadata,
        })
    }

    /// Process a software component for notice generation
    async fn process_component(
        &self,
        component: &SoftwareComponent,
        license_map: &mut HashMap<String, Vec<PackageNotice>>,
        copyright_map: &mut HashMap<String, Vec<String>>,
    ) -> Result<()> {
        let license_id = component.license.clone()
            .unwrap_or_else(|| "Unknown".to_string());

        let package_notice = PackageNotice {
            name: component.name.clone(),
            version: component.version.clone(),
            copyright: component.copyright.clone(),
            homepage: component.homepage.clone(),
            attribution: None,
        };

        license_map.entry(license_id).or_insert_with(Vec::new).push(package_notice);

        // Process copyright information
        if let Some(copyright) = &component.copyright {
            let holder = self.extract_copyright_holder(copyright);
            copyright_map.entry(holder).or_insert_with(Vec::new).push(component.name.clone());
        }

        Ok(())
    }

    /// Create license notice
    async fn create_license_notice(
        &self,
        license_id: &str,
        packages: Vec<PackageNotice>,
    ) -> Result<LicenseNotice> {
        let (license_name, license_text, license_url) = self.get_license_info(license_id).await?;

        let attribution_requirements = self.get_attribution_requirements(license_id).await?;

        Ok(LicenseNotice {
            license_id: license_id.to_string(),
            license_name,
            license_text: if self.config.include_full_licenses { license_text } else { None },
            license_url,
            packages,
            attribution_requirements,
        })
    }

    /// Get license information
    async fn get_license_info(&self, license_id: &str) -> Result<(String, Option<String>, Option<String>)> {
        // This would typically query a license database
        // For now, return basic information
        let (name, text, url) = match license_id {
            "MIT" => (
                "MIT License".to_string(),
                Some(include_str!("../../templates/licenses/MIT.txt").to_string()),
                Some("https://opensource.org/licenses/MIT".to_string()),
            ),
            "Apache-2.0" => (
                "Apache License 2.0".to_string(),
                Some(include_str!("../../templates/licenses/Apache-2.0.txt").to_string()),
                Some("https://www.apache.org/licenses/LICENSE-2.0".to_string()),
            ),
            "BSD-3-Clause" => (
                "BSD 3-Clause License".to_string(),
                Some(include_str!("../../templates/licenses/BSD-3-Clause.txt").to_string()),
                Some("https://opensource.org/licenses/BSD-3-Clause".to_string()),
            ),
            "GPL-3.0" => (
                "GNU General Public License v3.0".to_string(),
                Some(include_str!("../../templates/licenses/GPL-3.0.txt").to_string()),
                Some("https://www.gnu.org/licenses/gpl-3.0.html".to_string()),
            ),
            _ => (
                format!("{} License", license_id),
                None,
                None,
            ),
        };

        Ok((name, text, url))
    }

    /// Get attribution requirements for a license
    async fn get_attribution_requirements(&self, license_id: &str) -> Result<Vec<String>> {
        // Attribution requirements vary by license
        let requirements = match license_id {
            "MIT" => vec![
                "Include original copyright notice".to_string(),
                "Include license text".to_string(),
            ],
            "Apache-2.0" => vec![
                "Include original copyright notice".to_string(),
                "Include license text".to_string(),
                "Include NOTICE file if present".to_string(),
                "State changes made to the original".to_string(),
            ],
            "BSD-3-Clause" => vec![
                "Include original copyright notice".to_string(),
                "Include license text".to_string(),
            ],
            "GPL-3.0" => vec![
                "Include original copyright notice".to_string(),
                "Include license text".to_string(),
                "Make source code available".to_string(),
                "State changes made to the original".to_string(),
            ],
            _ => vec![
                "Include original copyright notice".to_string(),
                "Include license text".to_string(),
            ],
        };

        Ok(requirements)
    }

    /// Extract copyright holder from copyright text
    fn extract_copyright_holder(&self, copyright: &str) -> String {
        // Simple extraction - in practice, this would be more sophisticated
        if copyright.contains("Copyright") {
            copyright.replace("Copyright", "").trim().to_string()
        } else {
            copyright.to_string()
        }
    }

    /// Generate attribution requirements from license notices
    async fn generate_attribution_requirements(&self, license_notices: &[LicenseNotice]) -> Result<Vec<String>> {
        let mut requirements = Vec::new();

        for notice in license_notices {
            for req in &notice.attribution_requirements {
                if !requirements.contains(req) {
                    requirements.push(req.clone());
                }
            }
        }

        Ok(requirements)
    }

    /// Generate notice file header
    async fn generate_header(&self, request: &ComplianceRequest) -> Result<String> {
        let mut header = if let Some(custom_header) = &self.config.header_text {
            custom_header.clone()
        } else {
            format!(
                "NOTICE FILE FOR {}\n{}\n\n",
                request.config.title,
                "=".repeat(request.config.title.len() + 15)
            )
        };

        header.push_str(&format!(
            "This product includes software developed by {}.\n\n",
            request.config.organization
        ));

        header.push_str(&format!(
            "Generated on: {}\n",
            chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC")
        ));

        header.push_str(&format!(
            "Generated by: {} v{}\n\n",
            self.base.generator_config().creator.tool,
            env!("CARGO_PKG_VERSION")
        ));

        Ok(header)
    }

    /// Generate notice file footer
    async fn generate_footer(&self) -> Result<String> {
        if let Some(custom_footer) = &self.config.footer_text {
            Ok(custom_footer.clone())
        } else {
            Ok("\nEND OF NOTICE FILE\n".to_string())
        }
    }

    /// Format notice file as text
    fn format_as_text(&self, notice_file: &NoticeFile) -> String {
        let mut output = notice_file.header.clone();

        // License notices
        if !notice_file.license_notices.is_empty() {
            output.push_str("LICENSE INFORMATION\n");
            output.push_str("==================\n\n");

            for notice in &notice_file.license_notices {
                output.push_str(&format!("{} ({})\n", notice.license_name, notice.license_id));
                output.push_str(&"=".repeat(notice.license_name.len() + notice.license_id.len() + 3));
                output.push_str("\n\n");

                if self.config.include_package_info {
                    output.push_str("Used by packages:\n");
                    for package in &notice.packages {
                        output.push_str(&format!("  - {} {}\n", package.name, package.version));
                        if let Some(copyright) = &package.copyright {
                            output.push_str(&format!("    Copyright: {}\n", copyright));
                        }
                    }
                    output.push_str("\n");
                }

                if self.config.include_full_licenses {
                    if let Some(text) = &notice.license_text {
                        output.push_str("License Text:\n");
                        output.push_str("-------------\n");
                        output.push_str(text);
                        output.push_str("\n\n");
                    }
                }

                if let Some(url) = &notice.license_url {
                    output.push_str(&format!("License URL: {}\n\n", url));
                }
            }
        }

        // Copyright notices
        if self.config.include_copyrights && !notice_file.copyright_notices.is_empty() {
            output.push_str("COPYRIGHT NOTICES\n");
            output.push_str("=================\n\n");

            for copyright in &notice_file.copyright_notices {
                output.push_str(&format!("Copyright {}\n", copyright.holder));
                output.push_str(&format!("Used by: {}\n\n", copyright.packages.join(", ")));
            }
        }

        // Attribution requirements
        if self.config.include_attributions && !notice_file.attribution_requirements.is_empty() {
            output.push_str("ATTRIBUTION REQUIREMENTS\n");
            output.push_str("========================\n\n");

            for (i, req) in notice_file.attribution_requirements.iter().enumerate() {
                output.push_str(&format!("{}. {}\n", i + 1, req));
            }
            output.push_str("\n");
        }

        output.push_str(&notice_file.footer);
        output
    }

    /// Format notice file as HTML
    fn format_as_html(&self, notice_file: &NoticeFile) -> String {
        let mut output = String::from("<!DOCTYPE html>\n<html>\n<head>\n");
        output.push_str("<title>Software Notice File</title>\n");
        output.push_str("<style>\n");
        output.push_str("body { font-family: Arial, sans-serif; margin: 40px; }\n");
        output.push_str("h1, h2 { color: #333; }\n");
        output.push_str(".license { margin-bottom: 30px; }\n");
        output.push_str(".package { margin-left: 20px; }\n");
        output.push_str("</style>\n");
        output.push_str("</head>\n<body>\n");

        output.push_str(&format!("<pre>{}</pre>\n", notice_file.header.replace("\n", "<br>")));

        if !notice_file.license_notices.is_empty() {
            output.push_str("<h2>License Information</h2>\n");
            for notice in &notice_file.license_notices {
                output.push_str(&format!("<div class=\"license\">\n<h3>{} ({})</h3>\n", notice.license_name, notice.license_id));

                if self.config.include_package_info {
                    output.push_str("<h4>Used by packages:</h4>\n<ul>\n");
                    for package in &notice.packages {
                        output.push_str(&format!("<li class=\"package\">{} {}</li>\n", package.name, package.version));
                    }
                    output.push_str("</ul>\n");
                }

                if self.config.include_full_licenses {
                    if let Some(text) = &notice.license_text {
                        output.push_str("<h4>License Text:</h4>\n");
                        output.push_str(&format!("<pre>{}</pre>\n", text));
                    }
                }

                output.push_str("</div>\n");
            }
        }

        output.push_str("</body>\n</html>\n");
        output
    }
}

#[async_trait]
impl ReportGenerator for NoticeFileGenerator {
    async fn generate_report(
        &self,
        request: &ComplianceRequest,
        scan_results: &[ScanResult],
    ) -> Result<GeneratedReport> {
        let start_time = chrono::Utc::now();

        info!("Generating notice file for request {}", request.id);

        // Validate output directory
        self.base.validate_output_dir().await?;

        // Generate notice file
        let notice_file = self.generate_notice_file(request, scan_results).await?;

        // Generate outputs in requested formats
        let mut files = HashMap::new();
        for format in &request.config.output_formats {
            if self.supported_formats().contains(format) {
                let filename = self.base.generate_filename("notice", format, request.id);
                let file_path = self.base.create_output_path(&filename);

                let content = match format {
                    OutputFormat::Html => self.format_as_html(&notice_file),
                    _ => self.format_as_text(&notice_file),
                };

                self.base.write_file(&file_path, &content).await?;
                files.insert(format.clone(), file_path);
            }
        }

        let end_time = chrono::Utc::now();
        let duration = end_time - start_time;

        let metadata = self.base.generate_metadata(request);
        let mut enhanced_metadata = metadata;
        enhanced_metadata.insert("license_count".to_string(), serde_json::json!(notice_file.license_notices.len()));
        enhanced_metadata.insert("copyright_count".to_string(), serde_json::json!(notice_file.copyright_notices.len()));

        Ok(GeneratedReport {
            id: Uuid::new_v4(),
            report_type: ReportType::Notice,
            files,
            metadata: enhanced_metadata,
            generated_at: end_time,
            duration_ms: duration.num_milliseconds() as u64,
        })
    }

    fn supported_formats(&self) -> Vec<OutputFormat> {
        vec![OutputFormat::Html, OutputFormat::Csv] // Csv used as plain text
    }

    fn name(&self) -> &'static str {
        "Notice File Generator"
    }

    fn description(&self) -> &'static str {
        "Generates comprehensive notice files with license texts, copyrights, and attribution requirements"
    }

    fn can_handle(&self, request: &ComplianceRequest) -> bool {
        request.config.output_formats.iter().any(|f| self.supported_formats().contains(f))
    }
}

impl Default for NoticeFileConfig {
    fn default() -> Self {
        Self {
            include_full_licenses: true,
            include_copyrights: true,
            include_attributions: true,
            group_by_license: true,
            include_package_info: true,
            header_text: None,
            footer_text: None,
            language: "en".to_string(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_notice_file_config_default() {
        let config = NoticeFileConfig::default();
        assert!(config.include_full_licenses);
        assert!(config.include_copyrights);
        assert!(config.include_attributions);
        assert!(config.group_by_license);
        assert_eq!(config.language, "en");
    }

    #[test]
    fn test_notice_generator_creation() {
        let config = ReportGeneratorConfig::default();
        let compliance_config = ComplianceConfig::default();
        let notice_config = NoticeFileConfig::default();
        let generator = NoticeFileGenerator::new(config, compliance_config, notice_config);
        assert_eq!(generator.name(), "Notice File Generator");
    }

    #[test]
    fn test_supported_formats() {
        let config = ReportGeneratorConfig::default();
        let compliance_config = ComplianceConfig::default();
        let notice_config = NoticeFileConfig::default();
        let generator = NoticeFileGenerator::new(config, compliance_config, notice_config);
        let formats = generator.supported_formats();
        assert!(formats.contains(&OutputFormat::Html));
    }

    #[tokio::test]
    async fn test_copyright_holder_extraction() {
        let config = ReportGeneratorConfig::default();
        let compliance_config = ComplianceConfig::default();
        let notice_config = NoticeFileConfig::default();
        let generator = NoticeFileGenerator::new(config, compliance_config, notice_config);

        let copyright = "Copyright (c) 2024 Example Corp";
        let holder = generator.extract_copyright_holder(copyright);
        assert_eq!(holder, "(c) 2024 Example Corp");
    }
}