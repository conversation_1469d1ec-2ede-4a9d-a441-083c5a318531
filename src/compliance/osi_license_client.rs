//! # OSI License Client
//!
//! OSI approved license integration client. Handles OSI API communication,
//! license approval status updates, and historical tracking.

use crate::error::{InfinitumError, Result};
use chrono::{DateTime, Utc};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, time::Duration};
use tracing::{error, info, warn};

/// OSI license information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OsiLicense {
    /// License ID (e.g., "MIT", "Apache-2.0")
    pub id: String,
    /// License name
    pub name: String,
    /// OSI approved status
    pub approved: bool,
    /// Approval date
    pub approved_date: Option<DateTime<Utc>>,
    /// Deprecated status
    pub deprecated: bool,
    /// Superseded by (if deprecated)
    pub superseded_by: Option<String>,
    /// License text URL
    pub text_url: Option<String>,
    /// OSI page URL
    pub osi_url: String,
    /// Notes
    pub notes: Option<String>,
    /// Last updated timestamp
    pub last_updated: DateTime<Utc>,
}

/// OSI license approval history
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OsiApprovalHistory {
    /// License ID
    pub license_id: String,
    /// Approval status
    pub approved: bool,
    /// Change date
    pub change_date: DateTime<Utc>,
    /// Reason for change
    pub reason: Option<String>,
    /// Changed by
    pub changed_by: Option<String>,
}

/// OSI API response for license list
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OsiLicenseListResponse {
    /// Response timestamp
    pub timestamp: DateTime<Utc>,
    /// Total licenses
    pub total: usize,
    /// Licenses
    pub licenses: Vec<OsiLicense>,
}

/// OSI client configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OsiClientConfig {
    /// Base API URL
    pub api_base_url: String,
    /// API version
    pub api_version: String,
    /// Request timeout in seconds
    pub timeout_seconds: u64,
    /// User agent string
    pub user_agent: String,
    /// Enable caching
    pub enable_cache: bool,
    /// Cache TTL in seconds
    pub cache_ttl_seconds: u64,
    /// Rate limit requests per minute
    pub rate_limit_per_minute: u32,
}

/// OSI client statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OsiClientStats {
    /// Total API requests made
    pub total_requests: u64,
    /// Successful requests
    pub successful_requests: u64,
    /// Failed requests
    pub failed_requests: u64,
    /// Cache hits
    pub cache_hits: u64,
    /// Cache misses
    pub cache_misses: u64,
    /// Last successful update
    pub last_successful_update: Option<DateTime<Utc>>,
    /// Current approved license count
    pub approved_license_count: usize,
    /// Current total license count
    pub total_license_count: usize,
}

/// OSI License Client
pub struct OsiLicenseClient {
    /// HTTP client
    client: Client,
    /// Configuration
    config: OsiClientConfig,
    /// License cache
    license_cache: HashMap<String, (OsiLicense, DateTime<Utc>)>,
    /// Approval history cache
    history_cache: HashMap<String, Vec<OsiApprovalHistory>>,
    /// Statistics
    stats: OsiClientStats,
    /// Last rate limit reset
    last_rate_limit_reset: DateTime<Utc>,
    /// Current request count in this window
    current_request_count: u32,
}

impl OsiLicenseClient {
    /// Create new OSI license client
    pub fn new(config: OsiClientConfig) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(config.timeout_seconds))
            .user_agent(&config.user_agent)
            .build()
            .expect("Failed to create HTTP client");

        Self {
            client,
            config,
            license_cache: HashMap::new(),
            history_cache: HashMap::new(),
            stats: OsiClientStats {
                total_requests: 0,
                successful_requests: 0,
                failed_requests: 0,
                cache_hits: 0,
                cache_misses: 0,
                last_successful_update: None,
                approved_license_count: 0,
                total_license_count: 0,
            },
            last_rate_limit_reset: Utc::now(),
            current_request_count: 0,
        }
    }

    /// Fetch all OSI licenses
    pub async fn fetch_licenses(&mut self) -> Result<Vec<OsiLicense>> {
        self.check_rate_limit().await?;

        let url = format!(
            "{}/licenses?version={}",
            self.config.api_base_url, self.config.api_version
        );

        info!("Fetching OSI licenses from: {}", url);

        let response = self.client.get(&url).send().await?;
        self.stats.total_requests += 1;

        if !response.status().is_success() {
            self.stats.failed_requests += 1;
            return Err(InfinitumError::ApiError {
                service: "OSI".to_string(),
                status_code: response.status().as_u16(),
                message: format!("Failed to fetch licenses: {}", response.status()),
            });
        }

        let license_response: OsiLicenseListResponse = response.json().await?;
        self.stats.successful_requests += 1;

        let licenses = license_response.licenses;
        self.stats.total_license_count = licenses.len();
        self.stats.approved_license_count = licenses.iter().filter(|l| l.approved).count();
        self.stats.last_successful_update = Some(Utc::now());

        // Update cache
        if self.config.enable_cache {
            let now = Utc::now();
            for license in &licenses {
                self.license_cache.insert(
                    license.id.clone(),
                    (license.clone(), now),
                );
            }
        }

        info!(
            "Successfully fetched {} OSI licenses ({} approved)",
            licenses.len(),
            self.stats.approved_license_count
        );
        Ok(licenses)
    }

    /// Fetch only approved OSI licenses
    pub async fn fetch_approved_licenses(&mut self) -> Result<Vec<OsiLicense>> {
        let all_licenses = self.fetch_licenses().await?;
        Ok(all_licenses.into_iter().filter(|l| l.approved).collect())
    }

    /// Get specific license by ID
    pub async fn get_license(&mut self, license_id: &str) -> Result<Option<OsiLicense>> {
        // Check cache first
        if self.config.enable_cache {
            if let Some((license, cached_at)) = self.license_cache.get(license_id) {
                let age = Utc::now().signed_duration_since(*cached_at);
                if age.num_seconds() < self.config.cache_ttl_seconds as i64 {
                    self.stats.cache_hits += 1;
                    return Ok(Some(license.clone()));
                }
            }
        }

        self.check_rate_limit().await?;
        self.stats.cache_misses += 1;

        let url = format!(
            "{}/licenses/{}?version={}",
            self.config.api_base_url, license_id, self.config.api_version
        );

        let response = self.client.get(&url).send().await?;
        self.stats.total_requests += 1;

        if response.status() == reqwest::StatusCode::NOT_FOUND {
            return Ok(None);
        }

        if !response.status().is_success() {
            self.stats.failed_requests += 1;
            return Err(InfinitumError::ApiError {
                service: "OSI".to_string(),
                status_code: response.status().as_u16(),
                message: format!("Failed to fetch license {}: {}", license_id, response.status()),
            });
        }

        let license: OsiLicense = response.json().await?;
        self.stats.successful_requests += 1;

        // Update cache
        if self.config.enable_cache {
            self.license_cache.insert(
                license_id.to_string(),
                (license.clone(), Utc::now()),
            );
        }

        Ok(Some(license))
    }

    /// Check if license is OSI approved
    pub async fn is_approved(&mut self, license_id: &str) -> Result<bool> {
        if let Some(license) = self.get_license(license_id).await? {
            Ok(license.approved)
        } else {
            Err(InfinitumError::LicenseNotFound {
                license_id: license_id.to_string(),
                source: "OSI".to_string(),
            })
        }
    }

    /// Get approval history for a license
    pub async fn get_approval_history(&mut self, license_id: &str) -> Result<Vec<OsiApprovalHistory>> {
        // Check cache first
        if self.config.enable_cache {
            if let Some(history) = self.history_cache.get(license_id) {
                self.stats.cache_hits += 1;
                return Ok(history.clone());
            }
        }

        self.check_rate_limit().await?;
        self.stats.cache_misses += 1;

        let url = format!(
            "{}/licenses/{}/history?version={}",
            self.config.api_base_url, license_id, self.config.api_version
        );

        let response = self.client.get(&url).send().await?;
        self.stats.total_requests += 1;

        if response.status() == reqwest::StatusCode::NOT_FOUND {
            return Ok(Vec::new());
        }

        if !response.status().is_success() {
            self.stats.failed_requests += 1;
            return Err(InfinitumError::ApiError {
                service: "OSI".to_string(),
                status_code: response.status().as_u16(),
                message: format!("Failed to fetch approval history for {}: {}", license_id, response.status()),
            });
        }

        let history: Vec<OsiApprovalHistory> = response.json().await?;
        self.stats.successful_requests += 1;

        // Update cache
        if self.config.enable_cache {
            self.history_cache.insert(license_id.to_string(), history.clone());
        }

        Ok(history)
    }

    /// Get recently changed licenses
    pub async fn get_recent_changes(&mut self, since: DateTime<Utc>) -> Result<Vec<OsiLicense>> {
        self.check_rate_limit().await?;

        let since_str = since.to_rfc3339();
        let url = format!(
            "{}/licenses/changes?since={}&version={}",
            self.config.api_base_url, since_str, self.config.api_version
        );

        info!("Fetching OSI license changes since: {}", since_str);

        let response = self.client.get(&url).send().await?;
        self.stats.total_requests += 1;

        if !response.status().is_success() {
            self.stats.failed_requests += 1;
            return Err(InfinitumError::ApiError {
                service: "OSI".to_string(),
                status_code: response.status().as_u16(),
                message: format!("Failed to fetch recent changes: {}", response.status()),
            });
        }

        let changed_licenses: Vec<OsiLicense> = response.json().await?;
        self.stats.successful_requests += 1;

        info!("Found {} OSI license changes", changed_licenses.len());
        Ok(changed_licenses)
    }

    /// Search licenses by name or ID
    pub async fn search_licenses(&mut self, query: &str) -> Result<Vec<OsiLicense>> {
        self.check_rate_limit().await?;

        let encoded_query = urlencoding::encode(query);
        let url = format!(
            "{}/licenses/search?q={}&version={}",
            self.config.api_base_url, encoded_query, self.config.api_version
        );

        let response = self.client.get(&url).send().await?;
        self.stats.total_requests += 1;

        if !response.status().is_success() {
            self.stats.failed_requests += 1;
            return Err(InfinitumError::ApiError {
                service: "OSI".to_string(),
                status_code: response.status().as_u16(),
                message: format!("Failed to search licenses: {}", response.status()),
            });
        }

        let search_results: Vec<OsiLicense> = response.json().await?;
        self.stats.successful_requests += 1;

        Ok(search_results)
    }

    /// Get license text URL
    pub async fn get_license_text_url(&mut self, license_id: &str) -> Result<Option<String>> {
        if let Some(license) = self.get_license(license_id).await? {
            Ok(license.text_url)
        } else {
            Ok(None)
        }
    }

    /// Get client statistics
    pub fn get_stats(&self) -> &OsiClientStats {
        &self.stats
    }

    /// Clear cache
    pub fn clear_cache(&mut self) {
        self.license_cache.clear();
        self.history_cache.clear();
        info!("OSI client cache cleared");
    }

    /// Check rate limit and wait if necessary
    async fn check_rate_limit(&mut self) -> Result<()> {
        let now = Utc::now();
        let window_duration = now.signed_duration_since(self.last_rate_limit_reset);

        // Reset counter if window has passed
        if window_duration.num_seconds() >= 60 {
            self.last_rate_limit_reset = now;
            self.current_request_count = 0;
        }

        // Check if we're within rate limit
        if self.current_request_count >= self.config.rate_limit_per_minute {
            let wait_seconds = 60 - window_duration.num_seconds();
            if wait_seconds > 0 {
                warn!("Rate limit reached, waiting {} seconds", wait_seconds);
                tokio::time::sleep(Duration::from_secs(wait_seconds as u64)).await;
                self.last_rate_limit_reset = Utc::now();
                self.current_request_count = 0;
            }
        }

        self.current_request_count += 1;
        Ok(())
    }

    /// Validate API connectivity
    pub async fn validate_connectivity(&mut self) -> Result<bool> {
        let url = format!("{}/health?version={}", self.config.api_base_url, self.config.api_version);

        match self.client.get(&url).send().await {
            Ok(response) => Ok(response.status().is_success()),
            Err(_) => Ok(false),
        }
    }

    /// Get deprecated licenses
    pub async fn get_deprecated_licenses(&mut self) -> Result<Vec<OsiLicense>> {
        let all_licenses = self.fetch_licenses().await?;
        Ok(all_licenses.into_iter().filter(|l| l.deprecated).collect())
    }
}

impl Default for OsiClientConfig {
    fn default() -> Self {
        Self {
            api_base_url: "https://api.opensource.org".to_string(),
            api_version: "v1".to_string(),
            timeout_seconds: 30,
            user_agent: "Infinitum-Signal/1.0".to_string(),
            enable_cache: true,
            cache_ttl_seconds: 3600, // 1 hour
            rate_limit_per_minute: 60,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_osi_client_creation() {
        let config = OsiClientConfig::default();
        let client = OsiLicenseClient::new(config);
        assert_eq!(client.stats.total_requests, 0);
    }

    #[tokio::test]
    async fn test_rate_limit_check() {
        let config = OsiClientConfig {
            rate_limit_per_minute: 2,
            ..Default::default()
        };
        let mut client = OsiLicenseClient::new(config);

        // Should not wait for first two requests
        client.check_rate_limit().await.unwrap();
        client.check_rate_limit().await.unwrap();
        assert_eq!(client.current_request_count, 2);
    }

    #[test]
    fn test_default_config() {
        let config = OsiClientConfig::default();
        assert_eq!(config.api_base_url, "https://api.opensource.org");
        assert_eq!(config.api_version, "v1");
        assert!(config.enable_cache);
    }
}