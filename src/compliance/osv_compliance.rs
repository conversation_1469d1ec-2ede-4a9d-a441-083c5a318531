//! OSV Compliance Module
//!
//! This module provides compliance validation for OSV vulnerability data
//! against OWASP, NIST, and ISO 27001 standards.

use crate::{
use crate::{
    compliance::{ComplianceFinding, ComplianceFramework, Priority, RiskLevel, Severity},
    error::Result,
    logging::audit::get_audit_logger,
    scanners::{OsvScanResult, OsvVulnerability},
};
    compliance::{ComplianceFinding, ComplianceFramework, Priority, RiskLevel, Severity},
    error::Result,
    scanners::{OsvScanResult, OsvVulnerability},
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// OSV Compliance Validator
pub struct OsvComplianceValidator {
    /// OWASP compliance rules
    owasp_rules: HashMap<String, OwaspComplianceRule>,
    /// NIST compliance rules
    nist_rules: HashMap<String, NistComplianceRule>,
    /// ISO 27001 compliance rules
    iso27001_rules: HashMap<String, Iso27001ComplianceRule>,
}

/// OWASP Compliance Rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OwaspComplianceRule {
    /// Rule identifier
    pub id: String,
    /// Rule description
    pub description: String,
    /// Applicable OWASP Top 10 categories
    pub owasp_categories: Vec<String>,
    /// Severity mapping
    pub severity_mapping: HashMap<String, Severity>,
    /// Risk score multiplier
    pub risk_multiplier: f64,
}

/// NIST Compliance Rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NistComplianceRule {
    /// Rule identifier
    pub id: String,
    /// Rule description
    pub description: String,
    /// Applicable NIST controls
    pub nist_controls: Vec<String>,
    /// CVSS score thresholds
    pub cvss_thresholds: CvssThresholds,
    /// Compliance requirements
    pub requirements: Vec<String>,
}

/// ISO 27001 Compliance Rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Iso27001ComplianceRule {
    /// Rule identifier
    pub id: String,
    /// Rule description
    pub description: String,
    /// Applicable ISO 27001 controls
    pub iso_controls: Vec<String>,
    /// Risk assessment criteria
    pub risk_criteria: RiskCriteria,
    /// Remediation priority
    pub remediation_priority: Priority,
}

/// CVSS Score Thresholds
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CvssThresholds {
    /// Critical threshold
    pub critical: f64,
    /// High threshold
    pub high: f64,
    /// Medium threshold
    pub medium: f64,
    /// Low threshold
    pub low: f64,
}

/// Risk Assessment Criteria
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskCriteria {
    /// Impact score threshold
    pub impact_threshold: f64,
    /// Likelihood score threshold
    pub likelihood_threshold: f64,
    /// Risk acceptance level
    pub risk_acceptance: f64,
}

/// OSV Compliance Assessment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OsvComplianceAssessment {
    /// Framework being assessed
    pub framework: ComplianceFramework,
    /// Overall compliance score (0-100)
    pub compliance_score: f64,
    /// Risk level
    pub risk_level: RiskLevel,
    /// Compliance findings
    pub findings: Vec<ComplianceFinding>,
    /// Remediation recommendations
    pub recommendations: Vec<String>,
    /// Assessment metadata
    pub metadata: HashMap<String, String>,
}

impl Default for OsvComplianceValidator {
    fn default() -> Self {
        Self::new()
    }
        let compliance_score = compliance_score.max(0.0);
        let risk_level = self.calculate_risk_level(compliance_score);

        // Audit log compliance check
        if let Some(audit_logger) = get_audit_logger() {
            audit_logger.log_compliance_check("OWASP", compliance_score, findings.len(), "system");
        }
}

impl OsvComplianceValidator {
    /// Create new OSV compliance validator
    pub fn new() -> Self {
        let mut validator = Self {
            owasp_rules: HashMap::new(),
            nist_rules: HashMap::new(),
            iso27001_rules: HashMap::new(),
        };

        validator.initialize_owasp_rules();
        validator.initialize_nist_rules();
        validator.initialize_iso27001_rules();

        validator
    }

    /// Initialize OWASP compliance rules
    fn initialize_owasp_rules(&mut self) {
        // OWASP Top 10 2021 mappings for common vulnerabilities
        let rules = vec![
            OwaspComplianceRule {
                id: "owasp-a01".to_string(),
                description: "Broken Access Control".to_string(),
                owasp_categories: vec!["A01:2021".to_string()],
                severity_mapping: HashMap::from([
                    ("critical".to_string(), Severity::Critical),
                    ("high".to_string(), Severity::High),
                    ("medium".to_string(), Severity::Medium),
                    ("low".to_string(), Severity::Low),
                ]),
                risk_multiplier: 1.2,
            },
            OwaspComplianceRule {
                id: "owasp-a02".to_string(),
                description: "Cryptographic Failures".to_string(),
                owasp_categories: vec!["A02:2021".to_string()],
                severity_mapping: HashMap::from([
                    ("critical".to_string(), Severity::Critical),
                    ("high".to_string(), Severity::High),
                    ("medium".to_string(), Severity::Medium),
                    ("low".to_string(), Severity::Low),
                ]),
                risk_multiplier: 1.3,
            },
            OwaspComplianceRule {
                id: "owasp-a03".to_string(),
                description: "Injection".to_string(),
                owasp_categories: vec!["A03:2021".to_string()],
                severity_mapping: HashMap::from([
                    ("critical".to_string(), Severity::Critical),
                    ("high".to_string(), Severity::High),
                    ("medium".to_string(), Severity::Medium),
                    ("low".to_string(), Severity::Low),
                ]),
                risk_multiplier: 1.4,
            },
        ];

        for rule in rules {
            self.owasp_rules.insert(rule.id.clone(), rule);
        }
    }

    /// Initialize NIST compliance rules
    fn initialize_nist_rules(&mut self) {
        let rules = vec![
            NistComplianceRule {
                id: "nist-si-2".to_string(),
                description: "Flaw Remediation".to_string(),
                nist_controls: vec!["SI-2".to_string()],
                cvss_thresholds: CvssThresholds {
                    critical: 9.0,
                    high: 7.0,
                    medium: 4.0,
                    low: 0.1,
                },
                requirements: vec![
                    "Implement flaw remediation process".to_string(),
                    "Regular vulnerability scanning".to_string(),
                    "Patch management procedures".to_string(),
                ],
            },
            NistComplianceRule {
                id: "nist-ra-5".to_string(),
                description: "Vulnerability Monitoring and Scanning".to_string(),
                nist_controls: vec!["RA-5".to_string()],
                cvss_thresholds: CvssThresholds {
                    critical: 9.0,
                    high: 7.0,
                    medium: 4.0,
                    low: 0.1,
                },
                requirements: vec![
                    "Regular vulnerability assessments".to_string(),
                    "Automated scanning tools".to_string(),
                    "Risk-based prioritization".to_string(),
                ],
            },
        ];

        for rule in rules {
            self.nist_rules.insert(rule.id.clone(), rule);
        }
    }

    /// Initialize ISO 27001 compliance rules
    fn initialize_iso27001_rules(&mut self) {
        let rules = vec![
            Iso27001ComplianceRule {
                id: "iso-12-6-1".to_string(),
                description: "Information Security Incident Management".to_string(),
                iso_controls: vec!["A.12.6.1".to_string()],
                risk_criteria: RiskCriteria {
                    impact_threshold: 7.0,
                    likelihood_threshold: 6.0,
                    risk_acceptance: 3.0,
                },
                remediation_priority: Priority::High,
            },
            Iso27001ComplianceRule {
                id: "iso-12-6-2".to_string(),
                description: "Vulnerability Management".to_string(),
                iso_controls: vec!["A.12.6.2".to_string()],
                risk_criteria: RiskCriteria {
                    impact_threshold: 8.0,
                    likelihood_threshold: 7.0,
                    risk_acceptance: 2.0,
                },
                remediation_priority: Priority::Critical,
            },
        ];

        for rule in rules {
            self.iso27001_rules.insert(rule.id.clone(), rule);
        }
    }

    /// Assess OSV scan results against OWASP standards
    pub fn assess_owasp_compliance(&self, scan_result: &OsvScanResult) -> Result<OsvComplianceAssessment> {
        let mut findings = Vec::new();
        let mut total_score = 0.0;
        let mut vulnerability_count = 0;

        for vulnerability in &scan_result.vulnerabilities {
            vulnerability_count += 1;

            // Map vulnerability to OWASP categories
            let owasp_findings = self.map_vulnerability_to_owasp(vulnerability);
            findings.extend(owasp_findings);

            // Calculate compliance score penalty
            if let Some(cvss_score) = vulnerability.cvss_score {
                total_score += cvss_score * 2.0; // Base penalty
            }
        }

        // Calculate overall compliance score (inverse of risk score)
        let compliance_score = if vulnerability_count > 0 {
            (100.0 - (total_score / vulnerability_count as f64)).max(0.0).min(100.0)
        } else {
            100.0
        };

        let risk_level = self.calculate_risk_level(compliance_score);

        Ok(OsvComplianceAssessment {
            framework: ComplianceFramework::NistCsf, // Using NIST as proxy for OWASP
            compliance_score,
            risk_level,
            findings,
            recommendations: self.generate_owasp_recommendations(&findings),
            metadata: HashMap::from([
                ("vulnerability_count".to_string(), vulnerability_count.to_string()),
                ("assessment_type".to_string(), "OWASP".to_string()),
            ]),
        })
    }

    /// Assess OSV scan results against NIST standards
    pub fn assess_nist_compliance(&self, scan_result: &OsvScanResult) -> Result<OsvComplianceAssessment> {
        let mut findings = Vec::new();
        let mut compliance_score = 100.0;

        for vulnerability in &scan_result.vulnerabilities {
            let nist_findings = self.map_vulnerability_to_nist(vulnerability);
            findings.extend(nist_findings);

            // Apply NIST compliance penalties
            if let Some(cvss_score) = vulnerability.cvss_score {
                if cvss_score >= 9.0 {
                    compliance_score -= 15.0;
                } else if cvss_score >= 7.0 {
                    compliance_score -= 10.0;
                } else if cvss_score >= 4.0 {
                    compliance_score -= 5.0;
                }
            }
        }

        let compliance_score = compliance_score.max(0.0);
        let risk_level = self.calculate_risk_level(compliance_score);

        Ok(OsvComplianceAssessment {
            framework: ComplianceFramework::NistCsf,
            compliance_score,
            risk_level,
            findings,
            recommendations: self.generate_nist_recommendations(&findings),
            metadata: HashMap::from([
                ("vulnerability_count".to_string(), scan_result.vulnerabilities.len().to_string()),
                ("assessment_type".to_string(), "NIST".to_string()),
            ]),
        })
    }

    /// Assess OSV scan results against ISO 27001 standards
    pub fn assess_iso27001_compliance(&self, scan_result: &OsvScanResult) -> Result<OsvComplianceAssessment> {
        let mut findings = Vec::new();
        let mut compliance_score = 100.0;
        let mut critical_findings = 0;

        for vulnerability in &scan_result.vulnerabilities {
            let iso_findings = self.map_vulnerability_to_iso27001(vulnerability);
            findings.extend(iso_findings);

            // Count critical findings for ISO 27001
            if let Some(cvss_score) = vulnerability.cvss_score {
                if cvss_score >= 9.0 {
                    critical_findings += 1;
                    compliance_score -= 20.0;
                } else if cvss_score >= 7.0 {
                    compliance_score -= 10.0;
                }
            }
        }

        let compliance_score = compliance_score.max(0.0);
        let risk_level = if critical_findings > 0 {
            RiskLevel::High
        } else {
            self.calculate_risk_level(compliance_score)
        };

        Ok(OsvComplianceAssessment {
            framework: ComplianceFramework::Iso27001,
            compliance_score,
            risk_level,
            findings,
            recommendations: self.generate_iso27001_recommendations(&findings),
            metadata: HashMap::from([
                ("vulnerability_count".to_string(), scan_result.vulnerabilities.len().to_string()),
                ("critical_findings".to_string(), critical_findings.to_string()),
                ("assessment_type".to_string(), "ISO27001".to_string()),
            ]),
        })
    }

    /// Map vulnerability to OWASP compliance findings
    fn map_vulnerability_to_owasp(&self, vulnerability: &OsvVulnerability) -> Vec<ComplianceFinding> {
        let mut findings = Vec::new();

        // Map based on vulnerability summary keywords
        let summary_lower = vulnerability.summary.to_lowercase();

        if summary_lower.contains("injection") || summary_lower.contains("sql") || summary_lower.contains("xss") {
            if let Some(rule) = self.owasp_rules.get("owasp-a03") {
                findings.push(self.create_compliance_finding(vulnerability, rule, "A03:2021"));
            }
        }

        if summary_lower.contains("crypto") || summary_lower.contains("ssl") || summary_lower.contains("tls") {
            if let Some(rule) = self.owasp_rules.get("owasp-a02") {
                findings.push(self.create_compliance_finding(vulnerability, rule, "A02:2021"));
            }
        }

        if summary_lower.contains("access") || summary_lower.contains("auth") || summary_lower.contains("permission") {
            if let Some(rule) = self.owasp_rules.get("owasp-a01") {
                findings.push(self.create_compliance_finding(vulnerability, rule, "A01:2021"));
            }
        }

        findings
    }

    /// Map vulnerability to NIST compliance findings
    fn map_vulnerability_to_nist(&self, vulnerability: &OsvVulnerability) -> Vec<ComplianceFinding> {
        let mut findings = Vec::new();

        // Apply NIST vulnerability management rules
        if let Some(rule) = self.nist_rules.get("nist-si-2") {
            findings.push(self.create_nist_finding(vulnerability, rule));
        }

        if let Some(rule) = self.nist_rules.get("nist-ra-5") {
            findings.push(self.create_nist_finding(vulnerability, rule));
        }

        findings
    }

    /// Map vulnerability to ISO 27001 compliance findings
    fn map_vulnerability_to_iso27001(&self, vulnerability: &OsvVulnerability) -> Vec<ComplianceFinding> {
        let mut findings = Vec::new();

        // Apply ISO 27001 vulnerability management controls
        if let Some(rule) = self.iso27001_rules.get("iso-12-6-1") {
            findings.push(self.create_iso27001_finding(vulnerability, rule));
        }

        if let Some(rule) = self.iso27001_rules.get("iso-12-6-2") {
            findings.push(self.create_iso27001_finding(vulnerability, rule));
        }

        findings
    }

    /// Create compliance finding from OWASP rule
    fn create_compliance_finding(&self, vulnerability: &OsvVulnerability, rule: &OwaspComplianceRule, control_ref: &str) -> ComplianceFinding {
        let severity = vulnerability.severity.as_ref()
            .and_then(|s| rule.severity_mapping.get(s))
            .cloned()
            .unwrap_or(Severity::Medium);

        ComplianceFinding {
            id: format!("{}-{}", rule.id, vulnerability.id),
            title: format!("OWASP {}: {}", control_ref, vulnerability.summary),
            description: format!("{} - {}", rule.description, vulnerability.details.as_deref().unwrap_or("")),
            severity,
            control_reference: control_ref.to_string(),
            affected_components: vec![vulnerability.package.name.clone()],
            evidence: vec![
                format!("CVSS Score: {:?}", vulnerability.cvss_score),
                format!("Affected Versions: {:?}", vulnerability.affected_versions),
                format!("Fixed Versions: {:?}", vulnerability.fixed_versions),
            ],
            status: crate::compliance::FindingStatus::Open,
        }
    }

    /// Create NIST compliance finding
    fn create_nist_finding(&self, vulnerability: &OsvVulnerability, rule: &NistComplianceRule) -> ComplianceFinding {
        let severity = if let Some(cvss) = vulnerability.cvss_score {
            if cvss >= rule.cvss_thresholds.critical {
                Severity::Critical
            } else if cvss >= rule.cvss_thresholds.high {
                Severity::High
            } else if cvss >= rule.cvss_thresholds.medium {
                Severity::Medium
            } else {
                Severity::Low
            }
        } else {
            Severity::Medium
        };

        ComplianceFinding {
            id: format!("{}-{}", rule.id, vulnerability.id),
            title: format!("NIST {}: {}", rule.nist_controls.join(", "), vulnerability.summary),
            description: format!("{} - {}", rule.description, vulnerability.details.as_deref().unwrap_or("")),
            severity,
            control_reference: rule.nist_controls.join(", "),
            affected_components: vec![vulnerability.package.name.clone()],
            evidence: vec![
                format!("CVSS Score: {:?}", vulnerability.cvss_score),
                format!("Requirements: {:?}", rule.requirements),
            ],
            status: crate::compliance::FindingStatus::Open,
        }
    }

    /// Create ISO 27001 compliance finding
    fn create_iso27001_finding(&self, vulnerability: &OsvVulnerability, rule: &Iso27001ComplianceRule) -> ComplianceFinding {
        let severity = if let Some(cvss) = vulnerability.cvss_score {
            if cvss >= rule.risk_criteria.impact_threshold {
                Severity::Critical
            } else if cvss >= rule.risk_criteria.likelihood_threshold {
                Severity::High
            } else {
                Severity::Medium
            }
        } else {
            Severity::Medium
        };

        ComplianceFinding {
            id: format!("{}-{}", rule.id, vulnerability.id),
            title: format!("ISO 27001 {}: {}", rule.iso_controls.join(", "), vulnerability.summary),
            description: format!("{} - {}", rule.description, vulnerability.details.as_deref().unwrap_or("")),
            severity,
            control_reference: rule.iso_controls.join(", "),
            affected_components: vec![vulnerability.package.name.clone()],
            evidence: vec![
                format!("CVSS Score: {:?}", vulnerability.cvss_score),
                format!("Risk Criteria: Impact ≥ {}, Likelihood ≥ {}", rule.risk_criteria.impact_threshold, rule.risk_criteria.likelihood_threshold),
            ],
            status: crate::compliance::FindingStatus::Open,
        }
    }

    /// Calculate risk level from compliance score
    fn calculate_risk_level(&self, compliance_score: f64) -> RiskLevel {
        if compliance_score >= 80.0 {
            RiskLevel::Low
        } else if compliance_score >= 60.0 {
            RiskLevel::Medium
        } else if compliance_score >= 40.0 {
            RiskLevel::High
        } else {
            RiskLevel::VeryHigh
        }
    }

    /// Generate OWASP recommendations
    fn generate_owasp_recommendations(&self, findings: &[ComplianceFinding]) -> Vec<String> {
        let mut recommendations = Vec::new();

        if findings.iter().any(|f| f.control_reference.contains("A03")) {
            recommendations.push("Implement input validation and parameterized queries".to_string());
            recommendations.push("Use prepared statements for database operations".to_string());
        }

        if findings.iter().any(|f| f.control_reference.contains("A02")) {
            recommendations.push("Use strong encryption algorithms and proper key management".to_string());
            recommendations.push("Implement secure TLS configuration".to_string());
        }

        if findings.iter().any(|f| f.control_reference.contains("A01")) {
            recommendations.push("Implement proper access controls and authorization checks".to_string());
            recommendations.push("Regular review of user permissions and roles".to_string());
        }

        recommendations
    }

    /// Generate NIST recommendations
    fn generate_nist_recommendations(&self, findings: &[ComplianceFinding]) -> Vec<String> {
        vec![
            "Implement automated vulnerability scanning in CI/CD pipeline".to_string(),
            "Establish vulnerability remediation SLAs based on CVSS scores".to_string(),
            "Regular security assessments and penetration testing".to_string(),
            "Maintain comprehensive vulnerability database and tracking system".to_string(),
        ]
    }

    /// Generate ISO 27001 recommendations
    fn generate_iso27001_recommendations(&self, findings: &[ComplianceFinding]) -> Vec<String> {
        vec![
            "Establish information security incident management process".to_string(),
            "Implement systematic vulnerability management procedures".to_string(),
            "Regular risk assessments and security controls review".to_string(),
            "Document security policies and procedures for vulnerability handling".to_string(),
        ]
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::scanners::OsvPackage;

    #[test]
    fn test_osv_compliance_validator_creation() {
        let validator = OsvComplianceValidator::new();
        assert!(!validator.owasp_rules.is_empty());
        assert!(!validator.nist_rules.is_empty());
        assert!(!validator.iso27001_rules.is_empty());
    }

    #[test]
    fn test_risk_level_calculation() {
        let validator = OsvComplianceValidator::new();

        assert_eq!(validator.calculate_risk_level(85.0), RiskLevel::Low);
        assert_eq!(validator.calculate_risk_level(65.0), RiskLevel::Medium);
        assert_eq!(validator.calculate_risk_level(45.0), RiskLevel::High);
        assert_eq!(validator.calculate_risk_level(25.0), RiskLevel::VeryHigh);
    }

    #[test]
    fn test_compliance_assessment_creation() {
        let validator = OsvComplianceValidator::new();
        let scan_result = OsvScanResult {
            scanned_at: chrono::Utc::now(),
            components_scanned: 1,
            vulnerabilities: vec![],
            scan_duration: std::time::Duration::from_secs(1),
            api_calls: 1,
            cache_hits: 0,
            errors: vec![],
        };

        let assessment = validator.assess_owasp_compliance(&scan_result).unwrap();
        assert_eq!(assessment.compliance_score, 100.0);
        assert_eq!(assessment.risk_level, RiskLevel::Low);
        assert_eq!(assessment.framework, ComplianceFramework::NistCsf);
    }
}