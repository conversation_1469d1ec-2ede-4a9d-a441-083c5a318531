//! OSV Report Generator Module
//!
//! This module provides specialized report generation for OSV (Open Source Vulnerabilities)
//! scanner results, supporting SARIF, JSON, and HTML formats for CI/CD integration
//! and compliance reporting.

use crate::{
    compliance::{ComplianceConfig, OutputFormat},
    config::Config,
    error::{InfinitumError, Result},
    scanners::{OsvScanResult, OsvVulnerability, OsvPackage},
};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, path::Path};
use tracing::{info, instrument};
use uuid::Uuid;

/// OSV Report Generator
pub struct OsvReportGenerator {
    config: ComplianceConfig,
}

/// OSV Report Request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OsvReportRequest {
    /// Unique request identifier
    pub id: Uuid,
    /// OSV scan results
    pub scan_results: Vec<OsvScanResult>,
    /// Report configuration
    pub config: OsvReportConfig,
    /// Additional metadata
    pub metadata: HashMap<String, String>,
}

/// OSV Report Configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OsvReportConfig {
    /// Report title
    pub title: String,
    /// Organization name
    pub organization: String,
    /// Report author
    pub author: String,
    /// Output formats
    pub output_formats: Vec<OutputFormat>,
    /// Include executive summary
    pub include_executive_summary: bool,
    /// Include detailed findings
    pub include_detailed_findings: bool,
    /// Include remediation recommendations
    pub include_recommendations: bool,
    /// Minimum severity threshold
    pub severity_threshold: String,
    /// Include CVSS scores
    pub include_cvss_scores: bool,
    /// Include affected packages
    pub include_affected_packages: bool,
    /// Include remediation information
    pub include_remediation: bool,
}

/// Generated OSV Report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OsvReport {
    /// Report request
    pub request: OsvReportRequest,
    /// Report generation timestamp
    pub generated_at: chrono::DateTime<chrono::Utc>,
    /// Report summary
    pub summary: OsvReportSummary,
    /// Vulnerabilities found
    pub vulnerabilities: Vec<OsvVulnerability>,
    /// Generated file paths
    pub output_files: HashMap<OutputFormat, String>,
    /// Report metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// OSV Report Summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OsvReportSummary {
    /// Total vulnerabilities found
    pub total_vulnerabilities: usize,
    /// Vulnerabilities by severity
    pub by_severity: HashMap<String, usize>,
    /// Components scanned
    pub components_scanned: usize,
    /// Scan duration
    pub scan_duration: std::time::Duration,
    /// API calls made
    pub api_calls: usize,
    /// Cache hits
    pub cache_hits: usize,
    /// High severity vulnerabilities
    pub high_severity_count: usize,
    /// Critical severity vulnerabilities
    pub critical_severity_count: usize,
}

/// SARIF format structures for OSV vulnerabilities
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SarifReport {
    #[serde(rename = "$schema")]
    pub schema: String,
    pub version: String,
    pub runs: Vec<SarifRun>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SarifRun {
    pub tool: SarifTool,
    pub results: Vec<SarifResult>,
    pub invocations: Vec<SarifInvocation>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SarifTool {
    pub driver: SarifToolDriver,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SarifToolDriver {
    pub name: String,
    pub version: String,
    pub information_uri: String,
    pub rules: Vec<SarifRule>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SarifRule {
    pub id: String,
    pub name: String,
    pub short_description: SarifMessage,
    pub full_description: Option<SarifMessage>,
    pub help_uri: Option<String>,
    pub properties: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SarifMessage {
    pub text: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SarifResult {
    pub rule_id: String,
    pub level: String,
    pub message: SarifMessage,
    pub locations: Vec<SarifLocation>,
    pub properties: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SarifLocation {
    pub physical_location: SarifPhysicalLocation,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SarifPhysicalLocation {
    pub artifact_location: SarifArtifactLocation,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SarifArtifactLocation {
    pub uri: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SarifInvocation {
    pub execution_successful: bool,
    pub start_time_utc: String,
    pub end_time_utc: String,
}

impl Default for OsvReportConfig {
    fn default() -> Self {
        Self {
            title: "OSV Vulnerability Report".to_string(),
            organization: "Organization".to_string(),
            author: "Infinitium Signal".to_string(),
            output_formats: vec![OutputFormat::Json, OutputFormat::Html],
            include_executive_summary: true,
            include_detailed_findings: true,
            include_recommendations: true,
            severity_threshold: "low".to_string(),
            include_cvss_scores: true,
            include_affected_packages: true,
            include_remediation: true,
        }
    }
}

impl OsvReportGenerator {
    /// Create new OSV report generator
    pub fn new(config: &ComplianceConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }

    /// Generate OSV report
    #[instrument(skip(self, request))]
    pub async fn generate_report(&self, request: OsvReportRequest) -> Result<OsvReport> {
        info!("Generating OSV vulnerability report");

        let mut report = OsvReport {
            request: request.clone(),
            generated_at: chrono::Utc::now(),
            summary: self.calculate_summary(&request.scan_results),
            vulnerabilities: self.aggregate_vulnerabilities(&request.scan_results),
            output_files: HashMap::new(),
            metadata: HashMap::new(),
        };

        // Generate output files
        for format in &request.config.output_formats {
            match format {
                OutputFormat::Json => {
                    let json_path = self.generate_json_report(&report).await?;
                    report.output_files.insert(OutputFormat::Json, json_path);
                }
                OutputFormat::Html => {
                    let html_path = self.generate_html_report(&report).await?;
                    report.output_files.insert(OutputFormat::Html, html_path);
                }
                OutputFormat::Xml => {
                    let sarif_path = self.generate_sarif_report(&report).await?;
                    report.output_files.insert(OutputFormat::Xml, sarif_path);
                }
                _ => {
                    // Skip unsupported formats for OSV reports
                }
            }
        }

        info!("OSV report generated successfully");
        Ok(report)
    }

    /// Calculate report summary
    fn calculate_summary(&self, scan_results: &[OsvScanResult]) -> OsvReportSummary {
        let mut total_vulnerabilities = 0;
        let mut by_severity = HashMap::new();
        let mut components_scanned = 0;
        let mut total_scan_duration = std::time::Duration::from_secs(0);
        let mut total_api_calls = 0;
        let mut total_cache_hits = 0;
        let mut high_severity_count = 0;
        let mut critical_severity_count = 0;

        for result in scan_results {
            total_vulnerabilities += result.vulnerabilities.len();
            components_scanned += result.components_scanned;
            total_scan_duration += result.scan_duration;
            total_api_calls += result.api_calls;
            total_cache_hits += result.cache_hits;

            for vuln in &result.vulnerabilities {
                let severity = vuln.severity.as_deref().unwrap_or("unknown");
                *by_severity.entry(severity.to_string()).or_insert(0) += 1;

                if severity.eq_ignore_ascii_case("high") {
                    high_severity_count += 1;
                } else if severity.eq_ignore_ascii_case("critical") {
                    critical_severity_count += 1;
                }
            }
        }

        OsvReportSummary {
            total_vulnerabilities,
            by_severity,
            components_scanned,
            scan_duration: total_scan_duration,
            api_calls: total_api_calls,
            cache_hits: total_cache_hits,
            high_severity_count,
            critical_severity_count,
        }
    }

    /// Aggregate vulnerabilities from multiple scan results
    fn aggregate_vulnerabilities(&self, scan_results: &[OsvScanResult]) -> Vec<OsvVulnerability> {
        let mut all_vulnerabilities = Vec::new();

        for result in scan_results {
            all_vulnerabilities.extend(result.vulnerabilities.clone());
        }

        // Remove duplicates based on vulnerability ID
        let mut seen = HashMap::new();
        all_vulnerabilities.retain(|vuln| {
            seen.insert(vuln.id.clone(), true).is_none()
        });

        all_vulnerabilities
    }

    /// Generate JSON report
    async fn generate_json_report(&self, report: &OsvReport) -> Result<String> {
        let output_dir = &self.config.report_output_dir;
        let filename = format!("osv_report_{}.json", report.request.id);
        let file_path = Path::new(output_dir).join(&filename);

        tokio::fs::create_dir_all(output_dir).await?;
        let json_content = serde_json::to_string_pretty(report)?;
        tokio::fs::write(&file_path, json_content).await?;

        Ok(file_path.to_string_lossy().to_string())
    }

    /// Generate HTML report
    async fn generate_html_report(&self, report: &OsvReport) -> Result<String> {
        let output_dir = &self.config.report_output_dir;
        let filename = format!("osv_report_{}.html", report.request.id);
        let file_path = Path::new(output_dir).join(&filename);

        let html_content = self.generate_html_content(report).await?;
        tokio::fs::create_dir_all(output_dir).await?;
        tokio::fs::write(&file_path, html_content).await?;

        Ok(file_path.to_string_lossy().to_string())
    }

    /// Generate SARIF report
    async fn generate_sarif_report(&self, report: &OsvReport) -> Result<String> {
        let output_dir = &self.config.report_output_dir;
        let filename = format!("osv_report_{}.sarif", report.request.id);
        let file_path = Path::new(output_dir).join(&filename);

        let sarif_report = self.convert_to_sarif(report);
        let sarif_content = serde_json::to_string_pretty(&sarif_report)?;
        tokio::fs::create_dir_all(output_dir).await?;
        tokio::fs::write(&file_path, sarif_content).await?;

        Ok(file_path.to_string_lossy().to_string())
    }

    /// Generate HTML content
    async fn generate_html_content(&self, report: &OsvReport) -> Result<String> {
        let css = self.generate_css().await?;

        let html = format!(
            r#"<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{}</title>
    <style>{}</style>
</head>
<body>
    <div class="header">
        <h1>{}</h1>
        <div class="metadata">
            <p><strong>Generated:</strong> {}</p>
            <p><strong>Organization:</strong> {}</p>
            <p><strong>Author:</strong> {}</p>
        </div>
    </div>

    <div class="summary">
        <h2>Executive Summary</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <h3>Total Vulnerabilities</h3>
                <div class="stat-value">{}</div>
            </div>
            <div class="stat-card">
                <h3>Components Scanned</h3>
                <div class="stat-value">{}</div>
            </div>
            <div class="stat-card">
                <h3>High Severity</h3>
                <div class="stat-value">{}</div>
            </div>
            <div class="stat-card">
                <h3>Critical Severity</h3>
                <div class="stat-value">{}</div>
            </div>
        </div>
        {}
    </div>

    <div class="vulnerabilities">
        <h2>Vulnerabilities</h2>
        {}
    </div>

    <div class="footer">
        <p>Generated by Infinitium Signal OSV Scanner</p>
        <p>Report ID: {}</p>
    </div>
</body>
</html>"#,
            report.request.config.title,
            css,
            report.request.config.title,
            report.generated_at.format("%Y-%m-%d %H:%M:%S UTC"),
            report.request.config.organization,
            report.request.config.author,
            report.summary.total_vulnerabilities,
            report.summary.components_scanned,
            report.summary.high_severity_count,
            report.summary.critical_severity_count,
            self.generate_severity_chart(&report.summary),
            self.generate_vulnerability_table(&report.vulnerabilities),
            report.request.id
        );

        Ok(html)
    }

    /// Generate CSS for HTML report
    async fn generate_css(&self) -> Result<String> {
        Ok(r#"
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 20px;
    background: #f5f5f5;
    color: #333;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.header h1 {
    margin: 0 0 15px 0;
    font-size: 2.5em;
}

.metadata {
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 8px;
}

.summary {
    background: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid #667eea;
}

.stat-card h3 {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 0.9em;
    text-transform: uppercase;
}

.stat-value {
    font-size: 2.5em;
    font-weight: bold;
    color: #667eea;
}

.vulnerabilities {
    background: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.vulnerability-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.vulnerability-table th,
.vulnerability-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.vulnerability-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.vulnerability-table tr:hover {
    background: #f8f9fa;
}

.severity-critical { color: #dc3545; font-weight: bold; }
.severity-high { color: #fd7e14; font-weight: bold; }
.severity-medium { color: #ffc107; font-weight: bold; }
.severity-low { color: #28a745; font-weight: bold; }
.severity-unknown { color: #6c757d; }

.footer {
    text-align: center;
    padding: 20px;
    color: #666;
    border-top: 1px solid #ddd;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .header h1 {
        font-size: 2em;
    }
}
        "#.to_string())
    }

    /// Generate severity chart HTML
    fn generate_severity_chart(&self, summary: &OsvReportSummary) -> String {
        let mut chart_data = Vec::new();

        for (severity, count) in &summary.by_severity {
            if *count > 0 {
                chart_data.push(format!("{{ label: '{}', value: {} }}", severity, count));
            }
        }

        if chart_data.is_empty() {
            return "<p>No vulnerabilities found to display in chart.</p>".to_string();
        }

        format!(
            r#"<div class="severity-chart">
                <h3>Vulnerabilities by Severity</h3>
                <div id="severity-chart"></div>
                <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
                <script>
                    const ctx = document.getElementById('severity-chart').getContext('2d');
                    new Chart(ctx, {{
                        type: 'pie',
                        data: {{
                            labels: [{}],
                            datasets: [{{
                                data: [{}],
                                backgroundColor: [
                                    '#dc3545', '#fd7e14', '#ffc107', '#28a745', '#6c757d'
                                ]
                            }}]
                        }},
                        options: {{
                            responsive: true,
                            plugins: {{
                                legend: {{
                                    position: 'bottom'
                                }}
                            }}
                        }}
                    }});
                </script>
            </div>"#,
            chart_data.iter().map(|d| format!("'{}'", d.split("', value:").next().unwrap().trim_start_matches("{ label: '"))).collect::<Vec<_>>().join("','"),
            chart_data.iter().map(|d| d.split(", value: ").nth(1).unwrap().trim_end_matches(" }")).collect::<Vec<_>>().join(",")
        )
    }

    /// Generate vulnerability table HTML
    fn generate_vulnerability_table(&self, vulnerabilities: &[OsvVulnerability]) -> String {
        if vulnerabilities.is_empty() {
            return "<p>No vulnerabilities found.</p>".to_string();
        }

        let mut rows = String::new();

        for vuln in vulnerabilities {
            let severity_class = format!("severity-{}", vuln.severity.as_deref().unwrap_or("unknown").to_lowercase());
            let cvss_info = if let Some(score) = vuln.cvss_score {
                format!("{:.1}", score)
            } else {
                "N/A".to_string()
            };

            let affected_versions = if vuln.affected_versions.is_empty() {
                "N/A".to_string()
            } else {
                vuln.affected_versions.join(", ")
            };

            let fixed_versions = if vuln.fixed_versions.is_empty() {
                "N/A".to_string()
            } else {
                vuln.fixed_versions.join(", ")
            };

            rows.push_str(&format!(
                r#"<tr>
                    <td><a href="{}" target="_blank">{}</a></td>
                    <td>{}</td>
                    <td class="{}">{}</td>
                    <td>{}</td>
                    <td>{}</td>
                    <td>{}</td>
                    <td>{}</td>
                </tr>"#,
                vuln.references.first().unwrap_or(&"".to_string()),
                vuln.id,
                vuln.summary,
                severity_class,
                vuln.severity.as_deref().unwrap_or("Unknown"),
                cvss_info,
                vuln.package.name,
                affected_versions,
                fixed_versions
            ));
        }

        format!(
            r#"<table class="vulnerability-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Summary</th>
                        <th>Severity</th>
                        <th>CVSS Score</th>
                        <th>Package</th>
                        <th>Affected Versions</th>
                        <th>Fixed Versions</th>
                    </tr>
                </thead>
                <tbody>
                    {}
                </tbody>
            </table>"#,
            rows
        )
    }

    /// Convert OSV report to SARIF format
    fn convert_to_sarif(&self, report: &OsvReport) -> SarifReport {
        let mut rules = Vec::new();
        let mut results = Vec::new();

        for vuln in &report.vulnerabilities {
            let rule_id = vuln.id.clone();

            // Create rule
            let rule = SarifRule {
                id: rule_id.clone(),
                name: vuln.summary.clone(),
                short_description: SarifMessage {
                    text: vuln.summary.clone(),
                },
                full_description: vuln.details.as_ref().map(|d| SarifMessage {
                    text: d.clone(),
                }),
                help_uri: vuln.references.first().cloned(),
                properties: {
                    let mut props = HashMap::new();
                    if let Some(score) = vuln.cvss_score {
                        props.insert("cvssScore".to_string(), serde_json::json!(score));
                    }
                    if let Some(severity) = &vuln.severity {
                        props.insert("severity".to_string(), serde_json::json!(severity));
                    }
                    props.insert("package".to_string(), serde_json::json!(vuln.package.name));
                    props.insert("ecosystem".to_string(), serde_json::json!(vuln.package.ecosystem));
                    props
                },
            };

            rules.push(rule);

            // Create result
            let result = SarifResult {
                rule_id: rule_id.clone(),
                level: match vuln.severity.as_deref() {
                    Some("critical") => "error",
                    Some("high") => "error",
                    Some("medium") => "warning",
                    Some("low") => "note",
                    _ => "note",
                }.to_string(),
                message: SarifMessage {
                    text: vuln.summary.clone(),
                },
                locations: vec![SarifLocation {
                    physical_location: SarifPhysicalLocation {
                        artifact_location: SarifArtifactLocation {
                            uri: format!("pkg:{}/{}", vuln.package.ecosystem, vuln.package.name),
                        },
                    },
                }],
                properties: {
                    let mut props = HashMap::new();
                    props.insert("affectedVersions".to_string(), serde_json::json!(vuln.affected_versions));
                    props.insert("fixedVersions".to_string(), serde_json::json!(vuln.fixed_versions));
                    props.insert("references".to_string(), serde_json::json!(vuln.references));
                    if let Some(published) = vuln.published {
                        props.insert("published".to_string(), serde_json::json!(published));
                    }
                    if let Some(modified) = vuln.modified {
                        props.insert("modified".to_string(), serde_json::json!(modified));
                    }
                    props
                },
            };

            results.push(result);
        }

        let tool_driver = SarifToolDriver {
            name: "OSV Scanner".to_string(),
            version: env!("CARGO_PKG_VERSION").to_string(),
            information_uri: "https://osv.dev".to_string(),
            rules,
        };

        let invocation = SarifInvocation {
            execution_successful: true,
            start_time_utc: report.generated_at.to_rfc3339(),
            end_time_utc: report.generated_at.to_rfc3339(),
        };

        let run = SarifRun {
            tool: SarifTool {
                driver: tool_driver,
            },
            results,
            invocations: vec![invocation],
        };

        SarifReport {
            schema: "https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json".to_string(),
            version: "2.1.0".to_string(),
            runs: vec![run],
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::scanners::OsvPackage;

    #[test]
    fn test_osv_report_config_default() {
        let config = OsvReportConfig::default();
        assert_eq!(config.title, "OSV Vulnerability Report");
        assert!(config.include_cvss_scores);
        assert!(config.include_affected_packages);
        assert!(config.include_remediation);
    }

    #[test]
    fn test_calculate_summary() {
        let generator = OsvReportGenerator::new(&ComplianceConfig::default());

        let vuln1 = OsvVulnerability {
            id: "OSV-1".to_string(),
            summary: "Test vulnerability 1".to_string(),
            details: None,
            severity: Some("high".to_string()),
            cvss_score: Some(7.5),
            package: OsvPackage {
                name: "test-package".to_string(),
                ecosystem: "npm".to_string(),
                purl: None,
            },
            affected_versions: vec!["1.0.0".to_string()],
            fixed_versions: vec!["1.0.1".to_string()],
            references: vec!["https://osv.dev/OSV-1".to_string()],
            published: None,
            modified: None,
        };

        let vuln2 = OsvVulnerability {
            id: "OSV-2".to_string(),
            summary: "Test vulnerability 2".to_string(),
            details: None,
            severity: Some("critical".to_string()),
            cvss_score: Some(9.1),
            package: OsvPackage {
                name: "another-package".to_string(),
                ecosystem: "npm".to_string(),
                purl: None,
            },
            affected_versions: vec!["2.0.0".to_string()],
            fixed_versions: vec!["2.0.2".to_string()],
            references: vec!["https://osv.dev/OSV-2".to_string()],
            published: None,
            modified: None,
        };

        let scan_result = OsvScanResult {
            scanned_at: chrono::Utc::now(),
            components_scanned: 2,
            vulnerabilities: vec![vuln1, vuln2],
            scan_duration: std::time::Duration::from_secs(10),
            api_calls: 2,
            cache_hits: 0,
            errors: vec![],
        };

        let summary = generator.calculate_summary(&[scan_result]);

        assert_eq!(summary.total_vulnerabilities, 2);
        assert_eq!(summary.components_scanned, 2);
        assert_eq!(summary.high_severity_count, 1);
        assert_eq!(summary.critical_severity_count, 1);
        assert_eq!(summary.api_calls, 2);
    }

    #[test]
    fn test_aggregate_vulnerabilities() {
        let generator = OsvReportGenerator::new(&ComplianceConfig::default());

        let vuln1 = OsvVulnerability {
            id: "OSV-1".to_string(),
            summary: "Test vulnerability 1".to_string(),
            details: None,
            severity: Some("high".to_string()),
            cvss_score: Some(7.5),
            package: OsvPackage {
                name: "test-package".to_string(),
                ecosystem: "npm".to_string(),
                purl: None,
            },
            affected_versions: vec!["1.0.0".to_string()],
            fixed_versions: vec!["1.0.1".to_string()],
            references: vec!["https://osv.dev/OSV-1".to_string()],
            published: None,
            modified: None,
        };

        let scan_result1 = OsvScanResult {
            scanned_at: chrono::Utc::now(),
            components_scanned: 1,
            vulnerabilities: vec![vuln1.clone()],
            scan_duration: std::time::Duration::from_secs(5),
            api_calls: 1,
            cache_hits: 0,
            errors: vec![],
        };

        let scan_result2 = OsvScanResult {
            scanned_at: chrono::Utc::now(),
            components_scanned: 1,
            vulnerabilities: vec![vuln1], // Duplicate
            scan_duration: std::time::Duration::from_secs(5),
            api_calls: 1,
            cache_hits: 0,
            errors: vec![],
        };

        let aggregated = generator.aggregate_vulnerabilities(&[scan_result1, scan_result2]);

        // Should only have one vulnerability (duplicates removed)
        assert_eq!(aggregated.len(), 1);
        assert_eq!(aggregated[0].id, "OSV-1");
    }
}