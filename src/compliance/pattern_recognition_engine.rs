//! # Pattern Recognition Engine
//!
//! This module provides advanced pattern recognition capabilities for license compliance,
//! including similarity matching, anomaly detection, clustering for license categorization,
//! and temporal pattern analysis.

use crate::{
    compliance::ml_feature_extractor::{ExtractedFeatures, MLFeatureExtractor},
    error::{InfinitumError, Result},
};
use serde::{Deserialize, Serialize};
use std::{
    collections::{HashMap, HashSet},
    sync::Arc,
};
use tokio::sync::RwLock;
use tracing::{debug, info, instrument};
use ndarray::{Array2, Array1};
use chrono::{DateTime, Utc};

/// Pattern recognition configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatternRecognitionConfig {
    /// Similarity threshold for matching
    pub similarity_threshold: f64,
    /// Maximum number of similar patterns to return
    pub max_similar_patterns: usize,
    /// Enable anomaly detection
    pub enable_anomaly_detection: bool,
    /// Anomaly detection threshold
    pub anomaly_threshold: f64,
    /// Enable clustering
    pub enable_clustering: bool,
    /// Number of clusters for categorization
    pub num_clusters: usize,
    /// Enable temporal analysis
    pub enable_temporal_analysis: bool,
    /// Temporal window size in days
    pub temporal_window_days: i64,
}

/// Similarity match result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SimilarityMatch {
    /// Target pattern identifier
    pub target_id: String,
    /// Similarity score (0.0 to 1.0)
    pub similarity_score: f64,
    /// Matching features
    pub matching_features: Vec<String>,
    /// Confidence in the match
    pub confidence: f64,
    /// Match metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Anomaly detection result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnomalyResult {
    /// Pattern identifier
    pub pattern_id: String,
    /// Anomaly score (higher = more anomalous)
    pub anomaly_score: f64,
    /// Is this pattern anomalous
    pub is_anomalous: bool,
    /// Anomaly features
    pub anomaly_features: Vec<String>,
    /// Detection confidence
    pub confidence: f64,
    /// Anomaly type
    pub anomaly_type: String,
}

/// Clustering result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClusteringResult {
    /// Pattern identifier
    pub pattern_id: String,
    /// Cluster identifier
    pub cluster_id: usize,
    /// Cluster name/category
    pub cluster_name: String,
    /// Distance to cluster center
    pub distance_to_center: f64,
    /// Cluster confidence
    pub confidence: f64,
    /// Cluster characteristics
    pub characteristics: HashMap<String, f64>,
}

/// Temporal pattern analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemporalPattern {
    /// Pattern identifier
    pub pattern_id: String,
    /// Time series data points
    pub time_series: Vec<(DateTime<Utc>, f64)>,
    /// Trend analysis
    pub trend: TrendAnalysis,
    /// Seasonal patterns
    pub seasonality: Option<SeasonalPattern>,
    /// Pattern frequency
    pub frequency: f64,
    /// Pattern strength
    pub strength: f64,
}

/// Trend analysis result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrendAnalysis {
    /// Trend direction
    pub direction: TrendDirection,
    /// Trend slope
    pub slope: f64,
    /// Trend strength (0.0 to 1.0)
    pub strength: f64,
    /// R-squared value
    pub r_squared: f64,
}

/// Seasonal pattern
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SeasonalPattern {
    /// Season length in days
    pub period_days: i64,
    /// Seasonal strength
    pub strength: f64,
    /// Peak times
    pub peaks: Vec<DateTime<Utc>>,
    /// Valley times
    pub valleys: Vec<DateTime<Utc>>,
}

/// Trend direction
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum TrendDirection {
    /// Increasing trend
    Increasing,
    /// Decreasing trend
    Decreasing,
    /// No significant trend
    Stable,
}

/// Pattern data point
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatternData {
    /// Pattern identifier
    pub id: String,
    /// Pattern text
    pub text: String,
    /// Feature vector
    pub features: Vec<f64>,
    /// Pattern category
    pub category: String,
    /// Timestamp
    pub timestamp: DateTime<Utc>,
    /// Metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Pattern Recognition Engine
pub struct PatternRecognitionEngine {
    config: PatternRecognitionConfig,
    feature_extractor: Arc<MLFeatureExtractor>,
    pattern_database: Arc<RwLock<HashMap<String, PatternData>>>,
    cluster_centers: Arc<RwLock<HashMap<usize, Vec<f64>>>>,
    temporal_patterns: Arc<RwLock<HashMap<String, Vec<(DateTime<Utc>, f64)>>>>,
}

impl PatternRecognitionEngine {
    /// Create new Pattern Recognition Engine
    pub fn new(config: PatternRecognitionConfig, feature_extractor: Arc<MLFeatureExtractor>) -> Self {
        Self {
            config,
            feature_extractor,
            pattern_database: Arc::new(RwLock::new(HashMap::new())),
            cluster_centers: Arc::new(RwLock::new(HashMap::new())),
            temporal_patterns: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Add pattern to the database
    #[instrument(skip(self, text), fields(pattern_id = %pattern_id, text_len = text.len()))]
    pub async fn add_pattern(&self, pattern_id: String, text: &str, category: String) -> Result<()> {
        debug!("Adding pattern to database: {}", pattern_id);

        // Extract features
        let features = self.feature_extractor.extract_features(text, Some(&category)).await?;
        let feature_vector = features.to_feature_vector();

        let pattern_data = PatternData {
            id: pattern_id.clone(),
            text: text.to_string(),
            features: feature_vector,
            category,
            timestamp: Utc::now(),
            metadata: HashMap::from([
                ("feature_count".to_string(), serde_json::json!(features.total_feature_count())),
                ("text_length".to_string(), serde_json::json!(text.len())),
            ]),
        };

        // Store pattern
        let mut database = self.pattern_database.write().await;
        database.insert(pattern_id, pattern_data);

        info!("Pattern added to database");
        Ok(())
    }

    /// Find similar patterns
    #[instrument(skip(self, text), fields(text_len = text.len()))]
    pub async fn find_similar_patterns(&self, text: &str) -> Result<Vec<SimilarityMatch>> {
        debug!("Finding similar patterns for text");

        // Extract features from input text
        let features = self.feature_extractor.extract_features(text, None).await?;
        let feature_vector = features.to_feature_vector();

        let database = self.pattern_database.read().await;
        let mut similarities = Vec::new();

        for (pattern_id, pattern_data) in database.iter() {
            let similarity = self.cosine_similarity(&feature_vector, &pattern_data.features);
            let confidence = self.calculate_similarity_confidence(similarity, &features, pattern_data);

            if similarity >= self.config.similarity_threshold {
                let matching_features = self.find_matching_features(&features, pattern_data);

                let similarity_match = SimilarityMatch {
                    target_id: pattern_id.clone(),
                    similarity_score: similarity,
                    matching_features,
                    confidence,
                    metadata: HashMap::from([
                        ("target_category".to_string(), serde_json::json!(pattern_data.category)),
                        ("feature_overlap".to_string(), serde_json::json!(matching_features.len())),
                    ]),
                };

                similarities.push(similarity_match);
            }
        }

        // Sort by similarity score
        similarities.sort_by(|a, b| b.similarity_score.partial_cmp(&a.similarity_score).unwrap());

        // Limit results
        similarities.truncate(self.config.max_similar_patterns);

        info!("Found {} similar patterns", similarities.len());
        Ok(similarities)
    }

    /// Detect anomalies in patterns
    #[instrument(skip(self))]
    pub async fn detect_anomalies(&self) -> Result<Vec<AnomalyResult>> {
        if !self.config.enable_anomaly_detection {
            return Ok(Vec::new());
        }

        debug!("Detecting pattern anomalies");

        let database = self.pattern_database.read().await;
        let mut anomalies = Vec::new();

        // Calculate mean and standard deviation for each feature
        let feature_stats = self.calculate_feature_statistics(&database).await;

        for (pattern_id, pattern_data) in database.iter() {
            let anomaly_score = self.calculate_anomaly_score(&pattern_data.features, &feature_stats);
            let is_anomalous = anomaly_score >= self.config.anomaly_threshold;

            if is_anomalous {
                let anomaly_features = self.identify_anomaly_features(&pattern_data.features, &feature_stats);
                let anomaly_type = self.classify_anomaly(&anomaly_features);

                let anomaly_result = AnomalyResult {
                    pattern_id: pattern_id.clone(),
                    anomaly_score,
                    is_anomalous,
                    anomaly_features,
                    confidence: (anomaly_score - self.config.anomaly_threshold).min(1.0),
                    anomaly_type,
                };

                anomalies.push(anomaly_result);
            }
        }

        // Sort by anomaly score
        anomalies.sort_by(|a, b| b.anomaly_score.partial_cmp(&a.anomaly_score).unwrap());

        info!("Detected {} anomalies", anomalies.len());
        Ok(anomalies)
    }

    /// Perform clustering for pattern categorization
    #[instrument(skip(self))]
    pub async fn perform_clustering(&self) -> Result<Vec<ClusteringResult>> {
        if !self.config.enable_clustering {
            return Ok(Vec::new());
        }

        debug!("Performing pattern clustering");

        let database = self.pattern_database.read().await;

        if database.len() < self.config.num_clusters {
            return Err(InfinitumError::InvalidInput {
                field: "database_size".to_string(),
                message: format!("Not enough patterns for clustering: {} < {}", database.len(), self.config.num_clusters),
            });
        }

        // Extract feature matrix
        let patterns: Vec<&PatternData> = database.values().collect();
        let feature_matrix = self.create_feature_matrix(&patterns);

        // Perform K-means clustering
        let (cluster_assignments, cluster_centers) = self.kmeans_clustering(&feature_matrix, self.config.num_clusters);

        // Update cluster centers
        {
            let mut centers = self.cluster_centers.write().await;
            *centers = cluster_centers;
        }

        // Create clustering results
        let mut results = Vec::new();
        for (i, pattern) in patterns.iter().enumerate() {
            let cluster_id = cluster_assignments[i];
            let distance = self.euclidean_distance(&pattern.features, &self.cluster_centers.read().await[&cluster_id]);
            let cluster_name = self.get_cluster_name(cluster_id, &patterns);

            let result = ClusteringResult {
                pattern_id: pattern.id.clone(),
                cluster_id,
                cluster_name,
                distance_to_center: distance,
                confidence: 1.0 / (1.0 + distance), // Convert distance to confidence
                characteristics: self.get_cluster_characteristics(cluster_id, &patterns),
            };

            results.push(result);
        }

        info!("Clustering completed: {} clusters created", self.config.num_clusters);
        Ok(results)
    }

    /// Analyze temporal patterns
    #[instrument(skip(self))]
    pub async fn analyze_temporal_patterns(&self) -> Result<Vec<TemporalPattern>> {
        if !self.config.enable_temporal_analysis {
            return Ok(Vec::new());
        }

        debug!("Analyzing temporal patterns");

        let temporal_data = self.temporal_patterns.read().await;
        let mut temporal_patterns = Vec::new();

        for (pattern_id, time_series) in temporal_data.iter() {
            if time_series.len() < 3 {
                continue; // Need at least 3 points for analysis
            }

            let trend = self.analyze_trend(time_series);
            let seasonality = self.detect_seasonality(time_series);
            let frequency = self.calculate_frequency(time_series);
            let strength = self.calculate_pattern_strength(time_series);

            let temporal_pattern = TemporalPattern {
                pattern_id: pattern_id.clone(),
                time_series: time_series.clone(),
                trend,
                seasonality,
                frequency,
                strength,
            };

            temporal_patterns.push(temporal_pattern);
        }

        info!("Temporal analysis completed for {} patterns", temporal_patterns.len());
        Ok(temporal_patterns)
    }

    /// Update temporal data
    #[instrument(skip(self), fields(pattern_id = %pattern_id))]
    pub async fn update_temporal_data(&self, pattern_id: String, value: f64) -> Result<()> {
        let mut temporal_data = self.temporal_patterns.write().await;
        let time_series = temporal_data.entry(pattern_id).or_insert(Vec::new());
        time_series.push((Utc::now(), value));

        // Keep only recent data within temporal window
        let cutoff = Utc::now() - chrono::Duration::days(self.config.temporal_window_days);
        time_series.retain(|(timestamp, _)| *timestamp > cutoff);

        Ok(())
    }

    /// Get pattern statistics
    pub async fn get_pattern_stats(&self) -> HashMap<String, serde_json::Value> {
        let database = self.pattern_database.read().await;

        let total_patterns = database.len();
        let categories: HashSet<String> = database.values().map(|p| p.category.clone()).collect();
        let avg_features = if !database.is_empty() {
            database.values().map(|p| p.features.len()).sum::<usize>() as f64 / database.len() as f64
        } else {
            0.0
        };

        HashMap::from([
            ("total_patterns".to_string(), serde_json::json!(total_patterns)),
            ("unique_categories".to_string(), serde_json::json!(categories.len())),
            ("avg_features_per_pattern".to_string(), serde_json::json!(avg_features)),
            ("categories".to_string(), serde_json::json!(categories)),
        ])
    }

    /// Cosine similarity calculation
    fn cosine_similarity(&self, a: &[f64], b: &[f64]) -> f64 {
        if a.len() != b.len() {
            return 0.0;
        }

        let dot_product: f64 = a.iter().zip(b.iter()).map(|(x, y)| x * y).sum();
        let norm_a: f64 = a.iter().map(|x| x * x).sum::<f64>().sqrt();
        let norm_b: f64 = b.iter().map(|x| x * x).sum::<f64>().sqrt();

        if norm_a == 0.0 || norm_b == 0.0 {
            0.0
        } else {
            dot_product / (norm_a * norm_b)
        }
    }

    /// Calculate similarity confidence
    fn calculate_similarity_confidence(&self, similarity: f64, features: &ExtractedFeatures, pattern: &PatternData) -> f64 {
        let mut confidence = similarity;

        // Boost confidence based on category match
        if let Some(license_type) = features.metadata.get("license_type") {
            if license_type == &serde_json::json!(pattern.category) {
                confidence += 0.1;
            }
        }

        // Boost confidence based on feature richness
        let feature_richness = features.total_feature_count() as f64 / 1000.0;
        confidence += feature_richness * 0.05;

        confidence.min(1.0)
    }

    /// Find matching features between patterns
    fn find_matching_features(&self, features: &ExtractedFeatures, pattern: &PatternData) -> Vec<String> {
        let mut matching = Vec::new();

        // Compare license-specific features
        for (feature_name, value) in &features.license_specific_features {
            if *value > 0.5 { // Threshold for considering a feature present
                matching.push(format!("license_{}", feature_name));
            }
        }

        // Compare text statistics
        if features.text_stats.word_count > 10 {
            matching.push("text_stats_word_count".to_string());
        }

        matching
    }

    /// Calculate feature statistics for anomaly detection
    async fn calculate_feature_statistics(&self, database: &HashMap<String, PatternData>) -> HashMap<String, (f64, f64)> {
        let mut stats = HashMap::new();

        if database.is_empty() {
            return stats;
        }

        let patterns: Vec<&PatternData> = database.values().collect();
        let feature_count = patterns[0].features.len();

        for i in 0..feature_count {
            let values: Vec<f64> = patterns.iter().map(|p| p.features[i]).collect();
            let mean = values.iter().sum::<f64>() / values.len() as f64;
            let variance = values.iter().map(|v| (v - mean).powi(2)).sum::<f64>() / values.len() as f64;
            let std_dev = variance.sqrt();

            stats.insert(format!("feature_{}", i), (mean, std_dev));
        }

        stats
    }

    /// Calculate anomaly score
    fn calculate_anomaly_score(&self, features: &[f64], stats: &HashMap<String, (f64, f64)>) -> f64 {
        let mut total_score = 0.0;
        let mut feature_count = 0;

        for (i, &value) in features.iter().enumerate() {
            if let Some((mean, std_dev)) = stats.get(&format!("feature_{}", i)) {
                if *std_dev > 0.0 {
                    let z_score = (value - mean).abs() / std_dev;
                    total_score += z_score;
                    feature_count += 1;
                }
            }
        }

        if feature_count > 0 {
            total_score / feature_count as f64
        } else {
            0.0
        }
    }

    /// Identify anomaly features
    fn identify_anomaly_features(&self, features: &[f64], stats: &HashMap<String, (f64, f64)>) -> Vec<String> {
        let mut anomaly_features = Vec::new();

        for (i, &value) in features.iter().enumerate() {
            if let Some((mean, std_dev)) = stats.get(&format!("feature_{}", i)) {
                if *std_dev > 0.0 {
                    let z_score = (value - mean).abs() / std_dev;
                    if z_score > 2.0 { // More than 2 standard deviations
                        anomaly_features.push(format!("feature_{}", i));
                    }
                }
            }
        }

        anomaly_features
    }

    /// Classify anomaly type
    fn classify_anomaly(&self, anomaly_features: &[String]) -> String {
        if anomaly_features.len() > 5 {
            "high_dimensional_anomaly".to_string()
        } else if anomaly_features.len() > 2 {
            "moderate_anomaly".to_string()
        } else {
            "low_dimensional_anomaly".to_string()
        }
    }

    /// Create feature matrix for clustering
    fn create_feature_matrix(&self, patterns: &[&PatternData]) -> Array2<f64> {
        let n_samples = patterns.len();
        let n_features = patterns[0].features.len();

        let mut matrix = Array2::<f64>::zeros((n_samples, n_features));

        for (i, pattern) in patterns.iter().enumerate() {
            for (j, &value) in pattern.features.iter().enumerate() {
                matrix[[i, j]] = value;
            }
        }

        matrix
    }

    /// K-means clustering implementation
    fn kmeans_clustering(&self, data: &Array2<f64>, k: usize) -> (Vec<usize>, HashMap<usize, Vec<f64>>) {
        let n_samples = data.nrows();
        let n_features = data.ncols();

        // Initialize centroids randomly
        let mut centroids = HashMap::new();
        for i in 0..k {
            let random_sample = (i * n_samples / k) % n_samples;
            let centroid: Vec<f64> = (0..n_features).map(|j| data[[random_sample, j]]).collect();
            centroids.insert(i, centroid);
        }

        let mut assignments = vec![0; n_samples];
        let mut changed = true;
        let max_iterations = 100;

        for _ in 0..max_iterations {
            if !changed {
                break;
            }
            changed = false;

            // Assign points to nearest centroid
            for i in 0..n_samples {
                let point: Vec<f64> = (0..n_features).map(|j| data[[i, j]]).collect();
                let mut min_distance = f64::INFINITY;
                let mut best_cluster = 0;

                for (cluster_id, centroid) in &centroids {
                    let distance = self.euclidean_distance(&point, centroid);
                    if distance < min_distance {
                        min_distance = distance;
                        best_cluster = *cluster_id;
                    }
                }

                if assignments[i] != best_cluster {
                    assignments[i] = best_cluster;
                    changed = true;
                }
            }

            // Update centroids
            for cluster_id in 0..k {
                let cluster_points: Vec<Vec<f64>> = assignments.iter().enumerate()
                    .filter(|(_, &assignment)| assignment == cluster_id)
                    .map(|(i, _)| (0..n_features).map(|j| data[[i, j]]).collect())
                    .collect();

                if !cluster_points.is_empty() {
                    let mut new_centroid = vec![0.0; n_features];
                    for point in &cluster_points {
                        for j in 0..n_features {
                            new_centroid[j] += point[j];
                        }
                    }
                    for j in 0..n_features {
                        new_centroid[j] /= cluster_points.len() as f64;
                    }
                    centroids.insert(cluster_id, new_centroid);
                }
            }
        }

        (assignments, centroids)
    }

    /// Euclidean distance calculation
    fn euclidean_distance(&self, a: &[f64], b: &[f64]) -> f64 {
        a.iter().zip(b.iter())
            .map(|(x, y)| (x - y).powi(2))
            .sum::<f64>()
            .sqrt()
    }

    /// Get cluster name based on patterns
    fn get_cluster_name(&self, cluster_id: usize, patterns: &[&PatternData]) -> String {
        // Simple naming based on most common category in cluster
        let mut category_counts = HashMap::new();

        for pattern in patterns {
            // This is a simplified approach - in practice, you'd check cluster assignments
            *category_counts.entry(pattern.category.clone()).or_insert(0) += 1;
        }

        category_counts.into_iter()
            .max_by_key(|(_, count)| *count)
            .map(|(category, _)| format!("{}_cluster", category))
            .unwrap_or_else(|| format!("cluster_{}", cluster_id))
    }

    /// Get cluster characteristics
    fn get_cluster_characteristics(&self, cluster_id: usize, patterns: &[&PatternData]) -> HashMap<String, f64> {
        // Calculate average feature values for the cluster
        let mut characteristics = HashMap::new();

        if patterns.is_empty() {
            return characteristics;
        }

        let feature_count = patterns[0].features.len();

        for i in 0..feature_count {
            let mut sum = 0.0;
            let mut count = 0;

            for pattern in patterns {
                // Simplified - in practice, check cluster assignments
                sum += pattern.features[i];
                count += 1;
            }

            if count > 0 {
                characteristics.insert(format!("feature_{}_avg", i), sum / count as f64);
            }
        }

        characteristics
    }

    /// Analyze trend in time series
    fn analyze_trend(&self, time_series: &[(DateTime<Utc>, f64)]) -> TrendAnalysis {
        if time_series.len() < 2 {
            return TrendAnalysis {
                direction: TrendDirection::Stable,
                slope: 0.0,
                strength: 0.0,
                r_squared: 0.0,
            };
        }

        // Simple linear regression
        let n = time_series.len() as f64;
        let x_values: Vec<f64> = (0..time_series.len()).map(|i| i as f64).collect();
        let y_values: Vec<f64> = time_series.iter().map(|(_, y)| *y).collect();

        let x_mean = x_values.iter().sum::<f64>() / n;
        let y_mean = y_values.iter().sum::<f64>() / n;

        let numerator: f64 = x_values.iter().zip(y_values.iter())
            .map(|(x, y)| (x - x_mean) * (y - y_mean))
            .sum();
        let denominator: f64 = x_values.iter()
            .map(|x| (x - x_mean).powi(2))
            .sum();

        let slope = if denominator != 0.0 { numerator / denominator } else { 0.0 };

        // Calculate R-squared
        let y_pred: Vec<f64> = x_values.iter().map(|x| slope * (x - x_mean) + y_mean).collect();
        let ss_res: f64 = y_values.iter().zip(y_pred.iter())
            .map(|(y, y_p)| (y - y_p).powi(2))
            .sum();
        let ss_tot: f64 = y_values.iter()
            .map(|y| (y - y_mean).powi(2))
            .sum();
        let r_squared = if ss_tot != 0.0 { 1.0 - (ss_res / ss_tot) } else { 0.0 };

        let direction = if slope > 0.01 {
            TrendDirection::Increasing
        } else if slope < -0.01 {
            TrendDirection::Decreasing
        } else {
            TrendDirection::Stable
        };

        TrendAnalysis {
            direction,
            slope,
            strength: r_squared.sqrt(),
            r_squared,
        }
    }

    /// Detect seasonality in time series
    fn detect_seasonality(&self, time_series: &[(DateTime<Utc>, f64)]) -> Option<SeasonalPattern> {
        // Simplified seasonality detection
        // In a full implementation, this would use more sophisticated methods
        if time_series.len() < 10 {
            return None;
        }

        // Assume daily seasonality for license patterns
        let period_days = 1;
        let strength = 0.5; // Placeholder

        Some(SeasonalPattern {
            period_days,
            strength,
            peaks: Vec::new(), // Would need more complex analysis
            valleys: Vec::new(),
        })
    }

    /// Calculate pattern frequency
    fn calculate_frequency(&self, time_series: &[(DateTime<Utc>, f64)]) -> f64 {
        if time_series.len() < 2 {
            return 0.0;
        }

        let total_duration = time_series.last().unwrap().0 - time_series.first().unwrap().0;
        let total_days = total_duration.num_days() as f64;

        if total_days > 0.0 {
            time_series.len() as f64 / total_days
        } else {
            0.0
        }
    }

    /// Calculate pattern strength
    fn calculate_pattern_strength(&self, time_series: &[(DateTime<Utc>, f64)]) -> f64 {
        if time_series.len() < 2 {
            return 0.0;
        }

        let values: Vec<f64> = time_series.iter().map(|(_, v)| *v).collect();
        let mean = values.iter().sum::<f64>() / values.len() as f64;
        let variance = values.iter().map(|v| (v - mean).powi(2)).sum::<f64>() / values.len() as f64;

        if mean != 0.0 {
            (variance.sqrt() / mean).min(1.0)
        } else {
            0.0
        }
    }
}

impl Default for PatternRecognitionConfig {
    fn default() -> Self {
        Self {
            similarity_threshold: 0.7,
            max_similar_patterns: 10,
            enable_anomaly_detection: true,
            anomaly_threshold: 2.5,
            enable_clustering: true,
            num_clusters: 5,
            enable_temporal_analysis: true,
            temporal_window_days: 30,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Arc;

    #[tokio::test]
    async fn test_pattern_recognition_engine_creation() {
        let feature_config = crate::compliance::ml_feature_extractor::FeatureExtractorConfig::default();
        let feature_extractor = Arc::new(MLFeatureExtractor::new(feature_config));

        let config = PatternRecognitionConfig::default();
        let engine = PatternRecognitionEngine::new(config, feature_extractor);

        let stats = engine.get_pattern_stats().await;
        assert!(stats.contains_key("total_patterns"));
    }

    #[tokio::test]
    async fn test_pattern_addition() {
        let feature_config = crate::compliance::ml_feature_extractor::FeatureExtractorConfig::default();
        let feature_extractor = Arc::new(MLFeatureExtractor::new(feature_config));

        let config = PatternRecognitionConfig::default();
        let engine = PatternRecognitionEngine::new(config, feature_extractor);

        engine.add_pattern("test_pattern".to_string(), "MIT License text", "MIT".to_string()).await.unwrap();

        let stats = engine.get_pattern_stats().await;
        assert_eq!(stats.get("total_patterns").unwrap().as_i64().unwrap(), 1);
    }

    #[tokio::test]
    async fn test_similarity_calculation() {
        let feature_config = crate::compliance::ml_feature_extractor::FeatureExtractorConfig::default();
        let feature_extractor = Arc::new(MLFeatureExtractor::new(feature_config));

        let config = PatternRecognitionConfig::default();
        let engine = PatternRecognitionEngine::new(config, feature_extractor);

        // Add a pattern
        engine.add_pattern("pattern1".to_string(), "MIT License text", "MIT".to_string()).await.unwrap();

        // Find similar patterns
        let similar = engine.find_similar_patterns("MIT License content").await.unwrap();
        assert!(!similar.is_empty());
    }

    #[test]
    fn test_cosine_similarity() {
        let feature_config = crate::compliance::ml_feature_extractor::FeatureExtractorConfig::default();
        let feature_extractor = Arc::new(MLFeatureExtractor::new(feature_config));

        let config = PatternRecognitionConfig::default();
        let engine = PatternRecognitionEngine::new(config, feature_extractor);

        let a = vec![1.0, 2.0, 3.0];
        let b = vec![1.0, 2.0, 3.0];
        let similarity = engine.cosine_similarity(&a, &b);
        assert!((similarity - 1.0).abs() < 0.001);

        let c = vec![1.0, 0.0, 0.0];
        let d = vec![0.0, 1.0, 0.0];
        let similarity2 = engine.cosine_similarity(&c, &d);
        assert!(similarity2.abs() < 0.001);
    }
}