//! # Performance Monitor
//!
//! Advanced performance monitoring and alerting system for the license compliance platform.
//! Provides real-time performance metrics, error rate monitoring, performance bottleneck detection,
//! and system health dashboards with comprehensive alerting capabilities.

use crate::{
    error::{InfinitumError, Result},
    observability::custom_metrics::{CustomMetricsManager as MetricsCollector, PerformanceMetrics as CorePerformanceMetrics},
};
use serde::{Deserialize, Serialize};
use std::{
    collections::{HashMap, VecDeque},
    sync::Arc,
    time::{Duration, Instant},
};
use tokio::sync::RwLock;
use tracing::{debug, error, info, instrument, warn};
use chrono::{DateTime, Utc};

/// Performance monitor configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMonitorConfig {
    /// Enable real-time monitoring
    pub enable_real_time_monitoring: bool,
    /// Metrics collection interval (seconds)
    pub metrics_collection_interval_secs: u64,
    /// Performance history retention (hours)
    pub metrics_retention_hours: u64,
    /// Alert thresholds
    pub alert_thresholds: AlertThresholds,
    /// Enable bottleneck detection
    pub enable_bottleneck_detection: bool,
    /// Enable predictive alerting
    pub enable_predictive_alerting: bool,
    /// Performance baseline window (hours)
    pub baseline_window_hours: u64,
    /// Anomaly detection sensitivity (0-1)
    pub anomaly_detection_sensitivity: f64,
}

/// Alert thresholds
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertThresholds {
    /// Response time threshold (milliseconds)
    pub response_time_threshold_ms: u64,
    /// Error rate threshold (0-1)
    pub error_rate_threshold: f64,
    /// CPU usage threshold (0-1)
    pub cpu_usage_threshold: f64,
    /// Memory usage threshold (0-1)
    pub memory_usage_threshold: f64,
    /// Queue size threshold
    pub queue_size_threshold: usize,
    /// Throughput drop threshold (percentage)
    pub throughput_drop_threshold: f64,
}

/// Performance snapshot
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceSnapshot {
    /// Timestamp
    pub timestamp: DateTime<Utc>,
    /// Response time statistics
    pub response_time_stats: ResponseTimeStats,
    /// Error rate
    pub error_rate: f64,
    /// Throughput (requests per second)
    pub throughput_rps: f64,
    /// Resource usage
    pub resource_usage: ResourceUsage,
    /// Queue statistics
    pub queue_stats: QueueStats,
    /// Active connections
    pub active_connections: usize,
}

/// Response time statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResponseTimeStats {
    /// Average response time (milliseconds)
    pub avg_response_time_ms: f64,
    /// 95th percentile response time
    pub p95_response_time_ms: f64,
    /// 99th percentile response time
    pub p99_response_time_ms: f64,
    /// Minimum response time
    pub min_response_time_ms: f64,
    /// Maximum response time
    pub max_response_time_ms: f64,
}

/// Resource usage
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceUsage {
    /// CPU usage (0-1)
    pub cpu_usage: f64,
    /// Memory usage (MB)
    pub memory_usage_mb: usize,
    /// Disk I/O (MB/s)
    pub disk_io_mbps: f64,
    /// Network I/O (MB/s)
    pub network_io_mbps: f64,
}

/// Queue statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueueStats {
    /// Current queue size
    pub current_size: usize,
    /// Maximum queue size
    pub max_size: usize,
    /// Average queue time (milliseconds)
    pub avg_queue_time_ms: f64,
    /// Queue drop rate
    pub drop_rate: f64,
}

/// Performance alert
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceAlert {
    /// Alert ID
    pub alert_id: String,
    /// Alert type
    pub alert_type: AlertType,
    /// Severity
    pub severity: AlertSeverity,
    /// Message
    pub message: String,
    /// Metric name
    pub metric_name: String,
    /// Current value
    pub current_value: f64,
    /// Threshold value
    pub threshold_value: f64,
    /// Timestamp
    pub timestamp: DateTime<Utc>,
    /// Context information
    pub context: HashMap<String, serde_json::Value>,
}

/// Alert types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum AlertType {
    /// Response time alert
    ResponseTime,
    /// Error rate alert
    ErrorRate,
    /// Resource usage alert
    ResourceUsage,
    /// Throughput alert
    Throughput,
    /// Queue alert
    Queue,
    /// Bottleneck alert
    Bottleneck,
    /// Predictive alert
    Predictive,
}

/// Alert severity
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum AlertSeverity {
    /// Low severity
    Low,
    /// Medium severity
    Medium,
    /// High severity
    High,
    /// Critical severity
    Critical,
}

/// Bottleneck detection result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BottleneckResult {
    /// Detected bottlenecks
    pub bottlenecks: Vec<Bottleneck>,
    /// Overall system health score (0-1)
    pub system_health_score: f64,
    /// Performance recommendations
    pub recommendations: Vec<String>,
    /// Timestamp
    pub timestamp: DateTime<Utc>,
}

/// Bottleneck information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Bottleneck {
    /// Component name
    pub component: String,
    /// Bottleneck type
    pub bottleneck_type: BottleneckType,
    /// Severity score (0-1)
    pub severity_score: f64,
    /// Impact on performance
    pub performance_impact: f64,
    /// Description
    pub description: String,
    /// Suggested actions
    pub suggested_actions: Vec<String>,
}

/// Bottleneck types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum BottleneckType {
    /// CPU bottleneck
    Cpu,
    /// Memory bottleneck
    Memory,
    /// I/O bottleneck
    Io,
    /// Network bottleneck
    Network,
    /// Database bottleneck
    Database,
    /// Queue bottleneck
    Queue,
    /// Lock contention bottleneck
    LockContention,
}

/// Performance trend analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceTrend {
    /// Trend direction
    pub trend_direction: TrendDirection,
    /// Trend slope
    pub trend_slope: f64,
    /// Recent average performance
    pub recent_average: f64,
    /// Performance volatility
    pub volatility: f64,
    /// Data points analyzed
    pub data_points: usize,
    /// Confidence in trend
    pub trend_confidence: f64,
}

/// Trend direction
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum TrendDirection {
    /// Improving
    Improving,
    /// Degrading
    Degrading,
    /// Stable
    Stable,
}

/// Performance dashboard
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceDashboard {
    /// Current performance snapshot
    pub current_snapshot: PerformanceSnapshot,
    /// Recent alerts
    pub recent_alerts: Vec<PerformanceAlert>,
    /// Performance trends
    pub performance_trends: HashMap<String, PerformanceTrend>,
    /// System health overview
    pub system_health: SystemHealthOverview,
    /// Bottleneck analysis
    pub bottleneck_analysis: BottleneckResult,
    /// Generated at timestamp
    pub generated_at: DateTime<Utc>,
}

/// System health overview
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemHealthOverview {
    /// Overall health score (0-1)
    pub overall_health_score: f64,
    /// Health status
    pub health_status: HealthStatus,
    /// Component health scores
    pub component_health_scores: HashMap<String, f64>,
    /// Active issues count
    pub active_issues_count: usize,
    /// Recent improvements
    pub recent_improvements: Vec<String>,
    /// Areas needing attention
    pub areas_needing_attention: Vec<String>,
}

/// Health status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum HealthStatus {
    /// Excellent health
    Excellent,
    /// Good health
    Good,
    /// Fair health
    Fair,
    /// Poor health
    Poor,
    /// Critical health
    Critical,
}

/// Performance monitor
pub struct PerformanceMonitor {
    config: PerformanceMonitorConfig,
    performance_history: Arc<RwLock<VecDeque<PerformanceSnapshot>>>,
    active_alerts: Arc<RwLock<HashMap<String, PerformanceAlert>>>,
    baseline_metrics: Arc<RwLock<Option<PerformanceSnapshot>>>,
    metrics_collector: Arc<MetricsCollector>,
    alert_handlers: Vec<Box<dyn AlertHandler + Send + Sync>>,
}

/// Alert handler trait
pub trait AlertHandler: Send + Sync {
    /// Handle a performance alert
    fn handle_alert(&self, alert: &PerformanceAlert) -> Result<()>;
}

impl PerformanceMonitor {
    /// Create new performance monitor
    pub fn new(config: PerformanceMonitorConfig, metrics_collector: Arc<MetricsCollector>) -> Self {
        Self {
            config,
            performance_history: Arc::new(RwLock::new(VecDeque::with_capacity(10000))),
            active_alerts: Arc::new(RwLock::new(HashMap::new())),
            baseline_metrics: Arc::new(RwLock::new(None)),
            metrics_collector,
            alert_handlers: Vec::new(),
        }
    }

    /// Record performance metrics
    #[instrument(skip(self, snapshot))]
    pub async fn record_performance_snapshot(&self, snapshot: PerformanceSnapshot) -> Result<()> {
        // Store snapshot in history
        let mut history = self.performance_history.write().await;
        history.push_back(snapshot.clone());

        // Keep only recent history
        let cutoff_time = Utc::now() - chrono::Duration::hours(self.config.metrics_retention_hours as i64);
        while let Some(oldest) = history.front() {
            if oldest.timestamp < cutoff_time {
                history.pop_front();
            } else {
                break;
            }
        }

        // Update baseline if needed
        self.update_baseline_metrics(&snapshot).await?;

        // Check for alerts
        self.check_performance_alerts(&snapshot).await?;

        // Detect bottlenecks
        if self.config.enable_bottleneck_detection {
            let bottlenecks = self.detect_bottlenecks(&snapshot).await?;
            if !bottlenecks.bottlenecks.is_empty() {
                self.handle_bottleneck_detection(bottlenecks).await?;
            }
        }

        // Update metrics collector
        let core_metrics = CorePerformanceMetrics {
            response_time_ms: snapshot.response_time_stats.avg_response_time_ms,
            throughput_rps: snapshot.throughput_rps,
            error_rate: snapshot.error_rate,
            cpu_usage: snapshot.resource_usage.cpu_usage,
            memory_usage_mb: snapshot.resource_usage.memory_usage_mb,
            active_connections: snapshot.active_connections,
            timestamp: snapshot.timestamp,
        };

        self.metrics_collector.record_performance_metrics(core_metrics).await;

        Ok(())
    }

    /// Update baseline metrics
    async fn update_baseline_metrics(&self, snapshot: &PerformanceSnapshot) -> Result<()> {
        let mut baseline = self.baseline_metrics.write().await;

        if baseline.is_none() {
            // Initialize baseline with current snapshot
            *baseline = Some(snapshot.clone());
            return Ok(());
        }

        // Update baseline using exponential moving average
        if let Some(current_baseline) = baseline.as_mut() {
            let alpha = 0.1; // Smoothing factor

            current_baseline.response_time_stats.avg_response_time_ms =
                current_baseline.response_time_stats.avg_response_time_ms * (1.0 - alpha) +
                snapshot.response_time_stats.avg_response_time_ms * alpha;

            current_baseline.throughput_rps =
                current_baseline.throughput_rps * (1.0 - alpha) +
                snapshot.throughput_rps * alpha;

            current_baseline.error_rate =
                current_baseline.error_rate * (1.0 - alpha) +
                snapshot.error_rate * alpha;
        }

        Ok(())
    }

    /// Check for performance alerts
    async fn check_performance_alerts(&self, snapshot: &PerformanceSnapshot) -> Result<()> {
        let mut new_alerts = Vec::new();

        // Response time alert
        if snapshot.response_time_stats.avg_response_time_ms > self.config.alert_thresholds.response_time_threshold_ms as f64 {
            new_alerts.push(PerformanceAlert {
                alert_id: format!("response_time_{}", Utc::now().timestamp_millis()),
                alert_type: AlertType::ResponseTime,
                severity: if snapshot.response_time_stats.avg_response_time_ms > self.config.alert_thresholds.response_time_threshold_ms as f64 * 2.0 {
                    AlertSeverity::Critical
                } else {
                    AlertSeverity::High
                },
                message: format!("High response time: {:.2}ms", snapshot.response_time_stats.avg_response_time_ms),
                metric_name: "avg_response_time_ms".to_string(),
                current_value: snapshot.response_time_stats.avg_response_time_ms,
                threshold_value: self.config.alert_thresholds.response_time_threshold_ms as f64,
                timestamp: Utc::now(),
                context: HashMap::from([
                    ("p95_response_time".to_string(), serde_json::json!(snapshot.response_time_stats.p95_response_time_ms)),
                    ("p99_response_time".to_string(), serde_json::json!(snapshot.response_time_stats.p99_response_time_ms)),
                ]),
            });
        }

        // Error rate alert
        if snapshot.error_rate > self.config.alert_thresholds.error_rate_threshold {
            new_alerts.push(PerformanceAlert {
                alert_id: format!("error_rate_{}", Utc::now().timestamp_millis()),
                alert_type: AlertType::ErrorRate,
                severity: if snapshot.error_rate > self.config.alert_thresholds.error_rate_threshold * 2.0 {
                    AlertSeverity::Critical
                } else {
                    AlertSeverity::High
                },
                message: format!("High error rate: {:.3}", snapshot.error_rate),
                metric_name: "error_rate".to_string(),
                current_value: snapshot.error_rate,
                threshold_value: self.config.alert_thresholds.error_rate_threshold,
                timestamp: Utc::now(),
                context: HashMap::new(),
            });
        }

        // Resource usage alerts
        if snapshot.resource_usage.cpu_usage > self.config.alert_thresholds.cpu_usage_threshold {
            new_alerts.push(PerformanceAlert {
                alert_id: format!("cpu_usage_{}", Utc::now().timestamp_millis()),
                alert_type: AlertType::ResourceUsage,
                severity: AlertSeverity::Medium,
                message: format!("High CPU usage: {:.1}%", snapshot.resource_usage.cpu_usage * 100.0),
                metric_name: "cpu_usage".to_string(),
                current_value: snapshot.resource_usage.cpu_usage,
                threshold_value: self.config.alert_thresholds.cpu_usage_threshold,
                timestamp: Utc::now(),
                context: HashMap::new(),
            });
        }

        if snapshot.resource_usage.memory_usage_mb as f64 / 1024.0 > self.config.alert_thresholds.memory_usage_threshold {
            new_alerts.push(PerformanceAlert {
                alert_id: format!("memory_usage_{}", Utc::now().timestamp_millis()),
                alert_type: AlertType::ResourceUsage,
                severity: AlertSeverity::High,
                message: format!("High memory usage: {}MB", snapshot.resource_usage.memory_usage_mb),
                metric_name: "memory_usage_mb".to_string(),
                current_value: snapshot.resource_usage.memory_usage_mb as f64,
                threshold_value: self.config.alert_thresholds.memory_usage_threshold * 1024.0,
                timestamp: Utc::now(),
                context: HashMap::new(),
            });
        }

        // Queue size alert
        if snapshot.queue_stats.current_size > self.config.alert_thresholds.queue_size_threshold {
            new_alerts.push(PerformanceAlert {
                alert_id: format!("queue_size_{}", Utc::now().timestamp_millis()),
                alert_type: AlertType::Queue,
                severity: AlertSeverity::Medium,
                message: format!("Large queue size: {}", snapshot.queue_stats.current_size),
                metric_name: "queue_size".to_string(),
                current_value: snapshot.queue_stats.current_size as f64,
                threshold_value: self.config.alert_thresholds.queue_size_threshold as f64,
                timestamp: Utc::now(),
                context: HashMap::from([
                    ("avg_queue_time".to_string(), serde_json::json!(snapshot.queue_stats.avg_queue_time_ms)),
                ]),
            });
        }

        // Handle new alerts
        let mut active_alerts = self.active_alerts.write().await;
        for alert in new_alerts {
            // Check if similar alert already exists
            let alert_key = format!("{:?}_{}", alert.alert_type, alert.metric_name);
            if !active_alerts.contains_key(&alert_key) {
                active_alerts.insert(alert_key, alert.clone());

                // Notify alert handlers
                for handler in &self.alert_handlers {
                    if let Err(e) = handler.handle_alert(&alert) {
                        error!("Alert handler failed: {}", e);
                    }
                }

                // Log alert
                match alert.severity {
                    AlertSeverity::Critical => error!("🚨 CRITICAL ALERT: {}", alert.message),
                    AlertSeverity::High => error!("⚠️ HIGH ALERT: {}", alert.message),
                    AlertSeverity::Medium => warn!("⚡ MEDIUM ALERT: {}", alert.message),
                    AlertSeverity::Low => info!("ℹ️ LOW ALERT: {}", alert.message),
                }
            }
        }

        Ok(())
    }

    /// Detect performance bottlenecks
    async fn detect_bottlenecks(&self, snapshot: &PerformanceSnapshot) -> Result<BottleneckResult> {
        let mut bottlenecks = Vec::new();
        let mut system_health_score = 1.0;

        // CPU bottleneck detection
        if snapshot.resource_usage.cpu_usage > 0.8 {
            let severity = (snapshot.resource_usage.cpu_usage - 0.8) / 0.2;
            bottlenecks.push(Bottleneck {
                component: "CPU".to_string(),
                bottleneck_type: BottleneckType::Cpu,
                severity_score: severity.min(1.0),
                performance_impact: severity * 0.3,
                description: format!("High CPU usage: {:.1}%", snapshot.resource_usage.cpu_usage * 100.0),
                suggested_actions: vec![
                    "Consider horizontal scaling".to_string(),
                    "Optimize CPU-intensive operations".to_string(),
                    "Review background processes".to_string(),
                ],
            });
            system_health_score *= 1.0 - severity * 0.2;
        }

        // Memory bottleneck detection
        let memory_usage_ratio = snapshot.resource_usage.memory_usage_mb as f64 / 4096.0; // Assuming 4GB limit
        if memory_usage_ratio > 0.8 {
            let severity = (memory_usage_ratio - 0.8) / 0.2;
            bottlenecks.push(Bottleneck {
                component: "Memory".to_string(),
                bottleneck_type: BottleneckType::Memory,
                severity_score: severity.min(1.0),
                performance_impact: severity * 0.4,
                description: format!("High memory usage: {}MB", snapshot.resource_usage.memory_usage_mb),
                suggested_actions: vec![
                    "Implement memory pooling".to_string(),
                    "Review memory leaks".to_string(),
                    "Consider increasing memory limits".to_string(),
                ],
            });
            system_health_score *= 1.0 - severity * 0.3;
        }

        // Queue bottleneck detection
        let queue_usage_ratio = snapshot.queue_stats.current_size as f64 / snapshot.queue_stats.max_size as f64;
        if queue_usage_ratio > 0.7 {
            let severity = (queue_usage_ratio - 0.7) / 0.3;
            bottlenecks.push(Bottleneck {
                component: "Queue".to_string(),
                bottleneck_type: BottleneckType::Queue,
                severity_score: severity.min(1.0),
                performance_impact: severity * 0.5,
                description: format!("Queue nearly full: {}/{}", snapshot.queue_stats.current_size, snapshot.queue_stats.max_size),
                suggested_actions: vec![
                    "Increase queue processing capacity".to_string(),
                    "Implement queue partitioning".to_string(),
                    "Review request processing pipeline".to_string(),
                ],
            });
            system_health_score *= 1.0 - severity * 0.4;
        }

        // Response time bottleneck detection
        if snapshot.response_time_stats.avg_response_time_ms > 1000.0 {
            let severity = (snapshot.response_time_stats.avg_response_time_ms - 1000.0) / 2000.0;
            bottlenecks.push(Bottleneck {
                component: "Response Time".to_string(),
                bottleneck_type: BottleneckType::Network,
                severity_score: severity.min(1.0),
                performance_impact: severity * 0.6,
                description: format!("Slow response time: {:.2}ms", snapshot.response_time_stats.avg_response_time_ms),
                suggested_actions: vec![
                    "Optimize database queries".to_string(),
                    "Implement response caching".to_string(),
                    "Review network configuration".to_string(),
                ],
            });
            system_health_score *= 1.0 - severity * 0.5;
        }

        let recommendations = self.generate_bottleneck_recommendations(&bottlenecks);

        Ok(BottleneckResult {
            bottlenecks,
            system_health_score: system_health_score.max(0.0),
            recommendations,
            timestamp: Utc::now(),
        })
    }

    /// Generate bottleneck recommendations
    fn generate_bottleneck_recommendations(&self, bottlenecks: &[Bottleneck]) -> Vec<String> {
        let mut recommendations = Vec::new();

        if bottlenecks.is_empty() {
            recommendations.push("System performance is optimal".to_string());
            return recommendations;
        }

        // Sort bottlenecks by severity
        let mut sorted_bottlenecks = bottlenecks.to_vec();
        sorted_bottlenecks.sort_by(|a, b| b.severity_score.partial_cmp(&a.severity_score).unwrap());

        for bottleneck in sorted_bottlenecks.iter().take(3) {
            recommendations.push(format!(
                "Address {} bottleneck (severity: {:.2})",
                bottleneck.component, bottleneck.severity_score
            ));
        }

        if bottlenecks.len() > 3 {
            recommendations.push(format!("{} additional bottlenecks detected", bottlenecks.len() - 3));
        }

        recommendations
    }

    /// Handle bottleneck detection
    async fn handle_bottleneck_detection(&self, bottleneck_result: BottleneckResult) -> Result<()> {
        if bottleneck_result.bottlenecks.is_empty() {
            return Ok(());
        }

        // Create bottleneck alert
        let alert = PerformanceAlert {
            alert_id: format!("bottleneck_{}", Utc::now().timestamp_millis()),
            alert_type: AlertType::Bottleneck,
            severity: if bottleneck_result.system_health_score < 0.5 {
                AlertSeverity::Critical
            } else if bottleneck_result.system_health_score < 0.7 {
                AlertSeverity::High
            } else {
                AlertSeverity::Medium
            },
            message: format!("Performance bottlenecks detected: {} issues", bottleneck_result.bottlenecks.len()),
            metric_name: "system_health_score".to_string(),
            current_value: bottleneck_result.system_health_score,
            threshold_value: 0.8,
            timestamp: Utc::now(),
            context: HashMap::from([
                ("bottleneck_count".to_string(), serde_json::json!(bottleneck_result.bottlenecks.len())),
                ("recommendations".to_string(), serde_json::json!(bottleneck_result.recommendations)),
            ]),
        };

        // Handle alert
        let mut active_alerts = self.active_alerts.write().await;
        let alert_key = "bottleneck_detection".to_string();
        active_alerts.insert(alert_key, alert.clone());

        // Notify handlers
        for handler in &self.alert_handlers {
            if let Err(e) = handler.handle_alert(&alert) {
                error!("Alert handler failed: {}", e);
            }
        }

        Ok(())
    }

    /// Generate performance dashboard
    #[instrument(skip(self))]
    pub async fn generate_performance_dashboard(&self) -> Result<PerformanceDashboard> {
        let history = self.performance_history.read().await;
        let active_alerts = self.active_alerts.read().await;

        if history.is_empty() {
            return Err(InfinitumError::Internal {
                message: "No performance data available".to_string(),
            });
        }

        let current_snapshot = history.back().unwrap().clone();
        let recent_alerts: Vec<_> = active_alerts.values().cloned().collect();

        // Calculate performance trends
        let performance_trends = self.calculate_performance_trends().await?;

        // Generate system health overview
        let system_health = self.generate_system_health_overview(&current_snapshot, &recent_alerts).await?;

        // Get bottleneck analysis
        let bottleneck_analysis = self.detect_bottlenecks(&current_snapshot).await?;

        Ok(PerformanceDashboard {
            current_snapshot,
            recent_alerts,
            performance_trends,
            system_health,
            bottleneck_analysis,
            generated_at: Utc::now(),
        })
    }

    /// Calculate performance trends
    async fn calculate_performance_trends(&self) -> Result<HashMap<String, PerformanceTrend>> {
        let history = self.performance_history.read().await;

        if history.len() < 10 {
            return Ok(HashMap::new());
        }

        let mut trends = HashMap::new();

        // Response time trend
        let response_times: Vec<f64> = history.iter().rev().take(20).map(|s| s.response_time_stats.avg_response_time_ms).collect();
        if let Some(trend) = self.calculate_trend(&response_times) {
            trends.insert("response_time".to_string(), trend);
        }

        // Throughput trend
        let throughputs: Vec<f64> = history.iter().rev().take(20).map(|s| s.throughput_rps).collect();
        if let Some(trend) = self.calculate_trend(&throughputs) {
            trends.insert("throughput".to_string(), trend);
        }

        // Error rate trend
        let error_rates: Vec<f64> = history.iter().rev().take(20).map(|s| s.error_rate).collect();
        if let Some(trend) = self.calculate_trend(&error_rates) {
            trends.insert("error_rate".to_string(), trend);
        }

        Ok(trends)
    }

    /// Calculate trend for a metric series
    fn calculate_trend(&self, values: &[f64]) -> Option<PerformanceTrend> {
        if values.len() < 5 {
            return None;
        }

        let n = values.len() as f64;
        let recent_values: Vec<f64> = values.iter().rev().take(10.min(values.len())).cloned().collect();
        let recent_average = recent_values.iter().sum::<f64>() / recent_values.len() as f64;

        // Simple linear regression
        let x_sum: f64 = (0..recent_values.len()).map(|i| i as f64).sum();
        let y_sum: f64 = recent_values.iter().sum();
        let xy_sum: f64 = recent_values.iter().enumerate().map(|(i, &y)| i as f64 * y).sum();
        let x_squared_sum: f64 = (0..recent_values.len()).map(|i| (i as f64).powi(2)).sum();

        let slope = (n * xy_sum - x_sum * y_sum) / (n * x_squared_sum - x_sum.powi(2));

        let trend_direction = if slope > 0.01 {
            TrendDirection::Degrading // Higher values are worse for most metrics
        } else if slope < -0.01 {
            TrendDirection::Improving
        } else {
            TrendDirection::Stable
        };

        // Calculate volatility (standard deviation)
        let variance = recent_values.iter()
            .map(|v| (v - recent_average).powi(2))
            .sum::<f64>() / n;
        let volatility = variance.sqrt();

        Some(PerformanceTrend {
            trend_direction,
            trend_slope: slope,
            recent_average,
            volatility,
            data_points: recent_values.len(),
            trend_confidence: 0.8, // Simplified
        })
    }

    /// Generate system health overview
    async fn generate_system_health_overview(
        &self,
        snapshot: &PerformanceSnapshot,
        alerts: &[PerformanceAlert],
    ) -> Result<SystemHealthOverview> {
        let mut component_health_scores = HashMap::new();
        let mut active_issues_count = alerts.len();

        // Calculate component health scores
        component_health_scores.insert(
            "response_time".to_string(),
            1.0 - (snapshot.response_time_stats.avg_response_time_ms / 5000.0).min(1.0),
        );

        component_health_scores.insert(
            "error_rate".to_string(),
            1.0 - snapshot.error_rate,
        );

        component_health_scores.insert(
            "cpu".to_string(),
            1.0 - snapshot.resource_usage.cpu_usage,
        );

        component_health_scores.insert(
            "memory".to_string(),
            1.0 - (snapshot.resource_usage.memory_usage_mb as f64 / 4096.0).min(1.0),
        );

        component_health_scores.insert(
            "queue".to_string(),
            1.0 - (snapshot.queue_stats.current_size as f64 / snapshot.queue_stats.max_size as f64).min(1.0),
        );

        // Calculate overall health score
        let overall_health_score = component_health_scores.values().sum::<f64>() / component_health_scores.len() as f64;

        let health_status = if overall_health_score >= 0.9 {
            HealthStatus::Excellent
        } else if overall_health_score >= 0.8 {
            HealthStatus::Good
        } else if overall_health_score >= 0.7 {
            HealthStatus::Fair
        } else if overall_health_score >= 0.6 {
            HealthStatus::Poor
        } else {
            HealthStatus::Critical
        };

        let recent_improvements = vec!["Response time optimized".to_string()]; // Placeholder
        let areas_needing_attention = component_health_scores.iter()
            .filter(|(_, &score)| score < 0.8)
            .map(|(component, _)| component.clone())
            .collect();

        Ok(SystemHealthOverview {
            overall_health_score,
            health_status,
            component_health_scores,
            active_issues_count,
            recent_improvements,
            areas_needing_attention,
        })
    }

    /// Get current performance snapshot
    pub async fn get_current_snapshot(&self) -> Option<PerformanceSnapshot> {
        let history = self.performance_history.read().await;
        history.back().cloned()
    }

    /// Get active alerts
    pub async fn get_active_alerts(&self) -> HashMap<String, PerformanceAlert> {
        let active_alerts = self.active_alerts.read().await;
        active_alerts.clone()
    }

    /// Clear resolved alerts
    #[instrument(skip(self))]
    pub async fn clear_resolved_alerts(&self) -> Result<()> {
        let mut active_alerts = self.active_alerts.write().await;

        // For now, clear alerts older than 1 hour
        let cutoff_time = Utc::now() - chrono::Duration::hours(1);
        active_alerts.retain(|_, alert| alert.timestamp > cutoff_time);

        info!("Cleared {} resolved alerts", active_alerts.len());
        Ok(())
    }

    /// Add alert handler
    pub fn add_alert_handler(&mut self, handler: Box<dyn AlertHandler + Send + Sync>) {
        self.alert_handlers.push(handler);
    }

    /// Get performance history
    pub async fn get_performance_history(&self, hours: u64) -> Vec<PerformanceSnapshot> {
        let history = self.performance_history.read().await;
        let cutoff_time = Utc::now() - chrono::Duration::hours(hours as i64);

        history.iter()
            .filter(|snapshot| snapshot.timestamp > cutoff_time)
            .cloned()
            .collect()
    }

    /// Export performance data
    pub async fn export_performance_data(&self) -> Result<serde_json::Value> {
        let dashboard = self.generate_performance_dashboard().await?;
        let history = self.get_performance_history(24).await;
        let active_alerts = self.get_active_alerts().await;

        let export_data = serde_json::json!({
            "dashboard": dashboard,
            "performance_history": history,
            "active_alerts": active_alerts,
            "export_timestamp": Utc::now(),
            "config": self.config,
        });

        Ok(export_data)
    }
}

impl Default for PerformanceMonitorConfig {
    fn default() -> Self {
        Self {
            enable_real_time_monitoring: true,
            metrics_collection_interval_secs: 60,
            metrics_retention_hours: 24,
            alert_thresholds: AlertThresholds {
                response_time_threshold_ms: 2000,
                error_rate_threshold: 0.05,
                cpu_usage_threshold: 0.8,
                memory_usage_threshold: 0.8,
                queue_size_threshold: 1000,
                throughput_drop_threshold: 0.2,
            },
            enable_bottleneck_detection: true,
            enable_predictive_alerting: false,
            baseline_window_hours: 24,
            anomaly_detection_sensitivity: 0.8,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_performance_monitor_creation() {
        let config = PerformanceMonitorConfig::default();
        let metrics_collector = Arc::new(MetricsCollector::default());
        let monitor = PerformanceMonitor::new(config, metrics_collector);

        let snapshot = monitor.get_current_snapshot().await;
        assert!(snapshot.is_none()); // Should be none initially
    }

    #[tokio::test]
    async fn test_performance_snapshot_recording() {
        let config = PerformanceMonitorConfig::default();
        let metrics_collector = Arc::new(MetricsCollector::default());
        let monitor = PerformanceMonitor::new(config, metrics_collector);

        let snapshot = PerformanceSnapshot {
            timestamp: Utc::now(),
            response_time_stats: ResponseTimeStats {
                avg_response_time_ms: 500.0,
                p95_response_time_ms: 800.0,
                p99_response_time_ms: 1000.0,
                min_response_time_ms: 100.0,
                max_response_time_ms: 1500.0,
            },
            error_rate: 0.02,
            throughput_rps: 100.0,
            resource_usage: ResourceUsage {
                cpu_usage: 0.5,
                memory_usage_mb: 1024,
                disk_io_mbps: 50.0,
                network_io_mbps: 25.0,
            },
            queue_stats: QueueStats {
                current_size: 50,
                max_size: 1000,
                avg_queue_time_ms: 10.0,
                drop_rate: 0.001,
            },
            active_connections: 150,
        };

        monitor.record_performance_snapshot(snapshot.clone()).await.unwrap();

        let current = monitor.get_current_snapshot().await.unwrap();
        assert_eq!(current.response_time_stats.avg_response_time_ms, snapshot.response_time_stats.avg_response_time_ms);
    }

    #[test]
    fn test_alert_severity_ordering() {
        assert!(AlertSeverity::Critical > AlertSeverity::High);
        assert!(AlertSeverity::High > AlertSeverity::Medium);
        assert!(AlertSeverity::Medium > AlertSeverity::Low);
    }
}