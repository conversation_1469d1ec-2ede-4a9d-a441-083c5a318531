//! # Pipeline Orchestrator
//!
//! Unified pipeline management system for multi-platform CI/CD integration,
//! providing configuration validation, optimization, execution monitoring,
//! and result aggregation across different CI/CD platforms.

use crate::{
    compliance::{
        ci_cd_scanner::{CIDCScanner, CICDPlaform, CICDPlaformConfig, CICDPlaformRequest, CICDPlaformResult, CIOutputFormat, ScanMode, CIScanStatus},
        github_actions_integration::{GitHubActionsIntegration, GitHubActionsConfig, GitHubActionsRequest},
        gitlab_ci_integration::{GitLabCIIntegration, GitLabCIConfig, GitLabCIRequest},
        jenkins_integration::{JenkinsIntegration, JenkinsConfig, JenkinsRequest},
        docker_integration::{DockerIntegration, DockerIntegrationConfig, DockerScanRequest},
        ComplianceConfig,
    },
    config::ScanningConfig,
    error::{InfinitumError, Result},
};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    sync::Arc,
    time::Duration,
};
use tokio::sync::RwLock;
use tracing::{debug, info, instrument, warn, error};
use uuid::Uuid;

/// Pipeline orchestrator configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PipelineOrchestratorConfig {
    /// Enable parallel execution
    pub enable_parallel_execution: bool,
    /// Maximum concurrent pipelines
    pub max_concurrent_pipelines: usize,
    /// Pipeline timeout in seconds
    pub pipeline_timeout_seconds: u64,
    /// Enable result caching
    pub enable_result_caching: bool,
    /// Cache TTL in seconds
    pub cache_ttl_seconds: u64,
    /// Enable pipeline optimization
    pub enable_optimization: bool,
    /// Enable monitoring
    pub enable_monitoring: bool,
    /// Monitoring endpoint
    pub monitoring_endpoint: Option<String>,
    /// Enable notifications
    pub enable_notifications: bool,
    /// Notification channels
    pub notification_channels: Vec<NotificationChannel>,
}

/// Notification channel configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationChannel {
    /// Channel type
    pub channel_type: NotificationType,
    /// Channel configuration
    pub config: HashMap<String, String>,
}

/// Notification types
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum NotificationType {
    /// Slack notifications
    Slack,
    /// Email notifications
    Email,
    /// Webhook notifications
    Webhook,
    /// Microsoft Teams
    Teams,
}

/// Pipeline execution request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PipelineExecutionRequest {
    /// Request ID
    pub id: Uuid,
    /// Target platforms
    pub platforms: Vec<CICDPlaform>,
    /// Pipeline configuration
    pub config: PipelineConfig,
    /// Target path
    pub target: String,
    /// Scan mode
    pub scan_mode: ScanMode,
    /// Changed files (for incremental scans)
    pub changed_files: Option<Vec<String>>,
    /// Custom parameters
    pub custom_params: HashMap<String, serde_json::Value>,
    /// Metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Pipeline configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PipelineConfig {
    /// Base CI/CD configuration
    pub base_config: CICDPlaformConfig,
    /// Platform-specific configurations
    pub platform_configs: HashMap<String, PlatformSpecificConfig>,
    /// Global parameters
    pub global_params: HashMap<String, String>,
}

/// Platform-specific configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlatformSpecificConfig {
    /// Platform type
    pub platform: CICDPlaform,
    /// Platform-specific parameters
    pub params: HashMap<String, serde_json::Value>,
    /// Enable platform-specific features
    pub enabled_features: Vec<String>,
}

/// Pipeline execution result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PipelineExecutionResult {
    /// Request that generated this result
    pub request: PipelineExecutionRequest,
    /// Overall execution status
    pub status: PipelineExecutionStatus,
    /// Start time
    pub started_at: chrono::DateTime<chrono::Utc>,
    /// End time
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
    /// Total execution duration
    pub duration: Option<Duration>,
    /// Platform-specific results
    pub platform_results: HashMap<String, PlatformResult>,
    /// Aggregated summary
    pub summary: PipelineSummary,
    /// Execution statistics
    pub statistics: PipelineStatistics,
    /// Issues found across all platforms
    pub issues: Vec<PipelineIssue>,
    /// Recommendations
    pub recommendations: Vec<String>,
}

/// Pipeline execution status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum PipelineExecutionStatus {
    /// All pipelines completed successfully
    Success,
    /// Some pipelines completed with warnings
    Warning,
    /// Some pipelines failed
    PartialFailure,
    /// All pipelines failed
    Failure,
    /// Execution was cancelled
    Cancelled,
    /// Execution timed out
    Timeout,
}

/// Platform-specific result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlatformResult {
    /// Platform type
    pub platform: CICDPlaform,
    /// Execution status
    pub status: PlatformExecutionStatus,
    /// Start time
    pub started_at: chrono::DateTime<chrono::Utc>,
    /// End time
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
    /// Execution duration
    pub duration: Option<Duration>,
    /// Platform-specific result data
    pub result: PlatformResultData,
    /// Issues found on this platform
    pub issues: Vec<PlatformIssue>,
    /// Platform-specific metrics
    pub metrics: HashMap<String, serde_json::Value>,
}

/// Platform execution status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum PlatformExecutionStatus {
    /// Platform execution completed successfully
    Success,
    /// Platform execution completed with warnings
    Warning,
    /// Platform execution failed
    Failed,
    /// Platform execution was skipped
    Skipped,
    /// Platform execution timed out
    Timeout,
}

/// Platform result data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PlatformResultData {
    /// GitHub Actions result
    GitHubActions(crate::compliance::github_actions_integration::GitHubActionsResult),
    /// GitLab CI result
    GitLabCI(crate::compliance::gitlab_ci_integration::GitLabCIResult),
    /// Jenkins result
    Jenkins(crate::compliance::jenkins_integration::JenkinsResult),
    /// Docker result
    Docker(crate::compliance::docker_integration::DockerScanResult),
    /// Generic CI/CD result
    Generic(CICDPlaformResult),
}

/// Platform issue
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlatformIssue {
    /// Issue severity
    pub severity: String,
    /// Issue message
    pub message: String,
    /// Issue category
    pub category: String,
    /// Platform-specific details
    pub details: HashMap<String, serde_json::Value>,
}

/// Pipeline summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PipelineSummary {
    /// Total platforms executed
    pub total_platforms: usize,
    /// Successful platforms
    pub successful_platforms: usize,
    /// Failed platforms
    pub failed_platforms: usize,
    /// Skipped platforms
    pub skipped_platforms: usize,
    /// Overall compliance score
    pub overall_compliance_score: f64,
    /// Total files scanned
    pub total_files_scanned: u64,
    /// Total licenses found
    pub total_licenses_found: u64,
    /// High severity issues
    pub high_severity_issues: u32,
    /// Medium severity issues
    pub medium_severity_issues: u32,
    /// Low severity issues
    pub low_severity_issues: u32,
}

/// Pipeline statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PipelineStatistics {
    /// Total execution time
    pub total_execution_time_ms: u64,
    /// Average platform execution time
    pub avg_platform_execution_time_ms: f64,
    /// Platform success rate
    pub platform_success_rate: f64,
    /// Cache hit rate
    pub cache_hit_rate: f64,
    /// Resource usage statistics
    pub resource_usage: ResourceUsageStats,
}

/// Resource usage statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceUsageStats {
    /// Peak memory usage in MB
    pub peak_memory_mb: u64,
    /// Average CPU usage percentage
    pub avg_cpu_percent: f64,
    /// Total network usage in MB
    pub total_network_mb: u64,
    /// I/O operations count
    pub io_operations: u64,
}

/// Pipeline issue
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PipelineIssue {
    /// Issue severity
    pub severity: String,
    /// Issue message
    pub message: String,
    /// Affected platforms
    pub affected_platforms: Vec<String>,
    /// Issue category
    pub category: String,
    /// Resolution suggestions
    pub suggestions: Vec<String>,
}

/// Main pipeline orchestrator
pub struct PipelineOrchestrator {
    config: PipelineOrchestratorConfig,
    base_scanner: Arc<RwLock<CIDCScanner>>,
    github_integration: Option<Arc<RwLock<GitHubActionsIntegration>>>,
    gitlab_integration: Option<Arc<RwLock<GitLabCIIntegration>>>,
    jenkins_integration: Option<Arc<RwLock<JenkinsIntegration>>>,
    docker_integration: Option<Arc<RwLock<DockerIntegration>>>,
    cache: Arc<RwLock<HashMap<String, PipelineExecutionResult>>>,
    statistics: Arc<RwLock<PipelineStatistics>>,
}

impl PipelineOrchestrator {
    /// Create new pipeline orchestrator
    pub fn new(
        config: PipelineOrchestratorConfig,
        compliance_config: ComplianceConfig,
        scanning_config: ScanningConfig,
    ) -> Self {
        let base_scanner = Arc::new(RwLock::new(CIDCScanner::new(
            compliance_config,
            scanning_config,
        )));

        Self {
            config,
            base_scanner,
            github_integration: None,
            gitlab_integration: None,
            jenkins_integration: None,
            docker_integration: None,
            cache: Arc::new(RwLock::new(HashMap::new())),
            statistics: Arc::new(RwLock::new(PipelineStatistics::default())),
        }
    }

    /// Enable GitHub Actions integration
    pub fn enable_github_actions(&mut self, config: GitHubActionsConfig, compliance_config: ComplianceConfig, scanning_config: ScanningConfig) {
        self.github_integration = Some(Arc::new(RwLock::new(GitHubActionsIntegration::new(
            compliance_config,
            scanning_config,
            config,
        ))));
    }

    /// Enable GitLab CI integration
    pub fn enable_gitlab_ci(&mut self, config: GitLabCIConfig, compliance_config: ComplianceConfig, scanning_config: ScanningConfig) {
        self.gitlab_integration = Some(Arc::new(RwLock::new(GitLabCIIntegration::new(
            compliance_config,
            scanning_config,
            config,
        ))));
    }

    /// Enable Jenkins integration
    pub fn enable_jenkins(&mut self, config: JenkinsConfig, compliance_config: ComplianceConfig, scanning_config: ScanningConfig) {
        self.jenkins_integration = Some(Arc::new(RwLock::new(JenkinsIntegration::new(
            compliance_config,
            scanning_config,
            config,
        ))));
    }

    /// Enable Docker integration
    pub fn enable_docker(&mut self, config: DockerIntegrationConfig, compliance_config: ComplianceConfig, scanning_config: ScanningConfig) {
        self.docker_integration = Some(Arc::new(RwLock::new(DockerIntegration::new(
            compliance_config,
            scanning_config,
            config,
        ))));
    }

    /// Execute pipeline across multiple platforms
    #[instrument(skip(self), fields(request_id = %request.id))]
    pub async fn execute_pipeline(&self, request: PipelineExecutionRequest) -> Result<PipelineExecutionResult> {
        let start_time = chrono::Utc::now();
        info!(
            "Starting multi-platform pipeline execution for {} platforms",
            request.platforms.len()
        );

        // Check cache first
        if self.config.enable_result_caching {
            if let Some(cached_result) = self.check_cache(&request).await {
                debug!("Cache hit for pipeline execution");
                return Ok(cached_result);
            }
        }

        let mut result = PipelineExecutionResult {
            request: request.clone(),
            status: PipelineExecutionStatus::Success,
            started_at: start_time,
            completed_at: None,
            duration: None,
            platform_results: HashMap::new(),
            summary: PipelineSummary {
                total_platforms: request.platforms.len(),
                successful_platforms: 0,
                failed_platforms: 0,
                skipped_platforms: 0,
                overall_compliance_score: 0.0,
                total_files_scanned: 0,
                total_licenses_found: 0,
                high_severity_issues: 0,
                medium_severity_issues: 0,
                low_severity_issues: 0,
            },
            statistics: PipelineStatistics::default(),
            issues: Vec::new(),
            recommendations: Vec::new(),
        };

        // Execute pipelines on each platform
        if self.config.enable_parallel_execution {
            self.execute_parallel(&request, &mut result).await?;
        } else {
            self.execute_sequential(&request, &mut result).await?;
        }

        // Aggregate results
        self.aggregate_results(&mut result).await;

        // Update statistics
        self.update_statistics(&result).await;

        // Cache the result
        if self.config.enable_result_caching {
            self.cache_result(&request, &result).await;
        }

        result.completed_at = Some(chrono::Utc::now());
        result.duration = Some(Duration::from_millis(
            (result.completed_at.unwrap() - start_time).num_milliseconds() as u64
        ));

        info!(
            "Pipeline execution completed in {:?} with status {:?}",
            result.duration.unwrap(),
            result.status
        );

        Ok(result)
    }

    /// Execute pipelines in parallel
    async fn execute_parallel(&self, request: &PipelineExecutionRequest, result: &mut PipelineExecutionResult) -> Result<()> {
        use futures::future::join_all;

        let mut futures = Vec::new();

        for platform in &request.platforms {
            let platform_result = self.execute_platform_pipeline(request, *platform).await?;
            futures.push(async move { (platform, platform_result) });
        }

        let results = join_all(futures).await;

        for (platform, platform_result) in results {
            let platform_key = format!("{:?}", platform).to_lowercase();
            result.platform_results.insert(platform_key, platform_result);
        }

        Ok(())
    }

    /// Execute pipelines sequentially
    async fn execute_sequential(&self, request: &PipelineExecutionRequest, result: &mut PipelineExecutionResult) -> Result<()> {
        for platform in &request.platforms {
            let platform_result = self.execute_platform_pipeline(request, *platform).await?;
            let platform_key = format!("{:?}", platform).to_lowercase();
            result.platform_results.insert(platform_key, platform_result);
        }

        Ok(())
    }

    /// Execute pipeline on a specific platform
    async fn execute_platform_pipeline(&self, request: &PipelineExecutionRequest, platform: CICDPlaform) -> Result<PlatformResult> {
        let start_time = chrono::Utc::now();
        let platform_name = format!("{:?}", platform);

        info!("Executing pipeline on platform: {}", platform_name);

        let result = match platform {
            CICDPlaform::GitHubActions => {
                if let Some(integration) = &self.github_integration {
                    let github_request = self.convert_to_github_request(request)?;
                    let integration = integration.read().await;
                    let result = integration.execute_scan(github_request).await?;
                    PlatformResultData::GitHubActions(result)
                } else {
                    return Err(InfinitumError::Configuration {
                        message: "GitHub Actions integration not enabled".to_string(),
                    });
                }
            }
            CICDPlaform::GitLabCI => {
                if let Some(integration) = &self.gitlab_integration {
                    let gitlab_request = self.convert_to_gitlab_request(request)?;
                    let integration = integration.read().await;
                    let result = integration.execute_scan(gitlab_request).await?;
                    PlatformResultData::GitLabCI(result)
                } else {
                    return Err(InfinitumError::Configuration {
                        message: "GitLab CI integration not enabled".to_string(),
                    });
                }
            }
            CICDPlaform::Jenkins => {
                if let Some(integration) = &self.jenkins_integration {
                    let jenkins_request = self.convert_to_jenkins_request(request)?;
                    let integration = integration.read().await;
                    let result = integration.execute_scan(jenkins_request).await?;
                    PlatformResultData::Jenkins(result)
                } else {
                    return Err(InfinitumError::Configuration {
                        message: "Jenkins integration not enabled".to_string(),
                    });
                }
            }
            CICDPlaform::Generic => {
                let base_request = self.convert_to_base_request(request)?;
                let scanner = self.base_scanner.read().await;
                let result = scanner.execute_scan(base_request).await?;
                PlatformResultData::Generic(result)
            }
            _ => {
                warn!("Platform {} not yet supported, skipping", platform_name);
                return Ok(PlatformResult {
                    platform,
                    status: PlatformExecutionStatus::Skipped,
                    started_at: start_time,
                    completed_at: Some(chrono::Utc::now()),
                    duration: Some(Duration::from_millis(0)),
                    result: PlatformResultData::Generic(CICDPlaformResult {
                        request: self.convert_to_base_request(request)?,
                        status: CIScanStatus::Completed,
                        started_at: start_time,
                        completed_at: Some(chrono::Utc::now()),
                        duration: Some(Duration::from_millis(0)),
                        license_results: None,
                        compliance_results: None,
                        exit_code: 0,
                        summary: Default::default(),
                        formatted_output: String::new(),
                        issues: Vec::new(),
                        recommendations: Vec::new(),
                        metadata: HashMap::new(),
                    }),
                    issues: Vec::new(),
                    metrics: HashMap::new(),
                });
            }
        };

        let end_time = chrono::Utc::now();
        let duration = Duration::from_millis((end_time - start_time).num_milliseconds() as u64);

        Ok(PlatformResult {
            platform,
            status: PlatformExecutionStatus::Success,
            started_at: start_time,
            completed_at: Some(end_time),
            duration: Some(duration),
            result,
            issues: Vec::new(), // TODO: Extract issues from platform result
            metrics: HashMap::new(), // TODO: Collect platform metrics
        })
    }

    /// Convert to GitHub Actions request
    fn convert_to_github_request(&self, request: &PipelineExecutionRequest) -> Result<crate::compliance::github_actions_integration::GitHubActionsRequest> {
        // Implementation would convert generic request to GitHub-specific format
        // For now, return a placeholder
        Ok(crate::compliance::github_actions_integration::GitHubActionsRequest {
            id: request.id,
            target: request.target.clone(),
            trigger_type: crate::compliance::github_actions_integration::GitHubTriggerType::Manual,
            commit_sha: "placeholder".to_string(),
            ref_name: "main".to_string(),
            run_id: 1,
            run_number: 1,
            job_name: "license-scan".to_string(),
            workflow_name: "License Compliance".to_string(),
            pull_request: None,
            changed_files: request.changed_files.clone(),
            metadata: request.metadata.clone(),
        })
    }

    /// Convert to GitLab CI request
    fn convert_to_gitlab_request(&self, request: &PipelineExecutionRequest) -> Result<crate::compliance::gitlab_ci_integration::GitLabCIRequest> {
        // Implementation would convert generic request to GitLab-specific format
        // For now, return a placeholder
        Ok(crate::compliance::gitlab_ci_integration::GitLabCIRequest {
            id: request.id,
            target: request.target.clone(),
            trigger_type: crate::compliance::gitlab_ci_integration::GitLabTriggerType::Manual,
            commit_sha: "placeholder".to_string(),
            ref_name: "main".to_string(),
            pipeline_id: 1,
            job_name: "license-scan".to_string(),
            pipeline_name: "License Compliance".to_string(),
            merge_request: None,
            changed_files: request.changed_files.clone(),
            metadata: request.metadata.clone(),
        })
    }

    /// Convert to Jenkins request
    fn convert_to_jenkins_request(&self, request: &PipelineExecutionRequest) -> Result<crate::compliance::jenkins_integration::JenkinsRequest> {
        // Implementation would convert generic request to Jenkins-specific format
        // For now, return a placeholder
        Ok(crate::compliance::jenkins_integration::JenkinsRequest {
            id: request.id,
            workspace: request.target.clone(),
            build_number: 1,
            build_cause: "MANUALTRIGGER".to_string(),
            git_commit: "placeholder".to_string(),
            git_branch: Some("main".to_string()),
            pipeline_name: "License Compliance".to_string(),
            changed_files: request.changed_files.clone(),
            metadata: request.metadata.clone(),
        })
    }

    /// Convert to base CI/CD request
    fn convert_to_base_request(&self, request: &PipelineExecutionRequest) -> Result<CICDPlaformRequest> {
        Ok(CICDPlaformRequest {
            id: request.id,
            config: request.config.base_config.clone(),
            target: request.target.clone(),
            changed_files: request.changed_files.clone(),
            pr_info: None,
            build_info: None,
            metadata: request.metadata.clone(),
        })
    }

    /// Aggregate results from all platforms
    async fn aggregate_results(&self, result: &mut PipelineExecutionResult) {
        let mut total_compliance_score = 0.0;
        let mut total_files = 0u64;
        let mut total_licenses = 0u64;
        let mut total_high_issues = 0u32;
        let mut total_medium_issues = 0u32;
        let mut total_low_issues = 0u32;
        let mut successful_platforms = 0;
        let mut failed_platforms = 0;

        for platform_result in result.platform_results.values() {
            match &platform_result.result {
                PlatformResultData::GitHubActions(github_result) => {
                    total_compliance_score += github_result.base_result.summary.compliance_score;
                    total_files += github_result.base_result.summary.files_scanned;
                    total_licenses += github_result.base_result.summary.total_licenses;
                    total_high_issues += github_result.base_result.summary.high_severity_issues;
                    total_medium_issues += github_result.base_result.summary.medium_severity_issues;
                    total_low_issues += github_result.base_result.summary.low_severity_issues;
                }
                PlatformResultData::GitLabCI(gitlab_result) => {
                    total_compliance_score += gitlab_result.base_result.summary.compliance_score;
                    total_files += gitlab_result.base_result.summary.files_scanned;
                    total_licenses += gitlab_result.base_result.summary.total_licenses;
                    total_high_issues += gitlab_result.base_result.summary.high_severity_issues;
                    total_medium_issues += gitlab_result.base_result.summary.medium_severity_issues;
                    total_low_issues += gitlab_result.base_result.summary.low_severity_issues;
                }
                PlatformResultData::Jenkins(jenkins_result) => {
                    total_compliance_score += jenkins_result.base_result.summary.compliance_score;
                    total_files += jenkins_result.base_result.summary.files_scanned;
                    total_licenses += jenkins_result.base_result.summary.total_licenses;
                    total_high_issues += jenkins_result.base_result.summary.high_severity_issues;
                    total_medium_issues += jenkins_result.base_result.summary.medium_severity_issues;
                    total_low_issues += jenkins_result.base_result.summary.low_severity_issues;
                }
                PlatformResultData::Docker(docker_result) => {
                    total_compliance_score += docker_result.base_result.summary.compliance_score;
                    total_files += docker_result.base_result.summary.files_scanned;
                    total_licenses += docker_result.base_result.summary.total_licenses;
                    total_high_issues += docker_result.base_result.summary.high_severity_issues;
                    total_medium_issues += docker_result.base_result.summary.medium_severity_issues;
                    total_low_issues += docker_result.base_result.summary.low_severity_issues;
                }
                PlatformResultData::Generic(generic_result) => {
                    total_compliance_score += generic_result.summary.compliance_score;
                    total_files += generic_result.summary.files_scanned;
                    total_licenses += generic_result.summary.total_licenses;
                    total_high_issues += generic_result.summary.high_severity_issues;
                    total_medium_issues += generic_result.summary.medium_severity_issues;
                    total_low_issues += generic_result.summary.low_severity_issues;
                }
            }

            match platform_result.status {
                PlatformExecutionStatus::Success => successful_platforms += 1,
                PlatformExecutionStatus::Failed => failed_platforms += 1,
                _ => {}
            }
        }

        // Calculate averages
        let platform_count = result.platform_results.len() as f64;
        if platform_count > 0.0 {
            result.summary.overall_compliance_score = total_compliance_score / platform_count;
        }

        result.summary.successful_platforms = successful_platforms;
        result.summary.failed_platforms = failed_platforms;
        result.summary.total_files_scanned = total_files;
        result.summary.total_licenses_found = total_licenses;
        result.summary.high_severity_issues = total_high_issues;
        result.summary.medium_severity_issues = total_medium_issues;
        result.summary.low_severity_issues = total_low_issues;

        // Determine overall status
        if failed_platforms > 0 {
            if successful_platforms > 0 {
                result.status = PipelineExecutionStatus::PartialFailure;
            } else {
                result.status = PipelineExecutionStatus::Failure;
            }
        } else if successful_platforms > 0 {
            result.status = PipelineExecutionStatus::Success;
        }
    }

    /// Update execution statistics
    async fn update_statistics(&self, result: &PipelineExecutionResult) {
        let mut stats = self.statistics.write().await;

        if let Some(duration) = result.duration {
            stats.total_execution_time_ms += duration.as_millis() as u64;
        }

        let platform_count = result.platform_results.len() as f64;
        if platform_count > 0.0 {
            stats.avg_platform_execution_time_ms = stats.total_execution_time_ms as f64 / platform_count;
            stats.platform_success_rate = result.summary.successful_platforms as f64 / platform_count;
        }
    }

    /// Check cache for existing result
    async fn check_cache(&self, request: &PipelineExecutionRequest) -> Option<PipelineExecutionResult> {
        let cache_key = self.cache_key(request);
        let cache = self.cache.read().await;

        if let Some(result) = cache.get(&cache_key) {
            // Check if cache entry is still valid
            let age = chrono::Utc::now().signed_duration_since(result.completed_at.unwrap_or(result.started_at));
            if age.num_seconds() < self.config.cache_ttl_seconds as i64 {
                return Some(result.clone());
            }
        }
        None
    }

    /// Cache execution result
    async fn cache_result(&self, request: &PipelineExecutionRequest, result: &PipelineExecutionResult) {
        let cache_key = self.cache_key(request);
        let mut cache = self.cache.write().await;
        cache.insert(cache_key, result.clone());
    }

    /// Generate cache key for request
    fn cache_key(&self, request: &PipelineExecutionRequest) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        request.id.hash(&mut hasher);
        request.target.hash(&mut hasher);
        request.scan_mode.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }

    /// Get execution statistics
    pub async fn get_statistics(&self) -> PipelineStatistics {
        self.statistics.read().await.clone()
    }

    /// Clear execution cache
    pub async fn clear_cache(&self) {
        let mut cache = self.cache.write().await;
        cache.clear();
        info!("Pipeline execution cache cleared");
    }

    /// Validate orchestrator configuration
    pub fn validate_config(&self) -> Result<Vec<String>> {
        let mut issues = Vec::new();

        if self.config.max_concurrent_pipelines == 0 {
            issues.push("Maximum concurrent pipelines must be greater than 0".to_string());
        }

        if self.config.pipeline_timeout_seconds == 0 {
            issues.push("Pipeline timeout must be greater than 0".to_string());
        }

        Ok(issues)
    }
}

impl Default for PipelineOrchestratorConfig {
    fn default() -> Self {
        Self {
            enable_parallel_execution: true,
            max_concurrent_pipelines: 5,
            pipeline_timeout_seconds: 1800, // 30 minutes
            enable_result_caching: true,
            cache_ttl_seconds: 3600, // 1 hour
            enable_optimization: true,
            enable_monitoring: false,
            monitoring_endpoint: None,
            enable_notifications: false,
            notification_channels: vec![],
        }
    }
}

impl Default for PipelineStatistics {
    fn default() -> Self {
        Self {
            total_execution_time_ms: 0,
            avg_platform_execution_time_ms: 0.0,
            platform_success_rate: 0.0,
            cache_hit_rate: 0.0,
            resource_usage: ResourceUsageStats {
                peak_memory_mb: 0,
                avg_cpu_percent: 0.0,
                total_network_mb: 0,
                io_operations: 0,
            },
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_pipeline_orchestrator_config_default() {
        let config = PipelineOrchestratorConfig::default();
        assert!(config.enable_parallel_execution);
        assert_eq!(config.max_concurrent_pipelines, 5);
        assert_eq!(config.pipeline_timeout_seconds, 1800);
        assert!(config.enable_result_caching);
    }

    #[test]
    fn test_pipeline_statistics_default() {
        let stats = PipelineStatistics::default();
        assert_eq!(stats.total_execution_time_ms, 0);
        assert_eq!(stats.avg_platform_execution_time_ms, 0.0);
        assert_eq!(stats.platform_success_rate, 0.0);
    }

    #[test]
    fn test_cache_key_generation() {
        let orchestrator = PipelineOrchestrator::new(
            PipelineOrchestratorConfig::default(),
            ComplianceConfig::default(),
            ScanningConfig::default(),
        );

        let request = PipelineExecutionRequest {
            id: Uuid::new_v4(),
            platforms: vec![CICDPlaform::Generic],
            config: PipelineConfig {
                base_config: CICDPlaformConfig::default(),
                platform_configs: HashMap::new(),
                global_params: HashMap::new(),
            },
            target: "/test/path".to_string(),
            scan_mode: ScanMode::Full,
            changed_files: None,
            custom_params: HashMap::new(),
            metadata: HashMap::new(),
        };

        let key1 = orchestrator.cache_key(&request);
        let key2 = orchestrator.cache_key(&request);
        assert_eq!(key1, key2); // Same request should generate same key
    }
}