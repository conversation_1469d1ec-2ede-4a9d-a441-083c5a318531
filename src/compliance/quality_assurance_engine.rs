//! # Quality Assurance Engine
//!
//! Comprehensive quality control and validation system for the license compliance platform.
//! Provides input validation and sanitization, output validation and consistency checks,
//! cross-verification mechanisms, and quality metrics and reporting.

use crate::{
    compliance::{
        ComplianceValidationResult, ComplianceRequest, ComplianceReport,
        compliance_validator::{LicenseValidationResult, ValidationEvidence, ValidationSource, ValidationStatus},
    },
    error::{InfinitumError, Result},
    scanners::{ScanR<PERSON>ult, SoftwareComponent},
};
use serde::{Deserialize, Serialize};
use std::{
    collections::{HashMap, HashSet, VecDeque},
    sync::Arc,
};
use tokio::sync::RwLock;
use tracing::{debug, info, instrument, warn};
use chrono::{DateTime, Utc};
use regex::Regex;

/// Quality assurance configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityAssuranceConfig {
    /// Enable input validation
    pub enable_input_validation: bool,
    /// Enable output validation
    pub enable_output_validation: bool,
    /// Enable cross-verification
    pub enable_cross_verification: bool,
    /// Strict validation mode
    pub strict_mode: bool,
    /// Quality threshold (0-1)
    pub quality_threshold: f64,
    /// Maximum validation time (seconds)
    pub max_validation_time_secs: u64,
    /// Enable sanitization
    pub enable_sanitization: bool,
    /// Enable consistency checks
    pub enable_consistency_checks: bool,
    /// Quality metrics retention period (hours)
    pub metrics_retention_hours: u64,
}

/// Input validation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InputValidationResult {
    /// Whether input is valid
    pub is_valid: bool,
    /// Validation errors
    pub errors: Vec<String>,
    /// Validation warnings
    pub warnings: Vec<String>,
    /// Sanitized input (if applicable)
    pub sanitized_input: Option<serde_json::Value>,
    /// Validation score (0-1)
    pub validation_score: f64,
    /// Processing time (milliseconds)
    pub processing_time_ms: u64,
}

/// Output validation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OutputValidationResult {
    /// Whether output is valid
    pub is_valid: bool,
    /// Validation errors
    pub errors: Vec<String>,
    /// Validation warnings
    pub warnings: Vec<String>,
    /// Consistency score (0-1)
    pub consistency_score: f64,
    /// Quality score (0-1)
    pub quality_score: f64,
    /// Validation metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Cross-verification result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CrossVerificationResult {
    /// Overall verification confidence (0-1)
    pub verification_confidence: f64,
    /// Verification methods used
    pub methods_used: Vec<String>,
    /// Conflicting results
    pub conflicts: Vec<VerificationConflict>,
    /// Consensus result
    pub consensus_result: Option<serde_json::Value>,
    /// Verification metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Verification conflict
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VerificationConflict {
    /// Conflict description
    pub description: String,
    /// Conflicting values
    pub conflicting_values: Vec<serde_json::Value>,
    /// Sources of conflict
    pub sources: Vec<String>,
    /// Severity of conflict
    pub severity: ConflictSeverity,
}

/// Conflict severity
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ConflictSeverity {
    /// Low severity conflict
    Low,
    /// Medium severity conflict
    Medium,
    /// High severity conflict
    High,
    /// Critical conflict
    Critical,
}

/// Quality metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityMetrics {
    /// Overall quality score (0-1)
    pub overall_quality_score: f64,
    /// Input validation score
    pub input_validation_score: f64,
    /// Output validation score
    pub output_validation_score: f64,
    /// Cross-verification score
    pub cross_verification_score: f64,
    /// Consistency score
    pub consistency_score: f64,
    /// Error rate
    pub error_rate: f64,
    /// Processing time statistics
    pub processing_stats: ProcessingStatistics,
    /// Quality trends
    pub quality_trends: QualityTrends,
    /// Last updated
    pub last_updated: DateTime<Utc>,
}

/// Processing statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessingStatistics {
    /// Average processing time (milliseconds)
    pub avg_processing_time_ms: f64,
    /// 95th percentile processing time
    pub p95_processing_time_ms: f64,
    /// 99th percentile processing time
    pub p99_processing_time_ms: f64,
    /// Total validations processed
    pub total_validations: u64,
    /// Validations per second
    pub validations_per_second: f64,
}

/// Quality trends
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityTrends {
    /// Quality score trend (improving/degrading/stable)
    pub quality_trend: TrendDirection,
    /// Trend slope
    pub trend_slope: f64,
    /// Recent average quality
    pub recent_average: f64,
    /// Quality volatility
    pub volatility: f64,
}

/// Trend direction
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum TrendDirection {
    /// Improving
    Improving,
    /// Degrading
    Degrading,
    /// Stable
    Stable,
}

/// Validation rule
#[derive(Debug, Clone)]
pub struct ValidationRule {
    /// Rule name
    pub name: String,
    /// Rule type
    pub rule_type: ValidationRuleType,
    /// Validation function
    pub validator: Box<dyn Fn(&serde_json::Value) -> Result<ValidationResult> + Send + Sync>,
}

/// Validation rule type
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum ValidationRuleType {
    /// Input validation
    Input,
    /// Output validation
    Output,
    /// Consistency validation
    Consistency,
    /// Security validation
    Security,
}

/// Validation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationResult {
    /// Whether validation passed
    pub is_valid: bool,
    /// Validation message
    pub message: String,
    /// Validation score (0-1)
    pub score: f64,
    /// Validation metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Quality assurance engine
pub struct QualityAssuranceEngine {
    config: QualityAssuranceConfig,
    validation_rules: Vec<ValidationRule>,
    quality_history: Arc<RwLock<VecDeque<QualityMetrics>>>,
    input_patterns: Arc<RwLock<HashMap<String, Regex>>>,
    sanitization_rules: Vec<SanitizationRule>,
}

/// Sanitization rule
#[derive(Debug, Clone)]
pub struct SanitizationRule {
    /// Rule name
    pub name: String,
    /// Pattern to match
    pub pattern: Regex,
    /// Replacement string
    pub replacement: String,
    /// Rule priority
    pub priority: u32,
}

impl QualityAssuranceEngine {
    /// Create new quality assurance engine
    pub fn new(config: QualityAssuranceConfig) -> Self {
        let mut engine = Self {
            config,
            validation_rules: Vec::new(),
            quality_history: Arc::new(RwLock::new(VecDeque::with_capacity(1000))),
            input_patterns: Arc::new(RwLock::new(HashMap::new())),
            sanitization_rules: Vec::new(),
        };

        engine.initialize_validation_rules();
        engine.initialize_sanitization_rules();
        engine.initialize_input_patterns();
        engine
    }

    /// Initialize default validation rules
    fn initialize_validation_rules(&mut self) {
        // Input validation rules
        self.validation_rules.push(ValidationRule {
            name: "License Expression Format".to_string(),
            rule_type: ValidationRuleType::Input,
            validator: Box::new(|input| Self::validate_license_expression(input)),
        });

        self.validation_rules.push(ValidationRule {
            name: "Component Name Format".to_string(),
            rule_type: ValidationRuleType::Input,
            validator: Box::new(|input| Self::validate_component_name(input)),
        });

        // Output validation rules
        self.validation_rules.push(ValidationRule {
            name: "Compliance Score Range".to_string(),
            rule_type: ValidationRuleType::Output,
            validator: Box::new(|output| Self::validate_compliance_score_range(output)),
        });

        self.validation_rules.push(ValidationRule {
            name: "Report Structure".to_string(),
            rule_type: ValidationRuleType::Output,
            validator: Box::new(|output| Self::validate_report_structure(output)),
        });

        // Consistency validation rules
        self.validation_rules.push(ValidationRule {
            name: "License Consistency".to_string(),
            rule_type: ValidationRuleType::Consistency,
            validator: Box::new(|data| Self::validate_license_consistency(data)),
        });

        // Security validation rules
        self.validation_rules.push(ValidationRule {
            name: "Path Traversal Prevention".to_string(),
            rule_type: ValidationRuleType::Security,
            validator: Box::new(|input| Self::validate_path_traversal(input)),
        });
    }

    /// Initialize sanitization rules
    fn initialize_sanitization_rules(&mut self) {
        // Remove potentially dangerous characters
        self.sanitization_rules.push(SanitizationRule {
            name: "Dangerous Characters".to_string(),
            pattern: Regex::new(r#"[<>";]"#).unwrap(),
            replacement: "".to_string(),
            priority: 1,
        });

        // Normalize whitespace
        self.sanitization_rules.push(SanitizationRule {
            name: "Whitespace Normalization".to_string(),
            pattern: Regex::new(r"\s+").unwrap(),
            replacement: " ".to_string(),
            priority: 2,
        });

        // Remove null bytes
        self.sanitization_rules.push(SanitizationRule {
            name: "Null Byte Removal".to_string(),
            pattern: Regex::new(r"\x00").unwrap(),
            replacement: "".to_string(),
            priority: 3,
        });
    }

    /// Initialize input patterns
    fn initialize_input_patterns(&mut self) {
        let mut patterns = self.input_patterns.try_write().unwrap();

        // SPDX license identifier pattern
        patterns.insert(
            "spdx_license".to_string(),
            Regex::new(r"^[-.\w]+$").unwrap(),
        );

        // License expression pattern (simplified)
        patterns.insert(
            "license_expression".to_string(),
            Regex::new(r"^[\w\s\-\+\.\(\)ANDORWITH]+$").unwrap(),
        );

        // Component name pattern
        patterns.insert(
            "component_name".to_string(),
            Regex::new(r#"^[^<>";]+$"#).unwrap(),
        );
    }

    /// Validate input data
    #[instrument(skip(self, input))]
    pub async fn validate_input(&self, input: &serde_json::Value) -> Result<InputValidationResult> {
        if !self.config.enable_input_validation {
            return Ok(InputValidationResult {
                is_valid: true,
                errors: vec![],
                warnings: vec![],
                sanitized_input: None,
                validation_score: 1.0,
                processing_time_ms: 0,
            });
        }

        let start_time = std::time::Instant::now();
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        let mut validation_score = 1.0;

        // Apply input validation rules
        for rule in &self.validation_rules {
            if matches!(rule.rule_type, ValidationRuleType::Input) {
                match (rule.validator)(input) {
                    Ok(result) => {
                        if !result.is_valid {
                            errors.push(format!("{}: {}", rule.name, result.message));
                            validation_score *= result.score;
                        }
                    }
                    Err(e) => {
                        errors.push(format!("{} validation failed: {}", rule.name, e));
                        validation_score *= 0.5;
                    }
                }
            }
        }

        // Sanitize input if enabled
        let sanitized_input = if self.config.enable_sanitization {
            Some(self.sanitize_input(input).await?)
        } else {
            None
        };

        // Additional security checks
        if let Some(obj) = input.as_object() {
            for (key, value) in obj {
                if key.contains("..") || key.contains("/") || key.contains("\\") {
                    warnings.push(format!("Potentially unsafe key: {}", key));
                    validation_score *= 0.9;
                }

                if let Some(str_val) = value.as_str() {
                    if str_val.contains("..") || str_val.contains("../") || str_val.contains("..\\") {
                        errors.push(format!("Path traversal attempt detected in field: {}", key));
                        validation_score *= 0.1;
                    }
                }
            }
        }

        let processing_time = start_time.elapsed().as_millis() as u64;

        let is_valid = errors.is_empty() || (!self.config.strict_mode && validation_score >= self.config.quality_threshold);

        Ok(InputValidationResult {
            is_valid,
            errors,
            warnings,
            sanitized_input,
            validation_score,
            processing_time_ms: processing_time,
        })
    }

    /// Validate output data
    #[instrument(skip(self, output))]
    pub async fn validate_output(&self, output: &serde_json::Value) -> Result<OutputValidationResult> {
        if !self.config.enable_output_validation {
            return Ok(OutputValidationResult {
                is_valid: true,
                errors: vec![],
                warnings: vec![],
                consistency_score: 1.0,
                quality_score: 1.0,
                metadata: HashMap::new(),
            });
        }

        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        let mut consistency_score = 1.0;
        let mut quality_score = 1.0;
        let mut metadata = HashMap::new();

        // Apply output validation rules
        for rule in &self.validation_rules {
            if matches!(rule.rule_type, ValidationRuleType::Output) {
                match (rule.validator)(output) {
                    Ok(result) => {
                        if !result.is_valid {
                            errors.push(format!("{}: {}", rule.name, result.message));
                            quality_score *= result.score;
                        }
                        // Store validation metadata
                        metadata.extend(result.metadata);
                    }
                    Err(e) => {
                        errors.push(format!("{} validation failed: {}", rule.name, e));
                        quality_score *= 0.5;
                    }
                }
            }
        }

        // Perform consistency checks if enabled
        if self.config.enable_consistency_checks {
            let consistency_result = self.perform_consistency_checks(output).await?;
            consistency_score = consistency_result.consistency_score;

            if consistency_score < 0.8 {
                warnings.push(format!("Low consistency score: {:.2}", consistency_score));
            }

            metadata.insert("consistency_details".to_string(), serde_json::json!(consistency_result));
        }

        let is_valid = errors.is_empty() && quality_score >= self.config.quality_threshold;

        Ok(OutputValidationResult {
            is_valid,
            errors,
            warnings,
            consistency_score,
            quality_score,
            metadata,
        })
    }

    /// Perform cross-verification
    #[instrument(skip(self, primary_result, secondary_results))]
    pub async fn perform_cross_verification(
        &self,
        primary_result: &serde_json::Value,
        secondary_results: &[serde_json::Value],
    ) -> Result<CrossVerificationResult> {
        if !self.config.enable_cross_verification {
            return Ok(CrossVerificationResult {
                verification_confidence: 1.0,
                methods_used: vec!["primary_only".to_string()],
                conflicts: vec![],
                consensus_result: Some(primary_result.clone()),
                metadata: HashMap::new(),
            });
        }

        let mut conflicts = Vec::new();
        let mut verification_confidence = 1.0;
        let mut methods_used = vec!["primary".to_string()];
        let mut metadata = HashMap::new();

        // Compare primary result with secondary results
        for (i, secondary) in secondary_results.iter().enumerate() {
            methods_used.push(format!("secondary_{}", i + 1));

            let comparison = self.compare_results(primary_result, secondary).await?;
            metadata.insert(format!("comparison_{}", i + 1), serde_json::json!(comparison));

            if let Some(conflict) = comparison.conflict {
                conflicts.push(conflict);
                verification_confidence *= comparison.similarity_score;
            } else {
                verification_confidence *= comparison.similarity_score;
            }
        }

        // Determine consensus result
        let consensus_result = if conflicts.is_empty() && !secondary_results.is_empty() {
            // All results agree, use primary
            Some(primary_result.clone())
        } else if conflicts.len() < secondary_results.len() / 2 {
            // Majority agreement, use primary with modifications
            Some(primary_result.clone())
        } else {
            // Significant conflicts, no consensus
            None
        };

        Ok(CrossVerificationResult {
            verification_confidence,
            methods_used,
            conflicts,
            consensus_result,
            metadata,
        })
    }

    /// Sanitize input data
    async fn sanitize_input(&self, input: &serde_json::Value) -> Result<serde_json::Value> {
        let mut sanitized = input.clone();

        // Apply sanitization rules to string values
        if let Some(obj) = sanitized.as_object_mut() {
            for (_, value) in obj.iter_mut() {
                if let Some(str_val) = value.as_str() {
                    let mut sanitized_str = str_val.to_string();

                    // Apply sanitization rules in priority order
                    for rule in &self.sanitization_rules {
                        sanitized_str = rule.pattern.replace_all(&sanitized_str, &rule.replacement).to_string();
                    }

                    *value = serde_json::Value::String(sanitized_str);
                }
            }
        }

        Ok(sanitized)
    }

    /// Perform consistency checks
    async fn perform_consistency_checks(&self, output: &serde_json::Value) -> Result<ConsistencyCheckResult> {
        let mut consistency_score = 1.0;
        let mut issues = Vec::new();

        // Apply consistency validation rules
        for rule in &self.validation_rules {
            if matches!(rule.rule_type, ValidationRuleType::Consistency) {
                match (rule.validator)(output) {
                    Ok(result) => {
                        if !result.is_valid {
                            issues.push(result.message);
                            consistency_score *= result.score;
                        }
                    }
                    Err(e) => {
                        issues.push(format!("Consistency check failed: {}", e));
                        consistency_score *= 0.5;
                    }
                }
            }
        }

        // Check for logical inconsistencies in the data
        if let Some(obj) = output.as_object() {
            // Example: Check if compliance score matches finding severities
            if let (Some(score), Some(findings)) = (
                obj.get("compliance_score").and_then(|s| s.as_f64()),
                obj.get("findings").and_then(|f| f.as_array()),
            ) {
                let high_severity_findings = findings.iter()
                    .filter(|f| f.get("severity").and_then(|s| s.as_str()) == Some("high"))
                    .count();

                if score > 0.8 && high_severity_findings > 0 {
                    issues.push("High compliance score with high-severity findings".to_string());
                    consistency_score *= 0.9;
                }
            }
        }

        Ok(ConsistencyCheckResult {
            consistency_score,
            issues,
            checks_performed: self.validation_rules.iter()
                .filter(|r| matches!(r.rule_type, ValidationRuleType::Consistency))
                .count(),
        })
    }

    /// Compare two results for cross-verification
    async fn compare_results(&self, result1: &serde_json::Value, result2: &serde_json::Value) -> Result<ResultComparison> {
        let mut similarity_score = 1.0;
        let mut conflict = None;

        // Simple comparison - check if key values match
        if let (Some(obj1), Some(obj2)) = (result1.as_object(), result2.as_object()) {
            let keys1: HashSet<_> = obj1.keys().collect();
            let keys2: HashSet<_> = obj2.keys().collect();

            // Check key overlap
            let intersection: HashSet<_> = keys1.intersection(&keys2).collect();
            let union: HashSet<_> = keys1.union(&keys2).collect();
            let overlap_score = intersection.len() as f64 / union.len() as f64;

            similarity_score *= overlap_score;

            // Check value consistency for common keys
            for key in &intersection {
                let val1 = &obj1[*key];
                let val2 = &obj2[*key];

                if val1 != val2 {
                    // Values differ - check if it's a significant conflict
                    if self.is_significant_difference(val1, val2) {
                        conflict = Some(VerificationConflict {
                            description: format!("Value mismatch for key: {}", key),
                            conflicting_values: vec![val1.clone(), val2.clone()],
                            sources: vec!["primary".to_string(), "secondary".to_string()],
                            severity: ConflictSeverity::Medium,
                        });
                        similarity_score *= 0.7;
                    } else {
                        similarity_score *= 0.9;
                    }
                }
            }
        } else {
            // Different types - significant difference
            conflict = Some(VerificationConflict {
                description: "Result type mismatch".to_string(),
                conflicting_values: vec![result1.clone(), result2.clone()],
                sources: vec!["primary".to_string(), "secondary".to_string()],
                severity: ConflictSeverity::High,
            });
            similarity_score *= 0.5;
        }

        Ok(ResultComparison {
            similarity_score,
            conflict,
            comparison_metadata: HashMap::new(),
        })
    }

    /// Check if difference between values is significant
    fn is_significant_difference(&self, val1: &serde_json::Value, val2: &serde_json::Value) -> bool {
        match (val1, val2) {
            (serde_json::Value::Number(n1), serde_json::Value::Number(n2)) => {
                // Significant if difference > 10%
                let diff = ((n1.as_f64().unwrap_or(0.0) - n2.as_f64().unwrap_or(0.0)).abs() /
                           n1.as_f64().unwrap_or(1.0)).abs();
                diff > 0.1
            }
            (serde_json::Value::String(s1), serde_json::Value::String(s2)) => {
                // Significant if strings are very different
                let similarity = strsim::jaro_winkler(s1, s2);
                similarity < 0.8
            }
            _ => true, // Different types are significant
        }
    }

    /// Update quality metrics
    #[instrument(skip(self, input_result, output_result, cross_result))]
    pub async fn update_quality_metrics(
        &self,
        input_result: &InputValidationResult,
        output_result: &OutputValidationResult,
        cross_result: &CrossVerificationResult,
    ) -> Result<()> {
        let overall_quality_score = (input_result.validation_score +
                                   output_result.quality_score +
                                   cross_result.verification_confidence) / 3.0;

        let metrics = QualityMetrics {
            overall_quality_score,
            input_validation_score: input_result.validation_score,
            output_validation_score: output_result.quality_score,
            cross_verification_score: cross_result.verification_confidence,
            consistency_score: output_result.consistency_score,
            error_rate: if input_result.is_valid && output_result.is_valid { 0.0 } else { 1.0 },
            processing_stats: ProcessingStatistics {
                avg_processing_time_ms: (input_result.processing_time_ms as f64 + 10.0) / 2.0, // Placeholder
                p95_processing_time_ms: input_result.processing_time_ms as f64 * 1.5,
                p99_processing_time_ms: input_result.processing_time_ms as f64 * 2.0,
                total_validations: 1,
                validations_per_second: 1000.0 / input_result.processing_time_ms as f64,
            },
            quality_trends: self.calculate_quality_trends().await?,
            last_updated: Utc::now(),
        };

        let mut history = self.quality_history.write().await;
        history.push_back(metrics);

        // Keep only recent history
        let cutoff_time = Utc::now() - chrono::Duration::hours(self.config.metrics_retention_hours as i64);
        while let Some(oldest) = history.front() {
            if oldest.last_updated < cutoff_time {
                history.pop_front();
            } else {
                break;
            }
        }

        Ok(())
    }

    /// Calculate quality trends
    async fn calculate_quality_trends(&self) -> Result<QualityTrends> {
        let history = self.quality_history.read().await;

        if history.is_empty() {
            return Ok(QualityTrends {
                quality_trend: TrendDirection::Stable,
                trend_slope: 0.0,
                recent_average: 0.0,
                volatility: 0.0,
            });
        }

        let recent_values: Vec<f64> = history.iter().rev().take(10).map(|m| m.overall_quality_score).collect();
        let recent_average = recent_values.iter().sum::<f64>() / recent_values.len() as f64;

        // Calculate trend
        let n = recent_values.len() as f64;
        let slope = if n > 1.0 {
            let x_sum: f64 = (0..recent_values.len()).map(|i| i as f64).sum();
            let y_sum: f64 = recent_values.iter().sum();
            let xy_sum: f64 = recent_values.iter().enumerate().map(|(i, &y)| i as f64 * y).sum();
            let x_squared_sum: f64 = (0..recent_values.len()).map(|i| (i as f64).powi(2)).sum();

            (n * xy_sum - x_sum * y_sum) / (n * x_squared_sum - x_sum.powi(2))
        } else {
            0.0
        };

        let quality_trend = if slope > 0.001 {
            TrendDirection::Improving
        } else if slope < -0.001 {
            TrendDirection::Degrading
        } else {
            TrendDirection::Stable
        };

        // Calculate volatility
        let variance = recent_values.iter()
            .map(|v| (v - recent_average).powi(2))
            .sum::<f64>() / n;
        let volatility = variance.sqrt();

        Ok(QualityTrends {
            quality_trend,
            trend_slope: slope,
            recent_average,
            volatility,
        })
    }

    /// Get current quality metrics
    pub async fn get_quality_metrics(&self) -> Option<QualityMetrics> {
        let history = self.quality_history.read().await;
        history.back().cloned()
    }

    /// Add custom validation rule
    pub fn add_validation_rule(&mut self, rule: ValidationRule) {
        self.validation_rules.push(rule);
    }

    /// Get validation rules
    pub fn get_validation_rules(&self) -> &[ValidationRule] {
        &self.validation_rules
    }

    // Validation rule implementations
    fn validate_license_expression(input: &serde_json::Value) -> Result<ValidationResult> {
        if let Some(license) = input.get("license").and_then(|l| l.as_str()) {
            let patterns = HashMap::from([
                ("spdx_license", Regex::new(r"^[-.\w]+$").unwrap()),
                ("license_expression", Regex::new(r"^[\w\s\-\+\.\(\)ANDORWITH]+$").unwrap()),
            ]);

            for (pattern_name, pattern) in &patterns {
                if pattern.is_match(license) {
                    return Ok(ValidationResult {
                        is_valid: true,
                        message: format!("Valid {} format", pattern_name),
                        score: 1.0,
                        metadata: HashMap::from([
                            ("pattern_matched".to_string(), serde_json::json!(pattern_name)),
                        ]),
                    });
                }
            }

            Ok(ValidationResult {
                is_valid: false,
                message: "Invalid license expression format".to_string(),
                score: 0.3,
                metadata: HashMap::new(),
            })
        } else {
            Ok(ValidationResult {
                is_valid: false,
                message: "Missing license field".to_string(),
                score: 0.0,
                metadata: HashMap::new(),
            })
        }
    }

    fn validate_component_name(input: &serde_json::Value) -> Result<ValidationResult> {
        if let Some(name) = input.get("name").and_then(|n| n.as_str()) {
            let pattern = Regex::new(r#"^[^<>";]+$"#).unwrap();

            if pattern.is_match(name) {
                Ok(ValidationResult {
                    is_valid: true,
                    message: "Valid component name format".to_string(),
                    score: 1.0,
                    metadata: HashMap::new(),
                })
            } else {
                Ok(ValidationResult {
                    is_valid: false,
                    message: "Invalid component name format".to_string(),
                    score: 0.5,
                    metadata: HashMap::new(),
                })
            }
        } else {
            Ok(ValidationResult {
                is_valid: false,
                message: "Missing component name".to_string(),
                score: 0.0,
                metadata: HashMap::new(),
            })
        }
    }

    fn validate_compliance_score_range(output: &serde_json::Value) -> Result<ValidationResult> {
        if let Some(score) = output.get("compliance_score").and_then(|s| s.as_f64()) {
            if (0.0..=1.0).contains(&score) {
                Ok(ValidationResult {
                    is_valid: true,
                    message: format!("Valid compliance score: {:.3}", score),
                    score: 1.0,
                    metadata: HashMap::from([
                        ("score_value".to_string(), serde_json::json!(score)),
                    ]),
                })
            } else {
                Ok(ValidationResult {
                    is_valid: false,
                    message: format!("Compliance score out of range: {:.3}", score),
                    score: 0.1,
                    metadata: HashMap::new(),
                })
            }
        } else {
            Ok(ValidationResult {
                is_valid: false,
                message: "Missing compliance score".to_string(),
                score: 0.0,
                metadata: HashMap::new(),
            })
        }
    }

    fn validate_report_structure(output: &serde_json::Value) -> Result<ValidationResult> {
        let required_fields = ["compliance_score", "findings", "summary"];
        let mut missing_fields = Vec::new();

        for field in &required_fields {
            if output.get(*field).is_none() {
                missing_fields.push(*field);
            }
        }

        if missing_fields.is_empty() {
            Ok(ValidationResult {
                is_valid: true,
                message: "Valid report structure".to_string(),
                score: 1.0,
                metadata: HashMap::from([
                    ("field_count".to_string(), serde_json::json!(output.as_object().map(|o| o.len()).unwrap_or(0))),
                ]),
            })
        } else {
            Ok(ValidationResult {
                is_valid: false,
                message: format!("Missing required fields: {:?}", missing_fields),
                score: 0.5,
                metadata: HashMap::from([
                    ("missing_fields".to_string(), serde_json::json!(missing_fields)),
                ]),
            })
        }
    }

    fn validate_license_consistency(data: &serde_json::Value) -> Result<ValidationResult> {
        // Check if license information is consistent across different parts
        let mut consistency_score = 1.0;
        let mut issues = Vec::new();

        if let Some(findings) = data.get("findings").and_then(|f| f.as_array()) {
            let mut licenses = HashSet::new();

            for finding in findings {
                if let Some(license) = finding.get("license").and_then(|l| l.as_str()) {
                    licenses.insert(license);
                }
            }

            if licenses.len() > 1 {
                consistency_score *= 0.8;
                issues.push("Multiple different licenses found in findings".to_string());
            }
        }

        Ok(ValidationResult {
            is_valid: consistency_score >= 0.7,
            message: if issues.is_empty() {
                "License information is consistent".to_string()
            } else {
                format!("License consistency issues: {:?}", issues)
            },
            score: consistency_score,
            metadata: HashMap::from([
                ("consistency_issues".to_string(), serde_json::json!(issues)),
            ]),
        })
    }

    fn validate_path_traversal(input: &serde_json::Value) -> Result<ValidationResult> {
        let mut suspicious_paths = Vec::new();

        if let Some(obj) = input.as_object() {
            for (key, value) in obj {
                if let Some(str_val) = value.as_str() {
                    if str_val.contains("../") || str_val.contains("..\\") || str_val.contains("..") {
                        suspicious_paths.push(key.clone());
                    }
                }
            }
        }

        if suspicious_paths.is_empty() {
            Ok(ValidationResult {
                is_valid: true,
                message: "No path traversal attempts detected".to_string(),
                score: 1.0,
                metadata: HashMap::new(),
            })
        } else {
            Ok(ValidationResult {
                is_valid: false,
                message: format!("Path traversal attempts detected in fields: {:?}", suspicious_paths),
                score: 0.1,
                metadata: HashMap::from([
                    ("suspicious_fields".to_string(), serde_json::json!(suspicious_paths)),
                ]),
            })
        }
    }
}

/// Result comparison for cross-verification
#[derive(Debug)]
struct ResultComparison {
    similarity_score: f64,
    conflict: Option<VerificationConflict>,
    comparison_metadata: HashMap<String, serde_json::Value>,
}

/// Consistency check result
#[derive(Debug, Serialize, Deserialize)]
struct ConsistencyCheckResult {
    consistency_score: f64,
    issues: Vec<String>,
    checks_performed: usize,
}

impl Default for QualityAssuranceConfig {
    fn default() -> Self {
        Self {
            enable_input_validation: true,
            enable_output_validation: true,
            enable_cross_verification: true,
            strict_mode: false,
            quality_threshold: 0.8,
            max_validation_time_secs: 30,
            enable_sanitization: true,
            enable_consistency_checks: true,
            metrics_retention_hours: 24,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_input_validation() {
        let config = QualityAssuranceConfig::default();
        let engine = QualityAssuranceEngine::new(config);

        let input = serde_json::json!({
            "name": "test-component",
            "license": "MIT"
        });

        let result = engine.validate_input(&input).await.unwrap();
        assert!(result.is_valid);
        assert!(result.validation_score > 0.8);
    }

    #[tokio::test]
    async fn test_output_validation() {
        let config = QualityAssuranceConfig::default();
        let engine = QualityAssuranceEngine::new(config);

        let output = serde_json::json!({
            "compliance_score": 0.85,
            "findings": [],
            "summary": {}
        });

        let result = engine.validate_output(&output).await.unwrap();
        assert!(result.is_valid);
        assert!(result.quality_score > 0.8);
    }

    #[tokio::test]
    async fn test_cross_verification() {
        let config = QualityAssuranceConfig::default();
        let engine = QualityAssuranceEngine::new(config);

        let primary = serde_json::json!({"score": 0.9});
        let secondary = vec![serde_json::json!({"score": 0.85})];

        let result = engine.perform_cross_verification(&primary, &secondary).await.unwrap();
        assert!(result.verification_confidence > 0.7);
    }

    #[test]
    fn test_validation_rules() {
        let input = serde_json::json!({"license": "MIT"});
        let result = QualityAssuranceEngine::validate_license_expression(&input).unwrap();
        assert!(result.is_valid);

        let invalid_input = serde_json::json!({"license": "INVALID<>LICENSE"});
        let result = QualityAssuranceEngine::validate_license_expression(&invalid_input).unwrap();
        assert!(!result.is_valid);
    }
}