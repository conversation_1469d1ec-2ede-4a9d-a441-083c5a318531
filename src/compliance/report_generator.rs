//! # Report Generator Module
//!
//! This module provides a comprehensive report generation system for license compliance tracking.
//! It includes generators for SPDX, notice files, compliance reports, and supports multiple output formats.

use crate::{
    compliance::{ComplianceReport, ComplianceRequest, OutputFormat},
    config::ComplianceConfig,
    error::{InfinitumError, Result},
    scanners::ScanResult,
};
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, path::Path};
use uuid::Uuid;

/// Common trait for all report generators
#[async_trait]
pub trait ReportGenerator: Send + Sync {
    /// Generate a report from compliance request and scan results
    async fn generate_report(
        &self,
        request: &ComplianceRequest,
        scan_results: &[ScanResult],
    ) -> Result<GeneratedReport>;

    /// Get supported output formats
    fn supported_formats(&self) -> Vec<OutputFormat>;

    /// Get generator name
    fn name(&self) -> &'static str;

    /// Get generator description
    fn description(&self) -> &'static str;

    /// Validate if the generator can handle the given request
    fn can_handle(&self, request: &ComplianceRequest) -> bool;
}

/// Generated report result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeneratedReport {
    /// Report identifier
    pub id: Uuid,
    /// Report type
    pub report_type: ReportType,
    /// Generated files with their paths
    pub files: HashMap<OutputFormat, String>,
    /// Report metadata
    pub metadata: HashMap<String, serde_json::Value>,
    /// Generation timestamp
    pub generated_at: chrono::DateTime<chrono::Utc>,
    /// Generation duration in milliseconds
    pub duration_ms: u64,
}

/// Types of reports that can be generated
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ReportType {
    /// SPDX Software Bill of Materials
    Spdx,
    /// CycloneDX Software Bill of Materials
    CycloneDx,
    /// License notice file
    Notice,
    /// Detailed compliance report
    Compliance,
    /// Combined multi-format report
    Combined,
}

/// Base configuration for report generators
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReportGeneratorConfig {
    /// Output directory for generated reports
    pub output_dir: String,
    /// Default output formats
    pub default_formats: Vec<OutputFormat>,
    /// Template directory
    pub template_dir: Option<String>,
    /// Include metadata in reports
    pub include_metadata: bool,
    /// Include evidence in reports
    pub include_evidence: bool,
    /// Enable internationalization
    pub enable_i18n: bool,
    /// Default language for reports
    pub default_language: String,
    /// Creator information
    pub creator: CreatorInfo,
}

/// Creator information for reports
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreatorInfo {
    /// Creator name
    pub name: String,
    /// Creator organization
    pub organization: String,
    /// Creator contact information
    pub contact: Option<String>,
    /// Tool name and version
    pub tool: String,
}

/// Base report generator implementation with common functionality
pub struct BaseReportGenerator {
    config: ReportGeneratorConfig,
    compliance_config: ComplianceConfig,
}

impl BaseReportGenerator {
    /// Create new base generator
    pub fn new(config: ReportGeneratorConfig, compliance_config: ComplianceConfig) -> Self {
        Self {
            config,
            compliance_config,
        }
    }

    /// Get output directory path
    pub fn output_dir(&self) -> &Path {
        Path::new(&self.config.output_dir)
    }

    /// Generate unique filename for report
    pub fn generate_filename(&self, report_type: &str, format: &OutputFormat, request_id: Uuid) -> String {
        let extension = match format {
            OutputFormat::Json => "json",
            OutputFormat::Xml => "xml",
            OutputFormat::Html => "html",
            OutputFormat::Pdf => "pdf",
            OutputFormat::Csv => "csv",
            OutputFormat::CycloneDx => "json",
            OutputFormat::Spdx => "json",
        };

        format!("{}_{}.{}", report_type, request_id, extension)
    }

    /// Create output file path
    pub fn create_output_path(&self, filename: &str) -> String {
        self.output_dir().join(filename).to_string_lossy().to_string()
    }

    /// Write content to file
    pub async fn write_file(&self, path: &str, content: &str) -> Result<()> {
        tokio::fs::create_dir_all(self.output_dir()).await?;
        tokio::fs::write(path, content).await?;
        Ok(())
    }

    /// Write JSON content to file
    pub async fn write_json_file<T: Serialize>(&self, path: &str, data: &T) -> Result<()> {
        let content = serde_json::to_string_pretty(data)?;
        self.write_file(path, &content).await
    }

    /// Generate report metadata
    pub fn generate_metadata(&self, request: &ComplianceRequest) -> HashMap<String, serde_json::Value> {
        let mut metadata = HashMap::new();

        metadata.insert("request_id".to_string(), serde_json::json!(request.id));
        metadata.insert("framework".to_string(), serde_json::json!(request.framework));
        metadata.insert("organization".to_string(), serde_json::json!(request.config.organization));
        metadata.insert("author".to_string(), serde_json::json!(request.config.author));
        metadata.insert("generated_by".to_string(), serde_json::json!(self.config.creator.tool));
        metadata.insert("generator_version".to_string(), serde_json::json!(env!("CARGO_PKG_VERSION")));

        if self.config.include_metadata {
            metadata.insert("scan_results_count".to_string(), serde_json::json!(request.scan_results.len()));
            metadata.insert("output_formats".to_string(), serde_json::json!(request.config.output_formats));
        }

        metadata
    }

    /// Validate output directory
    pub async fn validate_output_dir(&self) -> Result<()> {
        let path = self.output_dir();
        if !path.exists() {
            tokio::fs::create_dir_all(path).await?;
        }

        // Check if directory is writable
        let test_file = path.join(".write_test");
        tokio::fs::write(&test_file, "test").await?;
        tokio::fs::remove_file(&test_file).await?;

        Ok(())
    }

    /// Get compliance configuration
    pub fn compliance_config(&self) -> &ComplianceConfig {
        &self.compliance_config
    }

    /// Get generator configuration
    pub fn generator_config(&self) -> &ReportGeneratorConfig {
        &self.config
    }
}

impl Default for ReportGeneratorConfig {
    fn default() -> Self {
        Self {
            output_dir: "reports".to_string(),
            default_formats: vec![OutputFormat::Json, OutputFormat::Html],
            template_dir: None,
            include_metadata: true,
            include_evidence: true,
            enable_i18n: false,
            default_language: "en".to_string(),
            creator: CreatorInfo {
                name: "Infinitium Signal".to_string(),
                organization: "Infinitium Signal".to_string(),
                contact: None,
                tool: format!("infinitum-signal/{}", env!("CARGO_PKG_VERSION")),
            },
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::compliance::ComplianceFramework;

    #[test]
    fn test_report_generator_config_default() {
        let config = ReportGeneratorConfig::default();
        assert_eq!(config.output_dir, "reports");
        assert!(config.include_metadata);
        assert!(!config.enable_i18n);
        assert_eq!(config.default_language, "en");
    }

    #[test]
    fn test_base_generator_filename_generation() {
        let config = ReportGeneratorConfig::default();
        let compliance_config = ComplianceConfig::default();
        let generator = BaseReportGenerator::new(config, compliance_config);
        let request_id = Uuid::new_v4();

        let filename = generator.generate_filename("spdx", &OutputFormat::Json, request_id);
        assert!(filename.ends_with(".json"));
        assert!(filename.contains("spdx"));
    }

    #[test]
    fn test_report_type_serialization() {
        let report_type = ReportType::Spdx;
        let serialized = serde_json::to_string(&report_type).unwrap();
        assert_eq!(serialized, "\"spdx\"");
    }
}