//! # Report Orchestrator
//!
//! Coordinates multiple report generation with parallel processing, format selection,
//! output directory management, and template system integration.

use crate::{
    compliance::{
        report_generator::{BaseReportGenerator, GeneratedReport, ReportGenerator, ReportGeneratorConfig, ReportType},
        spdx_report_generator::SpdxReportGenerator,
        notice_file_generator::{NoticeFileGenerator, NoticeFileConfig},
        compliance_report_generator::ComplianceReportGenerator,
        ComplianceRequest, OutputFormat,
    },
    config::ComplianceConfig,
    error::{InfinitumError, Result},
    scanners::ScanResult,
};
use async_trait::async_trait;
use futures::future::join_all;
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, path::Path, sync::Arc};
use tokio::sync::Semaphore;
use tracing::{info, instrument, warn};
use uuid::Uuid;

/// Orchestrator configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReportOrchestratorConfig {
    /// Maximum concurrent report generation tasks
    pub max_concurrent_tasks: usize,
    /// Output directory for all reports
    pub output_dir: String,
    /// Enable parallel processing
    pub enable_parallel: bool,
    /// Template directory
    pub template_dir: Option<String>,
    /// Default report configurations
    pub default_configs: HashMap<ReportType, ReportGeneratorConfig>,
    /// Enable progress tracking
    pub enable_progress_tracking: bool,
}

/// Report generation task
#[derive(Debug, Clone)]
struct ReportGenerationTask {
    /// Task identifier
    id: Uuid,
    /// Report type to generate
    report_type: ReportType,
    /// Request for this report
    request: ComplianceRequest,
    /// Scan results to use
    scan_results: Vec<ScanResult>,
}

/// Report generation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReportGenerationResult {
    /// Task identifier
    pub task_id: Uuid,
    /// Report type
    pub report_type: ReportType,
    /// Generated report (if successful)
    pub report: Option<GeneratedReport>,
    /// Error message (if failed)
    pub error: Option<String>,
    /// Generation duration in milliseconds
    pub duration_ms: u64,
    /// Generation timestamp
    pub generated_at: chrono::DateTime<chrono::Utc>,
}

/// Orchestration summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrchestrationSummary {
    /// Total tasks processed
    pub total_tasks: usize,
    /// Successful generations
    pub successful: usize,
    /// Failed generations
    pub failed: usize,
    /// Total generation time
    pub total_duration_ms: u64,
    /// Results by report type
    pub results_by_type: HashMap<ReportType, TypeSummary>,
    /// Overall status
    pub status: OrchestrationStatus,
}

/// Summary by report type
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TypeSummary {
    /// Number of tasks for this type
    pub count: usize,
    /// Successful generations
    pub successful: usize,
    /// Failed generations
    pub failed: usize,
    /// Average duration
    pub avg_duration_ms: u64,
}

/// Orchestration status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum OrchestrationStatus {
    /// All tasks completed successfully
    Completed,
    /// Some tasks failed
    PartialSuccess,
    /// All tasks failed
    Failed,
    /// Orchestration was cancelled
    Cancelled,
}

/// Report Orchestrator
pub struct ReportOrchestrator {
    config: ReportOrchestratorConfig,
    compliance_config: ComplianceConfig,
    generators: HashMap<ReportType, Arc<dyn ReportGenerator + Send + Sync>>,
    semaphore: Arc<Semaphore>,
}

impl ReportOrchestrator {
    /// Create new report orchestrator
    pub fn new(
        config: ReportOrchestratorConfig,
        compliance_config: ComplianceConfig,
    ) -> Self {
        let mut generators = HashMap::new();

        // Initialize generators with default configurations
        let spdx_config = config.default_configs.get(&ReportType::Spdx)
            .cloned()
            .unwrap_or_else(|| ReportGeneratorConfig {
                output_dir: config.output_dir.clone(),
                default_formats: vec![OutputFormat::Json],
                template_dir: config.template_dir.clone(),
                include_metadata: true,
                include_evidence: true,
                enable_i18n: false,
                default_language: "en".to_string(),
                creator: Default::default(),
            });

        let notice_config = config.default_configs.get(&ReportType::Notice)
            .cloned()
            .unwrap_or_else(|| ReportGeneratorConfig {
                output_dir: config.output_dir.clone(),
                default_formats: vec![OutputFormat::Html],
                template_dir: config.template_dir.clone(),
                include_metadata: true,
                include_evidence: true,
                enable_i18n: false,
                default_language: "en".to_string(),
                creator: Default::default(),
            });

        let compliance_config_gen = config.default_configs.get(&ReportType::Compliance)
            .cloned()
            .unwrap_or_else(|| ReportGeneratorConfig {
                output_dir: config.output_dir.clone(),
                default_formats: vec![OutputFormat::Json, OutputFormat::Html],
                template_dir: config.template_dir.clone(),
                include_metadata: true,
                include_evidence: true,
                enable_i18n: false,
                default_language: "en".to_string(),
                creator: Default::default(),
            });

        // Create generators
        generators.insert(
            ReportType::Spdx,
            Arc::new(SpdxReportGenerator::new(spdx_config, compliance_config.clone())),
        );

        generators.insert(
            ReportType::Notice,
            Arc::new(NoticeFileGenerator::new(
                notice_config,
                compliance_config.clone(),
                NoticeFileConfig::default(),
            )),
        );

        generators.insert(
            ReportType::Compliance,
            Arc::new(ComplianceReportGenerator::new(compliance_config_gen, compliance_config.clone())),
        );

        let semaphore = Arc::new(Semaphore::new(config.max_concurrent_tasks));

        Self {
            config,
            compliance_config,
            generators,
            semaphore,
        }
    }

    /// Generate multiple reports
    #[instrument(skip(self, requests, scan_results))]
    pub async fn generate_reports(
        &self,
        requests: Vec<ComplianceRequest>,
        scan_results: &[ScanResult],
    ) -> Result<OrchestrationSummary> {
        let start_time = chrono::Utc::now();
        info!("Starting report orchestration for {} requests", requests.len());

        // Validate output directory
        self.validate_output_directory().await?;

        // Create tasks
        let tasks = self.create_tasks(requests, scan_results).await?;

        // Execute tasks
        let results = if self.config.enable_parallel {
            self.execute_parallel(tasks).await?
        } else {
            self.execute_sequential(tasks).await?
        };

        // Generate summary
        let end_time = chrono::Utc::now();
        let total_duration = end_time - start_time;

        let summary = self.generate_summary(&results, total_duration.num_milliseconds() as u64).await?;

        info!(
            "Report orchestration completed: {} successful, {} failed",
            summary.successful,
            summary.failed
        );

        Ok(summary)
    }

    /// Generate single report
    pub async fn generate_single_report(
        &self,
        request: ComplianceRequest,
        scan_results: &[ScanResult],
    ) -> Result<GeneratedReport> {
        let tasks = vec![ReportGenerationTask {
            id: Uuid::new_v4(),
            report_type: self.determine_report_type(&request),
            request,
            scan_results: scan_results.to_vec(),
        }];

        let results = self.execute_sequential(tasks).await?;

        if results.is_empty() {
            return Err(InfinitumError::ReportGeneration {
                message: "No reports generated".to_string(),
            });
        }

        let result = &results[0];
        match &result.report {
            Some(report) => Ok(report.clone()),
            None => Err(InfinitumError::ReportGeneration {
                message: result.error.clone().unwrap_or_else(|| "Unknown error".to_string()),
            }),
        }
    }

    /// Add custom generator
    pub fn add_generator(
        &mut self,
        report_type: ReportType,
        generator: Arc<dyn ReportGenerator + Send + Sync>,
    ) {
        self.generators.insert(report_type, generator);
    }

    /// Remove generator
    pub fn remove_generator(&mut self, report_type: &ReportType) {
        self.generators.remove(report_type);
    }

    /// Get available report types
    pub fn available_report_types(&self) -> Vec<ReportType> {
        self.generators.keys().cloned().collect()
    }

    /// Validate output directory
    async fn validate_output_directory(&self) -> Result<()> {
        let path = Path::new(&self.config.output_dir);
        if !path.exists() {
            tokio::fs::create_dir_all(path).await?;
        }

        // Check if directory is writable
        let test_file = path.join(".orchestrator_test");
        tokio::fs::write(&test_file, "test").await?;
        tokio::fs::remove_file(&test_file).await?;

        Ok(())
    }

    /// Create tasks from requests
    async fn create_tasks(
        &self,
        requests: Vec<ComplianceRequest>,
        scan_results: &[ScanResult],
    ) -> Result<Vec<ReportGenerationTask>> {
        let mut tasks = Vec::new();

        for request in requests {
            let report_type = self.determine_report_type(&request);

            // Check if we have a generator for this type
            if !self.generators.contains_key(&report_type) {
                warn!("No generator available for report type {:?}", report_type);
                continue;
            }

            let task = ReportGenerationTask {
                id: Uuid::new_v4(),
                report_type: report_type.clone(),
                request,
                scan_results: scan_results.to_vec(),
            };

            tasks.push(task);
        }

        Ok(tasks)
    }

    /// Determine report type from request
    fn determine_report_type(&self, request: &ComplianceRequest) -> ReportType {
        // Determine based on output formats
        if request.config.output_formats.contains(&OutputFormat::Spdx) {
            ReportType::Spdx
        } else if request.config.output_formats.contains(&OutputFormat::CycloneDx) {
            ReportType::Spdx // For now, map CycloneDX to SPDX
        } else {
            ReportType::Compliance // Default to compliance report
        }
    }

    /// Execute tasks in parallel
    async fn execute_parallel(&self, tasks: Vec<ReportGenerationTask>) -> Result<Vec<ReportGenerationResult>> {
        let mut futures = Vec::new();

        for task in tasks {
            let generator = self.generators.get(&task.report_type).cloned();
            let semaphore = self.semaphore.clone();

            let future = async move {
                let _permit = semaphore.acquire().await.unwrap();
                self.execute_task(task, generator).await
            };

            futures.push(future);
        }

        let results = join_all(futures).await;
        Ok(results.into_iter().collect::<Result<Vec<_>>>()?)
    }

    /// Execute tasks sequentially
    async fn execute_sequential(&self, tasks: Vec<ReportGenerationTask>) -> Result<Vec<ReportGenerationResult>> {
        let mut results = Vec::new();

        for task in tasks {
            let generator = self.generators.get(&task.report_type).cloned();
            let result = self.execute_task(task, generator).await?;
            results.push(result);
        }

        Ok(results)
    }

    /// Execute single task
    async fn execute_task(
        &self,
        task: ReportGenerationTask,
        generator: Option<Arc<dyn ReportGenerator + Send + Sync>>,
    ) -> Result<ReportGenerationResult> {
        let start_time = chrono::Utc::now();

        let result = match generator {
            Some(gen) => {
                match gen.generate_report(&task.request, &task.scan_results).await {
                    Ok(report) => ReportGenerationResult {
                        task_id: task.id,
                        report_type: task.report_type,
                        report: Some(report),
                        error: None,
                        duration_ms: (chrono::Utc::now() - start_time).num_milliseconds() as u64,
                        generated_at: chrono::Utc::now(),
                    },
                    Err(e) => {
                        warn!("Report generation failed for task {}: {}", task.id, e);
                        ReportGenerationResult {
                            task_id: task.id,
                            report_type: task.report_type,
                            report: None,
                            error: Some(e.to_string()),
                            duration_ms: (chrono::Utc::now() - start_time).num_milliseconds() as u64,
                            generated_at: chrono::Utc::now(),
                        }
                    }
                }
            }
            None => ReportGenerationResult {
                task_id: task.id,
                report_type: task.report_type,
                report: None,
                error: Some(format!("No generator available for report type {:?}", task.report_type)),
                duration_ms: (chrono::Utc::now() - start_time).num_milliseconds() as u64,
                generated_at: chrono::Utc::now(),
            },
        };

        Ok(result)
    }

    /// Generate orchestration summary
    async fn generate_summary(
        &self,
        results: &[ReportGenerationResult],
        total_duration_ms: u64,
    ) -> Result<OrchestrationSummary> {
        let total_tasks = results.len();
        let successful = results.iter().filter(|r| r.report.is_some()).count();
        let failed = total_tasks - successful;

        let mut results_by_type = HashMap::new();

        for result in results {
            let type_summary = results_by_type.entry(result.report_type.clone()).or_insert(TypeSummary {
                count: 0,
                successful: 0,
                failed: 0,
                avg_duration_ms: 0,
            });

            type_summary.count += 1;
            if result.report.is_some() {
                type_summary.successful += 1;
            } else {
                type_summary.failed += 1;
            }
        }

        // Calculate average durations
        for result in results {
            if let Some(type_summary) = results_by_type.get_mut(&result.report_type) {
                type_summary.avg_duration_ms = (type_summary.avg_duration_ms + result.duration_ms) / 2;
            }
        }

        let status = if failed == 0 {
            OrchestrationStatus::Completed
        } else if successful > 0 {
            OrchestrationStatus::PartialSuccess
        } else {
            OrchestrationStatus::Failed
        };

        Ok(OrchestrationSummary {
            total_tasks,
            successful,
            failed,
            total_duration_ms,
            results_by_type,
            status,
        })
    }

    /// Get orchestrator configuration
    pub fn config(&self) -> &ReportOrchestratorConfig {
        &self.config
    }

    /// Update orchestrator configuration
    pub fn update_config(&mut self, config: ReportOrchestratorConfig) {
        self.config = config;
        // Update semaphore with new concurrency limit
        self.semaphore = Arc::new(Semaphore::new(self.config.max_concurrent_tasks));
    }
}

impl Default for ReportOrchestratorConfig {
    fn default() -> Self {
        Self {
            max_concurrent_tasks: 4,
            output_dir: "reports".to_string(),
            enable_parallel: true,
            template_dir: None,
            default_configs: HashMap::new(),
            enable_progress_tracking: false,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_orchestrator_config_default() {
        let config = ReportOrchestratorConfig::default();
        assert_eq!(config.max_concurrent_tasks, 4);
        assert_eq!(config.output_dir, "reports");
        assert!(config.enable_parallel);
    }

    #[test]
    fn test_orchestrator_creation() {
        let config = ReportOrchestratorConfig::default();
        let compliance_config = ComplianceConfig::default();
        let orchestrator = ReportOrchestrator::new(config, compliance_config);

        let available_types = orchestrator.available_report_types();
        assert!(available_types.contains(&ReportType::Spdx));
        assert!(available_types.contains(&ReportType::Notice));
        assert!(available_types.contains(&ReportType::Compliance));
    }

    #[test]
    fn test_orchestration_status_serialization() {
        let status = OrchestrationStatus::Completed;
        let serialized = serde_json::to_string(&status).unwrap();
        assert_eq!(serialized, "\"completed\"");
    }

    #[tokio::test]
    async fn test_output_directory_validation() {
        let config = ReportOrchestratorConfig {
            output_dir: "/tmp/test_reports".to_string(),
            ..Default::default()
        };
        let compliance_config = ComplianceConfig::default();
        let orchestrator = ReportOrchestrator::new(config, compliance_config);

        // This should create the directory if it doesn't exist
        let result = orchestrator.validate_output_directory().await;
        assert!(result.is_ok());
    }
}