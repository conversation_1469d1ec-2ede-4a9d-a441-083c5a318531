use crate::{compliance::Severity, config::ComplianceConfig, error::Result, scanners::ScanResult};
use serde::{Deserialize, Serialize};
use tracing::{info, instrument};

/// SEBI (Securities and Exchange Board of India) compliance exporter
pub struct SebiExporter {
    #[allow(dead_code)]
    config: ComplianceConfig,
}

/// SEBI CSCRF (Cyber Security and Cyber Resilience Framework) compliance report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SebiReport {
    /// Report metadata
    pub metadata: SebiMetadata,
    /// CSCRF compliance findings
    pub findings: Vec<SebiFinding>,
    /// Cyber security governance
    pub governance: GovernanceAssessment,
    /// Risk management framework
    pub risk_management: RiskManagementFramework,
    /// Incident response and business continuity
    pub incident_response: IncidentResponseFramework,
    /// Third-party risk management
    pub third_party_risk: ThirdPartyRiskManagement,
    /// Compliance score
    pub compliance_score: f64,
}

/// SEBI report metadata
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SebiMetadata {
    /// Report version (CSCRF v2.0)
    pub cscrf_version: String,
    /// Market infrastructure institution details
    pub mii_details: MiiDetails,
    /// Assessment period
    pub assessment_period: AssessmentPeriod,
    /// Report classification
    pub classification: String,
    /// Assessor information
    pub assessor: String,
}

/// Market Infrastructure Institution details
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MiiDetails {
    /// Institution name
    pub name: String,
    /// SEBI registration number
    pub sebi_registration: String,
    /// Institution type (Stock Exchange, Depository, Clearing Corporation, etc.)
    pub institution_type: InstitutionType,
    /// Business segments
    pub business_segments: Vec<String>,
    /// Annual turnover
    pub annual_turnover: Option<f64>,
    /// Number of participants/members
    pub participant_count: Option<u32>,
}

/// SEBI institution types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum InstitutionType {
    /// Stock Exchange
    StockExchange,
    /// Depository
    Depository,
    /// Clearing Corporation
    ClearingCorporation,
    /// Depository Participant
    DepositoryParticipant,
    /// Mutual Fund
    MutualFund,
    /// Portfolio Manager
    PortfolioManager,
    /// Investment Adviser
    InvestmentAdviser,
    /// Credit Rating Agency
    CreditRatingAgency,
}

/// Assessment period
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AssessmentPeriod {
    /// Start date
    pub start_date: chrono::DateTime<chrono::Utc>,
    /// End date
    pub end_date: chrono::DateTime<chrono::Utc>,
    /// Assessment scope
    pub scope: String,
}

/// SEBI CSCRF specific finding
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SebiFinding {
    /// Finding ID
    pub id: String,
    /// Finding title
    pub title: String,
    /// Finding description
    pub description: String,
    /// Severity level
    pub severity: Severity,
    /// CSCRF control reference
    pub control_reference: String,
    /// Affected systems/components
    pub affected_components: Vec<String>,
    /// Evidence collected
    pub evidence: Vec<String>,
    /// CSCRF domain
    pub cscrf_domain: CscrfDomain,
    /// Compliance status
    pub compliance_status: ComplianceStatus,
    /// Business impact
    pub business_impact: BusinessImpact,
    /// Remediation timeline
    pub remediation_timeline: String,
}

/// CSCRF domains as per SEBI guidelines
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum CscrfDomain {
    /// Cyber Security Governance
    CyberSecurityGovernance,
    /// Cyber Risk Management
    CyberRiskManagement,
    /// Cyber Resilience
    CyberResilience,
    /// Incident Response and Recovery
    IncidentResponseRecovery,
    /// Third Party Risk Management
    ThirdPartyRiskManagement,
    /// Cyber Security Awareness and Training
    CyberSecurityAwareness,
    /// Cyber Threat Intelligence
    CyberThreatIntelligence,
    /// Cyber Security Testing
    CyberSecurityTesting,
}

/// Compliance status for SEBI
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum ComplianceStatus {
    /// Fully compliant
    Compliant,
    /// Largely compliant
    LargelyCompliant,
    /// Partially compliant
    PartiallyCompliant,
    /// Non-compliant
    NonCompliant,
    /// Not applicable
    NotApplicable,
}

/// Business impact assessment
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
#[serde(rename_all = "snake_case")]
pub enum BusinessImpact {
    /// Negligible business impact
    Negligible,
    /// Low business impact
    Low,
    /// Medium business impact
    Medium,
    /// High business impact
    High,
    /// Critical business impact
    Critical,
}

/// Governance assessment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GovernanceAssessment {
    /// Board oversight
    pub board_oversight: bool,
    /// Cyber security policy
    pub cyber_security_policy: bool,
    /// Risk appetite statement
    pub risk_appetite_statement: bool,
    /// Organizational structure
    pub organizational_structure: bool,
    /// Roles and responsibilities
    pub roles_responsibilities: bool,
    /// Performance metrics
    pub performance_metrics: bool,
    /// Compliance score
    pub governance_score: f64,
}

/// Risk management framework
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskManagementFramework {
    /// Risk identification process
    pub risk_identification: bool,
    /// Risk assessment methodology
    pub risk_assessment: bool,
    /// Risk treatment plans
    pub risk_treatment: bool,
    /// Risk monitoring
    pub risk_monitoring: bool,
    /// Risk reporting
    pub risk_reporting: bool,
    /// Business impact analysis
    pub business_impact_analysis: bool,
    /// Risk score
    pub risk_score: f64,
}

/// Incident response framework
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IncidentResponseFramework {
    /// Incident response plan
    pub incident_response_plan: bool,
    /// Incident classification
    pub incident_classification: bool,
    /// Response team structure
    pub response_team: bool,
    /// Communication procedures
    pub communication_procedures: bool,
    /// Recovery procedures
    pub recovery_procedures: bool,
    /// Post-incident review
    pub post_incident_review: bool,
    /// SEBI reporting compliance
    pub sebi_reporting: bool,
    /// Response readiness score
    pub readiness_score: f64,
}

/// Third-party risk management
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThirdPartyRiskManagement {
    /// Vendor risk assessment
    pub vendor_risk_assessment: bool,
    /// Due diligence process
    pub due_diligence: bool,
    /// Contractual requirements
    pub contractual_requirements: bool,
    /// Ongoing monitoring
    pub ongoing_monitoring: bool,
    /// Vendor security testing
    pub vendor_security_testing: bool,
    /// Exit strategies
    pub exit_strategies: bool,
    /// Third-party risk score
    pub third_party_score: f64,
}

impl SebiExporter {
    /// Create new SEBI exporter
    pub fn new(config: &ComplianceConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }

    /// Generate SEBI CSCRF compliance report
    #[instrument(skip(self, scan_results))]
    pub async fn generate_report(&self, scan_results: &[ScanResult]) -> Result<SebiReport> {
        info!("Generating SEBI CSCRF compliance report");

        let mut report = SebiReport {
            metadata: self.create_metadata(),
            findings: Vec::new(),
            governance: self.assess_governance().await?,
            risk_management: self.assess_risk_management().await?,
            incident_response: self.assess_incident_response().await?,
            third_party_risk: self.assess_third_party_risk().await?,
            compliance_score: 0.0,
        };

        // Analyze scan results for SEBI CSCRF findings
        for scan_result in scan_results {
            let findings = self.analyze_scan_for_sebi_findings(scan_result).await?;
            report.findings.extend(findings);
        }

        // Calculate compliance score
        report.compliance_score = self.calculate_compliance_score(&report);

        info!(
            findings_count = report.findings.len(),
            compliance_score = report.compliance_score,
            "SEBI CSCRF report generated"
        );

        Ok(report)
    }

    /// Create report metadata
    fn create_metadata(&self) -> SebiMetadata {
        SebiMetadata {
            cscrf_version: "2.0".to_string(),
            mii_details: MiiDetails {
                name: "Sample Market Infrastructure Institution".to_string(),
                sebi_registration: "INE000000000".to_string(),
                institution_type: InstitutionType::StockExchange,
                business_segments: vec![
                    "Equity Trading".to_string(),
                    "Derivatives Trading".to_string(),
                    "Currency Trading".to_string(),
                ],
                annual_turnover: Some(1000000000.0), // 1 billion
                participant_count: Some(500),
            },
            assessment_period: AssessmentPeriod {
                start_date: chrono::Utc::now() - chrono::Duration::days(90),
                end_date: chrono::Utc::now(),
                scope: "IT Infrastructure, Trading Systems, and Risk Management Systems"
                    .to_string(),
            },
            classification: "Confidential - SEBI Regulatory".to_string(),
            assessor: "Infinitium Signal CSCRF Assessment".to_string(),
        }
    }

    /// Assess cyber security governance
    async fn assess_governance(&self) -> Result<GovernanceAssessment> {
        // In a real implementation, this would check actual governance documents and policies
        Ok(GovernanceAssessment {
            board_oversight: true,
            cyber_security_policy: true,
            risk_appetite_statement: true,
            organizational_structure: true,
            roles_responsibilities: true,
            performance_metrics: false, // Gap identified
            governance_score: 83.3,     // 5/6 * 100
        })
    }

    /// Assess risk management framework
    async fn assess_risk_management(&self) -> Result<RiskManagementFramework> {
        Ok(RiskManagementFramework {
            risk_identification: true,
            risk_assessment: true,
            risk_treatment: true,
            risk_monitoring: true,
            risk_reporting: true,
            business_impact_analysis: false, // Gap identified
            risk_score: 83.3,                // 5/6 * 100
        })
    }

    /// Assess incident response framework
    async fn assess_incident_response(&self) -> Result<IncidentResponseFramework> {
        Ok(IncidentResponseFramework {
            incident_response_plan: true,
            incident_classification: true,
            response_team: true,
            communication_procedures: true,
            recovery_procedures: true,
            post_incident_review: false, // Gap identified
            sebi_reporting: true,
            readiness_score: 85.7, // 6/7 * 100
        })
    }

    /// Assess third-party risk management
    async fn assess_third_party_risk(&self) -> Result<ThirdPartyRiskManagement> {
        Ok(ThirdPartyRiskManagement {
            vendor_risk_assessment: true,
            due_diligence: true,
            contractual_requirements: true,
            ongoing_monitoring: false,      // Gap identified
            vendor_security_testing: false, // Gap identified
            exit_strategies: true,
            third_party_score: 66.7, // 4/6 * 100
        })
    }

    /// Analyze scan results for SEBI CSCRF findings
    async fn analyze_scan_for_sebi_findings(
        &self,
        scan_result: &ScanResult,
    ) -> Result<Vec<SebiFinding>> {
        let mut findings = Vec::new();

        // Check for critical vulnerabilities affecting trading systems
        for vuln in &scan_result.vulnerabilities {
            if vuln.severity.to_lowercase() == "critical" {
                findings.push(SebiFinding {
                    id: format!("SEBI-CSCRF-{}", vuln.cve_id),
                    title: format!("Critical Vulnerability in Trading System: {}", vuln.cve_id),
                    description: format!("Critical vulnerability {} found in component {} which may impact trading operations", vuln.cve_id, vuln.component),
                    severity: Severity::Critical,
                    control_reference: "CSCRF-3.2.1".to_string(),
                    affected_components: vec![vuln.component.clone()],
                    evidence: vec![format!("CVE: {}, CVSS: {:?}", vuln.cve_id, vuln.cvss_score)],
                    cscrf_domain: CscrfDomain::CyberResilience,
                    compliance_status: ComplianceStatus::NonCompliant,
                    business_impact: BusinessImpact::Critical,
                    remediation_timeline: "24 hours".to_string(),
                });
            }
        }

        // Check for third-party components with vulnerabilities
        for component in &scan_result.software_components {
            if component.package_manager != "internal" {
                // This is a third-party component
                let component_vulns: Vec<_> = scan_result
                    .vulnerabilities
                    .iter()
                    .filter(|v| v.component == component.name)
                    .collect();

                if !component_vulns.is_empty() {
                    findings.push(SebiFinding {
                        id: format!("SEBI-TPR-{}", component.name),
                        title: format!("Third-Party Component Risk: {}", component.name),
                        description: format!(
                            "Third-party component {} has {} known vulnerabilities",
                            component.name,
                            component_vulns.len()
                        ),
                        severity: if component_vulns
                            .iter()
                            .any(|v| v.severity.to_lowercase() == "critical")
                        {
                            Severity::Critical
                        } else if component_vulns
                            .iter()
                            .any(|v| v.severity.to_lowercase() == "high")
                        {
                            Severity::High
                        } else {
                            Severity::Medium
                        },
                        control_reference: "CSCRF-5.1.3".to_string(),
                        affected_components: vec![component.name.clone()],
                        evidence: component_vulns.iter().map(|v| v.cve_id.clone()).collect(),
                        cscrf_domain: CscrfDomain::ThirdPartyRiskManagement,
                        compliance_status: ComplianceStatus::PartiallyCompliant,
                        business_impact: BusinessImpact::Medium,
                        remediation_timeline: "7 days".to_string(),
                    });
                }
            }
        }

        // Check for missing security controls
        if scan_result
            .software_components
            .iter()
            .any(|c| c.license.is_none())
        {
            findings.push(SebiFinding {
                id: "SEBI-LIC-001".to_string(),
                title: "Missing License Information".to_string(),
                description: "Some software components lack proper license information".to_string(),
                severity: Severity::Medium,
                control_reference: "CSCRF-5.2.1".to_string(),
                affected_components: scan_result
                    .software_components
                    .iter()
                    .filter(|c| c.license.is_none())
                    .map(|c| c.name.clone())
                    .collect(),
                evidence: vec!["License audit report".to_string()],
                cscrf_domain: CscrfDomain::ThirdPartyRiskManagement,
                compliance_status: ComplianceStatus::PartiallyCompliant,
                business_impact: BusinessImpact::Low,
                remediation_timeline: "30 days".to_string(),
            });
        }

        Ok(findings)
    }

    /// Calculate overall SEBI CSCRF compliance score
    fn calculate_compliance_score(&self, report: &SebiReport) -> f64 {
        // Weight different domains according to SEBI CSCRF importance
        let governance_weight = 0.25;
        let risk_management_weight = 0.25;
        let incident_response_weight = 0.20;
        let third_party_weight = 0.15;
        let findings_weight = 0.15;

        // Calculate findings score (inverse of severity)
        let findings_score = if report.findings.is_empty() {
            100.0
        } else {
            let critical_findings = report
                .findings
                .iter()
                .filter(|f| f.severity == Severity::Critical)
                .count();
            let high_findings = report
                .findings
                .iter()
                .filter(|f| f.severity == Severity::High)
                .count();
            let medium_findings = report
                .findings
                .iter()
                .filter(|f| f.severity == Severity::Medium)
                .count();

            let penalty = (critical_findings * 20) + (high_findings * 10) + (medium_findings * 5);
            (100.0 - penalty as f64).max(0.0)
        };

        // Weighted average
        (report.governance.governance_score * governance_weight)
            + (report.risk_management.risk_score * risk_management_weight)
            + (report.incident_response.readiness_score * incident_response_weight)
            + (report.third_party_risk.third_party_score * third_party_weight)
            + (findings_score * findings_weight)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_institution_type_serialization() {
        let inst_type = InstitutionType::StockExchange;
        let serialized = serde_json::to_string(&inst_type).unwrap();
        assert_eq!(serialized, "\"stock_exchange\"");
    }

    #[test]
    fn test_cscrf_domain() {
        let domain = CscrfDomain::CyberSecurityGovernance;
        assert_eq!(domain, CscrfDomain::CyberSecurityGovernance);
    }

    #[test]
    fn test_business_impact_ordering() {
        assert!(BusinessImpact::Critical > BusinessImpact::High);
        assert!(BusinessImpact::High > BusinessImpact::Medium);
    }
}
