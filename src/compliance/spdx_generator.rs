use crate::{config::ComplianceConfig, error::Result, scanners::ScanResult};
use serde::{Deserialize, Serialize};
use tracing::{info, instrument};
use uuid::Uuid;

/// SPDX document generator
pub struct SpdxGenerator {
    config: ComplianceConfig,
}

/// SPDX Document
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpdxDocument {
    #[serde(rename = "spdxVersion")]
    pub spdx_version: String,
    #[serde(rename = "dataLicense")]
    pub data_license: String,
    #[serde(rename = "SPDXID")]
    pub spdx_id: String,
    #[serde(rename = "documentName")]
    pub document_name: String,
    #[serde(rename = "documentNamespace")]
    pub document_namespace: String,
    #[serde(rename = "creationInfo")]
    pub creation_info: SpdxCreationInfo,
    #[serde(rename = "packages")]
    pub packages: Vec<SpdxPackage>,
    #[serde(rename = "relationships")]
    pub relationships: Vec<SpdxRelationship>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SpdxCreationInfo {
    pub created: chrono::DateTime<chrono::Utc>,
    pub creators: Vec<String>,
    #[serde(rename = "licenseListVersion")]
    pub license_list_version: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpdxPackage {
    #[serde(rename = "SPDXID")]
    pub spdx_id: String,
    pub name: String,
    #[serde(rename = "versionInfo")]
    pub version_info: String,
    #[serde(rename = "downloadLocation")]
    pub download_location: String,
    #[serde(rename = "filesAnalyzed")]
    pub files_analyzed: bool,
    #[serde(rename = "licenseConcluded")]
    pub license_concluded: String,
    #[serde(rename = "licenseDeclared")]
    pub license_declared: String,
    #[serde(rename = "copyrightText")]
    pub copyright_text: String,
    #[serde(rename = "externalRefs", skip_serializing_if = "Vec::is_empty")]
    pub external_refs: Vec<SpdxExternalRef>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpdxExternalRef {
    #[serde(rename = "referenceCategory")]
    pub reference_category: String,
    #[serde(rename = "referenceType")]
    pub reference_type: String,
    #[serde(rename = "referenceLocator")]
    pub reference_locator: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpdxRelationship {
    #[serde(rename = "spdxElementId")]
    pub spdx_element_id: String,
    #[serde(rename = "relationshipType")]
    pub relationship_type: String,
    #[serde(rename = "relatedSpdxElement")]
    pub related_spdx_element: String,
}

impl SpdxGenerator {
    pub fn new(config: &ComplianceConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }

    #[instrument(skip(self, scan_results))]
    pub async fn generate_document(&self, scan_results: &[ScanResult]) -> Result<String> {
        info!("Generating SPDX document");

        let document = self.create_document(scan_results).await?;
        let json_content = serde_json::to_string_pretty(&document)?;

        let output_dir = &self.config.report_output_dir;
        let filename = format!("spdx_document_{}.json", Uuid::new_v4());
        let file_path = std::path::Path::new(output_dir).join(&filename);

        tokio::fs::create_dir_all(output_dir).await?;
        tokio::fs::write(&file_path, &json_content).await?;

        info!(file_path = %file_path.display(), "SPDX document generated");
        Ok(file_path.to_string_lossy().to_string())
    }

    async fn create_document(&self, scan_results: &[ScanResult]) -> Result<SpdxDocument> {
        let mut packages = Vec::new();
        let relationships = Vec::new();

        for scan_result in scan_results {
            for component in &scan_result.software_components {
                let package = SpdxPackage {
                    spdx_id: format!(
                        "SPDXRef-Package-{}",
                        component.name.replace(['/', '-', '.'], "")
                    ),
                    name: component.name.clone(),
                    version_info: component.version.clone(),
                    download_location: "NOASSERTION".to_string(),
                    files_analyzed: false,
                    license_concluded: component
                        .license
                        .clone()
                        .unwrap_or_else(|| "NOASSERTION".to_string()),
                    license_declared: component
                        .license
                        .clone()
                        .unwrap_or_else(|| "NOASSERTION".to_string()),
                    copyright_text: "NOASSERTION".to_string(),
                    external_refs: Vec::new(),
                };
                packages.push(package);
            }
        }

        Ok(SpdxDocument {
            spdx_version: "SPDX-2.3".to_string(),
            data_license: "CC0-1.0".to_string(),
            spdx_id: "SPDXRef-DOCUMENT".to_string(),
            document_name: "Infinitium Signal SBOM".to_string(),
            document_namespace: format!("https://infinitum-signal.com/spdx/{}", Uuid::new_v4()),
            creation_info: SpdxCreationInfo {
                created: chrono::Utc::now(),
                creators: vec!["Tool: infinitum-signal".to_string()],
                license_list_version: "3.19".to_string(),
            },
            packages,
            relationships,
        })
    }
}
