//! # SPDX License Client
//!
//! SPDX license database integration client. Handles SPDX API communication,
//! license list updates, exception handling, and version tracking.

use crate::error::{InfinitumError, Result};
use chrono::{DateTime, Utc};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, time::Duration};
use tracing::{error, info, warn};

/// SPDX license information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpdxLicense {
    /// License ID (e.g., "MIT", "Apache-2.0")
    pub license_id: String,
    /// License name
    pub name: String,
    /// OSI approved status
    pub osi_approved: bool,
    /// FSF Libre status
    pub fsf_libre: Option<bool>,
    /// License text
    pub license_text: Option<String>,
    /// Standard license header
    pub standard_license_header: Option<String>,
    /// Cross references
    pub cross_refs: Vec<String>,
    /// Notes
    pub notes: Option<String>,
    /// Deprecated license status
    pub is_deprecated: bool,
    /// Last updated timestamp
    pub last_updated: DateTime<Utc>,
}

/// SPDX exception information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpdxException {
    /// Exception ID
    pub exception_id: String,
    /// Exception name
    pub name: String,
    /// Exception text
    pub exception_text: Option<String>,
    /// Notes
    pub notes: Option<String>,
    /// Deprecated status
    pub is_deprecated: bool,
    /// Last updated timestamp
    pub last_updated: DateTime<Utc>,
}

/// SPDX API response for license list
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpdxLicenseListResponse {
    /// License list version
    pub license_list_version: String,
    /// Release date
    pub release_date: String,
    /// Licenses
    pub licenses: Vec<SpdxLicense>,
}

/// SPDX API response for exceptions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpdxExceptionListResponse {
    /// Exception list version
    pub exception_list_version: String,
    /// Release date
    pub release_date: String,
    /// Exceptions
    pub exceptions: Vec<SpdxException>,
}

/// SPDX client configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpdxClientConfig {
    /// Base API URL
    pub api_base_url: String,
    /// API version
    pub api_version: String,
    /// Request timeout in seconds
    pub timeout_seconds: u64,
    /// User agent string
    pub user_agent: String,
    /// Enable caching
    pub enable_cache: bool,
    /// Cache TTL in seconds
    pub cache_ttl_seconds: u64,
    /// Rate limit requests per minute
    pub rate_limit_per_minute: u32,
}

/// SPDX client statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpdxClientStats {
    /// Total API requests made
    pub total_requests: u64,
    /// Successful requests
    pub successful_requests: u64,
    /// Failed requests
    pub failed_requests: u64,
    /// Cache hits
    pub cache_hits: u64,
    /// Cache misses
    pub cache_misses: u64,
    /// Last successful update
    pub last_successful_update: Option<DateTime<Utc>>,
    /// Current license count
    pub license_count: usize,
    /// Current exception count
    pub exception_count: usize,
}

/// SPDX License Client
pub struct SpdxLicenseClient {
    /// HTTP client
    client: Client,
    /// Configuration
    config: SpdxClientConfig,
    /// License cache
    license_cache: HashMap<String, (SpdxLicense, DateTime<Utc>)>,
    /// Exception cache
    exception_cache: HashMap<String, (SpdxException, DateTime<Utc>)>,
    /// Statistics
    stats: SpdxClientStats,
    /// Last rate limit reset
    last_rate_limit_reset: DateTime<Utc>,
    /// Current request count in this window
    current_request_count: u32,
}

impl SpdxLicenseClient {
    /// Create new SPDX license client
    pub fn new(config: SpdxClientConfig) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(config.timeout_seconds))
            .user_agent(&config.user_agent)
            .build()
            .expect("Failed to create HTTP client");

        Self {
            client,
            config,
            license_cache: HashMap::new(),
            exception_cache: HashMap::new(),
            stats: SpdxClientStats {
                total_requests: 0,
                successful_requests: 0,
                failed_requests: 0,
                cache_hits: 0,
                cache_misses: 0,
                last_successful_update: None,
                license_count: 0,
                exception_count: 0,
            },
            last_rate_limit_reset: Utc::now(),
            current_request_count: 0,
        }
    }

    /// Fetch all licenses from SPDX
    pub async fn fetch_licenses(&mut self) -> Result<Vec<SpdxLicense>> {
        self.check_rate_limit().await?;

        let url = format!(
            "{}/licenses?version={}",
            self.config.api_base_url, self.config.api_version
        );

        info!("Fetching SPDX licenses from: {}", url);

        let response = self.client.get(&url).send().await?;
        self.stats.total_requests += 1;

        if !response.status().is_success() {
            self.stats.failed_requests += 1;
            return Err(InfinitumError::ApiError {
                service: "SPDX".to_string(),
                status_code: response.status().as_u16(),
                message: format!("Failed to fetch licenses: {}", response.status()),
            });
        }

        let license_response: SpdxLicenseListResponse = response.json().await?;
        self.stats.successful_requests += 1;

        let licenses = license_response.licenses;
        self.stats.license_count = licenses.len();
        self.stats.last_successful_update = Some(Utc::now());

        // Update cache
        if self.config.enable_cache {
            let now = Utc::now();
            for license in &licenses {
                self.license_cache.insert(
                    license.license_id.clone(),
                    (license.clone(), now),
                );
            }
        }

        info!("Successfully fetched {} SPDX licenses", licenses.len());
        Ok(licenses)
    }

    /// Fetch license exceptions from SPDX
    pub async fn fetch_exceptions(&mut self) -> Result<Vec<SpdxException>> {
        self.check_rate_limit().await?;

        let url = format!(
            "{}/exceptions?version={}",
            self.config.api_base_url, self.config.api_version
        );

        info!("Fetching SPDX exceptions from: {}", url);

        let response = self.client.get(&url).send().await?;
        self.stats.total_requests += 1;

        if !response.status().is_success() {
            self.stats.failed_requests += 1;
            return Err(InfinitumError::ApiError {
                service: "SPDX".to_string(),
                status_code: response.status().as_u16(),
                message: format!("Failed to fetch exceptions: {}", response.status()),
            });
        }

        let exception_response: SpdxExceptionListResponse = response.json().await?;
        self.stats.successful_requests += 1;

        let exceptions = exception_response.exceptions;
        self.stats.exception_count = exceptions.len();

        // Update cache
        if self.config.enable_cache {
            let now = Utc::now();
            for exception in &exceptions {
                self.exception_cache.insert(
                    exception.exception_id.clone(),
                    (exception.clone(), now),
                );
            }
        }

        info!("Successfully fetched {} SPDX exceptions", exceptions.len());
        Ok(exceptions)
    }

    /// Get specific license by ID
    pub async fn get_license(&mut self, license_id: &str) -> Result<Option<SpdxLicense>> {
        // Check cache first
        if self.config.enable_cache {
            if let Some((license, cached_at)) = self.license_cache.get(license_id) {
                let age = Utc::now().signed_duration_since(*cached_at);
                if age.num_seconds() < self.config.cache_ttl_seconds as i64 {
                    self.stats.cache_hits += 1;
                    return Ok(Some(license.clone()));
                }
            }
        }

        self.check_rate_limit().await?;
        self.stats.cache_misses += 1;

        let url = format!(
            "{}/licenses/{}?version={}",
            self.config.api_base_url, license_id, self.config.api_version
        );

        let response = self.client.get(&url).send().await?;
        self.stats.total_requests += 1;

        if response.status() == reqwest::StatusCode::NOT_FOUND {
            return Ok(None);
        }

        if !response.status().is_success() {
            self.stats.failed_requests += 1;
            return Err(InfinitumError::ApiError {
                service: "SPDX".to_string(),
                status_code: response.status().as_u16(),
                message: format!("Failed to fetch license {}: {}", license_id, response.status()),
            });
        }

        let license: SpdxLicense = response.json().await?;
        self.stats.successful_requests += 1;

        // Update cache
        if self.config.enable_cache {
            self.license_cache.insert(
                license_id.to_string(),
                (license.clone(), Utc::now()),
            );
        }

        Ok(Some(license))
    }

    /// Get specific exception by ID
    pub async fn get_exception(&mut self, exception_id: &str) -> Result<Option<SpdxException>> {
        // Check cache first
        if self.config.enable_cache {
            if let Some((exception, cached_at)) = self.exception_cache.get(exception_id) {
                let age = Utc::now().signed_duration_since(*cached_at);
                if age.num_seconds() < self.config.cache_ttl_seconds as i64 {
                    self.stats.cache_hits += 1;
                    return Ok(Some(exception.clone()));
                }
            }
        }

        self.check_rate_limit().await?;
        self.stats.cache_misses += 1;

        let url = format!(
            "{}/exceptions/{}?version={}",
            self.config.api_base_url, exception_id, self.config.api_version
        );

        let response = self.client.get(&url).send().await?;
        self.stats.total_requests += 1;

        if response.status() == reqwest::StatusCode::NOT_FOUND {
            return Ok(None);
        }

        if !response.status().is_success() {
            self.stats.failed_requests += 1;
            return Err(InfinitumError::ApiError {
                service: "SPDX".to_string(),
                status_code: response.status().as_u16(),
                message: format!("Failed to fetch exception {}: {}", exception_id, response.status()),
            });
        }

        let exception: SpdxException = response.json().await?;
        self.stats.successful_requests += 1;

        // Update cache
        if self.config.enable_cache {
            self.exception_cache.insert(
                exception_id.to_string(),
                (exception.clone(), Utc::now()),
            );
        }

        Ok(Some(exception))
    }

    /// Check if license is OSI approved
    pub async fn is_osi_approved(&mut self, license_id: &str) -> Result<bool> {
        if let Some(license) = self.get_license(license_id).await? {
            Ok(license.osi_approved)
        } else {
            Err(InfinitumError::LicenseNotFound {
                license_id: license_id.to_string(),
                source: "SPDX".to_string(),
            })
        }
    }

    /// Get license text
    pub async fn get_license_text(&mut self, license_id: &str) -> Result<Option<String>> {
        if let Some(license) = self.get_license(license_id).await? {
            Ok(license.license_text)
        } else {
            Ok(None)
        }
    }

    /// Get client statistics
    pub fn get_stats(&self) -> &SpdxClientStats {
        &self.stats
    }

    /// Clear cache
    pub fn clear_cache(&mut self) {
        self.license_cache.clear();
        self.exception_cache.clear();
        info!("SPDX client cache cleared");
    }

    /// Check rate limit and wait if necessary
    async fn check_rate_limit(&mut self) -> Result<()> {
        let now = Utc::now();
        let window_duration = now.signed_duration_since(self.last_rate_limit_reset);

        // Reset counter if window has passed
        if window_duration.num_seconds() >= 60 {
            self.last_rate_limit_reset = now;
            self.current_request_count = 0;
        }

        // Check if we're within rate limit
        if self.current_request_count >= self.config.rate_limit_per_minute {
            let wait_seconds = 60 - window_duration.num_seconds();
            if wait_seconds > 0 {
                warn!("Rate limit reached, waiting {} seconds", wait_seconds);
                tokio::time::sleep(Duration::from_secs(wait_seconds as u64)).await;
                self.last_rate_limit_reset = Utc::now();
                self.current_request_count = 0;
            }
        }

        self.current_request_count += 1;
        Ok(())
    }

    /// Validate API connectivity
    pub async fn validate_connectivity(&mut self) -> Result<bool> {
        let url = format!("{}/health?version={}", self.config.api_base_url, self.config.api_version);

        match self.client.get(&url).send().await {
            Ok(response) => Ok(response.status().is_success()),
            Err(_) => Ok(false),
        }
    }
}

impl Default for SpdxClientConfig {
    fn default() -> Self {
        Self {
            api_base_url: "https://api.spdx.org".to_string(),
            api_version: "v1".to_string(),
            timeout_seconds: 30,
            user_agent: "Infinitum-Signal/1.0".to_string(),
            enable_cache: true,
            cache_ttl_seconds: 3600, // 1 hour
            rate_limit_per_minute: 60,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_spdx_client_creation() {
        let config = SpdxClientConfig::default();
        let client = SpdxLicenseClient::new(config);
        assert_eq!(client.stats.total_requests, 0);
    }

    #[tokio::test]
    async fn test_rate_limit_check() {
        let config = SpdxClientConfig {
            rate_limit_per_minute: 2,
            ..Default::default()
        };
        let mut client = SpdxLicenseClient::new(config);

        // Should not wait for first two requests
        client.check_rate_limit().await.unwrap();
        client.check_rate_limit().await.unwrap();
        assert_eq!(client.current_request_count, 2);
    }

    #[test]
    fn test_default_config() {
        let config = SpdxClientConfig::default();
        assert_eq!(config.api_base_url, "https://api.spdx.org");
        assert_eq!(config.api_version, "v1");
        assert!(config.enable_cache);
    }
}