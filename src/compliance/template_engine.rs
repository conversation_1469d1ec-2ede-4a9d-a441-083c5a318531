//! # Template Engine
//!
//! Jinja2-style templating system for custom report formats using Tera templates.

use crate::error::{InfinitumError, Result};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, path::Path};
use tera::{Context, Tera};
use tracing::{info, instrument};

/// Template engine configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TemplateEngineConfig {
    /// Template directory
    pub template_dir: String,
    /// Auto-reload templates on change
    pub auto_reload: bool,
    /// Default template file extension
    pub default_extension: String,
    /// Enable strict mode (fail on missing variables)
    pub strict_mode: bool,
}

/// Template context data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateContext {
    /// Template variables
    pub variables: HashMap<String, serde_json::Value>,
    /// Template metadata
    pub metadata: HashMap<String, String>,
}

/// Template engine for report generation
pub struct TemplateEngine {
    tera: Tera,
    config: TemplateEngineConfig,
}

impl TemplateEngine {
    /// Create new template engine
    pub fn new(config: TemplateEngineConfig) -> Result<Self> {
        let mut tera = if config.auto_reload {
            Tera::new(&format!("{}/**/*.{}", config.template_dir, config.default_extension))?
        } else {
            let mut tera = Tera::default();
            tera.load_from_glob(&format!("{}/**/*.{}", config.template_dir, config.default_extension))?;
            tera
        };

        if config.strict_mode {
            tera.set_strict_mode(true);
        }

        info!("Template engine initialized with {} templates", tera.get_template_names().count());

        Ok(Self { tera, config })
    }

    /// Render template with context
    #[instrument(skip(self, context))]
    pub fn render(&self, template_name: &str, context: &TemplateContext) -> Result<String> {
        let mut tera_context = Context::new();

        // Add variables to context
        for (key, value) in &context.variables {
            tera_context.insert(key, value);
        }

        // Add metadata
        tera_context.insert("metadata", &context.metadata);

        // Add utility functions
        self.add_utility_functions(&mut tera_context);

        match self.tera.render(template_name, &tera_context) {
            Ok(result) => Ok(result),
            Err(e) => Err(InfinitumError::TemplateRender {
                template: template_name.to_string(),
                error: e.to_string(),
            }),
        }
    }

    /// Render template with raw context data
    pub fn render_with_data<T: Serialize>(
        &self,
        template_name: &str,
        data: &T,
        metadata: HashMap<String, String>,
    ) -> Result<String> {
        let json_value = serde_json::to_value(data)?;
        let variables = if let serde_json::Value::Object(map) = json_value {
            map.into_iter().collect()
        } else {
            return Err(InfinitumError::TemplateRender {
                template: template_name.to_string(),
                error: "Data must be a JSON object".to_string(),
            });
        };

        let context = TemplateContext { variables, metadata };
        self.render(template_name, &context)
    }

    /// Check if template exists
    pub fn has_template(&self, template_name: &str) -> bool {
        self.tera.get_template_names().any(|name| name == template_name)
    }

    /// Get list of available templates
    pub fn available_templates(&self) -> Vec<String> {
        self.tera.get_template_names().map(|s| s.to_string()).collect()
    }

    /// Add custom template
    pub fn add_template(&mut self, name: &str, content: &str) -> Result<()> {
        self.tera.add_raw_template(name, content)?;
        Ok(())
    }

    /// Remove template
    pub fn remove_template(&mut self, name: &str) {
        // Tera doesn't have a direct remove method, but we can reload
        if self.config.auto_reload {
            if let Err(e) = self.reload_templates() {
                tracing::warn!("Failed to reload templates after removal: {}", e);
            }
        }
    }

    /// Reload templates from disk
    pub fn reload_templates(&mut self) -> Result<()> {
        if self.config.auto_reload {
            self.tera.full_reload()?;
        } else {
            let mut new_tera = Tera::default();
            new_tera.load_from_glob(&format!("{}/**/*.{}", self.config.template_dir, self.config.default_extension))?;
            if self.config.strict_mode {
                new_tera.set_strict_mode(true);
            }
            self.tera = new_tera;
        }
        Ok(())
    }

    /// Validate template syntax
    pub fn validate_template(&self, template_name: &str) -> Result<()> {
        if !self.has_template(template_name) {
            return Err(InfinitumError::TemplateNotFound {
                template: template_name.to_string(),
            });
        }

        // Try to render with empty context to check syntax
        let empty_context = TemplateContext {
            variables: HashMap::new(),
            metadata: HashMap::new(),
        };

        // This will fail if there are required variables in strict mode
        if !self.config.strict_mode {
            let _ = self.render(template_name, &empty_context)?;
        }

        Ok(())
    }

    /// Add utility functions to template context
    fn add_utility_functions(&self, context: &mut Context) {
        // Add current timestamp
        context.insert("now", &chrono::Utc::now().to_rfc3339());

        // Add utility functions
        context.insert("format_date", &"{{date | date:format='%Y-%m-%d'}}");
        context.insert("format_number", &"{{number | number:format='%.2f'}}");
    }

    /// Get template engine configuration
    pub fn config(&self) -> &TemplateEngineConfig {
        &self.config
    }
}

/// Template registry for managing multiple templates
pub struct TemplateRegistry {
    engines: HashMap<String, TemplateEngine>,
    default_engine: Option<String>,
}

impl TemplateRegistry {
    /// Create new template registry
    pub fn new() -> Self {
        Self {
            engines: HashMap::new(),
            default_engine: None,
        }
    }

    /// Register template engine
    pub fn register_engine(&mut self, name: &str, engine: TemplateEngine) {
        if self.engines.is_empty() {
            self.default_engine = Some(name.to_string());
        }
        self.engines.insert(name.to_string(), engine);
    }

    /// Get template engine by name
    pub fn get_engine(&self, name: &str) -> Option<&TemplateEngine> {
        self.engines.get(name)
    }

    /// Get default template engine
    pub fn get_default_engine(&self) -> Option<&TemplateEngine> {
        self.default_engine.as_ref()
            .and_then(|name| self.engines.get(name))
    }

    /// Set default engine
    pub fn set_default_engine(&mut self, name: &str) -> Result<()> {
        if self.engines.contains_key(name) {
            self.default_engine = Some(name.to_string());
            Ok(())
        } else {
            Err(InfinitumError::TemplateEngineNotFound {
                engine: name.to_string(),
            })
        }
    }

    /// Render template using specified engine
    pub fn render_with_engine(
        &self,
        engine_name: &str,
        template_name: &str,
        context: &TemplateContext,
    ) -> Result<String> {
        let engine = self.get_engine(engine_name)
            .ok_or_else(|| InfinitumError::TemplateEngineNotFound {
                engine: engine_name.to_string(),
            })?;

        engine.render(template_name, context)
    }

    /// Render template using default engine
    pub fn render(&self, template_name: &str, context: &TemplateContext) -> Result<String> {
        let engine = self.get_default_engine()
            .ok_or_else(|| InfinitumError::TemplateRender {
                template: template_name.to_string(),
                error: "No default template engine configured".to_string(),
            })?;

        engine.render(template_name, context)
    }

    /// List all registered engines
    pub fn list_engines(&self) -> Vec<String> {
        self.engines.keys().cloned().collect()
    }
}

impl Default for TemplateEngineConfig {
    fn default() -> Self {
        Self {
            template_dir: "templates".to_string(),
            auto_reload: false,
            default_extension: "html".to_string(),
            strict_mode: false,
        }
    }
}

impl Default for TemplateContext {
    fn default() -> Self {
        Self {
            variables: HashMap::new(),
            metadata: HashMap::new(),
        }
    }
}

/// Template helper functions
pub mod helpers {
    use super::*;

    /// Create SPDX report template
    pub fn create_spdx_template() -> &'static str {
        r#"
<!DOCTYPE html>
<html>
<head>
    <title>SPDX Report - {{metadata.title}}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { border-bottom: 2px solid #333; padding-bottom: 20px; }
        .package { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .license { background: #f9f9f9; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>SPDX Software Bill of Materials</h1>
        <p><strong>Document:</strong> {{spdx_id}}</p>
        <p><strong>Created:</strong> {{creation_info.created}}</p>
        <p><strong>Creator:</strong> {{creation_info.creators | join(sep=", ")}}</p>
    </div>

    <h2>Packages ({{packages | length}})</h2>
    {% for package in packages %}
    <div class="package">
        <h3>{{package.name}} v{{package.version_info}}</h3>
        <p><strong>SPDX ID:</strong> {{package.spdx_id}}</p>
        <p><strong>Download Location:</strong> {{package.download_location}}</p>
        <div class="license">
            <p><strong>License Concluded:</strong> {{package.license_concluded}}</p>
            <p><strong>License Declared:</strong> {{package.license_declared}}</p>
            {% if package.copyright_text %}
            <p><strong>Copyright:</strong> {{package.copyright_text}}</p>
            {% endif %}
        </div>
    </div>
    {% endfor %}

    <div class="footer">
        <p>Generated by {{metadata.tool_name}} v{{metadata.tool_version}}</p>
        <p>Organization: {{metadata.organization}}</p>
    </div>
</body>
</html>
        "#
    }

    /// Create compliance report template
    pub fn create_compliance_template() -> &'static str {
        r#"
<!DOCTYPE html>
<html>
<head>
    <title>Compliance Report - {{metadata.title}}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .score { font-size: 2em; font-weight: bold; color: #2c3e50; }
        .excellent { color: #27ae60; }
        .good { color: #2ecc71; }
        .satisfactory { color: #f39c12; }
        .critical { color: #e74c3c; }
        .finding { margin: 15px 0; padding: 15px; border-left: 4px solid #3498db; background: #f8f9fa; }
        .recommendation { margin: 15px 0; padding: 15px; border-left: 4px solid #e67e22; background: #fff3cd; }
    </style>
</head>
<body>
    <h1>{{metadata.title}}</h1>
    <p><strong>Organization:</strong> {{metadata.organization}}</p>
    <p><strong>Generated:</strong> {{metadata.generated_at}}</p>

    <h2>Compliance Assessment</h2>
    <div class="score {{assessment.compliance_level | lower}}">
        Overall Score: {{assessment.overall_score | round(precision=1)}}%
        <br><small>{{assessment.compliance_level}}</small>
    </div>

    <h3>Key Metrics</h3>
    <ul>
        <li>License Compliance: {{assessment.license_compliance_score | round(precision=1)}}%</li>
        <li>Security Compliance: {{assessment.security_compliance_score | round(precision=1)}}%</li>
        <li>Risk Compliance: {{assessment.risk_compliance_score | round(precision=1)}}%</li>
        <li>Critical Issues: {{assessment.critical_issues}}</li>
        <li>High Severity Issues: {{assessment.high_issues}}</li>
    </ul>

    {% if license_conflicts %}
    <h2>License Conflicts ({{license_conflicts | length}})</h2>
    {% for conflict in license_conflicts %}
    <div class="finding">
        <h4>{{conflict.description}}</h4>
        <p><strong>Severity:</strong> {{conflict.severity}}</p>
        <p><strong>Affected Components:</strong> {{conflict.affected_components | join(sep=", ")}}</p>
        <p><strong>Resolution:</strong> {{conflict.resolution_suggestions | join(sep="; ")}}</p>
    </div>
    {% endfor %}
    {% endif %}

    {% if recommendations %}
    <h2>Recommendations ({{recommendations | length}})</h2>
    {% for rec in recommendations %}
    <div class="recommendation">
        <h4>{{rec.title}}</h4>
        <p>{{rec.description}}</p>
        <p><strong>Priority:</strong> {{rec.priority}} | <strong>Effort:</strong> {{rec.effort}}</p>
        <p><strong>Impact:</strong> {{rec.expected_impact}}</p>
    </div>
    {% endfor %}
    {% endif %}

    <div class="footer">
        <p>Generated by {{metadata.tool_name}} v{{metadata.tool_version}}</p>
    </div>
</body>
</html>
        "#
    }

    /// Create notice file template
    pub fn create_notice_template() -> &'static str {
        r#"
NOTICE FILE FOR {{metadata.title}}

{{header}}

LICENSE INFORMATION
==================

{% for notice in license_notices %}
{{notice.license_name}} ({{notice.license_id}})
{% for _ in notice.license_name %}
{% if loop.index < notice.license_name | length + notice.license_id | length + 3 %}={% endif %}
{% endfor %}

Used by packages:
{% for package in notice.packages %}
  - {{package.name}} {{package.version}}
{% endfor %}

{% if include_full_licenses and notice.license_text %}
License Text:
-------------
{{notice.license_text}}
{% endif %}

{% endfor %}

{% if copyright_notices %}
COPYRIGHT NOTICES
=================

{% for copyright in copyright_notices %}
Copyright {{copyright.holder}}
Used by: {{copyright.packages | join(sep=", ")}}
{% endfor %}

{% endif %}

{{footer}}
        "#
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use tempfile::tempdir;

    #[test]
    fn test_template_engine_config_default() {
        let config = TemplateEngineConfig::default();
        assert_eq!(config.template_dir, "templates");
        assert_eq!(config.default_extension, "html");
        assert!(!config.strict_mode);
    }

    #[test]
    fn test_template_context_default() {
        let context = TemplateContext::default();
        assert!(context.variables.is_empty());
        assert!(context.metadata.is_empty());
    }

    #[tokio::test]
    async fn test_template_engine_creation() {
        let temp_dir = tempdir().unwrap();
        let template_path = temp_dir.path().join("test.html");
        fs::write(&template_path, "<h1>{{title}}</h1>").unwrap();

        let config = TemplateEngineConfig {
            template_dir: temp_dir.path().to_string_lossy().to_string(),
            auto_reload: false,
            default_extension: "html".to_string(),
            strict_mode: false,
        };

        let engine = TemplateEngine::new(config).unwrap();
        assert!(engine.available_templates().len() > 0);
    }

    #[tokio::test]
    async fn test_template_rendering() {
        let temp_dir = tempdir().unwrap();
        let template_path = temp_dir.path().join("test.html");
        fs::write(&template_path, "<h1>{{title}}</h1><p>{{description}}</p>").unwrap();

        let config = TemplateEngineConfig {
            template_dir: temp_dir.path().to_string_lossy().to_string(),
            auto_reload: false,
            default_extension: "html".to_string(),
            strict_mode: false,
        };

        let engine = TemplateEngine::new(config).unwrap();

        let mut variables = HashMap::new();
        variables.insert("title".to_string(), serde_json::json!("Test Title"));
        variables.insert("description".to_string(), serde_json::json!("Test Description"));

        let context = TemplateContext {
            variables,
            metadata: HashMap::new(),
        };

        let result = engine.render("test.html", &context).unwrap();
        assert!(result.contains("Test Title"));
        assert!(result.contains("Test Description"));
    }

    #[test]
    fn test_template_registry() {
        let mut registry = TemplateRegistry::new();
        assert!(registry.get_default_engine().is_none());

        let config = TemplateEngineConfig::default();
        let engine = TemplateEngine::new(config).unwrap();
        registry.register_engine("test", engine);

        assert!(registry.get_engine("test").is_some());
        assert!(registry.get_default_engine().is_some());
    }
}