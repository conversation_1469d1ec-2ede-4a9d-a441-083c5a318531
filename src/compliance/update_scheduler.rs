//! # Update Scheduler
//!
//! Automated update scheduling system with configurable frequencies,
//! priority-based queuing, failure handling, and monitoring.

use crate::{
    compliance::{
        license_database_updater::{LicenseDatabaseUpdater, UpdateOperation, UpdateStatus, UpdateType},
        license_update_processor::{LicenseUpdateProcessor, UpdateSource},
        spdx_license_client::SpdxLicenseClient,
        osi_license_client::OsiLicenseClient,
    },
    error::{InfinitumError, Result},
};
use chrono::{DateTime, Utc, Duration as ChronoDuration};
use serde::{Deserialize, Serialize};
use std::{
    collections::{BinaryHeap, HashMap, VecDeque},
    sync::Arc,
    time::Duration,
};
use tokio::{
    sync::{RwLock, mpsc},
    time,
};
use tracing::{error, info, warn};

/// Update job priority
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
#[serde(rename_all = "lowercase")]
pub enum UpdatePriority {
    /// Low priority
    Low = 1,
    /// Normal priority
    Normal = 2,
    /// High priority
    High = 3,
    /// Critical priority
    Critical = 4,
}

/// Scheduled update job
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScheduledUpdate {
    /// Unique job ID
    pub id: String,
    /// Update source
    pub source: UpdateSource,
    /// Update type
    pub update_type: UpdateType,
    /// Priority
    pub priority: UpdatePriority,
    /// Schedule configuration
    pub schedule: ScheduleConfig,
    /// Next execution time
    pub next_execution: DateTime<Utc>,
    /// Last execution time
    pub last_execution: Option<DateTime<Utc>>,
    /// Success count
    pub success_count: u32,
    /// Failure count
    pub failure_count: u32,
    /// Is enabled
    pub enabled: bool,
    /// Maximum retry attempts
    pub max_retries: u32,
    /// Current retry count
    pub current_retry: u32,
    /// Backoff delay for retries
    pub retry_backoff_seconds: u64,
}

/// Schedule configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScheduleConfig {
    /// Cron expression (optional)
    pub cron_expression: Option<String>,
    /// Interval in seconds (for simple intervals)
    pub interval_seconds: Option<u64>,
    /// Fixed times (for daily schedules)
    pub fixed_times: Option<Vec<String>>,
    /// Timezone
    pub timezone: String,
}

/// Scheduler statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SchedulerStats {
    /// Total scheduled jobs
    pub total_jobs: usize,
    /// Active jobs
    pub active_jobs: usize,
    /// Completed jobs today
    pub completed_today: u32,
    /// Failed jobs today
    pub failed_today: u32,
    /// Average execution time
    pub average_execution_time_ms: f64,
    /// Queue length
    pub queue_length: usize,
    /// Last activity timestamp
    pub last_activity: Option<DateTime<Utc>>,
}

/// Scheduler configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SchedulerConfig {
    /// Maximum concurrent jobs
    pub max_concurrent_jobs: usize,
    /// Queue size limit
    pub queue_size_limit: usize,
    /// Enable automatic retries
    pub enable_auto_retry: bool,
    /// Default retry attempts
    pub default_max_retries: u32,
    /// Default retry backoff
    pub default_retry_backoff_seconds: u64,
    /// Job timeout in seconds
    pub job_timeout_seconds: u64,
    /// Enable detailed logging
    pub enable_detailed_logging: bool,
    /// Health check interval
    pub health_check_interval_seconds: u64,
}

/// Update Scheduler
pub struct UpdateScheduler {
    /// Configuration
    config: SchedulerConfig,
    /// Scheduled jobs
    jobs: Arc<RwLock<HashMap<String, ScheduledUpdate>>>,
    /// Job execution queue (priority queue)
    job_queue: Arc<RwLock<BinaryHeap<QueuedJob>>>,
    /// Active jobs
    active_jobs: Arc<RwLock<HashMap<String, tokio::task::JoinHandle<()>>>>,
    /// Statistics
    stats: Arc<RwLock<SchedulerStats>>,
    /// Database updater
    database_updater: Option<Arc<RwLock<LicenseDatabaseUpdater>>>,
    /// Update processor
    update_processor: Option<Arc<RwLock<LicenseUpdateProcessor>>>,
    /// SPDX client
    spdx_client: Option<Arc<RwLock<SpdxLicenseClient>>>,
    /// OSI client
    osi_client: Option<Arc<RwLock<OsiLicenseClient>>>,
    /// Control channel for shutdown
    shutdown_tx: mpsc::UnboundedSender<()>,
    shutdown_rx: Arc<RwLock<Option<mpsc::UnboundedReceiver<()>>>>,
}

/// Queued job for priority execution
#[derive(Debug, Clone, Eq, PartialEq)]
struct QueuedJob {
    /// Job priority (higher = more important)
    priority: UpdatePriority,
    /// Job ID
    job_id: String,
    /// Scheduled execution time
    scheduled_time: DateTime<Utc>,
}

impl Ord for QueuedJob {
    fn cmp(&self, other: &Self) -> std::cmp::Ordering {
        // Higher priority first, then earlier scheduled time
        other.priority.cmp(&self.priority)
            .then_with(|| self.scheduled_time.cmp(&other.scheduled_time))
    }
}

impl PartialOrd for QueuedJob {
    fn partial_cmp(&self, other: &Self) -> Option<std::cmp::Ordering> {
        Some(self.cmp(other))
    }
}

impl UpdateScheduler {
    /// Create new update scheduler
    pub fn new(config: SchedulerConfig) -> Self {
        let (shutdown_tx, shutdown_rx) = mpsc::unbounded_channel();

        Self {
            config,
            jobs: Arc::new(RwLock::new(HashMap::new())),
            job_queue: Arc::new(RwLock::new(BinaryHeap::new())),
            active_jobs: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(RwLock::new(SchedulerStats {
                total_jobs: 0,
                active_jobs: 0,
                completed_today: 0,
                failed_today: 0,
                average_execution_time_ms: 0.0,
                queue_length: 0,
                last_activity: None,
            })),
            database_updater: None,
            update_processor: None,
            spdx_client: None,
            osi_client: None,
            shutdown_tx,
            shutdown_rx: Arc::new(RwLock::new(Some(shutdown_rx))),
        }
    }

    /// Set database updater
    pub fn with_database_updater(mut self, updater: Arc<RwLock<LicenseDatabaseUpdater>>) -> Self {
        self.database_updater = Some(updater);
        self
    }

    /// Set update processor
    pub fn with_update_processor(mut self, processor: Arc<RwLock<LicenseUpdateProcessor>>) -> Self {
        self.update_processor = Some(processor);
        self
    }

    /// Set SPDX client
    pub fn with_spdx_client(mut self, client: Arc<RwLock<SpdxLicenseClient>>) -> Self {
        self.spdx_client = Some(client);
        self
    }

    /// Set OSI client
    pub fn with_osi_client(mut self, client: Arc<RwLock<OsiLicenseClient>>) -> Self {
        self.osi_client = Some(client);
        self
    }

    /// Start the scheduler
    pub async fn start(&self) -> Result<()> {
        info!("Starting update scheduler");

        // Start the main scheduler loop
        let scheduler = Arc::new(self.clone());
        let handle = tokio::spawn(async move {
            scheduler.run().await;
        });

        // Store the handle (though we don't use it directly)
        let _ = handle;

        // Start health check loop
        let health_scheduler = Arc::new(self.clone());
        tokio::spawn(async move {
            health_scheduler.health_check_loop().await;
        });

        Ok(())
    }

    /// Stop the scheduler
    pub async fn stop(&self) -> Result<()> {
        info!("Stopping update scheduler");

        // Send shutdown signal
        let _ = self.shutdown_tx.send(());

        // Wait for active jobs to complete
        let mut active_jobs = self.active_jobs.write().await;
        for (job_id, handle) in active_jobs.drain() {
            info!("Waiting for job {} to complete", job_id);
            let _ = handle.await;
        }

        Ok(())
    }

    /// Schedule a new update job
    pub async fn schedule_job(&self, mut job: ScheduledUpdate) -> Result<String> {
        // Calculate next execution time
        job.next_execution = self.calculate_next_execution(&job.schedule).await?;

        let job_id = job.id.clone();
        self.jobs.write().await.insert(job_id.clone(), job.clone());

        // Add to queue if enabled and due
        if job.enabled && job.next_execution <= Utc::now() {
            self.add_to_queue(&job).await?;
        }

        let mut stats = self.stats.write().await;
        stats.total_jobs = self.jobs.read().await.len();

        info!("Scheduled job: {} (next: {})", job_id, job.next_execution);
        Ok(job_id)
    }

    /// Enable/disable a job
    pub async fn set_job_enabled(&self, job_id: &str, enabled: bool) -> Result<()> {
        let mut jobs = self.jobs.write().await;
        if let Some(job) = jobs.get_mut(job_id) {
            job.enabled = enabled;

            if enabled {
                job.next_execution = self.calculate_next_execution(&job.schedule).await?;
                if job.next_execution <= Utc::now() {
                    self.add_to_queue(job).await?;
                }
            } else {
                // Remove from queue
                self.remove_from_queue(job_id).await;
            }
        }
        Ok(())
    }

    /// Get scheduled jobs
    pub async fn get_scheduled_jobs(&self) -> Vec<ScheduledUpdate> {
        self.jobs.read().await.values().cloned().collect()
    }

    /// Get job by ID
    pub async fn get_job(&self, job_id: &str) -> Option<ScheduledUpdate> {
        self.jobs.read().await.get(job_id).cloned()
    }

    /// Get scheduler statistics
    pub async fn get_stats(&self) -> SchedulerStats {
        self.stats.read().await.clone()
    }

    /// Force execute a job immediately
    pub async fn execute_job_now(&self, job_id: &str) -> Result<()> {
        let jobs = self.jobs.read().await;
        if let Some(job) = jobs.get(job_id) {
            if job.enabled {
                self.add_to_queue(job).await?;
            }
        }
        Ok(())
    }

    /// Remove a scheduled job
    pub async fn remove_job(&self, job_id: &str) -> Result<bool> {
        let mut jobs = self.jobs.write().await;
        if let Some(_) = jobs.remove(job_id) {
            self.remove_from_queue(job_id).await;

            let mut stats = self.stats.write().await;
            stats.total_jobs = jobs.len();

            info!("Removed job: {}", job_id);
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// Main scheduler loop
    async fn run(&self) {
        info!("Scheduler main loop started");

        loop {
            // Check for shutdown signal
            if let Some(ref mut rx) = *self.shutdown_rx.write().await {
                if let Ok(_) = rx.try_recv() {
                    info!("Shutdown signal received, stopping scheduler");
                    break;
                }
            }

            // Process due jobs
            if let Err(e) = self.process_due_jobs().await {
                error!("Error processing due jobs: {}", e);
            }

            // Check for new jobs to schedule
            if let Err(e) = self.check_and_schedule_jobs().await {
                error!("Error checking jobs: {}", e);
            }

            // Small delay to prevent busy looping
            time::sleep(Duration::from_millis(100)).await;
        }

        info!("Scheduler main loop stopped");
    }

    /// Process jobs that are due for execution
    async fn process_due_jobs(&self) -> Result<()> {
        let mut queue = self.job_queue.write().await;
        let mut active_jobs = self.active_jobs.write().await;

        // Check concurrent job limit
        if active_jobs.len() >= self.config.max_concurrent_jobs {
            return Ok(());
        }

        // Process jobs from queue
        while active_jobs.len() < self.config.max_concurrent_jobs && !queue.is_empty() {
            if let Some(queued_job) = queue.pop() {
                if queued_job.scheduled_time <= Utc::now() {
                    // Execute the job
                    let scheduler = Arc::new(self.clone());
                    let job_id = queued_job.job_id.clone();

                    let handle = tokio::spawn(async move {
                        let result = scheduler.execute_job(&job_id).await;
                        if let Err(e) = result {
                            error!("Job {} execution failed: {}", job_id, e);
                        }
                    });

                    active_jobs.insert(job_id, handle);

                    let mut stats = self.stats.write().await;
                    stats.active_jobs = active_jobs.len();
                    stats.queue_length = queue.len();
                } else {
                    // Job not due yet, put it back
                    queue.push(queued_job);
                    break;
                }
            }
        }

        Ok(())
    }

    /// Execute a specific job
    async fn execute_job(&self, job_id: &str) -> Result<()> {
        let start_time = Utc::now();

        // Get job details
        let job = {
            let jobs = self.jobs.read().await;
            jobs.get(job_id).cloned()
        };

        if let Some(mut job) = job {
            info!("Executing job: {} ({:?})", job_id, job.update_type);

            let result = match job.source {
                UpdateSource::Spdx => self.execute_spdx_update().await,
                UpdateSource::Osi => self.execute_osi_update().await,
                _ => {
                    warn!("Unsupported update source: {:?}", job.source);
                    Ok(())
                }
            };

            let execution_time = Utc::now().signed_duration_since(start_time).num_milliseconds() as f64;

            // Update job statistics
            {
                let mut jobs = self.jobs.write().await;
                if let Some(job_ref) = jobs.get_mut(job_id) {
                    job_ref.last_execution = Some(Utc::now());

                    if result.is_ok() {
                        job_ref.success_count += 1;
                        job_ref.current_retry = 0;
                    } else {
                        job_ref.failure_count += 1;

                        if self.config.enable_auto_retry && job_ref.current_retry < job_ref.max_retries {
                            job_ref.current_retry += 1;
                            // Reschedule with backoff
                            job_ref.next_execution = Utc::now() + ChronoDuration::seconds(job_ref.retry_backoff_seconds as i64);
                            self.add_to_queue(job_ref).await?;
                        }
                    }

                    // Schedule next execution
                    job_ref.next_execution = self.calculate_next_execution(&job_ref.schedule).await?;
                }
            }

            // Update global statistics
            {
                let mut stats = self.stats.write().await;
                stats.last_activity = Some(Utc::now());

                if result.is_ok() {
                    stats.completed_today += 1;
                } else {
                    stats.failed_today += 1;
                }

                // Update average execution time
                let total_executions = stats.completed_today + stats.failed_today;
                if total_executions > 0 {
                    stats.average_execution_time_ms = (
                        stats.average_execution_time_ms * (total_executions - 1) as f64 +
                        execution_time
                    ) / total_executions as f64;
                }
            }

            // Remove from active jobs
            {
                let mut active_jobs = self.active_jobs.write().await;
                active_jobs.remove(job_id);

                let mut stats = self.stats.write().await;
                stats.active_jobs = active_jobs.len();
            }

            result
        } else {
            Err(InfinitumError::NotFound {
                resource: "job".to_string(),
                id: job_id.to_string(),
            })
        }
    }

    /// Execute SPDX update
    async fn execute_spdx_update(&self) -> Result<()> {
        if let Some(processor) = &self.update_processor {
            let mut processor = processor.write().await;
            let updates = processor.process_spdx_updates().await?;
            info!("Processed {} SPDX updates", updates.len());
        }
        Ok(())
    }

    /// Execute OSI update
    async fn execute_osi_update(&self) -> Result<()> {
        if let Some(processor) = &self.update_processor {
            let mut processor = processor.write().await;
            let updates = processor.process_osi_updates().await?;
            info!("Processed {} OSI updates", updates.len());
        }
        Ok(())
    }

    /// Check and schedule jobs that are due
    async fn check_and_schedule_jobs(&self) -> Result<()> {
        let jobs = self.jobs.read().await;
        let now = Utc::now();

        for job in jobs.values() {
            if job.enabled && job.next_execution <= now {
                // Check if already in queue
                let queue = self.job_queue.read().await;
                let already_queued = queue.iter().any(|qj| qj.job_id == job.id);

                if !already_queued {
                    self.add_to_queue(job).await?;
                }
            }
        }

        Ok(())
    }

    /// Add job to execution queue
    async fn add_to_queue(&self, job: &ScheduledUpdate) -> Result<()> {
        let mut queue = self.job_queue.write().await;

        if queue.len() >= self.config.queue_size_limit {
            return Err(InfinitumError::ResourceLimitExceeded {
                resource: "job_queue".to_string(),
                limit: self.config.queue_size_limit,
                current: queue.len(),
            });
        }

        let queued_job = QueuedJob {
            priority: job.priority.clone(),
            job_id: job.id.clone(),
            scheduled_time: job.next_execution,
        };

        queue.push(queued_job);

        let mut stats = self.stats.write().await;
        stats.queue_length = queue.len();

        Ok(())
    }

    /// Remove job from execution queue
    async fn remove_from_queue(&self, job_id: &str) {
        let mut queue = self.job_queue.write().await;
        queue.retain(|qj| qj.job_id != job_id);

        let mut stats = self.stats.write().await;
        stats.queue_length = queue.len();
    }

    /// Calculate next execution time for a job
    async fn calculate_next_execution(&self, schedule: &ScheduleConfig) -> Result<DateTime<Utc>> {
        let now = Utc::now();

        if let Some(interval_seconds) = schedule.interval_seconds {
            Ok(now + ChronoDuration::seconds(interval_seconds as i64))
        } else if let Some(cron_expr) = &schedule.cron_expression {
            // For now, use interval as fallback
            // In a real implementation, you'd parse the cron expression
            Ok(now + ChronoDuration::hours(24))
        } else {
            // Default to daily
            Ok(now + ChronoDuration::hours(24))
        }
    }

    /// Health check loop
    async fn health_check_loop(&self) {
        let mut interval = time::interval(Duration::from_secs(self.config.health_check_interval_seconds));

        loop {
            interval.tick().await;

            // Check for shutdown signal
            if let Some(ref mut rx) = *self.shutdown_rx.write().await {
                if let Ok(_) = rx.try_recv() {
                    break;
                }
            }

            // Perform health checks
            if let Err(e) = self.perform_health_checks().await {
                error!("Health check failed: {}", e);
            }
        }
    }

    /// Perform health checks
    async fn perform_health_checks(&self) -> Result<()> {
        // Check if scheduler is responsive
        let stats = self.stats.read().await;
        let now = Utc::now();

        if let Some(last_activity) = stats.last_activity {
            let inactive_duration = now.signed_duration_since(last_activity);
            if inactive_duration.num_hours() > 24 {
                warn!("Scheduler has been inactive for {} hours", inactive_duration.num_hours());
            }
        }

        // Check queue health
        let queue_len = self.job_queue.read().await.len();
        if queue_len > self.config.queue_size_limit / 2 {
            warn!("Job queue is at {}% capacity", (queue_len * 100) / self.config.queue_size_limit);
        }

        Ok(())
    }
}

impl Default for SchedulerConfig {
    fn default() -> Self {
        Self {
            max_concurrent_jobs: 3,
            queue_size_limit: 1000,
            enable_auto_retry: true,
            default_max_retries: 3,
            default_retry_backoff_seconds: 300, // 5 minutes
            job_timeout_seconds: 3600, // 1 hour
            enable_detailed_logging: true,
            health_check_interval_seconds: 300, // 5 minutes
        }
    }
}

impl Clone for UpdateScheduler {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            jobs: Arc::clone(&self.jobs),
            job_queue: Arc::clone(&self.job_queue),
            active_jobs: Arc::clone(&self.active_jobs),
            stats: Arc::clone(&self.stats),
            database_updater: self.database_updater.as_ref().map(Arc::clone),
            update_processor: self.update_processor.as_ref().map(Arc::clone),
            spdx_client: self.spdx_client.as_ref().map(Arc::clone),
            osi_client: self.osi_client.as_ref().map(Arc::clone),
            shutdown_tx: self.shutdown_tx.clone(),
            shutdown_rx: Arc::clone(&self.shutdown_rx),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_scheduler_creation() {
        let config = SchedulerConfig::default();
        let scheduler = UpdateScheduler::new(config);
        assert_eq!(scheduler.config.max_concurrent_jobs, 3);
    }

    #[test]
    fn test_default_config() {
        let config = SchedulerConfig::default();
        assert!(config.enable_auto_retry);
        assert_eq!(config.default_max_retries, 3);
        assert_eq!(config.job_timeout_seconds, 3600);
    }

    #[tokio::test]
    async fn test_job_scheduling() {
        let config = SchedulerConfig::default();
        let scheduler = UpdateScheduler::new(config);

        let job = ScheduledUpdate {
            id: "test_job".to_string(),
            source: UpdateSource::Spdx,
            update_type: UpdateType::Full,
            priority: UpdatePriority::Normal,
            schedule: ScheduleConfig {
                cron_expression: None,
                interval_seconds: Some(3600),
                fixed_times: None,
                timezone: "UTC".to_string(),
            },
            next_execution: Utc::now(),
            last_execution: None,
            success_count: 0,
            failure_count: 0,
            enabled: true,
            max_retries: 3,
            current_retry: 0,
            retry_backoff_seconds: 300,
        };

        let job_id = scheduler.schedule_job(job).await.unwrap();
        assert_eq!(job_id, "test_job");

        let jobs = scheduler.get_scheduled_jobs().await;
        assert_eq!(jobs.len(), 1);
        assert_eq!(jobs[0].id, "test_job");
    }
}