use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::{env, path::Path};
use tracing::info;
use crate::observability::ObservabilityConfig;

/// Main application configuration
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct Config {
    pub server: ServerConfig,
    pub database: DatabaseConfig,
    pub redis: RedisConfig,
    pub security: SecurityConfig,
    pub scanning: ScanningConfig,
    pub compliance: ComplianceConfig,
    pub vulnerability: VulnerabilityConfig,
    pub blockchain: BlockchainConfig,
    pub logging: LoggingConfig,
    pub metrics: MetricsConfig,
    pub observability: ObservabilityConfig,
    pub external_services: ExternalServicesConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
    pub workers: usize,
    pub timeout: u64,
    pub keep_alive: u64,
    pub max_request_size: usize,
    pub cors_origins: Vec<String>,
}

/// API-specific configuration (alias for ServerConfig)
pub type ApiConfig = ServerConfig;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub connect_timeout: u64,
    pub idle_timeout: u64,
    pub max_lifetime: u64,
    pub enabled: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RedisConfig {
    pub url: String,
    pub max_connections: u32,
    pub timeout: u64,
    pub pool_timeout: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    pub jwt_secret: String,
    pub jwt_expiration: u64,
    pub jwt_refresh_expiration: u64,
    pub encryption_key: String,
    pub hash_rounds: u32,
    pub api_rate_limit: u32,
    pub api_rate_window: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScanningConfig {
    pub timeout: u64,
    pub max_concurrent: usize,
    pub temp_dir: String,
    pub cleanup_interval: u64,
    pub max_file_size: u64,
    pub supported_languages: Vec<String>,
    pub license_scanner: crate::scanners::LicenseScannerConfig,
    pub osv_scanner: crate::scanners::OsvScannerConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceConfig {
    pub frameworks: Vec<String>,
    pub report_output_dir: String,
    pub report_retention_days: u32,
    pub auto_cleanup: bool,
    pub template_dir: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VulnerabilityConfig {
    pub enabled: bool,
    pub severity_threshold: String,
    pub auto_update: bool,
    pub update_interval: u64,
    pub sources: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlockchainConfig {
    pub enabled: bool,
    pub network: String,
    pub channel: String,
    pub chaincode: String,
    pub msp_id: String,
    pub peer_endpoint: String,
    pub orderer_endpoint: String,
    pub cert_path: String,
    pub key_path: String,
    pub ca_path: String,
    pub storage_backend: String,
    pub storage_path: String,
    pub chain_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    pub level: String,
    pub format: String,
    pub file: Option<String>,
    pub max_size: String,
    pub max_files: u32,
    pub compress: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricsConfig {
    pub enabled: bool,
    pub port: u16,
    pub path: String,
    pub prometheus_endpoint: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ExternalServicesConfig {
    pub nvd: NvdConfig,
    pub github: GitHubConfig,
    pub snyk: SnykConfig,
    pub osv: OsvConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NvdConfig {
    pub api_key: Option<String>,
    pub base_url: String,
    pub rate_limit: u32,
    pub timeout: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitHubConfig {
    pub token: Option<String>,
    pub api_url: String,
    pub timeout: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SnykConfig {
    pub api_token: Option<String>,
    pub org_id: Option<String>,
    pub base_url: String,
    pub timeout: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OsvConfig {
    pub api_url: String,
    pub timeout: u64,
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            host: env::var("SERVER_HOST").unwrap_or_else(|_| "0.0.0.0".to_string()),
            port: env::var("SERVER_PORT")
                .unwrap_or_else(|_| "8080".to_string())
                .parse()
                .unwrap_or(8080),
            workers: env::var("SERVER_WORKERS")
                .unwrap_or_else(|_| "4".to_string())
                .parse()
                .unwrap_or(4),
            timeout: 30,
            keep_alive: 75,
            max_request_size: 16 * 1024 * 1024, // 16MB
            cors_origins: vec!["*".to_string()],
        }
    }
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            url: env::var("DATABASE_URL")
                .unwrap_or_else(|_| "sqlite:infinitum_signal.db".to_string()),
            max_connections: 10,
            min_connections: 1,
            connect_timeout: 30,
            idle_timeout: 600,
            max_lifetime: 3600,
            enabled: env::var("DATABASE_ENABLED")
                .unwrap_or_else(|_| "false".to_string())
                .parse()
                .unwrap_or(false),
        }
    }
}

impl Default for RedisConfig {
    fn default() -> Self {
        Self {
            url: env::var("REDIS_URL").unwrap_or_else(|_| "redis://localhost:6379".to_string()),
            max_connections: 10,
            timeout: 5,
            pool_timeout: 30,
        }
    }
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            jwt_secret: env::var("JWT_SECRET")
                .unwrap_or_else(|_| "your-super-secret-jwt-key".to_string()),
            jwt_expiration: 3600,
            jwt_refresh_expiration: 86400,
            encryption_key: env::var("ENCRYPTION_KEY")
                .unwrap_or_else(|_| "your-32-byte-encryption-key-here".to_string()),
            hash_rounds: 12,
            api_rate_limit: 1000,
            api_rate_window: 3600,
        }
    }
}

impl Default for ScanningConfig {
    fn default() -> Self {
        Self {
            timeout: 300,
            max_concurrent: 5,
            temp_dir: "/tmp/infinitum-scans".to_string(),
            cleanup_interval: 3600,
            max_file_size: 100 * 1024 * 1024, // 100MB
            supported_languages: vec![
                "rust".to_string(),
                "javascript".to_string(),
                "python".to_string(),
                "java".to_string(),
                "go".to_string(),
                "c".to_string(),
                "cpp".to_string(),
            ],
            license_scanner: crate::scanners::LicenseScannerConfig::default(),
            osv_scanner: crate::scanners::OsvScannerConfig::default(),
        }
    }
}

impl Default for ComplianceConfig {
    fn default() -> Self {
        Self {
            frameworks: vec![
                "cert-in".to_string(),
                "sebi".to_string(),
                "iso27001".to_string(),
            ],
            report_output_dir: "/var/lib/infinitum-signal/reports".to_string(),
            report_retention_days: 90,
            auto_cleanup: true,
            template_dir: "templates".to_string(),
        }
    }
}

impl Default for VulnerabilityConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            severity_threshold: "medium".to_string(),
            auto_update: true,
            update_interval: 3600,
            sources: vec!["nvd".to_string(), "github".to_string(), "osv".to_string()],
        }
    }
}

impl Default for BlockchainConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            network: "hyperledger-fabric".to_string(),
            channel: "compliance-channel".to_string(),
            chaincode: "infinitum-compliance".to_string(),
            msp_id: "InfinitumMSP".to_string(),
            peer_endpoint: "grpc://localhost:7051".to_string(),
            orderer_endpoint: "grpc://localhost:7050".to_string(),
            cert_path: "/path/to/cert.pem".to_string(),
            key_path: "/path/to/key.pem".to_string(),
            ca_path: "/path/to/ca.pem".to_string(),
            storage_backend: "filesystem".to_string(),
            storage_path: "./blockchain_data".to_string(),
            chain_id: "infinitum-chain".to_string(),
        }
    }
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            level: env::var("RUST_LOG").unwrap_or_else(|_| "info".to_string()),
            format: "json".to_string(),
            file: None,
            max_size: "100MB".to_string(),
            max_files: 10,
            compress: true,
        }
    }
}

impl Default for MetricsConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            port: 9090,
            path: "/metrics".to_string(),
            prometheus_endpoint: None,
        }
    }
}

impl Default for NvdConfig {
    fn default() -> Self {
        Self {
            api_key: env::var("NVD_API_KEY").ok(),
            base_url: "https://services.nvd.nist.gov/rest/json".to_string(),
            rate_limit: 50,
            timeout: 30,
        }
    }
}

impl Default for GitHubConfig {
    fn default() -> Self {
        Self {
            token: env::var("GITHUB_TOKEN").ok(),
            api_url: "https://api.github.com".to_string(),
            timeout: 30,
        }
    }
}

impl Default for SnykConfig {
    fn default() -> Self {
        Self {
            api_token: env::var("SNYK_API_TOKEN").ok(),
            org_id: env::var("SNYK_ORG_ID").ok(),
            base_url: "https://api.snyk.io".to_string(),
            timeout: 30,
        }
    }
}

impl Default for OsvConfig {
    fn default() -> Self {
        Self {
            api_url: "https://api.osv.dev".to_string(),
            timeout: 30,
        }
    }
}

impl Config {
    /// Load configuration from file with environment variable overrides
    pub async fn load<P: AsRef<Path>>(path: P) -> Result<Self> {
        let path = path.as_ref();

        let config = if path.exists() {
            info!("Loading configuration from {}", path.display());
            let content = tokio::fs::read_to_string(path)
                .await
                .with_context(|| format!("Failed to read config file: {}", path.display()))?;

            serde_yaml::from_str(&content)
                .with_context(|| format!("Failed to parse config file: {}", path.display()))?
        } else {
            info!("Config file not found, using defaults with environment variables");
            Self::default()
        };

        Ok(config)
    }

    /// Validate configuration
    pub fn validate(&self) -> Result<()> {
        // Validate server config
        if self.server.port == 0 {
            anyhow::bail!("Server port cannot be 0");
        }

        // Validate database URL
        if self.database.url.is_empty() {
            anyhow::bail!("Database URL cannot be empty");
        }

        // Validate JWT secret
        if self.security.jwt_secret.len() < 32 {
            anyhow::bail!("JWT secret must be at least 32 characters");
        }

        // Validate encryption key
        if self.security.encryption_key.len() < 32 {
            anyhow::bail!("Encryption key must be at least 32 characters");
        }

        Ok(())
    }

    /// Get observability configuration with defaults
    pub fn get_observability_config(&self) -> crate::observability::ObservabilityConfig {
        // For now, return default. In the future, this could be loaded from config file
        crate::observability::ObservabilityConfig::default()
    }
}
