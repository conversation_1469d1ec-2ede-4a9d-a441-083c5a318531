//! # Database Module
//!
//! This module provides database connectivity, models, and data access layers
//! for the Infinitium Signal platform using PostgreSQL with SQLx.

pub mod connection;
pub mod migrations;
pub mod models;
pub mod schema;

use crate::{config::DatabaseConfig, error::Result};
use sqlx::{PgPool, Row};
use uuid::Uuid;

use tracing::{info, instrument};

/// Re-export database types
pub use connection::{ConnectionPool, DatabaseConnection};
pub use models::*;
pub use schema::*;

/// Database service for managing connections and operations
pub struct DatabaseService {
    pool: PgPool,
    config: DatabaseConfig,
}

/// Database health status
#[derive(Debug, Clone)]
pub struct DatabaseHealth {
    /// Connection status
    pub connected: bool,
    /// Pool statistics
    pub pool_stats: PoolStats,
    /// Database version
    pub version: Option<String>,
    /// Last health check
    pub last_check: chrono::DateTime<chrono::Utc>,
}

/// Connection pool statistics
#[derive(Debug, Clone)]
pub struct PoolStats {
    /// Total connections
    pub total_connections: u32,
    /// Active connections
    pub active_connections: u32,
    /// Idle connections
    pub idle_connections: u32,
    /// Maximum connections
    pub max_connections: u32,
}

impl DatabaseService {
    /// Create new database service
    pub async fn new(config: DatabaseConfig) -> Result<Self> {
        let pool = connection::create_pool(&config).await?;

        Ok(Self { pool, config })
    }

    /// Get database connection pool
    pub fn pool(&self) -> &PgPool {
        &self.pool
    }

    /// Run database migrations
    #[instrument(skip(self))]
    pub async fn migrate(&self) -> Result<()> {
        info!("Running database migrations");
        migrations::run_migrations(&self.pool).await
    }

    /// Check database health
    #[instrument(skip(self))]
    pub async fn health_check(&self) -> Result<DatabaseHealth> {
        let connected = self.check_connection().await;
        let pool_stats = self.get_pool_stats();
        let version = if connected {
            self.get_database_version().await.ok()
        } else {
            None
        };

        Ok(DatabaseHealth {
            connected,
            pool_stats,
            version,
            last_check: chrono::Utc::now(),
        })
    }

    /// Check if database connection is working
    async fn check_connection(&self) -> bool {
        sqlx::query("SELECT 1").fetch_one(&self.pool).await.is_ok()
    }

    /// Get connection pool statistics
    fn get_pool_stats(&self) -> PoolStats {
        PoolStats {
            total_connections: self.pool.size(),
            active_connections: self.pool.num_idle() as u32,
            idle_connections: self.pool.num_idle() as u32,
            max_connections: self.config.max_connections,
        }
    }

    /// Get database version
    async fn get_database_version(&self) -> Result<String> {
        let row = sqlx::query("SELECT version()")
            .fetch_one(&self.pool)
            .await?;

        Ok(row.get::<String, _>(0))
    }

    /// Initialize database schema
    pub async fn initialize_schema(&self) -> Result<()> {
        schema::create_tables(&self.pool).await
    }

    /// Clean up old records
    #[instrument(skip(self))]
    pub async fn cleanup_old_records(&self, retention_days: i64) -> Result<u64> {
        let cutoff_date = chrono::Utc::now() - chrono::Duration::days(retention_days);

        let result = sqlx::query("DELETE FROM scan_results WHERE created_at < $1")
            .bind(cutoff_date)
            .execute(&self.pool)
            .await?;

        info!(
            deleted_records = result.rows_affected(),
            retention_days = retention_days,
            "Cleaned up old scan results"
        );

        Ok(result.rows_affected())
    }

    /// Get database statistics
    pub async fn get_statistics(&self) -> Result<DatabaseStatistics> {
        let scan_results_count = sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM scan_results")
            .fetch_one(&self.pool)
            .await? as u64;

        let compliance_reports_count =
            sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM compliance_reports")
                .fetch_one(&self.pool)
                .await? as u64;

        let vulnerabilities_count =
            sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM vulnerabilities")
                .fetch_one(&self.pool)
                .await? as u64;

        let blockchain_records_count =
            sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM blockchain_records")
                .fetch_one(&self.pool)
                .await? as u64;

        Ok(DatabaseStatistics {
            scan_results_count: scan_results_count as u64,
            compliance_reports_count: compliance_reports_count as u64,
            vulnerabilities_count: vulnerabilities_count as u64,
            blockchain_records_count: blockchain_records_count as u64,
            last_updated: chrono::Utc::now(),
        })
    }

    /// Backup database
    #[instrument(skip(self))]
    pub async fn backup(&self, backup_path: &str) -> Result<()> {
        info!(backup_path = backup_path, "Starting database backup");

        // This would typically use pg_dump or similar
        // For now, we'll create a simple SQL export

        let tables = vec![
            "scan_results",
            "compliance_reports",
            "vulnerabilities",
            "blockchain_records",
            "software_components",
            "hardware_components",
        ];

        let mut backup_content = String::new();
        backup_content.push_str("-- Infinitium Signal Database Backup\n");
        backup_content.push_str(&format!("-- Generated: {}\n\n", chrono::Utc::now()));

        for table in tables {
            let rows = sqlx::query(&format!("SELECT * FROM {}", table))
                .fetch_all(&self.pool)
                .await?;

            backup_content.push_str(&format!("-- Table: {}\n", table));
            backup_content.push_str(&format!("-- Records: {}\n\n", rows.len()));
        }

        tokio::fs::write(backup_path, backup_content).await?;
        info!("Database backup completed");

        Ok(())
    }

    /// Restore database from backup
    #[instrument(skip(self))]
    pub async fn restore(&self, backup_path: &str) -> Result<()> {
        info!(backup_path = backup_path, "Starting database restore");

        let backup_content = tokio::fs::read_to_string(backup_path).await?;

        // Execute backup SQL
        sqlx::query(&backup_content).execute(&self.pool).await?;

        info!("Database restore completed");
        Ok(())
    }

    /// Find scan result by ID
    pub async fn find_scan_result_by_id(&self, id: Uuid) -> Result<Option<models::ScanResult>> {
        let result = sqlx::query_as::<_, models::ScanResult>(
            "SELECT * FROM scan_results WHERE id = $1"
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await?;

        Ok(result)
    }

    /// Save scan result
    pub async fn save_scan_result(&self, scan_result: &models::ScanResult) -> Result<()> {
        sqlx::query(
            r#"
            INSERT INTO scan_results (
                id, scan_type, target_path, target_hash, status, started_at,
                completed_at, error_message, result_data, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            ON CONFLICT (id) DO UPDATE SET
                status = EXCLUDED.status,
                completed_at = EXCLUDED.completed_at,
                error_message = EXCLUDED.error_message,
                result_data = EXCLUDED.result_data,
                updated_at = EXCLUDED.updated_at
            "#
        )
        .bind(scan_result.id)
        .bind(&scan_result.scan_type)
        .bind(&scan_result.target_path)
        .bind(&scan_result.target_hash)
        .bind(&scan_result.status)
        .bind(scan_result.started_at)
        .bind(scan_result.completed_at)
        .bind(&scan_result.error_message)
        .bind(&scan_result.result_data)
        .bind(scan_result.created_at)
        .bind(scan_result.updated_at)
        .execute(&self.pool)
        .await?;

        Ok(())
    }
}

/// Database statistics
#[derive(Debug, Clone)]
pub struct DatabaseStatistics {
    /// Number of scan results
    pub scan_results_count: u64,
    /// Number of compliance reports
    pub compliance_reports_count: u64,
    /// Number of vulnerabilities
    pub vulnerabilities_count: u64,
    /// Number of blockchain records
    pub blockchain_records_count: u64,
    /// Last updated timestamp
    pub last_updated: chrono::DateTime<chrono::Utc>,
}

/// Database repository trait for common operations
#[async_trait::async_trait]
pub trait Repository<T> {
    /// Create a new record
    async fn create(&self, entity: &T) -> Result<T>;

    /// Find record by ID
    async fn find_by_id(&self, id: uuid::Uuid) -> Result<Option<T>>;

    /// Update existing record
    async fn update(&self, entity: &T) -> Result<T>;

    /// Delete record by ID
    async fn delete(&self, id: uuid::Uuid) -> Result<bool>;

    /// List records with pagination
    async fn list(&self, offset: u64, limit: u64) -> Result<Vec<T>>;

    /// Count total records
    async fn count(&self) -> Result<u64>;
}

/// Database transaction wrapper
pub struct DatabaseTransaction<'a> {
    tx: sqlx::Transaction<'a, sqlx::Postgres>,
}

impl<'a> DatabaseTransaction<'a> {
    /// Begin a new transaction
    pub async fn begin(pool: &PgPool) -> Result<DatabaseTransaction<'_>> {
        let tx = pool.begin().await?;
        Ok(DatabaseTransaction { tx })
    }

    /// Commit the transaction
    pub async fn commit(self) -> Result<()> {
        self.tx.commit().await?;
        Ok(())
    }

    /// Rollback the transaction
    pub async fn rollback(self) -> Result<()> {
        self.tx.rollback().await?;
        Ok(())
    }

    /// Get the underlying transaction
    pub fn transaction(&mut self) -> &mut sqlx::Transaction<'a, sqlx::Postgres> {
        &mut self.tx
    }
}

/// Database query builder for complex queries
pub struct QueryBuilder {
    query: String,
    params: Vec<serde_json::Value>,
}

impl QueryBuilder {
    /// Create new query builder
    pub fn new() -> Self {
        Self {
            query: String::new(),
            params: Vec::new(),
        }
    }

    /// Add SELECT clause
    pub fn select(mut self, columns: &[&str]) -> Self {
        self.query.push_str("SELECT ");
        self.query.push_str(&columns.join(", "));
        self
    }

    /// Add FROM clause
    pub fn from(mut self, table: &str) -> Self {
        self.query.push_str(" FROM ");
        self.query.push_str(table);
        self
    }

    /// Add WHERE clause
    pub fn where_clause(mut self, condition: &str) -> Self {
        if self.query.contains(" WHERE ") {
            self.query.push_str(" AND ");
        } else {
            self.query.push_str(" WHERE ");
        }
        self.query.push_str(condition);
        self
    }

    /// Add ORDER BY clause
    pub fn order_by(mut self, column: &str, direction: &str) -> Self {
        self.query.push_str(" ORDER BY ");
        self.query.push_str(column);
        self.query.push(' ');
        self.query.push_str(direction);
        self
    }

    /// Add LIMIT clause
    pub fn limit(mut self, limit: u64) -> Self {
        self.query.push_str(" LIMIT ");
        self.query.push_str(&limit.to_string());
        self
    }

    /// Add OFFSET clause
    pub fn offset(mut self, offset: u64) -> Self {
        self.query.push_str(" OFFSET ");
        self.query.push_str(&offset.to_string());
        self
    }

    /// Add parameter
    pub fn param<T: Into<serde_json::Value>>(mut self, value: T) -> Self {
        self.params.push(value.into());
        self
    }

    /// Build the final query
    pub fn build(self) -> (String, Vec<serde_json::Value>) {
        (self.query, self.params)
    }
}

impl Default for QueryBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// Database connection pool manager
pub struct PoolManager {
    pools: std::collections::HashMap<String, PgPool>,
}

impl PoolManager {
    /// Create new pool manager
    pub fn new() -> Self {
        Self {
            pools: std::collections::HashMap::new(),
        }
    }

    /// Add a named connection pool
    pub async fn add_pool(&mut self, name: String, config: &DatabaseConfig) -> Result<()> {
        let pool = connection::create_pool(config).await?;
        self.pools.insert(name, pool);
        Ok(())
    }

    /// Get a connection pool by name
    pub fn get_pool(&self, name: &str) -> Option<&PgPool> {
        self.pools.get(name)
    }

    /// Remove a connection pool
    pub fn remove_pool(&mut self, name: &str) -> Option<PgPool> {
        self.pools.remove(name)
    }

    /// Close all connection pools
    pub async fn close_all(&mut self) {
        for (name, pool) in self.pools.drain() {
            pool.close().await;
            info!(pool_name = name, "Closed database connection pool");
        }
    }
}

impl Default for PoolManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_query_builder() {
        let (query, _params) = QueryBuilder::new()
            .select(&["id", "name", "created_at"])
            .from("scan_results")
            .where_clause("status = 'completed'")
            .order_by("created_at", "DESC")
            .limit(10)
            .offset(0)
            .build();

        assert!(query.contains("SELECT id, name, created_at"));
        assert!(query.contains("FROM scan_results"));
        assert!(query.contains("WHERE status = 'completed'"));
        assert!(query.contains("ORDER BY created_at DESC"));
        assert!(query.contains("LIMIT 10"));
        assert!(query.contains("OFFSET 0"));
    }

    #[test]
    fn test_pool_manager() {
        let manager = PoolManager::new();
        assert!(manager.get_pool("test").is_none());
    }

    #[test]
    fn test_database_statistics() {
        let stats = DatabaseStatistics {
            scan_results_count: 100,
            compliance_reports_count: 50,
            vulnerabilities_count: 200,
            blockchain_records_count: 75,
            last_updated: chrono::Utc::now(),
        };

        assert_eq!(stats.scan_results_count, 100);
        assert_eq!(stats.compliance_reports_count, 50);
    }
}
