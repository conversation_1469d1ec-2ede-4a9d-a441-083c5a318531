//! Database models for the Infinitium Signal system

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

/// Scan result stored in the database
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ScanResult {
    pub id: Uuid,
    pub scan_type: String,
    pub target_path: String,
    pub target_hash: String,
    pub status: ScanStatus,
    pub started_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
    pub error_message: Option<String>,
    pub result_data: serde_json::Value,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// Scan status enumeration
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "scan_status", rename_all = "lowercase")]
pub enum ScanStatus {
    Pending,
    Running,
    Completed,
    Failed,
    Cancelled,
}

/// SBOM (Software Bill of Materials) record
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct SbomRecord {
    pub id: Uuid,
    pub scan_result_id: Uuid,
    pub package_name: String,
    pub package_version: String,
    pub package_type: String,
    pub package_manager: String,
    pub file_path: String,
    pub license: Option<String>,
    pub description: Option<String>,
    pub homepage: Option<String>,
    pub repository: Option<String>,
    pub hash: Option<String>,
    pub created_at: DateTime<Utc>,
}

/// HBOM (Hardware Bill of Materials) record
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct HbomRecord {
    pub id: Uuid,
    pub scan_result_id: Uuid,
    pub component_name: String,
    pub component_version: Option<String>,
    pub component_type: String,
    pub manufacturer: Option<String>,
    pub model: Option<String>,
    pub serial_number: Option<String>,
    pub firmware_version: Option<String>,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
}

/// Vulnerability record
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct VulnerabilityRecord {
    pub id: Uuid,
    pub cve_id: String,
    pub title: String,
    pub description: String,
    pub severity: VulnerabilitySeverity,
    pub cvss_score: Option<f32>,
    pub cvss_vector: Option<String>,
    pub published_date: DateTime<Utc>,
    pub modified_date: DateTime<Utc>,
    pub references: serde_json::Value,
    pub affected_packages: serde_json::Value,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// Vulnerability severity levels
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "vulnerability_severity", rename_all = "lowercase")]
pub enum VulnerabilitySeverity {
    Critical,
    High,
    Medium,
    Low,
    Info,
}

/// Package vulnerability association
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct PackageVulnerability {
    pub id: Uuid,
    pub sbom_record_id: Uuid,
    pub vulnerability_id: Uuid,
    pub affected_version_range: String,
    pub fixed_version: Option<String>,
    pub detection_method: String,
    pub confidence_score: f32,
    pub created_at: DateTime<Utc>,
}

/// Compliance report record
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ComplianceReport {
    pub id: Uuid,
    pub scan_result_id: Uuid,
    pub report_type: ComplianceReportType,
    pub organization: String,
    pub contact_email: String,
    pub report_data: serde_json::Value,
    pub file_path: Option<String>,
    pub generated_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
}

/// Compliance report types
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "compliance_report_type", rename_all = "lowercase")]
pub enum ComplianceReportType {
    CertIn,
    Sebi,
    CycloneDx,
    Spdx,
}

/// Blockchain commit record
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct BlockchainCommit {
    pub id: Uuid,
    pub scan_result_id: Uuid,
    pub transaction_hash: String,
    pub block_number: Option<i64>,
    pub merkle_root: String,
    pub merkle_proof: serde_json::Value,
    pub network: String,
    pub gas_used: Option<i64>,
    pub committed_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
}

/// Verifiable credential record
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct VerifiableCredential {
    pub id: Uuid,
    pub scan_result_id: Uuid,
    pub credential_type: String,
    pub issuer: String,
    pub subject: String,
    pub credential_data: serde_json::Value,
    pub signature: String,
    pub proof: serde_json::Value,
    pub issued_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
}

/// API key record for authentication
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ApiKey {
    pub id: Uuid,
    pub key_hash: String,
    pub name: String,
    pub description: Option<String>,
    pub permissions: serde_json::Value,
    pub is_active: bool,
    pub last_used_at: Option<DateTime<Utc>>,
    pub expires_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// User session record
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct UserSession {
    pub id: Uuid,
    pub session_token: String,
    pub user_id: String,
    pub ip_address: String,
    pub user_agent: Option<String>,
    pub is_active: bool,
    pub last_activity: DateTime<Utc>,
    pub expires_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
}

/// Audit log record
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct AuditLog {
    pub id: Uuid,
    pub user_id: Option<String>,
    pub api_key_id: Option<Uuid>,
    pub action: String,
    pub resource_type: String,
    pub resource_id: Option<String>,
    pub details: serde_json::Value,
    pub ip_address: String,
    pub user_agent: Option<String>,
    pub timestamp: DateTime<Utc>,
}

/// System configuration record
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct SystemConfig {
    pub id: Uuid,
    pub key: String,
    pub value: serde_json::Value,
    pub description: Option<String>,
    pub is_sensitive: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl ScanResult {
    pub fn new(scan_type: String, target_path: String, target_hash: String) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            scan_type,
            target_path,
            target_hash,
            status: ScanStatus::Pending,
            started_at: now,
            completed_at: None,
            error_message: None,
            result_data: serde_json::Value::Null,
            created_at: now,
            updated_at: now,
        }
    }

    pub fn mark_running(&mut self) {
        self.status = ScanStatus::Running;
        self.updated_at = Utc::now();
    }

    pub fn mark_completed(&mut self, result_data: serde_json::Value) {
        self.status = ScanStatus::Completed;
        self.completed_at = Some(Utc::now());
        self.result_data = result_data;
        self.updated_at = Utc::now();
    }

    pub fn mark_failed(&mut self, error_message: String) {
        self.status = ScanStatus::Failed;
        self.completed_at = Some(Utc::now());
        self.error_message = Some(error_message);
        self.updated_at = Utc::now();
    }
}

impl VulnerabilityRecord {
    pub fn severity_score(&self) -> u8 {
        match self.severity {
            VulnerabilitySeverity::Critical => 5,
            VulnerabilitySeverity::High => 4,
            VulnerabilitySeverity::Medium => 3,
            VulnerabilitySeverity::Low => 2,
            VulnerabilitySeverity::Info => 1,
        }
    }
}

impl ApiKey {
    pub fn new(name: String, key_hash: String) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            key_hash,
            name,
            description: None,
            permissions: serde_json::Value::Array(vec![]),
            is_active: true,
            last_used_at: None,
            expires_at: None,
            created_at: now,
            updated_at: now,
        }
    }

    pub fn update_last_used(&mut self) {
        self.last_used_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }
}
