//! Database schema definitions and SQL queries

/// SQL schema for creating all database tables
pub const CREATE_TABLES_SQL: &str = r#"
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>reate custom types
CREATE TYPE scan_status AS ENUM ('pending', 'running', 'completed', 'failed', 'cancelled');
CREATE TYPE vulnerability_severity AS ENUM ('critical', 'high', 'medium', 'low', 'info');
CREATE TYPE compliance_report_type AS ENUM ('cert_in', 'sebi', 'cyclone_dx', 'spdx');

-- Scan results table
CREATE TABLE IF NOT EXISTS scan_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    scan_type VARCHAR(50) NOT NULL,
    target_path TEXT NOT NULL,
    target_hash VARCHAR(128) NOT NULL,
    status scan_status NOT NULL DEFAULT 'pending',
    started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    error_message TEXT,
    result_data JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- SBOM records table
CREATE TABLE IF NOT EXISTS sbom_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    scan_result_id UUID NOT NULL REFERENCES scan_results(id) ON DELETE CASCADE,
    package_name VARCHAR(255) NOT NULL,
    package_version VARCHAR(100) NOT NULL,
    package_type VARCHAR(50) NOT NULL,
    package_manager VARCHAR(50) NOT NULL,
    file_path TEXT NOT NULL,
    license VARCHAR(255),
    description TEXT,
    homepage TEXT,
    repository TEXT,
    hash VARCHAR(128),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- HBOM records table
CREATE TABLE IF NOT EXISTS hbom_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    scan_result_id UUID NOT NULL REFERENCES scan_results(id) ON DELETE CASCADE,
    component_name VARCHAR(255) NOT NULL,
    component_version VARCHAR(100),
    component_type VARCHAR(50) NOT NULL,
    manufacturer VARCHAR(255),
    model VARCHAR(255),
    serial_number VARCHAR(255),
    firmware_version VARCHAR(100),
    description TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Vulnerabilities table
CREATE TABLE IF NOT EXISTS vulnerabilities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cve_id VARCHAR(20) UNIQUE NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    severity vulnerability_severity NOT NULL,
    cvss_score REAL,
    cvss_vector TEXT,
    published_date TIMESTAMPTZ NOT NULL,
    modified_date TIMESTAMPTZ NOT NULL,
    references JSONB,
    affected_packages JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Package vulnerabilities association table
CREATE TABLE IF NOT EXISTS package_vulnerabilities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sbom_record_id UUID NOT NULL REFERENCES sbom_records(id) ON DELETE CASCADE,
    vulnerability_id UUID NOT NULL REFERENCES vulnerabilities(id) ON DELETE CASCADE,
    affected_version_range VARCHAR(255) NOT NULL,
    fixed_version VARCHAR(100),
    detection_method VARCHAR(50) NOT NULL,
    confidence_score REAL NOT NULL DEFAULT 1.0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(sbom_record_id, vulnerability_id)
);

-- Compliance reports table
CREATE TABLE IF NOT EXISTS compliance_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    scan_result_id UUID NOT NULL REFERENCES scan_results(id) ON DELETE CASCADE,
    report_type compliance_report_type NOT NULL,
    organization VARCHAR(255) NOT NULL,
    contact_email VARCHAR(255) NOT NULL,
    report_data JSONB NOT NULL,
    file_path TEXT,
    generated_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Blockchain commits table
CREATE TABLE IF NOT EXISTS blockchain_commits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    scan_result_id UUID NOT NULL REFERENCES scan_results(id) ON DELETE CASCADE,
    transaction_hash VARCHAR(128) UNIQUE NOT NULL,
    block_number BIGINT,
    merkle_root VARCHAR(128) NOT NULL,
    merkle_proof JSONB NOT NULL,
    network VARCHAR(50) NOT NULL,
    gas_used BIGINT,
    committed_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Verifiable credentials table
CREATE TABLE IF NOT EXISTS verifiable_credentials (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    scan_result_id UUID NOT NULL REFERENCES scan_results(id) ON DELETE CASCADE,
    credential_type VARCHAR(100) NOT NULL,
    issuer VARCHAR(255) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    credential_data JSONB NOT NULL,
    signature TEXT NOT NULL,
    proof JSONB NOT NULL,
    issued_at TIMESTAMPTZ NOT NULL,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- API keys table
CREATE TABLE IF NOT EXISTS api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key_hash VARCHAR(128) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    permissions JSONB NOT NULL DEFAULT '[]',
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    last_used_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- User sessions table
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_token VARCHAR(128) UNIQUE NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    ip_address INET NOT NULL,
    user_agent TEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    last_activity TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Audit logs table
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255),
    api_key_id UUID REFERENCES api_keys(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100) NOT NULL,
    resource_id VARCHAR(255),
    details JSONB,
    ip_address INET NOT NULL,
    user_agent TEXT,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- System configuration table
CREATE TABLE IF NOT EXISTS system_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(255) UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    is_sensitive BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_scan_results_status ON scan_results(status);
CREATE INDEX IF NOT EXISTS idx_scan_results_created_at ON scan_results(created_at);
CREATE INDEX IF NOT EXISTS idx_scan_results_target_hash ON scan_results(target_hash);

CREATE INDEX IF NOT EXISTS idx_sbom_records_scan_result_id ON sbom_records(scan_result_id);
CREATE INDEX IF NOT EXISTS idx_sbom_records_package_name ON sbom_records(package_name);
CREATE INDEX IF NOT EXISTS idx_sbom_records_package_type ON sbom_records(package_type);

CREATE INDEX IF NOT EXISTS idx_hbom_records_scan_result_id ON hbom_records(scan_result_id);
CREATE INDEX IF NOT EXISTS idx_hbom_records_component_name ON hbom_records(component_name);

CREATE INDEX IF NOT EXISTS idx_vulnerabilities_cve_id ON vulnerabilities(cve_id);
CREATE INDEX IF NOT EXISTS idx_vulnerabilities_severity ON vulnerabilities(severity);
CREATE INDEX IF NOT EXISTS idx_vulnerabilities_published_date ON vulnerabilities(published_date);

CREATE INDEX IF NOT EXISTS idx_package_vulnerabilities_sbom_record_id ON package_vulnerabilities(sbom_record_id);
CREATE INDEX IF NOT EXISTS idx_package_vulnerabilities_vulnerability_id ON package_vulnerabilities(vulnerability_id);

CREATE INDEX IF NOT EXISTS idx_compliance_reports_scan_result_id ON compliance_reports(scan_result_id);
CREATE INDEX IF NOT EXISTS idx_compliance_reports_report_type ON compliance_reports(report_type);

CREATE INDEX IF NOT EXISTS idx_blockchain_commits_scan_result_id ON blockchain_commits(scan_result_id);
CREATE INDEX IF NOT EXISTS idx_blockchain_commits_transaction_hash ON blockchain_commits(transaction_hash);

CREATE INDEX IF NOT EXISTS idx_verifiable_credentials_scan_result_id ON verifiable_credentials(scan_result_id);
CREATE INDEX IF NOT EXISTS idx_verifiable_credentials_credential_type ON verifiable_credentials(credential_type);

CREATE INDEX IF NOT EXISTS idx_api_keys_key_hash ON api_keys(key_hash);
CREATE INDEX IF NOT EXISTS idx_api_keys_is_active ON api_keys(is_active);

CREATE INDEX IF NOT EXISTS idx_user_sessions_session_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_is_active ON user_sessions(is_active);

CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp);

CREATE INDEX IF NOT EXISTS idx_system_config_key ON system_config(key);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_scan_results_updated_at BEFORE UPDATE ON scan_results
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_vulnerabilities_updated_at BEFORE UPDATE ON vulnerabilities
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_api_keys_updated_at BEFORE UPDATE ON api_keys
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_config_updated_at BEFORE UPDATE ON system_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
"#;

/// SQL for dropping all tables (for testing/cleanup)
pub const DROP_TABLES_SQL: &str = r#"
DROP TABLE IF EXISTS audit_logs CASCADE;
DROP TABLE IF EXISTS user_sessions CASCADE;
DROP TABLE IF EXISTS api_keys CASCADE;
DROP TABLE IF EXISTS verifiable_credentials CASCADE;
DROP TABLE IF EXISTS blockchain_commits CASCADE;
DROP TABLE IF EXISTS compliance_reports CASCADE;
DROP TABLE IF EXISTS package_vulnerabilities CASCADE;
DROP TABLE IF EXISTS vulnerabilities CASCADE;
DROP TABLE IF EXISTS hbom_records CASCADE;
DROP TABLE IF EXISTS sbom_records CASCADE;
DROP TABLE IF EXISTS scan_results CASCADE;
DROP TABLE IF EXISTS system_config CASCADE;

DROP TYPE IF EXISTS compliance_report_type;
DROP TYPE IF EXISTS vulnerability_severity;
DROP TYPE IF EXISTS scan_status;

DROP FUNCTION IF EXISTS update_updated_at_column();
"#;

/// Create all database tables
pub async fn create_tables(pool: &sqlx::PgPool) -> crate::error::Result<()> {
    use tracing::info;

    info!("Creating database tables");

    sqlx::query(CREATE_TABLES_SQL)
        .execute(pool)
        .await
        .map_err(|e| crate::error::InfinitumError::Database {
            message: e.to_string(),
        })?;

    info!("Database tables created successfully");
    Ok(())
}
