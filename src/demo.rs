use crate::error::Result;
use std::path::Path;

/// Simple demo module to showcase Infinitium Signal capabilities
/// without requiring full database setup
pub struct InfinitiumDemo {
    pub name: String,
    pub version: String,
}

impl InfinitiumDemo {
    pub fn new() -> Self {
        Self {
            name: "Infinitium Signal".to_string(),
            version: env!("CARGO_PKG_VERSION").to_string(),
        }
    }

    /// Demonstrate SBOM generation using external tools
    pub async fn demo_sbom_generation(&self, path: &Path) -> Result<String> {
        println!("🔍 Generating SBOM for: {}", path.display());

        // Check if Syft is available
        let output = std::process::Command::new("syft").arg("--version").output();

        match output {
            Ok(output) if output.status.success() => {
                let version = String::from_utf8_lossy(&output.stdout);
                println!("✅ Syft available: {}", version.trim());

                // Generate SBOM
                let sbom_output = std::process::Command::new("syft")
                    .arg(path.to_string_lossy().as_ref())
                    .arg("-o")
                    .arg("cyclonedx-json")
                    .output();

                match sbom_output {
                    Ok(output) if output.status.success() => {
                        let sbom = String::from_utf8_lossy(&output.stdout);
                        println!("✅ SBOM generated successfully");
                        Ok(sbom.to_string())
                    }
                    Ok(output) => {
                        let error = String::from_utf8_lossy(&output.stderr);
                        println!("❌ SBOM generation failed: {}", error);
                        Err(crate::error::InfinitumError::Scan {
                            message: format!("SBOM generation failed: {}", error),
                        })
                    }
                    Err(e) => {
                        println!("❌ Failed to run Syft: {}", e);
                        Err(crate::error::InfinitumError::ExternalService {
                            service: "Syft".to_string(),
                            message: e.to_string(),
                        })
                    }
                }
            }
            _ => {
                println!("❌ Syft not available");
                Err(crate::error::InfinitumError::ExternalService {
                    service: "Syft".to_string(),
                    message: "Syft not found in PATH".to_string(),
                })
            }
        }
    }

    /// Demonstrate vulnerability scanning using external tools
    pub async fn demo_vulnerability_scan(&self, path: &Path) -> Result<String> {
        println!("🔍 Scanning for vulnerabilities: {}", path.display());

        // Check if Trivy is available
        let output = std::process::Command::new("trivy")
            .arg("--version")
            .output();

        match output {
            Ok(output) if output.status.success() => {
                let version = String::from_utf8_lossy(&output.stdout);
                println!("✅ Trivy available: {}", version.trim());

                // Run vulnerability scan
                let scan_output = std::process::Command::new("trivy")
                    .arg("fs")
                    .arg("--format")
                    .arg("json")
                    .arg(path.to_string_lossy().as_ref())
                    .output();

                match scan_output {
                    Ok(output) if output.status.success() => {
                        let scan_result = String::from_utf8_lossy(&output.stdout);
                        println!("✅ Vulnerability scan completed");
                        Ok(scan_result.to_string())
                    }
                    Ok(output) => {
                        let error = String::from_utf8_lossy(&output.stderr);
                        println!("❌ Vulnerability scan failed: {}", error);
                        Err(crate::error::InfinitumError::Scan {
                            message: format!("Vulnerability scan failed: {}", error),
                        })
                    }
                    Err(e) => {
                        println!("❌ Failed to run Trivy: {}", e);
                        Err(crate::error::InfinitumError::ExternalService {
                            service: "Trivy".to_string(),
                            message: e.to_string(),
                        })
                    }
                }
            }
            _ => {
                println!("❌ Trivy not available");
                Err(crate::error::InfinitumError::ExternalService {
                    service: "Trivy".to_string(),
                    message: "Trivy not found in PATH".to_string(),
                })
            }
        }
    }

    /// Demonstrate cryptographic capabilities
    pub fn demo_crypto_operations(&self) -> Result<()> {
        println!("🔐 Demonstrating cryptographic operations");

        // Generate random string
        match crate::utils::crypto_utils::generate_random_string(32) {
            Ok(random_str) => {
                println!("✅ Generated random string: {}...", &random_str[..16]);
            }
            Err(e) => {
                println!("❌ Failed to generate random string: {}", e);
                return Err(e);
            }
        }

        // Generate HMAC
        let key = b"demo-key";
        let message = b"demo-message";
        match crate::utils::crypto_utils::generate_hmac(key, message) {
            Ok(hmac) => {
                println!("✅ Generated HMAC: {}...", &hmac[..16]);
            }
            Err(e) => {
                println!("❌ Failed to generate HMAC: {}", e);
                return Err(e);
            }
        }

        println!("✅ Cryptographic operations completed");
        Ok(())
    }

    /// Demonstrate file operations
    pub fn demo_file_operations(&self) -> Result<()> {
        println!("📁 Demonstrating file operations");

        // Create temporary file
        match crate::utils::file_utils::create_temp_file() {
            Ok(temp_file) => {
                println!("✅ Created temporary file: {:?}", temp_file.path());
            }
            Err(e) => {
                println!("❌ Failed to create temporary file: {}", e);
                return Err(e);
            }
        }

        // Create temporary directory
        match crate::utils::file_utils::create_temp_dir() {
            Ok(temp_dir) => {
                println!("✅ Created temporary directory: {:?}", temp_dir.path());
            }
            Err(e) => {
                println!("❌ Failed to create temporary directory: {}", e);
                return Err(e);
            }
        }

        println!("✅ File operations completed");
        Ok(())
    }

    /// Run comprehensive demo
    pub async fn run_demo(&self, target_path: Option<&Path>) -> Result<()> {
        println!("🚀 Starting Infinitium Signal Demo");
        println!("📋 Platform: {} v{}", self.name, self.version);
        println!("{}", "=".repeat(50));

        // Demo 1: File operations
        self.demo_file_operations()?;
        println!();

        // Demo 2: Crypto operations
        self.demo_crypto_operations()?;
        println!();

        // Demo 3: External tool integration (if path provided)
        if let Some(path) = target_path {
            if path.exists() {
                // SBOM generation
                match self.demo_sbom_generation(path).await {
                    Ok(_) => println!("✅ SBOM generation demo completed"),
                    Err(e) => println!("⚠️  SBOM generation demo failed: {}", e),
                }
                println!();

                // Vulnerability scanning
                match self.demo_vulnerability_scan(path).await {
                    Ok(_) => println!("✅ Vulnerability scanning demo completed"),
                    Err(e) => println!("⚠️  Vulnerability scanning demo failed: {}", e),
                }
                println!();
            } else {
                println!("⚠️  Target path does not exist: {}", path.display());
            }
        } else {
            println!("ℹ️  No target path provided, skipping external tool demos");
            println!("   Use: cargo run --bin infinitum-signal demo /path/to/scan");
        }

        println!("{}", "=".repeat(50));
        println!("🎉 Demo completed successfully!");
        println!();
        println!("🔧 Available tools:");
        println!("   • Trivy: Vulnerability scanner");
        println!("   • Syft: SBOM generator");
        println!("   • Grype: Vulnerability scanner");
        println!();
        println!("📚 Next steps:");
        println!("   • Set up PostgreSQL database");
        println!("   • Configure Redis cache");
        println!("   • Run API server: cargo run --bin infinitum-signal server");
        println!("   • Generate compliance reports");

        Ok(())
    }
}

impl Default for InfinitiumDemo {
    fn default() -> Self {
        Self::new()
    }
}
