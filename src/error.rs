use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    J<PERSON>,
};
use serde::{Deserialize, Serialize};

use thiserror::Error;
use tracing::error;

/// Result type alias for the application
pub type Result<T> = std::result::Result<T, InfinitumError>;

/// Main error type for the Infinitium Signal application
#[derive(Error, Debug)]
pub enum InfinitumError {
    // Configuration errors
    #[error("Configuration error: {message}")]
    Config { message: String },

    #[error("IO error: {message}")]
    Io { message: String },

    // Database errors
    #[error("Database error: {message}")]
    Database { message: String },

    // Authentication/Authorization errors
    #[error("Authentication failed: {message}")]
    Authentication { message: String },

    #[error("Authorization failed: {message}")]
    Authorization { message: String },

    // Scanning errors
    #[error("Scan error: {message}")]
    Scan { message: String },

    #[error("Project not found at path: {path}")]
    ProjectNotFound { path: String },

    #[error("Unsupported project type: {project_type}")]
    UnsupportedProjectType { project_type: String },

    #[error("Scan timeout after {timeout}s")]
    ScanTimeout { timeout: u64 },

    // Vulnerability errors
    #[error("Vulnerability analysis error: {message}")]
    Vulnerability { message: String },

    #[error("CVE not found: {cve_id}")]
    CveNotFound { cve_id: String },

    // Compliance errors
    #[error("Compliance error: {message}")]
    Compliance { message: String },

    #[error("Unsupported compliance framework: {framework}")]
    UnsupportedFramework { framework: String },

    #[error("Template render error: {template} - {error}")]
    TemplateRender { template: String, error: String },

    #[error("Template not found: {template}")]
    TemplateNotFound { template: String },

    #[error("Template engine not found: {engine}")]
    TemplateEngineNotFound { engine: String },

    #[error("Invalid language: {language}")]
    InvalidLanguage { language: String },

    #[error("Report generation failed: {message}")]
    ReportGeneration { message: String },

    // Blockchain errors
    #[error("Blockchain error: {message}")]
    Blockchain { message: String },

    #[error("Transaction failed: {tx_id}")]
    TransactionFailed { tx_id: String },

    // API errors
    #[error("Invalid request: {message}")]
    InvalidRequest { message: String },

    #[error("Resource not found: {resource}")]
    NotFound { resource: String },

    #[error("Rate limit exceeded")]
    RateLimitExceeded,

    #[error("Request timeout")]
    RequestTimeout,

    // Metrics errors
    #[error("Metrics error: {message}")]
    Metrics { message: String },

    // Scheduler errors
    #[error("Scheduler error: {message}")]
    SchedulerError { message: String },

    // Queue errors
    #[error("Queue error: {message}")]
    QueueError { message: String },

    // License database update errors
    #[error("License database update error: {message}")]
    LicenseUpdate { message: String },

    #[error("License not found: {license_id} (source: {source})")]
    LicenseNotFound { license_id: String, source: String },

    #[error("License conflict: {license_id} - {conflict_reason}")]
    LicenseConflict { license_id: String, conflict_reason: String },

    #[error("API error: {service} - {status_code} - {message}")]
    ApiError { service: String, status_code: u16, message: String },

    #[error("Configuration error: {message}")]
    ConfigurationError { message: String },

    #[error("Resource limit exceeded: {resource} - limit: {limit}, current: {current}")]
    ResourceLimitExceeded { resource: String, limit: usize, current: usize },

    #[error("Operation not supported: {operation}")]
    OperationNotSupported { operation: String },

    #[error("Invalid operation: {operation} - {reason}")]
    InvalidOperation { operation: String, reason: String },

    // External service errors
    #[error("External service error: {service} - {message}")]
    ExternalService { service: String, message: String },

    #[error("NVD API error: {message}")]
    NvdApi { message: String },

    #[error("GitHub API error: {message}")]
    GitHubApi { message: String },

    #[error("Snyk API error: {message}")]
    SnykApi { message: String },

    // ScanCode errors
    #[error("ScanCode error: {message}")]
    ScanCode { message: String },

    #[error("ScanCode executable not found: {path}")]
    ScanCodeNotFound { path: String },

    #[error("ScanCode execution timeout after {timeout}s")]
    ScanCodeTimeout { timeout: u64 },

    #[error("ScanCode output parsing error: {message}")]
    ScanCodeParse { message: String },

    #[error("ScanCode license detection failed: {message}")]
    ScanCodeLicenseDetection { message: String },

    // File system errors
    #[error("File system error: {message}")]
    FileSystem { message: String },

    #[error("File not found: {path}")]
    FileNotFound { path: String },

    #[error("Permission denied: {path}")]
    PermissionDenied { path: String },

    // Serialization errors
    #[error("Serialization error: {message}")]
    Serialization { message: String },

    #[error("JSON error: {message}")]
    Json { message: String },

    #[error("YAML error: {message}")]
    Yaml { message: String },

    // Network errors
    #[error("Network error: {message}")]
    Network { message: String },

    #[error("HTTP error: {status} - {message}")]
    Http { status: u16, message: String },

    // Internal errors
    #[error("Internal server error: {message}")]
    Internal { message: String },

    #[error("Service unavailable: {service}")]
    ServiceUnavailable { service: String },

    // Validation errors
    #[error("Validation error: {field} - {message}")]
    Validation { field: String, message: String },

    // Parse errors
    #[error("Parse error: {0}")]
    ParseError(String),

    // Serialization errors (alternative format)
    #[error("Serialization error: {0}")]
    SerializationError(String),

    // File type errors
    #[error("Invalid file type: {path}")]
    InvalidFileType { path: std::path::PathBuf },

    // Generic errors
    #[error("Unknown error: {message}")]
    Unknown { message: String },
}

/// Error response for API endpoints
#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorResponse {
    pub error: String,
    pub message: String,
    pub code: String,
    pub details: Option<serde_json::Value>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub request_id: Option<String>,
}

impl ErrorResponse {
    pub fn new(error: &InfinitumError) -> Self {
        Self {
            error: error.error_type(),
            message: error.to_string(),
            code: error.error_code(),
            details: error.error_details(),
            timestamp: chrono::Utc::now(),
            request_id: None,
        }
    }

    pub fn with_request_id(mut self, request_id: String) -> Self {
        self.request_id = Some(request_id);
        self
    }
}

impl InfinitumError {
    /// Get the error type as a string
    pub fn error_type(&self) -> String {
        match self {
            Self::Config { .. } => "CONFIG_ERROR".to_string(),
            Self::Database { .. } => "DATABASE_ERROR".to_string(),
            Self::TemplateRender { .. } | Self::TemplateNotFound { .. } | Self::TemplateEngineNotFound { .. } => "TEMPLATE_ERROR".to_string(),
            Self::InvalidLanguage { .. } => "I18N_ERROR".to_string(),
            Self::Authentication { .. } => "AUTHENTICATION_ERROR".to_string(),
            Self::Authorization { .. } => "AUTHORIZATION_ERROR".to_string(),
            Self::Scan { .. }
            | Self::ProjectNotFound { .. }
            | Self::UnsupportedProjectType { .. }
            | Self::ScanTimeout { .. } => "SCAN_ERROR".to_string(),
            Self::Vulnerability { .. } | Self::CveNotFound { .. } => {
                "VULNERABILITY_ERROR".to_string()
            }
            Self::Compliance { .. }
            | Self::UnsupportedFramework { .. }
            | Self::ReportGeneration { .. } => "COMPLIANCE_ERROR".to_string(),
            Self::Blockchain { .. } | Self::TransactionFailed { .. } => {
                "BLOCKCHAIN_ERROR".to_string()
            }
            Self::InvalidRequest { .. }
            | Self::NotFound { .. }
            | Self::RateLimitExceeded
            | Self::RequestTimeout => "API_ERROR".to_string(),
            Self::ExternalService { .. }
            | Self::NvdApi { .. }
            | Self::GitHubApi { .. }
            | Self::SnykApi { .. }
            | Self::ScanCode { .. }
            | Self::ScanCodeNotFound { .. }
            | Self::ScanCodeTimeout { .. }
            | Self::ScanCodeParse { .. }
            | Self::ScanCodeLicenseDetection { .. } => "EXTERNAL_SERVICE_ERROR".to_string(),
            Self::FileSystem { .. } | Self::FileNotFound { .. } | Self::PermissionDenied { .. } => {
                "FILE_SYSTEM_ERROR".to_string()
            }
            Self::Serialization { .. } | Self::Json { .. } | Self::Yaml { .. } => {
                "SERIALIZATION_ERROR".to_string()
            }
            Self::Network { .. } | Self::Http { .. } => "NETWORK_ERROR".to_string(),
            Self::Internal { .. } | Self::ServiceUnavailable { .. } => "INTERNAL_ERROR".to_string(),
            Self::Validation { .. } => "VALIDATION_ERROR".to_string(),
            Self::Unknown { .. } => "UNKNOWN_ERROR".to_string(),
            Self::ParseError(_) => "PARSE_ERROR".to_string(),
            Self::SerializationError(_) => "SERIALIZATION_ERROR".to_string(),
            Self::InvalidFileType { .. } => "INVALID_FILE_TYPE".to_string(),
            Self::Metrics { .. } => "METRICS_ERROR".to_string(),
            Self::SchedulerError { .. } => "SCHEDULER_ERROR".to_string(),
            Self::QueueError { .. } => "QUEUE_ERROR".to_string(),
            Self::LicenseUpdate { .. } => "LICENSE_UPDATE_ERROR".to_string(),
            Self::LicenseNotFound { .. } => "LICENSE_NOT_FOUND_ERROR".to_string(),
            Self::LicenseConflict { .. } => "LICENSE_CONFLICT_ERROR".to_string(),
            Self::ApiError { .. } => "API_ERROR".to_string(),
            Self::ConfigurationError { .. } => "CONFIGURATION_ERROR".to_string(),
            Self::ResourceLimitExceeded { .. } => "RESOURCE_LIMIT_ERROR".to_string(),
            Self::OperationNotSupported { .. } => "OPERATION_NOT_SUPPORTED_ERROR".to_string(),
            Self::InvalidOperation { .. } => "INVALID_OPERATION_ERROR".to_string(),
            Self::Io { .. } => "IO_ERROR".to_string(),
        }
    }

    /// Get the error code
    pub fn error_code(&self) -> String {
        match self {
            Self::TemplateRender { .. } => "E101".to_string(),
            Self::TemplateNotFound { .. } => "E102".to_string(),
            Self::TemplateEngineNotFound { .. } => "E103".to_string(),
            Self::InvalidLanguage { .. } => "E104".to_string(),
            Self::Config { .. } => "E001".to_string(),
            Self::Database { .. } => "E002".to_string(),
            Self::Authentication { .. } => "E003".to_string(),
            Self::Authorization { .. } => "E004".to_string(),
            Self::Scan { .. } => "E005".to_string(),
            Self::ProjectNotFound { .. } => "E006".to_string(),
            Self::UnsupportedProjectType { .. } => "E007".to_string(),
            Self::ScanTimeout { .. } => "E008".to_string(),
            Self::Vulnerability { .. } => "E009".to_string(),
            Self::CveNotFound { .. } => "E010".to_string(),
            Self::Compliance { .. } => "E011".to_string(),
            Self::UnsupportedFramework { .. } => "E012".to_string(),
            Self::ReportGeneration { .. } => "E013".to_string(),
            Self::Blockchain { .. } => "E014".to_string(),
            Self::TransactionFailed { .. } => "E015".to_string(),
            Self::InvalidRequest { .. } => "E016".to_string(),
            Self::NotFound { .. } => "E017".to_string(),
            Self::RateLimitExceeded => "E018".to_string(),
            Self::RequestTimeout => "E019".to_string(),
            Self::ExternalService { .. } => "E020".to_string(),
            Self::NvdApi { .. } => "E021".to_string(),
            Self::GitHubApi { .. } => "E022".to_string(),
            Self::SnykApi { .. } => "E023".to_string(),
            Self::ScanCode { .. } => "E024".to_string(),
            Self::ScanCodeNotFound { .. } => "E025".to_string(),
            Self::ScanCodeTimeout { .. } => "E026".to_string(),
            Self::ScanCodeParse { .. } => "E027".to_string(),
            Self::ScanCodeLicenseDetection { .. } => "E028".to_string(),
            Self::FileSystem { .. } => "E024".to_string(),
            Self::FileNotFound { .. } => "E025".to_string(),
            Self::PermissionDenied { .. } => "E026".to_string(),
            Self::Serialization { .. } => "E027".to_string(),
            Self::Json { .. } => "E028".to_string(),
            Self::Yaml { .. } => "E029".to_string(),
            Self::Network { .. } => "E030".to_string(),
            Self::Http { .. } => "E031".to_string(),
            Self::Internal { .. } => "E032".to_string(),
            Self::ServiceUnavailable { .. } => "E033".to_string(),
            Self::Validation { .. } => "E034".to_string(),
            Self::Unknown { .. } => "E999".to_string(),
            Self::ParseError(_) => "E998".to_string(),
            Self::SerializationError(_) => "E997".to_string(),
            Self::InvalidFileType { .. } => "E996".to_string(),
            Self::Metrics { .. } => "E997".to_string(),
            Self::SchedulerError { .. } => "E998".to_string(),
            Self::QueueError { .. } => "E999".to_string(),
            Self::LicenseUpdate { .. } => "E1000".to_string(),
            Self::LicenseNotFound { .. } => "E1001".to_string(),
            Self::LicenseConflict { .. } => "E1002".to_string(),
            Self::ApiError { .. } => "E1003".to_string(),
            Self::ConfigurationError { .. } => "E1004".to_string(),
            Self::ResourceLimitExceeded { .. } => "E1005".to_string(),
            Self::OperationNotSupported { .. } => "E1006".to_string(),
            Self::InvalidOperation { .. } => "E1007".to_string(),
            Self::Io { .. } => "E1008".to_string(),
        }
    }

    /// Get additional error details
    pub fn error_details(&self) -> Option<serde_json::Value> {
        match self {
            Self::ProjectNotFound { path } => Some(serde_json::json!({ "path": path })),
            Self::UnsupportedProjectType { project_type } => {
                Some(serde_json::json!({ "project_type": project_type }))
            }
            Self::TemplateRender { template, .. } => Some(serde_json::json!({ "template": template })),
            Self::TemplateNotFound { template } => Some(serde_json::json!({ "template": template })),
            Self::TemplateEngineNotFound { engine } => Some(serde_json::json!({ "engine": engine })),
            Self::InvalidLanguage { language } => Some(serde_json::json!({ "language": language })),
            Self::ScanTimeout { timeout } => Some(serde_json::json!({ "timeout": timeout })),
            Self::CveNotFound { cve_id } => Some(serde_json::json!({ "cve_id": cve_id })),
            Self::UnsupportedFramework { framework } => {
                Some(serde_json::json!({ "framework": framework }))
            }
            Self::TransactionFailed { tx_id } => Some(serde_json::json!({ "tx_id": tx_id })),
            Self::NotFound { resource } => Some(serde_json::json!({ "resource": resource })),
            Self::ExternalService { service, .. } => {
                Some(serde_json::json!({ "service": service }))
            }
            Self::FileNotFound { path } => Some(serde_json::json!({ "path": path })),
            Self::PermissionDenied { path } => Some(serde_json::json!({ "path": path })),
            Self::Http { status, .. } => Some(serde_json::json!({ "status": status })),
            Self::ServiceUnavailable { service } => Some(serde_json::json!({ "service": service })),
            Self::Validation { field, .. } => Some(serde_json::json!({ "field": field })),
            Self::ScanCodeNotFound { path } => Some(serde_json::json!({ "path": path })),
            Self::ScanCodeTimeout { timeout } => Some(serde_json::json!({ "timeout": timeout })),
            Self::LicenseNotFound { license_id, source } => Some(serde_json::json!({ "license_id": license_id, "source": source })),
            Self::LicenseConflict { license_id, conflict_reason } => Some(serde_json::json!({ "license_id": license_id, "conflict_reason": conflict_reason })),
            Self::ApiError { service, status_code, .. } => Some(serde_json::json!({ "service": service, "status_code": status_code })),
            Self::ResourceLimitExceeded { resource, limit, current } => Some(serde_json::json!({ "resource": resource, "limit": limit, "current": current })),
            Self::InvalidOperation { operation, reason } => Some(serde_json::json!({ "operation": operation, "reason": reason })),
            _ => None,
        }
    }

    /// Get HTTP status code for the error
    pub fn status_code(&self) -> StatusCode {
        match self {
            Self::Authentication { .. } => StatusCode::UNAUTHORIZED,
            Self::Authorization { .. } => StatusCode::FORBIDDEN,
            Self::NotFound { .. }
            | Self::ProjectNotFound { .. }
            | Self::FileNotFound { .. }
            | Self::CveNotFound { .. } => StatusCode::NOT_FOUND,
            Self::InvalidRequest { .. }
            | Self::Validation { .. }
            | Self::UnsupportedProjectType { .. }
            | Self::UnsupportedFramework { .. } => StatusCode::BAD_REQUEST,
            Self::RateLimitExceeded => StatusCode::TOO_MANY_REQUESTS,
            Self::RequestTimeout | Self::ScanTimeout { .. } | Self::ScanCodeTimeout { .. } => StatusCode::REQUEST_TIMEOUT,
            Self::ServiceUnavailable { .. } => StatusCode::SERVICE_UNAVAILABLE,
            Self::PermissionDenied { .. } => StatusCode::FORBIDDEN,
            _ => StatusCode::INTERNAL_SERVER_ERROR,
        }
    }
}

// Implement IntoResponse for Axum
impl IntoResponse for InfinitumError {
    fn into_response(self) -> Response {
        let status = self.status_code();
        let error_response = ErrorResponse::new(&self);

        // Log the error
        error!(
            error = %self,
            error_type = %self.error_type(),
            error_code = %self.error_code(),
            status = %status,
            "Request failed"
        );

        (status, Json(error_response)).into_response()
    }
}

// Implement From traits for common error types
impl From<sqlx::Error> for InfinitumError {
    fn from(err: sqlx::Error) -> Self {
        Self::Database {
            message: err.to_string(),
        }
    }
}

impl From<redis::RedisError> for InfinitumError {
    fn from(err: redis::RedisError) -> Self {
        Self::Database {
            message: format!("Redis error: {}", err),
        }
    }
}

impl From<reqwest::Error> for InfinitumError {
    fn from(err: reqwest::Error) -> Self {
        if err.is_timeout() {
            Self::RequestTimeout
        } else if let Some(status) = err.status() {
            Self::Http {
                status: status.as_u16(),
                message: err.to_string(),
            }
        } else {
            Self::Network {
                message: err.to_string(),
            }
        }
    }
}

impl From<serde_json::Error> for InfinitumError {
    fn from(err: serde_json::Error) -> Self {
        Self::Json {
            message: err.to_string(),
        }
    }
}

impl From<serde_yaml::Error> for InfinitumError {
    fn from(err: serde_yaml::Error) -> Self {
        Self::Yaml {
            message: err.to_string(),
        }
    }
}

impl From<std::io::Error> for InfinitumError {
    fn from(err: std::io::Error) -> Self {
        match err.kind() {
            std::io::ErrorKind::NotFound => Self::FileNotFound {
                path: "unknown".to_string(),
            },
            std::io::ErrorKind::PermissionDenied => Self::PermissionDenied {
                path: "unknown".to_string(),
            },
            _ => Self::FileSystem {
                message: err.to_string(),
            },
        }
    }
}

impl From<anyhow::Error> for InfinitumError {
    fn from(err: anyhow::Error) -> Self {
        Self::Internal {
            message: err.to_string(),
        }
    }
}

impl From<base64::DecodeError> for InfinitumError {
    fn from(err: base64::DecodeError) -> Self {
        Self::ParseError(format!("Base64 decode error: {}", err))
    }
}

impl From<ring::error::Unspecified> for InfinitumError {
    fn from(err: ring::error::Unspecified) -> Self {
        Self::Internal {
            message: format!("Cryptographic error: {:?}", err),
        }
    }
}

impl From<ring::error::KeyRejected> for InfinitumError {
    fn from(err: ring::error::KeyRejected) -> Self {
        Self::Internal {
            message: format!("Key rejected: {:?}", err),
        }
    }
}

impl From<toml::de::Error> for InfinitumError {
    fn from(err: toml::de::Error) -> Self {
        Self::ParseError(format!("TOML parse error: {}", err))
    }
}

impl From<tera::Error> for InfinitumError {
    fn from(err: tera::Error) -> Self {
        Self::TemplateRender {
            template: "unknown".to_string(),
            error: err.to_string(),
        }
    }
}

impl From<std::net::AddrParseError> for InfinitumError {
    fn from(err: std::net::AddrParseError) -> Self {
        Self::ParseError(format!("Address parse error: {}", err))
    }
}

impl From<aes_gcm::Error> for InfinitumError {
    fn from(err: aes_gcm::Error) -> Self {
        Self::Internal {
            message: format!("AES-GCM cryptographic error: {:?}", err),
        }
    }
}

impl From<sha2::digest::InvalidLength> for InfinitumError {
    fn from(err: sha2::digest::InvalidLength) -> Self {
        Self::Internal {
            message: format!("Invalid key length: {}", err),
        }
    }
}
