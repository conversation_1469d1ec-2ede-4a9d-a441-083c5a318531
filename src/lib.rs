//! # Infinitium Signal
//!
//! Enterprise Cyber-Compliance Platform with SBOM/HBOM scanning, vulnerability analysis,
//! and blockchain audit trails.
//!
//! ## Features
//!
//! - **SBOM/HBOM Generation**: Software and Hardware Bill of Materials scanning
//! - **Vulnerability Assessment**: Real-time CVE analysis and risk scoring
//! - **Compliance Frameworks**: CERT-In, SEBI, and international standards support
//! - **Blockchain Audit Trails**: Immutable compliance records with verifiable credentials
//! - **Multi-format Exports**: CycloneDX, SPDX, PDF reports
//!
//! ## Quick Start
//!
//! ```rust,no_run
//! use infinitium_signal::{
//!     config::{Config, ScanningConfig},
//!     scanners::SbomScanner,
//! };
//! use std::path::Path;
//!
//! #[tokio::main]
//! async fn main() -> anyhow::Result<()> {
//!     let config = Config::load("config.yaml").await?;
//!     let scanner = SbomScanner::new(&config.scanning);
//!
//!     let result = scanner.scan_project(Path::new("/path/to/project")).await?;
//!     println!("Found {} components", result.len());
//!
//!     Ok(())
//! }
//! ```

pub mod api;
pub mod blockchain;
pub mod compliance;
pub mod config;
pub mod database;
pub mod demo;
pub mod error;
pub mod logging;
pub mod metrics;
pub mod observability;
pub mod orchestration;
pub mod scanners;
pub mod utils;
pub mod vulnerability;

// Re-export commonly used types
pub use crate::{
    config::Config,
    error::{InfinitumError, Result},
};

/// Version information
pub const VERSION: &str = env!("CARGO_PKG_VERSION");
pub const NAME: &str = env!("CARGO_PKG_NAME");
pub const DESCRIPTION: &str = env!("CARGO_PKG_DESCRIPTION");

/// Build information
pub mod build_info {
    /// Git commit hash
    pub const GIT_HASH: &str = env!("VERGEN_GIT_SHA");

    /// Build timestamp
    pub const BUILD_TIMESTAMP: &str = env!("VERGEN_BUILD_TIMESTAMP");

    /// Rust version used for build
    pub const RUSTC_VERSION: &str = env!("VERGEN_RUSTC_SEMVER");

    /// Target triple
    pub const TARGET_TRIPLE: &str = env!("VERGEN_CARGO_TARGET_TRIPLE");
}

/// Application metadata
#[derive(Debug, Clone)]
pub struct AppInfo {
    pub name: &'static str,
    pub version: &'static str,
    pub description: &'static str,
    pub git_hash: &'static str,
    pub build_timestamp: &'static str,
    pub rustc_version: &'static str,
    pub target_triple: &'static str,
}

impl Default for AppInfo {
    fn default() -> Self {
        Self {
            name: NAME,
            version: VERSION,
            description: DESCRIPTION,
            git_hash: build_info::GIT_HASH,
            build_timestamp: build_info::BUILD_TIMESTAMP,
            rustc_version: build_info::RUSTC_VERSION,
            target_triple: build_info::TARGET_TRIPLE,
        }
    }
}

impl AppInfo {
    /// Get application information
    pub fn new() -> Self {
        Self::default()
    }

    /// Get short git hash (first 8 characters or full if shorter)
    fn short_git_hash(&self) -> &str {
        if self.git_hash.len() >= 8 {
            &self.git_hash[..8]
        } else {
            self.git_hash
        }
    }

    /// Get version string with build info
    pub fn version_string(&self) -> String {
        format!(
            "{} ({} built with rustc {})",
            self.version,
            self.short_git_hash(),
            self.rustc_version
        )
    }

    /// Get full build information
    pub fn build_info(&self) -> String {
        format!(
            "{} {} ({})\nBuilt: {} with rustc {}\nTarget: {}",
            self.name,
            self.version,
            self.short_git_hash(),
            self.build_timestamp,
            self.rustc_version,
            self.target_triple
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_app_info() {
        let info = AppInfo::new();
        assert_eq!(info.name, "infinitium-signal");
        assert!(!info.version.is_empty());
        assert!(!info.git_hash.is_empty());
    }

    #[test]
    fn test_version_string() {
        let info = AppInfo::new();
        let version_string = info.version_string();
        assert!(version_string.contains(info.version));
        assert!(version_string.contains("rustc"));
    }

    #[test]
    fn test_build_info() {
        let info = AppInfo::new();
        let build_info = info.build_info();
        assert!(build_info.contains(info.name));
        assert!(build_info.contains(info.version));
        assert!(build_info.contains("Built:"));
        assert!(build_info.contains("Target:"));
    }
}
