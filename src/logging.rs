use crate::{config::LoggingConfig, error::Result};
use anyhow::Context;
use std::str::FromStr;
use tracing::{info, Level};
use tracing_appender::{non_blocking, rolling};
use tracing_subscriber::{
    fmt::{self, format::FmtSpan},
    layer::SubscriberExt,
    util::SubscriberInitExt,
    EnvFilter, Layer,
};

/// Setup logging based on configuration
pub fn setup_logging(config: &LoggingConfig) -> Result<()> {
    let level = Level::from_str(&config.level)
        .with_context(|| format!("Invalid log level: {}", config.level))?;

    // Create environment filter
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new(format!("{}={}", env!("CARGO_PKG_NAME"), level)));

    // Create base subscriber
    let subscriber = tracing_subscriber::registry().with(env_filter);

    // Setup simplified console logging
    let fmt_layer = tracing_subscriber::fmt::layer()
        .with_target(false)
        .with_thread_ids(true)
        .with_line_number(true);

    subscriber.with(fmt_layer).init();

    info!(
        level = %level,
        format = %config.format,
        file = ?config.file,
        "Logging initialized"
    );

    Ok(())
}

/// Create console logging layer
#[allow(dead_code)]
fn create_console_layer(
    config: &LoggingConfig,
) -> Result<Box<dyn Layer<tracing_subscriber::Registry> + Send + Sync>> {
    let layer = match config.format.as_str() {
        "json" => fmt::layer()
            .json()
            .with_current_span(true)
            .with_span_events(FmtSpan::CLOSE)
            .with_target(true)
            .with_thread_ids(true)
            .with_thread_names(true)
            .boxed(),
        "pretty" => fmt::layer()
            .pretty()
            .with_span_events(FmtSpan::CLOSE)
            .with_target(true)
            .with_thread_ids(true)
            .with_thread_names(true)
            .boxed(),
        "compact" => fmt::layer()
            .compact()
            .with_span_events(FmtSpan::CLOSE)
            .with_target(true)
            .boxed(),
        _ => fmt::layer()
            .with_span_events(FmtSpan::CLOSE)
            .with_target(true)
            .boxed(),
    };

    Ok(layer)
}

/// Create file logging layer
#[allow(dead_code)]
fn create_file_layer(
    config: &LoggingConfig,
    file_path: &str,
) -> Result<Box<dyn Layer<tracing_subscriber::Registry> + Send + Sync>> {
    // Parse file path to get directory and filename
    let path = std::path::Path::new(file_path);
    let directory = path.parent().unwrap_or_else(|| std::path::Path::new("."));
    let filename = path
        .file_name()
        .and_then(|name| name.to_str())
        .unwrap_or("infinitum-signal.log");

    // Create rolling file appender
    let file_appender = rolling::daily(directory, filename);
    let (non_blocking, _guard) = non_blocking(file_appender);

    // Create file layer based on format
    let layer = match config.format.as_str() {
        "json" => fmt::layer()
            .json()
            .with_writer(non_blocking)
            .with_current_span(true)
            .with_span_events(FmtSpan::CLOSE)
            .with_target(true)
            .with_thread_ids(true)
            .with_thread_names(true)
            .with_ansi(false)
            .boxed(),
        _ => fmt::layer()
            .with_writer(non_blocking)
            .with_span_events(FmtSpan::CLOSE)
            .with_target(true)
            .with_ansi(false)
            .boxed(),
    };

    Ok(layer)
}

/// Logging middleware for HTTP requests
pub mod middleware {
    use axum::{extract::MatchedPath, http::Request, middleware::Next, response::IntoResponse};
    use std::time::Instant;
    use tracing::{info_span, Instrument};
    use uuid::Uuid;

    /// HTTP request logging middleware
    pub async fn logging_middleware(
        request: Request<axum::body::Body>,
        next: Next,
    ) -> impl IntoResponse {
        let start = Instant::now();
        let request_id = Uuid::new_v4().to_string();

        let method = request.method().clone();
        let uri = request.uri().clone();
        let version = request.version();
        let headers = request.headers().clone();

        // Get matched path for better grouping
        let path = request
            .extensions()
            .get::<MatchedPath>()
            .map(|matched_path| matched_path.as_str())
            .unwrap_or_else(|| uri.path());

        // Create span for the request
        let span = info_span!(
            "http_request",
            method = %method,
            path = %path,
            version = ?version,
            request_id = %request_id,
        );

        async move {
            let response = next.run(request).await;
            let duration = start.elapsed();
            let status = response.status();

            tracing::info!(
                status = %status,
                duration_ms = %duration.as_millis(),
                user_agent = ?headers.get("user-agent"),
                "HTTP request completed"
            );

            response
        }
        .instrument(span)
        .await
    }
}

/// Structured logging macros
#[macro_export]
macro_rules! log_scan_start {
    ($scan_id:expr, $project_path:expr, $scan_type:expr) => {
        tracing::info!(
            scan_id = %$scan_id,
            project_path = %$project_path,
            scan_type = %$scan_type,
            "Scan started"
        );
    };
}

#[macro_export]
macro_rules! log_scan_complete {
    ($scan_id:expr, $duration:expr, $dependencies:expr) => {
        tracing::info!(
            scan_id = %$scan_id,
            duration_ms = %$duration.as_millis(),
            dependencies_found = %$dependencies,
            "Scan completed successfully"
        );
    };
}

#[macro_export]
macro_rules! log_scan_error {
    ($scan_id:expr, $error:expr) => {
        tracing::error!(
            scan_id = %$scan_id,
            error = %$error,
            "Scan failed"
        );
    };
}

#[macro_export]
macro_rules! log_vulnerability_found {
    ($cve_id:expr, $severity:expr, $package:expr) => {
        tracing::warn!(
            cve_id = %$cve_id,
            severity = %$severity,
            package = %$package,
            "Vulnerability found"
        );
    };
}

#[macro_export]
macro_rules! log_compliance_report {
    ($framework:expr, $status:expr, $issues:expr) => {
        tracing::info!(
            framework = %$framework,
            status = %$status,
            issues_found = %$issues,
            "Compliance report generated"
        );
    };
}

#[macro_export]
macro_rules! log_blockchain_transaction {
    ($tx_id:expr, $operation:expr) => {
        tracing::info!(
            tx_id = %$tx_id,
            operation = %$operation,
            "Blockchain transaction recorded"
        );
    };
}

/// Performance monitoring utilities
pub mod performance {
    use std::time::Instant;
    use tracing::{info, warn};

    /// Timer for measuring operation duration
    pub struct Timer {
        start: Instant,
        operation: String,
        warn_threshold_ms: Option<u64>,
    }

    impl Timer {
        pub fn new(operation: impl Into<String>) -> Self {
            Self {
                start: Instant::now(),
                operation: operation.into(),
                warn_threshold_ms: None,
            }
        }

        pub fn with_warn_threshold(mut self, threshold_ms: u64) -> Self {
            self.warn_threshold_ms = Some(threshold_ms);
            self
        }

        pub fn elapsed(&self) -> std::time::Duration {
            self.start.elapsed()
        }
    }

    impl Drop for Timer {
        fn drop(&mut self) {
            let duration = self.elapsed();
            let duration_ms = duration.as_millis();

            if let Some(threshold) = self.warn_threshold_ms {
                if duration_ms > threshold as u128 {
                    warn!(
                        operation = %self.operation,
                        duration_ms = %duration_ms,
                        threshold_ms = %threshold,
                        "Operation exceeded performance threshold"
                    );
                    return;
                }
            }

            info!(
                operation = %self.operation,
                duration_ms = %duration_ms,
                "Operation completed"
            );
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_logging_config_validation() {
        let config = LoggingConfig {
            level: "info".to_string(),
            format: "json".to_string(),
            file: None,
            max_size: "100MB".to_string(),
            max_files: 10,
            compress: true,
        };

        // Test that we can parse the log level
        assert!(Level::from_str(&config.level).is_ok());
    }

    #[test]
    fn test_invalid_log_level() {
        let config = LoggingConfig {
            level: "invalid".to_string(),
            format: "json".to_string(),
            file: None,
            max_size: "100MB".to_string(),
            max_files: 10,
            compress: true,
        };

        assert!(Level::from_str(&config.level).is_err());
    }
    #[test]
    fn test_invalid_log_level() {
        let config = LoggingConfig {
            level: "invalid".to_string(),
            format: "json".to_string(),
            file: None,
            max_size: "100MB".to_string(),
            max_files: 10,
            compress: true,
        };

        assert!(Level::from_str(&config.level).is_err());
    }
}

/// Audit logging for compliance and security events
pub mod audit {
    use chrono::{DateTime, Utc};
    use serde::{Deserialize, Serialize};
    use std::collections::HashMap;
    use tracing::{error, info, warn};
    use uuid::Uuid;

    /// Audit event types
    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub enum AuditEventType {
        /// Vulnerability scan started
        ScanStarted,
        /// Vulnerability scan completed
        ScanCompleted,
        /// Vulnerability scan failed
        ScanFailed,
        /// Vulnerability detected
        VulnerabilityDetected,
        /// False positive filtered
        FalsePositiveFiltered,
        /// Compliance check performed
        ComplianceCheck,
        /// Configuration changed
        ConfigurationChanged,
        /// Access attempt
        AccessAttempt,
        /// Report generated
        ReportGenerated,
        /// API call made
        ApiCall,
        /// Cache operation
        CacheOperation,
    }

    /// Audit event severity
    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub enum AuditSeverity {
        /// Informational event
        Info,
        /// Warning event
        Warning,
        /// Error event
        Error,
        /// Critical security event
        Critical,
    }

    /// Audit event
    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct AuditEvent {
        /// Unique event ID
        pub id: Uuid,
        /// Event timestamp
        pub timestamp: DateTime<Utc>,
        /// Event type
        pub event_type: AuditEventType,
        /// Event severity
        pub severity: AuditSeverity,
        /// User or system that triggered the event
        pub actor: String,
        /// Resource affected
        pub resource: String,
        /// Action performed
        pub action: String,
        /// Event details
        pub details: HashMap<String, String>,
        /// IP address (if applicable)
        pub ip_address: Option<String>,
        /// User agent (if applicable)
        pub user_agent: Option<String>,
        /// Session ID (if applicable)
        pub session_id: Option<String>,
        /// Compliance framework (if applicable)
        pub compliance_framework: Option<String>,
        /// Result of the operation
        pub result: String,
    }

    /// Audit logger
    pub struct AuditLogger {
        /// Whether audit logging is enabled
        enabled: bool,
        /// Log file path for audit logs
        log_file: Option<String>,
        /// Whether to include sensitive data
        include_sensitive: bool,
    }

    impl AuditLogger {
        /// Create new audit logger
        pub fn new(enabled: bool, log_file: Option<String>, include_sensitive: bool) -> Self {
            Self {
                enabled,
                log_file,
                include_sensitive,
            }
        }

        /// Log an audit event
        pub fn log_event(&self, event: AuditEvent) {
            if !self.enabled {
                return;
            }

            let log_message = format!(
                "AUDIT: {} | {} | {} | {} | {} | {} | {} | {:?}",
                event.timestamp.format("%Y-%m-%d %H:%M:%S UTC"),
                event.event_type.as_ref(),
                event.severity.as_ref(),
                event.actor,
                event.resource,
                event.action,
                event.result,
                event.details
            );

            match event.severity {
                AuditSeverity::Critical => error!("{}", log_message),
                AuditSeverity::Error => error!("{}", log_message),
                AuditSeverity::Warning => warn!("{}", log_message),
                AuditSeverity::Info => info!("{}", log_message),
            }

            // TODO: Write to dedicated audit log file if configured
            if let Some(log_file) = &self.log_file {
                // In a real implementation, you'd write to the audit log file
                info!("Audit event would be written to: {}", log_file);
            }
        }

        /// Log OSV scan started
        pub fn log_osv_scan_started(&self, scan_id: &str, component_count: usize, actor: &str) {
            let event = AuditEvent {
                id: Uuid::new_v4(),
                timestamp: Utc::now(),
                event_type: AuditEventType::ScanStarted,
                severity: AuditSeverity::Info,
                actor: actor.to_string(),
                resource: format!("osv_scan:{}", scan_id),
                action: "scan_started".to_string(),
                details: HashMap::from([
                    ("scan_id".to_string(), scan_id.to_string()),
                    ("component_count".to_string(), component_count.to_string()),
                    ("scanner".to_string(), "OSV".to_string()),
                ]),
                ip_address: None,
                user_agent: None,
                session_id: None,
                compliance_framework: Some("OSV".to_string()),
                result: "started".to_string(),
            };
            self.log_event(event);
        }

        /// Log OSV scan completed
        pub fn log_osv_scan_completed(&self, scan_id: &str, vulnerabilities_found: usize, duration_ms: u64, actor: &str) {
            let event = AuditEvent {
                id: Uuid::new_v4(),
                timestamp: Utc::now(),
                event_type: AuditEventType::ScanCompleted,
                severity: AuditSeverity::Info,
                actor: actor.to_string(),
                resource: format!("osv_scan:{}", scan_id),
                action: "scan_completed".to_string(),
                details: HashMap::from([
                    ("scan_id".to_string(), scan_id.to_string()),
                    ("vulnerabilities_found".to_string(), vulnerabilities_found.to_string()),
                    ("duration_ms".to_string(), duration_ms.to_string()),
                    ("scanner".to_string(), "OSV".to_string()),
                ]),
                ip_address: None,
                user_agent: None,
                session_id: None,
                compliance_framework: Some("OSV".to_string()),
                result: "completed".to_string(),
            };
            self.log_event(event);
        }

        /// Log vulnerability detected
        pub fn log_vulnerability_detected(&self, vulnerability_id: &str, package: &str, severity: &str, cvss_score: Option<f64>, actor: &str) {
            let severity_level = match severity.to_lowercase().as_str() {
                "critical" => AuditSeverity::Critical,
                "high" => AuditSeverity::Error,
                "medium" => AuditSeverity::Warning,
                _ => AuditSeverity::Info,
            };

            let event = AuditEvent {
                id: Uuid::new_v4(),
                timestamp: Utc::now(),
                event_type: AuditEventType::VulnerabilityDetected,
                severity: severity_level,
                actor: actor.to_string(),
                resource: format!("vulnerability:{}", vulnerability_id),
                action: "detected".to_string(),
                details: HashMap::from([
                    ("vulnerability_id".to_string(), vulnerability_id.to_string()),
                    ("package".to_string(), package.to_string()),
                    ("severity".to_string(), severity.to_string()),
                    ("cvss_score".to_string(), cvss_score.map(|s| s.to_string()).unwrap_or_else(|| "N/A".to_string())),
                    ("scanner".to_string(), "OSV".to_string()),
                ]),
                ip_address: None,
                user_agent: None,
                session_id: None,
                compliance_framework: Some("OSV".to_string()),
                result: "detected".to_string(),
            };
            self.log_event(event);
        }

        /// Log false positive filtered
        pub fn log_false_positive_filtered(&self, vulnerability_id: &str, package: &str, reason: &str, actor: &str) {
            let event = AuditEvent {
                id: Uuid::new_v4(),
                timestamp: Utc::now(),
                event_type: AuditEventType::FalsePositiveFiltered,
                severity: AuditSeverity::Info,
                actor: actor.to_string(),
                resource: format!("vulnerability:{}", vulnerability_id),
                action: "filtered".to_string(),
                details: HashMap::from([
                    ("vulnerability_id".to_string(), vulnerability_id.to_string()),
                    ("package".to_string(), package.to_string()),
                    ("reason".to_string(), reason.to_string()),
                    ("scanner".to_string(), "OSV".to_string()),
                ]),
                ip_address: None,
                user_agent: None,
                session_id: None,
                compliance_framework: Some("OSV".to_string()),
                result: "filtered".to_string(),
            };
            self.log_event(event);
        }

        /// Log compliance check
        pub fn log_compliance_check(&self, framework: &str, score: f64, violations: usize, actor: &str) {
            let severity = if violations > 0 {
                AuditSeverity::Warning
            } else {
                AuditSeverity::Info
            };

            let event = AuditEvent {
                id: Uuid::new_v4(),
                timestamp: Utc::now(),
                event_type: AuditEventType::ComplianceCheck,
                severity,
                actor: actor.to_string(),
                resource: format!("compliance:{}", framework),
                action: "checked".to_string(),
                details: HashMap::from([
                    ("framework".to_string(), framework.to_string()),
                    ("compliance_score".to_string(), score.to_string()),
                    ("violations".to_string(), violations.to_string()),
                ]),
                ip_address: None,
                user_agent: None,
                session_id: None,
                compliance_framework: Some(framework.to_string()),
                result: format!("score: {:.1}%", score),
            };
            self.log_event(event);
        }

        /// Log configuration change
        pub fn log_config_change(&self, config_key: &str, old_value: &str, new_value: &str, actor: &str) {
            let event = AuditEvent {
                id: Uuid::new_v4(),
                timestamp: Utc::now(),
                event_type: AuditEventType::ConfigurationChanged,
                severity: AuditSeverity::Warning,
                actor: actor.to_string(),
                resource: format!("config:{}", config_key),
                action: "changed".to_string(),
                details: HashMap::from([
                    ("config_key".to_string(), config_key.to_string()),
                    ("old_value".to_string(), old_value.to_string()),
                    ("new_value".to_string(), new_value.to_string()),
                ]),
                ip_address: None,
                user_agent: None,
                session_id: None,
                compliance_framework: None,
                result: "changed".to_string(),
            };
            self.log_event(event);
        }

        /// Log API call
        pub fn log_api_call(&self, endpoint: &str, method: &str, status_code: u16, duration_ms: u64, actor: &str) {
            let severity = if status_code >= 400 {
                AuditSeverity::Warning
            } else {
                AuditSeverity::Info
            };

            let event = AuditEvent {
                id: Uuid::new_v4(),
                timestamp: Utc::now(),
                event_type: AuditEventType::ApiCall,
                severity,
                actor: actor.to_string(),
                resource: format!("api:{}", endpoint),
                action: method.to_string(),
                details: HashMap::from([
                    ("endpoint".to_string(), endpoint.to_string()),
                    ("method".to_string(), method.to_string()),
                    ("status_code".to_string(), status_code.to_string()),
                    ("duration_ms".to_string(), duration_ms.to_string()),
                ]),
                ip_address: None,
                user_agent: None,
                session_id: None,
                compliance_framework: None,
                result: status_code.to_string(),
            };
            self.log_event(event);
        }
    }

    impl std::fmt::Display for AuditEventType {
        fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
            match self {
                AuditEventType::ScanStarted => write!(f, "SCAN_STARTED"),
                AuditEventType::ScanCompleted => write!(f, "SCAN_COMPLETED"),
                AuditEventType::ScanFailed => write!(f, "SCAN_FAILED"),
                AuditEventType::VulnerabilityDetected => write!(f, "VULNERABILITY_DETECTED"),
                AuditEventType::FalsePositiveFiltered => write!(f, "FALSE_POSITIVE_FILTERED"),
                AuditEventType::ComplianceCheck => write!(f, "COMPLIANCE_CHECK"),
                AuditEventType::ConfigurationChanged => write!(f, "CONFIG_CHANGED"),
                AuditEventType::AccessAttempt => write!(f, "ACCESS_ATTEMPT"),
                AuditEventType::ReportGenerated => write!(f, "REPORT_GENERATED"),
                AuditEventType::ApiCall => write!(f, "API_CALL"),
                AuditEventType::CacheOperation => write!(f, "CACHE_OPERATION"),
            }
        }
    }

    impl std::fmt::Display for AuditSeverity {
        fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
            match self {
                AuditSeverity::Info => write!(f, "INFO"),
                AuditSeverity::Warning => write!(f, "WARNING"),
                AuditSeverity::Error => write!(f, "ERROR"),
                AuditSeverity::Critical => write!(f, "CRITICAL"),
            }
        }
    }

    /// Global audit logger instance
    static mut AUDIT_LOGGER: Option<AuditLogger> = None;

    /// Initialize global audit logger
    pub fn init_audit_logger(enabled: bool, log_file: Option<String>, include_sensitive: bool) {
        unsafe {
            AUDIT_LOGGER = Some(AuditLogger::new(enabled, log_file, include_sensitive));
        }
    }

    /// Get global audit logger
    pub fn get_audit_logger() -> Option<&'static AuditLogger> {
        unsafe { AUDIT_LOGGER.as_ref() }
    }
}
}
