use anyhow::Result;
use clap::{Parser, Subcommand};
use infinitium_signal::{config::Config, demo::InfinitiumDemo, logging::setup_logging};
use std::path::PathBuf;
use tracing::{error, info};

#[derive(Parser)]
#[command(name = "infinitium-signal")]
#[command(about = "Enterprise Cyber-Compliance Platform")]
#[command(version = env!("CARGO_PKG_VERSION"))]
struct Cli {
    /// Configuration file path
    #[arg(short, long, default_value = "config.yaml")]
    config: String,

    /// Log level
    #[arg(long, env = "RUST_LOG")]
    log_level: Option<String>,

    /// Enable development mode
    #[arg(long, env = "DEV_MODE")]
    dev_mode: bool,

    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// Run the API server
    Server {
        /// Server host
        #[arg(long, env = "SERVER_HOST")]
        host: Option<String>,

        /// Server port
        #[arg(long, env = "SERVER_PORT")]
        port: Option<u16>,
    },
    /// Run interactive demo
    Demo {
        /// Target path to scan (optional)
        #[arg(value_name = "PATH")]
        target: Option<PathBuf>,
    },
    /// Generate SBOM for a project
    Sbom {
        /// Target path to scan
        #[arg(value_name = "PATH")]
        target: PathBuf,

        /// Output format
        #[arg(short, long, default_value = "cyclonedx-json")]
        format: String,
    },
    /// Scan for vulnerabilities
    Scan {
        /// Target path to scan
        #[arg(value_name = "PATH")]
        target: PathBuf,

        /// Output format
        #[arg(short, long, default_value = "json")]
        format: String,
    },
}

#[tokio::main]
async fn main() -> Result<()> {
    // Parse command line arguments
    let cli = Cli::parse();

    // Setup basic logging first
    if std::env::var("RUST_LOG").is_err() {
        std::env::set_var("RUST_LOG", "info");
    }

    info!("Starting Infinitium Signal v{}", env!("CARGO_PKG_VERSION"));

    match cli.command {
        Commands::Demo { target } => {
            let demo = InfinitiumDemo::new();
            demo.run_demo(target.as_deref()).await?;
        }
        Commands::Sbom { target, format } => {
            run_sbom_generation(&target, &format)?;
        }
        Commands::Scan { target, format } => {
            run_vulnerability_scan(&target, &format)?;
        }
        Commands::Server { host, port } => {
            // Load configuration for server mode
            let config = Config::load(&cli.config).await?;

            // Setup logging
            setup_logging(&config.logging)?;

            // Override config with CLI arguments
            let mut config = config;
            if let Some(host) = host {
                config.server.host = host;
            }
            if let Some(port) = port {
                config.server.port = port;
            }

            info!(
                "Starting API server on {}:{}",
                config.server.host, config.server.port
            );

            // Initialize observability
            let observability_config = config.get_observability_config();
            let mut observability_manager = infinitium_signal::observability::ObservabilityManager::new(observability_config);
            observability_manager.initialize().await?;
            info!("Observability framework initialized");

            // Start the actual server
            let server =
                infinitium_signal::api::server::ApiServer::new(std::sync::Arc::new(config), std::sync::Arc::new(observability_manager)).await?;
            server.run().await?;
        }
    }

    Ok(())
}

/// Run SBOM generation using external tool
fn run_sbom_generation(target: &std::path::Path, format: &str) -> Result<()> {
    println!("🔍 Generating SBOM for: {}", target.display());
    println!("📄 Format: {}", format);

    let output = std::process::Command::new("syft")
        .arg(target.to_string_lossy().as_ref())
        .arg("-o")
        .arg(format)
        .output()
        .map_err(|e| {
            error!("Failed to run Syft: {}", e);
            e
        })?;

    match output.status.success() {
        true => {
            let sbom = String::from_utf8_lossy(&output.stdout);
            println!("{}", sbom);
            Ok(())
        }
        false => {
            let error = String::from_utf8_lossy(&output.stderr);
            error!("SBOM generation failed: {}", error);
            Err(anyhow::anyhow!("SBOM generation failed: {}", error))
        }
    }
}

/// Run vulnerability scan using external tool
fn run_vulnerability_scan(target: &std::path::Path, format: &str) -> Result<()> {
    println!("🔍 Scanning for vulnerabilities: {}", target.display());
    println!("📄 Format: {}", format);

    let output = std::process::Command::new("trivy")
        .arg("fs")
        .arg("--format")
        .arg(format)
        .arg(target.to_string_lossy().as_ref())
        .output()
        .map_err(|e| {
            error!("Failed to run Trivy: {}", e);
            e
        })?;

    match output.status.success() {
        true => {
            let scan_result = String::from_utf8_lossy(&output.stdout);
            println!("{}", scan_result);
            Ok(())
        }
        false => {
            let error = String::from_utf8_lossy(&output.stderr);
            error!("Vulnerability scan failed: {}", error);
            Err(anyhow::anyhow!("Vulnerability scan failed: {}", error))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cli_parsing() {
        let cli = Cli::try_parse_from(["infinitum-signal", "--config", "test.yaml", "demo"]);

        assert!(cli.is_ok());
        let cli = cli.unwrap();
        assert_eq!(cli.config, "test.yaml");
        assert!(matches!(cli.command, Commands::Demo { .. }));
    }
}
