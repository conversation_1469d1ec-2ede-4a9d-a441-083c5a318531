//! # Alerting and Anomaly Detection Framework
//!
//! This module provides a comprehensive alerting and anomaly detection system
//! that builds on the existing observability infrastructure.

pub mod alert_manager;
pub mod anomaly_detector;
pub mod alert_rules;
pub mod notification;
pub mod alert_analytics;
pub mod alert_api;
pub mod system_health_monitor;
pub mod performance_monitor;
pub mod compliance_monitor;
pub mod security_monitor;

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// Alert severity levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AlertSeverity {
    Info,
    Warning,
    Error,
    Critical,
}

impl AlertSeverity {
    pub fn as_str(&self) -> &'static str {
        match self {
            AlertSeverity::Info => "info",
            AlertSeverity::Warning => "warning",
            AlertSeverity::Error => "error",
            AlertSeverity::Critical => "critical",
        }
    }

    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "info" => Some(AlertSeverity::Info),
            "warning" => Some(AlertSeverity::Warning),
            "error" => Some(AlertSeverity::Error),
            "critical" => Some(AlertSeverity::Critical),
            _ => None,
        }
    }
}

/// Alert status in its lifecycle
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AlertStatus {
    Active,
    Acknowledged,
    Resolved,
    Silenced,
}

impl AlertStatus {
    pub fn as_str(&self) -> &'static str {
        match self {
            AlertStatus::Active => "active",
            AlertStatus::Acknowledged => "acknowledged",
            AlertStatus::Resolved => "resolved",
            AlertStatus::Silenced => "silenced",
        }
    }
}

/// Alert category for organization
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AlertCategory {
    SystemHealth,
    Performance,
    Security,
    Compliance,
    License,
    Business,
    Infrastructure,
    Custom(String),
}

impl AlertCategory {
    pub fn as_str(&self) -> &str {
        match self {
            AlertCategory::SystemHealth => "system_health",
            AlertCategory::Performance => "performance",
            AlertCategory::Security => "security",
            AlertCategory::Compliance => "compliance",
            AlertCategory::License => "license",
            AlertCategory::Business => "business",
            AlertCategory::Infrastructure => "infrastructure",
            AlertCategory::Custom(s) => s.as_str(),
        }
    }
}

/// Core alert structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Alert {
    pub id: String,
    pub title: String,
    pub description: String,
    pub severity: AlertSeverity,
    pub category: AlertCategory,
    pub status: AlertStatus,
    pub source: String,
    pub labels: HashMap<String, String>,
    pub annotations: HashMap<String, String>,
    pub value: Option<f64>,
    pub threshold: Option<f64>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub resolved_at: Option<DateTime<Utc>>,
    pub acknowledged_at: Option<DateTime<Utc>>,
    pub acknowledged_by: Option<String>,
    pub silenced_until: Option<DateTime<Utc>>,
    pub alert_rule_id: Option<String>,
    pub fingerprint: String,
    pub count: u32,
    pub last_occurrence: DateTime<Utc>,
}

/// Alert configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertConfig {
    pub enabled: bool,
    pub evaluation_interval_seconds: u64,
    pub max_alerts_per_rule: usize,
    pub alert_retention_days: u32,
    pub enable_deduplication: bool,
    pub deduplication_window_seconds: u64,
    pub enable_aggregation: bool,
    pub aggregation_window_seconds: u64,
    pub notification_channels: Vec<String>,
    pub maintenance_windows: Vec<MaintenanceWindow>,
}

/// Maintenance window configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MaintenanceWindow {
    pub name: String,
    pub start_time: String, // Cron expression or ISO 8601
    pub end_time: String,
    pub duration_minutes: u32,
    pub recurring: bool,
    pub affected_components: Vec<String>,
}

/// Alerting framework configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertingFrameworkConfig {
    pub alert_config: AlertConfig,
    pub anomaly_detection: anomaly_detector::AnomalyDetectionConfig,
    pub notification: notification::NotificationConfig,
    pub analytics: alert_analytics::AlertAnalyticsConfig,
}

/// Main alerting framework manager
#[derive(Clone)]
pub struct AlertingFramework {
    config: AlertingFrameworkConfig,
    alert_manager: Arc<RwLock<alert_manager::AlertManager>>,
    anomaly_detector: Arc<RwLock<anomaly_detector::AnomalyDetector>>,
    notification_manager: Arc<RwLock<notification::NotificationManager>>,
    analytics_manager: Arc<RwLock<alert_analytics::AlertAnalyticsManager>>,
    system_health_monitor: Option<Arc<RwLock<system_health_monitor::SystemHealthMonitor>>>,
    performance_monitor: Option<Arc<RwLock<performance_monitor::PerformanceMonitor>>>,
    compliance_monitor: Option<Arc<RwLock<compliance_monitor::ComplianceMonitor>>>,
    security_monitor: Option<Arc<RwLock<security_monitor::SecurityMonitor>>>,
    alert_api_server: Option<alert_api::AlertApiServer>,
}

impl AlertingFramework {
    /// Create a new alerting framework instance
    pub fn new(config: AlertingFrameworkConfig) -> Self {
        let alert_manager = Arc::new(RwLock::new(alert_manager::AlertManager::new(config.alert_config.clone())));
        let anomaly_detector = Arc::new(RwLock::new(anomaly_detector::AnomalyDetector::new(config.anomaly_detection.clone())));
        let notification_manager = Arc::new(RwLock::new(notification::NotificationManager::new(config.notification.clone())));
        let analytics_manager = Arc::new(RwLock::new(alert_analytics::AlertAnalyticsManager::new(config.analytics.clone())));

        // Initialize system health monitor if enabled
        let system_health_monitor = if config.alert_config.enable_deduplication {
            Some(Arc::new(RwLock::new(system_health_monitor::SystemHealthMonitor::new(
                system_health_monitor::SystemHealthMonitorConfig::default()
            ))))
        } else {
            None
        };

        // Initialize performance monitor if enabled
        let performance_monitor = if config.alert_config.enabled {
            Some(Arc::new(RwLock::new(performance_monitor::PerformanceMonitor::new(
                performance_monitor::PerformanceMonitorConfig::default()
            ))))
        } else {
            None
        };

        // Initialize compliance monitor if enabled
        let compliance_monitor = if config.alert_config.enabled {
            Some(Arc::new(RwLock::new(compliance_monitor::ComplianceMonitor::new(
                compliance_monitor::ComplianceMonitorConfig::default()
            ))))
        } else {
            None
        };

        // Initialize security monitor if enabled
        let security_monitor = if config.alert_config.enabled {
            Some(Arc::new(RwLock::new(security_monitor::SecurityMonitor::new(
                security_monitor::SecurityMonitorConfig::default()
            ))))
        } else {
            None
        };

        // Initialize alert API server if enabled
        let alert_api_server = if config.alert_config.enabled {
            Some(alert_api::AlertApiServer::new(
                alert_api::AlertApiConfig::default(),
                Arc::new(Self {
                    config: config.clone(),
                    alert_manager: alert_manager.clone(),
                    anomaly_detector: anomaly_detector.clone(),
                    notification_manager: notification_manager.clone(),
                    analytics_manager: analytics_manager.clone(),
                    system_health_monitor: system_health_monitor.clone(),
                    performance_monitor: performance_monitor.clone(),
                    compliance_monitor: compliance_monitor.clone(),
                    security_monitor: security_monitor.clone(),
                    alert_api_server: None,
                })
            ))
        } else {
            None
        };

        Self {
            config,
            alert_manager,
            anomaly_detector,
            notification_manager,
            analytics_manager,
            system_health_monitor,
            performance_monitor,
            compliance_monitor,
            security_monitor,
            alert_api_server,
        }
    }

    /// Initialize the alerting framework
    pub async fn initialize(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Initialize alert manager
        {
            let mut manager = self.alert_manager.write().await;
            manager.initialize().await?;
        }

        // Initialize anomaly detector
        {
            let mut detector = self.anomaly_detector.write().await;
            detector.initialize().await?;
        }

        // Initialize notification manager
        {
            let mut notification = self.notification_manager.write().await;
            notification.initialize().await?;
        }

        // Initialize analytics manager
        {
            let mut analytics = self.analytics_manager.write().await;
            analytics.initialize().await?;
        }

        // Initialize system health monitor and set alerting framework reference
        if let Some(ref mut health_monitor) = self.system_health_monitor {
            {
                let mut monitor = health_monitor.write().await;
                monitor.set_alerting_framework(Arc::new(self.clone()));
            }
        }

        // Initialize performance monitor and set alerting framework reference
        if let Some(ref mut perf_monitor) = self.performance_monitor {
            {
                let mut monitor = perf_monitor.write().await;
                monitor.set_alerting_framework(Arc::new(self.clone()));
            }
        }

        // Initialize compliance monitor and set alerting framework reference
        if let Some(ref mut comp_monitor) = self.compliance_monitor {
            {
                let mut monitor = comp_monitor.write().await;
                monitor.set_alerting_framework(Arc::new(self.clone()));
            }
        }

        // Initialize security monitor and set alerting framework reference
        if let Some(ref mut sec_monitor) = self.security_monitor {
            {
                let mut monitor = sec_monitor.write().await;
                monitor.set_alerting_framework(Arc::new(self.clone()));
            }
        }

        // Start alert API server
        if let Some(ref api_server) = self.alert_api_server {
            // Note: In a real implementation, this would be spawned as a background task
            // For now, we just initialize it
            println!("Alert API server initialized");
        }

        Ok(())
    }

    /// Get alert manager reference
    pub fn alert_manager(&self) -> Arc<RwLock<alert_manager::AlertManager>> {
        self.alert_manager.clone()
    }

    /// Get anomaly detector reference
    pub fn anomaly_detector(&self) -> Arc<RwLock<anomaly_detector::AnomalyDetector>> {
        self.anomaly_detector.clone()
    }

    /// Get notification manager reference
    pub fn notification_manager(&self) -> Arc<RwLock<notification::NotificationManager>> {
        self.notification_manager.clone()
    }

    /// Get analytics manager reference
    pub fn analytics_manager(&self) -> Arc<RwLock<alert_analytics::AlertAnalyticsManager>> {
        self.analytics_manager.clone()
    }

    /// Get system health monitor reference
    pub fn system_health_monitor(&self) -> Option<Arc<RwLock<system_health_monitor::SystemHealthMonitor>>> {
        self.system_health_monitor.clone()
    }

    /// Get performance monitor reference
    pub fn performance_monitor(&self) -> Option<Arc<RwLock<performance_monitor::PerformanceMonitor>>> {
        self.performance_monitor.clone()
    }

    /// Get compliance monitor reference
    pub fn compliance_monitor(&self) -> Option<Arc<RwLock<compliance_monitor::ComplianceMonitor>>> {
        self.compliance_monitor.clone()
    }

    /// Get security monitor reference
    pub fn security_monitor(&self) -> Option<Arc<RwLock<security_monitor::SecurityMonitor>>> {
        self.security_monitor.clone()
    }

    /// Get alert API server reference
    pub fn alert_api_server(&self) -> Option<&alert_api::AlertApiServer> {
        self.alert_api_server.as_ref()
    }

    /// Shutdown the alerting framework
    pub async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Shutdown in reverse order
        {
            let mut analytics = self.analytics_manager.write().await;
            analytics.shutdown().await?;
        }

        {
            let mut notification = self.notification_manager.write().await;
            notification.shutdown().await?;
        }

        {
            let mut detector = self.anomaly_detector.write().await;
            detector.shutdown().await?;
        }

        {
            let mut manager = self.alert_manager.write().await;
            manager.shutdown().await?;
        }

        // Shutdown system health monitor
        if let Some(ref mut health_monitor) = self.system_health_monitor {
            // System health monitor doesn't have a shutdown method, just drop it
        }

        Ok(())
    }
}

/// Default configuration factory
impl Default for AlertingFrameworkConfig {
    fn default() -> Self {
        Self {
            alert_config: AlertConfig {
                enabled: true,
                evaluation_interval_seconds: 30,
                max_alerts_per_rule: 100,
                alert_retention_days: 30,
                enable_deduplication: true,
                deduplication_window_seconds: 300,
                enable_aggregation: true,
                aggregation_window_seconds: 300,
                notification_channels: vec!["email".to_string(), "slack".to_string()],
                maintenance_windows: Vec::new(),
            },
            anomaly_detection: anomaly_detector::AnomalyDetectionConfig::default(),
            notification: notification::NotificationConfig::default(),
            analytics: alert_analytics::AlertAnalyticsConfig::default(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_alert_severity_from_str() {
        assert_eq!(AlertSeverity::from_str("info"), Some(AlertSeverity::Info));
        assert_eq!(AlertSeverity::from_str("WARNING"), Some(AlertSeverity::Warning));
        assert_eq!(AlertSeverity::from_str("Error"), Some(AlertSeverity::Error));
        assert_eq!(AlertSeverity::from_str("CRITICAL"), Some(AlertSeverity::Critical));
        assert_eq!(AlertSeverity::from_str("unknown"), None);
    }

    #[test]
    fn test_alert_severity_as_str() {
        assert_eq!(AlertSeverity::Info.as_str(), "info");
        assert_eq!(AlertSeverity::Warning.as_str(), "warning");
        assert_eq!(AlertSeverity::Error.as_str(), "error");
        assert_eq!(AlertSeverity::Critical.as_str(), "critical");
    }
}