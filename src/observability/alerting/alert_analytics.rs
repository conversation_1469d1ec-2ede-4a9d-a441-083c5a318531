//! # Alert Analytics and Reporting
//!
//! Analytics system for tracking alert patterns, effectiveness metrics,
//! and generating insights for alert management.

use std::collections::{HashMap, HashSet};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, Duration};

use super::{<PERSON>ert, <PERSON>ert<PERSON>ever<PERSON>, AlertStatus, AlertCategory};

/// Alert analytics configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AlertAnalyticsConfig {
    pub enabled: bool,
    pub retention_days: u32,
    pub analysis_window_hours: u32,
    pub enable_pattern_detection: bool,
    pub enable_effectiveness_tracking: bool,
    pub enable_predictive_analytics: bool,
}

impl Default for AlertAnalyticsConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            retention_days: 90,
            analysis_window_hours: 24,
            enable_pattern_detection: true,
            enable_effectiveness_tracking: true,
            enable_predictive_analytics: false,
        }
    }
}

/// Alert analytics manager
pub struct AlertAnalyticsManager {
    config: AlertAnalyticsConfig,
    alert_history: Vec<Alert>,
    pattern_detector: PatternDetector,
    effectiveness_tracker: EffectivenessTracker,
    predictive_analyzer: Option<PredictiveAnalyzer>,
}

impl AlertAnalyticsManager {
    /// Create a new alert analytics manager
    pub fn new(config: AlertAnalyticsConfig) -> Self {
        Self {
            pattern_detector: PatternDetector::new(),
            effectiveness_tracker: EffectivenessTracker::new(),
            predictive_analyzer: if config.enable_predictive_analytics {
                Some(PredictiveAnalyzer::new())
            } else {
                None
            },
            config,
            alert_history: Vec::new(),
        }
    }

    /// Initialize the analytics manager
    pub async fn initialize(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Load historical alert data if available
        Ok(())
    }

    /// Record an alert for analytics
    pub fn record_alert(&mut self, alert: &Alert) {
        self.alert_history.push(alert.clone());

        // Update pattern detector
        if self.config.enable_pattern_detection {
            self.pattern_detector.record_alert(alert);
        }

        // Update effectiveness tracker
        if self.config.enable_effectiveness_tracking {
            self.effectiveness_tracker.record_alert(alert);
        }

        // Clean up old data
        self.cleanup_old_data();
    }

    /// Get alert statistics
    pub fn get_alert_statistics(&self, time_range: Duration) -> AlertStatistics {
        let cutoff = Utc::now() - time_range;
        let recent_alerts: Vec<&Alert> = self.alert_history.iter()
            .filter(|alert| alert.created_at >= cutoff)
            .collect();

        let mut stats = AlertStatistics::default();

        for alert in recent_alerts {
            stats.total_alerts += 1;

            match alert.severity {
                AlertSeverity::Info => stats.info_alerts += 1,
                AlertSeverity::Warning => stats.warning_alerts += 1,
                AlertSeverity::Error => stats.error_alerts += 1,
                AlertSeverity::Critical => stats.critical_alerts += 1,
            }

            match alert.status {
                AlertStatus::Active => stats.active_alerts += 1,
                AlertStatus::Acknowledged => stats.acknowledged_alerts += 1,
                AlertStatus::Resolved => stats.resolved_alerts += 1,
                AlertStatus::Silenced => stats.silenced_alerts += 1,
            }

            // Count by category
            let category_count = stats.alerts_by_category
                .entry(alert.category.clone())
                .or_insert(0);
            *category_count += 1;

            // Count by source
            let source_count = stats.alerts_by_source
                .entry(alert.source.clone())
                .or_insert(0);
            *source_count += 1;
        }

        stats
    }

    /// Get mean time to resolution (MTTR)
    pub fn get_mttr(&self, time_range: Duration) -> Option<f64> {
        let cutoff = Utc::now() - time_range;
        let resolved_alerts: Vec<&Alert> = self.alert_history.iter()
            .filter(|alert| {
                alert.created_at >= cutoff &&
                alert.status == AlertStatus::Resolved &&
                alert.resolved_at.is_some()
            })
            .collect();

        if resolved_alerts.is_empty() {
            return None;
        }

        let total_resolution_time: f64 = resolved_alerts.iter()
            .map(|alert| {
                let resolved_at = alert.resolved_at.unwrap();
                (resolved_at - alert.created_at).num_seconds() as f64
            })
            .sum();

        Some(total_resolution_time / resolved_alerts.len() as f64)
    }

    /// Get alert effectiveness metrics
    pub fn get_effectiveness_metrics(&self) -> AlertEffectivenessMetrics {
        self.effectiveness_tracker.get_metrics()
    }

    /// Get alert patterns
    pub fn get_alert_patterns(&self) -> Vec<AlertPattern> {
        self.pattern_detector.get_patterns()
    }

    /// Get alert trends
    pub fn get_alert_trends(&self, time_range: Duration) -> AlertTrends {
        let cutoff = Utc::now() - time_range;
        let recent_alerts: Vec<&Alert> = self.alert_history.iter()
            .filter(|alert| alert.created_at >= cutoff)
            .collect();

        let mut trends = AlertTrends::default();

        // Group alerts by hour
        let mut hourly_counts: HashMap<i64, u32> = HashMap::new();

        for alert in recent_alerts {
            let hour = alert.created_at.timestamp() / 3600;
            *hourly_counts.entry(hour).or_insert(0) += 1;
        }

        // Calculate trend metrics
        let hours: Vec<i64> = hourly_counts.keys().cloned().collect();
        if !hours.is_empty() {
            let min_hour = *hours.iter().min().unwrap();
            let max_hour = *hours.iter().max().unwrap();
            let total_hours = (max_hour - min_hour + 1) as f64;

            trends.average_alerts_per_hour = hourly_counts.values().sum::<u32>() as f64 / total_hours;
            trends.peak_hour_alerts = *hourly_counts.values().max().unwrap_or(&0);
            trends.total_hours_analyzed = total_hours as u32;
        }

        trends
    }

    /// Generate alert report
    pub fn generate_report(&self, time_range: Duration) -> AlertReport {
        let statistics = self.get_alert_statistics(time_range);
        let mttr = self.get_mttr(time_range);
        let effectiveness = self.get_effectiveness_metrics();
        let patterns = self.get_alert_patterns();
        let trends = self.get_alert_trends(time_range);

        AlertReport {
            generated_at: Utc::now(),
            time_range,
            statistics: statistics.clone(),
            mttr,
            effectiveness: effectiveness.clone(),
            patterns,
            trends,
            recommendations: self.generate_recommendations(&statistics, &effectiveness),
        }
    }

    /// Generate recommendations based on analytics
    fn generate_recommendations(&self, stats: &AlertStatistics, effectiveness: &AlertEffectivenessMetrics) -> Vec<String> {
        let mut recommendations = Vec::new();

        // High alert volume recommendations
        if stats.total_alerts > 1000 {
            recommendations.push("Consider implementing alert aggregation to reduce noise".to_string());
        }

        // High false positive rate
        if effectiveness.false_positive_rate > 0.3 {
            recommendations.push("Review alert rules to reduce false positives".to_string());
        }

        // Long MTTR
        if let Some(mttr) = effectiveness.average_resolution_time_seconds {
            if mttr > 3600.0 { // 1 hour
                recommendations.push("Investigate processes to reduce mean time to resolution".to_string());
            }
        }

        // Critical alert handling
        if stats.critical_alerts > stats.total_alerts / 10 {
            recommendations.push("Too many critical alerts - review severity classification".to_string());
        }

        // Alert source diversity
        if stats.alerts_by_source.len() < 3 {
            recommendations.push("Consider adding more monitoring sources for better coverage".to_string());
        }

        recommendations
    }

    /// Clean up old alert data
    fn cleanup_old_data(&mut self) {
        let cutoff = Utc::now() - Duration::days(self.config.retention_days as i64);

        self.alert_history.retain(|alert| alert.created_at >= cutoff);
        self.pattern_detector.cleanup_old_data(cutoff);
        self.effectiveness_tracker.cleanup_old_data(cutoff);

        if let Some(predictive) = &mut self.predictive_analyzer {
            predictive.cleanup_old_data(cutoff);
        }
    }

    /// Shutdown the analytics manager
    pub async fn shutdown(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Clean up resources
        self.alert_history.clear();
        Ok(())
    }
}

/// Alert statistics
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct AlertStatistics {
    pub total_alerts: u64,
    pub active_alerts: u64,
    pub acknowledged_alerts: u64,
    pub resolved_alerts: u64,
    pub silenced_alerts: u64,
    pub info_alerts: u64,
    pub warning_alerts: u64,
    pub error_alerts: u64,
    pub critical_alerts: u64,
    pub alerts_by_category: HashMap<AlertCategory, u64>,
    pub alerts_by_source: HashMap<String, u64>,
}

/// Alert effectiveness metrics
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct AlertEffectivenessMetrics {
    pub total_resolved_alerts: u64,
    pub false_positive_rate: f64,
    pub true_positive_rate: f64,
    pub average_resolution_time_seconds: Option<f64>,
    pub median_resolution_time_seconds: Option<f64>,
    pub resolution_time_percentiles: HashMap<String, f64>,
    pub alerts_acknowledged_within_sla: u64,
    pub alerts_resolved_within_sla: u64,
}

/// Alert pattern
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertPattern {
    pub pattern_id: String,
    pub description: String,
    pub frequency: u32,
    pub average_severity: f64,
    pub common_labels: HashMap<String, String>,
    pub time_distribution: HashMap<String, u32>,
    pub confidence_score: f64,
    pub last_occurrence: DateTime<Utc>,
}

/// Alert trends
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct AlertTrends {
    pub average_alerts_per_hour: f64,
    pub peak_hour_alerts: u32,
    pub total_hours_analyzed: u32,
    pub trend_direction: String, // "increasing", "decreasing", "stable"
    pub seasonality_detected: bool,
}

/// Alert report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertReport {
    pub generated_at: DateTime<Utc>,
    pub time_range: Duration,
    pub statistics: AlertStatistics,
    pub mttr: Option<f64>,
    pub effectiveness: AlertEffectivenessMetrics,
    pub patterns: Vec<AlertPattern>,
    pub trends: AlertTrends,
    pub recommendations: Vec<String>,
}

/// Pattern detector for identifying alert patterns
pub struct PatternDetector {
    patterns: HashMap<String, AlertPattern>,
    alert_buffer: Vec<Alert>,
    max_buffer_size: usize,
}

impl PatternDetector {
    pub fn new() -> Self {
        Self {
            patterns: HashMap::new(),
            alert_buffer: Vec::new(),
            max_buffer_size: 1000,
        }
    }

    pub fn record_alert(&mut self, alert: &Alert) {
        self.alert_buffer.push(alert.clone());

        if self.alert_buffer.len() > self.max_buffer_size {
            self.alert_buffer.remove(0);
        }

        self.update_patterns();
    }

    fn update_patterns(&mut self) {
        // Simple pattern detection based on title similarity
        let mut title_groups: HashMap<String, Vec<&Alert>> = HashMap::new();

        for alert in &self.alert_buffer {
            let key = alert.title.to_lowercase();
            title_groups.entry(key).or_insert_with(Vec::new).push(alert);
        }

        for (title, alerts) in title_groups {
            if alerts.len() >= 3 { // Minimum pattern threshold
                let pattern_id = format!("pattern_{}", title.replace(" ", "_"));

                let pattern = self.patterns.entry(pattern_id.clone())
                    .or_insert_with(|| AlertPattern {
                        pattern_id,
                        description: format!("Recurring alerts with title: {}", title),
                        frequency: 0,
                        average_severity: 0.0,
                        common_labels: HashMap::new(),
                        time_distribution: HashMap::new(),
                        confidence_score: 0.0,
                        last_occurrence: Utc::now(),
                    });

                pattern.frequency = alerts.len() as u32;
                pattern.last_occurrence = alerts.iter()
                    .map(|a| a.created_at)
                    .max()
                    .unwrap_or(Utc::now());

                // Calculate average severity
                let total_severity: u32 = alerts.iter()
                    .map(|a| match a.severity {
                        AlertSeverity::Info => 1,
                        AlertSeverity::Warning => 2,
                        AlertSeverity::Error => 3,
                        AlertSeverity::Critical => 4,
                    })
                    .sum();
                pattern.average_severity = total_severity as f64 / alerts.len() as f64;

                // Calculate confidence score based on frequency and recency
                let hours_since_first = (Utc::now() - alerts[0].created_at).num_hours() as f64;
                if hours_since_first > 0.0 {
                    pattern.confidence_score = (alerts.len() as f64 / (hours_since_first / 24.0)).min(1.0);
                }
            }
        }
    }

    pub fn get_patterns(&self) -> Vec<AlertPattern> {
        self.patterns.values().cloned().collect()
    }

    pub fn cleanup_old_data(&mut self, cutoff: DateTime<Utc>) {
        self.alert_buffer.retain(|alert| alert.created_at >= cutoff);
        self.patterns.retain(|_, pattern| pattern.last_occurrence >= cutoff);
    }
}

/// Effectiveness tracker for alert quality metrics
pub struct EffectivenessTracker {
    resolved_alerts: Vec<Alert>,
    false_positives: Vec<Alert>,
    resolution_times: Vec<f64>,
}

impl EffectivenessTracker {
    pub fn new() -> Self {
        Self {
            resolved_alerts: Vec::new(),
            false_positives: Vec::new(),
            resolution_times: Vec::new(),
        }
    }

    pub fn record_alert(&mut self, alert: &Alert) {
        if alert.status == AlertStatus::Resolved {
            self.resolved_alerts.push(alert.clone());

            if let Some(resolved_at) = alert.resolved_at {
                let resolution_time = (resolved_at - alert.created_at).num_seconds() as f64;
                self.resolution_times.push(resolution_time);
            }
        }

        // In a real system, false positives would be marked by users
        // For now, we'll assume alerts that are resolved quickly are potentially false positives
        if alert.status == AlertStatus::Resolved {
            if let Some(resolved_at) = alert.resolved_at {
                let resolution_time = (resolved_at - alert.created_at).num_seconds();
                if resolution_time < 300 { // Less than 5 minutes
                    self.false_positives.push(alert.clone());
                }
            }
        }
    }

    pub fn get_metrics(&self) -> AlertEffectivenessMetrics {
        let mut metrics = AlertEffectivenessMetrics::default();

        metrics.total_resolved_alerts = self.resolved_alerts.len() as u64;

        if !self.resolved_alerts.is_empty() {
            metrics.false_positive_rate = self.false_positives.len() as f64 / self.resolved_alerts.len() as f64;
            metrics.true_positive_rate = 1.0 - metrics.false_positive_rate;
        }

        if !self.resolution_times.is_empty() {
            metrics.average_resolution_time_seconds = Some(
                self.resolution_times.iter().sum::<f64>() / self.resolution_times.len() as f64
            );

            // Calculate median
            let mut sorted_times = self.resolution_times.clone();
            sorted_times.sort_by(|a, b| a.partial_cmp(b).unwrap());
            let mid = sorted_times.len() / 2;
            metrics.median_resolution_time_seconds = Some(sorted_times[mid]);

            // Calculate percentiles
            let p95_index = (sorted_times.len() as f64 * 0.95) as usize;
            let p99_index = (sorted_times.len() as f64 * 0.99) as usize;

            metrics.resolution_time_percentiles.insert(
                "p95".to_string(),
                sorted_times.get(p95_index).copied().unwrap_or(0.0)
            );
            metrics.resolution_time_percentiles.insert(
                "p99".to_string(),
                sorted_times.get(p99_index).copied().unwrap_or(0.0)
            );
        }

        metrics
    }

    pub fn cleanup_old_data(&mut self, cutoff: DateTime<Utc>) {
        self.resolved_alerts.retain(|alert| alert.created_at >= cutoff);
        self.false_positives.retain(|alert| alert.created_at >= cutoff);
        self.resolution_times.clear(); // Recalculate from remaining alerts
    }
}

/// Predictive analyzer for forecasting alert patterns
pub struct PredictiveAnalyzer {
    // Placeholder for predictive analytics
    // In a real implementation, this would use ML models
}

impl PredictiveAnalyzer {
    pub fn new() -> Self {
        Self {}
    }

    pub fn cleanup_old_data(&mut self, _cutoff: DateTime<Utc>) {
        // Clean up old prediction data
    }
}