//! # Alert Management API
//!
//! REST API for alert management, querying, and configuration.
//! Provides endpoints for alert lifecycle management and analytics.

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use warp::Filter;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, Duration};

use super::{Alert, AlertStatus, AlertSeverity, AlertCategory, AlertingFramework};

/// Alert API configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertApiConfig {
    pub enabled: bool,
    pub host: String,
    pub port: u16,
    pub api_key_required: bool,
    pub cors_enabled: bool,
    pub rate_limiting_enabled: bool,
    pub max_requests_per_minute: u32,
}

/// Alert query parameters
#[derive(Debug, Deserialize)]
pub struct AlertQuery {
    pub status: Option<AlertStatus>,
    pub severity: Option<AlertSeverity>,
    pub category: Option<AlertCategory>,
    pub source: Option<String>,
    pub limit: Option<usize>,
    pub offset: Option<usize>,
    pub start_time: Option<DateTime<Utc>>,
    pub end_time: Option<DateTime<Utc>>,
}

/// Alert update request
#[derive(Debug, Deserialize)]
pub struct AlertUpdateRequest {
    pub status: Option<AlertStatus>,
    pub acknowledged_by: Option<String>,
    pub silenced_duration_minutes: Option<u32>,
}

/// Alert rule creation/update request
#[derive(Debug, Deserialize)]
pub struct AlertRuleRequest {
    pub name: String,
    pub description: String,
    pub category: AlertCategory,
    pub severity: AlertSeverity,
    pub query: String,
    pub enabled: bool,
}

/// API response wrapper
#[derive(Debug, Serialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
    pub timestamp: DateTime<Utc>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            timestamp: Utc::now(),
        }
    }

    pub fn error(message: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(message),
            timestamp: Utc::now(),
        }
    }
}

/// Alert statistics response
#[derive(Debug, Serialize)]
pub struct AlertStatisticsResponse {
    pub total_alerts: u64,
    pub active_alerts: u64,
    pub acknowledged_alerts: u64,
    pub resolved_alerts: u64,
    pub silenced_alerts: u64,
    pub alerts_by_severity: HashMap<String, u64>,
    pub alerts_by_category: HashMap<String, u64>,
    pub average_resolution_time_minutes: Option<f64>,
    pub mttr_by_category: HashMap<String, f64>,
}

/// Alert management API server
pub struct AlertApiServer {
    config: AlertApiConfig,
    alerting_framework: Arc<RwLock<AlertingFramework>>,
}

impl AlertApiServer {
    /// Create a new alert API server
    pub fn new(config: AlertApiConfig, alerting_framework: Arc<RwLock<AlertingFramework>>) -> Self {
        Self {
            config,
            alerting_framework,
        }
    }

    /// Start the API server
    pub async fn start(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if !self.config.enabled {
            return Ok(());
        }

        let routes = self.build_routes();

        let addr = format!("{}:{}", self.config.host, self.config.port);
        println!("Starting Alert API server on {}", addr);

        warp::serve(routes)
            .run(([0, 0, 0, 0], self.config.port))
            .await;

        Ok(())
    }

    /// Build API routes
    fn build_routes(&self) -> impl Filter<Extract = impl warp::Reply, Error = warp::Rejection> + Clone {
        let framework = self.alerting_framework.clone();

        // Health check endpoint
        let health = warp::path("health")
            .map(|| warp::reply::json(&ApiResponse::<String>::success("OK".to_string())));

        // Get all alerts
        let get_alerts = warp::path("alerts")
            .and(warp::get())
            .and(warp::query::<AlertQuery>())
            .and(with_framework(framework.clone()))
            .and_then(Self::handle_get_alerts);

        // Get specific alert
        let get_alert = warp::path!("alerts" / String)
            .and(warp::get())
            .and(with_framework(framework.clone()))
            .and_then(Self::handle_get_alert);

        // Update alert
        let update_alert = warp::path!("alerts" / String)
            .and(warp::put())
            .and(warp::body::json())
            .and(with_framework(framework.clone()))
            .and_then(Self::handle_update_alert);

        // Acknowledge alert
        let acknowledge_alert = warp::path!("alerts" / String / "acknowledge")
            .and(warp::post())
            .and(warp::body::json())
            .and(with_framework(framework.clone()))
            .and_then(Self::handle_acknowledge_alert);

        // Resolve alert
        let resolve_alert = warp::path!("alerts" / String / "resolve")
            .and(warp::post())
            .and(with_framework(framework.clone()))
            .and_then(Self::handle_resolve_alert);

        // Silence alert
        let silence_alert = warp::path!("alerts" / String / "silence")
            .and(warp::post())
            .and(warp::body::json())
            .and(with_framework(framework.clone()))
            .and_then(Self::handle_silence_alert);

        // Get alert statistics
        let get_stats = warp::path("alerts" / "stats")
            .and(warp::get())
            .and(with_framework(framework.clone()))
            .and_then(Self::handle_get_statistics);

        // Get alert rules
        let get_rules = warp::path("rules")
            .and(warp::get())
            .and(with_framework(framework.clone()))
            .and_then(Self::handle_get_alert_rules);

        // Create alert rule
        let create_rule = warp::path("rules")
            .and(warp::post())
            .and(warp::body::json())
            .and(with_framework(framework.clone()))
            .and_then(Self::handle_create_alert_rule);

        // Update alert rule
        let update_rule = warp::path!("rules" / String)
            .and(warp::put())
            .and(warp::body::json())
            .and(with_framework(framework.clone()))
            .and_then(Self::handle_update_alert_rule);

        // Delete alert rule
        let delete_rule = warp::path!("rules" / String)
            .and(warp::delete())
            .and(with_framework(framework.clone()))
            .and_then(Self::handle_delete_alert_rule);

        // Combine all routes
        let api = warp::path("api")
            .and(warp::path("v1"))
            .and(
                health
                    .or(get_alerts)
                    .or(get_alert)
                    .or(update_alert)
                    .or(acknowledge_alert)
                    .or(resolve_alert)
                    .or(silence_alert)
                    .or(get_stats)
                    .or(get_rules)
                    .or(create_rule)
                    .or(update_rule)
                    .or(delete_rule)
            );

        // Add CORS if enabled
        if self.config.cors_enabled {
            api.with(warp::cors().allow_any_origin().allow_methods(vec!["GET", "POST", "PUT", "DELETE"]).allow_headers(vec!["content-type"]))
        } else {
            api
        }
    }

    /// Handle GET /alerts
    async fn handle_get_alerts(
        query: AlertQuery,
        framework: Arc<RwLock<AlertingFramework>>,
    ) -> Result<impl warp::Reply, warp::Rejection> {
        let framework = framework.read().await;
        if let Some(alert_manager) = framework.alert_manager().try_read() {
            let mut alerts: Vec<&Alert> = alert_manager.get_alerts();

            // Apply filters
            if let Some(status) = query.status {
                alerts.retain(|a| a.status == status);
            }
            if let Some(severity) = query.severity {
                alerts.retain(|a| a.severity == severity);
            }
            if let Some(category) = query.category {
                alerts.retain(|a| a.category == category);
            }
            if let Some(source) = query.source {
                alerts.retain(|a| a.source == source);
            }
            if let Some(start_time) = query.start_time {
                alerts.retain(|a| a.created_at >= start_time);
            }
            if let Some(end_time) = query.end_time {
                alerts.retain(|a| a.created_at <= end_time);
            }

            // Apply pagination
            let offset = query.offset.unwrap_or(0);
            let limit = query.limit.unwrap_or(100).min(1000);
            let paginated_alerts: Vec<&Alert> = alerts
                .into_iter()
                .skip(offset)
                .take(limit)
                .collect();

            Ok(warp::reply::json(&ApiResponse::success(paginated_alerts)))
        } else {
            Ok(warp::reply::json(&ApiResponse::<Vec<Alert>>::error("Alert manager not available".to_string())))
        }
    }

    /// Handle GET /alerts/{id}
    async fn handle_get_alert(
        alert_id: String,
        framework: Arc<RwLock<AlertingFramework>>,
    ) -> Result<impl warp::Reply, warp::Rejection> {
        let framework = framework.read().await;
        if let Some(alert_manager) = framework.alert_manager().try_read() {
            if let Some(alert) = alert_manager.get_alert(&alert_id) {
                Ok(warp::reply::json(&ApiResponse::success(alert)))
            } else {
                Ok(warp::reply::json(&ApiResponse::<Alert>::error("Alert not found".to_string())))
            }
        } else {
            Ok(warp::reply::json(&ApiResponse::<Alert>::error("Alert manager not available".to_string())))
        }
    }

    /// Handle PUT /alerts/{id}
    async fn handle_update_alert(
        alert_id: String,
        update: AlertUpdateRequest,
        framework: Arc<RwLock<AlertingFramework>>,
    ) -> Result<impl warp::Reply, warp::Rejection> {
        let framework = framework.read().await;
        if let Some(mut alert_manager) = framework.alert_manager().try_write() {
            let mut success = true;

            if let Some(status) = update.status {
                match status {
                    AlertStatus::Acknowledged => {
                        success &= alert_manager.acknowledge_alert(&alert_id, &update.acknowledged_by.unwrap_or_else(|| "api".to_string()));
                    }
                    AlertStatus::Resolved => {
                        success &= alert_manager.resolve_alert(&alert_id);
                    }
                    AlertStatus::Silenced => {
                        if let Some(duration) = update.silenced_duration_minutes {
                            success &= alert_manager.silence_alert(&alert_id, duration);
                        }
                    }
                    _ => {}
                }
            }

            if success {
                if let Some(alert) = alert_manager.get_alert(&alert_id) {
                    Ok(warp::reply::json(&ApiResponse::success(alert)))
                } else {
                    Ok(warp::reply::json(&ApiResponse::<Alert>::error("Alert not found after update".to_string())))
                }
            } else {
                Ok(warp::reply::json(&ApiResponse::<Alert>::error("Failed to update alert".to_string())))
            }
        } else {
            Ok(warp::reply::json(&ApiResponse::<Alert>::error("Alert manager not available".to_string())))
        }
    }

    /// Handle POST /alerts/{id}/acknowledge
    async fn handle_acknowledge_alert(
        alert_id: String,
        request: HashMap<String, String>,
        framework: Arc<RwLock<AlertingFramework>>,
    ) -> Result<impl warp::Reply, warp::Rejection> {
        let user = request.get("user").cloned().unwrap_or_else(|| "api".to_string());

        let framework = framework.read().await;
        if let Some(mut alert_manager) = framework.alert_manager().try_write() {
            if alert_manager.acknowledge_alert(&alert_id, &user) {
                Ok(warp::reply::json(&ApiResponse::success("Alert acknowledged".to_string())))
            } else {
                Ok(warp::reply::json(&ApiResponse::<String>::error("Failed to acknowledge alert".to_string())))
            }
        } else {
            Ok(warp::reply::json(&ApiResponse::<String>::error("Alert manager not available".to_string())))
        }
    }

    /// Handle POST /alerts/{id}/resolve
    async fn handle_resolve_alert(
        alert_id: String,
        framework: Arc<RwLock<AlertingFramework>>,
    ) -> Result<impl warp::Reply, warp::Rejection> {
        let framework = framework.read().await;
        if let Some(mut alert_manager) = framework.alert_manager().try_write() {
            if alert_manager.resolve_alert(&alert_id) {
                Ok(warp::reply::json(&ApiResponse::success("Alert resolved".to_string())))
            } else {
                Ok(warp::reply::json(&ApiResponse::<String>::error("Failed to resolve alert".to_string())))
            }
        } else {
            Ok(warp::reply::json(&ApiResponse::<String>::error("Alert manager not available".to_string())))
        }
    }

    /// Handle POST /alerts/{id}/silence
    async fn handle_silence_alert(
        alert_id: String,
        request: HashMap<String, u32>,
        framework: Arc<RwLock<AlertingFramework>>,
    ) -> Result<impl warp::Reply, warp::Rejection> {
        let duration = request.get("duration_minutes").copied().unwrap_or(60);

        let framework = framework.read().await;
        if let Some(mut alert_manager) = framework.alert_manager().try_write() {
            if alert_manager.silence_alert(&alert_id, duration) {
                Ok(warp::reply::json(&ApiResponse::success("Alert silenced".to_string())))
            } else {
                Ok(warp::reply::json(&ApiResponse::<String>::error("Failed to silence alert".to_string())))
            }
        } else {
            Ok(warp::reply::json(&ApiResponse::<String>::error("Alert manager not available".to_string())))
        }
    }

    /// Handle GET /alerts/stats
    async fn handle_get_statistics(
        framework: Arc<RwLock<AlertingFramework>>,
    ) -> Result<impl warp::Reply, warp::Rejection> {
        let framework = framework.read().await;
        if let Some(alert_manager) = framework.alert_manager().try_read() {
            let alerts = alert_manager.get_alerts();

            let mut stats = AlertStatisticsResponse {
                total_alerts: alerts.len() as u64,
                active_alerts: 0,
                acknowledged_alerts: 0,
                resolved_alerts: 0,
                silenced_alerts: 0,
                alerts_by_severity: HashMap::new(),
                alerts_by_category: HashMap::new(),
                average_resolution_time_minutes: None,
                mttr_by_category: HashMap::new(),
            };

            let mut resolution_times = Vec::new();

            for alert in alerts {
                match alert.status {
                    AlertStatus::Active => stats.active_alerts += 1,
                    AlertStatus::Acknowledged => stats.acknowledged_alerts += 1,
                    AlertStatus::Resolved => {
                        stats.resolved_alerts += 1;
                        if let Some(resolved_at) = alert.resolved_at {
                            let resolution_time = (resolved_at - alert.created_at).num_minutes() as f64;
                            resolution_times.push(resolution_time);
                        }
                    }
                    AlertStatus::Silenced => stats.silenced_alerts += 1,
                }

                *stats.alerts_by_severity.entry(alert.severity.as_str().to_string()).or_insert(0) += 1;
                *stats.alerts_by_category.entry(alert.category.as_str().to_string()).or_insert(0) += 1;
            }

            if !resolution_times.is_empty() {
                stats.average_resolution_time_minutes = Some(
                    resolution_times.iter().sum::<f64>() / resolution_times.len() as f64
                );
            }

            Ok(warp::reply::json(&ApiResponse::success(stats)))
        } else {
            Ok(warp::reply::json(&ApiResponse::<AlertStatisticsResponse>::error("Alert manager not available".to_string())))
        }
    }

    /// Handle GET /rules
    async fn handle_get_alert_rules(
        framework: Arc<RwLock<AlertingFramework>>,
    ) -> Result<impl warp::Reply, warp::Rejection> {
        let framework = framework.read().await;
        if let Some(alert_manager) = framework.alert_manager().try_read() {
            let rules = alert_manager.get_alert_rules();
            Ok(warp::reply::json(&ApiResponse::success(rules)))
        } else {
            Ok(warp::reply::json(&ApiResponse::<Vec<super::alert_manager::AlertRule>>::error("Alert manager not available".to_string())))
        }
    }

    /// Handle POST /rules
    async fn handle_create_alert_rule(
        request: AlertRuleRequest,
        framework: Arc<RwLock<AlertingFramework>>,
    ) -> Result<impl warp::Reply, warp::Rejection> {
        let framework = framework.read().await;
        if let Some(mut alert_manager) = framework.alert_manager().try_write() {
            let rule = super::alert_manager::AlertRule {
                id: format!("rule_{}", Utc::now().timestamp()),
                name: request.name,
                description: request.description,
                enabled: request.enabled,
                category: request.category,
                severity: request.severity,
                query: request.query,
                condition: super::alert_manager::AlertCondition::Custom {
                    expression: "true".to_string(), // Placeholder
                },
                labels: HashMap::new(),
                annotations: HashMap::new(),
                evaluation_interval_seconds: 60,
                for_duration_seconds: 300,
                last_evaluation: None,
                last_alert: None,
            };

            alert_manager.add_alert_rule(rule.clone());
            Ok(warp::reply::json(&ApiResponse::success(rule)))
        } else {
            Ok(warp::reply::json(&ApiResponse::<super::alert_manager::AlertRule>::error("Alert manager not available".to_string())))
        }
    }

    /// Handle PUT /rules/{id}
    async fn handle_update_alert_rule(
        rule_id: String,
        request: AlertRuleRequest,
        framework: Arc<RwLock<AlertingFramework>>,
    ) -> Result<impl warp::Reply, warp::Rejection> {
        let framework = framework.read().await;
        if let Some(mut alert_manager) = framework.alert_manager().try_write() {
            // Remove old rule
            alert_manager.remove_alert_rule(&rule_id);

            // Add updated rule
            let rule = super::alert_manager::AlertRule {
                id: rule_id,
                name: request.name,
                description: request.description,
                enabled: request.enabled,
                category: request.category,
                severity: request.severity,
                query: request.query,
                condition: super::alert_manager::AlertCondition::Custom {
                    expression: "true".to_string(), // Placeholder
                },
                labels: HashMap::new(),
                annotations: HashMap::new(),
                evaluation_interval_seconds: 60,
                for_duration_seconds: 300,
                last_evaluation: None,
                last_alert: None,
            };

            alert_manager.add_alert_rule(rule.clone());
            Ok(warp::reply::json(&ApiResponse::success(rule)))
        } else {
            Ok(warp::reply::json(&ApiResponse::<super::alert_manager::AlertRule>::error("Alert manager not available".to_string())))
        }
    }

    /// Handle DELETE /rules/{id}
    async fn handle_delete_alert_rule(
        rule_id: String,
        framework: Arc<RwLock<AlertingFramework>>,
    ) -> Result<impl warp::Reply, warp::Rejection> {
        let framework = framework.read().await;
        if let Some(mut alert_manager) = framework.alert_manager().try_write() {
            if alert_manager.remove_alert_rule(&rule_id) {
                Ok(warp::reply::json(&ApiResponse::success("Rule deleted".to_string())))
            } else {
                Ok(warp::reply::json(&ApiResponse::<String>::error("Rule not found".to_string())))
            }
        } else {
            Ok(warp::reply::json(&ApiResponse::<String>::error("Alert manager not available".to_string())))
        }
    }
}

/// Helper function to pass framework to handlers
fn with_framework(
    framework: Arc<RwLock<AlertingFramework>>,
) -> impl Filter<Extract = (Arc<RwLock<AlertingFramework>>,), Error = std::convert::Infallible> + Clone {
    warp::any().map(move || framework.clone())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_api_response_success() {
        let response: ApiResponse<String> = ApiResponse::success("test".to_string());
        assert!(response.success);
        assert_eq!(response.data, Some("test".to_string()));
        assert!(response.error.is_none());
    }

    #[test]
    fn test_api_response_error() {
        let response: ApiResponse<String> = ApiResponse::error("test error".to_string());
        assert!(!response.success);
        assert!(response.data.is_none());
        assert_eq!(response.error, Some("test error".to_string()));
    }
}