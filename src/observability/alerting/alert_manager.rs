//! # Alert Manager
//!
//! Core component responsible for alert lifecycle management, rule evaluation,
//! deduplication, and aggregation.

use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use tokio::sync::RwLock;
use chrono::{DateTime, Utc, Duration};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use super::{Alert, AlertStatus, AlertSeverity, AlertCategory, AlertConfig};
use crate::observability::custom_metrics::CustomMetricsManager;

/// Alert rule definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertRule {
    pub id: String,
    pub name: String,
    pub description: String,
    pub enabled: bool,
    pub category: AlertCategory,
    pub severity: AlertSeverity,
    pub query: String, // PromQL or custom expression
    pub condition: AlertCondition,
    pub labels: HashMap<String, String>,
    pub annotations: HashMap<String, String>,
    pub evaluation_interval_seconds: u64,
    pub for_duration_seconds: u64,
    pub last_evaluation: Option<DateTime<Utc>>,
    pub last_alert: Option<DateTime<Utc>>,
}

/// Alert condition types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertCondition {
    Threshold {
        operator: ThresholdOperator,
        value: f64,
    },
    Rate {
        rate: f64,
        time_window_seconds: u64,
    },
    Anomaly {
        algorithm: String,
        sensitivity: f64,
    },
    Custom {
        expression: String,
    },
}

/// Threshold operators
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ThresholdOperator {
    GreaterThan,
    GreaterThanOrEqual,
    LessThan,
    LessThanOrEqual,
    Equal,
    NotEqual,
}

impl ThresholdOperator {
    pub fn evaluate(&self, actual: f64, threshold: f64) -> bool {
        match self {
            ThresholdOperator::GreaterThan => actual > threshold,
            ThresholdOperator::GreaterThanOrEqual => actual >= threshold,
            ThresholdOperator::LessThan => actual < threshold,
            ThresholdOperator::LessThanOrEqual => actual <= threshold,
            ThresholdOperator::Equal => (actual - threshold).abs() < f64::EPSILON,
            ThresholdOperator::NotEqual => (actual - threshold).abs() >= f64::EPSILON,
        }
    }
}

/// Alert manager for handling alert lifecycle
pub struct AlertManager {
    config: AlertConfig,
    alerts: HashMap<String, Alert>,
    alert_rules: HashMap<String, AlertRule>,
    active_alerts: HashMap<String, Vec<String>>, // fingerprint -> alert_ids
    silenced_alerts: HashMap<String, DateTime<Utc>>, // alert_id -> silenced_until
    maintenance_windows: Vec<super::MaintenanceWindow>,
    metrics_manager: Option<Arc<RwLock<CustomMetricsManager>>>,
}

impl AlertManager {
    /// Create a new alert manager
    pub fn new(config: AlertConfig) -> Self {
        Self {
            config,
            alerts: HashMap::new(),
            alert_rules: HashMap::new(),
            active_alerts: HashMap::new(),
            silenced_alerts: HashMap::new(),
            maintenance_windows: Vec::new(),
            metrics_manager: None,
        }
    }

    /// Initialize the alert manager
    pub async fn initialize(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Load default alert rules
        self.load_default_alert_rules();

        // Set up maintenance windows
        self.maintenance_windows = self.config.maintenance_windows.clone();

        Ok(())
    }

    /// Set metrics manager reference
    pub fn set_metrics_manager(&mut self, manager: Arc<RwLock<CustomMetricsManager>>) {
        self.metrics_manager = Some(manager);
    }

    /// Add an alert rule
    pub fn add_alert_rule(&mut self, rule: AlertRule) {
        self.alert_rules.insert(rule.id.clone(), rule);
    }

    /// Remove an alert rule
    pub fn remove_alert_rule(&mut self, rule_id: &str) -> bool {
        self.alert_rules.remove(rule_id).is_some()
    }

    /// Get alert rule by ID
    pub fn get_alert_rule(&self, rule_id: &str) -> Option<&AlertRule> {
        self.alert_rules.get(rule_id)
    }

    /// Get all alert rules
    pub fn get_alert_rules(&self) -> Vec<&AlertRule> {
        self.alert_rules.values().collect()
    }

    /// Evaluate all alert rules
    pub async fn evaluate_alert_rules(&mut self) -> Result<Vec<Alert>, Box<dyn std::error::Error + Send + Sync>> {
        let mut new_alerts = Vec::new();
        let now = Utc::now();

        for rule in self.alert_rules.values_mut() {
            if !rule.enabled {
                continue;
            }

            // Check if it's time to evaluate this rule
            if let Some(last_eval) = rule.last_evaluation {
                let next_eval = last_eval + Duration::seconds(rule.evaluation_interval_seconds as i64);
                if now < next_eval {
                    continue;
                }
            }

            rule.last_evaluation = Some(now);

            // Evaluate the rule condition
            if let Some(alert) = self.evaluate_rule(rule, now).await? {
                new_alerts.push(alert);
            }
        }

        Ok(new_alerts)
    }

    /// Evaluate a single alert rule
    async fn evaluate_rule(&self, rule: &AlertRule, now: DateTime<Utc>) -> Result<Option<Alert>, Box<dyn std::error::Error + Send + Sync>> {
        // Check if we're in a maintenance window
        if self.is_in_maintenance_window(&rule.category, now) {
            return Ok(None);
        }

        // Evaluate based on condition type
        let should_alert = match &rule.condition {
            AlertCondition::Threshold { operator, value } => {
                self.evaluate_threshold_condition(rule, *operator, *value).await?
            }
            AlertCondition::Rate { rate, time_window_seconds } => {
                self.evaluate_rate_condition(rule, *rate, *time_window_seconds).await?
            }
            AlertCondition::Anomaly { algorithm, sensitivity } => {
                self.evaluate_anomaly_condition(rule, algorithm, *sensitivity).await?
            }
            AlertCondition::Custom { expression } => {
                self.evaluate_custom_condition(rule, expression).await?
            }
        };

        if !should_alert {
            return Ok(None);
        }

        // Check if we should respect the "for" duration
        if let Some(last_alert) = rule.last_alert {
            let time_since_last_alert = now.signed_duration_since(last_alert);
            if time_since_last_alert.num_seconds() < rule.for_duration_seconds as i64 {
                return Ok(None);
            }
        }

        // Create the alert
        let alert = self.create_alert_from_rule(rule, now);
        Ok(Some(alert))
    }

    /// Evaluate threshold-based condition
    async fn evaluate_threshold_condition(&self, rule: &AlertRule, operator: ThresholdOperator, threshold: f64) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        // For now, simulate metric evaluation
        // In production, this would query Prometheus or the metrics system
        let current_value = self.get_metric_value(&rule.query).await?;
        Ok(operator.evaluate(current_value, threshold))
    }

    /// Evaluate rate-based condition
    async fn evaluate_rate_condition(&self, rule: &AlertRule, expected_rate: f64, time_window: u64) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        // Calculate rate over the time window
        let rate = self.calculate_rate(&rule.query, time_window).await?;
        Ok(rate > expected_rate)
    }

    /// Evaluate anomaly-based condition
    async fn evaluate_anomaly_condition(&self, rule: &AlertRule, _algorithm: &str, _sensitivity: f64) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        // This would integrate with the anomaly detector
        // For now, return false
        Ok(false)
    }

    /// Evaluate custom condition
    async fn evaluate_custom_condition(&self, rule: &AlertRule, _expression: &str) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        // Evaluate custom expression
        // For now, return false
        Ok(false)
    }

    /// Get metric value (placeholder for actual metric querying)
    async fn get_metric_value(&self, _query: &str) -> Result<f64, Box<dyn std::error::Error + Send + Sync>> {
        // In production, this would query Prometheus or the metrics system
        Ok(0.0)
    }

    /// Calculate rate over time window
    async fn calculate_rate(&self, _query: &str, _time_window: u64) -> Result<f64, Box<dyn std::error::Error + Send + Sync>> {
        // In production, this would calculate rate from metrics
        Ok(0.0)
    }

    /// Create alert from rule
    fn create_alert_from_rule(&self, rule: &AlertRule, now: DateTime<Utc>) -> Alert {
        let fingerprint = self.generate_fingerprint(rule);

        Alert {
            id: Uuid::new_v4().to_string(),
            title: rule.name.clone(),
            description: rule.description.clone(),
            severity: rule.severity,
            category: rule.category.clone(),
            status: AlertStatus::Active,
            source: "alert_rule".to_string(),
            labels: rule.labels.clone(),
            annotations: rule.annotations.clone(),
            value: None,
            threshold: None,
            created_at: now,
            updated_at: now,
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some(rule.id.clone()),
            fingerprint: fingerprint.clone(),
            count: 1,
            last_occurrence: now,
        }
    }

    /// Generate alert fingerprint for deduplication
    fn generate_fingerprint(&self, rule: &AlertRule) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        rule.name.hash(&mut hasher);
        rule.category.as_str().hash(&mut hasher);
        rule.labels.len().hash(&mut hasher); // Simple fingerprint
        format!("{:x}", hasher.finish())
    }

    /// Process alerts (deduplication, aggregation)
    pub fn process_alerts(&mut self, new_alerts: Vec<Alert>) -> Vec<Alert> {
        let mut processed_alerts = Vec::new();

        for alert in new_alerts {
            if self.config.enable_deduplication {
                if let Some(existing_ids) = self.active_alerts.get(&alert.fingerprint) {
                    if !existing_ids.is_empty() {
                        // Update existing alert instead of creating new one
                        let existing_id = existing_ids[0].clone();
                        self.update_existing_alert(&existing_id, &alert);
                        continue;
                    }
                }
            }

            // Add new alert
            self.alerts.insert(alert.id.clone(), alert.clone());
            self.active_alerts.entry(alert.fingerprint.clone())
                .or_insert_with(Vec::new)
                .push(alert.id.clone());
            processed_alerts.push(alert);
        }

        processed_alerts
    }

    /// Update existing alert
    fn update_existing_alert(&mut self, alert_id: &str, new_alert: &Alert) {
        if let Some(existing_alert) = self.alerts.get_mut(alert_id) {
            existing_alert.count += 1;
            existing_alert.last_occurrence = new_alert.last_occurrence;
            existing_alert.updated_at = new_alert.updated_at;
            existing_alert.value = new_alert.value;
        }
    }

    /// Acknowledge alert
    pub fn acknowledge_alert(&mut self, alert_id: &str, user: &str) -> bool {
        if let Some(alert) = self.alerts.get_mut(alert_id) {
            alert.status = AlertStatus::Acknowledged;
            alert.acknowledged_at = Some(Utc::now());
            alert.acknowledged_by = Some(user.to_string());
            alert.updated_at = Utc::now();
            true
        } else {
            false
        }
    }

    /// Resolve alert
    pub fn resolve_alert(&mut self, alert_id: &str) -> bool {
        if let Some(alert) = self.alerts.get_mut(alert_id) {
            alert.status = AlertStatus::Resolved;
            alert.resolved_at = Some(Utc::now());
            alert.updated_at = Utc::now();

            // Remove from active alerts
            if let Some(alert_ids) = self.active_alerts.get_mut(&alert.fingerprint) {
                alert_ids.retain(|id| id != alert_id);
                if alert_ids.is_empty() {
                    self.active_alerts.remove(&alert.fingerprint);
                }
            }

            true
        } else {
            false
        }
    }

    /// Silence alert
    pub fn silence_alert(&mut self, alert_id: &str, duration_minutes: u32) -> bool {
        if let Some(alert) = self.alerts.get_mut(alert_id) {
            alert.status = AlertStatus::Silenced;
            alert.silenced_until = Some(Utc::now() + Duration::minutes(duration_minutes as i64));
            alert.updated_at = Utc::now();
            self.silenced_alerts.insert(alert_id.to_string(), alert.silenced_until.unwrap());
            true
        } else {
            false
        }
    }

    /// Get active alerts
    pub fn get_active_alerts(&self) -> Vec<&Alert> {
        self.alerts.values()
            .filter(|alert| alert.status == AlertStatus::Active)
            .collect()
    }

    /// Get all alerts
    pub fn get_alerts(&self) -> Vec<&Alert> {
        self.alerts.values().collect()
    }

    /// Get alert by ID
    pub fn get_alert(&self, alert_id: &str) -> Option<&Alert> {
        self.alerts.get(alert_id)
    }

    /// Check if in maintenance window
    fn is_in_maintenance_window(&self, category: &AlertCategory, now: DateTime<Utc>) -> bool {
        for window in &self.maintenance_windows {
            if window.affected_components.contains(&category.as_str().to_string()) {
                // Simple check - in production, this would parse cron expressions
                // For now, assume no maintenance windows are active
                return false;
            }
        }
        false
    }

    /// Load default alert rules
    fn load_default_alert_rules(&mut self) {
        // System health alerts
        self.add_alert_rule(AlertRule {
            id: "system_cpu_high".to_string(),
            name: "High CPU Usage".to_string(),
            description: "System CPU usage is above 80%".to_string(),
            enabled: true,
            category: AlertCategory::SystemHealth,
            severity: AlertSeverity::Warning,
            query: "cpu_usage_percent".to_string(),
            condition: AlertCondition::Threshold {
                operator: ThresholdOperator::GreaterThan,
                value: 80.0,
            },
            labels: HashMap::new(),
            annotations: HashMap::new(),
            evaluation_interval_seconds: 60,
            for_duration_seconds: 300,
            last_evaluation: None,
            last_alert: None,
        });

        // Performance alerts
        self.add_alert_rule(AlertRule {
            id: "high_error_rate".to_string(),
            name: "High Error Rate".to_string(),
            description: "Error rate is above 5%".to_string(),
            enabled: true,
            category: AlertCategory::Performance,
            severity: AlertSeverity::Error,
            query: "error_rate_percent".to_string(),
            condition: AlertCondition::Threshold {
                operator: ThresholdOperator::GreaterThan,
                value: 5.0,
            },
            labels: HashMap::new(),
            annotations: HashMap::new(),
            evaluation_interval_seconds: 60,
            for_duration_seconds: 120,
            last_evaluation: None,
            last_alert: None,
        });

        // Security alerts
        self.add_alert_rule(AlertRule {
            id: "unauthorized_access".to_string(),
            name: "Unauthorized Access Attempts".to_string(),
            description: "Multiple unauthorized access attempts detected".to_string(),
            enabled: true,
            category: AlertCategory::Security,
            severity: AlertSeverity::Warning,
            query: "unauthorized_requests_total".to_string(),
            condition: AlertCondition::Rate {
                rate: 10.0,
                time_window_seconds: 300,
            },
            labels: HashMap::new(),
            annotations: HashMap::new(),
            evaluation_interval_seconds: 60,
            for_duration_seconds: 60,
            last_evaluation: None,
            last_alert: None,
        });
    }

    /// Shutdown the alert manager
    pub async fn shutdown(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Clean up resources
        self.alerts.clear();
        self.active_alerts.clear();
        self.silenced_alerts.clear();
        Ok(())
    }
}