//! # Alert Rules Engine
//!
//! Defines and manages alert rules with various condition types
//! and evaluation logic.

use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

use super::{AlertSeverity, AlertCategory};
use super::alert_manager::{AlertCondition, ThresholdOperator};

/// Alert rule configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertRuleConfig {
    pub id: String,
    pub name: String,
    pub description: String,
    pub enabled: bool,
    pub category: AlertCategory,
    pub severity: AlertSeverity,
    pub condition: AlertCondition,
    pub labels: HashMap<String, String>,
    pub annotations: HashMap<String, String>,
    pub evaluation_interval_seconds: u64,
    pub for_duration_seconds: u64,
    pub cooldown_period_seconds: u64,
    pub notification_channels: Vec<String>,
    pub maintenance_mode_override: bool,
}

/// Alert rule engine for managing rule definitions
pub struct AlertRulesEngine {
    rules: HashMap<String, AlertRuleConfig>,
    rule_templates: HashMap<String, AlertRuleTemplate>,
}

impl AlertRulesEngine {
    /// Create a new alert rules engine
    pub fn new() -> Self {
        let mut engine = Self {
            rules: HashMap::new(),
            rule_templates: HashMap::new(),
        };

        engine.load_default_templates();
        engine.load_default_rules();
        engine
    }

    /// Add a custom alert rule
    pub fn add_rule(&mut self, rule: AlertRuleConfig) {
        self.rules.insert(rule.id.clone(), rule);
    }

    /// Remove an alert rule
    pub fn remove_rule(&mut self, rule_id: &str) -> bool {
        self.rules.remove(rule_id).is_some()
    }

    /// Get an alert rule by ID
    pub fn get_rule(&self, rule_id: &str) -> Option<&AlertRuleConfig> {
        self.rules.get(rule_id)
    }

    /// Get all alert rules
    pub fn get_all_rules(&self) -> Vec<&AlertRuleConfig> {
        self.rules.values().collect()
    }

    /// Get enabled alert rules
    pub fn get_enabled_rules(&self) -> Vec<&AlertRuleConfig> {
        self.rules.values()
            .filter(|rule| rule.enabled)
            .collect()
    }

    /// Create rule from template
    pub fn create_rule_from_template(&mut self, template_id: &str, parameters: HashMap<String, String>) -> Option<AlertRuleConfig> {
        let template = self.rule_templates.get(template_id)?;

        let mut rule = AlertRuleConfig {
            id: format!("{}_{}", template_id, Utc::now().timestamp()),
            name: template.name.clone(),
            description: template.description.clone(),
            enabled: true,
            category: template.category,
            severity: template.severity,
            condition: template.condition.clone(),
            labels: template.labels.clone(),
            annotations: template.annotations.clone(),
            evaluation_interval_seconds: template.evaluation_interval_seconds,
            for_duration_seconds: template.for_duration_seconds,
            cooldown_period_seconds: template.cooldown_period_seconds,
            notification_channels: template.notification_channels.clone(),
            maintenance_mode_override: template.maintenance_mode_override,
        };

        // Apply parameters
        for (key, value) in parameters {
            match key.as_str() {
                "threshold" => {
                    if let AlertCondition::Threshold { ref mut value, .. } = rule.condition {
                        *value = value.parse().unwrap_or(*value);
                    }
                }
                "metric_name" => {
                    rule.labels.insert("metric".to_string(), value);
                }
                "service_name" => {
                    rule.labels.insert("service".to_string(), value);
                }
                _ => {}
            }
        }

        Some(rule)
    }

    /// Validate alert rule
    pub fn validate_rule(&self, rule: &AlertRuleConfig) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();

        if rule.name.is_empty() {
            errors.push("Rule name cannot be empty".to_string());
        }

        if rule.evaluation_interval_seconds == 0 {
            errors.push("Evaluation interval must be greater than 0".to_string());
        }

        match &rule.condition {
            AlertCondition::Threshold { value, .. } => {
                if *value <= 0.0 {
                    errors.push("Threshold value must be greater than 0".to_string());
                }
            }
            AlertCondition::Rate { rate, time_window_seconds } => {
                if *rate <= 0.0 {
                    errors.push("Rate must be greater than 0".to_string());
                }
                if *time_window_seconds == 0 {
                    errors.push("Time window must be greater than 0".to_string());
                }
            }
            AlertCondition::Anomaly { sensitivity, .. } => {
                if *sensitivity <= 0.0 || *sensitivity > 1.0 {
                    errors.push("Sensitivity must be between 0 and 1".to_string());
                }
            }
            AlertCondition::Custom { expression } => {
                if expression.is_empty() {
                    errors.push("Custom expression cannot be empty".to_string());
                }
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }

    /// Get rule templates
    pub fn get_rule_templates(&self) -> Vec<&AlertRuleTemplate> {
        self.rule_templates.values().collect()
    }

    /// Load default rule templates
    fn load_default_templates(&mut self) {
        // System health templates
        self.rule_templates.insert(
            "cpu_usage_high".to_string(),
            AlertRuleTemplate {
                id: "cpu_usage_high".to_string(),
                name: "High CPU Usage".to_string(),
                description: "CPU usage is above threshold".to_string(),
                category: AlertCategory::SystemHealth,
                severity: AlertSeverity::Warning,
                condition: AlertCondition::Threshold {
                    operator: ThresholdOperator::GreaterThan,
                    value: 80.0,
                },
                labels: HashMap::from([
                    ("resource".to_string(), "cpu".to_string()),
                ]),
                annotations: HashMap::from([
                    ("summary".to_string(), "High CPU usage detected".to_string()),
                    ("description".to_string(), "CPU usage is above {{threshold}}%".to_string()),
                ]),
                evaluation_interval_seconds: 60,
                for_duration_seconds: 300,
                cooldown_period_seconds: 600,
                notification_channels: vec!["email".to_string(), "slack".to_string()],
                maintenance_mode_override: false,
            },
        );

        self.rule_templates.insert(
            "memory_usage_high".to_string(),
            AlertRuleTemplate {
                id: "memory_usage_high".to_string(),
                name: "High Memory Usage".to_string(),
                description: "Memory usage is above threshold".to_string(),
                category: AlertCategory::SystemHealth,
                severity: AlertSeverity::Warning,
                condition: AlertCondition::Threshold {
                    operator: ThresholdOperator::GreaterThan,
                    value: 85.0,
                },
                labels: HashMap::from([
                    ("resource".to_string(), "memory".to_string()),
                ]),
                annotations: HashMap::from([
                    ("summary".to_string(), "High memory usage detected".to_string()),
                    ("description".to_string(), "Memory usage is above {{threshold}}%".to_string()),
                ]),
                evaluation_interval_seconds: 60,
                for_duration_seconds: 300,
                cooldown_period_seconds: 600,
                notification_channels: vec!["email".to_string(), "slack".to_string()],
                maintenance_mode_override: false,
            },
        );

        // Performance templates
        self.rule_templates.insert(
            "high_error_rate".to_string(),
            AlertRuleTemplate {
                id: "high_error_rate".to_string(),
                name: "High Error Rate".to_string(),
                description: "Error rate is above threshold".to_string(),
                category: AlertCategory::Performance,
                severity: AlertSeverity::Error,
                condition: AlertCondition::Threshold {
                    operator: ThresholdOperator::GreaterThan,
                    value: 5.0,
                },
                labels: HashMap::from([
                    ("metric".to_string(), "error_rate".to_string()),
                ]),
                annotations: HashMap::from([
                    ("summary".to_string(), "High error rate detected".to_string()),
                    ("description".to_string(), "Error rate is {{value}}%".to_string()),
                ]),
                evaluation_interval_seconds: 60,
                for_duration_seconds: 120,
                cooldown_period_seconds: 300,
                notification_channels: vec!["email".to_string(), "slack".to_string(), "pagerduty".to_string()],
                maintenance_mode_override: false,
            },
        );

        self.rule_templates.insert(
            "high_latency".to_string(),
            AlertRuleTemplate {
                id: "high_latency".to_string(),
                name: "High Latency".to_string(),
                description: "Request latency is above threshold".to_string(),
                category: AlertCategory::Performance,
                severity: AlertSeverity::Warning,
                condition: AlertCondition::Threshold {
                    operator: ThresholdOperator::GreaterThan,
                    value: 2000.0,
                },
                labels: HashMap::from([
                    ("metric".to_string(), "latency".to_string()),
                ]),
                annotations: HashMap::from([
                    ("summary".to_string(), "High latency detected".to_string()),
                    ("description".to_string(), "95th percentile latency is {{value}}ms".to_string()),
                ]),
                evaluation_interval_seconds: 60,
                for_duration_seconds: 300,
                cooldown_period_seconds: 600,
                notification_channels: vec!["email".to_string(), "slack".to_string()],
                maintenance_mode_override: false,
            },
        );

        // Security templates
        self.rule_templates.insert(
            "unauthorized_access".to_string(),
            AlertRuleTemplate {
                id: "unauthorized_access".to_string(),
                name: "Unauthorized Access Attempts".to_string(),
                description: "Multiple unauthorized access attempts detected".to_string(),
                category: AlertCategory::Security,
                severity: AlertSeverity::Warning,
                condition: AlertCondition::Rate {
                    rate: 10.0,
                    time_window_seconds: 300,
                },
                labels: HashMap::from([
                    ("type".to_string(), "security".to_string()),
                ]),
                annotations: HashMap::from([
                    ("summary".to_string(), "Unauthorized access attempts detected".to_string()),
                    ("description".to_string(), "Multiple unauthorized access attempts in the last 5 minutes".to_string()),
                ]),
                evaluation_interval_seconds: 60,
                for_duration_seconds: 60,
                cooldown_period_seconds: 300,
                notification_channels: vec!["email".to_string(), "slack".to_string(), "pagerduty".to_string()],
                maintenance_mode_override: true,
            },
        );

        // Compliance templates
        self.rule_templates.insert(
            "license_scan_failure".to_string(),
            AlertRuleTemplate {
                id: "license_scan_failure".to_string(),
                name: "License Scan Failure".to_string(),
                description: "License scanning process has failed".to_string(),
                category: AlertCategory::Compliance,
                severity: AlertSeverity::Error,
                condition: AlertCondition::Threshold {
                    operator: ThresholdOperator::GreaterThan,
                    value: 0.0,
                },
                labels: HashMap::from([
                    ("component".to_string(), "license_scanner".to_string()),
                ]),
                annotations: HashMap::from([
                    ("summary".to_string(), "License scan failure".to_string()),
                    ("description".to_string(), "License scanning process has failed".to_string()),
                ]),
                evaluation_interval_seconds: 300,
                for_duration_seconds: 0,
                cooldown_period_seconds: 1800,
                notification_channels: vec!["email".to_string(), "slack".to_string()],
                maintenance_mode_override: false,
            },
        );
    }

    /// Load default alert rules
    fn load_default_rules(&mut self) {
        // Create some default rules from templates
        if let Some(rule) = self.create_rule_from_template("cpu_usage_high", HashMap::new()) {
            self.add_rule(rule);
        }

        if let Some(rule) = self.create_rule_from_template("memory_usage_high", HashMap::new()) {
            self.add_rule(rule);
        }

        if let Some(rule) = self.create_rule_from_template("high_error_rate", HashMap::new()) {
            self.add_rule(rule);
        }

        if let Some(rule) = self.create_rule_from_template("unauthorized_access", HashMap::new()) {
            self.add_rule(rule);
        }
    }
}

/// Alert rule template
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertRuleTemplate {
    pub id: String,
    pub name: String,
    pub description: String,
    pub category: AlertCategory,
    pub severity: AlertSeverity,
    pub condition: AlertCondition,
    pub labels: HashMap<String, String>,
    pub annotations: HashMap<String, String>,
    pub evaluation_interval_seconds: u64,
    pub for_duration_seconds: u64,
    pub cooldown_period_seconds: u64,
    pub notification_channels: Vec<String>,
    pub maintenance_mode_override: bool,
}

/// Rule evaluation context
#[derive(Debug, Clone)]
pub struct RuleEvaluationContext {
    pub timestamp: DateTime<Utc>,
    pub metric_values: HashMap<String, f64>,
    pub labels: HashMap<String, String>,
    pub annotations: HashMap<String, String>,
}

impl RuleEvaluationContext {
    pub fn new() -> Self {
        Self {
            timestamp: Utc::now(),
            metric_values: HashMap::new(),
            labels: HashMap::new(),
            annotations: HashMap::new(),
        }
    }

    pub fn with_metric(mut self, name: &str, value: f64) -> Self {
        self.metric_values.insert(name.to_string(), value);
        self
    }

    pub fn with_label(mut self, key: &str, value: &str) -> Self {
        self.labels.insert(key.to_string(), value.to_string());
        self
    }

    pub fn with_annotation(mut self, key: &str, value: &str) -> Self {
        self.annotations.insert(key.to_string(), value.to_string());
        self
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_rule_engine_creation() {
        let engine = AlertRulesEngine::new();
        assert!(!engine.rules.is_empty());
        assert!(!engine.rule_templates.is_empty());
    }

    #[test]
    fn test_rule_validation() {
        let engine = AlertRulesEngine::new();

        // Valid rule
        let valid_rule = AlertRuleConfig {
            id: "test_rule".to_string(),
            name: "Test Rule".to_string(),
            description: "Test rule description".to_string(),
            enabled: true,
            category: AlertCategory::SystemHealth,
            severity: AlertSeverity::Warning,
            condition: AlertCondition::Threshold {
                operator: ThresholdOperator::GreaterThan,
                value: 80.0,
            },
            labels: HashMap::new(),
            annotations: HashMap::new(),
            evaluation_interval_seconds: 60,
            for_duration_seconds: 300,
            cooldown_period_seconds: 600,
            notification_channels: vec!["email".to_string()],
            maintenance_mode_override: false,
        };

        assert!(engine.validate_rule(&valid_rule).is_ok());

        // Invalid rule - empty name
        let invalid_rule = AlertRuleConfig {
            name: "".to_string(),
            ..valid_rule
        };

        assert!(engine.validate_rule(&invalid_rule).is_err());
    }

    #[test]
    fn test_create_rule_from_template() {
        let mut engine = AlertRulesEngine::new();

        let parameters = HashMap::from([
            ("threshold".to_string(), "90.0".to_string()),
            ("service_name".to_string(), "test-service".to_string()),
        ]);

        let rule = engine.create_rule_from_template("cpu_usage_high", parameters);
        assert!(rule.is_some());

        let rule = rule.unwrap();
        assert_eq!(rule.labels.get("service"), Some(&"test-service".to_string()));

        if let AlertCondition::Threshold { value, .. } = rule.condition {
            assert_eq!(value, 90.0);
        } else {
            panic!("Expected threshold condition");
        }
    }
}