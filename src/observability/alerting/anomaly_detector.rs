//! # Anomaly Detection Engine
//!
//! Implements statistical and machine learning-based anomaly detection
//! for metrics, logs, and performance data.

use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, Duration};

/// Anomaly detection configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnomalyDetectionConfig {
    pub enabled: bool,
    pub algorithms: Vec<AnomalyAlgorithm>,
    pub sensitivity: f64,
    pub training_window_hours: u32,
    pub detection_window_minutes: u32,
    pub min_data_points: usize,
    pub seasonal_analysis: bool,
    pub custom_thresholds: HashMap<String, f64>,
}

impl Default for AnomalyDetectionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            algorithms: vec![
                AnomalyAlgorithm::ZScore,
                AnomalyAlgorithm::MovingAverage,
                AnomalyAlgorithm::SeasonalDecomposition,
            ],
            sensitivity: 3.0, // 3-sigma rule
            training_window_hours: 24,
            detection_window_minutes: 5,
            min_data_points: 100,
            seasonal_analysis: true,
            custom_thresholds: HashMap::new(),
        }
    }
}

/// Anomaly detection algorithms
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AnomalyAlgorithm {
    ZScore,
    MovingAverage,
    SeasonalDecomposition,
    IsolationForest,
    OneClassSVM,
    Custom,
}

impl AnomalyAlgorithm {
    pub fn as_str(&self) -> &'static str {
        match self {
            AnomalyAlgorithm::ZScore => "zscore",
            AnomalyAlgorithm::MovingAverage => "moving_average",
            AnomalyAlgorithm::SeasonalDecomposition => "seasonal_decomposition",
            AnomalyAlgorithm::IsolationForest => "isolation_forest",
            AnomalyAlgorithm::OneClassSVM => "one_class_svm",
            AnomalyAlgorithm::Custom => "custom",
        }
    }
}

/// Anomaly detection result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnomalyResult {
    pub metric_name: String,
    pub timestamp: DateTime<Utc>,
    pub value: f64,
    pub expected_value: f64,
    pub deviation: f64,
    pub confidence: f64,
    pub algorithm: AnomalyAlgorithm,
    pub severity: AnomalySeverity,
    pub context: HashMap<String, String>,
}

impl AnomalyResult {
    pub fn is_anomaly(&self) -> bool {
        matches!(self.severity, AnomalySeverity::Low | AnomalySeverity::Medium | AnomalySeverity::High | AnomalySeverity::Critical)
    }
}

/// Anomaly severity levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AnomalySeverity {
    Normal,
    Low,
    Medium,
    High,
    Critical,
}

impl AnomalySeverity {
    pub fn as_str(&self) -> &'static str {
        match self {
            AnomalySeverity::Normal => "normal",
            AnomalySeverity::Low => "low",
            AnomalySeverity::Medium => "medium",
            AnomalySeverity::High => "high",
            AnomalySeverity::Critical => "critical",
        }
    }
}

/// Time series data point
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataPoint {
    pub timestamp: DateTime<Utc>,
    pub value: f64,
    pub labels: HashMap<String, String>,
}

/// Metric time series
#[derive(Debug, Clone)]
pub struct MetricTimeSeries {
    pub name: String,
    pub data_points: VecDeque<DataPoint>,
    pub max_points: usize,
    pub last_update: DateTime<Utc>,
}

impl MetricTimeSeries {
    pub fn new(name: String, max_points: usize) -> Self {
        Self {
            name,
            data_points: VecDeque::with_capacity(max_points),
            max_points,
            last_update: Utc::now(),
        }
    }

    pub fn add_point(&mut self, point: DataPoint) {
        if self.data_points.len() >= self.max_points {
            self.data_points.pop_front();
        }
        self.data_points.push_back(point);
        self.last_update = Utc::now();
    }

    pub fn get_recent_points(&self, hours: u32) -> Vec<&DataPoint> {
        let cutoff = Utc::now() - Duration::hours(hours as i64);
        self.data_points.iter()
            .filter(|p| p.timestamp >= cutoff)
            .collect()
    }

    pub fn get_values(&self) -> Vec<f64> {
        self.data_points.iter().map(|p| p.value).collect()
    }

    pub fn get_timestamps(&self) -> Vec<DateTime<Utc>> {
        self.data_points.iter().map(|p| p.timestamp).collect()
    }
}

/// Statistical anomaly detector using Z-score
pub struct ZScoreDetector {
    config: AnomalyDetectionConfig,
}

impl ZScoreDetector {
    pub fn new(config: AnomalyDetectionConfig) -> Self {
        Self { config }
    }

    pub fn detect_anomalies(&self, series: &MetricTimeSeries, new_value: f64, timestamp: DateTime<Utc>) -> Vec<AnomalyResult> {
        let values = series.get_values();
        if values.len() < self.config.min_data_points {
            return vec![];
        }

        let mean = values.iter().sum::<f64>() / values.len() as f64;
        let variance = values.iter()
            .map(|v| (v - mean).powi(2))
            .sum::<f64>() / values.len() as f64;
        let std_dev = variance.sqrt();

        if std_dev == 0.0 {
            return vec![];
        }

        let z_score = (new_value - mean) / std_dev;
        let deviation = z_score.abs();

        let severity = if deviation >= self.config.sensitivity * 3.0 {
            AnomalySeverity::Critical
        } else if deviation >= self.config.sensitivity * 2.0 {
            AnomalySeverity::High
        } else if deviation >= self.config.sensitivity * 1.5 {
            AnomalySeverity::Medium
        } else if deviation >= self.config.sensitivity {
            AnomalySeverity::Low
        } else {
            AnomalySeverity::Normal
        };

        if severity == AnomalySeverity::Normal {
            return vec![];
        }

        vec![AnomalyResult {
            metric_name: series.name.clone(),
            timestamp,
            value: new_value,
            expected_value: mean,
            deviation,
            confidence: 1.0 - (deviation / (self.config.sensitivity * 3.0)).min(1.0),
            algorithm: AnomalyAlgorithm::ZScore,
            severity,
            context: HashMap::from([
                ("z_score".to_string(), z_score.to_string()),
                ("mean".to_string(), mean.to_string()),
                ("std_dev".to_string(), std_dev.to_string()),
            ]),
        }]
    }
}

/// Moving average anomaly detector
pub struct MovingAverageDetector {
    config: AnomalyDetectionConfig,
    window_size: usize,
}

impl MovingAverageDetector {
    pub fn new(config: AnomalyDetectionConfig, window_size: usize) -> Self {
        Self {
            config,
            window_size,
        }
    }

    pub fn detect_anomalies(&self, series: &MetricTimeSeries, new_value: f64, timestamp: DateTime<Utc>) -> Vec<AnomalyResult> {
        let values = series.get_values();
        if values.len() < self.window_size + self.config.min_data_points {
            return vec![];
        }

        // Calculate moving average of recent values
        let recent_values: Vec<f64> = values.iter().rev().take(self.window_size).cloned().collect();
        let moving_avg = recent_values.iter().sum::<f64>() / recent_values.len() as f64;

        // Calculate standard deviation of recent values
        let variance = recent_values.iter()
            .map(|v| (v - moving_avg).powi(2))
            .sum::<f64>() / recent_values.len() as f64;
        let std_dev = variance.sqrt();

        if std_dev == 0.0 {
            return vec![];
        }

        let deviation = (new_value - moving_avg).abs() / std_dev;

        let severity = if deviation >= self.config.sensitivity * 3.0 {
            AnomalySeverity::Critical
        } else if deviation >= self.config.sensitivity * 2.0 {
            AnomalySeverity::High
        } else if deviation >= self.config.sensitivity * 1.5 {
            AnomalySeverity::Medium
        } else if deviation >= self.config.sensitivity {
            AnomalySeverity::Low
        } else {
            AnomalySeverity::Normal
        };

        if severity == AnomalySeverity::Normal {
            return vec![];
        }

        vec![AnomalyResult {
            metric_name: series.name.clone(),
            timestamp,
            value: new_value,
            expected_value: moving_avg,
            deviation,
            confidence: 1.0 - (deviation / (self.config.sensitivity * 3.0)).min(1.0),
            algorithm: AnomalyAlgorithm::MovingAverage,
            severity,
            context: HashMap::from([
                ("moving_average".to_string(), moving_avg.to_string()),
                ("window_size".to_string(), self.window_size.to_string()),
                ("std_dev".to_string(), std_dev.to_string()),
            ]),
        }]
    }
}

/// Seasonal decomposition anomaly detector
pub struct SeasonalDecompositionDetector {
    config: AnomalyDetectionConfig,
    season_length: usize,
}

impl SeasonalDecompositionDetector {
    pub fn new(config: AnomalyDetectionConfig, season_length: usize) -> Self {
        Self {
            config,
            season_length,
        }
    }

    pub fn detect_anomalies(&self, series: &MetricTimeSeries, new_value: f64, timestamp: DateTime<Utc>) -> Vec<AnomalyResult> {
        let values = series.get_values();
        if values.len() < self.season_length * 2 + self.config.min_data_points {
            return vec![];
        }

        // Simple seasonal decomposition (trend + seasonal + residual)
        let seasonal_component = self.calculate_seasonal_component(&values);
        let trend_component = self.calculate_trend_component(&values);
        let residual = new_value - seasonal_component - trend_component;

        // Calculate residual standard deviation
        let residuals: Vec<f64> = values.iter().enumerate()
            .map(|(i, &v)| {
                let seasonal = if i >= self.season_length {
                    values[i - self.season_length]
                } else {
                    v
                };
                v - seasonal - trend_component
            })
            .collect();

        let residual_mean = residuals.iter().sum::<f64>() / residuals.len() as f64;
        let residual_std = (residuals.iter()
            .map(|r| (r - residual_mean).powi(2))
            .sum::<f64>() / residuals.len() as f64).sqrt();

        if residual_std == 0.0 {
            return vec![];
        }

        let deviation = residual.abs() / residual_std;

        let severity = if deviation >= self.config.sensitivity * 3.0 {
            AnomalySeverity::Critical
        } else if deviation >= self.config.sensitivity * 2.0 {
            AnomalySeverity::High
        } else if deviation >= self.config.sensitivity * 1.5 {
            AnomalySeverity::Medium
        } else if deviation >= self.config.sensitivity {
            AnomalySeverity::Low
        } else {
            AnomalySeverity::Normal
        };

        if severity == AnomalySeverity::Normal {
            return vec![];
        }

        vec![AnomalyResult {
            metric_name: series.name.clone(),
            timestamp,
            value: new_value,
            expected_value: seasonal_component + trend_component,
            deviation,
            confidence: 1.0 - (deviation / (self.config.sensitivity * 3.0)).min(1.0),
            algorithm: AnomalyAlgorithm::SeasonalDecomposition,
            severity,
            context: HashMap::from([
                ("seasonal_component".to_string(), seasonal_component.to_string()),
                ("trend_component".to_string(), trend_component.to_string()),
                ("residual".to_string(), residual.to_string()),
                ("residual_std".to_string(), residual_std.to_string()),
            ]),
        }]
    }

    fn calculate_seasonal_component(&self, values: &[f64]) -> f64 {
        if values.len() < self.season_length {
            return 0.0;
        }

        // Simple average of corresponding seasonal values
        let seasonal_index = values.len() % self.season_length;
        let seasonal_values: Vec<f64> = values.iter()
            .enumerate()
            .filter(|(i, _)| i % self.season_length == seasonal_index)
            .map(|(_, &v)| v)
            .collect();

        seasonal_values.iter().sum::<f64>() / seasonal_values.len() as f64
    }

    fn calculate_trend_component(&self, values: &[f64]) -> f64 {
        if values.len() < 2 {
            return values.last().copied().unwrap_or(0.0);
        }

        // Simple linear trend
        let n = values.len() as f64;
        let x_sum: f64 = (0..values.len()).map(|i| i as f64).sum();
        let y_sum: f64 = values.iter().sum();
        let xy_sum: f64 = values.iter().enumerate()
            .map(|(i, &v)| i as f64 * v)
            .sum();
        let x_squared_sum: f64 = (0..values.len()).map(|i| (i as f64).powi(2)).sum();

        let slope = (n * xy_sum - x_sum * y_sum) / (n * x_squared_sum - x_sum.powi(2));
        let intercept = (y_sum - slope * x_sum) / n;

        slope * (values.len() - 1) as f64 + intercept
    }
}

/// Main anomaly detector
pub struct AnomalyDetector {
    config: AnomalyDetectionConfig,
    time_series: HashMap<String, MetricTimeSeries>,
    zscore_detector: ZScoreDetector,
    moving_avg_detector: MovingAverageDetector,
    seasonal_detector: SeasonalDecompositionDetector,
}

impl AnomalyDetector {
    /// Create a new anomaly detector
    pub fn new(config: AnomalyDetectionConfig) -> Self {
        let zscore_detector = ZScoreDetector::new(config.clone());
        let moving_avg_detector = MovingAverageDetector::new(config.clone(), 20); // 20-point moving average
        let seasonal_detector = SeasonalDecompositionDetector::new(config.clone(), 24); // 24-point seasonality

        Self {
            config,
            time_series: HashMap::new(),
            zscore_detector,
            moving_avg_detector,
            seasonal_detector,
        }
    }

    /// Initialize the anomaly detector
    pub async fn initialize(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Initialize time series storage
        // In production, this would load historical data
        Ok(())
    }

    /// Add metric data point
    pub fn add_metric_data(&mut self, metric_name: &str, value: f64, timestamp: DateTime<Utc>, labels: HashMap<String, String>) {
        let series = self.time_series.entry(metric_name.to_string())
            .or_insert_with(|| MetricTimeSeries::new(metric_name.to_string(), 1000));

        let point = DataPoint {
            timestamp,
            value,
            labels,
        };

        series.add_point(point);
    }

    /// Detect anomalies for a metric
    pub fn detect_anomalies(&self, metric_name: &str, value: f64, timestamp: DateTime<Utc>) -> Vec<AnomalyResult> {
        let series = match self.time_series.get(metric_name) {
            Some(s) => s,
            None => return vec![],
        };

        let mut all_anomalies = Vec::new();

        // Run all enabled algorithms
        for algorithm in &self.config.algorithms {
            let anomalies = match algorithm {
                AnomalyAlgorithm::ZScore => {
                    self.zscore_detector.detect_anomalies(series, value, timestamp)
                }
                AnomalyAlgorithm::MovingAverage => {
                    self.moving_avg_detector.detect_anomalies(series, value, timestamp)
                }
                AnomalyAlgorithm::SeasonalDecomposition => {
                    self.seasonal_detector.detect_anomalies(series, value, timestamp)
                }
                _ => vec![], // Other algorithms not implemented yet
            };

            all_anomalies.extend(anomalies);
        }

        // Remove duplicates and conflicting results
        self.deduplicate_anomalies(all_anomalies)
    }

    /// Detect anomalies for all metrics
    pub fn detect_all_anomalies(&self) -> HashMap<String, Vec<AnomalyResult>> {
        let mut results = HashMap::new();

        for (metric_name, series) in &self.time_series {
            if let Some(latest_point) = series.data_points.back() {
                let anomalies = self.detect_anomalies(metric_name, latest_point.value, latest_point.timestamp);
                if !anomalies.is_empty() {
                    results.insert(metric_name.clone(), anomalies);
                }
            }
        }

        results
    }

    /// Deduplicate anomaly results
    fn deduplicate_anomalies(&self, anomalies: Vec<AnomalyResult>) -> Vec<AnomalyResult> {
        if anomalies.is_empty() {
            return vec![];
        }

        // Group by timestamp and metric
        let mut grouped: HashMap<(String, DateTime<Utc>), Vec<AnomalyResult>> = HashMap::new();

        for anomaly in anomalies {
            let key = (anomaly.metric_name.clone(), anomaly.timestamp);
            grouped.entry(key).or_insert_with(Vec::new).push(anomaly);
        }

        // For each group, select the most severe anomaly
        let mut deduplicated = Vec::new();

        for (_, group_anomalies) in grouped {
            if let Some(most_severe) = group_anomalies.into_iter()
                .max_by_key(|a| match a.severity {
                    AnomalySeverity::Normal => 0,
                    AnomalySeverity::Low => 1,
                    AnomalySeverity::Medium => 2,
                    AnomalySeverity::High => 3,
                    AnomalySeverity::Critical => 4,
                }) {
                deduplicated.push(most_severe);
            }
        }

        deduplicated
    }

    /// Get time series for a metric
    pub fn get_time_series(&self, metric_name: &str) -> Option<&MetricTimeSeries> {
        self.time_series.get(metric_name)
    }

    /// Get all metric names
    pub fn get_metric_names(&self) -> Vec<String> {
        self.time_series.keys().cloned().collect()
    }

    /// Clear old data points
    pub fn cleanup_old_data(&mut self, max_age_hours: u32) {
        let cutoff = Utc::now() - Duration::hours(max_age_hours as i64);

        for series in self.time_series.values_mut() {
            while let Some(point) = series.data_points.front() {
                if point.timestamp < cutoff {
                    series.data_points.pop_front();
                } else {
                    break;
                }
            }
        }
    }

    /// Shutdown the anomaly detector
    pub async fn shutdown(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Clean up resources
        self.time_series.clear();
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_zscore_detector_normal_values() {
        let config = AnomalyDetectionConfig::default();
        let detector = ZScoreDetector::new(config);
        let mut series = MetricTimeSeries::new("test_metric".to_string(), 1000);

        // Add normal values
        for i in 0..150 {
            let value = 100.0 + (i as f64 * 0.1); // Slightly increasing trend
            series.add_point(DataPoint {
                timestamp: Utc::now(),
                value,
                labels: HashMap::new(),
            });
        }

        let anomalies = detector.detect_anomalies(&series, 115.0, Utc::now());
        assert!(anomalies.is_empty() || anomalies.iter().all(|a| !a.is_anomaly()));
    }

    #[test]
    fn test_zscore_detector_anomaly() {
        let config = AnomalyDetectionConfig::default();
        let detector = ZScoreDetector::new(config);
        let mut series = MetricTimeSeries::new("test_metric".to_string(), 1000);

        // Add normal values around 100
        for _ in 0..150 {
            series.add_point(DataPoint {
                timestamp: Utc::now(),
                value: 100.0 + (rand::random::<f64>() - 0.5) * 10.0, // Normal variation
                labels: HashMap::new(),
            });
        }

        // Add clear anomaly
        let anomalies = detector.detect_anomalies(&series, 200.0, Utc::now());
        assert!(!anomalies.is_empty());
        assert!(anomalies[0].is_anomaly());
    }
}