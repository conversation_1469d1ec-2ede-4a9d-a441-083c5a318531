//! # Compliance Monitor
//!
//! Monitors license compliance metrics and generates alerts based on
//! license detection accuracy, compliance violations, and system performance.

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, Duration};

use super::{<PERSON>ert, AlertSeverity, AlertStatus, AlertCategory, AlertingFramework};

/// Compliance monitor configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceMonitorConfig {
    pub enabled: bool,
    pub monitoring_interval_seconds: u64,
    pub license_accuracy_warning_threshold: f64,
    pub license_accuracy_critical_threshold: f64,
    pub compliance_violation_threshold: u32,
    pub ml_model_accuracy_warning_threshold: f64,
    pub ml_model_accuracy_critical_threshold: f64,
    pub database_update_failure_threshold: u32,
    pub license_scan_failure_threshold: u32,
    pub stale_data_threshold_hours: u32,
    pub enable_business_rule_alerts: bool,
    pub custom_compliance_rules: Vec<CustomComplianceRule>,
}

impl Default for ComplianceMonitorConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            monitoring_interval_seconds: 300, // 5 minutes
            license_accuracy_warning_threshold: 0.85,
            license_accuracy_critical_threshold: 0.75,
            compliance_violation_threshold: 10,
            ml_model_accuracy_warning_threshold: 0.80,
            ml_model_accuracy_critical_threshold: 0.70,
            database_update_failure_threshold: 3,
            license_scan_failure_threshold: 5,
            stale_data_threshold_hours: 24,
            enable_business_rule_alerts: true,
            custom_compliance_rules: Vec::new(),
        }
    }
}

/// Custom compliance rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CustomComplianceRule {
    pub id: String,
    pub name: String,
    pub description: String,
    pub metric_query: String,
    pub operator: String, // "gt", "lt", "eq", "contains", etc.
    pub threshold: String,
    pub severity: AlertSeverity,
    pub enabled: bool,
}

/// Compliance metrics snapshot
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceMetrics {
    pub timestamp: DateTime<Utc>,
    pub license_detection_accuracy: f64,
    pub total_license_predictions: u64,
    pub correct_predictions: u64,
    pub false_positives: u64,
    pub false_negatives: u64,
    pub compliance_violations: u64,
    pub active_violations: u64,
    pub resolved_violations: u64,
    pub ml_model_accuracy: f64,
    pub ml_model_version: String,
    pub database_update_failures: u64,
    pub license_scan_failures: u64,
    pub successful_scans: u64,
    pub total_scans: u64,
    pub data_freshness_hours: f64,
    pub license_database_size: u64,
    pub last_database_update: Option<DateTime<Utc>>,
    pub custom_metrics: HashMap<String, f64>,
}

/// Compliance monitor
pub struct ComplianceMonitor {
    config: ComplianceMonitorConfig,
    metrics_history: Vec<ComplianceMetrics>,
    max_history_size: usize,
    alerting_framework: Option<Arc<RwLock<AlertingFramework>>>,
}

impl ComplianceMonitor {
    /// Create a new compliance monitor
    pub fn new(config: ComplianceMonitorConfig) -> Self {
        Self {
            config,
            metrics_history: Vec::new(),
            max_history_size: 1000,
            alerting_framework: None,
        }
    }

    /// Set alerting framework reference
    pub fn set_alerting_framework(&mut self, framework: Arc<RwLock<AlertingFramework>>) {
        self.alerting_framework = Some(framework);
    }

    /// Record compliance metrics
    pub async fn record_metrics(&mut self, metrics: ComplianceMetrics) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Store metrics in history
        if self.metrics_history.len() >= self.max_history_size {
            self.metrics_history.remove(0);
        }
        self.metrics_history.push(metrics.clone());

        // Analyze metrics and generate alerts
        self.analyze_and_alert(metrics).await?;

        Ok(())
    }

    /// Analyze metrics and generate alerts
    async fn analyze_and_alert(&self, metrics: ComplianceMetrics) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if let Some(ref framework) = self.alerting_framework {
            let mut alerts = Vec::new();

            // License detection accuracy alerts
            if metrics.license_detection_accuracy < self.config.license_accuracy_critical_threshold {
                alerts.push(self.create_accuracy_alert(
                    "Critical License Detection Accuracy",
                    format!("License detection accuracy dropped to {:.1}% (threshold: {:.1}%)", metrics.license_detection_accuracy * 100.0, self.config.license_accuracy_critical_threshold * 100.0),
                    AlertSeverity::Critical,
                    metrics.license_detection_accuracy,
                ));
            } else if metrics.license_detection_accuracy < self.config.license_accuracy_warning_threshold {
                alerts.push(self.create_accuracy_alert(
                    "Low License Detection Accuracy",
                    format!("License detection accuracy is {:.1}% (threshold: {:.1}%)", metrics.license_detection_accuracy * 100.0, self.config.license_accuracy_warning_threshold * 100.0),
                    AlertSeverity::Warning,
                    metrics.license_detection_accuracy,
                ));
            }

            // Compliance violation alerts
            if metrics.active_violations >= self.config.compliance_violation_threshold {
                alerts.push(self.create_violation_alert(
                    "High Number of Compliance Violations",
                    format!("{} active compliance violations detected", metrics.active_violations),
                    AlertSeverity::Critical,
                    metrics.active_violations,
                ));
            }

            // ML model accuracy alerts
            if metrics.ml_model_accuracy < self.config.ml_model_accuracy_critical_threshold {
                alerts.push(self.create_ml_accuracy_alert(
                    "Critical ML Model Accuracy",
                    format!("ML model '{}' accuracy dropped to {:.1}% (threshold: {:.1}%)", metrics.ml_model_version, metrics.ml_model_accuracy * 100.0, self.config.ml_model_accuracy_critical_threshold * 100.0),
                    AlertSeverity::Critical,
                    metrics.ml_model_accuracy,
                    &metrics.ml_model_version,
                ));
            } else if metrics.ml_model_accuracy < self.config.ml_model_accuracy_warning_threshold {
                alerts.push(self.create_ml_accuracy_alert(
                    "Low ML Model Accuracy",
                    format!("ML model '{}' accuracy is {:.1}% (threshold: {:.1}%)", metrics.ml_model_version, metrics.ml_model_accuracy * 100.0, self.config.ml_model_accuracy_warning_threshold * 100.0),
                    AlertSeverity::Warning,
                    metrics.ml_model_accuracy,
                    &metrics.ml_model_version,
                ));
            }

            // Database update failure alerts
            if metrics.database_update_failures >= self.config.database_update_failure_threshold {
                alerts.push(self.create_database_alert(
                    "Database Update Failures",
                    format!("{} consecutive database update failures", metrics.database_update_failures),
                    AlertSeverity::Critical,
                    metrics.database_update_failures,
                ));
            }

            // License scan failure alerts
            if metrics.license_scan_failures >= self.config.license_scan_failure_threshold {
                alerts.push(self.create_scan_failure_alert(
                    "License Scan Failures",
                    format!("{} license scan failures in recent period", metrics.license_scan_failures),
                    AlertSeverity::Warning,
                    metrics.license_scan_failures,
                ));
            }

            // Stale data alerts
            if metrics.data_freshness_hours >= self.config.stale_data_threshold_hours as f64 {
                alerts.push(self.create_stale_data_alert(
                    "Stale License Data",
                    format!("License data is {:.1} hours old", metrics.data_freshness_hours),
                    AlertSeverity::Warning,
                    metrics.data_freshness_hours,
                ));
            }

            // Custom compliance rule alerts
            for rule in &self.config.custom_compliance_rules {
                if !rule.enabled {
                    continue;
                }

                if let Some(alert) = self.evaluate_custom_rule(rule, &metrics) {
                    alerts.push(alert);
                }
            }

            // Send alerts through the framework
            if !alerts.is_empty() {
                let framework = framework.read().await;
                let alert_manager = framework.alert_manager();

                {
                    let mut manager = alert_manager.write().await;
                    let processed_alerts = manager.process_alerts(alerts);

                    // Send notifications for new alerts
                    if let Some(notification_manager) = framework.notification_manager().try_read() {
                        for alert in processed_alerts {
                            let _ = notification_manager.send_alert_notification(&alert).await;
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// Create license accuracy alert
    fn create_accuracy_alert(&self, title: &str, description: String, severity: AlertSeverity, accuracy: f64) -> Alert {
        use super::Alert;

        Alert {
            id: format!("compliance_accuracy_{}_{}", severity.as_str(), Utc::now().timestamp()),
            title: title.to_string(),
            description,
            severity,
            category: AlertCategory::Compliance,
            status: AlertStatus::Active,
            source: "compliance_monitor".to_string(),
            labels: HashMap::from([
                ("metric".to_string(), "license_accuracy".to_string()),
                ("type".to_string(), "compliance".to_string()),
            ]),
            annotations: HashMap::from([
                ("accuracy_percent".to_string(), format!("{:.2}", accuracy * 100.0)),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(accuracy * 100.0),
            threshold: Some(match severity {
                AlertSeverity::Warning => self.config.license_accuracy_warning_threshold * 100.0,
                AlertSeverity::Critical => self.config.license_accuracy_critical_threshold * 100.0,
                _ => 0.0,
            }),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("compliance_accuracy".to_string()),
            fingerprint: format!("compliance_accuracy_{}_{}", severity.as_str(), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create compliance violation alert
    fn create_violation_alert(&self, title: &str, description: String, severity: AlertSeverity, violations: u64) -> Alert {
        use super::Alert;

        Alert {
            id: format!("compliance_violations_{}_{}", severity.as_str(), Utc::now().timestamp()),
            title: title.to_string(),
            description,
            severity,
            category: AlertCategory::Compliance,
            status: AlertStatus::Active,
            source: "compliance_monitor".to_string(),
            labels: HashMap::from([
                ("metric".to_string(), "compliance_violations".to_string()),
                ("type".to_string(), "compliance".to_string()),
            ]),
            annotations: HashMap::from([
                ("violation_count".to_string(), violations.to_string()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(violations as f64),
            threshold: Some(self.config.compliance_violation_threshold as f64),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("compliance_violations".to_string()),
            fingerprint: format!("compliance_violations_{}_{}", severity.as_str(), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create ML model accuracy alert
    fn create_ml_accuracy_alert(&self, title: &str, description: String, severity: AlertSeverity, accuracy: f64, model_version: &str) -> Alert {
        use super::Alert;

        Alert {
            id: format!("compliance_ml_accuracy_{}_{}", severity.as_str(), Utc::now().timestamp()),
            title: title.to_string(),
            description,
            severity,
            category: AlertCategory::Compliance,
            status: AlertStatus::Active,
            source: "compliance_monitor".to_string(),
            labels: HashMap::from([
                ("metric".to_string(), "ml_model_accuracy".to_string()),
                ("model_version".to_string(), model_version.to_string()),
                ("type".to_string(), "compliance".to_string()),
            ]),
            annotations: HashMap::from([
                ("accuracy_percent".to_string(), format!("{:.2}", accuracy * 100.0)),
                ("model_version".to_string(), model_version.to_string()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(accuracy * 100.0),
            threshold: Some(match severity {
                AlertSeverity::Warning => self.config.ml_model_accuracy_warning_threshold * 100.0,
                AlertSeverity::Critical => self.config.ml_model_accuracy_critical_threshold * 100.0,
                _ => 0.0,
            }),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("compliance_ml_accuracy".to_string()),
            fingerprint: format!("compliance_ml_accuracy_{}_{}", severity.as_str(), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create database update failure alert
    fn create_database_alert(&self, title: &str, description: String, severity: AlertSeverity, failures: u64) -> Alert {
        use super::Alert;

        Alert {
            id: format!("compliance_db_failures_{}_{}", severity.as_str(), Utc::now().timestamp()),
            title: title.to_string(),
            description,
            severity,
            category: AlertCategory::Compliance,
            status: AlertStatus::Active,
            source: "compliance_monitor".to_string(),
            labels: HashMap::from([
                ("metric".to_string(), "database_update_failures".to_string()),
                ("type".to_string(), "compliance".to_string()),
            ]),
            annotations: HashMap::from([
                ("failure_count".to_string(), failures.to_string()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(failures as f64),
            threshold: Some(self.config.database_update_failure_threshold as f64),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("compliance_database".to_string()),
            fingerprint: format!("compliance_db_failures_{}_{}", severity.as_str(), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create scan failure alert
    fn create_scan_failure_alert(&self, title: &str, description: String, severity: AlertSeverity, failures: u64) -> Alert {
        use super::Alert;

        Alert {
            id: format!("compliance_scan_failures_{}_{}", severity.as_str(), Utc::now().timestamp()),
            title: title.to_string(),
            description,
            severity,
            category: AlertCategory::Compliance,
            status: AlertStatus::Active,
            source: "compliance_monitor".to_string(),
            labels: HashMap::from([
                ("metric".to_string(), "scan_failures".to_string()),
                ("type".to_string(), "compliance".to_string()),
            ]),
            annotations: HashMap::from([
                ("failure_count".to_string(), failures.to_string()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(failures as f64),
            threshold: Some(self.config.license_scan_failure_threshold as f64),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("compliance_scan_failures".to_string()),
            fingerprint: format!("compliance_scan_failures_{}_{}", severity.as_str(), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create stale data alert
    fn create_stale_data_alert(&self, title: &str, description: String, severity: AlertSeverity, hours: f64) -> Alert {
        use super::Alert;

        Alert {
            id: format!("compliance_stale_data_{}_{}", severity.as_str(), Utc::now().timestamp()),
            title: title.to_string(),
            description,
            severity,
            category: AlertCategory::Compliance,
            status: AlertStatus::Active,
            source: "compliance_monitor".to_string(),
            labels: HashMap::from([
                ("metric".to_string(), "data_freshness".to_string()),
                ("type".to_string(), "compliance".to_string()),
            ]),
            annotations: HashMap::from([
                ("hours_old".to_string(), format!("{:.1}", hours)),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(hours),
            threshold: Some(self.config.stale_data_threshold_hours as f64),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("compliance_stale_data".to_string()),
            fingerprint: format!("compliance_stale_data_{}_{}", severity.as_str(), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Evaluate custom compliance rule
    fn evaluate_custom_rule(&self, rule: &CustomComplianceRule, metrics: &ComplianceMetrics) -> Option<Alert> {
        // Simple evaluation logic for custom rules
        // In a real implementation, this would parse the metric_query and evaluate it
        let value = match rule.metric_query.as_str() {
            "license_detection_accuracy" => metrics.license_detection_accuracy,
            "compliance_violations" => metrics.compliance_violations as f64,
            "ml_model_accuracy" => metrics.ml_model_accuracy,
            "database_update_failures" => metrics.database_update_failures as f64,
            "license_scan_failures" => metrics.license_scan_failures as f64,
            _ => {
                // Check custom metrics
                if let Some(val) = metrics.custom_metrics.get(&rule.metric_query) {
                    *val
                } else {
                    return None;
                }
            }
        };

        let threshold: f64 = rule.threshold.parse().ok()?;
        let condition_met = match rule.operator.as_str() {
            "gt" => value > threshold,
            "lt" => value < threshold,
            "eq" => (value - threshold).abs() < f64::EPSILON,
            "gte" => value >= threshold,
            "lte" => value <= threshold,
            _ => false,
        };

        if condition_met {
            Some(self.create_custom_rule_alert(rule, value))
        } else {
            None
        }
    }

    /// Create custom rule alert
    fn create_custom_rule_alert(&self, rule: &CustomComplianceRule, value: f64) -> Alert {
        use super::Alert;

        Alert {
            id: format!("compliance_custom_{}_{}", rule.id, Utc::now().timestamp()),
            title: format!("Custom Rule: {}", rule.name),
            description: format!("{} - Value: {:.2}", rule.description, value),
            severity: rule.severity,
            category: AlertCategory::Compliance,
            status: AlertStatus::Active,
            source: "compliance_monitor".to_string(),
            labels: HashMap::from([
                ("rule_id".to_string(), rule.id.clone()),
                ("rule_name".to_string(), rule.name.clone()),
                ("type".to_string(), "compliance".to_string()),
            ]),
            annotations: HashMap::from([
                ("metric_query".to_string(), rule.metric_query.clone()),
                ("operator".to_string(), rule.operator.clone()),
                ("threshold".to_string(), rule.threshold.clone()),
                ("value".to_string(), value.to_string()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(value),
            threshold: Some(rule.threshold.parse().unwrap_or(0.0)),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some(format!("custom_{}", rule.id)),
            fingerprint: format!("compliance_custom_{}_{}", rule.id, Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Get recent compliance metrics
    pub fn get_recent_metrics(&self, count: usize) -> Vec<&ComplianceMetrics> {
        self.metrics_history.iter().rev().take(count).collect()
    }

    /// Calculate compliance trends
    pub fn calculate_trends(&self) -> ComplianceTrends {
        if self.metrics_history.len() < 2 {
            return ComplianceTrends::default();
        }

        let recent = self.get_recent_metrics(10);
        let older = self.get_recent_metrics(20).into_iter().rev().take(10).collect::<Vec<_>>();

        if recent.len() < 2 || older.len() < 2 {
            return ComplianceTrends::default();
        }

        let recent_avg_accuracy = recent.iter().map(|m| m.license_detection_accuracy).sum::<f64>() / recent.len() as f64;
        let older_avg_accuracy = older.iter().map(|m| m.license_detection_accuracy).sum::<f64>() / older.len() as f64;

        let recent_avg_violations = recent.iter().map(|m| m.compliance_violations).sum::<f64>() / recent.len() as f64;
        let older_avg_violations = older.iter().map(|m| m.compliance_violations).sum::<f64>() / older.len() as f64;

        ComplianceTrends {
            accuracy_trend: if recent_avg_accuracy > older_avg_accuracy * 1.05 {
                "improving".to_string()
            } else if recent_avg_accuracy < older_avg_accuracy * 0.95 {
                "degrading".to_string()
            } else {
                "stable".to_string()
            },
            violations_trend: if recent_avg_violations > older_avg_violations * 1.2 {
                "increasing".to_string()
            } else if recent_avg_violations < older_avg_violations * 0.8 {
                "decreasing".to_string()
            } else {
                "stable".to_string()
            },
        }
    }

    /// Get compliance monitor configuration
    pub fn get_config(&self) -> &ComplianceMonitorConfig {
        &self.config
    }
}

/// Compliance trends
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ComplianceTrends {
    pub accuracy_trend: String,
    pub violations_trend: String,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_compliance_monitor_creation() {
        let config = ComplianceMonitorConfig::default();
        let monitor = ComplianceMonitor::new(config);
        assert!(monitor.config.enabled);
    }

    #[test]
    fn test_compliance_metrics_creation() {
        let metrics = ComplianceMetrics {
            timestamp: Utc::now(),
            license_detection_accuracy: 0.85,
            total_license_predictions: 1000,
            correct_predictions: 850,
            false_positives: 50,
            false_negatives: 100,
            compliance_violations: 5,
            active_violations: 3,
            resolved_violations: 2,
            ml_model_accuracy: 0.82,
            ml_model_version: "v1.2.3".to_string(),
            database_update_failures: 2,
            license_scan_failures: 3,
            successful_scans: 97,
            total_scans: 100,
            data_freshness_hours: 12.5,
            license_database_size: 50000,
            last_database_update: Some(Utc::now()),
            custom_metrics: HashMap::new(),
        };

        assert_eq!(metrics.license_detection_accuracy, 0.85);
        assert_eq!(metrics.total_license_predictions, 1000);
    }
}