//! # Notification System
//!
//! Multi-channel notification system for alerts with support for
//! email, Slack, PagerDuty, webhooks, and custom integrations.

use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use reqwest::Client;
use tokio::sync::RwLock;

use super::{Alert, AlertSeverity};

/// Notification configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationConfig {
    pub enabled: bool,
    pub channels: Vec<NotificationChannel>,
    pub templates: HashMap<String, NotificationTemplate>,
    pub rate_limiting: RateLimitingConfig,
    pub retry_policy: RetryPolicy,
}

impl Default for NotificationConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            channels: vec![
                NotificationChannel::Email(EmailChannel {
                    smtp_server: "smtp.gmail.com".to_string(),
                    smtp_port: 587,
                    username: "".to_string(),
                    password: "".to_string(),
                    from_address: "<EMAIL>".to_string(),
                    recipients: vec![],
                }),
                NotificationChannel::Slack(SlackChannel {
                    webhook_url: "".to_string(),
                    channel: "#alerts".to_string(),
                    username: "Infinitium Alert".to_string(),
                }),
            ],
            templates: HashMap::new(),
            rate_limiting: RateLimitingConfig::default(),
            retry_policy: RetryPolicy::default(),
        }
    }
}

/// Notification channel types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum NotificationChannel {
    Email(EmailChannel),
    Slack(SlackChannel),
    PagerDuty(PagerDutyChannel),
    Webhook(WebhookChannel),
    Custom(CustomChannel),
}

/// Email notification channel
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailChannel {
    pub smtp_server: String,
    pub smtp_port: u16,
    pub username: String,
    pub password: String,
    pub from_address: String,
    pub recipients: Vec<String>,
}

/// Slack notification channel
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SlackChannel {
    pub webhook_url: String,
    pub channel: String,
    pub username: String,
}

/// PagerDuty notification channel
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PagerDutyChannel {
    pub routing_key: String,
    pub integration_key: String,
    pub service_id: String,
}

/// Webhook notification channel
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebhookChannel {
    pub url: String,
    pub headers: HashMap<String, String>,
    pub method: String,
    pub timeout_seconds: u64,
}

/// Custom notification channel
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CustomChannel {
    pub name: String,
    pub config: HashMap<String, String>,
}

/// Notification template
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationTemplate {
    pub name: String,
    pub subject_template: String,
    pub body_template: String,
    pub severity_filters: Vec<AlertSeverity>,
}

/// Rate limiting configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitingConfig {
    pub enabled: bool,
    pub max_notifications_per_minute: u32,
    pub max_notifications_per_hour: u32,
    pub cooldown_period_seconds: u64,
}

impl Default for RateLimitingConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            max_notifications_per_minute: 10,
            max_notifications_per_hour: 100,
            cooldown_period_seconds: 60,
        }
    }
}

/// Retry policy configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetryPolicy {
    pub max_retries: u32,
    pub initial_delay_seconds: u64,
    pub max_delay_seconds: u64,
    pub backoff_multiplier: f64,
}

impl Default for RetryPolicy {
    fn default() -> Self {
        Self {
            max_retries: 3,
            initial_delay_seconds: 5,
            max_delay_seconds: 300,
            backoff_multiplier: 2.0,
        }
    }
}

/// Notification result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationResult {
    pub channel: String,
    pub success: bool,
    pub error_message: Option<String>,
    pub timestamp: DateTime<Utc>,
    pub retry_count: u32,
}

/// Notification manager
pub struct NotificationManager {
    config: NotificationConfig,
    http_client: Client,
    rate_limiter: RateLimiter,
    templates: HashMap<String, NotificationTemplate>,
}

impl NotificationManager {
    /// Create a new notification manager
    pub fn new(config: NotificationConfig) -> Self {
        let http_client = Client::new();
        let rate_limiter = RateLimiter::new(config.rate_limiting.clone());

        Self {
            templates: config.templates.clone(),
            config,
            http_client,
            rate_limiter,
        }
    }

    /// Initialize the notification manager
    pub async fn initialize(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Load default templates if none provided
        if self.templates.is_empty() {
            self.load_default_templates();
        }

        // Test connections to notification channels
        self.test_connections().await?;

        Ok(())
    }

    /// Send notification for an alert
    pub async fn send_alert_notification(&self, alert: &Alert) -> Vec<NotificationResult> {
        let mut results = Vec::new();

        // Check rate limiting
        if self.rate_limiter.should_throttle() {
            results.push(NotificationResult {
                channel: "rate_limited".to_string(),
                success: false,
                error_message: Some("Rate limit exceeded".to_string()),
                timestamp: Utc::now(),
                retry_count: 0,
            });
            return results;
        }

        // Send to all configured channels
        for channel in &self.config.channels {
            let result = self.send_to_channel(channel, alert).await;
            results.push(result);
        }

        results
    }

    /// Send notification to a specific channel
    async fn send_to_channel(&self, channel: &NotificationChannel, alert: &Alert) -> NotificationResult {
        let channel_name = self.get_channel_name(channel);

        match channel {
            NotificationChannel::Email(email_channel) => {
                self.send_email_notification(email_channel, alert).await
            }
            NotificationChannel::Slack(slack_channel) => {
                self.send_slack_notification(slack_channel, alert).await
            }
            NotificationChannel::PagerDuty(pd_channel) => {
                self.send_pagerduty_notification(pd_channel, alert).await
            }
            NotificationChannel::Webhook(webhook_channel) => {
                self.send_webhook_notification(webhook_channel, alert).await
            }
            NotificationChannel::Custom(custom_channel) => {
                self.send_custom_notification(custom_channel, alert).await
            }
        }
    }

    /// Send email notification
    async fn send_email_notification(&self, channel: &EmailChannel, alert: &Alert) -> NotificationResult {
        let timestamp = Utc::now();

        // Get template for email
        let template = self.get_template_for_alert(alert);

        // Render subject and body
        let subject = self.render_template(&template.subject_template, alert);
        let body = self.render_template(&template.body_template, alert);

        // In a real implementation, this would use an SMTP library
        // For now, we'll simulate sending
        println!("Sending email notification:");
        println!("To: {:?}", channel.recipients);
        println!("Subject: {}", subject);
        println!("Body: {}", body);

        NotificationResult {
            channel: "email".to_string(),
            success: true,
            error_message: None,
            timestamp,
            retry_count: 0,
        }
    }

    /// Send Slack notification
    async fn send_slack_notification(&self, channel: &SlackChannel, alert: &Alert) -> NotificationResult {
        let timestamp = Utc::now();

        if channel.webhook_url.is_empty() {
            return NotificationResult {
                channel: "slack".to_string(),
                success: false,
                error_message: Some("Slack webhook URL not configured".to_string()),
                timestamp,
                retry_count: 0,
            };
        }

        let template = self.get_template_for_alert(alert);
        let message = self.render_template(&template.body_template, alert);

        let payload = serde_json::json!({
            "channel": channel.channel,
            "username": channel.username,
            "text": message,
            "attachments": [{
                "color": self.get_slack_color_for_severity(alert.severity),
                "fields": [
                    {
                        "title": "Severity",
                        "value": alert.severity.as_str(),
                        "short": true
                    },
                    {
                        "title": "Category",
                        "value": alert.category.as_str(),
                        "short": true
                    }
                ]
            }]
        });

        match self.http_client
            .post(&channel.webhook_url)
            .json(&payload)
            .send()
            .await
        {
            Ok(response) => {
                if response.status().is_success() {
                    NotificationResult {
                        channel: "slack".to_string(),
                        success: true,
                        error_message: None,
                        timestamp,
                        retry_count: 0,
                    }
                } else {
                    NotificationResult {
                        channel: "slack".to_string(),
                        success: false,
                        error_message: Some(format!("HTTP {}", response.status())),
                        timestamp,
                        retry_count: 0,
                    }
                }
            }
            Err(e) => NotificationResult {
                channel: "slack".to_string(),
                success: false,
                error_message: Some(e.to_string()),
                timestamp,
                retry_count: 0,
            }
        }
    }

    /// Send PagerDuty notification
    async fn send_pagerduty_notification(&self, channel: &PagerDutyChannel, alert: &Alert) -> NotificationResult {
        let timestamp = Utc::now();

        let payload = serde_json::json!({
            "routing_key": channel.routing_key,
            "event_action": "trigger",
            "dedup_key": alert.fingerprint,
            "payload": {
                "summary": alert.title,
                "source": alert.source,
                "severity": self.map_severity_to_pagerduty(alert.severity),
                "component": alert.category.as_str(),
                "group": "infinitium-signal",
                "class": "alert",
                "custom_details": {
                    "description": alert.description,
                    "labels": alert.labels,
                    "annotations": alert.annotations
                }
            }
        });

        // PagerDuty Events API v2
        let url = "https://events.pagerduty.com/v2/enqueue";

        match self.http_client
            .post(url)
            .json(&payload)
            .send()
            .await
        {
            Ok(response) => {
                if response.status().is_success() {
                    NotificationResult {
                        channel: "pagerduty".to_string(),
                        success: true,
                        error_message: None,
                        timestamp,
                        retry_count: 0,
                    }
                } else {
                    NotificationResult {
                        channel: "pagerduty".to_string(),
                        success: false,
                        error_message: Some(format!("HTTP {}", response.status())),
                        timestamp,
                        retry_count: 0,
                    }
                }
            }
            Err(e) => NotificationResult {
                channel: "pagerduty".to_string(),
                success: false,
                error_message: Some(e.to_string()),
                timestamp,
                retry_count: 0,
            }
        }
    }

    /// Send webhook notification
    async fn send_webhook_notification(&self, channel: &WebhookChannel, alert: &Alert) -> NotificationResult {
        let timestamp = Utc::now();

        let payload = serde_json::json!({
            "alert": alert,
            "timestamp": timestamp.to_rfc3339()
        });

        let mut request = match channel.method.as_str() {
            "POST" => self.http_client.post(&channel.url),
            "PUT" => self.http_client.put(&channel.url),
            _ => self.http_client.post(&channel.url),
        };

        // Add headers
        for (key, value) in &channel.headers {
            request = request.header(key, value);
        }

        match request
            .json(&payload)
            .timeout(std::time::Duration::from_secs(channel.timeout_seconds))
            .send()
            .await
        {
            Ok(response) => {
                if response.status().is_success() {
                    NotificationResult {
                        channel: "webhook".to_string(),
                        success: true,
                        error_message: None,
                        timestamp,
                        retry_count: 0,
                    }
                } else {
                    NotificationResult {
                        channel: "webhook".to_string(),
                        success: false,
                        error_message: Some(format!("HTTP {}", response.status())),
                        timestamp,
                        retry_count: 0,
                    }
                }
            }
            Err(e) => NotificationResult {
                channel: "webhook".to_string(),
                success: false,
                error_message: Some(e.to_string()),
                timestamp,
                retry_count: 0,
            }
        }
    }

    /// Send custom notification
    async fn send_custom_notification(&self, channel: &CustomChannel, alert: &Alert) -> NotificationResult {
        let timestamp = Utc::now();

        // Custom notification logic would go here
        // For now, just log it
        println!("Sending custom notification to {}: {:?}", channel.name, alert);

        NotificationResult {
            channel: format!("custom:{}", channel.name),
            success: true,
            error_message: None,
            timestamp,
            retry_count: 0,
        }
    }

    /// Get channel name for logging
    fn get_channel_name(&self, channel: &NotificationChannel) -> String {
        match channel {
            NotificationChannel::Email(_) => "email".to_string(),
            NotificationChannel::Slack(_) => "slack".to_string(),
            NotificationChannel::PagerDuty(_) => "pagerduty".to_string(),
            NotificationChannel::Webhook(_) => "webhook".to_string(),
            NotificationChannel::Custom(c) => format!("custom:{}", c.name),
        }
    }

    /// Get template for alert based on severity
    fn get_template_for_alert(&self, alert: &Alert) -> &NotificationTemplate {
        // Find template that matches severity, or use default
        for template in self.templates.values() {
            if template.severity_filters.contains(&alert.severity) {
                return template;
            }
        }

        // Return default template
        self.templates.get("default").unwrap_or_else(|| {
            panic!("Default notification template not found")
        })
    }

    /// Render template with alert data
    fn render_template(&self, template: &str, alert: &Alert) -> String {
        // Simple template rendering - in production, use a proper template engine
        let mut result = template.to_string();

        result = result.replace("{{alert.title}}", &alert.title);
        result = result.replace("{{alert.description}}", &alert.description);
        result = result.replace("{{alert.severity}}", alert.severity.as_str());
        result = result.replace("{{alert.category}}", alert.category.as_str());
        result = result.replace("{{alert.source}}", &alert.source);
        result = result.replace("{{alert.status}}", alert.status.as_str());

        if let Some(value) = alert.value {
            result = result.replace("{{alert.value}}", &value.to_string());
        }

        if let Some(threshold) = alert.threshold {
            result = result.replace("{{alert.threshold}}", &threshold.to_string());
        }

        result
    }

    /// Get Slack color for severity
    fn get_slack_color_for_severity(&self, severity: AlertSeverity) -> String {
        match severity {
            AlertSeverity::Info => "good".to_string(),
            AlertSeverity::Warning => "warning".to_string(),
            AlertSeverity::Error => "danger".to_string(),
            AlertSeverity::Critical => "danger".to_string(),
        }
    }

    /// Map severity to PagerDuty severity
    fn map_severity_to_pagerduty(&self, severity: AlertSeverity) -> String {
        match severity {
            AlertSeverity::Info => "info".to_string(),
            AlertSeverity::Warning => "warning".to_string(),
            AlertSeverity::Error => "error".to_string(),
            AlertSeverity::Critical => "critical".to_string(),
        }
    }

    /// Load default notification templates
    fn load_default_templates(&mut self) {
        let default_template = NotificationTemplate {
            name: "default".to_string(),
            subject_template: "[{{alert.severity}}] {{alert.title}}".to_string(),
            body_template: r#"
Alert Details:
- Title: {{alert.title}}
- Description: {{alert.description}}
- Severity: {{alert.severity}}
- Category: {{alert.category}}
- Source: {{alert.source}}
- Status: {{alert.status}}
{{#if alert.value}}- Value: {{alert.value}}{{/if}}
{{#if alert.threshold}}- Threshold: {{alert.threshold}}{{/if}}

Labels: {{alert.labels}}
Annotations: {{alert.annotations}}

This alert was generated at {{alert.created_at}}
            "#.to_string(),
            severity_filters: vec![
                AlertSeverity::Info,
                AlertSeverity::Warning,
                AlertSeverity::Error,
                AlertSeverity::Critical,
            ],
        };

        let critical_template = NotificationTemplate {
            name: "critical".to_string(),
            subject_template: "🚨 CRITICAL: {{alert.title}}".to_string(),
            body_template: r#"
🚨 CRITICAL ALERT 🚨

{{alert.title}}

{{alert.description}}

Severity: {{alert.severity}}
Category: {{alert.category}}
Source: {{alert.source}}

{{#if alert.value}}Current Value: {{alert.value}}{{/if}}
{{#if alert.threshold}}Threshold: {{alert.threshold}}{{/if}}

IMMEDIATE ATTENTION REQUIRED!

Generated at: {{alert.created_at}}
            "#.to_string(),
            severity_filters: vec![AlertSeverity::Critical],
        };

        self.templates.insert("default".to_string(), default_template);
        self.templates.insert("critical".to_string(), critical_template);
    }

    /// Test connections to all channels
    async fn test_connections(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Test webhook and Slack connections
        for channel in &self.config.channels {
            match channel {
                NotificationChannel::Slack(slack) => {
                    if !slack.webhook_url.is_empty() {
                        let test_payload = serde_json::json!({
                            "text": "Infinitium Signal notification system test",
                            "username": slack.username
                        });

                        let _ = self.http_client
                            .post(&slack.webhook_url)
                            .json(&test_payload)
                            .send()
                            .await?;
                    }
                }
                NotificationChannel::Webhook(webhook) => {
                    let test_payload = serde_json::json!({
                        "test": true,
                        "message": "Infinitium Signal webhook test"
                    });

                    let mut request = self.http_client.post(&webhook.url);
                    for (key, value) in &webhook.headers {
                        request = request.header(key, value);
                    }

                    let _ = request
                        .json(&test_payload)
                        .timeout(std::time::Duration::from_secs(10))
                        .send()
                        .await?;
                }
                _ => {} // Other channels don't need connection testing
            }
        }

        Ok(())
    }

    /// Shutdown the notification manager
    pub async fn shutdown(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Clean up resources
        Ok(())
    }
}

/// Rate limiter for notifications
pub struct RateLimiter {
    config: RateLimitingConfig,
    notifications_this_minute: u32,
    notifications_this_hour: u32,
    last_minute_reset: DateTime<Utc>,
    last_hour_reset: DateTime<Utc>,
}

impl RateLimiter {
    pub fn new(config: RateLimitingConfig) -> Self {
        let now = Utc::now();
        Self {
            config,
            notifications_this_minute: 0,
            notifications_this_hour: 0,
            last_minute_reset: now,
            last_hour_reset: now,
        }
    }

    pub fn should_throttle(&mut self) -> bool {
        if !self.config.enabled {
            return false;
        }

        let now = Utc::now();

        // Reset counters if needed
        if (now - self.last_minute_reset).num_minutes() >= 1 {
            self.notifications_this_minute = 0;
            self.last_minute_reset = now;
        }

        if (now - self.last_hour_reset).num_hours() >= 1 {
            self.notifications_this_hour = 0;
            self.last_hour_reset = now;
        }

        // Check limits
        if self.notifications_this_minute >= self.config.max_notifications_per_minute {
            return true;
        }

        if self.notifications_this_hour >= self.config.max_notifications_per_hour {
            return true;
        }

        // Record the notification
        self.notifications_this_minute += 1;
        self.notifications_this_hour += 1;

        false
    }
}