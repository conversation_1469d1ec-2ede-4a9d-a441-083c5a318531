//! # Performance Monitor
//!
//! Monitors application performance metrics and generates alerts based on
//! latency, throughput, error rates, and bottleneck detection.

use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, Duration};

use super::{<PERSON><PERSON>, AlertSeverity, AlertStatus, AlertCategory, AlertingFramework};

/// Performance monitor configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMonitorConfig {
    pub enabled: bool,
    pub monitoring_interval_seconds: u64,
    pub request_latency_warning_ms: f64,
    pub request_latency_critical_ms: f64,
    pub error_rate_warning_percent: f64,
    pub error_rate_critical_percent: f64,
    pub throughput_warning_per_second: f64,
    pub throughput_critical_per_second: f64,
    pub queue_depth_warning: u64,
    pub queue_depth_critical: u64,
    pub database_connection_warning: u32,
    pub database_connection_critical: u32,
    pub memory_usage_warning_percent: f64,
    pub memory_usage_critical_percent: f64,
    pub cpu_usage_warning_percent: f64,
    pub cpu_usage_critical_percent: f64,
    pub slow_query_threshold_ms: f64,
    pub enable_percentile_alerts: bool,
    pub p95_latency_warning_ms: f64,
    pub p99_latency_critical_ms: f64,
}

impl Default for PerformanceMonitorConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            monitoring_interval_seconds: 30,
            request_latency_warning_ms: 1000.0,
            request_latency_critical_ms: 5000.0,
            error_rate_warning_percent: 1.0,
            error_rate_critical_percent: 5.0,
            throughput_warning_per_second: 10.0,
            throughput_critical_per_second: 1.0,
            queue_depth_warning: 100,
            queue_depth_critical: 1000,
            database_connection_warning: 50,
            database_connection_critical: 10,
            memory_usage_warning_percent: 75.0,
            memory_usage_critical_percent: 90.0,
            cpu_usage_warning_percent: 70.0,
            cpu_usage_critical_percent: 90.0,
            slow_query_threshold_ms: 1000.0,
            enable_percentile_alerts: true,
            p95_latency_warning_ms: 2000.0,
            p99_latency_critical_ms: 5000.0,
        }
    }
}

/// Performance metrics snapshot
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub timestamp: DateTime<Utc>,
    pub request_count: u64,
    pub error_count: u64,
    pub total_response_time_ms: f64,
    pub avg_response_time_ms: f64,
    pub p50_response_time_ms: f64,
    pub p95_response_time_ms: f64,
    pub p99_response_time_ms: f64,
    pub throughput_per_second: f64,
    pub error_rate_percent: f64,
    pub active_connections: u32,
    pub queue_depth: u64,
    pub database_connections_active: u32,
    pub database_connections_idle: u32,
    pub memory_usage_percent: f64,
    pub cpu_usage_percent: f64,
    pub slow_queries_count: u64,
    pub cache_hit_rate_percent: f64,
    pub endpoint_metrics: HashMap<String, EndpointMetrics>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EndpointMetrics {
    pub request_count: u64,
    pub error_count: u64,
    pub avg_response_time_ms: f64,
    pub p95_response_time_ms: f64,
    pub throughput_per_second: f64,
}

/// Performance monitor
pub struct PerformanceMonitor {
    config: PerformanceMonitorConfig,
    metrics_history: VecDeque<PerformanceMetrics>,
    max_history_size: usize,
    alerting_framework: Option<Arc<RwLock<AlertingFramework>>>,
}

impl PerformanceMonitor {
    /// Create a new performance monitor
    pub fn new(config: PerformanceMonitorConfig) -> Self {
        Self {
            config,
            metrics_history: VecDeque::new(),
            max_history_size: 1000,
            alerting_framework: None,
        }
    }

    /// Set alerting framework reference
    pub fn set_alerting_framework(&mut self, framework: Arc<RwLock<AlertingFramework>>) {
        self.alerting_framework = Some(framework);
    }

    /// Record performance metrics
    pub async fn record_metrics(&mut self, metrics: PerformanceMetrics) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Store metrics in history
        if self.metrics_history.len() >= self.max_history_size {
            self.metrics_history.pop_front();
        }
        self.metrics_history.push_back(metrics.clone());

        // Analyze metrics and generate alerts
        self.analyze_and_alert(metrics).await?;

        Ok(())
    }

    /// Analyze metrics and generate alerts
    async fn analyze_and_alert(&self, metrics: PerformanceMetrics) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if let Some(ref framework) = self.alerting_framework {
            let mut alerts = Vec::new();

            // Latency alerts
            if metrics.avg_response_time_ms >= self.config.request_latency_critical_ms {
                alerts.push(self.create_latency_alert(
                    "Critical Request Latency",
                    format!("Average response time is {:.1}ms (threshold: {:.1}ms)", metrics.avg_response_time_ms, self.config.request_latency_critical_ms),
                    AlertSeverity::Critical,
                    metrics.avg_response_time_ms,
                ));
            } else if metrics.avg_response_time_ms >= self.config.request_latency_warning_ms {
                alerts.push(self.create_latency_alert(
                    "High Request Latency",
                    format!("Average response time is {:.1}ms (threshold: {:.1}ms)", metrics.avg_response_time_ms, self.config.request_latency_warning_ms),
                    AlertSeverity::Warning,
                    metrics.avg_response_time_ms,
                ));
            }

            // Percentile latency alerts
            if self.config.enable_percentile_alerts {
                if metrics.p95_response_time_ms >= self.config.p95_latency_warning_ms {
                    alerts.push(self.create_latency_alert(
                        "High P95 Latency",
                        format!("95th percentile response time is {:.1}ms", metrics.p95_response_time_ms),
                        AlertSeverity::Warning,
                        metrics.p95_response_time_ms,
                    ));
                }

                if metrics.p99_response_time_ms >= self.config.p99_latency_critical_ms {
                    alerts.push(self.create_latency_alert(
                        "Critical P99 Latency",
                        format!("99th percentile response time is {:.1}ms", metrics.p99_response_time_ms),
                        AlertSeverity::Critical,
                        metrics.p99_response_time_ms,
                    ));
                }
            }

            // Error rate alerts
            if metrics.error_rate_percent >= self.config.error_rate_critical_percent {
                alerts.push(self.create_error_rate_alert(
                    "Critical Error Rate",
                    format!("Error rate is {:.2}% (threshold: {:.2}%)", metrics.error_rate_percent, self.config.error_rate_critical_percent),
                    AlertSeverity::Critical,
                    metrics.error_rate_percent,
                ));
            } else if metrics.error_rate_percent >= self.config.error_rate_warning_percent {
                alerts.push(self.create_error_rate_alert(
                    "High Error Rate",
                    format!("Error rate is {:.2}% (threshold: {:.2}%)", metrics.error_rate_percent, self.config.error_rate_warning_percent),
                    AlertSeverity::Warning,
                    metrics.error_rate_percent,
                ));
            }

            // Throughput alerts
            if metrics.throughput_per_second <= self.config.throughput_critical_per_second {
                alerts.push(self.create_throughput_alert(
                    "Critical Low Throughput",
                    format!("Throughput is {:.2} requests/sec (threshold: {:.2})", metrics.throughput_per_second, self.config.throughput_critical_per_second),
                    AlertSeverity::Critical,
                    metrics.throughput_per_second,
                ));
            } else if metrics.throughput_per_second <= self.config.throughput_warning_per_second {
                alerts.push(self.create_throughput_alert(
                    "Low Throughput",
                    format!("Throughput is {:.2} requests/sec (threshold: {:.2})", metrics.throughput_per_second, self.config.throughput_warning_per_second),
                    AlertSeverity::Warning,
                    metrics.throughput_per_second,
                ));
            }

            // Queue depth alerts
            if metrics.queue_depth >= self.config.queue_depth_critical {
                alerts.push(self.create_queue_alert(
                    "Critical Queue Depth",
                    format!("Request queue depth is {} (threshold: {})", metrics.queue_depth, self.config.queue_depth_critical),
                    AlertSeverity::Critical,
                    metrics.queue_depth,
                ));
            } else if metrics.queue_depth >= self.config.queue_depth_warning {
                alerts.push(self.create_queue_alert(
                    "High Queue Depth",
                    format!("Request queue depth is {} (threshold: {})", metrics.queue_depth, self.config.queue_depth_warning),
                    AlertSeverity::Warning,
                    metrics.queue_depth,
                ));
            }

            // Database connection alerts
            if metrics.database_connections_active <= self.config.database_connection_critical as u32 {
                alerts.push(self.create_db_connection_alert(
                    "Critical Database Connections",
                    format!("Active database connections: {} (threshold: {})", metrics.database_connections_active, self.config.database_connection_critical),
                    AlertSeverity::Critical,
                    metrics.database_connections_active,
                ));
            } else if metrics.database_connections_active <= self.config.database_connection_warning as u32 {
                alerts.push(self.create_db_connection_alert(
                    "Low Database Connections",
                    format!("Active database connections: {} (threshold: {})", metrics.database_connections_active, self.config.database_connection_warning),
                    AlertSeverity::Warning,
                    metrics.database_connections_active,
                ));
            }

            // Memory usage alerts
            if metrics.memory_usage_percent >= self.config.memory_usage_critical_percent {
                alerts.push(self.create_resource_alert(
                    "Critical Memory Usage",
                    format!("Memory usage is {:.1}% (threshold: {:.1}%)", metrics.memory_usage_percent, self.config.memory_usage_critical_percent),
                    AlertSeverity::Critical,
                    "memory",
                    metrics.memory_usage_percent,
                ));
            } else if metrics.memory_usage_percent >= self.config.memory_usage_warning_percent {
                alerts.push(self.create_resource_alert(
                    "High Memory Usage",
                    format!("Memory usage is {:.1}% (threshold: {:.1}%)", metrics.memory_usage_percent, self.config.memory_usage_warning_percent),
                    AlertSeverity::Warning,
                    "memory",
                    metrics.memory_usage_percent,
                ));
            }

            // CPU usage alerts
            if metrics.cpu_usage_percent >= self.config.cpu_usage_critical_percent {
                alerts.push(self.create_resource_alert(
                    "Critical CPU Usage",
                    format!("CPU usage is {:.1}% (threshold: {:.1}%)", metrics.cpu_usage_percent, self.config.cpu_usage_critical_percent),
                    AlertSeverity::Critical,
                    "cpu",
                    metrics.cpu_usage_percent,
                ));
            } else if metrics.cpu_usage_percent >= self.config.cpu_usage_warning_percent {
                alerts.push(self.create_resource_alert(
                    "High CPU Usage",
                    format!("CPU usage is {:.1}% (threshold: {:.1}%)", metrics.cpu_usage_percent, self.config.cpu_usage_warning_percent),
                    AlertSeverity::Warning,
                    "cpu",
                    metrics.cpu_usage_percent,
                ));
            }

            // Slow query alerts
            if metrics.slow_queries_count > 0 {
                alerts.push(self.create_slow_query_alert(
                    "Slow Queries Detected",
                    format!("{} slow queries detected (threshold: {:.1}ms)", metrics.slow_queries_count, self.config.slow_query_threshold_ms),
                    AlertSeverity::Warning,
                    metrics.slow_queries_count,
                ));
            }

            // Endpoint-specific alerts
            for (endpoint, endpoint_metrics) in &metrics.endpoint_metrics {
                if endpoint_metrics.error_rate_percent() >= self.config.error_rate_critical_percent {
                    alerts.push(self.create_endpoint_alert(
                        &format!("Critical Error Rate on {}", endpoint),
                        format!("Error rate for {} is {:.2}%", endpoint, endpoint_metrics.error_rate_percent()),
                        AlertSeverity::Critical,
                        endpoint,
                        endpoint_metrics.error_rate_percent(),
                    ));
                }
            }

            // Send alerts through the framework
            if !alerts.is_empty() {
                let framework = framework.read().await;
                let alert_manager = framework.alert_manager();

                {
                    let mut manager = alert_manager.write().await;
                    let processed_alerts = manager.process_alerts(alerts);

                    // Send notifications for new alerts
                    if let Some(notification_manager) = framework.notification_manager().try_read() {
                        for alert in processed_alerts {
                            let _ = notification_manager.send_alert_notification(&alert).await;
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// Create latency alert
    fn create_latency_alert(&self, title: &str, description: String, severity: AlertSeverity, latency_ms: f64) -> Alert {
        use super::Alert;

        Alert {
            id: format!("perf_latency_{}_{}", severity.as_str(), Utc::now().timestamp()),
            title: title.to_string(),
            description,
            severity,
            category: AlertCategory::Performance,
            status: AlertStatus::Active,
            source: "performance_monitor".to_string(),
            labels: HashMap::from([
                ("metric".to_string(), "latency".to_string()),
                ("type".to_string(), "performance".to_string()),
            ]),
            annotations: HashMap::from([
                ("latency_ms".to_string(), latency_ms.to_string()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(latency_ms),
            threshold: Some(match severity {
                AlertSeverity::Warning => self.config.request_latency_warning_ms,
                AlertSeverity::Critical => self.config.request_latency_critical_ms,
                _ => 0.0,
            }),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("performance_latency".to_string()),
            fingerprint: format!("perf_latency_{}_{}", severity.as_str(), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create error rate alert
    fn create_error_rate_alert(&self, title: &str, description: String, severity: AlertSeverity, error_rate: f64) -> Alert {
        use super::Alert;

        Alert {
            id: format!("perf_error_rate_{}_{}", severity.as_str(), Utc::now().timestamp()),
            title: title.to_string(),
            description,
            severity,
            category: AlertCategory::Performance,
            status: AlertStatus::Active,
            source: "performance_monitor".to_string(),
            labels: HashMap::from([
                ("metric".to_string(), "error_rate".to_string()),
                ("type".to_string(), "performance".to_string()),
            ]),
            annotations: HashMap::from([
                ("error_rate_percent".to_string(), error_rate.to_string()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(error_rate),
            threshold: Some(match severity {
                AlertSeverity::Warning => self.config.error_rate_warning_percent,
                AlertSeverity::Critical => self.config.error_rate_critical_percent,
                _ => 0.0,
            }),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("performance_error_rate".to_string()),
            fingerprint: format!("perf_error_rate_{}_{}", severity.as_str(), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create throughput alert
    fn create_throughput_alert(&self, title: &str, description: String, severity: AlertSeverity, throughput: f64) -> Alert {
        use super::Alert;

        Alert {
            id: format!("perf_throughput_{}_{}", severity.as_str(), Utc::now().timestamp()),
            title: title.to_string(),
            description,
            severity,
            category: AlertCategory::Performance,
            status: AlertStatus::Active,
            source: "performance_monitor".to_string(),
            labels: HashMap::from([
                ("metric".to_string(), "throughput".to_string()),
                ("type".to_string(), "performance".to_string()),
            ]),
            annotations: HashMap::from([
                ("throughput_per_second".to_string(), throughput.to_string()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(throughput),
            threshold: Some(match severity {
                AlertSeverity::Warning => self.config.throughput_warning_per_second,
                AlertSeverity::Critical => self.config.throughput_critical_per_second,
                _ => 0.0,
            }),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("performance_throughput".to_string()),
            fingerprint: format!("perf_throughput_{}_{}", severity.as_str(), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create queue depth alert
    fn create_queue_alert(&self, title: &str, description: String, severity: AlertSeverity, queue_depth: u64) -> Alert {
        use super::Alert;

        Alert {
            id: format!("perf_queue_{}_{}", severity.as_str(), Utc::now().timestamp()),
            title: title.to_string(),
            description,
            severity,
            category: AlertCategory::Performance,
            status: AlertStatus::Active,
            source: "performance_monitor".to_string(),
            labels: HashMap::from([
                ("metric".to_string(), "queue_depth".to_string()),
                ("type".to_string(), "performance".to_string()),
            ]),
            annotations: HashMap::from([
                ("queue_depth".to_string(), queue_depth.to_string()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(queue_depth as f64),
            threshold: Some(match severity {
                AlertSeverity::Warning => self.config.queue_depth_warning as f64,
                AlertSeverity::Critical => self.config.queue_depth_critical as f64,
                _ => 0.0,
            }),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("performance_queue".to_string()),
            fingerprint: format!("perf_queue_{}_{}", severity.as_str(), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create database connection alert
    fn create_db_connection_alert(&self, title: &str, description: String, severity: AlertSeverity, connections: u32) -> Alert {
        use super::Alert;

        Alert {
            id: format!("perf_db_conn_{}_{}", severity.as_str(), Utc::now().timestamp()),
            title: title.to_string(),
            description,
            severity,
            category: AlertCategory::Performance,
            status: AlertStatus::Active,
            source: "performance_monitor".to_string(),
            labels: HashMap::from([
                ("metric".to_string(), "db_connections".to_string()),
                ("type".to_string(), "performance".to_string()),
            ]),
            annotations: HashMap::from([
                ("active_connections".to_string(), connections.to_string()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(connections as f64),
            threshold: Some(match severity {
                AlertSeverity::Warning => self.config.database_connection_warning as f64,
                AlertSeverity::Critical => self.config.database_connection_critical as f64,
                _ => 0.0,
            }),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("performance_db_connections".to_string()),
            fingerprint: format!("perf_db_conn_{}_{}", severity.as_str(), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create resource utilization alert
    fn create_resource_alert(&self, title: &str, description: String, severity: AlertSeverity, resource: &str, utilization: f64) -> Alert {
        use super::Alert;

        Alert {
            id: format!("perf_resource_{}_{}_{}", resource, severity.as_str(), Utc::now().timestamp()),
            title: title.to_string(),
            description,
            severity,
            category: AlertCategory::Performance,
            status: AlertStatus::Active,
            source: "performance_monitor".to_string(),
            labels: HashMap::from([
                ("resource".to_string(), resource.to_string()),
                ("type".to_string(), "performance".to_string()),
            ]),
            annotations: HashMap::from([
                ("utilization_percent".to_string(), utilization.to_string()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(utilization),
            threshold: Some(match resource {
                "memory" => match severity {
                    AlertSeverity::Warning => self.config.memory_usage_warning_percent,
                    AlertSeverity::Critical => self.config.memory_usage_critical_percent,
                    _ => 0.0,
                },
                "cpu" => match severity {
                    AlertSeverity::Warning => self.config.cpu_usage_warning_percent,
                    AlertSeverity::Critical => self.config.cpu_usage_critical_percent,
                    _ => 0.0,
                },
                _ => 0.0,
            }),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some(format!("performance_{}", resource)),
            fingerprint: format!("perf_resource_{}_{}_{}", resource, severity.as_str(), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create slow query alert
    fn create_slow_query_alert(&self, title: &str, description: String, severity: AlertSeverity, slow_queries: u64) -> Alert {
        use super::Alert;

        Alert {
            id: format!("perf_slow_query_{}_{}", severity.as_str(), Utc::now().timestamp()),
            title: title.to_string(),
            description,
            severity,
            category: AlertCategory::Performance,
            status: AlertStatus::Active,
            source: "performance_monitor".to_string(),
            labels: HashMap::from([
                ("metric".to_string(), "slow_queries".to_string()),
                ("type".to_string(), "performance".to_string()),
            ]),
            annotations: HashMap::from([
                ("slow_queries_count".to_string(), slow_queries.to_string()),
                ("threshold_ms".to_string(), self.config.slow_query_threshold_ms.to_string()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(slow_queries as f64),
            threshold: Some(0.0), // Any slow queries trigger this alert
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("performance_slow_queries".to_string()),
            fingerprint: format!("perf_slow_query_{}_{}", severity.as_str(), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create endpoint-specific alert
    fn create_endpoint_alert(&self, title: &str, description: String, severity: AlertSeverity, endpoint: &str, error_rate: f64) -> Alert {
        use super::Alert;

        Alert {
            id: format!("perf_endpoint_{}_{}_{}", endpoint.replace("/", "_"), severity.as_str(), Utc::now().timestamp()),
            title: title.to_string(),
            description,
            severity,
            category: AlertCategory::Performance,
            status: AlertStatus::Active,
            source: "performance_monitor".to_string(),
            labels: HashMap::from([
                ("endpoint".to_string(), endpoint.to_string()),
                ("metric".to_string(), "endpoint_error_rate".to_string()),
                ("type".to_string(), "performance".to_string()),
            ]),
            annotations: HashMap::from([
                ("error_rate_percent".to_string(), error_rate.to_string()),
                ("endpoint".to_string(), endpoint.to_string()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(error_rate),
            threshold: Some(self.config.error_rate_critical_percent),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("performance_endpoint_error_rate".to_string()),
            fingerprint: format!("perf_endpoint_{}_{}_{}", endpoint.replace("/", "_"), severity.as_str(), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Get recent performance metrics
    pub fn get_recent_metrics(&self, count: usize) -> Vec<&PerformanceMetrics> {
        self.metrics_history.iter().rev().take(count).collect()
    }

    /// Calculate performance trends
    pub fn calculate_trends(&self) -> PerformanceTrends {
        if self.metrics_history.len() < 2 {
            return PerformanceTrends::default();
        }

        let recent = self.get_recent_metrics(10);
        let older = self.get_recent_metrics(20).into_iter().rev().take(10).collect::<Vec<_>>();

        if recent.len() < 2 || older.len() < 2 {
            return PerformanceTrends::default();
        }

        let recent_avg_latency = recent.iter().map(|m| m.avg_response_time_ms).sum::<f64>() / recent.len() as f64;
        let older_avg_latency = older.iter().map(|m| m.avg_response_time_ms).sum::<f64>() / older.len() as f64;

        let recent_avg_error_rate = recent.iter().map(|m| m.error_rate_percent).sum::<f64>() / recent.len() as f64;
        let older_avg_error_rate = older.iter().map(|m| m.error_rate_percent).sum::<f64>() / older.len() as f64;

        PerformanceTrends {
            latency_trend: if recent_avg_latency > older_avg_latency * 1.1 {
                "increasing".to_string()
            } else if recent_avg_latency < older_avg_latency * 0.9 {
                "decreasing".to_string()
            } else {
                "stable".to_string()
            },
            error_rate_trend: if recent_avg_error_rate > older_avg_error_rate * 1.1 {
                "increasing".to_string()
            } else if recent_avg_error_rate < older_avg_error_rate * 0.9 {
                "decreasing".to_string()
            } else {
                "stable".to_string()
            },
            throughput_trend: "stable".to_string(), // Would need more complex calculation
        }
    }

    /// Get performance monitor configuration
    pub fn get_config(&self) -> &PerformanceMonitorConfig {
        &self.config
    }
}

/// Performance trends
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct PerformanceTrends {
    pub latency_trend: String,
    pub error_rate_trend: String,
    pub throughput_trend: String,
}

impl EndpointMetrics {
    /// Calculate error rate percentage
    pub fn error_rate_percent(&self) -> f64 {
        if self.request_count == 0 {
            0.0
        } else {
            (self.error_count as f64 / self.request_count as f64) * 100.0
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_performance_monitor_creation() {
        let config = PerformanceMonitorConfig::default();
        let monitor = PerformanceMonitor::new(config);
        assert!(monitor.config.enabled);
    }

    #[test]
    fn test_endpoint_metrics_error_rate() {
        let metrics = EndpointMetrics {
            request_count: 100,
            error_count: 5,
            avg_response_time_ms: 150.0,
            p95_response_time_ms: 200.0,
            throughput_per_second: 10.0,
        };

        assert_eq!(metrics.error_rate_percent(), 5.0);
    }

    #[test]
    fn test_endpoint_metrics_zero_requests() {
        let metrics = EndpointMetrics {
            request_count: 0,
            error_count: 0,
            avg_response_time_ms: 0.0,
            p95_response_time_ms: 0.0,
            throughput_per_second: 0.0,
        };

        assert_eq!(metrics.error_rate_percent(), 0.0);
    }
}