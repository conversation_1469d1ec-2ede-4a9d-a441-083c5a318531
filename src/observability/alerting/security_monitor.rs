//! # Security Monitor
//!
//! Monitors security-related events and generates alerts based on
//! authentication failures, suspicious patterns, and security violations.

use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, Duration};

use super::{<PERSON><PERSON>, <PERSON><PERSON>everity, AlertStatus, AlertCategory, AlertingFramework};

/// Security monitor configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityMonitorConfig {
    pub enabled: bool,
    pub monitoring_interval_seconds: u64,
    pub failed_auth_threshold: u32,
    pub suspicious_activity_threshold: u32,
    pub data_access_anomaly_threshold: f64,
    pub compliance_violation_threshold: u32,
    pub ip_blocklist_check_enabled: bool,
    pub rate_limiting_enabled: bool,
    pub max_requests_per_minute: u32,
    pub session_anomaly_detection_enabled: bool,
    pub log_analysis_enabled: bool,
    pub suspicious_patterns: Vec<String>,
    pub trusted_ips: Vec<String>,
}

impl Default for SecurityMonitorConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            monitoring_interval_seconds: 60,
            failed_auth_threshold: 5,
            suspicious_activity_threshold: 10,
            data_access_anomaly_threshold: 2.0, // Standard deviations
            compliance_violation_threshold: 3,
            ip_blocklist_check_enabled: true,
            rate_limiting_enabled: true,
            max_requests_per_minute: 100,
            session_anomaly_detection_enabled: true,
            log_analysis_enabled: true,
            suspicious_patterns: vec![
                "sql injection".to_string(),
                "xss".to_string(),
                "path traversal".to_string(),
                "command injection".to_string(),
            ],
            trusted_ips: vec![],
        }
    }
}

/// Security event types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SecurityEventType {
    FailedAuthentication,
    SuspiciousActivity,
    DataAccessAnomaly,
    ComplianceViolation,
    RateLimitExceeded,
    SessionAnomaly,
    SuspiciousLogPattern,
    UnauthorizedAccess,
    PrivilegeEscalation,
    DataExfiltration,
}

impl SecurityEventType {
    pub fn as_str(&self) -> &'static str {
        match self {
            SecurityEventType::FailedAuthentication => "failed_authentication",
            SecurityEventType::SuspiciousActivity => "suspicious_activity",
            SecurityEventType::DataAccessAnomaly => "data_access_anomaly",
            SecurityEventType::ComplianceViolation => "compliance_violation",
            SecurityEventType::RateLimitExceeded => "rate_limit_exceeded",
            SecurityEventType::SessionAnomaly => "session_anomaly",
            SecurityEventType::SuspiciousLogPattern => "suspicious_log_pattern",
            SecurityEventType::UnauthorizedAccess => "unauthorized_access",
            SecurityEventType::PrivilegeEscalation => "privilege_escalation",
            SecurityEventType::DataExfiltration => "data_exfiltration",
        }
    }
}

/// Security event
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityEvent {
    pub id: String,
    pub event_type: SecurityEventType,
    pub timestamp: DateTime<Utc>,
    pub source_ip: Option<String>,
    pub user_id: Option<String>,
    pub resource: Option<String>,
    pub severity: AlertSeverity,
    pub description: String,
    pub metadata: HashMap<String, String>,
    pub confidence_score: f64,
}

/// Security metrics snapshot
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityMetrics {
    pub timestamp: DateTime<Utc>,
    pub failed_auth_attempts: u64,
    pub suspicious_activities: u64,
    pub data_access_anomalies: u64,
    pub compliance_violations: u64,
    pub rate_limit_exceeded: u64,
    pub session_anomalies: u64,
    pub suspicious_log_patterns: u64,
    pub blocked_ips: HashSet<String>,
    pub active_sessions: u64,
    pub unique_ips_today: u64,
    pub top_suspicious_ips: HashMap<String, u64>,
    pub recent_events: Vec<SecurityEvent>,
}

/// Security monitor
pub struct SecurityMonitor {
    config: SecurityMonitorConfig,
    metrics_history: Vec<SecurityMetrics>,
    max_history_size: usize,
    recent_events: Vec<SecurityEvent>,
    max_events_size: usize,
    alerting_framework: Option<Arc<RwLock<AlertingFramework>>>,
}

impl SecurityMonitor {
    /// Create a new security monitor
    pub fn new(config: SecurityMonitorConfig) -> Self {
        Self {
            config,
            metrics_history: Vec::new(),
            max_history_size: 1000,
            recent_events: Vec::new(),
            max_events_size: 10000,
            alerting_framework: None,
        }
    }

    /// Set alerting framework reference
    pub fn set_alerting_framework(&mut self, framework: Arc<RwLock<AlertingFramework>>) {
        self.alerting_framework = Some(framework);
    }

    /// Record security event
    pub async fn record_security_event(&mut self, event: SecurityEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Store event
        if self.recent_events.len() >= self.max_events_size {
            self.recent_events.remove(0);
        }
        self.recent_events.push(event.clone());

        // Analyze event and generate alerts
        self.analyze_event(event).await?;

        Ok(())
    }

    /// Record security metrics
    pub async fn record_security_metrics(&mut self, metrics: SecurityMetrics) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Store metrics in history
        if self.metrics_history.len() >= self.max_history_size {
            self.metrics_history.remove(0);
        }
        self.metrics_history.push(metrics.clone());

        // Analyze metrics and generate alerts
        self.analyze_metrics(metrics).await?;

        Ok(())
    }

    /// Analyze security event and generate alerts
    async fn analyze_event(&self, event: SecurityEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if let Some(ref framework) = self.alerting_framework {
            let mut alerts = Vec::new();

            match event.event_type {
                SecurityEventType::FailedAuthentication => {
                    // Check if this IP has too many failed attempts
                    let failed_attempts = self.count_recent_failed_auth_from_ip(&event.source_ip);
                    if failed_attempts >= self.config.failed_auth_threshold {
                        alerts.push(self.create_failed_auth_alert(&event, failed_attempts));
                    }
                }
                SecurityEventType::SuspiciousActivity => {
                    let suspicious_count = self.count_recent_suspicious_from_ip(&event.source_ip);
                    if suspicious_count >= self.config.suspicious_activity_threshold {
                        alerts.push(self.create_suspicious_activity_alert(&event, suspicious_count));
                    }
                }
                SecurityEventType::DataAccessAnomaly => {
                    if event.confidence_score >= self.config.data_access_anomaly_threshold {
                        alerts.push(self.create_data_access_alert(&event));
                    }
                }
                SecurityEventType::ComplianceViolation => {
                    let violation_count = self.count_recent_violations();
                    if violation_count >= self.config.compliance_violation_threshold {
                        alerts.push(self.create_compliance_violation_alert(&event, violation_count));
                    }
                }
                SecurityEventType::RateLimitExceeded => {
                    alerts.push(self.create_rate_limit_alert(&event));
                }
                SecurityEventType::SessionAnomaly => {
                    if self.config.session_anomaly_detection_enabled {
                        alerts.push(self.create_session_anomaly_alert(&event));
                    }
                }
                SecurityEventType::SuspiciousLogPattern => {
                    alerts.push(self.create_log_pattern_alert(&event));
                }
                SecurityEventType::UnauthorizedAccess => {
                    alerts.push(self.create_unauthorized_access_alert(&event));
                }
                SecurityEventType::PrivilegeEscalation => {
                    alerts.push(self.create_privilege_escalation_alert(&event));
                }
                SecurityEventType::DataExfiltration => {
                    alerts.push(self.create_data_exfiltration_alert(&event));
                }
            }

            // Send alerts through the framework
            if !alerts.is_empty() {
                let framework = framework.read().await;
                let alert_manager = framework.alert_manager();

                {
                    let mut manager = alert_manager.write().await;
                    let processed_alerts = manager.process_alerts(alerts);

                    // Send notifications for new alerts
                    if let Some(notification_manager) = framework.notification_manager().try_read() {
                        for alert in processed_alerts {
                            let _ = notification_manager.send_alert_notification(&alert).await;
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// Analyze security metrics and generate alerts
    async fn analyze_metrics(&self, metrics: SecurityMetrics) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if let Some(ref framework) = self.alerting_framework {
            let mut alerts = Vec::new();

            // Check for spikes in security events
            if let Some(previous) = self.metrics_history.last() {
                let failed_auth_increase = metrics.failed_auth_attempts as f64 / (previous.failed_auth_attempts.max(1)) as f64;
                if failed_auth_increase >= 3.0 && metrics.failed_auth_attempts > 10 {
                    alerts.push(self.create_security_spike_alert(
                        "Failed Authentication Spike",
                        format!("Failed authentication attempts increased by {:.1}x to {}", failed_auth_increase, metrics.failed_auth_attempts),
                        AlertSeverity::Warning,
                        "failed_auth_spike",
                    ));
                }

                let suspicious_increase = metrics.suspicious_activities as f64 / (previous.suspicious_activities.max(1)) as f64;
                if suspicious_increase >= 2.0 && metrics.suspicious_activities > 5 {
                    alerts.push(self.create_security_spike_alert(
                        "Suspicious Activity Spike",
                        format!("Suspicious activities increased by {:.1}x to {}", suspicious_increase, metrics.suspicious_activities),
                        AlertSeverity::Critical,
                        "suspicious_activity_spike",
                    ));
                }
            }

            // Check blocked IPs
            if !metrics.blocked_ips.is_empty() {
                alerts.push(self.create_blocked_ips_alert(&metrics.blocked_ips));
            }

            // Send alerts through the framework
            if !alerts.is_empty() {
                let framework = framework.read().await;
                let alert_manager = framework.alert_manager();

                {
                    let mut manager = alert_manager.write().await;
                    let processed_alerts = manager.process_alerts(alerts);

                    // Send notifications for new alerts
                    if let Some(notification_manager) = framework.notification_manager().try_read() {
                        for alert in processed_alerts {
                            let _ = notification_manager.send_alert_notification(&alert).await;
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// Count recent failed auth attempts from IP
    fn count_recent_failed_auth_from_ip(&self, ip: &Option<String>) -> u32 {
        if let Some(ip_addr) = ip {
            let cutoff = Utc::now() - Duration::minutes(15);
            self.recent_events.iter()
                .filter(|e| {
                    e.timestamp >= cutoff &&
                    matches!(e.event_type, SecurityEventType::FailedAuthentication) &&
                    e.source_ip.as_ref() == Some(ip_addr)
                })
                .count() as u32
        } else {
            0
        }
    }

    /// Count recent suspicious activities from IP
    fn count_recent_suspicious_from_ip(&self, ip: &Option<String>) -> u32 {
        if let Some(ip_addr) = ip {
            let cutoff = Utc::now() - Duration::minutes(30);
            self.recent_events.iter()
                .filter(|e| {
                    e.timestamp >= cutoff &&
                    matches!(e.event_type, SecurityEventType::SuspiciousActivity) &&
                    e.source_ip.as_ref() == Some(ip_addr)
                })
                .count() as u32
        } else {
            0
        }
    }

    /// Count recent compliance violations
    fn count_recent_violations(&self) -> u32 {
        let cutoff = Utc::now() - Duration::hours(1);
        self.recent_events.iter()
            .filter(|e| {
                e.timestamp >= cutoff &&
                matches!(e.event_type, SecurityEventType::ComplianceViolation)
            })
            .count() as u32
    }

    /// Create failed authentication alert
    fn create_failed_auth_alert(&self, event: &SecurityEvent, count: u32) -> Alert {
        use super::Alert;

        Alert {
            id: format!("security_failed_auth_{}_{}", event.source_ip.as_deref().unwrap_or("unknown"), Utc::now().timestamp()),
            title: "Multiple Failed Authentication Attempts".to_string(),
            description: format!("{} failed authentication attempts from IP {} in the last 15 minutes", count, event.source_ip.as_deref().unwrap_or("unknown")),
            severity: AlertSeverity::Warning,
            category: AlertCategory::Security,
            status: AlertStatus::Active,
            source: "security_monitor".to_string(),
            labels: HashMap::from([
                ("event_type".to_string(), "failed_authentication".to_string()),
                ("ip".to_string(), event.source_ip.clone().unwrap_or_else(|| "unknown".to_string())),
                ("type".to_string(), "security".to_string()),
            ]),
            annotations: HashMap::from([
                ("attempt_count".to_string(), count.to_string()),
                ("user_id".to_string(), event.user_id.clone().unwrap_or_else(|| "unknown".to_string())),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(count as f64),
            threshold: Some(self.config.failed_auth_threshold as f64),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("security_failed_auth".to_string()),
            fingerprint: format!("security_failed_auth_{}_{}", event.source_ip.as_deref().unwrap_or("unknown"), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create suspicious activity alert
    fn create_suspicious_activity_alert(&self, event: &SecurityEvent, count: u32) -> Alert {
        use super::Alert;

        Alert {
            id: format!("security_suspicious_{}_{}", event.source_ip.as_deref().unwrap_or("unknown"), Utc::now().timestamp()),
            title: "Suspicious Activity Detected".to_string(),
            description: format!("{} suspicious activities detected from IP {} in the last 30 minutes", count, event.source_ip.as_deref().unwrap_or("unknown")),
            severity: AlertSeverity::Critical,
            category: AlertCategory::Security,
            status: AlertStatus::Active,
            source: "security_monitor".to_string(),
            labels: HashMap::from([
                ("event_type".to_string(), "suspicious_activity".to_string()),
                ("ip".to_string(), event.source_ip.clone().unwrap_or_else(|| "unknown".to_string())),
                ("type".to_string(), "security".to_string()),
            ]),
            annotations: HashMap::from([
                ("activity_count".to_string(), count.to_string()),
                ("description".to_string(), event.description.clone()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(count as f64),
            threshold: Some(self.config.suspicious_activity_threshold as f64),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("security_suspicious_activity".to_string()),
            fingerprint: format!("security_suspicious_{}_{}", event.source_ip.as_deref().unwrap_or("unknown"), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create data access anomaly alert
    fn create_data_access_alert(&self, event: &SecurityEvent) -> Alert {
        use super::Alert;

        Alert {
            id: format!("security_data_access_{}_{}", event.user_id.as_deref().unwrap_or("unknown"), Utc::now().timestamp()),
            title: "Data Access Anomaly Detected".to_string(),
            description: format!("Anomalous data access pattern detected: {}", event.description),
            severity: AlertSeverity::Warning,
            category: AlertCategory::Security,
            status: AlertStatus::Active,
            source: "security_monitor".to_string(),
            labels: HashMap::from([
                ("event_type".to_string(), "data_access_anomaly".to_string()),
                ("user_id".to_string(), event.user_id.clone().unwrap_or_else(|| "unknown".to_string())),
                ("resource".to_string(), event.resource.clone().unwrap_or_else(|| "unknown".to_string())),
                ("type".to_string(), "security".to_string()),
            ]),
            annotations: HashMap::from([
                ("confidence_score".to_string(), event.confidence_score.to_string()),
                ("description".to_string(), event.description.clone()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(event.confidence_score),
            threshold: Some(self.config.data_access_anomaly_threshold),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("security_data_access".to_string()),
            fingerprint: format!("security_data_access_{}_{}", event.user_id.as_deref().unwrap_or("unknown"), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create compliance violation alert
    fn create_compliance_violation_alert(&self, event: &SecurityEvent, count: u32) -> Alert {
        use super::Alert;

        Alert {
            id: format!("security_compliance_{}", Utc::now().timestamp()),
            title: "Multiple Compliance Violations".to_string(),
            description: format!("{} compliance violations detected in the last hour", count),
            severity: AlertSeverity::Critical,
            category: AlertCategory::Security,
            status: AlertStatus::Active,
            source: "security_monitor".to_string(),
            labels: HashMap::from([
                ("event_type".to_string(), "compliance_violation".to_string()),
                ("type".to_string(), "security".to_string()),
            ]),
            annotations: HashMap::from([
                ("violation_count".to_string(), count.to_string()),
                ("description".to_string(), event.description.clone()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(count as f64),
            threshold: Some(self.config.compliance_violation_threshold as f64),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("security_compliance_violation".to_string()),
            fingerprint: format!("security_compliance_{}", Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create rate limit alert
    fn create_rate_limit_alert(&self, event: &SecurityEvent) -> Alert {
        use super::Alert;

        Alert {
            id: format!("security_rate_limit_{}_{}", event.source_ip.as_deref().unwrap_or("unknown"), Utc::now().timestamp()),
            title: "Rate Limit Exceeded".to_string(),
            description: format!("Rate limit exceeded by IP {}: {}", event.source_ip.as_deref().unwrap_or("unknown"), event.description),
            severity: AlertSeverity::Warning,
            category: AlertCategory::Security,
            status: AlertStatus::Active,
            source: "security_monitor".to_string(),
            labels: HashMap::from([
                ("event_type".to_string(), "rate_limit_exceeded".to_string()),
                ("ip".to_string(), event.source_ip.clone().unwrap_or_else(|| "unknown".to_string())),
                ("type".to_string(), "security".to_string()),
            ]),
            annotations: HashMap::from([
                ("description".to_string(), event.description.clone()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: None,
            threshold: Some(self.config.max_requests_per_minute as f64),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("security_rate_limit".to_string()),
            fingerprint: format!("security_rate_limit_{}_{}", event.source_ip.as_deref().unwrap_or("unknown"), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create session anomaly alert
    fn create_session_anomaly_alert(&self, event: &SecurityEvent) -> Alert {
        use super::Alert;

        Alert {
            id: format!("security_session_{}_{}", event.user_id.as_deref().unwrap_or("unknown"), Utc::now().timestamp()),
            title: "Session Anomaly Detected".to_string(),
            description: format!("Suspicious session activity: {}", event.description),
            severity: AlertSeverity::Warning,
            category: AlertCategory::Security,
            status: AlertStatus::Active,
            source: "security_monitor".to_string(),
            labels: HashMap::from([
                ("event_type".to_string(), "session_anomaly".to_string()),
                ("user_id".to_string(), event.user_id.clone().unwrap_or_else(|| "unknown".to_string())),
                ("type".to_string(), "security".to_string()),
            ]),
            annotations: HashMap::from([
                ("description".to_string(), event.description.clone()),
                ("confidence_score".to_string(), event.confidence_score.to_string()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(event.confidence_score),
            threshold: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("security_session_anomaly".to_string()),
            fingerprint: format!("security_session_{}_{}", event.user_id.as_deref().unwrap_or("unknown"), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create log pattern alert
    fn create_log_pattern_alert(&self, event: &SecurityEvent) -> Alert {
        use super::Alert;

        Alert {
            id: format!("security_log_pattern_{}", Utc::now().timestamp()),
            title: "Suspicious Log Pattern Detected".to_string(),
            description: format!("Suspicious pattern found in logs: {}", event.description),
            severity: AlertSeverity::Warning,
            category: AlertCategory::Security,
            status: AlertStatus::Active,
            source: "security_monitor".to_string(),
            labels: HashMap::from([
                ("event_type".to_string(), "suspicious_log_pattern".to_string()),
                ("type".to_string(), "security".to_string()),
            ]),
            annotations: HashMap::from([
                ("description".to_string(), event.description.clone()),
                ("source_ip".to_string(), event.source_ip.clone().unwrap_or_else(|| "unknown".to_string())),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: None,
            threshold: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("security_log_pattern".to_string()),
            fingerprint: format!("security_log_pattern_{}", Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create unauthorized access alert
    fn create_unauthorized_access_alert(&self, event: &SecurityEvent) -> Alert {
        use super::Alert;

        Alert {
            id: format!("security_unauthorized_{}_{}", event.source_ip.as_deref().unwrap_or("unknown"), Utc::now().timestamp()),
            title: "Unauthorized Access Attempt".to_string(),
            description: format!("Unauthorized access attempt: {}", event.description),
            severity: AlertSeverity::Critical,
            category: AlertCategory::Security,
            status: AlertStatus::Active,
            source: "security_monitor".to_string(),
            labels: HashMap::from([
                ("event_type".to_string(), "unauthorized_access".to_string()),
                ("ip".to_string(), event.source_ip.clone().unwrap_or_else(|| "unknown".to_string())),
                ("resource".to_string(), event.resource.clone().unwrap_or_else(|| "unknown".to_string())),
                ("type".to_string(), "security".to_string()),
            ]),
            annotations: HashMap::from([
                ("description".to_string(), event.description.clone()),
                ("user_id".to_string(), event.user_id.clone().unwrap_or_else(|| "unknown".to_string())),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: None,
            threshold: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("security_unauthorized_access".to_string()),
            fingerprint: format!("security_unauthorized_{}_{}", event.source_ip.as_deref().unwrap_or("unknown"), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create privilege escalation alert
    fn create_privilege_escalation_alert(&self, event: &SecurityEvent) -> Alert {
        use super::Alert;

        Alert {
            id: format!("security_privilege_{}_{}", event.user_id.as_deref().unwrap_or("unknown"), Utc::now().timestamp()),
            title: "Privilege Escalation Attempt".to_string(),
            description: format!("Privilege escalation attempt detected: {}", event.description),
            severity: AlertSeverity::Critical,
            category: AlertCategory::Security,
            status: AlertStatus::Active,
            source: "security_monitor".to_string(),
            labels: HashMap::from([
                ("event_type".to_string(), "privilege_escalation".to_string()),
                ("user_id".to_string(), event.user_id.clone().unwrap_or_else(|| "unknown".to_string())),
                ("type".to_string(), "security".to_string()),
            ]),
            annotations: HashMap::from([
                ("description".to_string(), event.description.clone()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: None,
            threshold: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("security_privilege_escalation".to_string()),
            fingerprint: format!("security_privilege_{}_{}", event.user_id.as_deref().unwrap_or("unknown"), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create data exfiltration alert
    fn create_data_exfiltration_alert(&self, event: &SecurityEvent) -> Alert {
        use super::Alert;

        Alert {
            id: format!("security_exfiltration_{}_{}", event.user_id.as_deref().unwrap_or("unknown"), Utc::now().timestamp()),
            title: "Data Exfiltration Detected".to_string(),
            description: format!("Potential data exfiltration: {}", event.description),
            severity: AlertSeverity::Critical,
            category: AlertCategory::Security,
            status: AlertStatus::Active,
            source: "security_monitor".to_string(),
            labels: HashMap::from([
                ("event_type".to_string(), "data_exfiltration".to_string()),
                ("user_id".to_string(), event.user_id.clone().unwrap_or_else(|| "unknown".to_string())),
                ("type".to_string(), "security".to_string()),
            ]),
            annotations: HashMap::from([
                ("description".to_string(), event.description.clone()),
                ("resource".to_string(), event.resource.clone().unwrap_or_else(|| "unknown".to_string())),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: None,
            threshold: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("security_data_exfiltration".to_string()),
            fingerprint: format!("security_exfiltration_{}_{}", event.user_id.as_deref().unwrap_or("unknown"), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create security spike alert
    fn create_security_spike_alert(&self, title: &str, description: String, severity: AlertSeverity, alert_type: &str) -> Alert {
        use super::Alert;

        Alert {
            id: format!("security_spike_{}_{}", alert_type, Utc::now().timestamp()),
            title: title.to_string(),
            description,
            severity,
            category: AlertCategory::Security,
            status: AlertStatus::Active,
            source: "security_monitor".to_string(),
            labels: HashMap::from([
                ("event_type".to_string(), "security_spike".to_string()),
                ("spike_type".to_string(), alert_type.to_string()),
                ("type".to_string(), "security".to_string()),
            ]),
            annotations: HashMap::from([
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: None,
            threshold: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some(format!("security_spike_{}", alert_type)),
            fingerprint: format!("security_spike_{}_{}", alert_type, Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create blocked IPs alert
    fn create_blocked_ips_alert(&self, blocked_ips: &HashSet<String>) -> Alert {
        use super::Alert;

        let ip_list = blocked_ips.iter().take(5).cloned().collect::<Vec<_>>().join(", ");
        let description = if blocked_ips.len() > 5 {
            format!("{} IPs blocked (showing first 5): {}", blocked_ips.len(), ip_list)
        } else {
            format!("{} IPs blocked: {}", blocked_ips.len(), ip_list)
        };

        Alert {
            id: format!("security_blocked_ips_{}", Utc::now().timestamp()),
            title: "IPs Blocked Due to Security Violations".to_string(),
            description,
            severity: AlertSeverity::Warning,
            category: AlertCategory::Security,
            status: AlertStatus::Active,
            source: "security_monitor".to_string(),
            labels: HashMap::from([
                ("event_type".to_string(), "blocked_ips".to_string()),
                ("type".to_string(), "security".to_string()),
            ]),
            annotations: HashMap::from([
                ("blocked_count".to_string(), blocked_ips.len().to_string()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(blocked_ips.len() as f64),
            threshold: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("security_blocked_ips".to_string()),
            fingerprint: format!("security_blocked_ips_{}", Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Get recent security events
    pub fn get_recent_events(&self, count: usize) -> Vec<&SecurityEvent> {
        self.recent_events.iter().rev().take(count).collect()
    }

    /// Get security monitor configuration
    pub fn get_config(&self) -> &SecurityMonitorConfig {
        &self.config
    }

    /// Check if IP is trusted
    pub fn is_trusted_ip(&self, ip: &str) -> bool {
        self.config.trusted_ips.contains(&ip.to_string())
    }

    /// Check if IP is blocked
    pub fn is_blocked_ip(&self, ip: &str) -> bool {
        if let Some(metrics) = self.metrics_history.last() {
            metrics.blocked_ips.contains(ip)
        } else {
            false
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_security_monitor_creation() {
        let config = SecurityMonitorConfig::default();
        let monitor = SecurityMonitor::new(config);
        assert!(monitor.config.enabled);
    }

    #[test]
    fn test_security_event_creation() {
        let event = SecurityEvent {
            id: "test_event".to_string(),
            event_type: SecurityEventType::FailedAuthentication,
            timestamp: Utc::now(),
            source_ip: Some("***********".to_string()),
            user_id: Some("user123".to_string()),
            resource: Some("/api/login".to_string()),
            severity: AlertSeverity::Warning,
            description: "Invalid password".to_string(),
            metadata: HashMap::new(),
            confidence_score: 0.8,
        };

        assert_eq!(event.event_type.as_str(), "failed_authentication");
        assert_eq!(event.severity, AlertSeverity::Warning);
    }

    #[test]
    fn test_security_metrics_creation() {
        let mut blocked_ips = HashSet::new();
        blocked_ips.insert("***********00".to_string());

        let metrics = SecurityMetrics {
            timestamp: Utc::now(),
            failed_auth_attempts: 15,
            suspicious_activities: 8,
            data_access_anomalies: 3,
            compliance_violations: 2,
            rate_limit_exceeded: 5,
            session_anomalies: 1,
            suspicious_log_patterns: 0,
            blocked_ips,
            active_sessions: 45,
            unique_ips_today: 123,
            top_suspicious_ips: HashMap::new(),
            recent_events: vec![],
        };

        assert_eq!(metrics.failed_auth_attempts, 15);
        assert_eq!(metrics.blocked_ips.len(), 1);
    }
}