//! # System Health Monitor
//!
//! Monitors system health metrics and generates alerts based on
//! resource utilization, service availability, and component health.

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, Duration};
use sysinfo::{System, Cpu, Process, Disk, NetworkData};

use super::{<PERSON>ert, AlertSeverity, AlertStatus, AlertCategory, AlertingFramework};

/// System health monitor configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemHealthMonitorConfig {
    pub enabled: bool,
    pub monitoring_interval_seconds: u64,
    pub cpu_threshold_warning: f64,
    pub cpu_threshold_critical: f64,
    pub memory_threshold_warning: f64,
    pub memory_threshold_critical: f64,
    pub disk_threshold_warning: f64,
    pub disk_threshold_critical: f64,
    pub network_threshold_warning: f64,
    pub network_threshold_critical: f64,
    pub process_monitoring_enabled: bool,
    pub critical_processes: Vec<String>,
    pub service_availability_check_enabled: bool,
    pub service_endpoints: Vec<String>,
}

impl Default for SystemHealthMonitorConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            monitoring_interval_seconds: 30,
            cpu_threshold_warning: 70.0,
            cpu_threshold_critical: 90.0,
            memory_threshold_warning: 75.0,
            memory_threshold_critical: 95.0,
            disk_threshold_warning: 80.0,
            disk_threshold_critical: 95.0,
            network_threshold_warning: 80.0,
            network_threshold_critical: 95.0,
            process_monitoring_enabled: true,
            critical_processes: vec![
                "infinitium-signal".to_string(),
                "prometheus".to_string(),
                "grafana".to_string(),
                "postgresql".to_string(),
            ],
            service_availability_check_enabled: true,
            service_endpoints: vec![
                "http://localhost:8080/health".to_string(),
                "http://localhost:9090/-/healthy".to_string(),
                "http://localhost:3000/api/health".to_string(),
            ],
        }
    }
}

/// System health status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemHealthStatus {
    pub timestamp: DateTime<Utc>,
    pub overall_health: SystemHealthScore,
    pub cpu_health: ComponentHealth,
    pub memory_health: ComponentHealth,
    pub disk_health: ComponentHealth,
    pub network_health: ComponentHealth,
    pub process_health: HashMap<String, ProcessHealth>,
    pub service_health: HashMap<String, ServiceHealth>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentHealth {
    pub status: HealthStatus,
    pub utilization_percent: f64,
    pub threshold_warning: f64,
    pub threshold_critical: f64,
    pub details: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessHealth {
    pub pid: Option<u32>,
    pub status: HealthStatus,
    pub cpu_percent: f64,
    pub memory_percent: f64,
    pub running: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceHealth {
    pub endpoint: String,
    pub status: HealthStatus,
    pub response_time_ms: Option<u64>,
    pub last_check: DateTime<Utc>,
    pub consecutive_failures: u32,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum HealthStatus {
    Healthy,
    Warning,
    Critical,
    Unknown,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum SystemHealthScore {
    Excellent = 5,
    Good = 4,
    Fair = 3,
    Poor = 2,
    Critical = 1,
}

impl HealthStatus {
    pub fn as_str(&self) -> &'static str {
        match self {
            HealthStatus::Healthy => "healthy",
            HealthStatus::Warning => "warning",
            HealthStatus::Critical => "critical",
            HealthStatus::Unknown => "unknown",
        }
    }
}

/// System health monitor
pub struct SystemHealthMonitor {
    config: SystemHealthMonitorConfig,
    system: System,
    last_check: DateTime<Utc>,
    alerting_framework: Option<Arc<RwLock<AlertingFramework>>>,
}

impl SystemHealthMonitor {
    /// Create a new system health monitor
    pub fn new(config: SystemHealthMonitorConfig) -> Self {
        let mut system = System::new_all();
        system.refresh_all();

        Self {
            config,
            system,
            last_check: Utc::now(),
            alerting_framework: None,
        }
    }

    /// Set alerting framework reference
    pub fn set_alerting_framework(&mut self, framework: Arc<RwLock<AlertingFramework>>) {
        self.alerting_framework = Some(framework);
    }

    /// Perform health check and return status
    pub async fn check_health(&mut self) -> Result<SystemHealthStatus, Box<dyn std::error::Error + Send + Sync>> {
        self.system.refresh_all();
        let now = Utc::now();

        let cpu_health = self.check_cpu_health();
        let memory_health = self.check_memory_health();
        let disk_health = self.check_disk_health();
        let network_health = self.check_network_health();
        let process_health = self.check_process_health();
        let service_health = self.check_service_health().await;

        let overall_health = self.calculate_overall_health(&cpu_health, &memory_health, &disk_health, &network_health);

        let status = SystemHealthStatus {
            timestamp: now,
            overall_health,
            cpu_health,
            memory_health,
            disk_health,
            network_health,
            process_health,
            service_health,
        };

        // Generate alerts based on health status
        self.generate_alerts(&status).await?;

        self.last_check = now;
        Ok(status)
    }

    /// Check CPU health
    fn check_cpu_health(&self) -> ComponentHealth {
        let cpu_usage = self.system.global_cpu_info().cpu_usage() as f64;

        let status = if cpu_usage >= self.config.cpu_threshold_critical {
            HealthStatus::Critical
        } else if cpu_usage >= self.config.cpu_threshold_warning {
            HealthStatus::Warning
        } else {
            HealthStatus::Healthy
        };

        let mut details = HashMap::new();
        details.insert("cpu_count".to_string(), self.system.cpus().len().to_string());
        details.insert("cpu_frequency".to_string(), format!("{:.2}", self.system.global_cpu_info().frequency() as f64 / 1000.0));

        ComponentHealth {
            status,
            utilization_percent: cpu_usage,
            threshold_warning: self.config.cpu_threshold_warning,
            threshold_critical: self.config.cpu_threshold_critical,
            details,
        }
    }

    /// Check memory health
    fn check_memory_health(&self) -> ComponentHealth {
        let total_memory = self.system.total_memory() as f64;
        let used_memory = self.system.used_memory() as f64;
        let memory_usage = if total_memory > 0.0 {
            (used_memory / total_memory) * 100.0
        } else {
            0.0
        };

        let status = if memory_usage >= self.config.memory_threshold_critical {
            HealthStatus::Critical
        } else if memory_usage >= self.config.memory_threshold_warning {
            HealthStatus::Warning
        } else {
            HealthStatus::Healthy
        };

        let mut details = HashMap::new();
        details.insert("total_memory_mb".to_string(), format!("{:.2}", total_memory / 1024.0 / 1024.0));
        details.insert("used_memory_mb".to_string(), format!("{:.2}", used_memory / 1024.0 / 1024.0));
        details.insert("available_memory_mb".to_string(), format!("{:.2}", (total_memory - used_memory) / 1024.0 / 1024.0));

        ComponentHealth {
            status,
            utilization_percent: memory_usage,
            threshold_warning: self.config.memory_threshold_warning,
            threshold_critical: self.config.memory_threshold_critical,
            details,
        }
    }

    /// Check disk health
    fn check_disk_health(&self) -> ComponentHealth {
        let mut total_space = 0u64;
        let mut available_space = 0u64;

        for disk in self.system.disks() {
            total_space += disk.total_space();
            available_space += disk.available_space();
        }

        let used_space = total_space.saturating_sub(available_space);
        let disk_usage = if total_space > 0 {
            (used_space as f64 / total_space as f64) * 100.0
        } else {
            0.0
        };

        let status = if disk_usage >= self.config.disk_threshold_critical {
            HealthStatus::Critical
        } else if disk_usage >= self.config.disk_threshold_warning {
            HealthStatus::Warning
        } else {
            HealthStatus::Healthy
        };

        let mut details = HashMap::new();
        details.insert("total_space_gb".to_string(), format!("{:.2}", total_space as f64 / 1024.0 / 1024.0 / 1024.0));
        details.insert("used_space_gb".to_string(), format!("{:.2}", used_space as f64 / 1024.0 / 1024.0 / 1024.0));
        details.insert("available_space_gb".to_string(), format!("{:.2}", available_space as f64 / 1024.0 / 1024.0 / 1024.0));

        ComponentHealth {
            status,
            utilization_percent: disk_usage,
            threshold_warning: self.config.disk_threshold_warning,
            threshold_critical: self.config.disk_threshold_critical,
            details,
        }
    }

    /// Check network health
    fn check_network_health(&self) -> ComponentHealth {
        let mut total_received = 0u64;
        let mut total_transmitted = 0u64;

        for network in self.system.networks() {
            let data = network.1;
            total_received += data.received();
            total_transmitted += data.transmitted();
        }

        // Calculate network utilization (simplified - in practice you'd track over time)
        let network_usage = if total_received + total_transmitted > 0 {
            // This is a simplified calculation - real network monitoring would track rates
            50.0 // Placeholder
        } else {
            0.0
        };

        let status = if network_usage >= self.config.network_threshold_critical {
            HealthStatus::Critical
        } else if network_usage >= self.config.network_threshold_warning {
            HealthStatus::Warning
        } else {
            HealthStatus::Healthy
        };

        let mut details = HashMap::new();
        details.insert("bytes_received".to_string(), total_received.to_string());
        details.insert("bytes_transmitted".to_string(), total_transmitted.to_string());

        ComponentHealth {
            status,
            utilization_percent: network_usage,
            threshold_warning: self.config.network_threshold_warning,
            threshold_critical: self.config.network_threshold_critical,
            details,
        }
    }

    /// Check process health
    fn check_process_health(&self) -> HashMap<String, ProcessHealth> {
        let mut process_health = HashMap::new();

        for process_name in &self.config.critical_processes {
            let mut health = ProcessHealth {
                pid: None,
                status: HealthStatus::Unknown,
                cpu_percent: 0.0,
                memory_percent: 0.0,
                running: false,
            };

            // Find the process
            for (pid, process) in self.system.processes() {
                if process.name().contains(process_name) {
                    health.pid = Some(*pid);
                    health.cpu_percent = process.cpu_usage() as f64;
                    health.memory_percent = (process.memory() as f64 / self.system.total_memory() as f64) * 100.0;
                    health.running = true;
                    health.status = HealthStatus::Healthy;
                    break;
                }
            }

            if !health.running {
                health.status = HealthStatus::Critical;
            }

            process_health.insert(process_name.clone(), health);
        }

        process_health
    }

    /// Check service health
    async fn check_service_health(&self) -> HashMap<String, ServiceHealth> {
        let mut service_health = HashMap::new();

        if !self.config.service_availability_check_enabled {
            return service_health;
        }

        let client = reqwest::Client::new();

        for endpoint in &self.config.service_endpoints {
            let mut health = ServiceHealth {
                endpoint: endpoint.clone(),
                status: HealthStatus::Unknown,
                response_time_ms: None,
                last_check: Utc::now(),
                consecutive_failures: 0,
            };

            let start_time = std::time::Instant::now();

            match client
                .get(endpoint)
                .timeout(std::time::Duration::from_secs(5))
                .send()
                .await
            {
                Ok(response) => {
                    let response_time = start_time.elapsed().as_millis() as u64;
                    health.response_time_ms = Some(response_time);

                    if response.status().is_success() {
                        health.status = HealthStatus::Healthy;
                        health.consecutive_failures = 0;
                    } else {
                        health.status = HealthStatus::Critical;
                        health.consecutive_failures += 1;
                    }
                }
                Err(_) => {
                    health.status = HealthStatus::Critical;
                    health.consecutive_failures += 1;
                }
            }

            service_health.insert(endpoint.clone(), health);
        }

        service_health
    }

    /// Calculate overall system health
    fn calculate_overall_health(
        &self,
        cpu: &ComponentHealth,
        memory: &ComponentHealth,
        disk: &ComponentHealth,
        network: &ComponentHealth,
    ) -> SystemHealthScore {
        let components = vec![cpu, memory, disk, network];
        let critical_count = components.iter().filter(|c| c.status == HealthStatus::Critical).count();
        let warning_count = components.iter().filter(|c| c.status == HealthStatus::Warning).count();

        if critical_count > 0 {
            SystemHealthScore::Critical
        } else if warning_count > 1 {
            SystemHealthScore::Poor
        } else if warning_count > 0 {
            SystemHealthScore::Fair
        } else {
            SystemHealthScore::Excellent
        }
    }

    /// Generate alerts based on health status
    async fn generate_alerts(&self, status: &SystemHealthStatus) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if let Some(ref framework) = self.alerting_framework {
            let mut alerts = Vec::new();

            // CPU alerts
            if status.cpu_health.status == HealthStatus::Critical {
                alerts.push(self.create_resource_alert(
                    "Critical CPU Usage",
                    format!("CPU usage is {:.1}% (threshold: {:.1}%)", status.cpu_health.utilization_percent, self.config.cpu_threshold_critical),
                    AlertSeverity::Critical,
                    "cpu",
                    status.cpu_health.utilization_percent,
                ));
            } else if status.cpu_health.status == HealthStatus::Warning {
                alerts.push(self.create_resource_alert(
                    "High CPU Usage",
                    format!("CPU usage is {:.1}% (threshold: {:.1}%)", status.cpu_health.utilization_percent, self.config.cpu_threshold_warning),
                    AlertSeverity::Warning,
                    "cpu",
                    status.cpu_health.utilization_percent,
                ));
            }

            // Memory alerts
            if status.memory_health.status == HealthStatus::Critical {
                alerts.push(self.create_resource_alert(
                    "Critical Memory Usage",
                    format!("Memory usage is {:.1}% (threshold: {:.1}%)", status.memory_health.utilization_percent, self.config.memory_threshold_critical),
                    AlertSeverity::Critical,
                    "memory",
                    status.memory_health.utilization_percent,
                ));
            } else if status.memory_health.status == HealthStatus::Warning {
                alerts.push(self.create_resource_alert(
                    "High Memory Usage",
                    format!("Memory usage is {:.1}% (threshold: {:.1}%)", status.memory_health.utilization_percent, self.config.memory_threshold_warning),
                    AlertSeverity::Warning,
                    "memory",
                    status.memory_health.utilization_percent,
                ));
            }

            // Disk alerts
            if status.disk_health.status == HealthStatus::Critical {
                alerts.push(self.create_resource_alert(
                    "Critical Disk Usage",
                    format!("Disk usage is {:.1}% (threshold: {:.1}%)", status.disk_health.utilization_percent, self.config.disk_threshold_critical),
                    AlertSeverity::Critical,
                    "disk",
                    status.disk_health.utilization_percent,
                ));
            } else if status.disk_health.status == HealthStatus::Warning {
                alerts.push(self.create_resource_alert(
                    "High Disk Usage",
                    format!("Disk usage is {:.1}% (threshold: {:.1}%)", status.disk_health.utilization_percent, self.config.disk_threshold_warning),
                    AlertSeverity::Warning,
                    "disk",
                    status.disk_health.utilization_percent,
                ));
            }

            // Process alerts
            for (process_name, process_health) in &status.process_health {
                if !process_health.running {
                    alerts.push(self.create_process_alert(process_name, process_health));
                }
            }

            // Service alerts
            for (endpoint, service_health) in &status.service_health {
                if service_health.status == HealthStatus::Critical && service_health.consecutive_failures >= 3 {
                    alerts.push(self.create_service_alert(endpoint, service_health));
                }
            }

            // Send alerts through the framework
            if !alerts.is_empty() {
                let framework = framework.read().await;
                let alert_manager = framework.alert_manager();

                {
                    let mut manager = alert_manager.write().await;
                    let processed_alerts = manager.process_alerts(alerts);

                    // Send notifications for new alerts
                    if let Some(notification_manager) = framework.notification_manager().try_read() {
                        for alert in processed_alerts {
                            let _ = notification_manager.send_alert_notification(&alert).await;
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// Create resource utilization alert
    fn create_resource_alert(&self, title: &str, description: String, severity: AlertSeverity, resource: &str, value: f64) -> Alert {
        use super::Alert;

        Alert {
            id: format!("system_{}_{}", resource, Utc::now().timestamp()),
            title: title.to_string(),
            description,
            severity,
            category: AlertCategory::SystemHealth,
            status: AlertStatus::Active,
            source: "system_health_monitor".to_string(),
            labels: HashMap::from([
                ("resource".to_string(), resource.to_string()),
                ("monitor".to_string(), "system_health".to_string()),
            ]),
            annotations: HashMap::from([
                ("value".to_string(), value.to_string()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: Some(value),
            threshold: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some(format!("system_{}_high", resource)),
            fingerprint: format!("system_{}_usage_{}", resource, Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create process health alert
    fn create_process_alert(&self, process_name: &str, health: &ProcessHealth) -> Alert {
        use super::Alert;

        Alert {
            id: format!("process_{}_{}", process_name, Utc::now().timestamp()),
            title: format!("Process {} Not Running", process_name),
            description: format!("Critical process '{}' is not running", process_name),
            severity: AlertSeverity::Critical,
            category: AlertCategory::SystemHealth,
            status: AlertStatus::Active,
            source: "system_health_monitor".to_string(),
            labels: HashMap::from([
                ("process".to_string(), process_name.to_string()),
                ("type".to_string(), "process_health".to_string()),
            ]),
            annotations: HashMap::from([
                ("process_name".to_string(), process_name.to_string()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: None,
            threshold: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("process_health".to_string()),
            fingerprint: format!("process_{}_down_{}", process_name, Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Create service health alert
    fn create_service_alert(&self, endpoint: &str, health: &ServiceHealth) -> Alert {
        use super::Alert;

        Alert {
            id: format!("service_{}_{}", endpoint.replace("/", "_"), Utc::now().timestamp()),
            title: "Service Unavailable".to_string(),
            description: format!("Service endpoint '{}' is unavailable ({} consecutive failures)", endpoint, health.consecutive_failures),
            severity: AlertSeverity::Critical,
            category: AlertCategory::SystemHealth,
            status: AlertStatus::Active,
            source: "system_health_monitor".to_string(),
            labels: HashMap::from([
                ("endpoint".to_string(), endpoint.to_string()),
                ("type".to_string(), "service_health".to_string()),
            ]),
            annotations: HashMap::from([
                ("endpoint".to_string(), endpoint.to_string()),
                ("consecutive_failures".to_string(), health.consecutive_failures.to_string()),
                ("timestamp".to_string(), Utc::now().to_rfc3339()),
            ]),
            value: health.response_time_ms.map(|t| t as f64),
            threshold: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("service_availability".to_string()),
            fingerprint: format!("service_{}_unavailable_{}", endpoint.replace("/", "_"), Utc::now().timestamp()),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Get current health status
    pub fn get_health_status(&self) -> &SystemHealthMonitorConfig {
        &self.config
    }
}