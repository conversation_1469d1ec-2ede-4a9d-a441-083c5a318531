//! # Alerting System Tests
//!
//! Integration tests for the alerting and anomaly detection system.

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use chrono::Utc;

use super::{AlertingFramework, AlertingFrameworkConfig, AlertSeverity, AlertCategory, AlertStatus};

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_alerting_framework_initialization() {
        let config = AlertingFrameworkConfig::default();
        let framework = AlertingFramework::new(config);

        // Test that all components are initialized
        assert!(framework.alert_manager().try_read().is_some());
        assert!(framework.anomaly_detector().try_read().is_some());
        assert!(framework.notification_manager().try_read().is_some());
        assert!(framework.analytics_manager().try_read().is_some());
    }

    #[tokio::test]
    async fn test_alert_lifecycle() {
        let config = AlertingFrameworkConfig::default();
        let framework = AlertingFramework::new(config);

        // Initialize the framework
        framework.initialize().await.unwrap();

        // Create a test alert
        let test_alert = super::Alert {
            id: "test_alert_1".to_string(),
            title: "Test Alert".to_string(),
            description: "This is a test alert".to_string(),
            severity: AlertSeverity::Warning,
            category: AlertCategory::SystemHealth,
            status: AlertStatus::Active,
            source: "test".to_string(),
            labels: HashMap::new(),
            annotations: HashMap::new(),
            value: Some(85.0),
            threshold: Some(80.0),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: None,
            fingerprint: "test_fingerprint".to_string(),
            count: 1,
            last_occurrence: Utc::now(),
        };

        // Test alert manager operations
        {
            let alert_manager = framework.alert_manager();
            let mut manager = alert_manager.write().await;

            // Add alert
            manager.alerts.insert(test_alert.id.clone(), test_alert.clone());

            // Test retrieval
            let retrieved = manager.get_alert(&test_alert.id);
            assert!(retrieved.is_some());
            assert_eq!(retrieved.unwrap().title, "Test Alert");

            // Test acknowledge
            let acknowledged = manager.acknowledge_alert(&test_alert.id, "test_user");
            assert!(acknowledged);

            // Test resolve
            let resolved = manager.resolve_alert(&test_alert.id);
            assert!(resolved);
        }
    }

    #[tokio::test]
    async fn test_anomaly_detection() {
        let config = AlertingFrameworkConfig::default();
        let framework = AlertingFramework::new(config);

        // Initialize the framework
        framework.initialize().await.unwrap();

        // Test anomaly detector
        {
            let anomaly_detector = framework.anomaly_detector();
            let mut detector = anomaly_detector.write().await;

            // Add some test data points
            for i in 0..150 {
                let value = 100.0 + (i as f64 * 0.1); // Normal trend
                detector.add_metric_data(
                    "test_metric",
                    value,
                    Utc::now(),
                    HashMap::new(),
                );
            }

            // Test anomaly detection
            let anomalies = detector.detect_anomalies("test_metric", 115.0, Utc::now());
            assert!(anomalies.is_empty() || anomalies.iter().all(|a| !a.is_anomaly()));
        }
    }

    #[tokio::test]
    async fn test_notification_system() {
        let config = AlertingFrameworkConfig::default();
        let framework = AlertingFramework::new(config);

        // Initialize the framework
        framework.initialize().await.unwrap();

        // Test notification manager
        {
            let notification_manager = framework.notification_manager();
            let manager = notification_manager.read().await;

            // Test rate limiter
            let mut rate_limiter = super::notification::RateLimiter::new(
                super::notification::RateLimitingConfig::default()
            );

            // Should not be throttled initially
            assert!(!rate_limiter.should_throttle());
        }
    }

    #[tokio::test]
    async fn test_alert_analytics() {
        let config = AlertingFrameworkConfig::default();
        let framework = AlertingFramework::new(config);

        // Initialize the framework
        framework.initialize().await.unwrap();

        // Test analytics manager
        {
            let analytics_manager = framework.analytics_manager();
            let manager = analytics_manager.read().await;

            // Test pattern detector
            let mut pattern_detector = super::alert_analytics::PatternDetector::new();
            pattern_detector.record_alert(&super::Alert {
                id: "test_alert".to_string(),
                title: "Test Alert".to_string(),
                description: "Test description".to_string(),
                severity: AlertSeverity::Warning,
                category: AlertCategory::SystemHealth,
                status: AlertStatus::Active,
                source: "test".to_string(),
                labels: HashMap::new(),
                annotations: HashMap::new(),
                value: None,
                threshold: None,
                created_at: Utc::now(),
                updated_at: Utc::now(),
                resolved_at: None,
                acknowledged_at: None,
                acknowledged_by: None,
                silenced_until: None,
                alert_rule_id: None,
                fingerprint: "test".to_string(),
                count: 1,
                last_occurrence: Utc::now(),
            });

            // Should have recorded the alert
            assert!(!pattern_detector.alert_patterns.is_empty());
        }
    }

    #[tokio::test]
    async fn test_performance_monitor() {
        let config = AlertingFrameworkConfig::default();
        let framework = AlertingFramework::new(config);

        // Initialize the framework
        framework.initialize().await.unwrap();

        // Test performance monitor if available
        if let Some(perf_monitor) = framework.performance_monitor() {
            let mut monitor = perf_monitor.write().await;

            // Create test performance metrics
            let metrics = super::performance_monitor::PerformanceMetrics {
                timestamp: Utc::now(),
                request_count: 1000,
                error_count: 50,
                total_response_time_ms: 150000.0,
                avg_response_time_ms: 150.0,
                p50_response_time_ms: 120.0,
                p95_response_time_ms: 300.0,
                p99_response_time_ms: 500.0,
                throughput_per_second: 10.0,
                error_rate_percent: 5.0,
                active_connections: 25,
                queue_depth: 5,
                database_connections_active: 8,
                database_connections_idle: 2,
                memory_usage_percent: 65.0,
                cpu_usage_percent: 45.0,
                slow_queries_count: 2,
                cache_hit_rate_percent: 85.0,
                endpoint_metrics: HashMap::new(),
            };

            // Record metrics (should not panic)
            monitor.record_metrics(metrics).await.unwrap();
        }
    }

    #[tokio::test]
    async fn test_compliance_monitor() {
        let config = AlertingFrameworkConfig::default();
        let framework = AlertingFramework::new(config);

        // Initialize the framework
        framework.initialize().await.unwrap();

        // Test compliance monitor if available
        if let Some(comp_monitor) = framework.compliance_monitor() {
            let mut monitor = comp_monitor.write().await;

            // Create test compliance metrics
            let metrics = super::compliance_monitor::ComplianceMetrics {
                timestamp: Utc::now(),
                license_detection_accuracy: 0.85,
                total_license_predictions: 1000,
                correct_predictions: 850,
                false_positives: 50,
                false_negatives: 100,
                compliance_violations: 5,
                active_violations: 3,
                resolved_violations: 2,
                ml_model_accuracy: 0.82,
                ml_model_version: "v1.2.3".to_string(),
                database_update_failures: 2,
                license_scan_failures: 3,
                successful_scans: 97,
                total_scans: 100,
                data_freshness_hours: 12.5,
                license_database_size: 50000,
                last_database_update: Some(Utc::now()),
                custom_metrics: HashMap::new(),
            };

            // Record metrics (should not panic)
            monitor.record_metrics(metrics).await.unwrap();
        }
    }

    #[tokio::test]
    async fn test_security_monitor() {
        let config = AlertingFrameworkConfig::default();
        let framework = AlertingFramework::new(config);

        // Initialize the framework
        framework.initialize().await.unwrap();

        // Test security monitor if available
        if let Some(sec_monitor) = framework.security_monitor() {
            let mut monitor = sec_monitor.write().await;

            // Create test security event
            let event = super::security_monitor::SecurityEvent {
                id: "test_event".to_string(),
                event_type: super::security_monitor::SecurityEventType::FailedAuthentication,
                timestamp: Utc::now(),
                source_ip: Some("***********".to_string()),
                user_id: Some("test_user".to_string()),
                resource: Some("/api/login".to_string()),
                severity: AlertSeverity::Warning,
                description: "Invalid password attempt".to_string(),
                metadata: HashMap::new(),
                confidence_score: 0.8,
            };

            // Record event (should not panic)
            monitor.record_security_event(event).await.unwrap();
        }
    }

    #[tokio::test]
    async fn test_alert_api_server() {
        let config = AlertingFrameworkConfig::default();
        let framework = AlertingFramework::new(config);

        // Initialize the framework
        framework.initialize().await.unwrap();

        // Test alert API server if available
        if let Some(api_server) = framework.alert_api_server() {
            // API server should be initialized
            assert!(true); // If we reach here, the server was created successfully
        }
    }

    #[tokio::test]
    async fn test_framework_shutdown() {
        let config = AlertingFrameworkConfig::default();
        let framework = AlertingFramework::new(config);

        // Initialize the framework
        framework.initialize().await.unwrap();

        // Shutdown should not panic
        framework.shutdown().await.unwrap();
    }
}