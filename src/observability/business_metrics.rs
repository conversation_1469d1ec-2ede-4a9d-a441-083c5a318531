//! # Business Metrics Monitor
//!
//! This module provides business metrics monitoring capabilities that track
//! compliance scan throughput, CI/CD integration success rates, user adoption,
//! and other business-relevant metrics.

use crate::observability::{ObservabilityManager, custom_metrics::*};
use std::sync::Arc;
use tokio::sync::RwLock;
use std::time::{Duration, Instant};

/// Business metrics monitor
pub struct BusinessMetricsMonitor {
    observability: Arc<ObservabilityManager>,
    scan_metrics: RwLock<ComplianceScanMetrics>,
    ci_cd_metrics: RwLock<CICDMetrics>,
    user_metrics: RwLock<UserMetrics>,
    last_throughput_update: RwLock<Instant>,
}

impl BusinessMetricsMonitor {
    /// Create a new business metrics monitor
    pub fn new(observability: Arc<ObservabilityManager>) -> Self {
        Self {
            observability,
            scan_metrics: RwLock::new(ComplianceScanMetrics::default()),
            ci_cd_metrics: RwLock::new(CICDMetrics::default()),
            user_metrics: RwLock::new(UserMetrics::default()),
            last_throughput_update: RwLock::new(Instant::now()),
        }
    }

    /// Record compliance scan result
    pub async fn record_compliance_scan(&self, successful: bool, scan_type: &str, duration_ms: f64) {
        // Update scan metrics
        {
            let mut metrics = self.scan_metrics.write().await;
            metrics.total_scans += 1;
            if successful {
                metrics.successful_scans += 1;
            }
            metrics.last_scan_duration_ms = duration_ms;
            metrics.scan_types.entry(scan_type.to_string()).and_modify(|e| *e += 1).or_insert(1);
        }

        // Update business metrics
        {
            let mut business_metrics = self.observability.business_metrics.write().await;
            business_metrics.compliance_scans_total += 1;
            if successful {
                business_metrics.compliance_scans_successful += 1;
            }
        }

        // Record to observability
        self.observability.record_business(successful, 0).await;

        // Update throughput
        self.update_scan_throughput().await;
    }

    /// Record license database update
    pub async fn record_license_update(&self, update_type: &str, successful: bool) {
        // Update business metrics
        {
            let mut business_metrics = self.observability.business_metrics.write().await;
            business_metrics.license_database_updates += 1;
        }

        // Record to observability
        self.observability.record_business(successful, 0).await;
    }

    /// Record CI/CD integration event
    pub async fn record_ci_cd_event(&self, integration_type: &str, successful: bool) {
        // Update CI/CD metrics
        {
            let mut metrics = self.ci_cd_metrics.write().await;
            metrics.total_events += 1;
            if successful {
                metrics.successful_events += 1;
            }
            metrics.integration_types.entry(integration_type.to_string()).and_modify(|e| *e += 1).or_insert(1);
        }

        // Update business metrics
        {
            let mut business_metrics = self.observability.business_metrics.write().await;
            business_metrics.ci_cd_integrations_active += if successful { 1 } else { 0 };
        }

        // Record to observability
        self.observability.record_business(successful, 1).await;
    }

    /// Record user session
    pub async fn record_user_session(&self, session_type: &str, duration_seconds: u64) {
        // Update user metrics
        {
            let mut metrics = self.user_metrics.write().await;
            metrics.total_sessions += 1;
            metrics.session_types.entry(session_type.to_string()).and_modify(|e| *e += 1).or_insert(1);
            metrics.total_session_duration_seconds += duration_seconds;
        }

        // Update business metrics
        {
            let mut business_metrics = self.observability.business_metrics.write().await;
            business_metrics.user_sessions_total += 1;
        }

        // Record to observability
        self.observability.record_business(true, 0).await;
    }

    /// Record API call
    pub async fn record_api_call(&self, endpoint: &str, successful: bool, response_time_ms: f64) {
        // Update business metrics
        {
            let mut business_metrics = self.observability.business_metrics.write().await;
            business_metrics.api_calls_total += 1;
        }

        // Record to observability
        self.observability.record_business(successful, 0).await;
    }

    /// Update scan throughput (calls per minute)
    async fn update_scan_throughput(&self) {
        let mut last_update = self.last_throughput_update.write().await;
        let elapsed = last_update.elapsed();

        if elapsed >= Duration::from_secs(60) {
            let scan_metrics = self.scan_metrics.read().await;
            let throughput = scan_metrics.total_scans as f64 / elapsed.as_secs_f64() * 60.0;

            // Update business metrics
            let mut business_metrics = self.observability.business_metrics.write().await;
            business_metrics.scan_throughput_per_minute = throughput;

            *last_update = Instant::now();
        }
    }

    /// Calculate adoption rate based on various metrics
    pub async fn calculate_adoption_rate(&self) -> f64 {
        let scan_metrics = self.scan_metrics.read().await;
        let ci_cd_metrics = self.ci_cd_metrics.read().await;
        let user_metrics = self.user_metrics.read().await;

        // Simple adoption rate calculation based on activity
        let total_activity = scan_metrics.total_scans + ci_cd_metrics.total_events + user_metrics.total_sessions;
        let successful_activity = scan_metrics.successful_scans + ci_cd_metrics.successful_events + user_metrics.total_sessions;

        if total_activity > 0 {
            successful_activity as f64 / total_activity as f64
        } else {
            0.0
        }
    }

    /// Get compliance scan metrics
    pub async fn get_compliance_scan_metrics(&self) -> ComplianceScanMetrics {
        self.scan_metrics.read().await.clone()
    }

    /// Get CI/CD metrics
    pub async fn get_ci_cd_metrics(&self) -> CICDMetrics {
        self.ci_cd_metrics.read().await.clone()
    }

    /// Get user metrics
    pub async fn get_user_metrics(&self) -> UserMetrics {
        self.user_metrics.read().await.clone()
    }

    /// Get business metrics summary
    pub async fn get_business_metrics_summary(&self) -> BusinessMetricsSummary {
        let scan_metrics = self.get_compliance_scan_metrics().await;
        let ci_cd_metrics = self.get_ci_cd_metrics().await;
        let user_metrics = self.get_user_metrics().await;
        let adoption_rate = self.calculate_adoption_rate().await;

        BusinessMetricsSummary {
            total_scans: scan_metrics.total_scans,
            scan_success_rate: if scan_metrics.total_scans > 0 {
                scan_metrics.successful_scans as f64 / scan_metrics.total_scans as f64
            } else {
                0.0
            },
            ci_cd_success_rate: if ci_cd_metrics.total_events > 0 {
                ci_cd_metrics.successful_events as f64 / ci_cd_metrics.total_events as f64
            } else {
                0.0
            },
            total_users: user_metrics.total_sessions,
            adoption_rate,
            scan_throughput_per_minute: self.observability.business_metrics.read().await.scan_throughput_per_minute,
        }
    }
}

/// Compliance scan metrics
#[derive(Debug, Clone, Default)]
pub struct ComplianceScanMetrics {
    pub total_scans: u64,
    pub successful_scans: u64,
    pub last_scan_duration_ms: f64,
    pub scan_types: std::collections::HashMap<String, u64>,
}

/// CI/CD metrics
#[derive(Debug, Clone, Default)]
pub struct CICDMetrics {
    pub total_events: u64,
    pub successful_events: u64,
    pub integration_types: std::collections::HashMap<String, u64>,
}

/// User metrics
#[derive(Debug, Clone, Default)]
pub struct UserMetrics {
    pub total_sessions: u64,
    pub session_types: std::collections::HashMap<String, u64>,
    pub total_session_duration_seconds: u64,
}

/// Business metrics summary
#[derive(Debug, Clone)]
pub struct BusinessMetricsSummary {
    pub total_scans: u64,
    pub scan_success_rate: f64,
    pub ci_cd_success_rate: f64,
    pub total_users: u64,
    pub adoption_rate: f64,
    pub scan_throughput_per_minute: f64,
}

/// Business intelligence analyzer
pub struct BusinessIntelligenceAnalyzer {
    monitor: Arc<BusinessMetricsMonitor>,
}

impl BusinessIntelligenceAnalyzer {
    /// Create a new business intelligence analyzer
    pub fn new(monitor: Arc<BusinessMetricsMonitor>) -> Self {
        Self { monitor }
    }

    /// Analyze business trends
    pub async fn analyze_trends(&self) -> BusinessTrendsAnalysis {
        let summary = self.monitor.get_business_metrics_summary().await;

        BusinessTrendsAnalysis {
            scan_volume_trend: self.analyze_scan_volume_trend().await,
            success_rate_trend: self.analyze_success_rate_trend().await,
            user_engagement_trend: self.analyze_user_engagement_trend().await,
            recommendations: self.generate_recommendations(&summary).await,
        }
    }

    /// Analyze scan volume trends
    async fn analyze_scan_volume_trend(&self) -> TrendAnalysis {
        // Simplified trend analysis - in a real implementation, this would analyze historical data
        let summary = self.monitor.get_business_metrics_summary().await;

        if summary.scan_throughput_per_minute > 10.0 {
            TrendAnalysis::Increasing
        } else if summary.scan_throughput_per_minute > 5.0 {
            TrendAnalysis::Stable
        } else {
            TrendAnalysis::Decreasing
        }
    }

    /// Analyze success rate trends
    async fn analyze_success_rate_trend(&self) -> TrendAnalysis {
        let summary = self.monitor.get_business_metrics_summary().await;

        if summary.scan_success_rate > 0.9 {
            TrendAnalysis::Increasing
        } else if summary.scan_success_rate > 0.7 {
            TrendAnalysis::Stable
        } else {
            TrendAnalysis::Decreasing
        }
    }

    /// Analyze user engagement trends
    async fn analyze_user_engagement_trend(&self) -> TrendAnalysis {
        let summary = self.monitor.get_business_metrics_summary().await;

        if summary.adoption_rate > 0.8 {
            TrendAnalysis::Increasing
        } else if summary.adoption_rate > 0.5 {
            TrendAnalysis::Stable
        } else {
            TrendAnalysis::Decreasing
        }
    }

    /// Generate business recommendations
    async fn generate_recommendations(&self, summary: &BusinessMetricsSummary) -> Vec<String> {
        let mut recommendations = Vec::new();

        if summary.scan_success_rate < 0.8 {
            recommendations.push("Consider improving scan accuracy through model retraining".to_string());
        }

        if summary.scan_throughput_per_minute < 5.0 {
            recommendations.push("Consider optimizing scan performance or scaling infrastructure".to_string());
        }

        if summary.adoption_rate < 0.6 {
            recommendations.push("Consider user engagement initiatives to improve adoption rate".to_string());
        }

        if summary.ci_cd_success_rate < 0.9 {
            recommendations.push("Review CI/CD integration configurations for reliability improvements".to_string());
        }

        if recommendations.is_empty() {
            recommendations.push("Business metrics are performing well - continue monitoring".to_string());
        }

        recommendations
    }
}

/// Trend analysis
#[derive(Debug, Clone)]
pub enum TrendAnalysis {
    Increasing,
    Stable,
    Decreasing,
}

/// Business trends analysis
#[derive(Debug, Clone)]
pub struct BusinessTrendsAnalysis {
    pub scan_volume_trend: TrendAnalysis,
    pub success_rate_trend: TrendAnalysis,
    pub user_engagement_trend: TrendAnalysis,
    pub recommendations: Vec<String>,
}