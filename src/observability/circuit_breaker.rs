//! # Circuit Breaker and Graceful Degradation Framework
//!
//! This module provides comprehensive circuit breaker patterns and graceful degradation
//! mechanisms to ensure observability system resilience during failures and high-load scenarios.

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use opentelemetry::KeyValue;

/// Circuit breaker states
#[derive(Debug, <PERSON>lone, Co<PERSON>, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum CircuitBreakerState {
    Closed,   // Normal operation
    Open,     // Circuit is open, failing fast
    HalfOpen, // Testing if service has recovered
}

/// Circuit breaker configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CircuitBreakerConfig {
    /// Service or component name
    pub service_name: String,
    /// Failure threshold (number of failures before opening)
    pub failure_threshold: u32,
    /// Recovery timeout in seconds
    pub recovery_timeout_seconds: u64,
    /// Success threshold for half-open state
    pub success_threshold: u32,
    /// Timeout for individual requests in seconds
    pub timeout_seconds: u64,
    /// Monitoring window in seconds
    pub monitoring_window_seconds: u64,
    /// Enable automatic recovery
    pub enable_auto_recovery: bool,
    /// Maximum number of concurrent requests
    pub max_concurrent_requests: usize,
}

/// Circuit breaker metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircuitBreakerMetrics {
    pub total_requests: u64,
    pub successful_requests: u64,
    pub failed_requests: u64,
    pub timeout_requests: u64,
    pub rejected_requests: u64,
    pub state_changes: u64,
    pub last_failure_time: Option<std::time::SystemTime>,
    pub consecutive_failures: u32,
    pub consecutive_successes: u32,
    pub current_state: CircuitBreakerState,
    pub state_changed_at: std::time::SystemTime,
}

/// Individual circuit breaker instance
#[derive(Debug)]
pub struct CircuitBreaker {
    config: CircuitBreakerConfig,
    state: CircuitBreakerState,
    metrics: CircuitBreakerMetrics,
    last_state_change: Instant,
    failure_count: u32,
    success_count: u32,
    concurrent_requests: usize,
}

impl CircuitBreaker {
    /// Create a new circuit breaker
    pub fn new(config: CircuitBreakerConfig) -> Self {
        let now = std::time::SystemTime::now();
        Self {
            config,
            state: CircuitBreakerState::Closed,
            metrics: CircuitBreakerMetrics {
                total_requests: 0,
                successful_requests: 0,
                failed_requests: 0,
                timeout_requests: 0,
                rejected_requests: 0,
                state_changes: 0,
                last_failure_time: None,
                consecutive_failures: 0,
                consecutive_successes: 0,
                current_state: CircuitBreakerState::Closed,
                state_changed_at: now,
            },
            last_state_change: Instant::now(),
            failure_count: 0,
            success_count: 0,
            concurrent_requests: 0,
        }
    }

    /// Execute a function with circuit breaker protection
    pub async fn execute<F, Fut, T, E>(&mut self, operation: F) -> Result<T, CircuitBreakerError<E>>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = Result<T, E>>,
        E: std::fmt::Debug,
    {
        self.metrics.total_requests += 1;

        // Check if circuit is open
        if self.state == CircuitBreakerState::Open {
            if self.should_attempt_recovery() {
                self.transition_to_half_open();
            } else {
                self.metrics.rejected_requests += 1;
                return Err(CircuitBreakerError::CircuitOpen(self.config.service_name.clone()));
            }
        }

        // Check concurrent request limit
        if self.concurrent_requests >= self.config.max_concurrent_requests {
            self.metrics.rejected_requests += 1;
            return Err(CircuitBreakerError::TooManyConcurrentRequests);
        }

        self.concurrent_requests += 1;

        // Execute with timeout
        let timeout_duration = Duration::from_secs(self.config.timeout_seconds);
        let result = tokio::time::timeout(timeout_duration, operation()).await;

        self.concurrent_requests -= 1;

        match result {
            Ok(Ok(success)) => {
                self.on_success();
                Ok(success)
            }
            Ok(Err(error)) => {
                self.on_failure();
                Err(CircuitBreakerError::OperationFailed(error))
            }
            Err(_) => {
                self.on_timeout();
                Err(CircuitBreakerError::Timeout)
            }
        }
    }

    /// Record a successful operation
    pub fn on_success(&mut self) {
        self.metrics.successful_requests += 1;
        self.metrics.consecutive_successes += 1;
        self.metrics.consecutive_failures = 0;

        if self.state == CircuitBreakerState::HalfOpen {
            self.success_count += 1;
            if self.success_count >= self.config.success_threshold {
                self.transition_to_closed();
            }
        }
    }

    /// Record a failed operation
    pub fn on_failure(&mut self) {
        self.metrics.failed_requests += 1;
        self.metrics.consecutive_failures += 1;
        self.metrics.consecutive_successes = 0;
        self.metrics.last_failure_time = Some(std::time::SystemTime::now());

        self.failure_count += 1;

        if self.failure_count >= self.config.failure_threshold {
            self.transition_to_open();
        }
    }

    /// Record a timeout
    pub fn on_timeout(&mut self) {
        self.metrics.timeout_requests += 1;
        self.on_failure();
    }

    /// Check if we should attempt recovery from open state
    fn should_attempt_recovery(&self) -> bool {
        if !self.config.enable_auto_recovery {
            return false;
        }

        let elapsed = self.last_state_change.elapsed();
        elapsed >= Duration::from_secs(self.config.recovery_timeout_seconds)
    }

    /// Transition to open state
    fn transition_to_open(&mut self) {
        self.state = CircuitBreakerState::Open;
        self.metrics.current_state = CircuitBreakerState::Open;
        self.metrics.state_changes += 1;
        self.metrics.state_changed_at = std::time::SystemTime::now();
        self.last_state_change = Instant::now();
        self.success_count = 0;
    }

    /// Transition to half-open state
    fn transition_to_half_open(&mut self) {
        self.state = CircuitBreakerState::HalfOpen;
        self.metrics.current_state = CircuitBreakerState::HalfOpen;
        self.metrics.state_changes += 1;
        self.metrics.state_changed_at = std::time::SystemTime::now();
        self.last_state_change = Instant::now();
        self.success_count = 0;
    }

    /// Transition to closed state
    fn transition_to_closed(&mut self) {
        self.state = CircuitBreakerState::Closed;
        self.metrics.current_state = CircuitBreakerState::Closed;
        self.metrics.state_changes += 1;
        self.metrics.state_changed_at = std::time::SystemTime::now();
        self.last_state_change = Instant::now();
        self.failure_count = 0;
        self.success_count = 0;
    }

    /// Get current metrics
    pub fn metrics(&self) -> &CircuitBreakerMetrics {
        &self.metrics
    }

    /// Get current state
    pub fn state(&self) -> CircuitBreakerState {
        self.state
    }

    /// Manually reset the circuit breaker
    pub fn reset(&mut self) {
        self.transition_to_closed();
    }

    /// Force open the circuit breaker
    pub fn force_open(&mut self) {
        self.transition_to_open();
    }
}

/// Circuit breaker error types
#[derive(Debug, thiserror::Error)]
pub enum CircuitBreakerError<E> {
    #[error("Circuit breaker is open for service: {0}")]
    CircuitOpen(String),
    #[error("Too many concurrent requests")]
    TooManyConcurrentRequests,
    #[error("Operation timed out")]
    Timeout,
    #[error("Operation failed: {0:?}")]
    OperationFailed(E),
}

/// Circuit breaker registry for managing multiple circuit breakers
#[derive(Debug)]
pub struct CircuitBreakerRegistry {
    breakers: HashMap<String, Arc<RwLock<CircuitBreaker>>>,
    default_config: CircuitBreakerConfig,
}

impl CircuitBreakerRegistry {
    /// Create a new circuit breaker registry
    pub fn new(default_config: CircuitBreakerConfig) -> Self {
        Self {
            breakers: HashMap::new(),
            default_config,
        }
    }

    /// Get or create a circuit breaker for a service
    pub async fn get_or_create(&mut self, service_name: &str) -> Arc<RwLock<CircuitBreaker>> {
        if let Some(breaker) = self.breakers.get(service_name) {
            return breaker.clone();
        }

        let mut config = self.default_config.clone();
        config.service_name = service_name.to_string();

        let breaker = Arc::new(RwLock::new(CircuitBreaker::new(config)));
        self.breakers.insert(service_name.to_string(), breaker.clone());
        breaker
    }

    /// Get all circuit breaker metrics
    pub async fn get_all_metrics(&self) -> HashMap<String, CircuitBreakerMetrics> {
        let mut metrics = HashMap::new();
        for (name, breaker) in &self.breakers {
            let breaker_metrics = breaker.read().await.metrics().clone();
            metrics.insert(name.clone(), breaker_metrics);
        }
        metrics
    }

    /// Reset all circuit breakers
    pub async fn reset_all(&self) {
        for breaker in self.breakers.values() {
            let mut breaker = breaker.write().await;
            breaker.reset();
        }
    }
}

/// Graceful degradation manager
#[derive(Debug)]
pub struct GracefulDegradationManager {
    degraded_features: HashMap<String, DegradedFeature>,
    degradation_thresholds: HashMap<String, DegradationThreshold>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DegradedFeature {
    pub name: String,
    pub enabled: bool,
    pub degradation_level: DegradationLevel,
    pub fallback_mechanism: Option<String>,
    pub estimated_recovery_time: Option<std::time::SystemTime>,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum DegradationLevel {
    None,
    Partial,
    Full,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DegradationThreshold {
    pub metric_name: String,
    pub threshold_value: f64,
    pub operator: ThresholdOperator,
    pub degradation_action: DegradationAction,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ThresholdOperator {
    GreaterThan,
    LessThan,
    Equal,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DegradationAction {
    DisableFeature(String),
    ReduceFrequency(String),
    EnableFallback(String),
}

impl GracefulDegradationManager {
    /// Create a new graceful degradation manager
    pub fn new() -> Self {
        Self {
            degraded_features: HashMap::new(),
            degradation_thresholds: HashMap::new(),
        }
    }

    /// Add a degradation threshold
    pub fn add_threshold(&mut self, name: String, threshold: DegradationThreshold) {
        self.degradation_thresholds.insert(name, threshold);
    }

    /// Check if a feature should be degraded based on metrics
    pub fn check_degradation(&mut self, metric_name: &str, metric_value: f64) {
        if let Some(threshold) = self.degradation_thresholds.get(metric_name) {
            let should_degrade = match threshold.operator {
                ThresholdOperator::GreaterThan => metric_value > threshold.threshold_value,
                ThresholdOperator::LessThan => metric_value < threshold.threshold_value,
                ThresholdOperator::Equal => (metric_value - threshold.threshold_value).abs() < f64::EPSILON,
            };

            if should_degrade {
                let degradation_action = threshold.degradation_action.clone();
                self.apply_degradation(&degradation_action);
            }
        }
    }

    /// Apply degradation action
    fn apply_degradation(&mut self, action: &DegradationAction) {
        match action {
            DegradationAction::DisableFeature(feature_name) => {
                self.degraded_features.insert(
                    feature_name.clone(),
                    DegradedFeature {
                        name: feature_name.clone(),
                        enabled: false,
                        degradation_level: DegradationLevel::Full,
                        fallback_mechanism: None,
                        estimated_recovery_time: Some(std::time::SystemTime::now() + Duration::from_secs(300)),
                    },
                );
            }
            DegradationAction::ReduceFrequency(feature_name) => {
                self.degraded_features.insert(
                    feature_name.clone(),
                    DegradedFeature {
                        name: feature_name.clone(),
                        enabled: true,
                        degradation_level: DegradationLevel::Partial,
                        fallback_mechanism: Some("reduced_frequency".to_string()),
                        estimated_recovery_time: Some(std::time::SystemTime::now() + Duration::from_secs(180)),
                    },
                );
            }
            DegradationAction::EnableFallback(feature_name) => {
                self.degraded_features.insert(
                    feature_name.clone(),
                    DegradedFeature {
                        name: feature_name.clone(),
                        enabled: true,
                        degradation_level: DegradationLevel::Partial,
                        fallback_mechanism: Some("fallback_mode".to_string()),
                        estimated_recovery_time: Some(std::time::SystemTime::now() + Duration::from_secs(120)),
                    },
                );
            }
        }
    }

    /// Check if a feature is available
    pub fn is_feature_available(&self, feature_name: &str) -> bool {
        self.degraded_features
            .get(feature_name)
            .map(|f| f.enabled)
            .unwrap_or(true)
    }

    /// Get degradation level for a feature
    pub fn get_degradation_level(&self, feature_name: &str) -> DegradationLevel {
        self.degraded_features
            .get(feature_name)
            .map(|f| f.degradation_level)
            .unwrap_or(DegradationLevel::None)
    }

    /// Get all degraded features
    pub fn get_degraded_features(&self) -> &HashMap<String, DegradedFeature> {
        &self.degraded_features
    }

    /// Recover a feature
    pub fn recover_feature(&mut self, feature_name: &str) {
        self.degraded_features.remove(feature_name);
    }
}

/// Circuit breaker and graceful degradation framework configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FaultToleranceConfig {
    pub circuit_breaker: CircuitBreakerConfig,
    pub graceful_degradation: GracefulDegradationConfig,
    pub monitoring: FaultToleranceMonitoringConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GracefulDegradationConfig {
    pub enabled: bool,
    pub thresholds: Vec<DegradationThreshold>,
    pub recovery_check_interval_seconds: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FaultToleranceMonitoringConfig {
    pub metrics_collection_interval_seconds: u64,
    pub alert_on_circuit_open: bool,
    pub alert_on_feature_degradation: bool,
}

/// Main fault tolerance framework manager
#[derive(Debug)]
pub struct FaultToleranceManager {
    circuit_breaker_registry: CircuitBreakerRegistry,
    graceful_degradation_manager: GracefulDegradationManager,
    config: FaultToleranceConfig,
}

impl FaultToleranceManager {
    /// Create a new fault tolerance manager
    pub fn new(config: FaultToleranceConfig) -> Self {
        let circuit_breaker_registry = CircuitBreakerRegistry::new(config.circuit_breaker.clone());
        let graceful_degradation_manager = GracefulDegradationManager::new();

        // Initialize degradation thresholds
        let mut degradation_manager = GracefulDegradationManager::new();
        for threshold in &config.graceful_degradation.thresholds {
            degradation_manager.add_threshold(
                threshold.metric_name.clone(),
                threshold.clone(),
            );
        }

        Self {
            circuit_breaker_registry,
            graceful_degradation_manager: degradation_manager,
            config,
        }
    }

    /// Get circuit breaker for a service
    pub async fn get_circuit_breaker(&mut self, service_name: &str) -> Arc<RwLock<CircuitBreaker>> {
        self.circuit_breaker_registry.get_or_create(service_name).await
    }

    /// Execute operation with circuit breaker protection
    pub async fn execute_with_circuit_breaker<F, Fut, T, E>(
        &mut self,
        service_name: &str,
        operation: F,
    ) -> Result<T, CircuitBreakerError<E>>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = Result<T, E>>,
        E: std::fmt::Debug,
    {
        let breaker = self.get_circuit_breaker(service_name).await;
        let mut breaker = breaker.write().await;
        breaker.execute(operation).await
    }

    /// Check graceful degradation based on metrics
    pub fn check_graceful_degradation(&mut self, metric_name: &str, metric_value: f64) {
        self.graceful_degradation_manager.check_degradation(metric_name, metric_value);
    }

    /// Check if feature is available
    pub fn is_feature_available(&self, feature_name: &str) -> bool {
        self.graceful_degradation_manager.is_feature_available(feature_name)
    }

    /// Get circuit breaker metrics
    pub async fn get_circuit_breaker_metrics(&self) -> HashMap<String, CircuitBreakerMetrics> {
        self.circuit_breaker_registry.get_all_metrics().await
    }

    /// Get degraded features
    pub fn get_degraded_features(&self) -> &HashMap<String, DegradedFeature> {
        self.graceful_degradation_manager.get_degraded_features()
    }

    /// Reset all circuit breakers
    pub async fn reset_all_circuit_breakers(&self) {
        self.circuit_breaker_registry.reset_all().await
    }

    /// Recover a degraded feature
    pub fn recover_feature(&mut self, feature_name: &str) {
        self.graceful_degradation_manager.recover_feature(feature_name);
    }
}

impl Default for CircuitBreakerConfig {
    fn default() -> Self {
        Self {
            service_name: "default".to_string(),
            failure_threshold: 5,
            recovery_timeout_seconds: 60,
            success_threshold: 3,
            timeout_seconds: 30,
            monitoring_window_seconds: 300,
            enable_auto_recovery: true,
            max_concurrent_requests: 100,
        }
    }
}

impl Default for FaultToleranceConfig {
    fn default() -> Self {
        Self {
            circuit_breaker: CircuitBreakerConfig::default(),
            graceful_degradation: GracefulDegradationConfig {
                enabled: true,
                thresholds: vec![
                    DegradationThreshold {
                        metric_name: "system_cpu_usage_percent".to_string(),
                        threshold_value: 90.0,
                        operator: ThresholdOperator::GreaterThan,
                        degradation_action: DegradationAction::ReduceFrequency("metrics_collection".to_string()),
                    },
                    DegradationThreshold {
                        metric_name: "system_memory_usage_percent".to_string(),
                        threshold_value: 85.0,
                        operator: ThresholdOperator::GreaterThan,
                        degradation_action: DegradationAction::DisableFeature("advanced_analytics".to_string()),
                    },
                ],
                recovery_check_interval_seconds: 60,
            },
            monitoring: FaultToleranceMonitoringConfig {
                metrics_collection_interval_seconds: 30,
                alert_on_circuit_open: true,
                alert_on_feature_degradation: true,
            },
        }
    }
}