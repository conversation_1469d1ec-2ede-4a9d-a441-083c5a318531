//! # Configuration Management Framework
//!
//! This module provides comprehensive configuration management capabilities
//! including validation, hot-reloading, backup, versioning, and environment-specific settings.

use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// Configuration management settings
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ConfigManagementConfig {
    /// Enable configuration management
    pub enabled: bool,
    /// Configuration file path
    pub config_file_path: PathBuf,
    /// Backup directory
    pub backup_directory: PathBuf,
    /// Hot reload enabled
    pub hot_reload_enabled: bool,
    /// Configuration validation enabled
    pub validation_enabled: bool,
    /// Environment-specific configurations
    pub environments: HashMap<String, EnvironmentConfig>,
    /// Configuration versioning enabled
    pub versioning_enabled: bool,
    /// Maximum backup versions to keep
    pub max_backup_versions: usize,
}

/// Environment-specific configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct EnvironmentConfig {
    /// Environment name
    pub name: String,
    /// Fault tolerance settings for this environment
    pub fault_tolerance: FaultToleranceOverrides,
    /// Resource limits for this environment
    pub resource_limits: ResourceLimitOverrides,
    /// Monitoring settings for this environment
    pub monitoring: MonitoringOverrides,
}

/// Fault tolerance overrides for environment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FaultToleranceOverrides {
    /// Circuit breaker failure threshold
    pub circuit_breaker_failure_threshold: Option<u32>,
    /// Circuit breaker recovery timeout
    pub circuit_breaker_recovery_timeout_seconds: Option<u64>,
    /// Retry attempts
    pub retry_attempts: Option<usize>,
    /// Timeout settings
    pub timeout_seconds: Option<u64>,
}

/// Resource limit overrides for environment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceLimitOverrides {
    /// CPU limit percentage
    pub cpu_limit_percent: Option<f64>,
    /// Memory limit percentage
    pub memory_limit_percent: Option<f64>,
    /// Request rate limit
    pub request_rate_limit: Option<u64>,
}

/// Monitoring overrides for environment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringOverrides {
    /// Metrics collection interval
    pub metrics_interval_seconds: Option<u64>,
    /// Alert thresholds
    pub alert_thresholds: Option<AlertThresholdOverrides>,
}

/// Alert threshold overrides
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertThresholdOverrides {
    pub cpu_warning_percent: Option<f64>,
    pub memory_warning_percent: Option<f64>,
    pub error_rate_threshold: Option<f64>,
}

/// Configuration version
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigVersion {
    pub version: String,
    pub timestamp: DateTime<Utc>,
    pub author: String,
    pub changes: Vec<String>,
    pub checksum: String,
    pub environment: String,
}

/// Configuration validation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationResult {
    pub is_valid: bool,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
    pub timestamp: DateTime<Utc>,
}

/// Configuration change event
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigChangeEvent {
    pub id: String,
    pub change_type: ConfigChangeType,
    pub timestamp: DateTime<Utc>,
    pub old_version: Option<String>,
    pub new_version: String,
    pub affected_components: Vec<String>,
    pub impact_assessment: ImpactAssessment,
}

/// Configuration change types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ConfigChangeType {
    Created,
    Updated,
    Deleted,
    RolledBack,
    EnvironmentOverride,
}

/// Impact assessment for configuration changes
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImpactAssessment {
    pub risk_level: RiskLevel,
    pub affected_services: Vec<String>,
    pub estimated_downtime: Option<u64>, // seconds
    pub rollback_required: bool,
    pub testing_required: bool,
}

/// Risk levels for configuration changes
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
    Critical,
}

/// Configuration backup
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigBackup {
    pub id: String,
    pub timestamp: DateTime<Utc>,
    pub version: String,
    pub environment: String,
    pub config_data: serde_json::Value,
    pub checksum: String,
    pub size_bytes: usize,
}

/// Configuration manager
#[derive(Debug)]
pub struct ConfigManager {
    config: ConfigManagementConfig,
    current_config: Arc<RwLock<serde_json::Value>>,
    config_versions: Arc<RwLock<Vec<ConfigVersion>>>,
    backups: Arc<RwLock<Vec<ConfigBackup>>>,
    change_events: Arc<RwLock<Vec<ConfigChangeEvent>>>,
    validation_results: Arc<RwLock<Vec<ValidationResult>>>,
}

impl ConfigManager {
    /// Create a new configuration manager
    pub fn new(config: ConfigManagementConfig) -> Self {
        Self {
            config,
            current_config: Arc::new(RwLock::new(serde_json::json!({}))),
            config_versions: Arc::new(RwLock::new(Vec::new())),
            backups: Arc::new(RwLock::new(Vec::new())),
            change_events: Arc::new(RwLock::new(Vec::new())),
            validation_results: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// Load configuration from file
    pub async fn load_config(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if !self.config.config_file_path.exists() {
            return Err(format!("Configuration file not found: {:?}", self.config.config_file_path).into());
        }

        let config_data = tokio::fs::read_to_string(&self.config.config_file_path).await?;
        let config: serde_json::Value = serde_json::from_str(&config_data)?;

        // Validate configuration
        if self.config.validation_enabled {
            let validation_result = self.validate_config(&config).await?;
            if !validation_result.is_valid {
                return Err(format!("Configuration validation failed: {:?}", validation_result.errors).into());
            }
        }

        // Store current config
        {
            let mut current = self.current_config.write().await;
            *current = config.clone();
        }

        // Create backup
        if self.config.versioning_enabled {
            self.create_backup(&config, "default").await?;
        }

        Ok(())
    }

    /// Save configuration to file
    pub async fn save_config(&self, config: &serde_json::Value) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let config_data = serde_json::to_string_pretty(config)?;

        // Create backup before saving
        if self.config.versioning_enabled {
            self.create_backup(config, "default").await?;
        }

        tokio::fs::write(&self.config.config_file_path, config_data).await?;

        // Update current config
        {
            let mut current = self.current_config.write().await;
            *current = config.clone();
        }

        Ok(())
    }

    /// Validate configuration
    pub async fn validate_config(&self, config: &serde_json::Value) -> Result<ValidationResult, Box<dyn std::error::Error + Send + Sync>> {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();

        // Basic structure validation
        if !config.is_object() {
            errors.push("Configuration must be a JSON object".to_string());
        } else {
            let obj = config.as_object().unwrap();

            // Check required fields
            if !obj.contains_key("service_name") {
                errors.push("Missing required field: service_name".to_string());
            }

            if !obj.contains_key("tracing") {
                warnings.push("Missing tracing configuration".to_string());
            }

            if !obj.contains_key("metrics") {
                warnings.push("Missing metrics configuration".to_string());
            }

            // Validate numeric fields
            if let Some(cpu_limit) = obj.get("cpu_limit_percent") {
                if let Some(cpu_val) = cpu_limit.as_f64() {
                    if cpu_val <= 0.0 || cpu_val > 100.0 {
                        errors.push("CPU limit must be between 0 and 100".to_string());
                    }
                } else {
                    errors.push("CPU limit must be a number".to_string());
                }
            }
        }

        let result = ValidationResult {
            is_valid: errors.is_empty(),
            errors,
            warnings,
            timestamp: Utc::now(),
        };

        // Store validation result
        {
            let mut results = self.validation_results.write().await;
            results.push(result.clone());
        }

        Ok(result)
    }

    /// Get configuration for specific environment
    pub async fn get_environment_config(&self, environment: &str) -> Result<serde_json::Value, Box<dyn std::error::Error + Send + Sync>> {
        let base_config = {
            self.current_config.read().await.clone()
        };

        if let Some(env_config) = self.config.environments.get(environment) {
            // Apply environment overrides
            let mut modified_config = base_config;

            // Apply fault tolerance overrides
            if let Some(threshold) = env_config.fault_tolerance.circuit_breaker_failure_threshold {
                if let Some(tracing) = modified_config.get_mut("tracing") {
                    if let Some(processors) = tracing.get_mut("processors") {
                        if let Some(batch) = processors.get_mut("batch") {
                            batch["max_queue_size"] = threshold.into();
                        }
                    }
                }
            }

            // Apply resource limit overrides
            if let Some(cpu_limit) = env_config.resource_limits.cpu_limit_percent {
                modified_config["cpu_limit_percent"] = cpu_limit.into();
            }

            Ok(modified_config)
        } else {
            Ok(base_config)
        }
    }

    /// Create configuration backup
    pub async fn create_backup(&self, config: &serde_json::Value, environment: &str) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let backup_id = format!("backup_{}", Utc::now().timestamp());
        let checksum = self.calculate_checksum(&serde_json::to_string(config)?);

        let backup = ConfigBackup {
            id: backup_id.clone(),
            timestamp: Utc::now(),
            version: self.get_next_version().await,
            environment: environment.to_string(),
            config_data: config.clone(),
            checksum,
            size_bytes: serde_json::to_string(config)?.len(),
        };

        // Store backup
        {
            let mut backups = self.backups.write().await;
            backups.push(backup);

            // Keep only max versions
            if backups.len() > self.config.max_backup_versions {
                backups.remove(0);
            }
        }

        Ok(backup_id)
    }

    /// Restore configuration from backup
    pub async fn restore_backup(&self, backup_id: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let backup = {
            let backups = self.backups.read().await;
            backups.iter().find(|b| b.id == backup_id).cloned()
        };

        if let Some(backup) = backup {
            self.save_config(&backup.config_data).await?;

            // Record change event
            self.record_change_event(
                ConfigChangeType::RolledBack,
                Some(backup.version.clone()),
                backup.version,
                vec!["all".to_string()],
            ).await;
        } else {
            return Err(format!("Backup not found: {}", backup_id).into());
        }

        Ok(())
    }

    /// Get configuration versions
    pub async fn get_config_versions(&self) -> Vec<ConfigVersion> {
        let versions = self.config_versions.read().await;
        versions.clone()
    }

    /// Get configuration backups
    pub async fn get_config_backups(&self) -> Vec<ConfigBackup> {
        let backups = self.backups.read().await;
        backups.clone()
    }

    /// Get configuration change events
    pub async fn get_change_events(&self) -> Vec<ConfigChangeEvent> {
        let events = self.change_events.read().await;
        events.clone()
    }

    /// Get latest validation results
    pub async fn get_validation_results(&self, limit: usize) -> Vec<ValidationResult> {
        let results = self.validation_results.read().await;
        results.iter().rev().take(limit).cloned().collect()
    }

    /// Record configuration change event
    pub async fn record_change_event(
        &self,
        change_type: ConfigChangeType,
        old_version: Option<String>,
        new_version: String,
        affected_components: Vec<String>,
    ) {
        let impact = self.assess_impact(&change_type, &affected_components);

        let event = ConfigChangeEvent {
            id: format!("change_{}", Utc::now().timestamp()),
            change_type,
            timestamp: Utc::now(),
            old_version,
            new_version,
            affected_components,
            impact_assessment: impact,
        };

        let mut events = self.change_events.write().await;
        events.push(event);
    }

    /// Assess impact of configuration change
    fn assess_impact(&self, change_type: &ConfigChangeType, affected_components: &[String]) -> ImpactAssessment {
        let risk_level = match change_type {
            ConfigChangeType::Created => RiskLevel::Low,
            ConfigChangeType::Updated => {
                if affected_components.contains(&"circuit_breaker".to_string()) {
                    RiskLevel::High
                } else {
                    RiskLevel::Medium
                }
            }
            ConfigChangeType::Deleted => RiskLevel::Critical,
            ConfigChangeType::RolledBack => RiskLevel::Medium,
            ConfigChangeType::EnvironmentOverride => RiskLevel::Low,
        };

        ImpactAssessment {
            risk_level,
            affected_services: affected_components.clone(),
            estimated_downtime: match risk_level {
                RiskLevel::Low => Some(0),
                RiskLevel::Medium => Some(30),
                RiskLevel::High => Some(300),
                RiskLevel::Critical => Some(1800),
            },
            rollback_required: matches!(risk_level, RiskLevel::High | RiskLevel::Critical),
            testing_required: matches!(risk_level, RiskLevel::Medium | RiskLevel::High | RiskLevel::Critical),
        }
    }

    /// Get next version number
    async fn get_next_version(&self) -> String {
        let versions = self.config_versions.read().await;
        let latest_version = versions.last().map(|v| &v.version).unwrap_or(&"v0.0.0".to_string());

        // Simple version increment (in real implementation, use proper semver)
        format!("v{}", Utc::now().timestamp())
    }

    /// Calculate checksum for data
    fn calculate_checksum(&self, data: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        data.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }

    /// Get configuration management statistics
    pub async fn get_statistics(&self) -> ConfigManagementStats {
        let versions = self.config_versions.read().await;
        let backups = self.backups.read().await;
        let events = self.change_events.read().await;
        let validations = self.validation_results.read().await;

        let total_changes = events.len();
        let failed_validations = validations.iter().filter(|v| !v.is_valid).count();

        let high_risk_changes = events.iter()
            .filter(|e| matches!(e.impact_assessment.risk_level, RiskLevel::High | RiskLevel::Critical))
            .count();

        ConfigManagementStats {
            total_versions: versions.len(),
            total_backups: backups.len(),
            total_changes,
            failed_validations,
            high_risk_changes,
            last_backup: backups.last().map(|b| b.timestamp),
            last_change: events.last().map(|e| e.timestamp),
        }
    }
}

/// Configuration management statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigManagementStats {
    pub total_versions: usize,
    pub total_backups: usize,
    pub total_changes: usize,
    pub failed_validations: usize,
    pub high_risk_changes: usize,
    pub last_backup: Option<DateTime<Utc>>,
    pub last_change: Option<DateTime<Utc>>,
}

impl Default for ConfigManagementConfig {
    fn default() -> Self {
        let mut environments = HashMap::new();

        // Development environment
        environments.insert("development".to_string(), EnvironmentConfig {
            name: "development".to_string(),
            fault_tolerance: FaultToleranceOverrides {
                circuit_breaker_failure_threshold: Some(10),
                circuit_breaker_recovery_timeout_seconds: Some(30),
                retry_attempts: Some(5),
                timeout_seconds: Some(60),
            },
            resource_limits: ResourceLimitOverrides {
                cpu_limit_percent: Some(50.0),
                memory_limit_percent: Some(60.0),
                request_rate_limit: Some(1000),
            },
            monitoring: MonitoringOverrides {
                metrics_interval_seconds: Some(30),
                alert_thresholds: Some(AlertThresholdOverrides {
                    cpu_warning_percent: Some(40.0),
                    memory_warning_percent: Some(50.0),
                    error_rate_threshold: Some(5.0),
                }),
            },
        });

        // Production environment
        environments.insert("production".to_string(), EnvironmentConfig {
            name: "production".to_string(),
            fault_tolerance: FaultToleranceOverrides {
                circuit_breaker_failure_threshold: Some(3),
                circuit_breaker_recovery_timeout_seconds: Some(120),
                retry_attempts: Some(3),
                timeout_seconds: Some(30),
            },
            resource_limits: ResourceLimitOverrides {
                cpu_limit_percent: Some(80.0),
                memory_limit_percent: Some(85.0),
                request_rate_limit: Some(5000),
            },
            monitoring: MonitoringOverrides {
                metrics_interval_seconds: Some(60),
                alert_thresholds: Some(AlertThresholdOverrides {
                    cpu_warning_percent: Some(70.0),
                    memory_warning_percent: Some(75.0),
                    error_rate_threshold: Some(1.0),
                }),
            },
        });

        Self {
            enabled: true,
            config_file_path: PathBuf::from("config/observability.json"),
            backup_directory: PathBuf::from("config/backups"),
            hot_reload_enabled: true,
            validation_enabled: true,
            environments,
            versioning_enabled: true,
            max_backup_versions: 10,
        }
    }
}