//! # Custom Metrics Framework
//!
//! This module provides a comprehensive custom metrics framework extending the
//! ObservabilityManager with advanced metrics capabilities for license compliance monitoring.

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use opentelemetry::metrics::{Counter, Histogram, Gauge, ObservableGauge, Meter};

/// Error metrics for tracking application errors
#[derive(Debug, <PERSON>lone)]
pub struct ErrorMetrics {
    pub error_count: u64,
    pub error_rate: f64,
    pub error_types: std::collections::HashMap<String, u64>,
}

/// Health metrics for system health monitoring
#[derive(Debug, <PERSON>lone)]
pub struct HealthMetrics {
    pub cpu_usage: f64,
    pub memory_usage: f64,
    pub disk_usage: f64,
    pub network_latency: f64,
}

/// Metric category enumeration
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ial<PERSON>q, <PERSON><PERSON>, <PERSON>h, Serialize, Deserialize)]
pub enum MetricCategory {
    Performance,
    Business,
    SystemHealth,
    LicenseDetection,
}

/// Custom metric definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CustomMetricDefinition {
    pub name: String,
    pub description: String,
    pub category: MetricCategory,
    pub metric_type: MetricType,
    pub unit: Option<String>,
    pub labels: Vec<String>,
}

/// Metric type enumeration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MetricType {
    Counter,
    Histogram,
    Gauge,
    ObservableGauge,
}

/// Metric registry for organizing metrics by category
#[derive(Debug)]
pub struct MetricRegistry {
    pub performance_metrics: HashMap<String, CustomMetricDefinition>,
    pub business_metrics: HashMap<String, CustomMetricDefinition>,
    pub system_health_metrics: HashMap<String, CustomMetricDefinition>,
    pub license_detection_metrics: HashMap<String, CustomMetricDefinition>,
}

impl MetricRegistry {
    /// Create a new metric registry with default metric definitions
    pub fn new() -> Self {
        let mut registry = Self {
            performance_metrics: HashMap::new(),
            business_metrics: HashMap::new(),
            system_health_metrics: HashMap::new(),
            license_detection_metrics: HashMap::new(),
        };

        registry.initialize_default_metrics();
        registry
    }

    /// Initialize default metric definitions
    fn initialize_default_metrics(&mut self) {
        // Performance metrics
        self.performance_metrics.insert(
            "request_queue_depth".to_string(),
            CustomMetricDefinition {
                name: "request_queue_depth".to_string(),
                description: "Current depth of request processing queue".to_string(),
                category: MetricCategory::Performance,
                metric_type: MetricType::Gauge,
                unit: Some("1".to_string()),
                labels: vec!["queue_type".to_string()],
            },
        );

        self.performance_metrics.insert(
            "processing_time_ms".to_string(),
            CustomMetricDefinition {
                name: "processing_time_ms".to_string(),
                description: "Time taken to process requests".to_string(),
                category: MetricCategory::Performance,
                metric_type: MetricType::Histogram,
                unit: Some("ms".to_string()),
                labels: vec!["operation".to_string()],
            },
        );

        // Business metrics
        self.business_metrics.insert(
            "compliance_scans_total".to_string(),
            CustomMetricDefinition {
                name: "compliance_scans_total".to_string(),
                description: "Total number of compliance scans performed".to_string(),
                category: MetricCategory::Business,
                metric_type: MetricType::Counter,
                unit: Some("1".to_string()),
                labels: vec!["scan_type".to_string()],
            },
        );

        // System health metrics
        self.system_health_metrics.insert(
            "component_health_score".to_string(),
            CustomMetricDefinition {
                name: "component_health_score".to_string(),
                description: "Health score of system components (0.0 to 1.0)".to_string(),
                category: MetricCategory::SystemHealth,
                metric_type: MetricType::Gauge,
                unit: Some("1".to_string()),
                labels: vec!["component".to_string()],
            },
        );

        // License detection metrics
        self.license_detection_metrics.insert(
            "license_predictions_total".to_string(),
            CustomMetricDefinition {
                name: "license_predictions_total".to_string(),
                description: "Total number of license predictions made".to_string(),
                category: MetricCategory::LicenseDetection,
                metric_type: MetricType::Counter,
                unit: Some("1".to_string()),
                labels: vec!["model_version".to_string()],
            },
        );
    }
}

/// Custom metrics manager
#[derive(Debug)]
pub struct CustomMetricsManager {
    registry: MetricRegistry,
    meter: Meter,
    counters: HashMap<String, Counter<u64>>,
    histograms: HashMap<String, Histogram<f64>>,
    gauges: HashMap<String, Gauge<f64>>,
    observable_gauges: HashMap<String, ObservableGauge<f64>>,
}

impl CustomMetricsManager {
    /// Create a new custom metrics manager
    pub fn new() -> Self {
        let meter = opentelemetry::global::meter("infinitium-signal-custom");
        let registry = MetricRegistry::new();

        Self {
            registry,
            meter,
            counters: HashMap::new(),
            histograms: HashMap::new(),
            gauges: HashMap::new(),
            observable_gauges: HashMap::new(),
        }
    }

    /// Register a custom metric
    pub fn register_metric(&mut self, definition: CustomMetricDefinition) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        match definition.metric_type {
            MetricType::Counter => {
                let counter = self.meter
                    .u64_counter(definition.name.clone())
                    .with_description(definition.description)
                    .build();
                self.counters.insert(definition.name.clone(), counter);
            }
            MetricType::Histogram => {
                let histogram = self.meter
                    .f64_histogram(definition.name.clone())
                    .with_description(definition.description)
                    .build();
                self.histograms.insert(definition.name.clone(), histogram);
            }
            MetricType::Gauge => {
                let gauge = self.meter
                    .f64_gauge(definition.name.clone())
                    .with_description(definition.description)
                    .build();
                self.gauges.insert(definition.name.clone(), gauge);
            }
            MetricType::ObservableGauge => {
                let observable_gauge = self.meter
                    .f64_observable_gauge(definition.name.clone())
                    .with_description(definition.description)
                    .build();
                self.observable_gauges.insert(definition.name.clone(), observable_gauge);
            }
        }

        // Add to appropriate registry
        match definition.category {
            MetricCategory::Performance => {
                self.registry.performance_metrics.insert(definition.name, definition);
            }
            MetricCategory::Business => {
                self.registry.business_metrics.insert(definition.name, definition);
            }
            MetricCategory::SystemHealth => {
                self.registry.system_health_metrics.insert(definition.name, definition);
            }
            MetricCategory::LicenseDetection => {
                self.registry.license_detection_metrics.insert(definition.name, definition);
            }
        }

        Ok(())
    }

    /// Record a counter metric
    pub fn record_counter(&self, name: &str, value: u64, labels: Vec<opentelemetry::KeyValue>) {
        if let Some(counter) = self.counters.get(name) {
            counter.add(value, &labels);
        }
    }

    /// Record a histogram metric
    pub fn record_histogram(&self, name: &str, value: f64, labels: Vec<opentelemetry::KeyValue>) {
        if let Some(histogram) = self.histograms.get(name) {
            histogram.record(value, &labels);
        }
    }

    /// Set a gauge metric
    pub fn set_gauge(&self, name: &str, value: f64, labels: Vec<opentelemetry::KeyValue>) {
        if let Some(gauge) = self.gauges.get(name) {
            gauge.set(value, &labels);
        }
    }

    /// Get metric registry
    pub fn registry(&self) -> &MetricRegistry {
        &self.registry
    }
}

/// License detection accuracy metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LicenseDetectionMetrics {
    pub total_predictions: u64,
    pub correct_predictions: u64,
    pub false_positives: u64,
    pub false_negatives: u64,
    pub precision: f64,
    pub recall: f64,
    pub f1_score: f64,
    pub avg_confidence: f64,
    pub confidence_distribution: HashMap<String, u64>,
}

impl Default for LicenseDetectionMetrics {
    fn default() -> Self {
        Self {
            total_predictions: 0,
            correct_predictions: 0,
            false_positives: 0,
            false_negatives: 0,
            precision: 0.0,
            recall: 0.0,
            f1_score: 0.0,
            avg_confidence: 0.0,
            confidence_distribution: HashMap::new(),
        }
    }
}

impl LicenseDetectionMetrics {
    /// Update metrics with new prediction results
    pub fn update(&mut self, predicted_correct: bool, confidence: f64) {
        self.total_predictions += 1;
        self.avg_confidence = (self.avg_confidence * (self.total_predictions - 1) as f64 + confidence) / self.total_predictions as f64;

        if predicted_correct {
            self.correct_predictions += 1;
        } else {
            self.false_positives += 1;
        }

        // Update confidence distribution
        let bucket = format!("{:.1}", (confidence * 10.0).floor() / 10.0);
        *self.confidence_distribution.entry(bucket).or_insert(0) += 1;

        self.calculate_rates();
    }

    /// Calculate precision, recall, and F1-score
    fn calculate_rates(&mut self) {
        if self.total_predictions == 0 {
            return;
        }

        let predicted_positives = self.correct_predictions + self.false_positives;
        if predicted_positives > 0 {
            self.precision = self.correct_predictions as f64 / predicted_positives as f64;
        }

        // For recall, we assume all predictions are attempts to detect licenses
        self.recall = self.correct_predictions as f64 / self.total_predictions as f64;

        if self.precision + self.recall > 0.0 {
            self.f1_score = 2.0 * self.precision * self.recall / (self.precision + self.recall);
        }
    }
}

/// System health metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemHealthMetrics {
    pub component_health_scores: HashMap<String, f64>,
    pub service_availability: f64,
    pub uptime_seconds: u64,
    pub error_rate: f64,
    pub resource_utilization: ResourceUtilization,
}

impl Default for SystemHealthMetrics {
    fn default() -> Self {
        Self {
            component_health_scores: HashMap::new(),
            service_availability: 1.0,
            uptime_seconds: 0,
            error_rate: 0.0,
            resource_utilization: ResourceUtilization::default(),
        }
    }
}

/// Resource utilization metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceUtilization {
    pub cpu_usage_percent: f64,
    pub memory_usage_bytes: u64,
    pub memory_usage_percent: f64,
    pub disk_usage_bytes: u64,
    pub disk_usage_percent: f64,
    pub network_io_bytes: u64,
}

impl Default for ResourceUtilization {
    fn default() -> Self {
        Self {
            cpu_usage_percent: 0.0,
            memory_usage_bytes: 0,
            memory_usage_percent: 0.0,
            disk_usage_bytes: 0,
            disk_usage_percent: 0.0,
            network_io_bytes: 0,
        }
    }
}

/// Performance metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub request_queue_depth: u64,
    pub processing_times_ms: Vec<f64>,
    pub avg_processing_time_ms: f64,
    pub p95_processing_time_ms: f64,
    pub p99_processing_time_ms: f64,
    pub db_connection_pool_active: u32,
    pub db_connection_pool_idle: u32,
    pub db_query_duration_ms: Vec<f64>,
    pub network_io_metrics: NetworkIOMetrics,
}

impl Default for PerformanceMetrics {
    fn default() -> Self {
        Self {
            request_queue_depth: 0,
            processing_times_ms: Vec::new(),
            avg_processing_time_ms: 0.0,
            p95_processing_time_ms: 0.0,
            p99_processing_time_ms: 0.0,
            db_connection_pool_active: 0,
            db_connection_pool_idle: 0,
            db_query_duration_ms: Vec::new(),
            network_io_metrics: NetworkIOMetrics::default(),
        }
    }
}

impl PerformanceMetrics {
    /// Add processing time and update statistics
    pub fn add_processing_time(&mut self, duration_ms: f64) {
        self.processing_times_ms.push(duration_ms);

        // Keep only last 1000 measurements for memory efficiency
        if self.processing_times_ms.len() > 1000 {
            self.processing_times_ms.remove(0);
        }

        self.update_statistics();
    }

    /// Update average and percentile statistics
    fn update_statistics(&mut self) {
        if self.processing_times_ms.is_empty() {
            return;
        }

        // Calculate average
        self.avg_processing_time_ms = self.processing_times_ms.iter().sum::<f64>() / self.processing_times_ms.len() as f64;

        // Calculate percentiles
        let mut sorted_times = self.processing_times_ms.clone();
        sorted_times.sort_by(|a, b| a.partial_cmp(b).unwrap());

        let p95_index = (sorted_times.len() as f64 * 0.95) as usize;
        let p99_index = (sorted_times.len() as f64 * 0.99) as usize;

        self.p95_processing_time_ms = sorted_times.get(p95_index).copied().unwrap_or(0.0);
        self.p99_processing_time_ms = sorted_times.get(p99_index).copied().unwrap_or(0.0);
    }
}

/// Network I/O metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkIOMetrics {
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub connections_active: u32,
    pub connections_total: u64,
}

impl Default for NetworkIOMetrics {
    fn default() -> Self {
        Self {
            bytes_sent: 0,
            bytes_received: 0,
            connections_active: 0,
            connections_total: 0,
        }
    }
}

/// Business metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BusinessMetrics {
    pub compliance_scans_total: u64,
    pub compliance_scans_successful: u64,
    pub license_database_updates: u64,
    pub ci_cd_integrations_active: u32,
    pub user_sessions_total: u64,
    pub api_calls_total: u64,
    pub scan_throughput_per_minute: f64,
    pub adoption_rate: f64,
}

impl Default for BusinessMetrics {
    fn default() -> Self {
        Self {
            compliance_scans_total: 0,
            compliance_scans_successful: 0,
            license_database_updates: 0,
            ci_cd_integrations_active: 0,
            user_sessions_total: 0,
            api_calls_total: 0,
            scan_throughput_per_minute: 0.0,
            adoption_rate: 0.0,
        }
    }
}

/// Metrics aggregation configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricsAggregationConfig {
    pub enable_buffering: bool,
    pub buffer_size: usize,
    pub aggregation_interval_seconds: u64,
    pub retention_period_days: u32,
    pub export_formats: Vec<ExportFormat>,
}

impl Default for MetricsAggregationConfig {
    fn default() -> Self {
        Self {
            enable_buffering: true,
            buffer_size: 1000,
            aggregation_interval_seconds: 60,
            retention_period_days: 30,
            export_formats: vec![ExportFormat::Prometheus, ExportFormat::JSON],
        }
    }
}

/// Export format enumeration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExportFormat {
    Prometheus,
    JSON,
    CSV,
    InfluxDB,
}

/// Metrics buffer for high-throughput scenarios
#[derive(Debug)]
pub struct MetricsBuffer {
    buffer: Vec<BufferedMetric>,
    max_size: usize,
    flush_interval: std::time::Duration,
}

impl MetricsBuffer {
    /// Create a new metrics buffer
    pub fn new(max_size: usize, flush_interval: std::time::Duration) -> Self {
        Self {
            buffer: Vec::new(),
            max_size,
            flush_interval,
        }
    }

    /// Add a metric to the buffer
    pub fn add_metric(&mut self, metric: BufferedMetric) {
        self.buffer.push(metric);

        // Flush if buffer is full
        if self.buffer.len() >= self.max_size {
            self.flush();
        }
    }

    /// Flush buffered metrics
    pub fn flush(&mut self) {
        if self.buffer.is_empty() {
            return;
        }

        // TODO: Implement actual flushing to configured exporters
        // For now, just clear the buffer
        self.buffer.clear();
    }

    /// Get current buffer size
    pub fn size(&self) -> usize {
        self.buffer.len()
    }
}

/// Buffered metric entry
#[derive(Debug, Clone)]
pub struct BufferedMetric {
    pub name: String,
    pub value: f64,
    pub labels: HashMap<String, String>,
    pub timestamp: std::time::SystemTime,
    pub metric_type: MetricType,
}

impl BufferedMetric {
    /// Create a new buffered metric
    pub fn new(name: String, value: f64, labels: HashMap<String, String>, metric_type: MetricType) -> Self {
        Self {
            name,
            value,
            labels,
            timestamp: std::time::SystemTime::now(),
            metric_type,
        }
    }
}