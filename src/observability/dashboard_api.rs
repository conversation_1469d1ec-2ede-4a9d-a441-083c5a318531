//! # Dashboard Management API Module
//!
//! This module provides REST API endpoints for dashboard creation, management,
//! and deployment automation.
//!
//! ## Features
//!
//! - REST API for dashboard CRUD operations
//! - Template management endpoints
//! - Automated deployment to Grafana
//! - Dashboard versioning and rollback
//! - Integration with existing API framework

use std::collections::HashMap;
use std::sync::Arc;
use serde::{Deserialize, Serialize};
use tokio::sync::RwLock;
use axum::{
    extract::{Path, Query, State, WebSocketUpgrade},
    http::StatusCode,
    response::Json,
    routing::{get, post},
    Router,
};
use futures_util::{SinkExt, StreamExt};

use super::dashboards::{DashboardConfig, DashboardGenerator, RealTimeDashboardManager};
use super::ObservabilityManager;

/// Dashboard API configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DashboardApiConfig {
    /// API base path
    pub base_path: String,
    /// Enable dashboard deployment
    pub enable_deployment: bool,
    /// Grafana API endpoint
    pub grafana_endpoint: Option<String>,
    /// Grafana API key
    pub grafana_api_key: Option<String>,
}

/// Dashboard creation request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateDashboardRequest {
    /// Template name to use
    pub template_name: String,
    /// Template parameters
    pub parameters: HashMap<String, String>,
    /// Custom title (optional)
    pub title: Option<String>,
    /// Custom description (optional)
    pub description: Option<String>,
}

/// Dashboard update request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateDashboardRequest {
    /// Updated dashboard configuration
    pub config: DashboardConfig,
}

/// Dashboard deployment request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeployDashboardRequest {
    /// Dashboard ID to deploy
    pub dashboard_id: String,
    /// Target Grafana folder ID
    pub folder_id: Option<String>,
    /// Overwrite existing dashboard
    pub overwrite: bool,
}

/// API response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
}

/// Dashboard metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DashboardMetadata {
    pub id: String,
    pub title: String,
    pub description: String,
    pub template: String,
    pub created_at: String,
    pub updated_at: String,
    pub version: u32,
}

/// Dashboard API server
pub struct DashboardApiServer {
    config: DashboardApiConfig,
    observability_manager: Arc<ObservabilityManager>,
    dashboard_versions: Arc<RwLock<HashMap<String, Vec<DashboardConfig>>>>,
}

impl DashboardApiServer {
    /// Create a new dashboard API server
    pub fn new(config: DashboardApiConfig, observability_manager: Arc<ObservabilityManager>) -> Self {
        Self {
            config,
            observability_manager,
            dashboard_versions: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Create API routes
    pub fn create_routes(&self) -> Router {
        let state = DashboardApiState {
            config: self.config.clone(),
            observability_manager: self.observability_manager.clone(),
            dashboard_versions: self.dashboard_versions.clone(),
        };

        Router::new()
            .route("/health", get(health_check))
            .route("/templates", get(list_templates))
            .route("/templates/:template_name", get(get_template))
            .route("/dashboards", get(list_dashboards).post(create_dashboard))
            .route("/dashboards/:dashboard_id", get(get_dashboard))
            .route("/dashboards/:dashboard_id/deploy", post(deploy_dashboard))
            .route("/realtime/:dashboard_id", get(websocket_handler))
            .with_state(state)
    }

    /// Deploy dashboard to Grafana
    async fn deploy_to_grafana(
        &self,
        dashboard_json: &str,
        folder_id: Option<&str>,
        overwrite: bool,
    ) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        if let (Some(endpoint), Some(api_key)) = (&self.config.grafana_endpoint, &self.config.grafana_api_key) {
            // TODO: Implement actual HTTP request to Grafana API
            // For now, just log the deployment
            println!("Deploying dashboard to Grafana: {}", endpoint);
            println!("Dashboard JSON length: {}", dashboard_json.len());
            Ok("Dashboard deployed successfully".to_string())
        } else {
            Err("Grafana endpoint or API key not configured".into())
        }
    }
}

/// Shared state for dashboard API handlers
#[derive(Clone)]
struct DashboardApiState {
    config: DashboardApiConfig,
    observability_manager: Arc<ObservabilityManager>,
    dashboard_versions: Arc<RwLock<HashMap<String, Vec<DashboardConfig>>>>,
}

/// Health check handler
async fn health_check() -> Json<ApiResponse<String>> {
    Json(ApiResponse {
        success: true,
        data: Some("Dashboard API is healthy".to_string()),
        error: None,
    })
}

/// List templates handler
async fn list_templates(
    State(state): State<DashboardApiState>,
) -> Json<ApiResponse<Vec<String>>> {
    let templates = state.observability_manager.get_dashboard_templates();
    Json(ApiResponse {
        success: true,
        data: Some(templates),
        error: None,
    })
}

/// Get template details handler
async fn get_template(
    State(state): State<DashboardApiState>,
    Path(template_name): Path<String>,
) -> Json<ApiResponse<super::dashboards::DashboardTemplate>> {
    match state.observability_manager.get_dashboard_generator().get_template(&template_name) {
        Some(template) => Json(ApiResponse {
            success: true,
            data: Some(template.clone()),
            error: None,
        }),
        None => Json(ApiResponse {
            success: false,
            data: None,
            error: Some(format!("Template '{}' not found", template_name)),
        }),
    }
}

/// Create dashboard handler
async fn create_dashboard(
    State(state): State<DashboardApiState>,
    Json(request): Json<CreateDashboardRequest>,
) -> Json<ApiResponse<DashboardMetadata>> {
    match state.observability_manager.generate_dashboard(&request.template_name, request.parameters).await {
        Ok(config) => {
            let dashboard_id = format!("{}_{}", request.template_name, chrono::Utc::now().timestamp());

            // Store version
            let mut versions_lock = state.dashboard_versions.write().await;
            versions_lock.entry(dashboard_id.clone()).or_insert_with(Vec::new).push(config.clone());

            let metadata = DashboardMetadata {
                id: dashboard_id.clone(),
                title: config.title.clone(),
                description: config.description.clone(),
                template: request.template_name,
                created_at: chrono::Utc::now().to_rfc3339(),
                updated_at: chrono::Utc::now().to_rfc3339(),
                version: 1,
            };

            Json(ApiResponse {
                success: true,
                data: Some(metadata),
                error: None,
            })
        }
        Err(e) => Json(ApiResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
        }),
    }
}

/// Get dashboard handler
async fn get_dashboard(
    State(state): State<DashboardApiState>,
    Path(dashboard_id): Path<String>,
) -> Json<ApiResponse<String>> {
    let versions_lock = state.dashboard_versions.read().await;
    if let Some(versions_list) = versions_lock.get(&dashboard_id) {
        if let Some(latest) = versions_list.last() {
            match state.observability_manager.export_dashboard_json(latest) {
                Ok(json) => Json(ApiResponse {
                    success: true,
                    data: Some(json),
                    error: None,
                }),
                Err(e) => Json(ApiResponse {
                    success: false,
                    data: None,
                    error: Some(e.to_string()),
                }),
            }
        } else {
            Json(ApiResponse {
                success: false,
                data: None,
                error: Some("Dashboard not found".to_string()),
            })
        }
    } else {
        Json(ApiResponse {
            success: false,
            data: None,
            error: Some("Dashboard not found".to_string()),
        })
    }
}

/// Deploy dashboard handler
async fn deploy_dashboard(
    State(state): State<DashboardApiState>,
    Path(dashboard_id): Path<String>,
    Json(request): Json<DeployDashboardRequest>,
) -> Json<ApiResponse<String>> {
    if !state.config.enable_deployment {
        return Json(ApiResponse {
            success: false,
            data: None,
            error: Some("Dashboard deployment is disabled".to_string()),
        });
    }

    // TODO: Implement actual Grafana deployment
    // For now, just return success
    Json(ApiResponse {
        success: true,
        data: Some(format!("Dashboard {} deployed successfully", dashboard_id)),
        error: None,
    })
}

/// List dashboards handler
async fn list_dashboards(
    State(state): State<DashboardApiState>,
) -> Json<ApiResponse<Vec<DashboardMetadata>>> {
    let versions_lock = state.dashboard_versions.read().await;
    let dashboards: Vec<DashboardMetadata> = versions_lock.iter()
        .filter_map(|(id, versions_list)| {
            versions_list.last().map(|config| DashboardMetadata {
                id: id.clone(),
                title: config.title.clone(),
                description: config.description.clone(),
                template: "unknown".to_string(), // TODO: Store template info
                created_at: chrono::Utc::now().to_rfc3339(),
                updated_at: chrono::Utc::now().to_rfc3339(),
                version: versions_list.len() as u32,
            })
        })
        .collect();

    Json(ApiResponse {
        success: true,
        data: Some(dashboards),
        error: None,
    })
}

/// WebSocket handler for real-time updates
async fn websocket_handler(
    State(state): State<DashboardApiState>,
    Path(dashboard_id): Path<String>,
    ws: WebSocketUpgrade,
) -> axum::response::Response {
    ws.on_upgrade(move |socket| handle_websocket(socket, state.observability_manager.clone(), dashboard_id))
}

/// Handle WebSocket connection for real-time updates
async fn handle_websocket(
    socket: axum::extract::ws::WebSocket,
    manager: Arc<ObservabilityManager>,
    dashboard_id: String,
) {
    let (sender, receiver) = socket.split();
    let (tx, rx) = tokio::sync::mpsc::unbounded_channel();

    // Register connection with real-time manager
    manager.get_realtime_dashboard_manager().register_connection(&dashboard_id, tx).await;

    // Handle incoming messages
    let receive_task = tokio::spawn(async move {
        let mut receiver = receiver;
        while let Some(msg) = receiver.next().await {
            match msg {
                Ok(axum::extract::ws::Message::Close(_)) => break,
                Ok(_) => {} // Handle other messages if needed
                Err(_) => break,
            }
        }
    });

    // Handle outgoing messages
    let send_task = tokio::spawn(async move {
        let mut rx = tokio_stream::wrappers::UnboundedReceiverStream::new(rx);
        let mut sender = sender;
        while let Some(msg) = rx.next().await {
            if sender.send(axum::extract::ws::Message::Text(msg)).await.is_err() {
                break;
            }
        }
    });

    // Wait for either task to complete
    tokio::select! {
        _ = receive_task => {}
        _ = send_task => {}
    }
}

/// Default configuration for dashboard API
impl Default for DashboardApiConfig {
    fn default() -> Self {
        Self {
            base_path: "api/dashboards".to_string(),
            enable_deployment: false,
            grafana_endpoint: None,
            grafana_api_key: None,
        }
    }
}