//! # Dashboard Framework Module
//!
//! This module provides a comprehensive dashboard framework for generating,
//! managing, and deploying Grafana dashboards with real-time monitoring capabilities.
//!
//! ## Features
//!
//! - Dashboard template system with parameterization
//! - Automated dashboard generation from metrics
//! - Real-time data streaming integration
//! - Dashboard versioning and deployment automation
//! - Custom visualization components
//! - Integration with existing Grafana infrastructure

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

/// Dashboard configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DashboardConfig {
    /// Dashboard title
    pub title: String,
    /// Dashboard description
    pub description: String,
    /// Dashboard tags
    pub tags: Vec<String>,
    /// Refresh interval
    pub refresh: String,
    /// Time range
    pub time_range: TimeRange,
    /// Dashboard panels
    pub panels: Vec<Panel>,
    /// Template variables
    pub templating: Templating,
    /// Dashboard annotations
    pub annotations: Annotations,
}

/// Time range configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TimeRange {
    pub from: String,
    pub to: String,
}

/// Panel configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Panel {
    /// Panel ID
    pub id: u32,
    /// Panel title
    pub title: String,
    /// Panel type
    pub panel_type: PanelType,
    /// Grid position
    pub grid_pos: GridPos,
    /// Data source
    pub datasource: String,
    /// Panel targets (queries)
    pub targets: Vec<Target>,
    /// Panel options
    pub options: serde_json::Value,
    /// Field configuration
    pub field_config: serde_json::Value,
}

/// Panel types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PanelType {
    Timeseries,
    Stat,
    Table,
    Gauge,
    Barchart,
    Heatmap,
    Logs,
    Custom(String),
}

/// Grid position
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GridPos {
    pub h: u32,
    pub w: u32,
    pub x: u32,
    pub y: u32,
}

/// Query target
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Target {
    /// Target expression (PromQL, etc.)
    pub expr: String,
    /// Legend format
    pub legend_format: String,
    /// Target reference ID
    pub ref_id: String,
}

/// Template variables
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Templating {
    pub list: Vec<TemplateVariable>,
}

/// Template variable
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateVariable {
    /// Variable name
    pub name: String,
    /// Variable type
    pub var_type: String,
    /// Variable query
    pub query: String,
    /// Default value
    pub default: String,
    /// Variable options
    pub options: Vec<String>,
}

/// Annotations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Annotations {
    pub list: Vec<Annotation>,
}

/// Annotation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Annotation {
    /// Annotation name
    pub name: String,
    /// Data source
    pub datasource: String,
    /// Query
    pub query: String,
    /// Enable annotation
    pub enable: bool,
}

/// Dashboard template
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DashboardTemplate {
    /// Template name
    pub name: String,
    /// Template description
    pub description: String,
    /// Template parameters
    pub parameters: HashMap<String, TemplateParameter>,
    /// Base dashboard configuration
    pub base_config: DashboardConfig,
}

/// Template parameter
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateParameter {
    /// Parameter name
    pub name: String,
    /// Parameter type
    pub param_type: ParameterType,
    /// Default value
    pub default: String,
    /// Parameter description
    pub description: String,
    /// Validation regex
    pub validation: Option<String>,
}

/// Parameter types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ParameterType {
    String,
    Number,
    Boolean,
    Select,
}

/// Dashboard generator
pub struct DashboardGenerator {
    templates: HashMap<String, DashboardTemplate>,
    generated_dashboards: Arc<RwLock<HashMap<String, DashboardConfig>>>,
}

/// Real-time dashboard manager
pub struct RealTimeDashboardManager {
    generator: DashboardGenerator,
    websocket_connections: Arc<RwLock<HashMap<String, Vec<tokio::sync::mpsc::UnboundedSender<String>>>>>,
}

impl DashboardGenerator {
    /// Create a new dashboard generator
    pub fn new() -> Self {
        let mut templates = HashMap::new();

        // Initialize built-in templates
        templates.insert("system_overview".to_string(), Self::create_system_overview_template());
        templates.insert("compliance_monitoring".to_string(), Self::create_compliance_template());
        templates.insert("distributed_tracing".to_string(), Self::create_tracing_template());
        templates.insert("log_analysis".to_string(), Self::create_log_analysis_template());
        templates.insert("performance_profiling".to_string(), Self::create_performance_template());

        Self {
            templates,
            generated_dashboards: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Generate dashboard from template
    pub async fn generate_dashboard(
        &self,
        template_name: &str,
        parameters: HashMap<String, String>,
    ) -> Result<DashboardConfig, Box<dyn std::error::Error + Send + Sync>> {
        let template = self.templates.get(template_name)
            .ok_or_else(|| format!("Template '{}' not found", template_name))?;

        let mut config = template.base_config.clone();

        // Apply parameters
        self.apply_parameters(&mut config, &parameters)?;

        // Generate unique ID and store
        let dashboard_id = format!("{}_{}", template_name, chrono::Utc::now().timestamp());
        let mut dashboards = self.generated_dashboards.write().await;
        dashboards.insert(dashboard_id.clone(), config.clone());

        Ok(config)
    }

    /// Apply template parameters to dashboard configuration
    fn apply_parameters(
        &self,
        config: &mut DashboardConfig,
        parameters: &HashMap<String, String>,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Apply parameters to title
        if let Some(service_name) = parameters.get("service_name") {
            config.title = config.title.replace("{{service_name}}", service_name);
        }

        // Apply parameters to targets
        for panel in &mut config.panels {
            for target in &mut panel.targets {
                for (key, value) in parameters {
                    target.expr = target.expr.replace(&format!("{{{{{}}}}}", key), value);
                    target.legend_format = target.legend_format.replace(&format!("{{{{{}}}}}", key), value);
                }
            }
        }

        Ok(())
    }

    /// Get available templates
    pub fn get_templates(&self) -> Vec<String> {
        self.templates.keys().cloned().collect()
    }

    /// Get template details
    pub fn get_template(&self, name: &str) -> Option<&DashboardTemplate> {
        self.templates.get(name)
    }

    /// Export dashboard as JSON
    pub fn export_dashboard_json(&self, config: &DashboardConfig) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let grafana_dashboard = GrafanaDashboard::from_config(config);
        Ok(serde_json::to_string_pretty(&grafana_dashboard)?)
    }

    /// Create system overview template
    fn create_system_overview_template() -> DashboardTemplate {
        let mut parameters = HashMap::new();
        parameters.insert("service_name".to_string(), TemplateParameter {
            name: "service_name".to_string(),
            param_type: ParameterType::String,
            default: "infinitium-signal".to_string(),
            description: "Service name for dashboard".to_string(),
            validation: None,
        });

        DashboardTemplate {
            name: "system_overview".to_string(),
            description: "System overview dashboard with key metrics and health indicators".to_string(),
            parameters,
            base_config: DashboardConfig {
                title: "{{service_name}} - System Overview",
                description: "Real-time system monitoring dashboard".to_string(),
                tags: vec!["system".to_string(), "overview".to_string()],
                refresh: "30s".to_string(),
                time_range: TimeRange {
                    from: "now-1h".to_string(),
                    to: "now".to_string(),
                },
                panels: vec![
                    // Request Rate Panel
                    Panel {
                        id: 1,
                        title: "Request Rate".to_string(),
                        panel_type: PanelType::Timeseries,
                        grid_pos: GridPos { h: 8, w: 12, x: 0, y: 0 },
                        datasource: "Prometheus".to_string(),
                        targets: vec![Target {
                            expr: "rate(infinitium_signal_requests_total[5m])".to_string(),
                            legend_format: "{{method}} {{endpoint}}".to_string(),
                            ref_id: "A".to_string(),
                        }],
                        options: serde_json::json!({
                            "legend": { "calcs": [], "displayMode": "list", "placement": "bottom" },
                            "tooltip": { "mode": "single" }
                        }),
                        field_config: serde_json::json!({
                            "defaults": {
                                "color": { "mode": "palette-classic" },
                                "custom": {
                                    "axisLabel": "",
                                    "axisPlacement": "auto",
                                    "barAlignment": 0,
                                    "drawStyle": "line",
                                    "fillOpacity": 10,
                                    "gradientMode": "none",
                                    "hideFrom": { "legend": false, "tooltip": false, "vis": false },
                                    "lineInterpolation": "linear",
                                    "lineWidth": 1,
                                    "pointSize": 5,
                                    "scaleDistribution": { "type": "linear" },
                                    "showPoints": "never",
                                    "spanNulls": false,
                                    "stacking": { "group": "A", "mode": "none" },
                                    "thresholdsStyle": { "mode": "off" }
                                },
                                "mappings": [],
                                "thresholds": {
                                    "mode": "absolute",
                                    "steps": [
                                        { "color": "green", "value": null },
                                        { "color": "red", "value": 80 }
                                    ]
                                },
                                "unit": "reqps"
                            },
                            "overrides": []
                        }),
                    },
                    // Service Status Panel
                    Panel {
                        id: 2,
                        title: "Service Status".to_string(),
                        panel_type: PanelType::Stat,
                        grid_pos: GridPos { h: 8, w: 12, x: 12, y: 0 },
                        datasource: "Prometheus".to_string(),
                        targets: vec![Target {
                            expr: "up{job=\"{{service_name}}\"}".to_string(),
                            legend_format: "Service Status".to_string(),
                            ref_id: "A".to_string(),
                        }],
                        options: serde_json::json!({
                            "colorMode": "value",
                            "graphMode": "area",
                            "justifyMode": "auto",
                            "orientation": "auto",
                            "reduceOptions": {
                                "calcs": ["lastNotNull"],
                                "fields": "",
                                "values": false
                            },
                            "text": {},
                            "textMode": "auto"
                        }),
                        field_config: serde_json::json!({
                            "defaults": {
                                "color": { "mode": "thresholds" },
                                "mappings": [],
                                "thresholds": {
                                    "mode": "absolute",
                                    "steps": [
                                        { "color": "green", "value": null },
                                        { "color": "red", "value": 1 }
                                    ]
                                },
                                "unit": "short"
                            },
                            "overrides": []
                        }),
                    },
                ],
                templating: Templating { list: vec![] },
                annotations: Annotations { list: vec![] },
            },
        }
    }

    /// Create compliance monitoring template
    fn create_compliance_template() -> DashboardTemplate {
        let mut parameters = HashMap::new();
        parameters.insert("service_name".to_string(), TemplateParameter {
            name: "service_name".to_string(),
            param_type: ParameterType::String,
            default: "infinitium-signal".to_string(),
            description: "Service name for dashboard".to_string(),
            validation: None,
        });

        DashboardTemplate {
            name: "compliance_monitoring".to_string(),
            description: "License compliance and ML model performance monitoring".to_string(),
            parameters,
            base_config: DashboardConfig {
                title: "{{service_name}} - Compliance Monitoring",
                description: "License detection accuracy and compliance scan performance".to_string(),
                tags: vec!["compliance".to_string(), "license".to_string()],
                refresh: "1m".to_string(),
                time_range: TimeRange {
                    from: "now-24h".to_string(),
                    to: "now".to_string(),
                },
                panels: vec![
                    // License Detection Accuracy
                    Panel {
                        id: 1,
                        title: "License Detection Accuracy".to_string(),
                        panel_type: PanelType::Timeseries,
                        grid_pos: GridPos { h: 8, w: 12, x: 0, y: 0 },
                        datasource: "Prometheus".to_string(),
                        targets: vec![
                            Target {
                                expr: "license_precision".to_string(),
                                legend_format: "Precision".to_string(),
                                ref_id: "A".to_string(),
                            },
                            Target {
                                expr: "license_recall".to_string(),
                                legend_format: "Recall".to_string(),
                                ref_id: "B".to_string(),
                            },
                            Target {
                                expr: "license_f1_score".to_string(),
                                legend_format: "F1 Score".to_string(),
                                ref_id: "C".to_string(),
                            },
                        ],
                        options: serde_json::json!({
                            "legend": { "calcs": [], "displayMode": "list", "placement": "bottom" },
                            "tooltip": { "mode": "single" }
                        }),
                        field_config: serde_json::json!({
                            "defaults": {
                                "color": { "mode": "palette-classic" },
                                "custom": {
                                    "axisLabel": "",
                                    "axisPlacement": "auto",
                                    "barAlignment": 0,
                                    "drawStyle": "line",
                                    "fillOpacity": 10,
                                    "gradientMode": "none",
                                    "hideFrom": { "legend": false, "tooltip": false, "vis": false },
                                    "lineInterpolation": "linear",
                                    "lineWidth": 1,
                                    "pointSize": 5,
                                    "scaleDistribution": { "type": "linear" },
                                    "showPoints": "never",
                                    "spanNulls": false,
                                    "stacking": { "group": "A", "mode": "none" },
                                    "thresholdsStyle": { "mode": "off" }
                                },
                                "mappings": [],
                                "thresholds": {
                                    "mode": "absolute",
                                    "steps": [
                                        { "color": "green", "value": null },
                                        { "color": "red", "value": 80 }
                                    ]
                                },
                                "unit": "percentunit"
                            },
                            "overrides": []
                        }),
                    },
                ],
                templating: Templating { list: vec![] },
                annotations: Annotations { list: vec![] },
            },
        }
    }

    /// Create distributed tracing template
    fn create_tracing_template() -> DashboardTemplate {
        let mut parameters = HashMap::new();
        parameters.insert("service_name".to_string(), TemplateParameter {
            name: "service_name".to_string(),
            param_type: ParameterType::String,
            default: "infinitium-signal".to_string(),
            description: "Service name for dashboard".to_string(),
            validation: None,
        });

        DashboardTemplate {
            name: "distributed_tracing".to_string(),
            description: "Service dependency visualization and trace performance analysis".to_string(),
            parameters,
            base_config: DashboardConfig {
                title: "{{service_name}} - Distributed Tracing",
                description: "End-to-end request flow and trace analysis".to_string(),
                tags: vec!["tracing".to_string(), "distributed".to_string()],
                refresh: "30s".to_string(),
                time_range: TimeRange {
                    from: "now-1h".to_string(),
                    to: "now".to_string(),
                },
                panels: vec![
                    // Trace Duration
                    Panel {
                        id: 1,
                        title: "Trace Duration".to_string(),
                        panel_type: PanelType::Timeseries,
                        grid_pos: GridPos { h: 8, w: 12, x: 0, y: 0 },
                        datasource: "Jaeger".to_string(),
                        targets: vec![Target {
                            expr: "histogram_quantile(0.95, rate(trace_duration_seconds_bucket[5m]))".to_string(),
                            legend_format: "95th percentile".to_string(),
                            ref_id: "A".to_string(),
                        }],
                        options: serde_json::json!({
                            "legend": { "calcs": [], "displayMode": "list", "placement": "bottom" },
                            "tooltip": { "mode": "single" }
                        }),
                        field_config: serde_json::json!({
                            "defaults": {
                                "color": { "mode": "palette-classic" },
                                "unit": "s"
                            },
                            "overrides": []
                        }),
                    },
                ],
                templating: Templating { list: vec![] },
                annotations: Annotations { list: vec![] },
            },
        }
    }

    /// Create log analysis template
    fn create_log_analysis_template() -> DashboardTemplate {
        let mut parameters = HashMap::new();
        parameters.insert("service_name".to_string(), TemplateParameter {
            name: "service_name".to_string(),
            param_type: ParameterType::String,
            default: "infinitium-signal".to_string(),
            description: "Service name for dashboard".to_string(),
            validation: None,
        });

        DashboardTemplate {
            name: "log_analysis".to_string(),
            description: "Log volume trends and error pattern analysis".to_string(),
            parameters,
            base_config: DashboardConfig {
                title: "{{service_name}} - Log Analysis",
                description: "Log monitoring and anomaly detection".to_string(),
                tags: vec!["logs".to_string(), "analysis".to_string()],
                refresh: "30s".to_string(),
                time_range: TimeRange {
                    from: "now-6h".to_string(),
                    to: "now".to_string(),
                },
                panels: vec![
                    // Log Volume
                    Panel {
                        id: 1,
                        title: "Log Volume".to_string(),
                        panel_type: PanelType::Timeseries,
                        grid_pos: GridPos { h: 8, w: 12, x: 0, y: 0 },
                        datasource: "Loki".to_string(),
                        targets: vec![Target {
                            expr: "rate({job=\"{{service_name}}\"}[5m])".to_string(),
                            legend_format: "Log rate".to_string(),
                            ref_id: "A".to_string(),
                        }],
                        options: serde_json::json!({
                            "legend": { "calcs": [], "displayMode": "list", "placement": "bottom" },
                            "tooltip": { "mode": "single" }
                        }),
                        field_config: serde_json::json!({
                            "defaults": {
                                "color": { "mode": "palette-classic" },
                                "unit": "reqps"
                            },
                            "overrides": []
                        }),
                    },
                ],
                templating: Templating { list: vec![] },
                annotations: Annotations { list: vec![] },
            },
        }
    }

    /// Create performance profiling template
    fn create_performance_template() -> DashboardTemplate {
        let mut parameters = HashMap::new();
        parameters.insert("service_name".to_string(), TemplateParameter {
            name: "service_name".to_string(),
            param_type: ParameterType::String,
            default: "infinitium-signal".to_string(),
            description: "Service name for dashboard".to_string(),
            validation: None,
        });

        DashboardTemplate {
            name: "performance_profiling".to_string(),
            description: "Resource utilization and bottleneck identification".to_string(),
            parameters,
            base_config: DashboardConfig {
                title: "{{service_name}} - Performance Profiling",
                description: "System performance monitoring and optimization insights".to_string(),
                tags: vec!["performance".to_string(), "profiling".to_string()],
                refresh: "30s".to_string(),
                time_range: TimeRange {
                    from: "now-1h".to_string(),
                    to: "now".to_string(),
                },
                panels: vec![
                    // CPU Usage
                    Panel {
                        id: 1,
                        title: "CPU Usage".to_string(),
                        panel_type: PanelType::Timeseries,
                        grid_pos: GridPos { h: 8, w: 12, x: 0, y: 0 },
                        datasource: "Prometheus".to_string(),
                        targets: vec![Target {
                            expr: "system_cpu_usage_percent".to_string(),
                            legend_format: "CPU Usage %".to_string(),
                            ref_id: "A".to_string(),
                        }],
                        options: serde_json::json!({
                            "legend": { "calcs": [], "displayMode": "list", "placement": "bottom" },
                            "tooltip": { "mode": "single" }
                        }),
                        field_config: serde_json::json!({
                            "defaults": {
                                "color": { "mode": "palette-classic" },
                                "unit": "percent"
                            },
                            "overrides": []
                        }),
                    },
                    // Memory Usage
                    Panel {
                        id: 2,
                        title: "Memory Usage".to_string(),
                        panel_type: PanelType::Timeseries,
                        grid_pos: GridPos { h: 8, w: 12, x: 12, y: 0 },
                        datasource: "Prometheus".to_string(),
                        targets: vec![Target {
                            expr: "system_memory_usage_percent".to_string(),
                            legend_format: "Memory Usage %".to_string(),
                            ref_id: "A".to_string(),
                        }],
                        options: serde_json::json!({
                            "legend": { "calcs": [], "displayMode": "list", "placement": "bottom" },
                            "tooltip": { "mode": "single" }
                        }),
                        field_config: serde_json::json!({
                            "defaults": {
                                "color": { "mode": "palette-classic" },
                                "unit": "percent"
                            },
                            "overrides": []
                        }),
                    },
                ],
                templating: Templating { list: vec![] },
                annotations: Annotations { list: vec![] },
            },
        }
    }
}

/// Grafana dashboard format
#[derive(Debug, Clone, Serialize, Deserialize)]
struct GrafanaDashboard {
    pub dashboard: GrafanaDashboardInner,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct GrafanaDashboardInner {
    pub id: Option<u32>,
    pub title: String,
    pub description: String,
    pub tags: Vec<String>,
    pub refresh: String,
    pub time: serde_json::Value,
    pub timepicker: serde_json::Value,
    pub templating: serde_json::Value,
    pub annotations: serde_json::Value,
    pub panels: Vec<serde_json::Value>,
    pub schema_version: u32,
    pub version: u32,
    pub links: Vec<serde_json::Value>,
    pub editable: bool,
    pub gnet_id: Option<u32>,
    pub graph_tooltip: u32,
    pub uid: String,
    pub style: String,
}

impl GrafanaDashboard {
    fn from_config(config: &DashboardConfig) -> Self {
        let uid = format!("{}-{}", config.title.to_lowercase().replace(" ", "-"), chrono::Utc::now().timestamp());

        GrafanaDashboard {
            dashboard: GrafanaDashboardInner {
                id: None,
                title: config.title.clone(),
                description: config.description.clone(),
                tags: config.tags.clone(),
                refresh: config.refresh.clone(),
                time: serde_json::json!({
                    "from": config.time_range.from,
                    "to": config.time_range.to
                }),
                timepicker: serde_json::json!({}),
                templating: serde_json::json!({ "list": [] }),
                annotations: serde_json::json!({ "list": [] }),
                panels: config.panels.iter().map(|panel| {
                    serde_json::json!({
                        "id": panel.id,
                        "title": panel.title,
                        "type": match panel.panel_type {
                            PanelType::Timeseries => "timeseries",
                            PanelType::Stat => "stat",
                            PanelType::Table => "table",
                            PanelType::Gauge => "gauge",
                            PanelType::Barchart => "barchart",
                            PanelType::Heatmap => "heatmap",
                            PanelType::Logs => "logs",
                            PanelType::Custom(ref t) => t,
                        },
                        "gridPos": {
                            "h": panel.grid_pos.h,
                            "w": panel.grid_pos.w,
                            "x": panel.grid_pos.x,
                            "y": panel.grid_pos.y
                        },
                        "datasource": panel.datasource,
                        "targets": panel.targets.iter().map(|target| {
                            serde_json::json!({
                                "expr": target.expr,
                                "legendFormat": target.legend_format,
                                "refId": target.ref_id
                            })
                        }).collect::<Vec<_>>(),
                        "options": panel.options,
                        "fieldConfig": panel.field_config
                    })
                }).collect(),
                schema_version: 27,
                version: 1,
                links: vec![],
                editable: true,
                gnet_id: None,
                graph_tooltip: 0,
                uid,
                style: "dark".to_string(),
            },
        }
    }
}

impl RealTimeDashboardManager {
    /// Create a new real-time dashboard manager
    pub fn new(generator: DashboardGenerator) -> Self {
        Self {
            generator,
            websocket_connections: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Register WebSocket connection for real-time updates
    pub async fn register_connection(&self, dashboard_id: &str, sender: tokio::sync::mpsc::UnboundedSender<String>) {
        let mut connections = self.websocket_connections.write().await;
        connections.entry(dashboard_id.to_string()).or_insert_with(Vec::new).push(sender);
    }

    /// Broadcast real-time update to dashboard subscribers
    pub async fn broadcast_update(&self, dashboard_id: &str, update_data: serde_json::Value) {
        if let Some(connections) = self.websocket_connections.read().await.get(dashboard_id) {
            let message = serde_json::to_string(&update_data).unwrap_or_default();
            for sender in connections {
                let _ = sender.send(message.clone());
            }
        }
    }

    /// Get dashboard generator
    pub fn generator(&self) -> &DashboardGenerator {
        &self.generator
    }
}