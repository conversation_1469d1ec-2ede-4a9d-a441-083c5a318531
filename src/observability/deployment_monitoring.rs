//! # Deployment Monitoring Module
//!
//! Comprehensive deployment monitoring and observability for CI/CD pipelines.
//! Tracks deployment events, performance metrics, rollbacks, and provides
//! real-time monitoring and alerting capabilities.

pub mod events;
pub mod metrics;
pub mod rollback;
pub mod alerting;
pub mod analytics;
pub mod api;
pub mod webhooks;
pub mod security;

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;

use crate::observability::{ObservabilityManager, instrumentation};
use crate::error::{InfinitumError, Result};
use crate::compliance::ci_cd_scanner::{CICDPlaform, ScanMode};

/// Deployment status enumeration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum DeploymentStatus {
    /// Deployment initiated
    Initiated,
    /// Deployment in progress
    InProgress,
    /// Deployment completed successfully
    Completed,
    /// Deployment failed
    Failed,
    /// Deployment rolled back
    RolledBack,
    /// Deployment cancelled
    Cancelled,
    /// Deployment timed out
    Timeout,
}

/// Deployment environment
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum DeploymentEnvironment {
    /// Development environment
    Development,
    /// Staging environment
    Staging,
    /// Production environment
    Production,
    /// Testing environment
    Testing,
    /// Custom environment
    Custom(String),
}

/// Deployment event types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum DeploymentEventType {
    /// Deployment started
    DeploymentStarted,
    /// Deployment stage completed
    StageCompleted,
    /// Deployment completed
    DeploymentCompleted,
    /// Deployment failed
    DeploymentFailed,
    /// Rollback initiated
    RollbackInitiated,
    /// Rollback completed
    RollbackCompleted,
    /// Rollback failed
    RollbackFailed,
    /// Health check passed
    HealthCheckPassed,
    /// Health check failed
    HealthCheckFailed,
    /// Resource utilization alert
    ResourceAlert,
    /// Performance degradation detected
    PerformanceDegradation,
}

/// Deployment configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentConfig {
    /// Enable deployment monitoring
    pub enabled: bool,
    /// Maximum deployment duration in seconds
    pub max_deployment_duration_seconds: u64,
    /// Health check timeout in seconds
    pub health_check_timeout_seconds: u64,
    /// Enable automatic rollback on failure
    pub enable_auto_rollback: bool,
    /// Rollback timeout in seconds
    pub rollback_timeout_seconds: u64,
    /// Alert thresholds
    pub alert_thresholds: AlertThresholds,
    /// Notification channels
    pub notification_channels: Vec<NotificationChannel>,
    /// Webhook endpoints
    pub webhook_endpoints: Vec<String>,
}

/// Alert thresholds for deployment monitoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertThresholds {
    /// Maximum deployment duration threshold (seconds)
    pub max_deployment_duration: u64,
    /// Minimum success rate threshold (0.0-1.0)
    pub min_success_rate: f64,
    /// Maximum resource utilization threshold (0.0-1.0)
    pub max_resource_utilization: f64,
    /// Maximum error rate threshold (0.0-1.0)
    pub max_error_rate: f64,
}

/// Notification channel configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationChannel {
    /// Channel type
    pub channel_type: NotificationChannelType,
    /// Channel configuration
    pub config: HashMap<String, String>,
}

/// Notification channel types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum NotificationChannelType {
    /// Email notifications
    Email,
    /// Slack notifications
    Slack,
    /// Microsoft Teams notifications
    Teams,
    /// Webhook notifications
    Webhook,
    /// SMS notifications
    Sms,
}

/// Deployment metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentMetadata {
    /// Unique deployment ID
    pub deployment_id: Uuid,
    /// CI/CD platform
    pub platform: CICDPlaform,
    /// Environment
    pub environment: DeploymentEnvironment,
    /// Version/tag being deployed
    pub version: String,
    /// Git commit hash
    pub commit_hash: Option<String>,
    /// Branch name
    pub branch: Option<String>,
    /// User who initiated deployment
    pub initiated_by: String,
    /// Timestamp when deployment was initiated
    pub initiated_at: DateTime<Utc>,
    /// Build number
    pub build_number: Option<String>,
    /// Pipeline ID
    pub pipeline_id: Option<String>,
    /// Custom metadata
    pub custom_metadata: HashMap<String, serde_json::Value>,
}

/// Deployment stage information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentStage {
    /// Stage name
    pub name: String,
    /// Stage status
    pub status: DeploymentStatus,
    /// Stage start time
    pub started_at: DateTime<Utc>,
    /// Stage completion time
    pub completed_at: Option<DateTime<Utc>>,
    /// Stage duration in seconds
    pub duration_seconds: Option<u64>,
    /// Stage logs
    pub logs: Vec<String>,
    /// Stage artifacts
    pub artifacts: Vec<String>,
}

/// Deployment metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentMetrics {
    /// Total deployment duration
    pub total_duration_seconds: u64,
    /// CPU utilization during deployment
    pub cpu_utilization_percent: f64,
    /// Memory utilization during deployment
    pub memory_utilization_percent: f64,
    /// Network I/O during deployment
    pub network_io_bytes: u64,
    /// Disk I/O during deployment
    pub disk_io_bytes: u64,
    /// Number of health checks performed
    pub health_checks_count: u32,
    /// Number of successful health checks
    pub successful_health_checks: u32,
    /// Number of failed health checks
    pub failed_health_checks: u32,
}

/// Rollback information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RollbackInfo {
    /// Rollback ID
    pub rollback_id: Uuid,
    /// Reason for rollback
    pub reason: String,
    /// Rollback strategy
    pub strategy: RollbackStrategy,
    /// Rollback status
    pub status: DeploymentStatus,
    /// Rollback start time
    pub started_at: DateTime<Utc>,
    /// Rollback completion time
    pub completed_at: Option<DateTime<Utc>>,
    /// Rollback duration
    pub duration_seconds: Option<u64>,
    /// Previous deployment version
    pub previous_version: String,
    /// Rollback logs
    pub logs: Vec<String>,
}

/// Rollback strategy
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum RollbackStrategy {
    /// Immediate rollback to previous version
    Immediate,
    /// Gradual rollback with canary deployment
    Gradual,
    /// Blue-green rollback
    BlueGreen,
    /// Custom rollback strategy
    Custom(String),
}

/// Main deployment monitor
pub struct DeploymentMonitor {
    config: DeploymentConfig,
    observability_manager: Arc<ObservabilityManager>,
    active_deployments: Arc<RwLock<HashMap<Uuid, DeploymentMetadata>>>,
    deployment_history: Arc<RwLock<Vec<DeploymentRecord>>>,
    event_processor: events::DeploymentEventProcessor,
    metrics_collector: metrics::DeploymentMetricsCollector,
    rollback_manager: rollback::RollbackManager,
    alert_manager: alerting::DeploymentAlertManager,
    analytics_engine: analytics::DeploymentAnalyticsEngine,
    api_server: Option<api::DeploymentApiServer>,
    webhook_manager: webhooks::WebhookManager,
    security_monitor: security::DeploymentSecurityMonitor,
}

impl DeploymentMonitor {
    /// Create new deployment monitor
    pub fn new(
        config: DeploymentConfig,
        observability_manager: Arc<ObservabilityManager>,
    ) -> Self {
        let event_processor = events::DeploymentEventProcessor::new(observability_manager.clone());
        let metrics_collector = metrics::DeploymentMetricsCollector::new(observability_manager.clone());
        let rollback_manager = rollback::RollbackManager::new(observability_manager.clone());
        let alert_manager = alerting::DeploymentAlertManager::new(
            config.alert_thresholds.clone(),
            observability_manager.clone(),
        );
        let analytics_engine = analytics::DeploymentAnalyticsEngine::new(observability_manager.clone());
        let webhook_manager = webhooks::WebhookManager::new(config.webhook_endpoints.clone());
        let security_monitor = security::DeploymentSecurityMonitor::new(observability_manager.clone());

        Self {
            config,
            observability_manager,
            active_deployments: Arc::new(RwLock::new(HashMap::new())),
            deployment_history: Arc::new(RwLock::new(Vec::new())),
            event_processor,
            metrics_collector,
            rollback_manager,
            alert_manager,
            analytics_engine,
            api_server: None,
            webhook_manager,
            security_monitor,
        }
    }

    /// Initialize the deployment monitor
    pub async fn initialize(&mut self) -> Result<()> {
        // Initialize components
        self.event_processor.initialize().await?;
        self.metrics_collector.initialize().await?;
        self.rollback_manager.initialize().await?;
        self.alert_manager.initialize().await?;
        self.analytics_engine.initialize().await?;
        self.webhook_manager.initialize().await?;
        self.security_monitor.initialize().await?;

        // Start API server if enabled
        if self.config.enabled {
            let api_config = api::DeploymentApiConfig::default();
            self.api_server = Some(api::DeploymentApiServer::new(
                api_config,
                Arc::new(self.clone()),
            ));

            if let Some(server) = &self.api_server {
                server.start().await?;
            }
        }

        Ok(())
    }

    /// Start deployment monitoring for a CI/CD pipeline
    pub async fn start_deployment(
        &self,
        platform: CICDPlaform,
        environment: DeploymentEnvironment,
        version: String,
        initiated_by: String,
        metadata: HashMap<String, serde_json::Value>,
    ) -> Result<Uuid> {
        let deployment_id = Uuid::new_v4();
        let now = Utc::now();

        let deployment_metadata = DeploymentMetadata {
            deployment_id,
            platform,
            environment,
            version: version.clone(),
            commit_hash: metadata.get("commit_hash")
                .and_then(|v| v.as_str())
                .map(|s| s.to_string()),
            branch: metadata.get("branch")
                .and_then(|v| v.as_str())
                .map(|s| s.to_string()),
            initiated_by: initiated_by.clone(),
            initiated_at: now,
            build_number: metadata.get("build_number")
                .and_then(|v| v.as_str())
                .map(|s| s.to_string()),
            pipeline_id: metadata.get("pipeline_id")
                .and_then(|v| v.as_str())
                .map(|s| s.to_string()),
            custom_metadata: metadata,
        };

        // Record deployment start event
        self.event_processor.record_event(
            deployment_id,
            DeploymentEventType::DeploymentStarted,
            Some(format!("Deployment {} started by {}", version, initiated_by)),
            Some(serde_json::to_value(&deployment_metadata).unwrap_or_default()),
        ).await?;

        // Add to active deployments
        {
            let mut active = self.active_deployments.write().await;
            active.insert(deployment_id, deployment_metadata.clone());
        }

        // Record metrics
        self.metrics_collector.record_deployment_start(
            deployment_id,
            &platform,
            &environment,
            &version,
        ).await?;

        // Send webhook notification
        self.webhook_manager.send_deployment_event(
            "deployment_started",
            &deployment_metadata,
        ).await?;

        // Create span for deployment tracking
        let _span = instrumentation::create_span_with_attributes(
            "deployment_execution",
            vec![
                opentelemetry::KeyValue::new("deployment_id", deployment_id.to_string()),
                opentelemetry::KeyValue::new("platform", format!("{:?}", platform)),
                opentelemetry::KeyValue::new("environment", format!("{:?}", environment)),
                opentelemetry::KeyValue::new("version", version),
            ],
        );

        Ok(deployment_id)
    }

    /// Update deployment progress
    pub async fn update_deployment_progress(
        &self,
        deployment_id: Uuid,
        stage: String,
        status: DeploymentStatus,
        logs: Vec<String>,
    ) -> Result<()> {
        // Record stage completion event
        self.event_processor.record_event(
            deployment_id,
            DeploymentEventType::StageCompleted,
            Some(format!("Stage '{}' completed with status {:?}", stage, status)),
            Some(serde_json::json!({
                "stage": stage,
                "status": status,
                "logs_count": logs.len()
            })),
        ).await?;

        // Update metrics
        self.metrics_collector.record_stage_completion(
            deployment_id,
            &stage,
            &status,
        ).await?;

        Ok(())
    }

    /// Complete deployment
    pub async fn complete_deployment(
        &self,
        deployment_id: Uuid,
        status: DeploymentStatus,
        metrics: DeploymentMetrics,
        artifacts: Vec<String>,
    ) -> Result<()> {
        let completion_time = Utc::now();

        // Get deployment metadata
        let metadata = {
            let active = self.active_deployments.read().await;
            active.get(&deployment_id).cloned()
        };

        if let Some(metadata) = metadata {
            // Record completion event
            let event_type = match status {
                DeploymentStatus::Completed => DeploymentEventType::DeploymentCompleted,
                DeploymentStatus::Failed => DeploymentEventType::DeploymentFailed,
                _ => DeploymentEventType::DeploymentCompleted,
            };

            self.event_processor.record_event(
                deployment_id,
                event_type,
                Some(format!("Deployment completed with status {:?}", status)),
                Some(serde_json::json!({
                    "status": status,
                    "duration_seconds": metrics.total_duration_seconds,
                    "artifacts_count": artifacts.len()
                })),
            ).await?;

            // Record final metrics
            self.metrics_collector.record_deployment_completion(
                deployment_id,
                &status,
                &metrics,
            ).await?;

            // Check for alerts
            self.alert_manager.check_deployment_alerts(
                deployment_id,
                &metadata,
                &metrics,
            ).await?;

            // Send webhook notification
            self.webhook_manager.send_deployment_event(
                "deployment_completed",
                &metadata,
            ).await?;

            // Create deployment record
            let record = DeploymentRecord {
                metadata,
                status,
                completed_at: Some(completion_time),
                metrics,
                artifacts,
                stages: Vec::new(), // Would be populated from stage updates
                rollback_info: None,
            };

            // Add to history
            {
                let mut history = self.deployment_history.write().await;
                history.push(record);
            }

            // Remove from active deployments
            {
                let mut active = self.active_deployments.write().await;
                active.remove(&deployment_id);
            }

            // Update analytics
            self.analytics_engine.update_deployment_analytics(
                deployment_id,
                &status,
                &metrics,
            ).await?;
        }

        Ok(())
    }

    /// Initiate rollback
    pub async fn initiate_rollback(
        &self,
        deployment_id: Uuid,
        reason: String,
        strategy: RollbackStrategy,
    ) -> Result<Uuid> {
        self.rollback_manager.initiate_rollback(
            deployment_id,
            reason,
            strategy,
        ).await
    }

    /// Get deployment status
    pub async fn get_deployment_status(&self, deployment_id: Uuid) -> Result<Option<DeploymentStatus>> {
        // Check active deployments first
        {
            let active = self.active_deployments.read().await;
            if let Some(metadata) = active.get(&deployment_id) {
                // For active deployments, we might need to check current status
                // This would be enhanced with real-time status tracking
                return Ok(Some(DeploymentStatus::InProgress));
            }
        }

        // Check history
        {
            let history = self.deployment_history.read().await;
            for record in history.iter().rev() {
                if record.metadata.deployment_id == deployment_id {
                    return Ok(Some(record.status.clone()));
                }
            }
        }

        Ok(None)
    }

    /// Get deployment metrics
    pub async fn get_deployment_metrics(&self, deployment_id: Uuid) -> Result<Option<DeploymentMetrics>> {
        let history = self.deployment_history.read().await;
        for record in history.iter().rev() {
            if record.metadata.deployment_id == deployment_id {
                return Ok(Some(record.metrics.clone()));
            }
        }
        Ok(None)
    }

    /// Get deployment history
    pub async fn get_deployment_history(
        &self,
        environment: Option<DeploymentEnvironment>,
        limit: Option<usize>,
    ) -> Result<Vec<DeploymentRecord>> {
        let history = self.deployment_history.read().await;
        let mut filtered_history: Vec<_> = if let Some(env) = environment {
            history.iter()
                .filter(|record| record.metadata.environment == env)
                .cloned()
                .collect()
        } else {
            history.clone()
        };

        // Sort by completion time (most recent first)
        filtered_history.sort_by(|a, b| {
            b.completed_at.unwrap_or(b.metadata.initiated_at)
                .cmp(&a.completed_at.unwrap_or(a.metadata.initiated_at))
        });

        // Apply limit
        if let Some(limit) = limit {
            filtered_history.truncate(limit);
        }

        Ok(filtered_history)
    }

    /// Get deployment analytics
    pub async fn get_deployment_analytics(
        &self,
        time_range_hours: Option<u64>,
    ) -> Result<analytics::DeploymentAnalytics> {
        self.analytics_engine.get_analytics(time_range_hours).await
    }

    /// Shutdown deployment monitor
    pub async fn shutdown(&self) -> Result<()> {
        if let Some(server) = &self.api_server {
            server.stop().await?;
        }

        self.event_processor.shutdown().await?;
        self.metrics_collector.shutdown().await?;
        self.rollback_manager.shutdown().await?;
        self.alert_manager.shutdown().await?;
        self.analytics_engine.shutdown().await?;
        self.webhook_manager.shutdown().await?;
        self.security_monitor.shutdown().await?;

        Ok(())
    }
}

/// Deployment record for history
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentRecord {
    /// Deployment metadata
    pub metadata: DeploymentMetadata,
    /// Final deployment status
    pub status: DeploymentStatus,
    /// Completion timestamp
    pub completed_at: Option<DateTime<Utc>>,
    /// Deployment metrics
    pub metrics: DeploymentMetrics,
    /// Deployment artifacts
    pub artifacts: Vec<String>,
    /// Deployment stages
    pub stages: Vec<DeploymentStage>,
    /// Rollback information if applicable
    pub rollback_info: Option<RollbackInfo>,
}

impl Default for DeploymentConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            max_deployment_duration_seconds: 3600, // 1 hour
            health_check_timeout_seconds: 300,     // 5 minutes
            enable_auto_rollback: false,
            rollback_timeout_seconds: 1800,        // 30 minutes
            alert_thresholds: AlertThresholds::default(),
            notification_channels: Vec::new(),
            webhook_endpoints: Vec::new(),
        }
    }
}

impl Default for AlertThresholds {
    fn default() -> Self {
        Self {
            max_deployment_duration: 1800, // 30 minutes
            min_success_rate: 0.95,        // 95%
            max_resource_utilization: 0.90, // 90%
            max_error_rate: 0.05,          // 5%
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_deployment_config_default() {
        let config = DeploymentConfig::default();
        assert!(config.enabled);
        assert_eq!(config.max_deployment_duration_seconds, 3600);
        assert_eq!(config.health_check_timeout_seconds, 300);
        assert!(!config.enable_auto_rollback);
    }

    #[test]
    fn test_alert_thresholds_default() {
        let thresholds = AlertThresholds::default();
        assert_eq!(thresholds.max_deployment_duration, 1800);
        assert_eq!(thresholds.min_success_rate, 0.95);
        assert_eq!(thresholds.max_resource_utilization, 0.90);
        assert_eq!(thresholds.max_error_rate, 0.05);
    }
}