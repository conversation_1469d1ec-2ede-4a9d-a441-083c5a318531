//! # Deployment Alerting and Notifications
//!
//! Manages deployment alerts, notifications, and escalation policies
//! for comprehensive deployment monitoring and incident response.

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;

use crate::observability::{ObservabilityManager, instrumentation};
use crate::error::{InfinitumError, Result};
use super::{DeploymentMetadata, DeploymentMetrics, AlertThresholds, NotificationChannel, NotificationChannelType};

/// Alert configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AlertConfig {
    /// Enable alerting
    pub enabled: bool,
    /// Alert thresholds
    pub thresholds: AlertThresholds,
    /// Notification channels
    pub channels: Vec<NotificationChannel>,
    /// Escalation policy
    pub escalation_policy: EscalationPolicy,
    /// Alert cooldown period in seconds
    pub alert_cooldown_seconds: u64,
}

/// Escalation policy
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct EscalationPolicy {
    /// Escalation levels
    pub levels: Vec<EscalationLevel>,
    /// Maximum escalation level
    pub max_level: u32,
    /// Escalation delay in seconds
    pub escalation_delay_seconds: u64,
}

/// Escalation level
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EscalationLevel {
    /// Level number
    pub level: u32,
    /// Delay before escalation in seconds
    pub delay_seconds: u64,
    /// Channels to notify at this level
    pub channels: Vec<String>,
    /// Recipients for this level
    pub recipients: Vec<String>,
}

/// Deployment alert
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentAlert {
    /// Alert ID
    pub alert_id: Uuid,
    /// Deployment ID
    pub deployment_id: Uuid,
    /// Alert type
    pub alert_type: AlertType,
    /// Severity
    pub severity: AlertSeverity,
    /// Title
    pub title: String,
    /// Description
    pub description: String,
    /// Created timestamp
    pub created_at: DateTime<Utc>,
    /// Resolved timestamp
    pub resolved_at: Option<DateTime<Utc>>,
    /// Status
    pub status: AlertStatus,
    /// Labels for categorization
    pub labels: HashMap<String, String>,
    /// Annotations with additional data
    pub annotations: HashMap<String, String>,
}

/// Alert types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum AlertType {
    /// Deployment duration exceeded threshold
    DeploymentDurationExceeded,
    /// Deployment failed
    DeploymentFailed,
    /// Resource utilization high
    ResourceUtilizationHigh,
    /// Health check failed
    HealthCheckFailed,
    /// Rollback initiated
    RollbackInitiated,
    /// Rollback failed
    RollbackFailed,
    /// Performance degradation
    PerformanceDegradation,
    /// Custom alert
    Custom(String),
}

/// Alert severity levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum AlertSeverity {
    /// Info level
    Info,
    /// Warning level
    Warning,
    /// Error level
    Error,
    /// Critical level
    Critical,
}

/// Alert status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum AlertStatus {
    /// Alert is active
    Active,
    /// Alert is resolved
    Resolved,
    /// Alert is suppressed
    Suppressed,
}

/// Alert notification
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertNotification {
    /// Notification ID
    pub notification_id: Uuid,
    /// Alert ID
    pub alert_id: Uuid,
    /// Channel type
    pub channel_type: NotificationChannelType,
    /// Recipient
    pub recipient: String,
    /// Subject
    pub subject: String,
    /// Message
    pub message: String,
    /// Sent timestamp
    pub sent_at: DateTime<Utc>,
    /// Delivery status
    pub delivery_status: NotificationStatus,
}

/// Notification delivery status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum NotificationStatus {
    /// Notification sent successfully
    Sent,
    /// Notification failed to send
    Failed,
    /// Notification pending
    Pending,
}

/// Deployment alert manager
pub struct DeploymentAlertManager {
    config: AlertConfig,
    observability_manager: Arc<ObservabilityManager>,
    active_alerts: Arc<RwLock<HashMap<Uuid, DeploymentAlert>>>,
    alert_history: Arc<RwLock<Vec<DeploymentAlert>>>,
    notification_history: Arc<RwLock<Vec<AlertNotification>>>,
    alert_cooldowns: Arc<RwLock<HashMap<String, DateTime<Utc>>>>,
}

impl DeploymentAlertManager {
    /// Create new alert manager
    pub fn new(
        thresholds: AlertThresholds,
        observability_manager: Arc<ObservabilityManager>,
    ) -> Self {
        let config = AlertConfig {
            enabled: true,
            thresholds,
            channels: Vec::new(),
            escalation_policy: EscalationPolicy::default(),
            alert_cooldown_seconds: 300, // 5 minutes
        };

        Self {
            config,
            observability_manager,
            active_alerts: Arc::new(RwLock::new(HashMap::new())),
            alert_history: Arc::new(RwLock::new(Vec::new())),
            notification_history: Arc::new(RwLock::new(Vec::new())),
            alert_cooldowns: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Initialize the alert manager
    pub async fn initialize(&self) -> Result<()> {
        // Start alert monitoring task
        if self.config.enabled {
            self.start_alert_monitoring_task();
        }

        Ok(())
    }

    /// Check deployment alerts
    pub async fn check_deployment_alerts(
        &self,
        deployment_id: Uuid,
        metadata: &DeploymentMetadata,
        metrics: &DeploymentMetrics,
    ) -> Result<()> {
        // Check deployment duration
        if metrics.total_duration_seconds > self.config.thresholds.max_deployment_duration {
            self.create_alert(
                deployment_id,
                AlertType::DeploymentDurationExceeded,
                AlertSeverity::Warning,
                format!("Deployment duration exceeded threshold: {}s", metrics.total_duration_seconds),
                format!("Deployment {} exceeded maximum duration of {}s", deployment_id, self.config.thresholds.max_deployment_duration),
                Some(HashMap::from([
                    ("deployment_id".to_string(), deployment_id.to_string()),
                    ("duration".to_string(), metrics.total_duration_seconds.to_string()),
                    ("threshold".to_string(), self.config.thresholds.max_deployment_duration.to_string()),
                ])),
            ).await?;
        }

        // Check resource utilization
        if metrics.cpu_utilization_percent > self.config.thresholds.max_resource_utilization * 100.0 {
            self.create_alert(
                deployment_id,
                AlertType::ResourceUtilizationHigh,
                AlertSeverity::Warning,
                format!("High CPU utilization: {:.1}%", metrics.cpu_utilization_percent),
                format!("Deployment {} CPU utilization exceeded threshold", deployment_id),
                Some(HashMap::from([
                    ("deployment_id".to_string(), deployment_id.to_string()),
                    ("cpu_percent".to_string(), metrics.cpu_utilization_percent.to_string()),
                    ("threshold".to_string(), (self.config.thresholds.max_resource_utilization * 100.0).to_string()),
                ])),
            ).await?;
        }

        if metrics.memory_utilization_percent > self.config.thresholds.max_resource_utilization * 100.0 {
            self.create_alert(
                deployment_id,
                AlertType::ResourceUtilizationHigh,
                AlertSeverity::Warning,
                format!("High memory utilization: {:.1}%", metrics.memory_utilization_percent),
                format!("Deployment {} memory utilization exceeded threshold", deployment_id),
                Some(HashMap::from([
                    ("deployment_id".to_string(), deployment_id.to_string()),
                    ("memory_percent".to_string(), metrics.memory_utilization_percent.to_string()),
                    ("threshold".to_string(), (self.config.thresholds.max_resource_utilization * 100.0).to_string()),
                ])),
            ).await?;
        }

        // Check health checks
        let health_check_rate = if metrics.health_checks_count > 0 {
            metrics.successful_health_checks as f64 / metrics.health_checks_count as f64
        } else {
            1.0
        };

        if health_check_rate < self.config.thresholds.min_success_rate {
            self.create_alert(
                deployment_id,
                AlertType::HealthCheckFailed,
                AlertSeverity::Error,
                format!("Health check success rate below threshold: {:.1}%", health_check_rate * 100.0),
                format!("Deployment {} health checks failing", deployment_id),
                Some(HashMap::from([
                    ("deployment_id".to_string(), deployment_id.to_string()),
                    ("success_rate".to_string(), (health_check_rate * 100.0).to_string()),
                    ("threshold".to_string(), (self.config.thresholds.min_success_rate * 100.0).to_string()),
                ])),
            ).await?;
        }

        Ok(())
    }

    /// Create deployment alert
    pub async fn create_alert(
        &self,
        deployment_id: Uuid,
        alert_type: AlertType,
        severity: AlertSeverity,
        title: String,
        description: String,
        labels: Option<HashMap<String, String>>,
    ) -> Result<Uuid> {
        let alert_id = Uuid::new_v4();
        let now = Utc::now();

        // Check cooldown
        let alert_key = format!("{}:{}", deployment_id, alert_type);
        {
            let cooldowns = self.alert_cooldowns.read().await;
            if let Some(last_alert_time) = cooldowns.get(&alert_key) {
                let elapsed = (now - *last_alert_time).num_seconds() as u64;
                if elapsed < self.config.alert_cooldown_seconds {
                    // Alert is in cooldown, skip
                    return Ok(alert_id);
                }
            }
        }

        let alert = DeploymentAlert {
            alert_id,
            deployment_id,
            alert_type: alert_type.clone(),
            severity: severity.clone(),
            title: title.clone(),
            description: description.clone(),
            created_at: now,
            resolved_at: None,
            status: AlertStatus::Active,
            labels: labels.unwrap_or_default(),
            annotations: HashMap::new(),
        };

        // Store alert
        {
            let mut active_alerts = self.active_alerts.write().await;
            active_alerts.insert(alert_id, alert.clone());
        }

        // Update cooldown
        {
            let mut cooldowns = self.alert_cooldowns.write().await;
            cooldowns.insert(alert_key, now);
        }

        // Send notifications
        self.send_alert_notifications(&alert).await?;

        // Record metrics
        self.record_alert_metrics(&alert_type, &severity).await?;

        // Create OpenTelemetry span
        let _span = instrumentation::create_span_with_attributes(
            "deployment_alert",
            vec![
                opentelemetry::KeyValue::new("alert_id", alert_id.to_string()),
                opentelemetry::KeyValue::new("deployment_id", deployment_id.to_string()),
                opentelemetry::KeyValue::new("alert_type", format!("{:?}", alert_type)),
                opentelemetry::KeyValue::new("severity", format!("{:?}", severity)),
            ],
        );

        Ok(alert_id)
    }

    /// Resolve alert
    pub async fn resolve_alert(&self, alert_id: Uuid) -> Result<()> {
        let now = Utc::now();

        // Update alert status
        {
            let mut active_alerts = self.active_alerts.write().await;
            if let Some(alert) = active_alerts.get_mut(&alert_id) {
                alert.status = AlertStatus::Resolved;
                alert.resolved_at = Some(now);

                // Move to history
                let mut history = self.alert_history.write().await;
                history.push(alert.clone());

                // Remove from active
                active_alerts.remove(&alert_id);
            }
        }

        // Send resolution notification
        // This would send a resolution notification

        Ok(())
    }

    /// Get active alerts
    pub async fn get_active_alerts(&self) -> Result<Vec<DeploymentAlert>> {
        let active_alerts = self.active_alerts.read().await;
        Ok(active_alerts.values().cloned().collect())
    }

    /// Get alert history
    pub async fn get_alert_history(
        &self,
        deployment_id: Option<Uuid>,
        limit: Option<usize>,
    ) -> Result<Vec<DeploymentAlert>> {
        let history = self.alert_history.read().await;
        let mut filtered_history: Vec<_> = if let Some(deployment_id) = deployment_id {
            history.iter()
                .filter(|alert| alert.deployment_id == deployment_id)
                .cloned()
                .collect()
        } else {
            history.clone()
        };

        // Sort by creation time (most recent first)
        filtered_history.sort_by(|a, b| b.created_at.cmp(&a.created_at));

        // Apply limit
        if let Some(limit) = limit {
            filtered_history.truncate(limit);
        }

        Ok(filtered_history)
    }

    /// Send alert notifications
    async fn send_alert_notifications(&self, alert: &DeploymentAlert) -> Result<()> {
        for channel in &self.config.channels {
            let notification_id = Uuid::new_v4();
            let now = Utc::now();

            let notification = AlertNotification {
                notification_id,
                alert_id: alert.alert_id,
                channel_type: channel.channel_type.clone(),
                recipient: channel.config.get("recipient")
                    .cloned()
                    .unwrap_or_else(|| "default".to_string()),
                subject: alert.title.clone(),
                message: alert.description.clone(),
                sent_at: now,
                delivery_status: NotificationStatus::Pending,
            };

            // Send notification based on channel type
            let status = match self.send_notification(&channel, &notification).await {
                Ok(_) => NotificationStatus::Sent,
                Err(_) => NotificationStatus::Failed,
            };

            // Update notification status
            let mut updated_notification = notification;
            updated_notification.delivery_status = status;

            // Store notification
            {
                let mut history = self.notification_history.write().await;
                history.push(updated_notification);
            }
        }

        Ok(())
    }

    /// Send notification to specific channel
    async fn send_notification(
        &self,
        channel: &NotificationChannel,
        notification: &AlertNotification,
    ) -> Result<()> {
        match channel.channel_type {
            NotificationChannelType::Email => {
                // Send email notification
                self.send_email_notification(channel, notification).await
            }
            NotificationChannelType::Slack => {
                // Send Slack notification
                self.send_slack_notification(channel, notification).await
            }
            NotificationChannelType::Webhook => {
                // Send webhook notification
                self.send_webhook_notification(channel, notification).await
            }
            _ => {
                // Other channel types would be implemented similarly
                tracing::info!("Notification sent via {}: {}", channel.channel_type, notification.subject);
                Ok(())
            }
        }
    }

    /// Send email notification
    async fn send_email_notification(
        &self,
        _channel: &NotificationChannel,
        notification: &AlertNotification,
    ) -> Result<()> {
        // Email sending implementation would go here
        tracing::info!("Email notification: {} - {}", notification.subject, notification.message);
        Ok(())
    }

    /// Send Slack notification
    async fn send_slack_notification(
        &self,
        _channel: &NotificationChannel,
        notification: &AlertNotification,
    ) -> Result<()> {
        // Slack notification implementation would go here
        tracing::info!("Slack notification: {} - {}", notification.subject, notification.message);
        Ok(())
    }

    /// Send webhook notification
    async fn send_webhook_notification(
        &self,
        channel: &NotificationChannel,
        notification: &AlertNotification,
    ) -> Result<()> {
        // Webhook notification implementation would go here
        if let Some(webhook_url) = channel.config.get("webhook_url") {
            tracing::info!("Webhook notification to {}: {} - {}", webhook_url, notification.subject, notification.message);
        }
        Ok(())
    }

    /// Record alert metrics
    async fn record_alert_metrics(
        &self,
        alert_type: &AlertType,
        severity: &AlertSeverity,
    ) -> Result<()> {
        let labels = vec![
            opentelemetry::KeyValue::new("alert_type", format!("{:?}", alert_type)),
            opentelemetry::KeyValue::new("severity", format!("{:?}", severity)),
        ];

        instrumentation::record_counter(
            &instrumentation::counter("deployment_alerts_total", "Total deployment alerts"),
            1,
            labels,
        );

        Ok(())
    }

    /// Start alert monitoring task
    fn start_alert_monitoring_task(&self) {
        let active_alerts = self.active_alerts.clone();
        let escalation_policy = self.config.escalation_policy.clone();

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(60)); // Check every minute
            loop {
                interval.tick().await;

                // Check for alerts that need escalation
                Self::check_alert_escalations(&active_alerts, &escalation_policy).await;
            }
        });
    }

    /// Check alert escalations
    async fn check_alert_escalations(
        active_alerts: &Arc<RwLock<HashMap<Uuid, DeploymentAlert>>>,
        _escalation_policy: &EscalationPolicy,
    ) {
        let now = Utc::now();
        let alerts = active_alerts.read().await;

        for (alert_id, alert) in alerts.iter() {
            let elapsed = (now - alert.created_at).num_minutes();

            // Check if alert needs escalation based on elapsed time and severity
            // This would implement the escalation logic based on the policy
            if elapsed > 5 && matches!(alert.severity, AlertSeverity::Critical) {
                tracing::warn!("Alert {} requires escalation after {} minutes", alert_id, elapsed);
                // Escalation logic would go here
            }
        }
    }

    /// Shutdown the alert manager
    pub async fn shutdown(&self) -> Result<()> {
        // Resolve all active alerts
        let active_alert_ids: Vec<_> = {
            let active_alerts = self.active_alerts.read().await;
            active_alerts.keys().cloned().collect()
        };

        for alert_id in active_alert_ids {
            self.resolve_alert(alert_id).await?;
        }

        Ok(())
    }
}

impl Default for EscalationPolicy {
    fn default() -> Self {
        Self {
            levels: vec![
                EscalationLevel {
                    level: 1,
                    delay_seconds: 300, // 5 minutes
                    channels: vec!["email".to_string()],
                    recipients: vec!["<EMAIL>".to_string()],
                },
                EscalationLevel {
                    level: 2,
                    delay_seconds: 900, // 15 minutes
                    channels: vec!["slack".to_string(), "sms".to_string()],
                    recipients: vec!["<EMAIL>".to_string()],
                },
            ],
            max_level: 2,
            escalation_delay_seconds: 300,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_alert_config_creation() {
        let thresholds = AlertThresholds::default();
        let manager = ObservabilityManager::new(crate::observability::ObservabilityConfig::default());
        let alert_manager = DeploymentAlertManager::new(thresholds, Arc::new(manager));

        assert!(alert_manager.config.enabled);
        assert_eq!(alert_manager.config.alert_cooldown_seconds, 300);
    }

    #[test]
    fn test_escalation_policy_default() {
        let policy = EscalationPolicy::default();
        assert_eq!(policy.levels.len(), 2);
        assert_eq!(policy.max_level, 2);
        assert_eq!(policy.escalation_delay_seconds, 300);
    }
}