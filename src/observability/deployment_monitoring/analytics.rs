//! # Deployment Analytics and Reporting
//!
//! Provides comprehensive analytics, reporting, and insights for deployment
//! monitoring, including trend analysis, forecasting, and performance metrics.

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, Duration};
use uuid::Uuid;

use crate::observability::{ObservabilityManager, instrumentation};
use crate::error::{InfinitumError, Result};
use super::{DeploymentRecord, DeploymentEnvironment, DeploymentStatus};

/// Analytics configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AnalyticsConfig {
    /// Enable analytics
    pub enabled: bool,
    /// Analytics retention period in days
    pub retention_days: u64,
    /// Enable trend analysis
    pub enable_trend_analysis: bool,
    /// Enable forecasting
    pub enable_forecasting: bool,
    /// Analytics update interval in seconds
    pub update_interval_seconds: u64,
}

/// Deployment analytics
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DeploymentAnalytics {
    /// Total deployments
    pub total_deployments: u64,
    /// Successful deployments
    pub successful_deployments: u64,
    /// Failed deployments
    pub failed_deployments: u64,
    /// Success rate (0.0-1.0)
    pub success_rate: f64,
    /// Average deployment duration in seconds
    pub avg_deployment_duration: f64,
    /// Median deployment duration in seconds
    pub median_deployment_duration: f64,
    /// P95 deployment duration in seconds
    pub p95_deployment_duration: f64,
    /// Average CPU utilization
    pub avg_cpu_utilization: f64,
    /// Average memory utilization
    pub avg_memory_utilization: f64,
    /// Deployment frequency (deployments per day)
    pub deployment_frequency: f64,
    /// Mean time between deployments (hours)
    pub mtbd_hours: f64,
    /// Change failure rate (0.0-1.0)
    pub change_failure_rate: f64,
    /// Mean time to recovery (hours)
    pub mttr_hours: f64,
    /// Analytics by environment
    pub by_environment: HashMap<String, EnvironmentAnalytics>,
    /// Trend analysis
    pub trends: TrendAnalysis,
    /// Time range for analytics
    pub time_range: AnalyticsTimeRange,
}

/// Environment-specific analytics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnvironmentAnalytics {
    /// Environment name
    pub environment: String,
    /// Total deployments in environment
    pub total_deployments: u64,
    /// Success rate for environment
    pub success_rate: f64,
    /// Average duration for environment
    pub avg_duration: f64,
    /// Deployment frequency for environment
    pub deployment_frequency: f64,
}

/// Trend analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrendAnalysis {
    /// Success rate trend (percentage points per day)
    pub success_rate_trend: f64,
    /// Duration trend (seconds per day)
    pub duration_trend: f64,
    /// Frequency trend (deployments per day change)
    pub frequency_trend: f64,
    /// Quality score trend
    pub quality_trend: f64,
}

/// Analytics time range
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalyticsTimeRange {
    /// Start time
    pub start_time: DateTime<Utc>,
    /// End time
    pub end_time: DateTime<Utc>,
    /// Duration in hours
    pub duration_hours: u64,
}

/// Deployment forecast
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentForecast {
    /// Forecasted deployments for next period
    pub forecasted_deployments: u64,
    /// Forecasted success rate
    pub forecasted_success_rate: f64,
    /// Forecast confidence (0.0-1.0)
    pub confidence: f64,
    /// Forecast period in hours
    pub forecast_period_hours: u64,
}

/// Quality metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityMetrics {
    /// Deployment quality score (0.0-1.0)
    pub quality_score: f64,
    /// Reliability score
    pub reliability_score: f64,
    /// Performance score
    pub performance_score: f64,
    /// Compliance score
    pub compliance_score: f64,
    /// Risk assessment
    pub risk_assessment: RiskLevel,
}

/// Risk levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum RiskLevel {
    /// Low risk
    Low,
    /// Medium risk
    Medium,
    /// High risk
    High,
    /// Critical risk
    Critical,
}

/// Deployment analytics engine
pub struct DeploymentAnalyticsEngine {
    config: AnalyticsConfig,
    observability_manager: Arc<ObservabilityManager>,
    deployment_history: Arc<RwLock<Vec<DeploymentRecord>>>,
    analytics_cache: Arc<RwLock<HashMap<String, DeploymentAnalytics>>>,
    quality_metrics: Arc<RwLock<HashMap<String, QualityMetrics>>>,
}

impl DeploymentAnalyticsEngine {
    /// Create new analytics engine
    pub fn new(observability_manager: Arc<ObservabilityManager>) -> Self {
        let config = AnalyticsConfig::default();

        Self {
            config,
            observability_manager,
            deployment_history: Arc::new(RwLock::new(Vec::new())),
            analytics_cache: Arc::new(RwLock::new(HashMap::new())),
            quality_metrics: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Initialize the analytics engine
    pub async fn initialize(&self) -> Result<()> {
        // Start analytics update task
        if self.config.enabled {
            self.start_analytics_update_task();
        }

        Ok(())
    }

    /// Update deployment analytics
    pub async fn update_deployment_analytics(
        &self,
        deployment_id: Uuid,
        status: &DeploymentStatus,
        metrics: &super::DeploymentMetrics,
    ) -> Result<()> {
        // This would be called when new deployment data is available
        // For now, we'll update the cache key to trigger recalculation
        let cache_key = "global".to_string();
        {
            let mut cache = self.analytics_cache.write().await;
            cache.remove(&cache_key); // Force recalculation on next request
        }

        // Record analytics metrics
        self.record_analytics_metrics(deployment_id, status, metrics).await?;

        Ok(())
    }

    /// Get deployment analytics
    pub async fn get_analytics(&self, time_range_hours: Option<u64>) -> Result<DeploymentAnalytics> {
        let hours = time_range_hours.unwrap_or(168); // Default to 7 days
        let cache_key = format!("global_{}", hours);

        // Check cache first
        {
            let cache = self.analytics_cache.read().await;
            if let Some(analytics) = cache.get(&cache_key) {
                return Ok(analytics.clone());
            }
        }

        // Calculate analytics
        let analytics = self.calculate_analytics(hours).await?;

        // Cache the result
        {
            let mut cache = self.analytics_cache.write().await;
            cache.insert(cache_key, analytics.clone());
        }

        Ok(analytics)
    }

    /// Get environment-specific analytics
    pub async fn get_environment_analytics(
        &self,
        environment: DeploymentEnvironment,
        time_range_hours: Option<u64>,
    ) -> Result<EnvironmentAnalytics> {
        let all_analytics = self.get_analytics(time_range_hours).await?;
        let env_key = format!("{:?}", environment).to_lowercase();

        if let Some(env_analytics) = all_analytics.by_environment.get(&env_key) {
            Ok(env_analytics.clone())
        } else {
            Ok(EnvironmentAnalytics {
                environment: env_key,
                total_deployments: 0,
                success_rate: 0.0,
                avg_duration: 0.0,
                deployment_frequency: 0.0,
            })
        }
    }

    /// Generate deployment forecast
    pub async fn generate_forecast(&self, forecast_hours: u64) -> Result<DeploymentForecast> {
        let analytics = self.get_analytics(Some(168)).await?; // Use last 7 days for forecasting

        // Simple linear forecasting based on recent trends
        let daily_deployments = analytics.total_deployments as f64 / 7.0;
        let forecasted_deployments = (daily_deployments * (forecast_hours as f64 / 24.0)) as u64;

        // Forecast success rate with trend adjustment
        let success_rate_trend = analytics.trends.success_rate_trend;
        let forecasted_success_rate = (analytics.success_rate + (success_rate_trend * (forecast_hours as f64 / 24.0)))
            .max(0.0)
            .min(1.0);

        Ok(DeploymentForecast {
            forecasted_deployments,
            forecasted_success_rate,
            confidence: 0.7, // Placeholder confidence
            forecast_period_hours: forecast_hours,
        })
    }

    /// Get quality metrics
    pub async fn get_quality_metrics(&self, time_range_hours: Option<u64>) -> Result<QualityMetrics> {
        let hours = time_range_hours.unwrap_or(168);
        let cache_key = format!("quality_{}", hours);

        // Check cache first
        {
            let quality = self.quality_metrics.read().await;
            if let Some(metrics) = quality.get(&cache_key) {
                return Ok(metrics.clone());
            }
        }

        // Calculate quality metrics
        let quality = self.calculate_quality_metrics(hours).await?;

        // Cache the result
        {
            let mut quality_cache = self.quality_metrics.write().await;
            quality_cache.insert(cache_key, quality.clone());
        }

        Ok(quality)
    }

    /// Calculate deployment analytics
    async fn calculate_analytics(&self, time_range_hours: u64) -> Result<DeploymentAnalytics> {
        let history = self.deployment_history.read().await;
        let now = Utc::now();
        let cutoff = now - Duration::hours(time_range_hours as i64);

        // Filter deployments within time range
        let relevant_deployments: Vec<_> = history.iter()
            .filter(|record| {
                record.completed_at.unwrap_or(record.metadata.initiated_at) >= cutoff
            })
            .collect();

        if relevant_deployments.is_empty() {
            return Ok(DeploymentAnalytics::default());
        }

        // Calculate basic metrics
        let total_deployments = relevant_deployments.len() as u64;
        let successful_deployments = relevant_deployments.iter()
            .filter(|record| matches!(record.status, DeploymentStatus::Completed))
            .count() as u64;
        let failed_deployments = total_deployments - successful_deployments;
        let success_rate = successful_deployments as f64 / total_deployments as f64;

        // Calculate duration statistics
        let durations: Vec<_> = relevant_deployments.iter()
            .filter_map(|record| record.metrics.total_duration_seconds)
            .collect();

        let avg_deployment_duration = if !durations.is_empty() {
            durations.iter().sum::<u64>() as f64 / durations.len() as f64
        } else {
            0.0
        };

        let median_deployment_duration = if !durations.is_empty() {
            let mut sorted = durations.clone();
            sorted.sort();
            let mid = sorted.len() / 2;
            if sorted.len() % 2 == 0 {
                (sorted[mid - 1] + sorted[mid]) as f64 / 2.0
            } else {
                sorted[mid] as f64
            }
        } else {
            0.0
        };

        let p95_deployment_duration = if !durations.is_empty() {
            let mut sorted = durations.clone();
            sorted.sort();
            let p95_index = (sorted.len() as f64 * 0.95) as usize;
            sorted.get(p95_index).copied().unwrap_or(0) as f64
        } else {
            0.0
        };

        // Calculate resource utilization averages
        let avg_cpu_utilization = relevant_deployments.iter()
            .map(|record| record.metrics.cpu_utilization_percent)
            .sum::<f64>() / total_deployments as f64;

        let avg_memory_utilization = relevant_deployments.iter()
            .map(|record| record.metrics.memory_utilization_percent)
            .sum::<f64>() / total_deployments as f64;

        // Calculate deployment frequency
        let time_span_hours = time_range_hours as f64;
        let deployment_frequency = total_deployments as f64 / (time_span_hours / 24.0);

        // Calculate MTBD (simplified)
        let mtbd_hours = if successful_deployments > 1 {
            time_span_hours / successful_deployments as f64
        } else {
            time_span_hours
        };

        // Calculate change failure rate
        let change_failure_rate = failed_deployments as f64 / total_deployments as f64;

        // Calculate MTTR (simplified - time from failure to recovery)
        let mttr_hours = if failed_deployments > 0 {
            // This would need more sophisticated calculation based on actual recovery times
            time_span_hours / failed_deployments as f64
        } else {
            0.0
        };

        // Calculate environment-specific analytics
        let mut by_environment = HashMap::new();
        let environments = vec!["production", "staging", "development", "testing"];

        for env_name in environments {
            let env_deployments: Vec<_> = relevant_deployments.iter()
                .filter(|record| {
                    format!("{:?}", record.metadata.environment).to_lowercase() == env_name
                })
                .collect();

            if !env_deployments.is_empty() {
                let env_total = env_deployments.len() as u64;
                let env_successful = env_deployments.iter()
                    .filter(|record| matches!(record.status, DeploymentStatus::Completed))
                    .count() as u64;
                let env_success_rate = env_successful as f64 / env_total as f64;
                let env_avg_duration = env_deployments.iter()
                    .filter_map(|record| record.metrics.total_duration_seconds)
                    .sum::<u64>() as f64 / env_total as f64;
                let env_frequency = env_total as f64 / (time_span_hours / 24.0);

                by_environment.insert(env_name.to_string(), EnvironmentAnalytics {
                    environment: env_name.to_string(),
                    total_deployments: env_total,
                    success_rate: env_success_rate,
                    avg_duration: env_avg_duration,
                    deployment_frequency: env_frequency,
                });
            }
        }

        // Calculate trends (simplified)
        let trends = self.calculate_trends(&relevant_deployments, time_range_hours).await?;

        Ok(DeploymentAnalytics {
            total_deployments,
            successful_deployments,
            failed_deployments,
            success_rate,
            avg_deployment_duration,
            median_deployment_duration,
            p95_deployment_duration,
            avg_cpu_utilization,
            avg_memory_utilization,
            deployment_frequency,
            mtbd_hours,
            change_failure_rate,
            mttr_hours,
            by_environment,
            trends,
            time_range: AnalyticsTimeRange {
                start_time: cutoff,
                end_time: now,
                duration_hours: time_range_hours,
            },
        })
    }

    /// Calculate quality metrics
    async fn calculate_quality_metrics(&self, time_range_hours: u64) -> Result<QualityMetrics> {
        let analytics = self.get_analytics(Some(time_range_hours)).await?;

        // Calculate quality score based on multiple factors
        let reliability_score = analytics.success_rate;
        let performance_score = 1.0 - (analytics.avg_deployment_duration / 3600.0).min(1.0); // Better if faster
        let compliance_score = 1.0 - analytics.change_failure_rate; // Better if fewer failures

        // Overall quality score (weighted average)
        let quality_score = (reliability_score * 0.4) + (performance_score * 0.3) + (compliance_score * 0.3);

        // Risk assessment based on quality score
        let risk_assessment = if quality_score >= 0.9 {
            RiskLevel::Low
        } else if quality_score >= 0.7 {
            RiskLevel::Medium
        } else if quality_score >= 0.5 {
            RiskLevel::High
        } else {
            RiskLevel::Critical
        };

        Ok(QualityMetrics {
            quality_score,
            reliability_score,
            performance_score,
            compliance_score,
            risk_assessment,
        })
    }

    /// Calculate trends
    async fn calculate_trends(
        &self,
        deployments: &[&DeploymentRecord],
        time_range_hours: u64,
    ) -> Result<TrendAnalysis> {
        // Simplified trend calculation
        // In a real implementation, this would use more sophisticated statistical methods

        let half_time = time_range_hours / 2;
        let midpoint = Utc::now() - Duration::hours(half_time as i64);

        let first_half: Vec<_> = deployments.iter()
            .filter(|record| record.completed_at.unwrap_or(record.metadata.initiated_at) < midpoint)
            .collect();

        let second_half: Vec<_> = deployments.iter()
            .filter(|record| record.completed_at.unwrap_or(record.metadata.initiated_at) >= midpoint)
            .collect();

        // Calculate success rate trends
        let first_half_success_rate = if !first_half.is_empty() {
            let successful = first_half.iter()
                .filter(|record| matches!(record.status, DeploymentStatus::Completed))
                .count();
            successful as f64 / first_half.len() as f64
        } else {
            0.0
        };

        let second_half_success_rate = if !second_half.is_empty() {
            let successful = second_half.iter()
                .filter(|record| matches!(record.status, DeploymentStatus::Completed))
                .count();
            successful as f64 / second_half.len() as f64
        } else {
            0.0
        };

        let success_rate_trend = second_half_success_rate - first_half_success_rate;

        // Calculate duration trends
        let first_half_avg_duration = if !first_half.is_empty() {
            let total_duration: u64 = first_half.iter()
                .filter_map(|record| record.metrics.total_duration_seconds)
                .sum();
            total_duration as f64 / first_half.len() as f64
        } else {
            0.0
        };

        let second_half_avg_duration = if !second_half.is_empty() {
            let total_duration: u64 = second_half.iter()
                .filter_map(|record| record.metrics.total_duration_seconds)
                .sum();
            total_duration as f64 / second_half.len() as f64
        } else {
            0.0
        };

        let duration_trend = second_half_avg_duration - first_half_avg_duration;

        // Calculate frequency trends
        let first_half_frequency = first_half.len() as f64 / (half_time as f64 / 24.0);
        let second_half_frequency = second_half.len() as f64 / (half_time as f64 / 24.0);
        let frequency_trend = second_half_frequency - first_half_frequency;

        // Calculate quality trend
        let quality_trend = (success_rate_trend * 0.5) + ((duration_trend / 100.0) * -0.3) + (frequency_trend * 0.2);

        Ok(TrendAnalysis {
            success_rate_trend,
            duration_trend,
            frequency_trend,
            quality_trend,
        })
    }

    /// Record analytics metrics
    async fn record_analytics_metrics(
        &self,
        deployment_id: Uuid,
        status: &DeploymentStatus,
        metrics: &super::DeploymentMetrics,
    ) -> Result<()> {
        let labels = vec![
            opentelemetry::KeyValue::new("deployment_id", deployment_id.to_string()),
            opentelemetry::KeyValue::new("status", format!("{:?}", status)),
        ];

        instrumentation::record_histogram(
            &instrumentation::histogram("deployment_analytics_duration", "Deployment duration for analytics"),
            metrics.total_duration_seconds as f64,
            labels.clone(),
        );

        instrumentation::record_histogram(
            &instrumentation::histogram("deployment_analytics_cpu", "Deployment CPU utilization for analytics"),
            metrics.cpu_utilization_percent,
            labels.clone(),
        );

        instrumentation::record_histogram(
            &instrumentation::histogram("deployment_analytics_memory", "Deployment memory utilization for analytics"),
            metrics.memory_utilization_percent,
            labels,
        );

        Ok(())
    }

    /// Start analytics update task
    fn start_analytics_update_task(&self) {
        let analytics_cache = self.analytics_cache.clone();
        let quality_metrics = self.quality_metrics.clone();
        let update_interval = self.config.update_interval_seconds;

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(update_interval));
            loop {
                interval.tick().await;

                // Clear caches to force recalculation on next request
                let mut cache = analytics_cache.write().await;
                cache.clear();

                let mut quality = quality_metrics.write().await;
                quality.clear();

                tracing::debug!("Cleared analytics caches for refresh");
            }
        });
    }

    /// Shutdown the analytics engine
    pub async fn shutdown(&self) -> Result<()> {
        // Clear caches
        {
            let mut cache = self.analytics_cache.write().await;
            cache.clear();
        }

        {
            let mut quality = self.quality_metrics.write().await;
            quality.clear();
        }

        Ok(())
    }
}

impl Default for DeploymentAnalytics {
    fn default() -> Self {
        Self {
            total_deployments: 0,
            successful_deployments: 0,
            failed_deployments: 0,
            success_rate: 0.0,
            avg_deployment_duration: 0.0,
            median_deployment_duration: 0.0,
            p95_deployment_duration: 0.0,
            avg_cpu_utilization: 0.0,
            avg_memory_utilization: 0.0,
            deployment_frequency: 0.0,
            mtbd_hours: 0.0,
            change_failure_rate: 0.0,
            mttr_hours: 0.0,
            by_environment: HashMap::new(),
            trends: TrendAnalysis::default(),
            time_range: AnalyticsTimeRange {
                start_time: Utc::now(),
                end_time: Utc::now(),
                duration_hours: 0,
            },
        }
    }
}

impl Default for TrendAnalysis {
    fn default() -> Self {
        Self {
            success_rate_trend: 0.0,
            duration_trend: 0.0,
            frequency_trend: 0.0,
            quality_trend: 0.0,
        }
    }
}

impl Default for AnalyticsConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            retention_days: 90,
            enable_trend_analysis: true,
            enable_forecasting: true,
            update_interval_seconds: 300, // 5 minutes
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_analytics_config_default() {
        let config = AnalyticsConfig::default();
        assert!(config.enabled);
        assert_eq!(config.retention_days, 90);
        assert!(config.enable_trend_analysis);
        assert!(config.enable_forecasting);
        assert_eq!(config.update_interval_seconds, 300);
    }

    #[test]
    fn test_deployment_analytics_default() {
        let analytics = DeploymentAnalytics::default();
        assert_eq!(analytics.total_deployments, 0);
        assert_eq!(analytics.successful_deployments, 0);
        assert_eq!(analytics.failed_deployments, 0);
        assert_eq!(analytics.success_rate, 0.0);
    }

    #[test]
    fn test_trend_analysis_default() {
        let trends = TrendAnalysis::default();
        assert_eq!(trends.success_rate_trend, 0.0);
        assert_eq!(trends.duration_trend, 0.0);
        assert_eq!(trends.frequency_trend, 0.0);
        assert_eq!(trends.quality_trend, 0.0);
    }
}