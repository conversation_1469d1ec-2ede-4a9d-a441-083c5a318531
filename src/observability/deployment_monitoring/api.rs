//! # Deployment Monitoring API
//!
//! REST API endpoints for deployment monitoring, providing programmatic
//! access to deployment data, analytics, and control operations.

use std::sync::Arc;
use serde::{Deserialize, Serialize};
use warp::Filter;
use tokio::sync::RwLock;

use crate::error::{InfinitumError, Result};
use super::{DeploymentMonitor, DeploymentEnvironment, DeploymentStatus};

/// API configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentApiConfig {
    /// API host
    pub host: String,
    /// API port
    pub port: u16,
    /// Enable CORS
    pub enable_cors: bool,
    /// API key for authentication
    pub api_key: Option<String>,
    /// Rate limiting
    pub rate_limit: RateLimitConfig,
}

/// Rate limiting configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    /// Requests per minute
    pub requests_per_minute: u32,
    /// Burst size
    pub burst_size: u32,
}

/// API response wrapper
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    /// Success flag
    pub success: bool,
    /// Response data
    pub data: Option<T>,
    /// Error message
    pub error: Option<String>,
    /// Timestamp
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Deployment summary for API
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentSummary {
    /// Deployment ID
    pub deployment_id: String,
    /// Version
    pub version: String,
    /// Environment
    pub environment: String,
    /// Status
    pub status: String,
    /// Start time
    pub started_at: chrono::DateTime<chrono::Utc>,
    /// Duration in seconds
    pub duration_seconds: Option<u64>,
    /// Success flag
    pub success: bool,
}

/// Deployment details for API
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentDetails {
    /// Deployment summary
    pub summary: DeploymentSummary,
    /// Build information
    pub build_info: Option<BuildInfo>,
    /// Metrics
    pub metrics: Option<super::DeploymentMetrics>,
    /// Stages
    pub stages: Vec<DeploymentStage>,
    /// Artifacts
    pub artifacts: Vec<String>,
    /// Logs
    pub logs: Vec<String>,
}

/// Build information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildInfo {
    /// Build number
    pub build_number: String,
    /// Build URL
    pub build_url: Option<String>,
    /// Commit hash
    pub commit_hash: Option<String>,
    /// Branch
    pub branch: Option<String>,
}

/// Deployment stage for API
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentStage {
    /// Stage name
    pub name: String,
    /// Status
    pub status: String,
    /// Start time
    pub started_at: chrono::DateTime<chrono::Utc>,
    /// Duration in seconds
    pub duration_seconds: Option<u64>,
    /// Logs
    pub logs: Vec<String>,
}

/// Analytics response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalyticsResponse {
    /// Analytics data
    pub analytics: super::analytics::DeploymentAnalytics,
    /// Quality metrics
    pub quality: super::analytics::QualityMetrics,
    /// Forecast
    pub forecast: Option<super::analytics::DeploymentForecast>,
}

/// Alert response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertResponse {
    /// Active alerts
    pub active_alerts: Vec<super::alerting::DeploymentAlert>,
    /// Recent alerts
    pub recent_alerts: Vec<super::alerting::DeploymentAlert>,
}

/// Rollback request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RollbackRequest {
    /// Deployment ID to rollback
    pub deployment_id: String,
    /// Reason for rollback
    pub reason: String,
    /// Rollback strategy
    pub strategy: String,
}

/// Control request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ControlRequest {
    /// Action to perform
    pub action: String,
    /// Target deployment ID
    pub deployment_id: Option<String>,
    /// Parameters
    pub parameters: std::collections::HashMap<String, serde_json::Value>,
}

/// Deployment API server
pub struct DeploymentApiServer {
    config: DeploymentApiConfig,
    deployment_monitor: Arc<DeploymentMonitor>,
    server_handle: Option<tokio::task::JoinHandle<()>>,
}

impl DeploymentApiServer {
    /// Create new API server
    pub fn new(
        config: DeploymentApiConfig,
        deployment_monitor: Arc<DeploymentMonitor>,
    ) -> Self {
        Self {
            config,
            deployment_monitor,
            server_handle: None,
        }
    }

    /// Start the API server
    pub async fn start(&mut self) -> Result<()> {
        let routes = self.build_routes();
        let addr = format!("{}:{}", self.config.host, self.config.port);

        let server = warp::serve(routes)
            .bind(([0, 0, 0, 0], self.config.port));

        self.server_handle = Some(tokio::spawn(async move {
            server.await;
        }));

        tracing::info!("Deployment API server started on {}", addr);
        Ok(())
    }

    /// Stop the API server
    pub async fn stop(&mut self) -> Result<()> {
        if let Some(handle) = self.server_handle.take() {
            handle.abort();
            tracing::info!("Deployment API server stopped");
        }
        Ok(())
    }

    /// Build API routes
    fn build_routes(&self) -> impl warp::Filter<Extract = impl warp::Reply, Error = warp::Rejection> + Clone {
        let deployment_monitor = self.deployment_monitor.clone();

        // Health check endpoint
        let health = warp::path("health")
            .and(warp::get())
            .map(|| {
                let response = ApiResponse {
                    success: true,
                    data: Some(serde_json::json!({"status": "healthy"})),
                    error: None,
                    timestamp: chrono::Utc::now(),
                };
                warp::reply::json(&response)
            });

        // Get deployment status
        let get_deployment = warp::path!("deployments" / String)
            .and(warp::get())
            .and(with_monitor(deployment_monitor.clone()))
            .and_then(get_deployment_handler);

        // List deployments
        let list_deployments = warp::path("deployments")
            .and(warp::get())
            .and(warp::query::<std::collections::HashMap<String, String>>())
            .and(with_monitor(deployment_monitor.clone()))
            .and_then(list_deployments_handler);

        // Get deployment metrics
        let get_metrics = warp::path!("deployments" / String / "metrics")
            .and(warp::get())
            .and(with_monitor(deployment_monitor.clone()))
            .and_then(get_deployment_metrics_handler);

        // Get analytics
        let get_analytics = warp::path("analytics")
            .and(warp::get())
            .and(warp::query::<std::collections::HashMap<String, String>>())
            .and(with_monitor(deployment_monitor.clone()))
            .and_then(get_analytics_handler);

        // Get alerts
        let get_alerts = warp::path("alerts")
            .and(warp::get())
            .and(with_monitor(deployment_monitor.clone()))
            .and_then(get_alerts_handler);

        // Initiate rollback
        let rollback = warp::path("rollback")
            .and(warp::post())
            .and(warp::body::json())
            .and(with_monitor(deployment_monitor.clone()))
            .and_then(rollback_handler);

        // Control operations
        let control = warp::path("control")
            .and(warp::post())
            .and(warp::body::json())
            .and(with_monitor(deployment_monitor.clone()))
            .and_then(control_handler);

        // Combine all routes
        let api = warp::path("api")
            .and(warp::path("v1"))
            .and(
                health
                    .or(get_deployment)
                    .or(list_deployments)
                    .or(get_metrics)
                    .or(get_analytics)
                    .or(get_alerts)
                    .or(rollback)
                    .or(control)
            );

        // Add CORS if enabled
        if self.config.enable_cors {
            api.with(warp::cors().allow_any_origin())
        } else {
            api
        }
    }
}

/// Helper function to pass monitor to handlers
fn with_monitor(
    monitor: Arc<DeploymentMonitor>,
) -> impl Filter<Extract = (Arc<DeploymentMonitor>,), Error = std::convert::Infallible> + Clone {
    warp::any().map(move || monitor.clone())
}

/// Get deployment handler
async fn get_deployment_handler(
    deployment_id: String,
    monitor: Arc<DeploymentMonitor>,
) -> std::result::Result<impl warp::Reply, warp::Rejection> {
    let uuid = match uuid::Uuid::parse_str(&deployment_id) {
        Ok(uuid) => uuid,
        Err(_) => {
            return Ok(warp::reply::json(&ApiResponse::<()> {
                success: false,
                data: None,
                error: Some("Invalid deployment ID format".to_string()),
                timestamp: chrono::Utc::now(),
            }));
        }
    };

    match monitor.get_deployment_status(uuid).await {
        Ok(Some(status)) => {
            let summary = DeploymentSummary {
                deployment_id: deployment_id.clone(),
                version: "unknown".to_string(), // Would need to be populated from metadata
                environment: "unknown".to_string(),
                status: format!("{:?}", status),
                started_at: chrono::Utc::now(), // Would need actual start time
                duration_seconds: None,
                success: matches!(status, DeploymentStatus::Completed),
            };

            let response = ApiResponse {
                success: true,
                data: Some(summary),
                error: None,
                timestamp: chrono::Utc::now(),
            };

            Ok(warp::reply::json(&response))
        }
        Ok(None) => {
            let response = ApiResponse::<()> {
                success: false,
                data: None,
                error: Some("Deployment not found".to_string()),
                timestamp: chrono::Utc::now(),
            };

            Ok(warp::reply::json(&response))
        }
        Err(e) => {
            let response = ApiResponse::<()> {
                success: false,
                data: None,
                error: Some(format!("Error retrieving deployment: {}", e)),
                timestamp: chrono::Utc::now(),
            };

            Ok(warp::reply::json(&response))
        }
    }
}

/// List deployments handler
async fn list_deployments_handler(
    query: std::collections::HashMap<String, String>,
    monitor: Arc<DeploymentMonitor>,
) -> std::result::Result<impl warp::Reply, warp::Rejection> {
    let environment = query.get("environment")
        .and_then(|e| match e.to_lowercase().as_str() {
            "production" => Some(DeploymentEnvironment::Production),
            "staging" => Some(DeploymentEnvironment::Staging),
            "development" => Some(DeploymentEnvironment::Development),
            "testing" => Some(DeploymentEnvironment::Testing),
            _ => None,
        });

    let limit = query.get("limit")
        .and_then(|l| l.parse::<usize>().ok())
        .unwrap_or(50);

    match monitor.get_deployment_history(environment, Some(limit)).await {
        Ok(history) => {
            let summaries: Vec<DeploymentSummary> = history.into_iter()
                .map(|record| DeploymentSummary {
                    deployment_id: record.metadata.deployment_id.to_string(),
                    version: record.metadata.version,
                    environment: format!("{:?}", record.metadata.environment),
                    status: format!("{:?}", record.status),
                    started_at: record.metadata.initiated_at,
                    duration_seconds: record.metrics.total_duration_seconds,
                    success: matches!(record.status, DeploymentStatus::Completed),
                })
                .collect();

            let response = ApiResponse {
                success: true,
                data: Some(summaries),
                error: None,
                timestamp: chrono::Utc::now(),
            };

            Ok(warp::reply::json(&response))
        }
        Err(e) => {
            let response = ApiResponse::<()> {
                success: false,
                data: None,
                error: Some(format!("Error retrieving deployments: {}", e)),
                timestamp: chrono::Utc::now(),
            };

            Ok(warp::reply::json(&response))
        }
    }
}

/// Get deployment metrics handler
async fn get_deployment_metrics_handler(
    deployment_id: String,
    monitor: Arc<DeploymentMonitor>,
) -> std::result::Result<impl warp::Reply, warp::Rejection> {
    let uuid = match uuid::Uuid::parse_str(&deployment_id) {
        Ok(uuid) => uuid,
        Err(_) => {
            return Ok(warp::reply::json(&ApiResponse::<()> {
                success: false,
                data: None,
                error: Some("Invalid deployment ID format".to_string()),
                timestamp: chrono::Utc::now(),
            }));
        }
    };

    match monitor.get_deployment_metrics(uuid).await {
        Ok(Some(metrics)) => {
            let response = ApiResponse {
                success: true,
                data: Some(metrics),
                error: None,
                timestamp: chrono::Utc::now(),
            };

            Ok(warp::reply::json(&response))
        }
        Ok(None) => {
            let response = ApiResponse::<()> {
                success: false,
                data: None,
                error: Some("Deployment metrics not found".to_string()),
                timestamp: chrono::Utc::now(),
            };

            Ok(warp::reply::json(&response))
        }
        Err(e) => {
            let response = ApiResponse::<()> {
                success: false,
                data: None,
                error: Some(format!("Error retrieving metrics: {}", e)),
                timestamp: chrono::Utc::now(),
            };

            Ok(warp::reply::json(&response))
        }
    }
}

/// Get analytics handler
async fn get_analytics_handler(
    query: std::collections::HashMap<String, String>,
    monitor: Arc<DeploymentMonitor>,
) -> std::result::Result<impl warp::Reply, warp::Rejection> {
    let time_range_hours = query.get("hours")
        .and_then(|h| h.parse::<u64>().ok());

    match monitor.get_deployment_analytics(time_range_hours).await {
        Ok(analytics) => {
            let response = ApiResponse {
                success: true,
                data: Some(analytics),
                error: None,
                timestamp: chrono::Utc::now(),
            };

            Ok(warp::reply::json(&response))
        }
        Err(e) => {
            let response = ApiResponse::<()> {
                success: false,
                data: None,
                error: Some(format!("Error retrieving analytics: {}", e)),
                timestamp: chrono::Utc::now(),
            };

            Ok(warp::reply::json(&response))
        }
    }
}

/// Get alerts handler
async fn get_alerts_handler(
    monitor: Arc<DeploymentMonitor>,
) -> std::result::Result<impl warp::Reply, warp::Rejection> {
    // This would need to be implemented to access alert manager
    // For now, return empty response
    let response = ApiResponse {
        success: true,
        data: Some(AlertResponse {
            active_alerts: vec![],
            recent_alerts: vec![],
        }),
        error: None,
        timestamp: chrono::Utc::now(),
    };

    Ok(warp::reply::json(&response))
}

/// Rollback handler
async fn rollback_handler(
    request: RollbackRequest,
    monitor: Arc<DeploymentMonitor>,
) -> std::result::Result<impl warp::Reply, warp::Rejection> {
    let deployment_uuid = match uuid::Uuid::parse_str(&request.deployment_id) {
        Ok(uuid) => uuid,
        Err(_) => {
            return Ok(warp::reply::json(&ApiResponse::<()> {
                success: false,
                data: None,
                error: Some("Invalid deployment ID format".to_string()),
                timestamp: chrono::Utc::now(),
            }));
        }
    };

    let strategy = match request.strategy.to_lowercase().as_str() {
        "immediate" => super::RollbackStrategy::Immediate,
        "gradual" => super::RollbackStrategy::Gradual,
        "blue_green" => super::RollbackStrategy::BlueGreen,
        _ => super::RollbackStrategy::Immediate,
    };

    match monitor.initiate_rollback(deployment_uuid, request.reason, strategy).await {
        Ok(rollback_id) => {
            let response = ApiResponse {
                success: true,
                data: Some(serde_json::json!({
                    "rollback_id": rollback_id.to_string(),
                    "message": "Rollback initiated successfully"
                })),
                error: None,
                timestamp: chrono::Utc::now(),
            };

            Ok(warp::reply::json(&response))
        }
        Err(e) => {
            let response = ApiResponse::<()> {
                success: false,
                data: None,
                error: Some(format!("Error initiating rollback: {}", e)),
                timestamp: chrono::Utc::now(),
            };

            Ok(warp::reply::json(&response))
        }
    }
}

/// Control handler
async fn control_handler(
    request: ControlRequest,
    monitor: Arc<DeploymentMonitor>,
) -> std::result::Result<impl warp::Reply, warp::Rejection> {
    // Handle control operations
    match request.action.to_lowercase().as_str() {
        "pause" => {
            // Implementation for pausing deployments
            let response = ApiResponse {
                success: true,
                data: Some(serde_json::json!({"message": "Deployment control operation not yet implemented"})),
                error: None,
                timestamp: chrono::Utc::now(),
            };
            Ok(warp::reply::json(&response))
        }
        "resume" => {
            // Implementation for resuming deployments
            let response = ApiResponse {
                success: true,
                data: Some(serde_json::json!({"message": "Deployment control operation not yet implemented"})),
                error: None,
                timestamp: chrono::Utc::now(),
            };
            Ok(warp::reply::json(&response))
        }
        _ => {
            let response = ApiResponse::<()> {
                success: false,
                data: None,
                error: Some(format!("Unknown control action: {}", request.action)),
                timestamp: chrono::Utc::now(),
            };
            Ok(warp::reply::json(&response))
        }
    }
}

impl Default for DeploymentApiConfig {
    fn default() -> Self {
        Self {
            host: "0.0.0.0".to_string(),
            port: 8080,
            enable_cors: true,
            api_key: None,
            rate_limit: RateLimitConfig::default(),
        }
    }
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        Self {
            requests_per_minute: 100,
            burst_size: 20,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_api_config_default() {
        let config = DeploymentApiConfig::default();
        assert_eq!(config.host, "0.0.0.0");
        assert_eq!(config.port, 8080);
        assert!(config.enable_cors);
    }

    #[test]
    fn test_rate_limit_config_default() {
        let config = RateLimitConfig::default();
        assert_eq!(config.requests_per_minute, 100);
        assert_eq!(config.burst_size, 20);
    }
}