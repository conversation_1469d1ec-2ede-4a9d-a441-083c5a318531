//! # Deployment Event Processing
//!
//! Handles deployment event collection, processing, and streaming.
//! Provides real-time event tracking and correlation capabilities.

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;

use crate::observability::{ObservabilityManager, instrumentation};
use crate::error::{InfinitumError, Result};
use super::{DeploymentEventType, DeploymentMetadata};

/// Deployment event structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentEvent {
    /// Event ID
    pub event_id: Uuid,
    /// Deployment ID
    pub deployment_id: Uuid,
    /// Event type
    pub event_type: DeploymentEventType,
    /// Event timestamp
    pub timestamp: DateTime<Utc>,
    /// Event message
    pub message: Option<String>,
    /// Event data
    pub data: Option<serde_json::Value>,
    /// Event severity
    pub severity: EventSeverity,
    /// Source component
    pub source: String,
    /// Correlation ID for tracking related events
    pub correlation_id: Option<Uuid>,
}

/// Event severity levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum EventSeverity {
    /// Info level
    Info,
    /// Warning level
    Warning,
    /// Error level
    Error,
    /// Critical level
    Critical,
}

/// Event processing configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EventProcessingConfig {
    /// Enable event buffering
    pub enable_buffering: bool,
    /// Maximum buffer size
    pub max_buffer_size: usize,
    /// Flush interval in seconds
    pub flush_interval_seconds: u64,
    /// Enable event correlation
    pub enable_correlation: bool,
    /// Maximum correlation window in seconds
    pub max_correlation_window_seconds: u64,
}

/// Event correlation rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CorrelationRule {
    /// Rule name
    pub name: String,
    /// Event types to correlate
    pub event_types: Vec<DeploymentEventType>,
    /// Time window for correlation
    pub time_window_seconds: u64,
    /// Minimum events required
    pub min_events: usize,
    /// Correlation logic
    pub logic: CorrelationLogic,
}

/// Correlation logic types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CorrelationLogic {
    /// All events must occur
    All,
    /// Any event can trigger
    Any,
    /// Sequence of events
    Sequence,
    /// Custom logic
    Custom(String),
}

/// Deployment event processor
pub struct DeploymentEventProcessor {
    config: EventProcessingConfig,
    observability_manager: Arc<ObservabilityManager>,
    event_buffer: Arc<RwLock<Vec<DeploymentEvent>>>,
    correlation_rules: Vec<CorrelationRule>,
    event_stream: Option<tokio::sync::broadcast::Sender<DeploymentEvent>>,
}

impl DeploymentEventProcessor {
    /// Create new event processor
    pub fn new(observability_manager: Arc<ObservabilityManager>) -> Self {
        let config = EventProcessingConfig::default();
        let correlation_rules = Self::default_correlation_rules();

        Self {
            config,
            observability_manager,
            event_buffer: Arc::new(RwLock::new(Vec::new())),
            correlation_rules,
            event_stream: None,
        }
    }

    /// Initialize the event processor
    pub async fn initialize(&mut self) -> Result<()> {
        // Create broadcast channel for event streaming
        let (tx, _) = tokio::sync::broadcast::channel(1000);
        self.event_stream = Some(tx);

        // Start background tasks
        if self.config.enable_buffering {
            self.start_buffer_flush_task();
        }

        if self.config.enable_correlation {
            self.start_correlation_task();
        }

        Ok(())
    }

    /// Record a deployment event
    pub async fn record_event(
        &self,
        deployment_id: Uuid,
        event_type: DeploymentEventType,
        message: Option<String>,
        data: Option<serde_json::Value>,
    ) -> Result<Uuid> {
        let event_id = Uuid::new_v4();
        let timestamp = Utc::now();

        let event = DeploymentEvent {
            event_id,
            deployment_id,
            event_type: event_type.clone(),
            timestamp,
            message: message.clone(),
            data: data.clone(),
            severity: self.determine_severity(&event_type),
            source: "deployment_monitor".to_string(),
            correlation_id: None,
        };

        // Add to buffer
        if self.config.enable_buffering {
            let mut buffer = self.event_buffer.write().await;
            buffer.push(event.clone());

            // Check buffer size limit
            if buffer.len() >= self.config.max_buffer_size {
                self.flush_buffer().await?;
            }
        }

        // Send to stream
        if let Some(stream) = &self.event_stream {
            let _ = stream.send(event.clone());
        }

        // Record metrics
        self.record_event_metrics(&event_type, &event.severity).await?;

        // Create OpenTelemetry span
        let _span = instrumentation::create_span_with_attributes(
            "deployment_event",
            vec![
                opentelemetry::KeyValue::new("event_id", event_id.to_string()),
                opentelemetry::KeyValue::new("deployment_id", deployment_id.to_string()),
                opentelemetry::KeyValue::new("event_type", format!("{:?}", event_type)),
                opentelemetry::KeyValue::new("severity", format!("{:?}", event.severity)),
            ],
        );

        Ok(event_id)
    }

    /// Get events for a deployment
    pub async fn get_deployment_events(
        &self,
        deployment_id: Uuid,
        limit: Option<usize>,
    ) -> Result<Vec<DeploymentEvent>> {
        let buffer = self.event_buffer.read().await;
        let mut events: Vec<_> = buffer.iter()
            .filter(|event| event.deployment_id == deployment_id)
            .cloned()
            .collect();

        // Sort by timestamp (newest first)
        events.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));

        // Apply limit
        if let Some(limit) = limit {
            events.truncate(limit);
        }

        Ok(events)
    }

    /// Get correlated events
    pub async fn get_correlated_events(
        &self,
        deployment_id: Uuid,
        correlation_id: Uuid,
    ) -> Result<Vec<DeploymentEvent>> {
        let buffer = self.event_buffer.read().await;
        let events: Vec<_> = buffer.iter()
            .filter(|event| {
                event.deployment_id == deployment_id &&
                event.correlation_id == Some(correlation_id)
            })
            .cloned()
            .collect();

        Ok(events)
    }

    /// Search events by criteria
    pub async fn search_events(
        &self,
        deployment_id: Option<Uuid>,
        event_type: Option<DeploymentEventType>,
        severity: Option<EventSeverity>,
        time_range_start: Option<DateTime<Utc>>,
        time_range_end: Option<DateTime<Utc>>,
        limit: Option<usize>,
    ) -> Result<Vec<DeploymentEvent>> {
        let buffer = self.event_buffer.read().await;
        let mut events: Vec<_> = buffer.iter()
            .filter(|event| {
                (deployment_id.is_none() || event.deployment_id == deployment_id.unwrap()) &&
                (event_type.is_none() || event.event_type == event_type.clone().unwrap()) &&
                (severity.is_none() || event.severity == severity.clone().unwrap()) &&
                (time_range_start.is_none() || event.timestamp >= time_range_start.unwrap()) &&
                (time_range_end.is_none() || event.timestamp <= time_range_end.unwrap())
            })
            .cloned()
            .collect();

        // Sort by timestamp (newest first)
        events.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));

        // Apply limit
        if let Some(limit) = limit {
            events.truncate(limit);
        }

        Ok(events)
    }

    /// Subscribe to deployment events
    pub fn subscribe_events(&self) -> Option<tokio::sync::broadcast::Receiver<DeploymentEvent>> {
        self.event_stream.as_ref().map(|stream| stream.subscribe())
    }

    /// Flush event buffer
    async fn flush_buffer(&self) -> Result<()> {
        let mut buffer = self.event_buffer.write().await;
        if buffer.is_empty() {
            return Ok(());
        }

        // Process events in batches
        let events_to_process: Vec<_> = buffer.drain(..).collect();

        // Here you would typically send events to external storage
        // For now, we'll just log them
        for event in &events_to_process {
            tracing::info!(
                "Flushed deployment event: {} - {} - {:?}",
                event.deployment_id,
                event.event_type,
                event.severity
            );
        }

        // Record flush metrics
        self.record_buffer_flush_metrics(events_to_process.len()).await?;

        Ok(())
    }

    /// Start buffer flush task
    fn start_buffer_flush_task(&self) {
        let buffer = self.event_buffer.clone();
        let flush_interval = self.config.flush_interval_seconds;

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(flush_interval));
            loop {
                interval.tick().await;
                if let Err(e) = Self::flush_buffer_static(&buffer).await {
                    tracing::error!("Failed to flush event buffer: {}", e);
                }
            }
        });
    }

    /// Static method for buffer flushing (used in tokio::spawn)
    async fn flush_buffer_static(buffer: &Arc<RwLock<Vec<DeploymentEvent>>>) -> Result<()> {
        let mut buf = buffer.write().await;
        if buf.is_empty() {
            return Ok(());
        }

        let events_count = buf.len();
        buf.clear();

        tracing::info!("Flushed {} deployment events from buffer", events_count);
        Ok(())
    }

    /// Start correlation task
    fn start_correlation_task(&self) {
        let buffer = self.event_buffer.clone();
        let rules = self.correlation_rules.clone();
        let correlation_window = self.config.max_correlation_window_seconds;

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(30));
            loop {
                interval.tick().await;
                if let Err(e) = Self::process_correlations(&buffer, &rules, correlation_window).await {
                    tracing::error!("Failed to process event correlations: {}", e);
                }
            }
        });
    }

    /// Process event correlations
    async fn process_correlations(
        buffer: &Arc<RwLock<Vec<DeploymentEvent>>>,
        rules: &[CorrelationRule],
        correlation_window: u64,
    ) -> Result<()> {
        let buffer_read = buffer.read().await;
        let now = Utc::now();

        for rule in rules {
            let window_start = now - chrono::Duration::seconds(correlation_window as i64);

            let matching_events: Vec<_> = buffer_read.iter()
                .filter(|event| {
                    event.timestamp >= window_start &&
                    rule.event_types.contains(&event.event_type)
                })
                .collect();

            if matching_events.len() >= rule.min_events {
                // Generate correlation ID
                let correlation_id = Uuid::new_v4();

                // Update events with correlation ID
                drop(buffer_read);
                let mut buffer_write = buffer.write().await;

                for event in buffer_write.iter_mut() {
                    if matching_events.iter().any(|me| me.event_id == event.event_id) {
                        event.correlation_id = Some(correlation_id);
                    }
                }

                tracing::info!(
                    "Correlated {} events with rule '{}' and correlation ID {}",
                    matching_events.len(),
                    rule.name,
                    correlation_id
                );
            }
        }

        Ok(())
    }

    /// Determine event severity based on type
    fn determine_severity(&self, event_type: &DeploymentEventType) -> EventSeverity {
        match event_type {
            DeploymentEventType::DeploymentFailed |
            DeploymentEventType::RollbackFailed |
            DeploymentEventType::HealthCheckFailed => EventSeverity::Error,

            DeploymentEventType::PerformanceDegradation |
            DeploymentEventType::ResourceAlert => EventSeverity::Warning,

            DeploymentEventType::DeploymentStarted |
            DeploymentEventType::StageCompleted |
            DeploymentEventType::DeploymentCompleted |
            DeploymentEventType::RollbackInitiated |
            DeploymentEventType::RollbackCompleted |
            DeploymentEventType::HealthCheckPassed => EventSeverity::Info,
        }
    }

    /// Record event metrics
    async fn record_event_metrics(
        &self,
        event_type: &DeploymentEventType,
        severity: &EventSeverity,
    ) -> Result<()> {
        // Record to OpenTelemetry
        let labels = vec![
            opentelemetry::KeyValue::new("event_type", format!("{:?}", event_type)),
            opentelemetry::KeyValue::new("severity", format!("{:?}", severity)),
        ];

        instrumentation::record_counter(
            &instrumentation::counter("deployment_events_total", "Total deployment events"),
            1,
            labels,
        );

        Ok(())
    }

    /// Record buffer flush metrics
    async fn record_buffer_flush_metrics(&self, events_count: usize) -> Result<()> {
        instrumentation::record_histogram(
            &instrumentation::histogram("deployment_event_buffer_flush_size", "Size of event buffer flushes"),
            events_count as f64,
            vec![],
        );

        Ok(())
    }

    /// Get default correlation rules
    fn default_correlation_rules() -> Vec<CorrelationRule> {
        vec![
            CorrelationRule {
                name: "deployment_failure_sequence".to_string(),
                event_types: vec![
                    DeploymentEventType::HealthCheckFailed,
                    DeploymentEventType::PerformanceDegradation,
                    DeploymentEventType::DeploymentFailed,
                ],
                time_window_seconds: 300, // 5 minutes
                min_events: 2,
                logic: CorrelationLogic::Sequence,
            },
            CorrelationRule {
                name: "resource_alert_cluster".to_string(),
                event_types: vec![
                    DeploymentEventType::ResourceAlert,
                    DeploymentEventType::PerformanceDegradation,
                ],
                time_window_seconds: 60, // 1 minute
                min_events: 3,
                logic: CorrelationLogic::All,
            },
        ]
    }

    /// Shutdown the event processor
    pub async fn shutdown(&self) -> Result<()> {
        // Flush any remaining events
        self.flush_buffer().await?;
        Ok(())
    }
}

impl Default for EventProcessingConfig {
    fn default() -> Self {
        Self {
            enable_buffering: true,
            max_buffer_size: 1000,
            flush_interval_seconds: 60,
            enable_correlation: true,
            max_correlation_window_seconds: 300,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_event_processing_config_default() {
        let config = EventProcessingConfig::default();
        assert!(config.enable_buffering);
        assert_eq!(config.max_buffer_size, 1000);
        assert_eq!(config.flush_interval_seconds, 60);
        assert!(config.enable_correlation);
    }

    #[test]
    fn test_determine_severity() {
        let processor = DeploymentEventProcessor::new(Arc::new(ObservabilityManager::new(
            crate::observability::ObservabilityConfig::default()
        )));

        assert_eq!(
            processor.determine_severity(&DeploymentEventType::DeploymentFailed),
            EventSeverity::Error
        );
        assert_eq!(
            processor.determine_severity(&DeploymentEventType::DeploymentStarted),
            EventSeverity::Info
        );
    }
}