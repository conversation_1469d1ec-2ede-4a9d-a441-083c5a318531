//! # Deployment Metrics Collection
//!
//! Collects and aggregates deployment performance metrics, resource utilization,
//! and health indicators for comprehensive deployment monitoring.

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;

use crate::observability::{ObservabilityManager, instrumentation};
use crate::error::{InfinitumError, Result};
use crate::compliance::ci_cd_scanner::CICDPlaform;
use super::{DeploymentStatus, DeploymentEnvironment, DeploymentMetrics};

/// Metrics collection configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricsCollectionConfig {
    /// Enable metrics collection
    pub enabled: bool,
    /// Collection interval in seconds
    pub collection_interval_seconds: u64,
    /// Metrics retention period in hours
    pub retention_hours: u64,
    /// Enable resource monitoring
    pub enable_resource_monitoring: bool,
    /// Enable performance monitoring
    pub enable_performance_monitoring: bool,
    /// Enable health monitoring
    pub enable_health_monitoring: bool,
}

/// Resource utilization metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceUtilizationMetrics {
    /// CPU utilization percentage
    pub cpu_percent: f64,
    /// Memory utilization percentage
    pub memory_percent: f64,
    /// Disk utilization percentage
    pub disk_percent: f64,
    /// Network I/O in bytes
    pub network_io_bytes: u64,
    /// Disk I/O in bytes
    pub disk_io_bytes: u64,
    /// Timestamp
    pub timestamp: DateTime<Utc>,
}

/// Performance metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    /// Response time in milliseconds
    pub response_time_ms: f64,
    /// Throughput (requests per second)
    pub throughput_rps: f64,
    /// Error rate (0.0-1.0)
    pub error_rate: f64,
    /// Queue depth
    pub queue_depth: u64,
    /// Timestamp
    pub timestamp: DateTime<Utc>,
}

/// Health check metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckMetrics {
    /// Total health checks performed
    pub total_checks: u32,
    /// Successful health checks
    pub successful_checks: u32,
    /// Failed health checks
    pub failed_checks: u32,
    /// Average response time in milliseconds
    pub avg_response_time_ms: f64,
    /// Health check success rate (0.0-1.0)
    pub success_rate: f64,
    /// Timestamp
    pub timestamp: DateTime<Utc>,
}

/// Deployment metrics collector
pub struct DeploymentMetricsCollector {
    config: MetricsCollectionConfig,
    observability_manager: Arc<ObservabilityManager>,
    deployment_metrics: Arc<RwLock<HashMap<Uuid, Vec<DeploymentMetrics>>>>,
    resource_metrics: Arc<RwLock<HashMap<Uuid, Vec<ResourceUtilizationMetrics>>>>,
    performance_metrics: Arc<RwLock<HashMap<Uuid, Vec<PerformanceMetrics>>>>,
    health_metrics: Arc<RwLock<HashMap<Uuid, Vec<HealthCheckMetrics>>>>,
    aggregated_metrics: Arc<RwLock<HashMap<String, AggregatedMetrics>>>,
}

impl DeploymentMetricsCollector {
    /// Create new metrics collector
    pub fn new(observability_manager: Arc<ObservabilityManager>) -> Self {
        let config = MetricsCollectionConfig::default();

        Self {
            config,
            observability_manager,
            deployment_metrics: Arc::new(RwLock::new(HashMap::new())),
            resource_metrics: Arc::new(RwLock::new(HashMap::new())),
            performance_metrics: Arc::new(RwLock::new(HashMap::new())),
            health_metrics: Arc::new(RwLock::new(HashMap::new())),
            aggregated_metrics: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Initialize the metrics collector
    pub async fn initialize(&self) -> Result<()> {
        // Start background collection task
        if self.config.enabled {
            self.start_metrics_collection_task();
            self.start_cleanup_task();
        }

        Ok(())
    }

    /// Record deployment start
    pub async fn record_deployment_start(
        &self,
        deployment_id: Uuid,
        platform: &CICDPlaform,
        environment: &DeploymentEnvironment,
        version: &str,
    ) -> Result<()> {
        // Record to OpenTelemetry
        let labels = vec![
            opentelemetry::KeyValue::new("deployment_id", deployment_id.to_string()),
            opentelemetry::KeyValue::new("platform", format!("{:?}", platform)),
            opentelemetry::KeyValue::new("environment", format!("{:?}", environment)),
            opentelemetry::KeyValue::new("version", version.to_string()),
        ];

        instrumentation::record_counter(
            &instrumentation::counter("deployment_starts_total", "Total deployment starts"),
            1,
            labels,
        );

        Ok(())
    }

    /// Record stage completion
    pub async fn record_stage_completion(
        &self,
        deployment_id: Uuid,
        stage: &str,
        status: &DeploymentStatus,
    ) -> Result<()> {
        // Record to OpenTelemetry
        let labels = vec![
            opentelemetry::KeyValue::new("deployment_id", deployment_id.to_string()),
            opentelemetry::KeyValue::new("stage", stage.to_string()),
            opentelemetry::KeyValue::new("status", format!("{:?}", status)),
        ];

        instrumentation::record_counter(
            &instrumentation::counter("deployment_stage_completions_total", "Total deployment stage completions"),
            1,
            labels,
        );

        Ok(())
    }

    /// Record deployment completion
    pub async fn record_deployment_completion(
        &self,
        deployment_id: Uuid,
        status: &DeploymentStatus,
        metrics: &DeploymentMetrics,
    ) -> Result<()> {
        // Store metrics
        {
            let mut deployment_metrics = self.deployment_metrics.write().await;
            deployment_metrics.entry(deployment_id)
                .or_insert_with(Vec::new)
                .push(metrics.clone());
        }

        // Record to OpenTelemetry
        let labels = vec![
            opentelemetry::KeyValue::new("deployment_id", deployment_id.to_string()),
            opentelemetry::KeyValue::new("status", format!("{:?}", status)),
        ];

        instrumentation::record_histogram(
            &instrumentation::histogram("deployment_duration_seconds", "Deployment duration in seconds"),
            metrics.total_duration_seconds as f64,
            labels.clone(),
        );

        instrumentation::record_histogram(
            &instrumentation::histogram("deployment_cpu_utilization_percent", "Deployment CPU utilization"),
            metrics.cpu_utilization_percent,
            labels.clone(),
        );

        instrumentation::record_histogram(
            &instrumentation::histogram("deployment_memory_utilization_percent", "Deployment memory utilization"),
            metrics.memory_utilization_percent,
            labels.clone(),
        );

        instrumentation::record_counter(
            &instrumentation::counter("deployment_health_checks_total", "Total health checks during deployment"),
            metrics.health_checks_count as u64,
            labels.clone(),
        );

        // Update aggregated metrics
        self.update_aggregated_metrics(deployment_id, status, metrics).await?;

        Ok(())
    }

    /// Record resource utilization
    pub async fn record_resource_utilization(
        &self,
        deployment_id: Uuid,
        metrics: ResourceUtilizationMetrics,
    ) -> Result<()> {
        if !self.config.enable_resource_monitoring {
            return Ok(());
        }

        // Store metrics
        {
            let mut resource_metrics = self.resource_metrics.write().await;
            resource_metrics.entry(deployment_id)
                .or_insert_with(Vec::new)
                .push(metrics.clone());
        }

        // Record to OpenTelemetry
        let labels = vec![
            opentelemetry::KeyValue::new("deployment_id", deployment_id.to_string()),
        ];

        self.observability_manager.custom_metrics_manager.set_gauge(
            "deployment_cpu_utilization_percent",
            metrics.cpu_percent,
            labels.clone(),
        );

        self.observability_manager.custom_metrics_manager.set_gauge(
            "deployment_memory_utilization_percent",
            metrics.memory_percent,
            labels.clone(),
        );

        self.observability_manager.custom_metrics_manager.set_gauge(
            "deployment_disk_utilization_percent",
            metrics.disk_percent,
            labels.clone(),
        );

        Ok(())
    }

    /// Record performance metrics
    pub async fn record_performance_metrics(
        &self,
        deployment_id: Uuid,
        metrics: PerformanceMetrics,
    ) -> Result<()> {
        if !self.config.enable_performance_monitoring {
            return Ok(());
        }

        // Store metrics
        {
            let mut performance_metrics = self.performance_metrics.write().await;
            performance_metrics.entry(deployment_id)
                .or_insert_with(Vec::new)
                .push(metrics.clone());
        }

        // Record to OpenTelemetry
        let labels = vec![
            opentelemetry::KeyValue::new("deployment_id", deployment_id.to_string()),
        ];

        instrumentation::record_histogram(
            &instrumentation::histogram("deployment_response_time_ms", "Response time during deployment"),
            metrics.response_time_ms,
            labels.clone(),
        );

        self.observability_manager.custom_metrics_manager.set_gauge(
            "deployment_throughput_rps",
            metrics.throughput_rps,
            labels.clone(),
        );

        self.observability_manager.custom_metrics_manager.set_gauge(
            "deployment_error_rate",
            metrics.error_rate,
            labels.clone(),
        );

        Ok(())
    }

    /// Record health check metrics
    pub async fn record_health_check_metrics(
        &self,
        deployment_id: Uuid,
        metrics: HealthCheckMetrics,
    ) -> Result<()> {
        if !self.config.enable_health_monitoring {
            return Ok(());
        }

        // Store metrics
        {
            let mut health_metrics = self.health_metrics.write().await;
            health_metrics.entry(deployment_id)
                .or_insert_with(Vec::new)
                .push(metrics.clone());
        }

        // Record to OpenTelemetry
        let labels = vec![
            opentelemetry::KeyValue::new("deployment_id", deployment_id.to_string()),
        ];

        instrumentation::record_counter(
            &instrumentation::counter("deployment_health_checks_total", "Total health checks"),
            metrics.total_checks as u64,
            labels.clone(),
        );

        instrumentation::record_counter(
            &instrumentation::counter("deployment_health_checks_successful", "Successful health checks"),
            metrics.successful_checks as u64,
            labels.clone(),
        );

        self.observability_manager.custom_metrics_manager.set_gauge(
            "deployment_health_check_success_rate",
            metrics.success_rate,
            labels.clone(),
        );

        Ok(())
    }

    /// Get deployment metrics
    pub async fn get_deployment_metrics(&self, deployment_id: Uuid) -> Result<Vec<DeploymentMetrics>> {
        let deployment_metrics = self.deployment_metrics.read().await;
        Ok(deployment_metrics.get(&deployment_id)
            .cloned()
            .unwrap_or_default())
    }

    /// Get resource utilization metrics
    pub async fn get_resource_metrics(&self, deployment_id: Uuid) -> Result<Vec<ResourceUtilizationMetrics>> {
        let resource_metrics = self.resource_metrics.read().await;
        Ok(resource_metrics.get(&deployment_id)
            .cloned()
            .unwrap_or_default())
    }

    /// Get performance metrics
    pub async fn get_performance_metrics(&self, deployment_id: Uuid) -> Result<Vec<PerformanceMetrics>> {
        let performance_metrics = self.performance_metrics.read().await;
        Ok(performance_metrics.get(&deployment_id)
            .cloned()
            .unwrap_or_default())
    }

    /// Get health check metrics
    pub async fn get_health_metrics(&self, deployment_id: Uuid) -> Result<Vec<HealthCheckMetrics>> {
        let health_metrics = self.health_metrics.read().await;
        Ok(health_metrics.get(&deployment_id)
            .cloned()
            .unwrap_or_default())
    }

    /// Get aggregated metrics
    pub async fn get_aggregated_metrics(&self, key: &str) -> Result<Option<AggregatedMetrics>> {
        let aggregated = self.aggregated_metrics.read().await;
        Ok(aggregated.get(key).cloned())
    }

    /// Get deployment success rate
    pub async fn get_deployment_success_rate(
        &self,
        environment: Option<DeploymentEnvironment>,
        time_range_hours: Option<u64>,
    ) -> Result<f64> {
        let deployment_metrics = self.deployment_metrics.read().await;
        let now = Utc::now();

        let mut total_deployments = 0;
        let mut successful_deployments = 0;

        for (deployment_id, metrics_list) in deployment_metrics.iter() {
            // Check time range if specified
            if let Some(hours) = time_range_hours {
                let cutoff = now - chrono::Duration::hours(hours as i64);
                if metrics_list.iter().all(|m| m.timestamp < cutoff) {
                    continue;
                }
            }

            // This is a simplified check - in practice, you'd need to correlate
            // with deployment status from the main monitor
            for metrics in metrics_list {
                total_deployments += 1;
                // Consider deployment successful if it completed and has reasonable metrics
                if metrics.total_duration_seconds > 0 &&
                   metrics.cpu_utilization_percent >= 0.0 &&
                   metrics.memory_utilization_percent >= 0.0 {
                    successful_deployments += 1;
                }
            }
        }

        if total_deployments == 0 {
            return Ok(0.0);
        }

        Ok(successful_deployments as f64 / total_deployments as f64)
    }

    /// Update aggregated metrics
    async fn update_aggregated_metrics(
        &self,
        deployment_id: Uuid,
        status: &DeploymentStatus,
        metrics: &DeploymentMetrics,
    ) -> Result<()> {
        let mut aggregated = self.aggregated_metrics.write().await;

        // Update global metrics
        let global_key = "global".to_string();
        let global_metrics = aggregated.entry(global_key)
            .or_insert_with(AggregatedMetrics::default);

        global_metrics.total_deployments += 1;
        global_metrics.total_duration_seconds += metrics.total_duration_seconds;
        global_metrics.avg_cpu_utilization = (global_metrics.avg_cpu_utilization * (global_metrics.total_deployments - 1) as f64 + metrics.cpu_utilization_percent) / global_metrics.total_deployments as f64;
        global_metrics.avg_memory_utilization = (global_metrics.avg_memory_utilization * (global_metrics.total_deployments - 1) as f64 + metrics.memory_utilization_percent) / global_metrics.total_deployments as f64;

        if matches!(status, DeploymentStatus::Completed) {
            global_metrics.successful_deployments += 1;
        }

        Ok(())
    }

    /// Start metrics collection task
    fn start_metrics_collection_task(&self) {
        let collection_interval = self.config.collection_interval_seconds;
        let enable_resource = self.config.enable_resource_monitoring;
        let enable_performance = self.config.enable_performance_monitoring;
        let enable_health = self.config.enable_health_monitoring;

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(collection_interval));
            loop {
                interval.tick().await;

                // Collect system resource metrics
                if enable_resource {
                    if let Err(e) = Self::collect_system_resource_metrics().await {
                        tracing::error!("Failed to collect system resource metrics: {}", e);
                    }
                }

                // Collect performance metrics
                if enable_performance {
                    if let Err(e) = Self::collect_performance_metrics().await {
                        tracing::error!("Failed to collect performance metrics: {}", e);
                    }
                }

                // Collect health metrics
                if enable_health {
                    if let Err(e) = Self::collect_health_metrics().await {
                        tracing::error!("Failed to collect health metrics: {}", e);
                    }
                }
            }
        });
    }

    /// Collect system resource metrics
    async fn collect_system_resource_metrics() -> Result<()> {
        // This would integrate with system monitoring libraries
        // For now, return placeholder implementation
        tracing::debug!("Collecting system resource metrics");
        Ok(())
    }

    /// Collect performance metrics
    async fn collect_performance_metrics() -> Result<()> {
        // This would integrate with application performance monitoring
        tracing::debug!("Collecting performance metrics");
        Ok(())
    }

    /// Collect health metrics
    async fn collect_health_metrics() -> Result<()> {
        // This would perform health checks
        tracing::debug!("Collecting health metrics");
        Ok(())
    }

    /// Start cleanup task
    fn start_cleanup_task(&self) {
        let retention_hours = self.config.retention_hours;
        let deployment_metrics = self.deployment_metrics.clone();
        let resource_metrics = self.resource_metrics.clone();
        let performance_metrics = self.performance_metrics.clone();
        let health_metrics = self.health_metrics.clone();

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(3600)); // Run hourly
            loop {
                interval.tick().await;

                let cutoff = Utc::now() - chrono::Duration::hours(retention_hours as i64);

                // Clean up old metrics
                Self::cleanup_old_metrics(&deployment_metrics, cutoff).await;
                Self::cleanup_old_metrics(&resource_metrics, cutoff).await;
                Self::cleanup_old_metrics(&performance_metrics, cutoff).await;
                Self::cleanup_old_metrics(&health_metrics, cutoff).await;
            }
        });
    }

    /// Clean up old metrics
    async fn cleanup_old_metrics<T: HasTimestamp>(
        metrics_map: &Arc<RwLock<HashMap<Uuid, Vec<T>>>>,
        cutoff: DateTime<Utc>,
    ) {
        let mut metrics = metrics_map.write().await;

        for metrics_list in metrics.values_mut() {
            metrics_list.retain(|m| m.timestamp() >= cutoff);
        }

        // Remove empty entries
        metrics.retain(|_, list| !list.is_empty());
    }

    /// Shutdown the metrics collector
    pub async fn shutdown(&self) -> Result<()> {
        // Flush any pending metrics
        Ok(())
    }
}

/// Aggregated metrics structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AggregatedMetrics {
    /// Total deployments
    pub total_deployments: u64,
    /// Successful deployments
    pub successful_deployments: u64,
    /// Total duration in seconds
    pub total_duration_seconds: u64,
    /// Average CPU utilization
    pub avg_cpu_utilization: f64,
    /// Average memory utilization
    pub avg_memory_utilization: f64,
    /// Success rate
    pub success_rate: f64,
}

impl Default for AggregatedMetrics {
    fn default() -> Self {
        Self {
            total_deployments: 0,
            successful_deployments: 0,
            total_duration_seconds: 0,
            avg_cpu_utilization: 0.0,
            avg_memory_utilization: 0.0,
            success_rate: 0.0,
        }
    }
}

impl Default for MetricsCollectionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            collection_interval_seconds: 30,
            retention_hours: 168, // 7 days
            enable_resource_monitoring: true,
            enable_performance_monitoring: true,
            enable_health_monitoring: true,
        }
    }
}

/// Trait for types that have timestamps
trait HasTimestamp {
    fn timestamp(&self) -> DateTime<Utc>;
}

impl HasTimestamp for DeploymentMetrics {
    fn timestamp(&self) -> DateTime<Utc> {
        // DeploymentMetrics doesn't have a timestamp field in the current design
        // This would need to be added or handled differently
        Utc::now()
    }
}

impl HasTimestamp for ResourceUtilizationMetrics {
    fn timestamp(&self) -> DateTime<Utc> {
        self.timestamp
    }
}

impl HasTimestamp for PerformanceMetrics {
    fn timestamp(&self) -> DateTime<Utc> {
        self.timestamp
    }
}

impl HasTimestamp for HealthCheckMetrics {
    fn timestamp(&self) -> DateTime<Utc> {
        self.timestamp
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_metrics_collection_config_default() {
        let config = MetricsCollectionConfig::default();
        assert!(config.enabled);
        assert_eq!(config.collection_interval_seconds, 30);
        assert_eq!(config.retention_hours, 168);
        assert!(config.enable_resource_monitoring);
    }

    #[test]
    fn test_aggregated_metrics_default() {
        let metrics = AggregatedMetrics::default();
        assert_eq!(metrics.total_deployments, 0);
        assert_eq!(metrics.successful_deployments, 0);
        assert_eq!(metrics.total_duration_seconds, 0);
        assert_eq!(metrics.avg_cpu_utilization, 0.0);
        assert_eq!(metrics.avg_memory_utilization, 0.0);
        assert_eq!(metrics.success_rate, 0.0);
    }
}