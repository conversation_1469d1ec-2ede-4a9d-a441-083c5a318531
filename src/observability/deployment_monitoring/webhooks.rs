//! # Deployment Webhooks
//!
//! Webhook support for deployment monitoring, enabling real-time notifications
//! to external systems and CI/CD tools.

use std::collections::HashMap;
use std::sync::Arc;
use serde::{Deserialize, Serialize};
use reqwest::Client;
use tokio::sync::RwLock;

use crate::error::{InfinitumError, Result};
use super::{DeploymentMetadata, DeploymentEventType};

/// Webhook configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebhookConfig {
    /// Enable webhooks
    pub enabled: bool,
    /// Webhook endpoints
    pub endpoints: Vec<String>,
    /// Request timeout in seconds
    pub timeout_seconds: u64,
    /// Retry configuration
    pub retry_config: WebhookRetryConfig,
    /// Custom headers
    pub headers: HashMap<String, String>,
}

/// Webhook retry configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct WebhookRetryConfig {
    /// Maximum retry attempts
    pub max_attempts: u32,
    /// Initial delay between retries in seconds
    pub initial_delay_seconds: u64,
    /// Maximum delay between retries in seconds
    pub max_delay_seconds: u64,
}

/// Webhook payload
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebhookPayload {
    /// Event type
    pub event_type: String,
    /// Deployment ID
    pub deployment_id: String,
    /// Timestamp
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// Event data
    pub data: serde_json::Value,
    /// Metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Webhook delivery record
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebhookDelivery {
    /// Delivery ID
    pub delivery_id: uuid::Uuid,
    /// Webhook URL
    pub webhook_url: String,
    /// Payload
    pub payload: WebhookPayload,
    /// Delivery status
    pub status: WebhookStatus,
    /// Attempt count
    pub attempt_count: u32,
    /// Last attempt timestamp
    pub last_attempt_at: Option<chrono::DateTime<chrono::Utc>>,
    /// Next retry timestamp
    pub next_retry_at: Option<chrono::DateTime<chrono::Utc>>,
    /// Error message
    pub error_message: Option<String>,
}

/// Webhook delivery status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum WebhookStatus {
    /// Delivery pending
    Pending,
    /// Delivery in progress
    InProgress,
    /// Delivery successful
    Success,
    /// Delivery failed
    Failed,
    /// Delivery abandoned after max retries
    Abandoned,
}

/// Webhook manager
pub struct WebhookManager {
    config: WebhookConfig,
    client: Client,
    delivery_history: Arc<RwLock<HashMap<uuid::Uuid, WebhookDelivery>>>,
}

impl WebhookManager {
    /// Create new webhook manager
    pub fn new(endpoints: Vec<String>) -> Self {
        let config = WebhookConfig {
            enabled: true,
            endpoints,
            timeout_seconds: 30,
            retry_config: WebhookRetryConfig::default(),
            headers: HashMap::new(),
        };

        let client = Client::builder()
            .timeout(std::time::Duration::from_secs(config.timeout_seconds))
            .build()
            .unwrap_or_else(|_| Client::new());

        Self {
            config,
            client,
            delivery_history: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Initialize the webhook manager
    pub async fn initialize(&self) -> Result<()> {
        // Start retry task
        self.start_retry_task();
        Ok(())
    }

    /// Send deployment event webhook
    pub async fn send_deployment_event(
        &self,
        event_type: &str,
        metadata: &DeploymentMetadata,
    ) -> Result<()> {
        if !self.config.enabled || self.config.endpoints.is_empty() {
            return Ok(());
        }

        let payload = WebhookPayload {
            event_type: event_type.to_string(),
            deployment_id: metadata.deployment_id.to_string(),
            timestamp: chrono::Utc::now(),
            data: serde_json::to_value(metadata).unwrap_or_default(),
            metadata: metadata.custom_metadata.clone(),
        };

        for endpoint in &self.config.endpoints {
            let delivery_id = uuid::Uuid::new_v4();
            let delivery = WebhookDelivery {
                delivery_id,
                webhook_url: endpoint.clone(),
                payload: payload.clone(),
                status: WebhookStatus::Pending,
                attempt_count: 0,
                last_attempt_at: None,
                next_retry_at: None,
                error_message: None,
            };

            // Store delivery record
            {
                let mut history = self.delivery_history.write().await;
                history.insert(delivery_id, delivery.clone());
            }

            // Send webhook
            if let Err(e) = self.send_webhook(&delivery).await {
                tracing::error!("Failed to send webhook to {}: {}", endpoint, e);
            }
        }

        Ok(())
    }

    /// Send custom webhook
    pub async fn send_custom_webhook(
        &self,
        event_type: &str,
        deployment_id: uuid::Uuid,
        data: serde_json::Value,
        metadata: HashMap<String, serde_json::Value>,
    ) -> Result<()> {
        if !self.config.enabled || self.config.endpoints.is_empty() {
            return Ok(());
        }

        let payload = WebhookPayload {
            event_type: event_type.to_string(),
            deployment_id: deployment_id.to_string(),
            timestamp: chrono::Utc::now(),
            data,
            metadata,
        };

        for endpoint in &self.config.endpoints {
            let delivery_id = uuid::Uuid::new_v4();
            let delivery = WebhookDelivery {
                delivery_id,
                webhook_url: endpoint.clone(),
                payload: payload.clone(),
                status: WebhookStatus::Pending,
                attempt_count: 0,
                last_attempt_at: None,
                next_retry_at: None,
                error_message: None,
            };

            // Store delivery record
            {
                let mut history = self.delivery_history.write().await;
                history.insert(delivery_id, delivery.clone());
            }

            // Send webhook
            if let Err(e) = self.send_webhook(&delivery).await {
                tracing::error!("Failed to send custom webhook to {}: {}", endpoint, e);
            }
        }

        Ok(())
    }

    /// Send webhook to endpoint
    async fn send_webhook(&self, delivery: &WebhookDelivery) -> Result<()> {
        let mut request = self.client
            .post(&delivery.webhook_url)
            .json(&delivery.payload);

        // Add custom headers
        for (key, value) in &self.config.headers {
            request = request.header(key, value);
        }

        let response = request.send().await?;

        if response.status().is_success() {
            // Update delivery status to success
            self.update_delivery_status(delivery.delivery_id, WebhookStatus::Success, None).await?;
            tracing::info!("Webhook delivered successfully to {}", delivery.webhook_url);
        } else {
            let error_msg = format!("HTTP {}: {}", response.status(), response.text().await.unwrap_or_default());
            self.handle_delivery_failure(delivery.delivery_id, error_msg).await?;
        }

        Ok(())
    }

    /// Handle delivery failure
    async fn handle_delivery_failure(&self, delivery_id: uuid::Uuid, error_message: String) -> Result<()> {
        let mut history = self.delivery_history.write().await;

        if let Some(delivery) = history.get_mut(&delivery_id) {
            delivery.attempt_count += 1;
            delivery.last_attempt_at = Some(chrono::Utc::now());
            delivery.error_message = Some(error_message.clone());

            if delivery.attempt_count >= self.config.retry_config.max_attempts {
                delivery.status = WebhookStatus::Abandoned;
                tracing::error!("Webhook delivery abandoned after {} attempts: {}", delivery.attempt_count, error_message);
            } else {
                delivery.status = WebhookStatus::Failed;
                // Schedule retry
                let delay = self.calculate_retry_delay(delivery.attempt_count);
                delivery.next_retry_at = Some(chrono::Utc::now() + chrono::Duration::seconds(delay as i64));
                tracing::warn!("Webhook delivery failed, will retry in {} seconds: {}", delay, error_message);
            }
        }

        Ok(())
    }

    /// Update delivery status
    async fn update_delivery_status(
        &self,
        delivery_id: uuid::Uuid,
        status: WebhookStatus,
        error_message: Option<String>,
    ) -> Result<()> {
        let mut history = self.delivery_history.write().await;

        if let Some(delivery) = history.get_mut(&delivery_id) {
            delivery.status = status;
            delivery.last_attempt_at = Some(chrono::Utc::now());
            if let Some(error) = error_message {
                delivery.error_message = Some(error);
            }
        }

        Ok(())
    }

    /// Calculate retry delay
    fn calculate_retry_delay(&self, attempt: u32) -> u64 {
        let base_delay = self.config.retry_config.initial_delay_seconds as f64;
        let max_delay = self.config.retry_config.max_delay_seconds as f64;

        // Exponential backoff
        let delay = base_delay * (2.0_f64).powi(attempt.saturating_sub(1) as i32);
        let actual_delay = delay.min(max_delay);

        actual_delay as u64
    }

    /// Get delivery status
    pub async fn get_delivery_status(&self, delivery_id: uuid::Uuid) -> Result<Option<WebhookDelivery>> {
        let history = self.delivery_history.read().await;
        Ok(history.get(&delivery_id).cloned())
    }

    /// Get delivery history
    pub async fn get_delivery_history(
        &self,
        limit: Option<usize>,
    ) -> Result<Vec<WebhookDelivery>> {
        let history = self.delivery_history.read().await;
        let mut deliveries: Vec<_> = history.values().cloned().collect();

        // Sort by last attempt time (most recent first)
        deliveries.sort_by(|a, b| {
            b.last_attempt_at.unwrap_or(b.payload.timestamp)
                .cmp(&a.last_attempt_at.unwrap_or(a.payload.timestamp))
        });

        // Apply limit
        if let Some(limit) = limit {
            deliveries.truncate(limit);
        }

        Ok(deliveries)
    }

    /// Start retry task
    fn start_retry_task(&self) {
        let delivery_history = self.delivery_history.clone();
        let retry_config = self.config.retry_config.clone();

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(30)); // Check every 30 seconds

            loop {
                interval.tick().await;

                let now = chrono::Utc::now();
                let mut deliveries_to_retry = Vec::new();

                // Find deliveries that need retry
                {
                    let history = delivery_history.read().await;
                    for (delivery_id, delivery) in history.iter() {
                        if delivery.status == WebhookStatus::Failed &&
                           delivery.attempt_count < retry_config.max_attempts {
                            if let Some(next_retry) = delivery.next_retry_at {
                                if now >= next_retry {
                                    deliveries_to_retry.push(delivery.clone());
                                }
                            }
                        }
                    }
                }

                // Retry deliveries
                for delivery in deliveries_to_retry {
                    tracing::info!("Retrying webhook delivery to {}", delivery.webhook_url);

                    // Update status to in progress
                    {
                        let mut history = delivery_history.write().await;
                        if let Some(d) = history.get_mut(&delivery.delivery_id) {
                            d.status = WebhookStatus::InProgress;
                        }
                    }

                    // Attempt delivery
                    let client = Client::builder()
                        .timeout(std::time::Duration::from_secs(30))
                        .build()
                        .unwrap_or_else(|_| Client::new());

                    let mut request = client
                        .post(&delivery.webhook_url)
                        .json(&delivery.payload);

                    // Add custom headers
                    for (key, value) in &HashMap::new() { // Would use actual headers
                        request = request.header(key, value);
                    }

                    match request.send().await {
                        Ok(response) => {
                            if response.status().is_success() {
                                Self::update_delivery_status_static(&delivery_history, delivery.delivery_id, WebhookStatus::Success, None).await;
                                tracing::info!("Webhook retry successful to {}", delivery.webhook_url);
                            } else {
                                let error_msg = format!("HTTP {}: {}", response.status(), response.text().await.unwrap_or_default());
                                Self::handle_delivery_failure_static(&delivery_history, delivery.delivery_id, error_msg, retry_config.clone()).await;
                            }
                        }
                        Err(e) => {
                            Self::handle_delivery_failure_static(&delivery_history, delivery.delivery_id, e.to_string(), retry_config.clone()).await;
                        }
                    }
                }
            }
        });
    }

    /// Static method for updating delivery status (used in tokio::spawn)
    async fn update_delivery_status_static(
        history: &Arc<RwLock<HashMap<uuid::Uuid, WebhookDelivery>>>,
        delivery_id: uuid::Uuid,
        status: WebhookStatus,
        error_message: Option<String>,
    ) {
        let mut hist = history.write().await;
        if let Some(delivery) = hist.get_mut(&delivery_id) {
            delivery.status = status;
            delivery.last_attempt_at = Some(chrono::Utc::now());
            if let Some(error) = error_message {
                delivery.error_message = Some(error);
            }
        }
    }

    /// Static method for handling delivery failure (used in tokio::spawn)
    async fn handle_delivery_failure_static(
        history: &Arc<RwLock<HashMap<uuid::Uuid, WebhookDelivery>>>,
        delivery_id: uuid::Uuid,
        error_message: String,
        retry_config: WebhookRetryConfig,
    ) {
        let mut hist = history.write().await;
        if let Some(delivery) = hist.get_mut(&delivery_id) {
            delivery.attempt_count += 1;
            delivery.last_attempt_at = Some(chrono::Utc::now());
            delivery.error_message = Some(error_message.clone());

            if delivery.attempt_count >= retry_config.max_attempts {
                delivery.status = WebhookStatus::Abandoned;
                tracing::error!("Webhook delivery abandoned after {} attempts: {}", delivery.attempt_count, error_message);
            } else {
                delivery.status = WebhookStatus::Failed;
                // Schedule retry
                let delay = Self::calculate_retry_delay_static(delivery.attempt_count, &retry_config);
                delivery.next_retry_at = Some(chrono::Utc::now() + chrono::Duration::seconds(delay as i64));
                tracing::warn!("Webhook delivery failed, will retry in {} seconds: {}", delay, error_message);
            }
        }
    }

    /// Static method for calculating retry delay
    fn calculate_retry_delay_static(attempt: u32, config: &WebhookRetryConfig) -> u64 {
        let base_delay = config.initial_delay_seconds as f64;
        let max_delay = config.max_delay_seconds as f64;

        // Exponential backoff
        let delay = base_delay * (2.0_f64).powi(attempt.saturating_sub(1) as i32);
        let actual_delay = delay.min(max_delay);

        actual_delay as u64
    }

    /// Shutdown the webhook manager
    pub async fn shutdown(&self) -> Result<()> {
        // Clear delivery history
        let mut history = self.delivery_history.write().await;
        history.clear();

        Ok(())
    }
}

impl Default for WebhookRetryConfig {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            initial_delay_seconds: 5,
            max_delay_seconds: 300, // 5 minutes
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_webhook_retry_config_default() {
        let config = WebhookRetryConfig::default();
        assert_eq!(config.max_attempts, 3);
        assert_eq!(config.initial_delay_seconds, 5);
        assert_eq!(config.max_delay_seconds, 300);
    }

    #[test]
    fn test_calculate_retry_delay() {
        let manager = WebhookManager::new(vec![]);
        let config = WebhookRetryConfig::default();

        // Test first retry (attempt 1)
        let delay1 = manager.calculate_retry_delay(1);
        assert_eq!(delay1, 5);

        // Test second retry (attempt 2)
        let delay2 = manager.calculate_retry_delay(2);
        assert_eq!(delay2, 10);

        // Test third retry (attempt 3)
        let delay3 = manager.calculate_retry_delay(3);
        assert_eq!(delay3, 20);
    }
}