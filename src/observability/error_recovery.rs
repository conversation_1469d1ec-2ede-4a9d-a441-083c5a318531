//! # Error Recovery and Resilience Framework
//!
//! This module provides comprehensive error recovery and resilience mechanisms
//! to ensure observability system stability during failures and error conditions.

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// Error recovery configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorRecoveryConfig {
    /// Enable error recovery
    pub enabled: bool,
    /// Retry configuration
    pub retry: RetryConfig,
    /// Dead letter queue configuration
    pub dead_letter_queue: DeadLetterQueueConfig,
    /// Timeout configuration
    pub timeout: TimeoutConfig,
    /// Recovery procedures configuration
    pub recovery: RecoveryConfig,
}

/// Retry configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetryConfig {
    /// Enable retry mechanisms
    pub enabled: bool,
    /// Maximum retry attempts
    pub max_attempts: u32,
    /// Initial retry delay in milliseconds
    pub initial_delay_ms: u64,
    /// Maximum retry delay in milliseconds
    pub max_delay_ms: u64,
    /// Backoff multiplier
    pub backoff_multiplier: f64,
    /// Jitter enabled
    pub jitter_enabled: bool,
    /// Retryable error types
    pub retryable_errors: Vec<String>,
}

/// Dead letter queue configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeadLetterQueueConfig {
    /// Enable dead letter queues
    pub enabled: bool,
    /// Maximum queue size
    pub max_queue_size: usize,
    /// Message retention period in seconds
    pub retention_seconds: u64,
    /// Processing batch size
    pub batch_size: usize,
    /// Reprocessing enabled
    pub reprocessing_enabled: bool,
    /// Maximum reprocessing attempts
    pub max_reprocessing_attempts: u32,
}

/// Timeout configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimeoutConfig {
    /// Enable timeout handling
    pub enabled: bool,
    /// Default timeout in seconds
    pub default_timeout_seconds: u64,
    /// Service-specific timeouts
    pub service_timeouts: HashMap<String, u64>,
    /// Circuit breaker timeout in seconds
    pub circuit_breaker_timeout_seconds: u64,
}

/// Recovery procedures configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecoveryConfig {
    /// Enable recovery procedures
    pub enabled: bool,
    /// Data loss recovery enabled
    pub data_loss_recovery_enabled: bool,
    /// State recovery enabled
    pub state_recovery_enabled: bool,
    /// Automatic recovery enabled
    pub automatic_recovery_enabled: bool,
    /// Recovery timeout in seconds
    pub recovery_timeout_seconds: u64,
}

/// Retry attempt information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetryAttempt {
    pub attempt_number: u32,
    pub timestamp: DateTime<Utc>,
    pub error_message: String,
    pub delay_ms: u64,
}

/// Failed message in dead letter queue
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FailedMessage {
    pub id: String,
    pub original_message: serde_json::Value,
    pub error_message: String,
    pub failed_at: DateTime<Utc>,
    pub retry_attempts: Vec<RetryAttempt>,
    pub service_name: String,
    pub operation: String,
    pub reprocessing_attempts: u32,
    pub max_reprocessing_attempts: u32,
}

/// Recovery operation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecoveryOperation {
    pub id: String,
    pub operation_type: RecoveryOperationType,
    pub status: RecoveryStatus,
    pub started_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
    pub error_message: Option<String>,
    pub affected_services: Vec<String>,
}

/// Recovery operation types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum RecoveryOperationType {
    DataLossRecovery,
    StateRecovery,
    ServiceRestart,
    ConfigurationReload,
    CircuitBreakerReset,
}

/// Recovery status
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum RecoveryStatus {
    Pending,
    InProgress,
    Completed,
    Failed,
    Cancelled,
}

/// Error recovery metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorRecoveryMetrics {
    pub total_retry_attempts: u64,
    pub successful_retries: u64,
    pub failed_retries: u64,
    pub dead_letter_queue_size: usize,
    pub messages_reprocessed: u64,
    pub recovery_operations_total: u64,
    pub recovery_operations_successful: u64,
    pub average_retry_delay_ms: f64,
    pub timeout_events: u64,
}

/// Retry manager for handling retry logic
#[derive(Debug)]
pub struct RetryManager {
    config: RetryConfig,
}

impl RetryManager {
    /// Create a new retry manager
    pub fn new(config: RetryConfig) -> Self {
        Self { config }
    }

    /// Execute operation with retry logic
    pub async fn execute_with_retry<F, Fut, T, E, F2>(
        &self,
        operation: F,
        should_retry: F2,
    ) -> Result<T, RetryError<E>>
    where
        F: Fn() -> Fut,
        Fut: std::future::Future<Output = Result<T, E>>,
        F2: Fn(&E) -> bool,
        E: std::fmt::Debug,
    {
        let mut attempts = 0;
        let mut last_error = None;

        loop {
            attempts += 1;

            match operation().await {
                Ok(result) => return Ok(result),
                Err(error) => {
                    last_error = Some(error);

                    if attempts >= self.config.max_attempts || !should_retry(last_error.as_ref().unwrap()) {
                        break;
                    }

                    let delay = self.calculate_delay(attempts);
                    tokio::time::sleep(delay).await;
                }
            }
        }

        Err(RetryError::MaxAttemptsExceeded {
            attempts,
            last_error: last_error.unwrap(),
        })
    }

    /// Calculate delay for retry attempt
    fn calculate_delay(&self, attempt: u32) -> Duration {
        let base_delay = self.config.initial_delay_ms as f64;
        let multiplier = self.config.backoff_multiplier;
        let max_delay = self.config.max_delay_ms as f64;

        let delay = base_delay * multiplier.powi(attempt.saturating_sub(1) as i32);
        let delay = delay.min(max_delay);

        let mut final_delay = delay as u64;

        // Add jitter if enabled
        if self.config.jitter_enabled {
            use rand::prelude::*;
            let jitter = rand::thread_rng().gen_range(0..(final_delay / 4));
            final_delay += jitter;
        }

        Duration::from_millis(final_delay)
    }

    /// Check if error is retryable
    pub fn is_retryable_error(&self, error_message: &str) -> bool {
        if self.config.retryable_errors.is_empty() {
            return true; // If no specific errors configured, retry all
        }

        self.config.retryable_errors
            .iter()
            .any(|retryable| error_message.contains(retryable))
    }
}

/// Dead letter queue manager
#[derive(Debug)]
pub struct DeadLetterQueueManager {
    config: DeadLetterQueueConfig,
    queue: Arc<RwLock<Vec<FailedMessage>>>,
}

impl DeadLetterQueueManager {
    /// Create a new dead letter queue manager
    pub fn new(config: DeadLetterQueueConfig) -> Self {
        Self {
            config,
            queue: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// Add failed message to dead letter queue
    pub async fn add_failed_message(&self, message: FailedMessage) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut queue = self.queue.write().await;

        // Check queue size limit
        if queue.len() >= self.config.max_queue_size {
            // Remove oldest message
            queue.remove(0);
        }

        queue.push(message);
        Ok(())
    }

    /// Get messages for reprocessing
    pub async fn get_messages_for_reprocessing(&self, batch_size: usize) -> Vec<FailedMessage> {
        let mut queue = self.queue.write().await;
        let mut messages = Vec::new();

        // Get messages that haven't exceeded max reprocessing attempts
        for message in queue.iter_mut() {
            if message.reprocessing_attempts < message.max_reprocessing_attempts {
                messages.push(message.clone());
                message.reprocessing_attempts += 1;

                if messages.len() >= batch_size {
                    break;
                }
            }
        }

        messages
    }

    /// Remove successfully reprocessed message
    pub async fn remove_message(&self, message_id: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut queue = self.queue.write().await;
        queue.retain(|msg| msg.id != message_id);
        Ok(())
    }

    /// Get queue statistics
    pub async fn get_queue_stats(&self) -> DeadLetterQueueStats {
        let queue = self.queue.read().await;

        let total_messages = queue.len();
        let reprocessable_messages = queue
            .iter()
            .filter(|msg| msg.reprocessing_attempts < msg.max_reprocessing_attempts)
            .count();

        let oldest_message = queue
            .iter()
            .min_by_key(|msg| msg.failed_at)
            .map(|msg| msg.failed_at);

        DeadLetterQueueStats {
            total_messages,
            reprocessable_messages,
            oldest_message,
        }
    }

    /// Clean up expired messages
    pub async fn cleanup_expired_messages(&self) {
        let mut queue = self.queue.write().await;
        let now = Utc::now();

        queue.retain(|msg| {
            let age = now.signed_duration_since(msg.failed_at).num_seconds() as u64;
            age < self.config.retention_seconds
        });
    }
}

/// Dead letter queue statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeadLetterQueueStats {
    pub total_messages: usize,
    pub reprocessable_messages: usize,
    pub oldest_message: Option<DateTime<Utc>>,
}

/// Timeout manager for handling timeouts
#[derive(Debug)]
pub struct TimeoutManager {
    config: TimeoutConfig,
}

impl TimeoutManager {
    /// Create a new timeout manager
    pub fn new(config: TimeoutConfig) -> Self {
        Self { config }
    }

    /// Execute operation with timeout
    pub async fn execute_with_timeout<F, Fut, T>(
        &self,
        operation: F,
        service_name: Option<&str>,
    ) -> Result<T, TimeoutError>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = T>,
    {
        let timeout_duration = self.get_timeout_duration(service_name);
        let result = tokio::time::timeout(timeout_duration, operation()).await;

        match result {
            Ok(value) => Ok(value),
            Err(_) => Err(TimeoutError::OperationTimedOut {
                service: service_name.map(|s| s.to_string()),
                timeout_seconds: timeout_duration.as_secs(),
            }),
        }
    }

    /// Get timeout duration for service
    fn get_timeout_duration(&self, service_name: Option<&str>) -> Duration {
        if let Some(service) = service_name {
            if let Some(&timeout) = self.config.service_timeouts.get(service) {
                return Duration::from_secs(timeout);
            }
        }

        Duration::from_secs(self.config.default_timeout_seconds)
    }
}

/// Recovery manager for handling recovery operations
#[derive(Debug)]
pub struct RecoveryManager {
    config: RecoveryConfig,
    operations: Arc<RwLock<HashMap<String, RecoveryOperation>>>,
}

impl RecoveryManager {
    /// Create a new recovery manager
    pub fn new(config: RecoveryConfig) -> Self {
        Self {
            config,
            operations: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Initiate data loss recovery
    pub async fn initiate_data_loss_recovery(&self, affected_services: Vec<String>) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        if !self.config.data_loss_recovery_enabled {
            return Err("Data loss recovery not enabled".into());
        }

        let operation_id = format!("data_recovery_{}", Utc::now().timestamp());

        let operation = RecoveryOperation {
            id: operation_id.clone(),
            operation_type: RecoveryOperationType::DataLossRecovery,
            status: RecoveryStatus::InProgress,
            started_at: Utc::now(),
            completed_at: None,
            error_message: None,
            affected_services,
        };

        {
            let mut operations = self.operations.write().await;
            operations.insert(operation_id.clone(), operation);
        }

        // Simulate recovery process
        tokio::time::sleep(Duration::from_secs(5)).await;

        {
            let mut operations = self.operations.write().await;
            if let Some(op) = operations.get_mut(&operation_id) {
                op.status = RecoveryStatus::Completed;
                op.completed_at = Some(Utc::now());
            }
        }

        Ok(operation_id)
    }

    /// Initiate state recovery
    pub async fn initiate_state_recovery(&self, affected_services: Vec<String>) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        if !self.config.state_recovery_enabled {
            return Err("State recovery not enabled".into());
        }

        let operation_id = format!("state_recovery_{}", Utc::now().timestamp());

        let operation = RecoveryOperation {
            id: operation_id.clone(),
            operation_type: RecoveryOperationType::StateRecovery,
            status: RecoveryStatus::InProgress,
            started_at: Utc::now(),
            completed_at: None,
            error_message: None,
            affected_services,
        };

        {
            let mut operations = self.operations.write().await;
            operations.insert(operation_id.clone(), operation);
        }

        // Simulate recovery process
        tokio::time::sleep(Duration::from_secs(3)).await;

        {
            let mut operations = self.operations.write().await;
            if let Some(op) = operations.get_mut(&operation_id) {
                op.status = RecoveryStatus::Completed;
                op.completed_at = Some(Utc::now());
            }
        }

        Ok(operation_id)
    }

    /// Get recovery operation status
    pub async fn get_recovery_status(&self, operation_id: &str) -> Option<RecoveryOperation> {
        let operations = self.operations.read().await;
        operations.get(operation_id).cloned()
    }

    /// Get all recovery operations
    pub async fn get_all_recovery_operations(&self) -> Vec<RecoveryOperation> {
        let operations = self.operations.read().await;
        operations.values().cloned().collect()
    }
}

/// Error types for retry operations
#[derive(Debug, thiserror::Error)]
pub enum RetryError<E> {
    #[error("Maximum retry attempts ({attempts}) exceeded")]
    MaxAttemptsExceeded { attempts: u32, last_error: E },
}

/// Error types for timeout operations
#[derive(Debug, thiserror::Error)]
pub enum TimeoutError {
    #[error("Operation timed out for service '{service:?}' after {timeout_seconds} seconds")]
    OperationTimedOut { service: Option<String>, timeout_seconds: u64 },
}

/// Main error recovery and resilience manager
#[derive(Debug)]
pub struct ErrorRecoveryManager {
    config: ErrorRecoveryConfig,
    retry_manager: RetryManager,
    dead_letter_queue_manager: DeadLetterQueueManager,
    timeout_manager: TimeoutManager,
    recovery_manager: RecoveryManager,
    metrics: Arc<RwLock<ErrorRecoveryMetrics>>,
}

impl ErrorRecoveryManager {
    /// Create a new error recovery manager
    pub fn new(config: ErrorRecoveryConfig) -> Self {
        let retry_manager = RetryManager::new(config.retry.clone());
        let dead_letter_queue_manager = DeadLetterQueueManager::new(config.dead_letter_queue.clone());
        let timeout_manager = TimeoutManager::new(config.timeout.clone());
        let recovery_manager = RecoveryManager::new(config.recovery.clone());

        let metrics = ErrorRecoveryMetrics {
            total_retry_attempts: 0,
            successful_retries: 0,
            failed_retries: 0,
            dead_letter_queue_size: 0,
            messages_reprocessed: 0,
            recovery_operations_total: 0,
            recovery_operations_successful: 0,
            average_retry_delay_ms: 0.0,
            timeout_events: 0,
        };

        Self {
            config,
            retry_manager,
            dead_letter_queue_manager,
            timeout_manager,
            recovery_manager,
            metrics: Arc::new(RwLock::new(metrics)),
        }
    }

    /// Execute operation with full error recovery
    pub async fn execute_with_recovery<F, Fut, T, E, F2>(
        &self,
        operation: F,
        should_retry: F2,
        service_name: &str,
        operation_name: &str,
    ) -> Result<T, ErrorRecoveryError<E>>
    where
        F: Fn() -> Fut,
        Fut: std::future::Future<Output = Result<T, E>>,
        F2: Fn(&E) -> bool,
        E: std::fmt::Debug + Clone,
    {
        // First try with timeout
        let timeout_result = self.timeout_manager
            .execute_with_timeout(
                || self.retry_manager.execute_with_retry(operation, should_retry),
                Some(service_name),
            )
            .await;

        match timeout_result {
            Ok(retry_result) => {
                match retry_result {
                    Ok(success) => {
                        // Update metrics
                        {
                            let mut metrics = self.metrics.write().await;
                            metrics.successful_retries += 1;
                        }
                        Ok(success)
                    }
                    Err(retry_error) => {
                        // Handle retry failure - add to dead letter queue
                        self.handle_retry_failure(
                            &retry_error,
                            service_name,
                            operation_name,
                        ).await;

                        Err(ErrorRecoveryError::RetryFailed(retry_error))
                    }
                }
            }
            Err(timeout_error) => {
                // Handle timeout
                {
                    let mut metrics = self.metrics.write().await;
                    metrics.timeout_events += 1;
                }

                Err(ErrorRecoveryError::Timeout(timeout_error))
            }
        }
    }

    /// Handle retry failure by adding to dead letter queue
    async fn handle_retry_failure<E>(
        &self,
        retry_error: &RetryError<E>,
        service_name: &str,
        operation_name: &str,
    ) where
        E: std::fmt::Debug + Clone,
    {
        if !self.config.dead_letter_queue.enabled {
            return;
        }

        let failed_message = FailedMessage {
            id: format!("failed_{}_{}", service_name, Utc::now().timestamp()),
            original_message: serde_json::Value::Null, // Would contain actual message
            error_message: format!("{:?}", retry_error),
            failed_at: Utc::now(),
            retry_attempts: Vec::new(), // Would be populated with actual attempts
            service_name: service_name.to_string(),
            operation: operation_name.to_string(),
            reprocessing_attempts: 0,
            max_reprocessing_attempts: self.config.dead_letter_queue.max_reprocessing_attempts,
        };

        let _ = self.dead_letter_queue_manager.add_failed_message(failed_message).await;

        // Update metrics
        {
            let mut metrics = self.metrics.write().await;
            metrics.failed_retries += 1;
        }
    }

    /// Initiate data loss recovery
    pub async fn initiate_data_loss_recovery(&self, affected_services: Vec<String>) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let operation_id = self.recovery_manager.initiate_data_loss_recovery(affected_services).await?;

        {
            let mut metrics = self.metrics.write().await;
            metrics.recovery_operations_total += 1;
        }

        Ok(operation_id)
    }

    /// Get error recovery metrics
    pub async fn get_metrics(&self) -> ErrorRecoveryMetrics {
        let mut metrics = self.metrics.read().await.clone();

        // Update queue size
        if let Ok(stats) = self.dead_letter_queue_manager.get_queue_stats().await {
            metrics.dead_letter_queue_size = stats.total_messages;
        }

        metrics
    }

    /// Clean up expired messages in dead letter queue
    pub async fn cleanup_dead_letter_queue(&self) {
        self.dead_letter_queue_manager.cleanup_expired_messages().await;
    }
}

/// Error types for error recovery operations
#[derive(Debug, thiserror::Error)]
pub enum ErrorRecoveryError<E> {
    #[error("Retry failed: {0}")]
    RetryFailed(RetryError<E>),
    #[error("Operation timed out: {0}")]
    Timeout(TimeoutError),
}

impl Default for ErrorRecoveryConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            retry: RetryConfig {
                enabled: true,
                max_attempts: 3,
                initial_delay_ms: 1000,
                max_delay_ms: 30000,
                backoff_multiplier: 2.0,
                jitter_enabled: true,
                retryable_errors: vec![
                    "connection refused".to_string(),
                    "timeout".to_string(),
                    "temporary failure".to_string(),
                ],
            },
            dead_letter_queue: DeadLetterQueueConfig {
                enabled: true,
                max_queue_size: 10000,
                retention_seconds: 604800, // 7 days
                batch_size: 100,
                reprocessing_enabled: true,
                max_reprocessing_attempts: 5,
            },
            timeout: TimeoutConfig {
                enabled: true,
                default_timeout_seconds: 30,
                service_timeouts: HashMap::new(),
                circuit_breaker_timeout_seconds: 60,
            },
            recovery: RecoveryConfig {
                enabled: true,
                data_loss_recovery_enabled: true,
                state_recovery_enabled: true,
                automatic_recovery_enabled: true,
                recovery_timeout_seconds: 300,
            },
        }
    }
}