//! # Log Aggregation and Correlation Module
//!
//! This module provides comprehensive log aggregation, correlation, and analysis capabilities
//! that integrate with the existing OpenTelemetry tracing and metrics infrastructure.

use crate::observability::{ObservabilityManager, tracing::AdvancedTracing, log_export::{LogExportManager, LogExporter}};
use opentelemetry::logs::{Log<PERSON><PERSON><PERSON>, Logger, Logger<PERSON>ider, Severity};
use opentelemetry::{KeyValue, Value};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{error, info, warn, Level};
use uuid::Uuid;

/// Configuration for log aggregation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogAggregationConfig {
    /// Enable log aggregation
    pub enabled: bool,
    /// Maximum buffer size for log entries
    pub max_buffer_size: usize,
    /// Batch size for processing logs
    pub batch_size: usize,
    /// Flush interval in seconds
    pub flush_interval_seconds: u64,
    /// Maximum memory usage in MB
    pub max_memory_mb: usize,
    /// Log retention period in hours
    pub retention_hours: u64,
    /// Enable correlation with traces
    pub correlate_with_traces: bool,
    /// Enable correlation with metrics
    pub correlate_with_metrics: bool,
    /// Enable anomaly detection
    pub enable_anomaly_detection: bool,
}

/// Structured log entry with correlation data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CorrelatedLogEntry {
    /// Unique log entry ID
    pub id: String,
    /// Timestamp in Unix milliseconds
    pub timestamp: u64,
    /// Log level
    pub level: String,
    /// Log message
    pub message: String,
    /// Component that generated the log
    pub component: String,
    /// Trace ID for correlation
    pub trace_id: Option<String>,
    /// Span ID for correlation
    pub span_id: Option<String>,
    /// Request ID for grouping related logs
    pub request_id: Option<String>,
    /// User ID for user-specific logs
    pub user_id: Option<String>,
    /// Session ID
    pub session_id: Option<String>,
    /// Error type if this is an error log
    pub error_type: Option<String>,
    /// Error code
    pub error_code: Option<String>,
    /// Business context (key-value pairs)
    pub business_context: HashMap<String, String>,
    /// Performance metrics context
    pub performance_context: Option<PerformanceContext>,
    /// Security context
    pub security_context: Option<SecurityContext>,
    /// Additional metadata
    pub metadata: HashMap<String, Value>,
}

/// Performance context for logs
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceContext {
    /// Operation duration in milliseconds
    pub duration_ms: Option<f64>,
    /// Memory usage in bytes
    pub memory_usage_bytes: Option<u64>,
    /// CPU usage percentage
    pub cpu_usage_percent: Option<f64>,
    /// Queue depth
    pub queue_depth: Option<u64>,
    /// Database connection count
    pub db_connections: Option<u32>,
}

/// Security context for logs
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityContext {
    /// Event type (authentication, authorization, etc.)
    pub event_type: String,
    /// User role
    pub user_role: Option<String>,
    /// IP address
    pub ip_address: Option<String>,
    /// User agent
    pub user_agent: Option<String>,
    /// Security level
    pub security_level: String,
}

/// Log aggregation manager
pub struct LogAggregationManager {
    config: LogAggregationConfig,
    observability_manager: Arc<ObservabilityManager>,
    advanced_tracing: Arc<AdvancedTracing>,
    log_buffer: Arc<RwLock<VecDeque<CorrelatedLogEntry>>>,
    processed_logs: Arc<RwLock<HashMap<String, CorrelatedLogEntry>>>,
    error_aggregator: Arc<RwLock<ErrorAggregator>>,
    anomaly_detector: Option<Arc<RwLock<AnomalyDetector>>>,
    export_manager: Option<Arc<RwLock<LogExportManager>>>,
}

/// Error aggregation for deduplication and analysis
pub struct ErrorAggregator {
    error_counts: HashMap<String, u64>,
    error_timestamps: HashMap<String, Vec<u64>>,
    error_contexts: HashMap<String, Vec<CorrelatedLogEntry>>,
}

/// Anomaly detection for log patterns
pub struct AnomalyDetector {
    normal_patterns: HashMap<String, PatternStats>,
    anomaly_threshold: f64,
    learning_period_hours: u64,
}

/// Statistics for log patterns
#[derive(Debug, Clone)]
pub struct PatternStats {
    pub count: u64,
    pub avg_frequency_per_hour: f64,
    pub last_seen: u64,
    pub severity_distribution: HashMap<String, u64>,
}

impl LogAggregationManager {
    /// Create a new log aggregation manager
    pub fn new(
        config: LogAggregationConfig,
        observability_manager: Arc<ObservabilityManager>,
    ) -> Self {
        let advanced_tracing = Arc::new(AdvancedTracing::new(observability_manager.clone()));

        let anomaly_detector = if config.enable_anomaly_detection {
            Some(Arc::new(RwLock::new(AnomalyDetector::new())))
        } else {
            None
        };

        Self {
            config: config.clone(),
            observability_manager,
            advanced_tracing,
            log_buffer: Arc::new(RwLock::new(VecDeque::with_capacity(config.max_buffer_size))),
            processed_logs: Arc::new(RwLock::new(HashMap::new())),
            error_aggregator: Arc::new(RwLock::new(ErrorAggregator::new())),
            anomaly_detector,
            export_manager: None,
        }
    }

    /// Log an entry with correlation data
    pub async fn log_entry(&self, mut entry: CorrelatedLogEntry) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Enrich with trace context if enabled
        if self.config.correlate_with_traces {
            entry.trace_id = self.advanced_tracing.get_current_trace_id();
            entry.span_id = self.advanced_tracing.get_current_span_id();
        }

        // Enrich with performance context if enabled
        if self.config.correlate_with_metrics {
            entry.performance_context = Some(self.collect_performance_context().await);
        }

        // Generate ID if not provided
        if entry.id.is_empty() {
            entry.id = Uuid::new_v4().to_string();
        }

        // Set timestamp if not provided
        if entry.timestamp == 0 {
            entry.timestamp = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)?
                .as_millis() as u64;
        }

        // Add to buffer
        {
            let mut buffer = self.log_buffer.write().await;
            buffer.push_back(entry.clone());

            // Check buffer size limit
            if buffer.len() > self.config.max_buffer_size {
                buffer.pop_front(); // Remove oldest entry
            }
        }

        // Process entry for error aggregation
        if entry.level == "ERROR" || entry.level == "WARN" {
            self.process_error_entry(entry.clone()).await;
        }

        // Process for anomaly detection
        if let Some(detector) = &self.anomaly_detector {
            detector.write().await.analyze_entry(&entry).await;
        }

        // Store processed log
        {
            let mut processed = self.processed_logs.write().await;
            processed.insert(entry.id.clone(), entry.clone());
        }

        // Send to export manager if available
        if let Some(export_manager) = &self.export_manager {
            let manager = export_manager.read().await;
            manager.queue_entry(entry).await;
        }

        Ok(())
    }

    /// Collect performance context from metrics
    async fn collect_performance_context(&self) -> PerformanceContext {
        // This would integrate with the existing metrics system
        // For now, return placeholder values
        PerformanceContext {
            duration_ms: None,
            memory_usage_bytes: None,
            cpu_usage_percent: None,
            queue_depth: None,
            db_connections: None,
        }
    }

    /// Process error entry for aggregation
    async fn process_error_entry(&self, entry: CorrelatedLogEntry) {
        let mut aggregator = self.error_aggregator.write().await;
        aggregator.add_error(entry);
    }

    /// Get aggregated error statistics
    pub async fn get_error_statistics(&self) -> HashMap<String, ErrorStats> {
        let aggregator = self.error_aggregator.read().await;
        aggregator.get_statistics()
    }

    /// Get anomaly detection results
    pub async fn get_anomaly_alerts(&self) -> Vec<AnomalyAlert> {
        if let Some(detector) = &self.anomaly_detector {
            detector.read().await.get_alerts()
        } else {
            Vec::new()
        }
    }

    /// Search logs with filters
    pub async fn search_logs(&self, filters: LogSearchFilters) -> Vec<CorrelatedLogEntry> {
        let processed = self.processed_logs.read().await;
        processed
            .values()
            .filter(|entry| filters.matches(entry))
            .cloned()
            .collect()
    }

    /// Get logs correlated with a specific trace
    pub async fn get_logs_by_trace(&self, trace_id: &str) -> Vec<CorrelatedLogEntry> {
        let processed = self.processed_logs.read().await;
        processed
            .values()
            .filter(|entry| entry.trace_id.as_ref() == Some(&trace_id.to_string()))
            .cloned()
            .collect()
    }

    /// Get logs for a specific request
    pub async fn get_logs_by_request(&self, request_id: &str) -> Vec<CorrelatedLogEntry> {
        let processed = self.processed_logs.read().await;
        processed
            .values()
            .filter(|entry| entry.request_id.as_ref() == Some(&request_id.to_string()))
            .cloned()
            .collect()
    }

    /// Flush buffered logs (for batch processing)
    pub async fn flush_buffer(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut buffer = self.log_buffer.write().await;
        if buffer.is_empty() {
            return Ok(());
        }

        // Process batch
        let batch: Vec<_> = buffer.drain(..).collect();
        info!("Flushed {} log entries from buffer", batch.len());

        // Send to export manager if available
        if let Some(export_manager) = &self.export_manager {
            let manager = export_manager.read().await;
            if let Err(e) = manager.flush().await {
                tracing::error!("Failed to flush logs to export manager: {}", e);
            }
        }

        Ok(())
    }

    /// Get buffer statistics
    pub async fn get_buffer_stats(&self) -> BufferStats {
        let buffer = self.log_buffer.read().await;
        BufferStats {
            current_size: buffer.len(),
            max_size: self.config.max_buffer_size,
            utilization_percent: (buffer.len() as f64 / self.config.max_buffer_size as f64) * 100.0,
        }
    }

    /// Set the export manager
    pub fn set_export_manager(&mut self, export_manager: Arc<RwLock<LogExportManager>>) {
        self.export_manager = Some(export_manager);
    }

    /// Get export manager health
    pub async fn get_export_health(&self) -> Option<std::collections::HashMap<String, Result<(), String>>> {
        if let Some(export_manager) = &self.export_manager {
            let manager = export_manager.read().await;
            Some(manager.health_check().await)
        } else {
            None
        }
    }
}

/// Error statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorStats {
    pub count: u64,
    pub first_seen: u64,
    pub last_seen: u64,
    pub frequency_per_hour: f64,
    pub affected_components: Vec<String>,
}

/// Anomaly alert
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnomalyAlert {
    pub pattern: String,
    pub severity: String,
    pub confidence: f64,
    pub timestamp: u64,
    pub description: String,
}

/// Log search filters
#[derive(Debug, Clone, Default)]
pub struct LogSearchFilters {
    pub level: Option<String>,
    pub component: Option<String>,
    pub trace_id: Option<String>,
    pub request_id: Option<String>,
    pub user_id: Option<String>,
    pub error_type: Option<String>,
    pub time_range: Option<TimeRange>,
    pub message_contains: Option<String>,
}

/// Time range for filtering
#[derive(Debug, Clone)]
pub struct TimeRange {
    pub start: u64,
    pub end: u64,
}

/// Buffer statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BufferStats {
    pub current_size: usize,
    pub max_size: usize,
    pub utilization_percent: f64,
}

impl LogSearchFilters {
    /// Check if a log entry matches the filters
    pub fn matches(&self, entry: &CorrelatedLogEntry) -> bool {
        if let Some(ref level) = self.level {
            if entry.level != *level {
                return false;
            }
        }

        if let Some(ref component) = self.component {
            if entry.component != *component {
                return false;
            }
        }

        if let Some(ref trace_id) = self.trace_id {
            if entry.trace_id.as_ref() != Some(trace_id) {
                return false;
            }
        }

        if let Some(ref request_id) = self.request_id {
            if entry.request_id.as_ref() != Some(request_id) {
                return false;
            }
        }

        if let Some(ref user_id) = self.user_id {
            if entry.user_id.as_ref() != Some(user_id) {
                return false;
            }
        }

        if let Some(ref error_type) = self.error_type {
            if entry.error_type.as_ref() != Some(error_type) {
                return false;
            }
        }

        if let Some(ref time_range) = self.time_range {
            if entry.timestamp < time_range.start || entry.timestamp > time_range.end {
                return false;
            }
        }

        if let Some(ref contains) = self.message_contains {
            if !entry.message.to_lowercase().contains(&contains.to_lowercase()) {
                return false;
            }
        }

        true
    }
}

impl ErrorAggregator {
    /// Create a new error aggregator
    pub fn new() -> Self {
        Self {
            error_counts: HashMap::new(),
            error_timestamps: HashMap::new(),
            error_contexts: HashMap::new(),
        }
    }

    /// Add an error entry
    pub fn add_error(&mut self, entry: CorrelatedLogEntry) {
        let error_key = format!("{}:{}", entry.component, entry.error_type.as_deref().unwrap_or("unknown"));
        let timestamp = entry.timestamp;

        *self.error_counts.entry(error_key.clone()).or_insert(0) += 1;
        self.error_timestamps.entry(error_key.clone()).or_insert_with(Vec::new).push(timestamp);
        self.error_contexts.entry(error_key).or_insert_with(Vec::new).push(entry);
    }

    /// Get error statistics
    pub fn get_statistics(&self) -> HashMap<String, ErrorStats> {
        let mut stats = HashMap::new();
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;

        for (error_key, count) in &self.error_counts {
            let timestamps = self.error_timestamps.get(error_key).unwrap();
            let first_seen = *timestamps.iter().min().unwrap_or(&current_time);
            let last_seen = *timestamps.iter().max().unwrap_or(&current_time);

            let time_span_hours = (last_seen.saturating_sub(first_seen)) as f64 / (1000.0 * 60.0 * 60.0);
            let frequency_per_hour = if time_span_hours > 0.0 {
                *count as f64 / time_span_hours
            } else {
                *count as f64
            };

            let affected_components = self.error_contexts.get(error_key)
                .unwrap()
                .iter()
                .map(|entry| entry.component.clone())
                .collect::<std::collections::HashSet<_>>()
                .into_iter()
                .collect();

            stats.insert(error_key.clone(), ErrorStats {
                count: *count,
                first_seen,
                last_seen,
                frequency_per_hour,
                affected_components,
            });
        }

        stats
    }
}

impl AnomalyDetector {
    /// Create a new anomaly detector
    pub fn new() -> Self {
        Self {
            normal_patterns: HashMap::new(),
            anomaly_threshold: 2.0, // 2 standard deviations
            learning_period_hours: 24,
        }
    }

    /// Analyze a log entry for anomalies
    pub async fn analyze_entry(&mut self, entry: &CorrelatedLogEntry) {
        let pattern_key = format!("{}:{}", entry.component, entry.level);
        let current_time = entry.timestamp;

        let stats = self.normal_patterns.entry(pattern_key.clone()).or_insert_with(|| PatternStats {
            count: 0,
            avg_frequency_per_hour: 0.0,
            last_seen: current_time,
            severity_distribution: HashMap::new(),
        });

        stats.count += 1;
        *stats.severity_distribution.entry(entry.level.clone()).or_insert(0) += 1;

        // Update frequency calculation
        let time_diff_hours = (current_time - stats.last_seen) as f64 / (1000.0 * 60.0 * 60.0);
        if time_diff_hours > 0.0 {
            stats.avg_frequency_per_hour = stats.count as f64 / time_diff_hours.max(1.0);
        }

        stats.last_seen = current_time;
    }

    /// Get anomaly alerts
    pub fn get_alerts(&self) -> Vec<AnomalyAlert> {
        // Simplified anomaly detection - in production, this would use statistical methods
        Vec::new()
    }
}

impl Default for LogAggregationConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            max_buffer_size: 10000,
            batch_size: 100,
            flush_interval_seconds: 30,
            max_memory_mb: 100,
            retention_hours: 168, // 7 days
            correlate_with_traces: true,
            correlate_with_metrics: true,
            enable_anomaly_detection: true,
        }
    }
}