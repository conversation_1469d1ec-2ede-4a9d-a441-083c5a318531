//! # Log Export Module
//!
//! This module provides log export capabilities to various backends
//! including Elasticsearch, file systems, and external logging services.

use crate::observability::log_aggregation::CorrelatedLogEntry;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

/// Configuration for log export
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogExportConfig {
    /// Enable log export
    pub enabled: bool,
    /// Export batch size
    pub batch_size: usize,
    /// Export interval in seconds
    pub export_interval_seconds: u64,
    /// Maximum retry attempts
    pub max_retry_attempts: u32,
    /// Retry delay in milliseconds
    pub retry_delay_ms: u64,
    /// Export backends configuration
    pub backends: ExportBackends,
}

/// Export backends configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ExportBackends {
    /// Elasticsearch configuration
    pub elasticsearch: Option<ElasticsearchConfig>,
    /// File system configuration
    pub file: Option<FileConfig>,
    /// Console configuration
    pub console: Option<ConsoleConfig>,
    /// Loki configuration
    pub loki: Option<LokiConfig>,
}

/// Elasticsearch export configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ElasticsearchConfig {
    /// Elasticsearch endpoints
    pub endpoints: Vec<String>,
    /// Index name pattern
    pub index_pattern: String,
    /// Authentication username
    pub username: Option<String>,
    /// Authentication password
    pub password: Option<String>,
    /// API key
    pub api_key: Option<String>,
    /// Bulk request size
    pub bulk_size: usize,
    /// Compression enabled
    pub compression: bool,
}

/// File system export configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileConfig {
    /// Log directory path
    pub directory: String,
    /// File name pattern
    pub filename_pattern: String,
    /// Maximum file size in MB
    pub max_file_size_mb: usize,
    /// Maximum number of files to keep
    pub max_files: usize,
    /// Compression enabled
    pub compression: bool,
}

/// Console export configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConsoleConfig {
    /// Enable pretty printing
    pub pretty: bool,
    /// Include timestamps
    pub timestamps: bool,
    /// Color output
    pub colors: bool,
}

/// Loki export configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LokiConfig {
    /// Loki endpoint
    pub endpoint: String,
    /// Tenant ID
    pub tenant_id: Option<String>,
    /// Labels to add to all log entries
    pub labels: HashMap<String, String>,
    /// Batch size for pushing logs
    pub batch_size: usize,
}

/// Log export manager
pub struct LogExportManager {
    config: LogExportConfig,
    exporters: Vec<Box<dyn LogExporter + Send + Sync>>,
    export_queue: Arc<RwLock<Vec<CorrelatedLogEntry>>>,
    is_running: Arc<RwLock<bool>>,
}

#[async_trait::async_trait]
pub trait LogExporter: Send + Sync {
    /// Export a batch of log entries
    async fn export_batch(&self, entries: &[CorrelatedLogEntry]) -> Result<(), Box<dyn std::error::Error + Send + Sync>>;

    /// Get the name of this exporter
    fn name(&self) -> &str;

    /// Check if the exporter is healthy
    async fn health_check(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>>;
}

/// Elasticsearch exporter
pub struct ElasticsearchExporter {
    config: ElasticsearchConfig,
    client: reqwest::Client,
}

impl ElasticsearchExporter {
    pub fn new(config: ElasticsearchConfig) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        let mut client_builder = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(30));

        // Add authentication if configured
        if let (Some(username), Some(password)) = (&config.username, &config.password) {
            client_builder = client_builder.default_headers({
                let mut headers = reqwest::header::HeaderMap::new();
                let auth = format!("{}:{}", username, password);
                let encoded = base64::encode(auth);
                headers.insert(
                    reqwest::header::AUTHORIZATION,
                    format!("Basic {}", encoded).parse().unwrap(),
                );
                headers
            });
        }

        if let Some(api_key) = &config.api_key {
            client_builder = client_builder.default_headers({
                let mut headers = reqwest::header::HeaderMap::new();
                headers.insert(
                    reqwest::header::AUTHORIZATION,
                    format!("ApiKey {}", api_key).parse().unwrap(),
                );
                headers
            });
        }

        let client = client_builder.build()?;

        Ok(Self { config, client })
    }
}

#[async_trait::async_trait]
impl LogExporter for ElasticsearchExporter {
    async fn export_batch(&self, entries: &[CorrelatedLogEntry]) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if entries.is_empty() {
            return Ok(());
        }

        // Prepare bulk request body
        let mut bulk_body = String::new();

        for entry in entries {
            // Create index action
            let index_name = self.get_index_name(entry.timestamp);
            let action = serde_json::json!({
                "index": {
                    "_index": index_name,
                    "_id": entry.id
                }
            });
            bulk_body.push_str(&serde_json::to_string(&action)?);
            bulk_body.push('\n');

            // Add document
            let doc = self.convert_to_elasticsearch_doc(entry);
            bulk_body.push_str(&serde_json::to_string(&doc)?);
            bulk_body.push('\n');
        }

        // Send bulk request to Elasticsearch
        for endpoint in &self.config.endpoints {
            let url = format!("{}/_bulk", endpoint.trim_end_matches('/'));
            let response = self.client
                .post(&url)
                .header(reqwest::header::CONTENT_TYPE, "application/x-ndjson")
                .body(bulk_body.clone())
                .send()
                .await?;

            if response.status().is_success() {
                let response_body: serde_json::Value = response.json().await?;
                if let Some(errors) = response_body.get("errors").and_then(|e| e.as_bool()) {
                    if errors {
                        tracing::warn!("Elasticsearch bulk request had errors: {:?}", response_body);
                    }
                }
                return Ok(());
            } else {
                tracing::warn!("Elasticsearch request failed with status: {}", response.status());
            }
        }

        Err("All Elasticsearch endpoints failed".into())
    }

    fn name(&self) -> &str {
        "elasticsearch"
    }

    async fn health_check(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        for endpoint in &self.config.endpoints {
            let url = format!("{}/_cluster/health", endpoint.trim_end_matches('/'));
            let response = self.client.get(&url).send().await?;
            if response.status().is_success() {
                return Ok(());
            }
        }
        Err("All Elasticsearch endpoints are unhealthy".into())
    }
}

impl ElasticsearchExporter {
    fn get_index_name(&self, timestamp: u64) -> String {
        let datetime = chrono::DateTime::from_timestamp_millis(timestamp as i64)
            .unwrap_or_else(|| chrono::Utc::now());
        let date_str = datetime.format("%Y-%m-%d").to_string();
        self.config.index_pattern.replace("%{date}", &date_str)
    }

    fn convert_to_elasticsearch_doc(&self, entry: &CorrelatedLogEntry) -> serde_json::Value {
        let mut doc = serde_json::json!({
            "@timestamp": entry.timestamp,
            "level": entry.level,
            "message": entry.message,
            "component": entry.component,
            "id": entry.id
        });

        if let Some(ref trace_id) = entry.trace_id {
            doc["trace_id"] = serde_json::Value::String(trace_id.clone());
        }
        if let Some(ref span_id) = entry.span_id {
            doc["span_id"] = serde_json::Value::String(span_id.clone());
        }
        if let Some(ref request_id) = entry.request_id {
            doc["request_id"] = serde_json::Value::String(request_id.clone());
        }
        if let Some(ref user_id) = entry.user_id {
            doc["user_id"] = serde_json::Value::String(user_id.clone());
        }
        if let Some(ref error_type) = entry.error_type {
            doc["error_type"] = serde_json::Value::String(error_type.clone());
        }
        if let Some(ref error_code) = entry.error_code {
            doc["error_code"] = serde_json::Value::String(error_code.clone());
        }

        // Add business context
        if !entry.business_context.is_empty() {
            doc["business_context"] = serde_json::to_value(&entry.business_context).unwrap_or_default();
        }

        // Add performance context
        if let Some(ref perf) = entry.performance_context {
            let mut perf_obj = serde_json::json!({});
            if let Some(duration) = perf.duration_ms {
                perf_obj["duration_ms"] = serde_json::json!(duration);
            }
            if let Some(memory) = perf.memory_usage_bytes {
                perf_obj["memory_usage_bytes"] = serde_json::json!(memory);
            }
            if let Some(cpu) = perf.cpu_usage_percent {
                perf_obj["cpu_usage_percent"] = serde_json::json!(cpu);
            }
            doc["performance"] = perf_obj;
        }

        // Add security context
        if let Some(ref sec) = entry.security_context {
            doc["security"] = serde_json::to_value(sec).unwrap_or_default();
        }

        // Add metadata
        if !entry.metadata.is_empty() {
            doc["metadata"] = serde_json::to_value(&entry.metadata).unwrap_or_default();
        }

        doc
    }
}

/// File system exporter
pub struct FileExporter {
    config: FileConfig,
    current_file: Arc<RwLock<Option<std::fs::File>>>,
    current_file_size: Arc<RwLock<u64>>,
}

impl FileExporter {
    pub fn new(config: FileConfig) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        // Create directory if it doesn't exist
        std::fs::create_dir_all(&config.directory)?;

        Ok(Self {
            config,
            current_file: Arc::new(RwLock::new(None)),
            current_file_size: Arc::new(RwLock::new(0)),
        })
    }

    async fn get_current_file(&self) -> Result<std::fs::File, Box<dyn std::error::Error + Send + Sync>> {
        let mut current_file = self.current_file.write().await;
        let mut current_size = self.current_file_size.write().await;

        // Check if we need to rotate the file
        if current_file.is_none() || *current_size >= (self.config.max_file_size_mb as u64 * 1024 * 1024) {
            // Rotate file if it exists
            if current_file.is_some() {
                drop(current_file);
                self.rotate_file().await?;
                *current_file = self.current_file.write().await;
            }

            // Create new file
            let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
            let filename = self.config.filename_pattern.replace("%{timestamp}", &timestamp.to_string());
            let filepath = std::path::Path::new(&self.config.directory).join(filename);

            let file = std::fs::OpenOptions::new()
                .create(true)
                .append(true)
                .open(&filepath)?;

            *current_file = Some(file);
            *current_size = 0;
        }

        Ok(current_file.as_ref().unwrap().try_clone()?)
    }

    async fn rotate_file(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Get list of existing log files
        let mut files = Vec::new();
        let dir = std::fs::read_dir(&self.config.directory)?;
        for entry in dir {
            let entry = entry?;
            let path = entry.path();
            if path.is_file() && path.extension().and_then(|s| s.to_str()) == Some("log") {
                files.push((entry.metadata()?.modified()?, path));
            }
        }

        // Sort by modification time (newest first)
        files.sort_by(|a, b| b.0.cmp(&a.0));

        // Remove old files if we exceed the limit
        while files.len() >= self.config.max_files {
            if let Some((_, path)) = files.pop() {
                std::fs::remove_file(&path)?;
            }
        }

        Ok(())
    }
}

#[async_trait::async_trait]
impl LogExporter for FileExporter {
    async fn export_batch(&self, entries: &[CorrelatedLogEntry]) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut file = self.get_current_file().await?;
        let mut current_size = self.current_file_size.write().await;

        for entry in entries {
            let log_line = self.format_log_entry(entry);
            let bytes = log_line.as_bytes();

            file.write_all(bytes)?;
            file.write_all(b"\n")?;

            *current_size += bytes.len() as u64 + 1; // +1 for newline
        }

        file.flush()?;
        Ok(())
    }

    fn name(&self) -> &str {
        "file"
    }

    async fn health_check(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Check if directory is writable
        let test_file = std::path::Path::new(&self.config.directory).join("health_check.tmp");
        std::fs::write(&test_file, b"health check")?;
        std::fs::remove_file(&test_file)?;
        Ok(())
    }
}

impl FileExporter {
    fn format_log_entry(&self, entry: &CorrelatedLogEntry) -> String {
        let timestamp = chrono::DateTime::from_timestamp_millis(entry.timestamp as i64)
            .unwrap_or_else(|| chrono::Utc::now())
            .to_rfc3339();

        let mut parts = vec![
            timestamp,
            entry.level.clone(),
            entry.component.clone(),
            entry.message.clone(),
        ];

        if let Some(ref trace_id) = entry.trace_id {
            parts.push(format!("trace_id={}", trace_id));
        }
        if let Some(ref request_id) = entry.request_id {
            parts.push(format!("request_id={}", request_id));
        }

        parts.join(" | ")
    }
}

/// Console exporter
pub struct ConsoleExporter {
    config: ConsoleConfig,
}

impl ConsoleExporter {
    pub fn new(config: ConsoleConfig) -> Self {
        Self { config }
    }
}

#[async_trait::async_trait]
impl LogExporter for ConsoleExporter {
    async fn export_batch(&self, entries: &[CorrelatedLogEntry]) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        for entry in entries {
            let output = if self.config.pretty {
                self.format_pretty(entry)
            } else {
                self.format_compact(entry)
            };

            if self.config.colors {
                println!("{}", self.colorize_output(&output, &entry.level));
            } else {
                println!("{}", output);
            }
        }
        Ok(())
    }

    fn name(&self) -> &str {
        "console"
    }

    async fn health_check(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        Ok(())
    }
}

impl ConsoleExporter {
    fn format_pretty(&self, entry: &CorrelatedLogEntry) -> String {
        let timestamp = if self.config.timestamps {
            let dt = chrono::DateTime::from_timestamp_millis(entry.timestamp as i64)
                .unwrap_or_else(|| chrono::Utc::now());
            format!("[{}] ", dt.format("%Y-%m-%d %H:%M:%S%.3f"))
        } else {
            String::new()
        };

        format!("{}{} [{}] {}", timestamp, entry.level, entry.component, entry.message)
    }

    fn format_compact(&self, entry: &CorrelatedLogEntry) -> String {
        format!("{} [{}] {}", entry.level, entry.component, entry.message)
    }

    fn colorize_output(&self, output: &str, level: &str) -> String {
        match level {
            "ERROR" => format!("\x1b[31m{}\x1b[0m", output), // Red
            "WARN" => format!("\x1b[33m{}\x1b[0m", output),  // Yellow
            "INFO" => format!("\x1b[32m{}\x1b[0m", output),  // Green
            "DEBUG" => format!("\x1b[34m{}\x1b[0m", output), // Blue
            "TRACE" => format!("\x1b[35m{}\x1b[0m", output), // Magenta
            _ => output.to_string(),
        }
    }
}

impl LogExportManager {
    pub fn new(config: LogExportConfig) -> Self {
        Self {
            config,
            exporters: Vec::new(),
            export_queue: Arc::new(RwLock::new(Vec::new())),
            is_running: Arc::new(RwLock::new(false)),
        }
    }

    pub async fn add_exporter(&mut self, exporter: Box<dyn LogExporter + Send + Sync>) {
        self.exporters.push(exporter);
    }

    pub async fn initialize_exporters(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Initialize Elasticsearch exporter
        if let Some(es_config) = &self.config.backends.elasticsearch {
            let exporter = ElasticsearchExporter::new(es_config.clone())?;
            self.add_exporter(Box::new(exporter)).await;
        }

        // Initialize file exporter
        if let Some(file_config) = &self.config.backends.file {
            let exporter = FileExporter::new(file_config.clone())?;
            self.add_exporter(Box::new(exporter)).await;
        }

        // Initialize console exporter
        if let Some(console_config) = &self.config.backends.console {
            let exporter = ConsoleExporter::new(console_config.clone());
            self.add_exporter(Box::new(exporter)).await;
        }

        Ok(())
    }

    pub async fn queue_entry(&self, entry: CorrelatedLogEntry) {
        let mut queue = self.export_queue.write().await;
        queue.push(entry);

        // Check if we should flush
        if queue.len() >= self.config.batch_size {
            let batch = queue.drain(..).collect::<Vec<_>>();
            drop(queue);
            self.flush_batch(batch).await;
        }
    }

    pub async fn flush(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut queue = self.export_queue.write().await;
        if queue.is_empty() {
            return Ok(());
        }

        let batch = queue.drain(..).collect::<Vec<_>>();
        drop(queue);
        self.flush_batch(batch).await;
        Ok(())
    }

    async fn flush_batch(&self, batch: Vec<CorrelatedLogEntry>) {
        for exporter in &self.exporters {
            if let Err(e) = exporter.export_batch(&batch).await {
                tracing::error!("Failed to export logs to {}: {}", exporter.name(), e);
            }
        }
    }

    pub async fn start_periodic_flush(&self) {
        let mut is_running = self.is_running.write().await;
        if *is_running {
            return;
        }
        *is_running = true;

        let export_queue = Arc::clone(&self.export_queue);
        let exporters = self.exporters.clone();
        let batch_size = self.config.batch_size;
        let interval = std::time::Duration::from_secs(self.config.export_interval_seconds);

        tokio::spawn(async move {
            let mut interval_timer = tokio::time::interval(interval);

            loop {
                interval_timer.tick().await;

                let batch = {
                    let mut queue = export_queue.write().await;
                    if queue.len() >= batch_size {
                        queue.drain(..).collect::<Vec<_>>()
                    } else {
                        continue;
                    }
                };

                for exporter in &exporters {
                    if let Err(e) = exporter.export_batch(&batch).await {
                        tracing::error!("Failed to export logs to {}: {}", exporter.name(), e);
                    }
                }
            }
        });
    }

    pub async fn health_check(&self) -> HashMap<String, Result<(), String>> {
        let mut results = HashMap::new();

        for exporter in &self.exporters {
            let result = exporter.health_check().await
                .map_err(|e| e.to_string());
            results.insert(exporter.name().to_string(), result);
        }

        results
    }
}

impl Default for LogExportConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            batch_size: 100,
            export_interval_seconds: 30,
            max_retry_attempts: 3,
            retry_delay_ms: 1000,
            backends: ExportBackends {
                elasticsearch: None,
                file: Some(FileConfig {
                    directory: "/var/log/infinitium-signal".to_string(),
                    filename_pattern: "infinitum-signal-%{timestamp}.log".to_string(),
                    max_file_size_mb: 100,
                    max_files: 10,
                    compression: false,
                }),
                console: Some(ConsoleConfig {
                    pretty: true,
                    timestamps: true,
                    colors: true,
                }),
                loki: None,
            },
        }
    }
}