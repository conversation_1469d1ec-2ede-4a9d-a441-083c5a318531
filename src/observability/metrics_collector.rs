//! # Metrics Collection Enhancer
//!
//! This module provides enhanced metrics collection capabilities including
//! buffering for high-throughput scenarios, export to multiple formats,
//! and advanced aggregation features.

use crate::observability::{ObservabilityManager, custom_metrics::*};
use std::sync::Arc;
use tokio::sync::RwLock;
use std::collections::HashMap;
use std::time::{Duration, Instant, SystemTime};
use tokio::time;

/// Metrics collection enhancer
pub struct MetricsCollectionEnhancer {
    observability: Arc<ObservabilityManager>,
    buffer: Arc<RwLock<MetricsBuffer>>,
    exporters: HashMap<ExportFormat, Box<dyn MetricsExporter>>,
    aggregation_config: MetricsAggregationConfig,
    last_aggregation: RwLock<Instant>,
}

impl MetricsCollectionEnhancer {
    /// Create a new metrics collection enhancer
    pub fn new(observability: Arc<ObservabilityManager>) -> Self {
        let buffer = Arc::new(RwLock::new(MetricsBuffer::new(1000, Duration::from_secs(60))));
        let mut exporters = HashMap::new();

        // Initialize exporters
        exporters.insert(ExportFormat::Prometheus, Box::new(PrometheusExporter::new()) as Box<dyn MetricsExporter>);
        exporters.insert(ExportFormat::JSON, Box::new(JsonExporter::new()) as Box<dyn MetricsExporter>);
        exporters.insert(ExportFormat::CSV, Box::new(CsvExporter::new()) as Box<dyn MetricsExporter>);

        Self {
            observability,
            buffer,
            exporters,
            aggregation_config: MetricsAggregationConfig::default(),
            last_aggregation: RwLock::new(Instant::now()),
        }
    }

    /// Start the metrics collection enhancement
    pub async fn start(&self) {
        let buffer_clone = Arc::clone(&self.buffer);
        let aggregation_interval = self.aggregation_config.aggregation_interval_seconds;

        // Start buffer flush task
        tokio::spawn(async move {
            let mut interval = time::interval(Duration::from_secs(aggregation_interval));
            loop {
                interval.tick().await;
                let mut buffer = buffer_clone.write().await;
                buffer.flush();
            }
        });
    }

    /// Record a metric with buffering
    pub async fn record_metric(&self, name: String, value: f64, labels: HashMap<String, String>, metric_type: MetricType) {
        // Add to buffer for high-throughput scenarios
        if self.aggregation_config.enable_buffering {
            let mut buffer = self.buffer.write().await;
            buffer.add_metric(BufferedMetric::new(name.clone(), value, labels.clone(), metric_type.clone()));
        }

        // Record immediately to OpenTelemetry
        match metric_type {
            MetricType::Counter => {
                let ot_labels = labels.into_iter()
                    .map(|(k, v)| opentelemetry::KeyValue::new(k, v))
                    .collect::<Vec<_>>();
                self.observability.custom_metrics_manager.record_counter(&name, value as u64, ot_labels);
            }
            MetricType::Histogram => {
                let ot_labels = labels.into_iter()
                    .map(|(k, v)| opentelemetry::KeyValue::new(k, v))
                    .collect::<Vec<_>>();
                self.observability.custom_metrics_manager.record_histogram(&name, value, ot_labels);
            }
            MetricType::Gauge => {
                let ot_labels = labels.into_iter()
                    .map(|(k, v)| opentelemetry::KeyValue::new(k, v))
                    .collect::<Vec<_>>();
                self.observability.custom_metrics_manager.set_gauge(&name, value, ot_labels);
            }
            MetricType::ObservableGauge => {
                // Observable gauges are handled differently
            }
        }
    }

    /// Export metrics in specified format
    pub async fn export_metrics(&self, format: &ExportFormat) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        if let Some(exporter) = self.exporters.get(format) {
            let license_metrics = self.observability.get_license_detection_metrics().await;
            let system_health = self.observability.get_system_health_metrics().await;
            let performance = self.observability.get_performance_metrics().await;
            let business = self.observability.get_business_metrics().await;

            exporter.export(&license_metrics, &system_health, &performance, &business).await
        } else {
            Err(format!("Unsupported export format: {:?}", format).into())
        }
    }

    /// Export metrics to all configured formats
    pub async fn export_all_formats(&self) -> Result<HashMap<ExportFormat, String>, Box<dyn std::error::Error + Send + Sync>> {
        let mut results = HashMap::new();

        for format in &self.aggregation_config.export_formats {
            let export = self.export_metrics(format).await?;
            results.insert(format.clone(), export);
        }

        Ok(results)
    }

    /// Perform metrics aggregation
    pub async fn perform_aggregation(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut last_agg = self.last_aggregation.write().await;
        let elapsed = last_agg.elapsed();

        if elapsed >= Duration::from_secs(self.aggregation_config.aggregation_interval_seconds) {
            // Perform aggregation logic here
            // This could include calculating averages, percentiles, etc.

            *last_agg = Instant::now();
        }

        Ok(())
    }

    /// Get buffer statistics
    pub async fn get_buffer_stats(&self) -> BufferStats {
        let buffer = self.buffer.read().await;
        BufferStats {
            current_size: buffer.size(),
            max_size: buffer.max_size,
            flush_interval: buffer.flush_interval,
        }
    }

    /// Force buffer flush
    pub async fn flush_buffer(&self) {
        let mut buffer = self.buffer.write().await;
        buffer.flush();
    }
}

/// Metrics buffer with enhanced features
pub struct MetricsBuffer {
    buffer: Vec<BufferedMetric>,
    max_size: usize,
    flush_interval: Duration,
    last_flush: Instant,
}

impl MetricsBuffer {
    /// Create a new metrics buffer
    pub fn new(max_size: usize, flush_interval: Duration) -> Self {
        Self {
            buffer: Vec::new(),
            max_size,
            flush_interval,
            last_flush: Instant::now(),
        }
    }

    /// Add a metric to the buffer
    pub fn add_metric(&mut self, metric: BufferedMetric) {
        self.buffer.push(metric);

        // Auto-flush if buffer is full or flush interval has passed
        if self.buffer.len() >= self.max_size || self.last_flush.elapsed() >= self.flush_interval {
            self.flush();
        }
    }

    /// Flush buffered metrics
    pub fn flush(&mut self) {
        if self.buffer.is_empty() {
            return;
        }

        // Process buffered metrics (e.g., batch export, aggregation)
        // For now, just clear the buffer
        self.buffer.clear();
        self.last_flush = Instant::now();
    }

    /// Get current buffer size
    pub fn size(&self) -> usize {
        self.buffer.len()
    }

    /// Get max buffer size
    pub fn max_size(&self) -> usize {
        self.max_size
    }
}

/// Metrics exporter trait
#[async_trait::async_trait]
pub trait MetricsExporter: Send + Sync {
    async fn export(
        &self,
        license_metrics: &LicenseDetectionMetrics,
        system_health: &SystemHealthMetrics,
        performance: &PerformanceMetrics,
        business: &BusinessMetrics,
    ) -> Result<String, Box<dyn std::error::Error + Send + Sync>>;
}

/// Prometheus exporter
pub struct PrometheusExporter;

impl PrometheusExporter {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl MetricsExporter for PrometheusExporter {
    async fn export(
        &self,
        license_metrics: &LicenseDetectionMetrics,
        system_health: &SystemHealthMetrics,
        performance: &PerformanceMetrics,
        business: &BusinessMetrics,
    ) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let mut output = String::new();

        // License detection metrics
        output.push_str(&format!("# HELP license_predictions_total Total number of license predictions\n"));
        output.push_str(&format!("# TYPE license_predictions_total counter\n"));
        output.push_str(&format!("license_predictions_total {}\n", license_metrics.total_predictions));

        output.push_str(&format!("# HELP license_accuracy License detection accuracy\n"));
        output.push_str(&format!("# TYPE license_accuracy gauge\n"));
        output.push_str(&format!("license_accuracy {:.4}\n", license_metrics.precision));

        // System health metrics
        output.push_str(&format!("# HELP system_cpu_usage_percent CPU usage percentage\n"));
        output.push_str(&format!("# TYPE system_cpu_usage_percent gauge\n"));
        output.push_str(&format!("system_cpu_usage_percent {:.2}\n", system_health.resource_utilization.cpu_usage_percent));

        // Performance metrics
        output.push_str(&format!("# HELP request_processing_time_ms Request processing time\n"));
        output.push_str(&format!("# TYPE request_processing_time_ms histogram\n"));
        output.push_str(&format!("request_processing_time_ms {:.2}\n", performance.avg_processing_time_ms));

        // Business metrics
        output.push_str(&format!("# HELP compliance_scans_total Total compliance scans\n"));
        output.push_str(&format!("# TYPE compliance_scans_total counter\n"));
        output.push_str(&format!("compliance_scans_total {}\n", business.compliance_scans_total));

        Ok(output)
    }
}

/// JSON exporter
pub struct JsonExporter;

impl JsonExporter {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl MetricsExporter for JsonExporter {
    async fn export(
        &self,
        license_metrics: &LicenseDetectionMetrics,
        system_health: &SystemHealthMetrics,
        performance: &PerformanceMetrics,
        business: &BusinessMetrics,
    ) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let metrics = serde_json::json!({
            "license_detection": license_metrics,
            "system_health": system_health,
            "performance": performance,
            "business": business,
            "export_timestamp": chrono::Utc::now(),
        });

        Ok(serde_json::to_string_pretty(&metrics)?)
    }
}

/// CSV exporter
pub struct CsvExporter;

impl CsvExporter {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl MetricsExporter for CsvExporter {
    async fn export(
        &self,
        license_metrics: &LicenseDetectionMetrics,
        system_health: &SystemHealthMetrics,
        performance: &PerformanceMetrics,
        business: &BusinessMetrics,
    ) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let mut output = String::new();

        // Header
        output.push_str("metric_type,metric_name,value,timestamp\n");

        // License metrics
        output.push_str(&format!("license,total_predictions,{},{}\n",
            license_metrics.total_predictions, chrono::Utc::now().timestamp()));
        output.push_str(&format!("license,precision,{:.4},{}\n",
            license_metrics.precision, chrono::Utc::now().timestamp()));

        // System health metrics
        output.push_str(&format!("system,cpu_usage_percent,{:.2},{}\n",
            system_health.resource_utilization.cpu_usage_percent, chrono::Utc::now().timestamp()));
        output.push_str(&format!("system,memory_usage_percent,{:.2},{}\n",
            system_health.resource_utilization.memory_usage_percent, chrono::Utc::now().timestamp()));

        // Performance metrics
        output.push_str(&format!("performance,avg_processing_time_ms,{:.2},{}\n",
            performance.avg_processing_time_ms, chrono::Utc::now().timestamp()));
        output.push_str(&format!("performance,request_queue_depth,{},{}\n",
            performance.request_queue_depth, chrono::Utc::now().timestamp()));

        // Business metrics
        output.push_str(&format!("business,compliance_scans_total,{},{}\n",
            business.compliance_scans_total, chrono::Utc::now().timestamp()));
        output.push_str(&format!("business,scan_throughput_per_minute,{:.2},{}\n",
            business.scan_throughput_per_minute, chrono::Utc::now().timestamp()));

        Ok(output)
    }
}

/// Buffer statistics
#[derive(Debug, Clone)]
pub struct BufferStats {
    pub current_size: usize,
    pub max_size: usize,
    pub flush_interval: Duration,
}

/// Metrics retention manager
pub struct MetricsRetentionManager {
    retention_period: Duration,
    cleanup_interval: Duration,
}

impl MetricsRetentionManager {
    /// Create a new retention manager
    pub fn new(retention_period_days: u32) -> Self {
        Self {
            retention_period: Duration::from_secs(retention_period_days as u64 * 24 * 60 * 60),
            cleanup_interval: Duration::from_secs(60 * 60), // Clean up every hour
        }
    }

    /// Start cleanup task
    pub async fn start_cleanup(&self) {
        let retention = self.retention_period;
        let interval = self.cleanup_interval;

        tokio::spawn(async move {
            let mut ticker = time::interval(interval);
            loop {
                ticker.tick().await;
                // Perform cleanup of old metrics
                // This would integrate with a metrics storage system
            }
        });
    }
}