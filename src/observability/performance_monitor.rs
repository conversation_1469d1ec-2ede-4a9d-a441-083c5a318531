//! # Performance Monitor
//!
//! This module provides performance monitoring capabilities that track
//! processing times, queue depths, database connections, and other
//! performance-related metrics.

use crate::observability::{ObservabilityManager, custom_metrics::*};
use std::sync::Arc;
use std::collections::HashMap;
use tokio::sync::RwLock;
use std::time::{Duration, Instant};

/// Performance monitor
pub struct PerformanceMonitor {
    observability: Arc<ObservabilityManager>,
    active_operations: RwLock<HashMap<String, OperationTracker>>,
    queue_depths: RwLock<HashMap<String, u64>>,
}

impl PerformanceMonitor {
    /// Create a new performance monitor
    pub fn new(observability: Arc<ObservabilityManager>) -> Self {
        Self {
            observability,
            active_operations: RwLock::new(HashMap::new()),
            queue_depths: RwLock::new(HashMap::new()),
        }
    }

    /// Start tracking an operation
    pub async fn start_operation(&self, operation_id: String, operation_type: &str) {
        let tracker = OperationTracker {
            operation_type: operation_type.to_string(),
            start_time: Instant::now(),
        };

        let mut active_ops = self.active_operations.write().await;
        active_ops.insert(operation_id, tracker);

        // Update queue depth
        self.update_queue_depth(operation_type, 1).await;
    }

    /// Complete tracking an operation
    pub async fn complete_operation(&self, operation_id: String) -> Option<f64> {
        let mut active_ops = self.active_operations.write().await;

        if let Some(tracker) = active_ops.remove(&operation_id) {
            let duration_ms = tracker.start_time.elapsed().as_millis() as f64;

            // Update queue depth
            self.update_queue_depth(&tracker.operation_type, -1).await;

            // Record performance metrics
            self.observability.record_performance(
                &tracker.operation_type,
                duration_ms,
                self.get_queue_depth(&tracker.operation_type).await,
            ).await;

            // Update performance metrics
            {
                let mut perf_metrics = self.observability.performance_metrics.write().await;
                perf_metrics.add_processing_time(duration_ms);
                perf_metrics.request_queue_depth = self.get_total_queue_depth().await;
            }

            Some(duration_ms)
        } else {
            None
        }
    }

    /// Update queue depth for an operation type
    async fn update_queue_depth(&self, operation_type: &str, delta: i64) {
        let mut depths = self.queue_depths.write().await;
        let current = depths.get(operation_type).copied().unwrap_or(0);
        let new_depth = (current as i64 + delta).max(0) as u64;
        depths.insert(operation_type.to_string(), new_depth);
    }

    /// Get queue depth for an operation type
    async fn get_queue_depth(&self, operation_type: &str) -> u64 {
        let depths = self.queue_depths.read().await;
        depths.get(operation_type).copied().unwrap_or(0)
    }

    /// Get total queue depth across all operations
    async fn get_total_queue_depth(&self) -> u64 {
        let depths = self.queue_depths.read().await;
        depths.values().sum()
    }

    /// Record database query performance
    pub async fn record_db_query(&self, operation: &str, duration_ms: f64, connection_count: u32) {
        // Update performance metrics
        {
            let mut perf_metrics = self.observability.performance_metrics.write().await;
            perf_metrics.db_query_duration_ms.push(duration_ms);
            perf_metrics.db_connection_pool_active = connection_count;

            // Keep only last 100 measurements
            if perf_metrics.db_query_duration_ms.len() > 100 {
                perf_metrics.db_query_duration_ms.remove(0);
            }
        }

        // Record to observability
        self.observability.record_performance(
            &format!("db_{}", operation),
            duration_ms,
            0, // DB operations don't have queue depth in the same way
        ).await;
    }

    /// Record network I/O metrics
    pub async fn record_network_io(&self, bytes_sent: u64, bytes_received: u64, active_connections: u32) {
        // Update performance metrics
        {
            let mut perf_metrics = self.observability.performance_metrics.write().await;
            perf_metrics.network_io_metrics.bytes_sent += bytes_sent;
            perf_metrics.network_io_metrics.bytes_received += bytes_received;
            perf_metrics.network_io_metrics.connections_active = active_connections;
            perf_metrics.network_io_metrics.connections_total += 1;
        }
    }

    /// Get current performance metrics
    pub async fn get_performance_metrics(&self) -> PerformanceMetrics {
        self.observability.get_performance_metrics().await
    }

    /// Get active operations count
    pub async fn get_active_operations_count(&self) -> usize {
        let active_ops = self.active_operations.read().await;
        active_ops.len()
    }

    /// Get queue depths
    pub async fn get_queue_depths(&self) -> HashMap<String, u64> {
        let depths = self.queue_depths.read().await;
        depths.clone()
    }
}

/// Operation tracker
struct OperationTracker {
    operation_type: String,
    start_time: Instant,
}

/// Request queue monitor
pub struct RequestQueueMonitor {
    observability: Arc<ObservabilityManager>,
    queue_sizes: RwLock<HashMap<String, u64>>,
}

impl RequestQueueMonitor {
    /// Create a new request queue monitor
    pub fn new(observability: Arc<ObservabilityManager>) -> Self {
        Self {
            observability,
            queue_sizes: RwLock::new(HashMap::new()),
        }
    }

    /// Update queue size
    pub async fn update_queue_size(&self, queue_name: &str, size: u64) {
        let mut sizes = self.queue_sizes.write().await;
        sizes.insert(queue_name.to_string(), size);

        // Record to observability
        self.observability.record_performance(
            &format!("queue_{}", queue_name),
            0.0, // No processing time for queue size
            size,
        ).await;
    }

    /// Get queue size
    pub async fn get_queue_size(&self, queue_name: &str) -> u64 {
        let sizes = self.queue_sizes.read().await;
        sizes.get(queue_name).copied().unwrap_or(0)
    }

    /// Get all queue sizes
    pub async fn get_all_queue_sizes(&self) -> HashMap<String, u64> {
        let sizes = self.queue_sizes.read().await;
        sizes.clone()
    }
}

/// Database connection pool monitor
pub struct DatabaseConnectionMonitor {
    observability: Arc<ObservabilityManager>,
}

impl DatabaseConnectionMonitor {
    /// Create a new database connection monitor
    pub fn new(observability: Arc<ObservabilityManager>) -> Self {
        Self { observability }
    }

    /// Update connection pool metrics
    pub async fn update_connection_pool(&self, active: u32, idle: u32, total: u32) {
        // Update performance metrics
        {
            let mut perf_metrics = self.observability.performance_metrics.write().await;
            perf_metrics.db_connection_pool_active = active;
            perf_metrics.db_connection_pool_idle = idle;
        }

        // Record as performance metrics
        self.observability.record_performance(
            "db_connection_pool",
            0.0, // No processing time
            total as u64,
        ).await;
    }
}

/// Bottleneck detector
pub struct BottleneckDetector {
    observability: Arc<ObservabilityManager>,
    thresholds: BottleneckThresholds,
}

impl BottleneckDetector {
    /// Create a new bottleneck detector
    pub fn new(observability: Arc<ObservabilityManager>) -> Self {
        Self {
            observability,
            thresholds: BottleneckThresholds::default(),
        }
    }

    /// Check for bottlenecks
    pub async fn check_for_bottlenecks(&self) -> Vec<BottleneckAlert> {
        let mut alerts = Vec::new();

        let perf_metrics = self.observability.get_performance_metrics().await;
        let system_health = self.observability.get_system_health_metrics().await;

        // Check processing time bottlenecks
        if perf_metrics.p95_processing_time_ms > self.thresholds.max_p95_processing_time_ms {
            alerts.push(BottleneckAlert {
                bottleneck_type: BottleneckType::HighProcessingTime,
                severity: AlertSeverity::High,
                message: format!("P95 processing time ({:.2}ms) exceeds threshold ({:.2}ms)",
                    perf_metrics.p95_processing_time_ms, self.thresholds.max_p95_processing_time_ms),
                recommendation: "Consider optimizing processing logic or scaling resources".to_string(),
            });
        }

        // Check queue depth bottlenecks
        if perf_metrics.request_queue_depth > self.thresholds.max_queue_depth {
            alerts.push(BottleneckAlert {
                bottleneck_type: BottleneckType::HighQueueDepth,
                severity: AlertSeverity::High,
                message: format!("Queue depth ({}) exceeds threshold ({})",
                    perf_metrics.request_queue_depth, self.thresholds.max_queue_depth),
                recommendation: "Consider increasing processing capacity or optimizing queue management".to_string(),
            });
        }

        // Check CPU bottlenecks
        if system_health.resource_utilization.cpu_usage_percent > self.thresholds.max_cpu_usage_percent {
            alerts.push(BottleneckAlert {
                bottleneck_type: BottleneckType::HighCpuUsage,
                severity: AlertSeverity::Medium,
                message: format!("CPU usage ({:.1}%) exceeds threshold ({:.1}%)",
                    system_health.resource_utilization.cpu_usage_percent, self.thresholds.max_cpu_usage_percent),
                recommendation: "Consider optimizing CPU-intensive operations or scaling compute resources".to_string(),
            });
        }

        // Check memory bottlenecks
        if system_health.resource_utilization.memory_usage_percent > self.thresholds.max_memory_usage_percent {
            alerts.push(BottleneckAlert {
                bottleneck_type: BottleneckType::HighMemoryUsage,
                severity: AlertSeverity::High,
                message: format!("Memory usage ({:.1}%) exceeds threshold ({:.1}%)",
                    system_health.resource_utilization.memory_usage_percent, self.thresholds.max_memory_usage_percent),
                recommendation: "Consider optimizing memory usage or increasing memory allocation".to_string(),
            });
        }

        alerts
    }
}

/// Bottleneck thresholds
#[derive(Debug, Clone)]
pub struct BottleneckThresholds {
    pub max_p95_processing_time_ms: f64,
    pub max_queue_depth: u64,
    pub max_cpu_usage_percent: f64,
    pub max_memory_usage_percent: f64,
}

impl Default for BottleneckThresholds {
    fn default() -> Self {
        Self {
            max_p95_processing_time_ms: 1000.0, // 1 second
            max_queue_depth: 100,
            max_cpu_usage_percent: 80.0,
            max_memory_usage_percent: 85.0,
        }
    }
}

/// Bottleneck alert
#[derive(Debug, Clone)]
pub struct BottleneckAlert {
    pub bottleneck_type: BottleneckType,
    pub severity: AlertSeverity,
    pub message: String,
    pub recommendation: String,
}

/// Bottleneck type
#[derive(Debug, Clone)]
pub enum BottleneckType {
    HighProcessingTime,
    HighQueueDepth,
    HighCpuUsage,
    HighMemoryUsage,
}

/// Alert severity
#[derive(Debug, Clone)]
pub enum AlertSeverity {
    Low,
    Medium,
    High,
    Critical,
}