//! # Performance Profiling Framework
//!
//! This module provides comprehensive performance profiling capabilities including:
//! - Continuous performance profiling with sampling
//! - CPU and memory profiling
//! - Profiling data collection and storage
//! - Flame graph generation and analysis
//! - Real-time bottleneck detection
//! - Performance analysis tools

use crate::observability::{ObservabilityManager, custom_metrics::*};
use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// Performance profiling framework
pub struct PerformanceProfiler {
    config: ProfilingConfig,
    observability: Arc<ObservabilityManager>,
    cpu_profiler: CpuProfiler,
    memory_profiler: MemoryProfiler,
    profiling_data: RwLock<ProfilingDataStore>,
    bottleneck_detector: BottleneckDetector,
    scaling_analyzer: ScalingAnalyzer,
    performance_analyzer: PerformanceAnalyzer,
}

impl PerformanceProfiler {
    /// Create a new performance profiler
    pub fn new(config: ProfilingConfig, observability: Arc<ObservabilityManager>) -> Self {
        Self {
            config: config.clone(),
            observability,
            cpu_profiler: CpuProfiler::new(config.cpu_profiling.clone()),
            memory_profiler: MemoryProfiler::new(config.memory_profiling.clone()),
            profiling_data: RwLock::new(ProfilingDataStore::new(config.data_storage.clone())),
            bottleneck_detector: BottleneckDetector::new(config.bottleneck_detection.clone()),
            scaling_analyzer: ScalingAnalyzer::new(config.scaling_analysis.clone()),
            performance_analyzer: PerformanceAnalyzer::new(config.performance_analysis.clone()),
        }
    }

    /// Start continuous profiling
    pub async fn start_continuous_profiling(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Start CPU profiling
        if self.config.cpu_profiling.enabled {
            self.cpu_profiler.start_profiling().await?;
        }

        // Start memory profiling
        if self.config.memory_profiling.enabled {
            self.memory_profiler.start_profiling().await?;
        }

        // Start bottleneck detection
        if self.config.bottleneck_detection.enabled {
            self.bottleneck_detector.start_detection().await?;
        }

        // Start scaling analysis
        if self.config.scaling_analysis.enabled {
            self.scaling_analyzer.start_analysis().await?;
        }

        Ok(())
    }

    /// Stop continuous profiling
    pub async fn stop_continuous_profiling(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Stop CPU profiling
        if self.config.cpu_profiling.enabled {
            self.cpu_profiler.stop_profiling().await?;
        }

        // Stop memory profiling
        if self.config.memory_profiling.enabled {
            self.memory_profiler.stop_profiling().await?;
        }

        // Stop bottleneck detection
        if self.config.bottleneck_detection.enabled {
            self.bottleneck_detector.stop_detection().await?;
        }

        // Stop scaling analysis
        if self.config.scaling_analysis.enabled {
            self.scaling_analyzer.stop_analysis().await?;
        }

        Ok(())
    }

    /// Collect current profiling data
    pub async fn collect_profiling_data(&self) -> Result<ProfilingSnapshot, Box<dyn std::error::Error + Send + Sync>> {
        let mut snapshot = ProfilingSnapshot {
            timestamp: Utc::now(),
            cpu_profile: None,
            memory_profile: None,
            bottlenecks: Vec::new(),
            scaling_recommendations: Vec::new(),
            performance_insights: Vec::new(),
        };

        // Collect CPU profiling data
        if self.config.cpu_profiling.enabled {
            snapshot.cpu_profile = Some(self.cpu_profiler.collect_profile().await?);
        }

        // Collect memory profiling data
        if self.config.memory_profiling.enabled {
            snapshot.memory_profile = Some(self.memory_profiler.collect_profile().await?);
        }

        // Collect bottleneck analysis
        if self.config.bottleneck_detection.enabled {
            snapshot.bottlenecks = self.bottleneck_detector.analyze_bottlenecks().await?;
        }

        // Collect scaling recommendations
        if self.config.scaling_analysis.enabled {
            snapshot.scaling_recommendations = self.scaling_analyzer.generate_recommendations().await?;
        }

        // Collect performance insights
        if self.config.performance_analysis.enabled {
            snapshot.performance_insights = self.performance_analyzer.analyze_performance().await?;
        }

        // Store profiling data
        {
            let mut data_store = self.profiling_data.write().await;
            data_store.store_snapshot(snapshot.clone()).await?;
        }

        Ok(snapshot)
    }

    /// Generate flame graph from profiling data
    pub async fn generate_flame_graph(&self, duration: Duration) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let data_store = self.profiling_data.read().await;
        let recent_snapshots = data_store.get_recent_snapshots(duration).await?;

        // Generate flame graph data from CPU profiles
        let mut flame_graph_data = String::new();
        flame_graph_data.push_str("Flame Graph Data\n");

        for snapshot in recent_snapshots {
            if let Some(cpu_profile) = &snapshot.cpu_profile {
                for sample in &cpu_profile.samples {
                    flame_graph_data.push_str(&format!("{} {}\n", sample.stack_trace, sample.count));
                }
            }
        }

        Ok(flame_graph_data)
    }

    /// Get performance recommendations
    pub async fn get_performance_recommendations(&self) -> Result<Vec<PerformanceRecommendation>, Box<dyn std::error::Error + Send + Sync>> {
        let data_store = self.profiling_data.read().await;
        let recent_snapshots = data_store.get_recent_snapshots(Duration::from_secs(3600)).await?; // Last hour

        let mut recommendations = Vec::new();

        // Analyze bottlenecks for recommendations
        for snapshot in recent_snapshots {
            for bottleneck in &snapshot.bottlenecks {
                recommendations.extend(self.generate_bottleneck_recommendations(bottleneck).await?);
            }
        }

        // Add scaling recommendations
        for snapshot in recent_snapshots {
            recommendations.extend(snapshot.scaling_recommendations.clone());
        }

        Ok(recommendations)
    }

    /// Generate recommendations based on bottleneck analysis
    async fn generate_bottleneck_recommendations(&self, bottleneck: &Bottleneck) -> Result<Vec<PerformanceRecommendation>, Box<dyn std::error::Error + Send + Sync>> {
        let mut recommendations = Vec::new();

        match bottleneck.bottleneck_type {
            BottleneckType::HighCpuUsage => {
                recommendations.push(PerformanceRecommendation {
                    recommendation_type: RecommendationType::Optimization,
                    severity: RecommendationSeverity::High,
                    title: "High CPU Usage Detected".to_string(),
                    description: format!("CPU usage is {:.1}% which exceeds threshold", bottleneck.metric_value),
                    actions: vec![
                        "Optimize CPU-intensive operations".to_string(),
                        "Consider horizontal scaling".to_string(),
                        "Review thread pool configurations".to_string(),
                    ],
                    estimated_impact: "20-40% CPU reduction".to_string(),
                    implementation_effort: ImplementationEffort::Medium,
                });
            }
            BottleneckType::HighMemoryUsage => {
                recommendations.push(PerformanceRecommendation {
                    recommendation_type: RecommendationType::Optimization,
                    severity: RecommendationSeverity::High,
                    title: "High Memory Usage Detected".to_string(),
                    description: format!("Memory usage is {:.1}% which exceeds threshold", bottleneck.metric_value),
                    actions: vec![
                        "Optimize memory allocations".to_string(),
                        "Implement memory pooling".to_string(),
                        "Review garbage collection settings".to_string(),
                    ],
                    estimated_impact: "30-50% memory reduction".to_string(),
                    implementation_effort: ImplementationEffort::Medium,
                });
            }
            BottleneckType::SlowDatabaseQueries => {
                recommendations.push(PerformanceRecommendation {
                    recommendation_type: RecommendationType::Database,
                    severity: RecommendationSeverity::Medium,
                    title: "Slow Database Queries Detected".to_string(),
                    description: format!("Database query latency is {:.1}ms which exceeds threshold", bottleneck.metric_value),
                    actions: vec![
                        "Add database indexes".to_string(),
                        "Optimize query patterns".to_string(),
                        "Implement query caching".to_string(),
                    ],
                    estimated_impact: "50-70% query time reduction".to_string(),
                    implementation_effort: ImplementationEffort::Low,
                });
            }
            _ => {}
        }

        Ok(recommendations)
    }

    /// Export profiling data
    pub async fn export_profiling_data(&self, format: ExportFormat) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let data_store = self.profiling_data.read().await;
        let all_snapshots = data_store.get_all_snapshots().await?;

        match format {
            ExportFormat::JSON => {
                serde_json::to_string_pretty(&all_snapshots)
                    .map_err(|e| Box::new(e) as Box<dyn std::error::Error + Send + Sync>)
            }
            ExportFormat::CSV => {
                let mut csv_data = String::new();
                csv_data.push_str("timestamp,cpu_usage,memory_usage,bottleneck_count,recommendation_count\n");

                for snapshot in all_snapshots {
                    let cpu_usage = snapshot.cpu_profile.as_ref()
                        .map(|p| p.cpu_usage_percent)
                        .unwrap_or(0.0);
                    let memory_usage = snapshot.memory_profile.as_ref()
                        .map(|p| p.memory_usage_percent)
                        .unwrap_or(0.0);
                    let bottleneck_count = snapshot.bottlenecks.len();
                    let recommendation_count = snapshot.scaling_recommendations.len();

                    csv_data.push_str(&format!("{},{:.2},{:.2},{},{}\n",
                        snapshot.timestamp.to_rfc3339(),
                        cpu_usage,
                        memory_usage,
                        bottleneck_count,
                        recommendation_count
                    ));
                }

                Ok(csv_data)
            }
            _ => Err("Unsupported export format".into())
        }
    }
}

/// Profiling configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProfilingConfig {
    pub enabled: bool,
    pub sampling_interval_ms: u64,
    pub retention_period_hours: u64,
    pub cpu_profiling: CpuProfilingConfig,
    pub memory_profiling: MemoryProfilingConfig,
    pub bottleneck_detection: BottleneckDetectionConfig,
    pub scaling_analysis: ScalingAnalysisConfig,
    pub performance_analysis: PerformanceAnalysisConfig,
    pub data_storage: DataStorageConfig,
}

impl Default for ProfilingConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            sampling_interval_ms: 1000, // 1 second
            retention_period_hours: 24, // 24 hours
            cpu_profiling: CpuProfilingConfig::default(),
            memory_profiling: MemoryProfilingConfig::default(),
            bottleneck_detection: BottleneckDetectionConfig::default(),
            scaling_analysis: ScalingAnalysisConfig::default(),
            performance_analysis: PerformanceAnalysisConfig::default(),
            data_storage: DataStorageConfig::default(),
        }
    }
}

/// CPU profiling configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CpuProfilingConfig {
    pub enabled: bool,
    pub sampling_rate: u32, // Samples per second
    pub max_stack_depth: usize,
    pub include_idle_time: bool,
}

impl Default for CpuProfilingConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            sampling_rate: 100,
            max_stack_depth: 128,
            include_idle_time: false,
        }
    }
}

/// Memory profiling configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryProfilingConfig {
    pub enabled: bool,
    pub sampling_interval_ms: u64,
    pub track_allocations: bool,
    pub track_deallocations: bool,
    pub heap_dump_interval_minutes: u64,
}

impl Default for MemoryProfilingConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            sampling_interval_ms: 5000, // 5 seconds
            track_allocations: true,
            track_deallocations: true,
            heap_dump_interval_minutes: 60, // 1 hour
        }
    }
}

/// Bottleneck detection configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BottleneckDetectionConfig {
    pub enabled: bool,
    pub analysis_interval_seconds: u64,
    pub cpu_threshold_percent: f64,
    pub memory_threshold_percent: f64,
    pub queue_depth_threshold: u64,
    pub db_query_threshold_ms: f64,
    pub network_io_threshold_bytes: u64,
}

impl Default for BottleneckDetectionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            analysis_interval_seconds: 30,
            cpu_threshold_percent: 80.0,
            memory_threshold_percent: 85.0,
            queue_depth_threshold: 100,
            db_query_threshold_ms: 1000.0,
            network_io_threshold_bytes: 100_000_000, // 100MB
        }
    }
}

/// Scaling analysis configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScalingAnalysisConfig {
    pub enabled: bool,
    pub analysis_window_minutes: u64,
    pub cpu_scaling_threshold: f64,
    pub memory_scaling_threshold: f64,
    pub queue_scaling_threshold: u64,
    pub cost_optimization_enabled: bool,
}

impl Default for ScalingAnalysisConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            analysis_window_minutes: 60,
            cpu_scaling_threshold: 70.0,
            memory_scaling_threshold: 75.0,
            queue_scaling_threshold: 50,
            cost_optimization_enabled: true,
        }
    }
}

/// Performance analysis configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceAnalysisConfig {
    pub enabled: bool,
    pub trend_analysis_window_hours: u64,
    pub regression_detection_sensitivity: f64,
    pub seasonal_pattern_detection: bool,
    pub comparative_analysis_enabled: bool,
}

impl Default for PerformanceAnalysisConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            trend_analysis_window_hours: 168, // 1 week
            regression_detection_sensitivity: 0.1, // 10% change
            seasonal_pattern_detection: true,
            comparative_analysis_enabled: true,
        }
    }
}

/// Data storage configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataStorageConfig {
    pub max_snapshots: usize,
    pub compression_enabled: bool,
    pub storage_path: Option<String>,
    pub archive_after_hours: u64,
}

impl Default for DataStorageConfig {
    fn default() -> Self {
        Self {
            max_snapshots: 10000,
            compression_enabled: true,
            storage_path: None,
            archive_after_hours: 168, // 1 week
        }
    }
}

/// CPU profiler
pub struct CpuProfiler {
    config: CpuProfilingConfig,
    is_profiling: RwLock<bool>,
    samples: RwLock<Vec<CpuSample>>,
}

impl CpuProfiler {
    pub fn new(config: CpuProfilingConfig) -> Self {
        Self {
            config,
            is_profiling: RwLock::new(false),
            samples: RwLock::new(Vec::new()),
        }
    }

    pub async fn start_profiling(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut is_profiling = self.is_profiling.write().await;
        *is_profiling = true;

        // In a real implementation, this would start the CPU profiler
        // For now, we'll simulate profiling
        Ok(())
    }

    pub async fn stop_profiling(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut is_profiling = self.is_profiling.write().await;
        *is_profiling = false;
        Ok(())
    }

    pub async fn collect_profile(&self) -> Result<CpuProfile, Box<dyn std::error::Error + Send + Sync>> {
        let samples = self.samples.read().await.clone();

        // Calculate CPU usage from samples
        let total_samples = samples.len() as f64;
        let on_cpu_samples = samples.iter().filter(|s| !s.is_idle).count() as f64;
        let cpu_usage_percent = if total_samples > 0.0 {
            (on_cpu_samples / total_samples) * 100.0
        } else {
            0.0
        };

        Ok(CpuProfile {
            timestamp: Utc::now(),
            cpu_usage_percent,
            samples,
            total_samples: total_samples as u64,
            sampling_rate: self.config.sampling_rate,
        })
    }
}

/// Memory profiler
pub struct MemoryProfiler {
    config: MemoryProfilingConfig,
    is_profiling: RwLock<bool>,
    allocations: RwLock<Vec<MemoryAllocation>>,
}

impl MemoryProfiler {
    pub fn new(config: MemoryProfilingConfig) -> Self {
        Self {
            config,
            is_profiling: RwLock::new(false),
            allocations: RwLock::new(Vec::new()),
        }
    }

    pub async fn start_profiling(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut is_profiling = self.is_profiling.write().await;
        *is_profiling = true;

        // In a real implementation, this would start memory profiling
        Ok(())
    }

    pub async fn stop_profiling(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut is_profiling = self.is_profiling.write().await;
        *is_profiling = false;
        Ok(())
    }

    pub async fn collect_profile(&self) -> Result<MemoryProfile, Box<dyn std::error::Error + Send + Sync>> {
        let allocations = self.allocations.read().await.clone();

        // Calculate memory usage statistics
        let total_allocated: u64 = allocations.iter().map(|a| a.size_bytes).sum();
        let active_allocations = allocations.iter().filter(|a| !a.deallocated).count();

        // Estimate memory usage percentage (simplified)
        let memory_usage_percent = (total_allocated as f64 / 1_000_000_000.0) * 100.0; // Assume 1GB total for demo

        Ok(MemoryProfile {
            timestamp: Utc::now(),
            memory_usage_percent,
            total_allocated_bytes: total_allocated,
            active_allocations: active_allocations as u64,
            allocations: allocations.clone(),
            heap_size_bytes: total_allocated, // Simplified
        })
    }
}

/// Bottleneck detector
pub struct BottleneckDetector {
    config: BottleneckDetectionConfig,
    is_detecting: RwLock<bool>,
}

impl BottleneckDetector {
    pub fn new(config: BottleneckDetectionConfig) -> Self {
        Self {
            config,
            is_detecting: RwLock::new(false),
        }
    }

    pub async fn start_detection(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut is_detecting = self.is_detecting.write().await;
        *is_detecting = true;
        Ok(())
    }

    pub async fn stop_detection(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut is_detecting = self.is_detecting.write().await;
        *is_detecting = false;
        Ok(())
    }

    pub async fn analyze_bottlenecks(&self) -> Result<Vec<Bottleneck>, Box<dyn std::error::Error + Send + Sync>> {
        let mut bottlenecks = Vec::new();

        // In a real implementation, this would analyze current system metrics
        // For now, we'll return mock bottlenecks for demonstration

        // Simulate CPU bottleneck detection
        if rand::random::<f64>() > 0.7 { // 30% chance
            bottlenecks.push(Bottleneck {
                timestamp: Utc::now(),
                bottleneck_type: BottleneckType::HighCpuUsage,
                severity: BottleneckSeverity::High,
                metric_value: 85.5,
                description: "CPU usage exceeds threshold".to_string(),
                affected_components: vec!["request_processor".to_string()],
                recommendations: vec![
                    "Consider horizontal scaling".to_string(),
                    "Optimize CPU-intensive operations".to_string(),
                ],
            });
        }

        // Simulate memory bottleneck detection
        if rand::random::<f64>() > 0.8 { // 20% chance
            bottlenecks.push(Bottleneck {
                timestamp: Utc::now(),
                bottleneck_type: BottleneckType::HighMemoryUsage,
                severity: BottleneckSeverity::Critical,
                metric_value: 92.3,
                description: "Memory usage critically high".to_string(),
                affected_components: vec!["cache_manager".to_string()],
                recommendations: vec![
                    "Implement memory pooling".to_string(),
                    "Review memory allocation patterns".to_string(),
                ],
            });
        }

        Ok(bottlenecks)
    }
}

/// Scaling analyzer
pub struct ScalingAnalyzer {
    config: ScalingAnalysisConfig,
    is_analyzing: RwLock<bool>,
}

impl ScalingAnalyzer {
    pub fn new(config: ScalingAnalysisConfig) -> Self {
        Self {
            config,
            is_analyzing: RwLock::new(false),
        }
    }

    pub async fn start_analysis(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut is_analyzing = self.is_analyzing.write().await;
        *is_analyzing = true;
        Ok(())
    }

    pub async fn stop_analysis(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut is_analyzing = self.is_analyzing.write().await;
        *is_analyzing = false;
        Ok(())
    }

    pub async fn generate_recommendations(&self) -> Result<Vec<PerformanceRecommendation>, Box<dyn std::error::Error + Send + Sync>> {
        let mut recommendations = Vec::new();

        // Generate scaling recommendations based on analysis
        recommendations.push(PerformanceRecommendation {
            recommendation_type: RecommendationType::Scaling,
            severity: RecommendationSeverity::Medium,
            title: "Consider Horizontal Scaling".to_string(),
            description: "Current load patterns suggest additional instances would improve performance".to_string(),
            actions: vec![
                "Increase replica count to 3".to_string(),
                "Implement auto-scaling based on CPU usage".to_string(),
                "Review load balancer configuration".to_string(),
            ],
            estimated_impact: "40% improvement in response times".to_string(),
            implementation_effort: ImplementationEffort::Low,
        });

        if self.config.cost_optimization_enabled {
            recommendations.push(PerformanceRecommendation {
                recommendation_type: RecommendationType::CostOptimization,
                severity: RecommendationSeverity::Low,
                title: "Optimize Resource Allocation".to_string(),
                description: "Current resource usage suggests potential cost savings".to_string(),
                actions: vec![
                    "Use spot instances for non-critical workloads".to_string(),
                    "Implement resource right-sizing".to_string(),
                    "Schedule workloads during off-peak hours".to_string(),
                ],
                estimated_impact: "15-25% cost reduction".to_string(),
                implementation_effort: ImplementationEffort::Medium,
            });
        }

        Ok(recommendations)
    }
}

/// Performance analyzer
pub struct PerformanceAnalyzer {
    config: PerformanceAnalysisConfig,
    is_analyzing: RwLock<bool>,
}

impl PerformanceAnalyzer {
    pub fn new(config: PerformanceAnalysisConfig) -> Self {
        Self {
            config,
            is_analyzing: RwLock::new(false),
        }
    }

    pub async fn analyze_performance(&self) -> Result<Vec<PerformanceInsight>, Box<dyn std::error::Error + Send + Sync>> {
        let mut insights = Vec::new();

        // Generate performance insights
        insights.push(PerformanceInsight {
            insight_type: InsightType::Trend,
            severity: InsightSeverity::Info,
            title: "Performance Trend Analysis".to_string(),
            description: "Response times have improved by 15% over the last week".to_string(),
            metrics: HashMap::from([
                ("avg_response_time".to_string(), "245ms".to_string()),
                ("p95_response_time".to_string(), "450ms".to_string()),
                ("throughput".to_string(), "1250 req/sec".to_string()),
            ]),
            recommendations: vec![
                "Continue monitoring for sustained improvement".to_string(),
            ],
        });

        if self.config.regression_detection_sensitivity > 0.0 {
            insights.push(PerformanceInsight {
                insight_type: InsightType::Regression,
                severity: InsightSeverity::Warning,
                title: "Performance Regression Detected".to_string(),
                description: "Database query performance has degraded by 12%".to_string(),
                metrics: HashMap::from([
                    ("db_query_time".to_string(), "850ms".to_string()),
                    ("regression_percentage".to_string(), "12%".to_string()),
                ]),
                recommendations: vec![
                    "Review recent database schema changes".to_string(),
                    "Check for missing indexes".to_string(),
                    "Analyze slow query logs".to_string(),
                ],
            });
        }

        Ok(insights)
    }
}

/// Profiling data store
pub struct ProfilingDataStore {
    config: DataStorageConfig,
    snapshots: VecDeque<ProfilingSnapshot>,
}

impl ProfilingDataStore {
    pub fn new(config: DataStorageConfig) -> Self {
        Self {
            config: config.clone(),
            snapshots: VecDeque::with_capacity(config.max_snapshots),
        }
    }

    pub async fn store_snapshot(&mut self, snapshot: ProfilingSnapshot) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Add new snapshot
        self.snapshots.push_back(snapshot);

        // Maintain size limit
        while self.snapshots.len() > self.config.max_snapshots {
            self.snapshots.pop_front();
        }

        // Archive old data if configured
        if self.config.archive_after_hours > 0 {
            self.archive_old_data().await?;
        }

        Ok(())
    }

    pub async fn get_recent_snapshots(&self, duration: Duration) -> Result<Vec<&ProfilingSnapshot>, Box<dyn std::error::Error + Send + Sync>> {
        let cutoff_time = Utc::now() - chrono::Duration::from_std(duration).unwrap_or(chrono::Duration::hours(1));

        let recent_snapshots: Vec<&ProfilingSnapshot> = self.snapshots
            .iter()
            .filter(|s| s.timestamp > cutoff_time)
            .collect();

        Ok(recent_snapshots)
    }

    pub async fn get_all_snapshots(&self) -> Result<Vec<&ProfilingSnapshot>, Box<dyn std::error::Error + Send + Sync>> {
        Ok(self.snapshots.iter().collect())
    }

    async fn archive_old_data(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let cutoff_time = Utc::now() - chrono::Duration::hours(self.config.archive_after_hours as i64);

        // In a real implementation, this would archive old snapshots to persistent storage
        // For now, we'll just remove them
        self.snapshots.retain(|s| s.timestamp > cutoff_time);

        Ok(())
    }
}

/// Profiling snapshot
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProfilingSnapshot {
    pub timestamp: DateTime<Utc>,
    pub cpu_profile: Option<CpuProfile>,
    pub memory_profile: Option<MemoryProfile>,
    pub bottlenecks: Vec<Bottleneck>,
    pub scaling_recommendations: Vec<PerformanceRecommendation>,
    pub performance_insights: Vec<PerformanceInsight>,
}

/// CPU profile data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CpuProfile {
    pub timestamp: DateTime<Utc>,
    pub cpu_usage_percent: f64,
    pub samples: Vec<CpuSample>,
    pub total_samples: u64,
    pub sampling_rate: u32,
}

/// CPU sample
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CpuSample {
    pub timestamp: DateTime<Utc>,
    pub stack_trace: String,
    pub count: u64,
    pub is_idle: bool,
}

/// Memory profile data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryProfile {
    pub timestamp: DateTime<Utc>,
    pub memory_usage_percent: f64,
    pub total_allocated_bytes: u64,
    pub active_allocations: u64,
    pub allocations: Vec<MemoryAllocation>,
    pub heap_size_bytes: u64,
}

/// Memory allocation record
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryAllocation {
    pub timestamp: DateTime<Utc>,
    pub size_bytes: u64,
    pub stack_trace: String,
    pub deallocated: bool,
    pub allocation_id: u64,
}

/// Bottleneck information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Bottleneck {
    pub timestamp: DateTime<Utc>,
    pub bottleneck_type: BottleneckType,
    pub severity: BottleneckSeverity,
    pub metric_value: f64,
    pub description: String,
    pub affected_components: Vec<String>,
    pub recommendations: Vec<String>,
}

/// Bottleneck type
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BottleneckType {
    HighCpuUsage,
    HighMemoryUsage,
    SlowDatabaseQueries,
    HighQueueDepth,
    NetworkIOContention,
    ThreadContention,
}

/// Bottleneck severity
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BottleneckSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// Performance recommendation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceRecommendation {
    pub recommendation_type: RecommendationType,
    pub severity: RecommendationSeverity,
    pub title: String,
    pub description: String,
    pub actions: Vec<String>,
    pub estimated_impact: String,
    pub implementation_effort: ImplementationEffort,
}

/// Recommendation type
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RecommendationType {
    Optimization,
    Scaling,
    Database,
    Infrastructure,
    CostOptimization,
}

/// Recommendation severity
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RecommendationSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// Implementation effort
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ImplementationEffort {
    Low,
    Medium,
    High,
}

/// Performance insight
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceInsight {
    pub insight_type: InsightType,
    pub severity: InsightSeverity,
    pub title: String,
    pub description: String,
    pub metrics: HashMap<String, String>,
    pub recommendations: Vec<String>,
}

/// Insight type
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum InsightType {
    Trend,
    Regression,
    Anomaly,
    Seasonal,
    Comparative,
}

/// Insight severity
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum InsightSeverity {
    Info,
    Warning,
    Critical,
}

/// Export format
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExportFormat {
    JSON,
    CSV,
    Prometheus,
    InfluxDB,
}