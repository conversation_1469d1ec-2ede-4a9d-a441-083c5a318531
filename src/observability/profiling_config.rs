//! # Performance Profiling Configuration
//!
//! This module provides configuration structures for the performance profiling system.

use serde::{Deserialize, Serialize};
use std::time::Duration;

/// Main profiling configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProfilingConfig {
    /// Enable performance profiling
    pub enabled: bool,
    /// CPU profiling configuration
    pub cpu_profiling: CpuProfilingConfig,
    /// Memory profiling configuration
    pub memory_profiling: MemoryProfilingConfig,
    /// Bottleneck detection configuration
    pub bottleneck_detection: BottleneckDetectionConfig,
    /// Scaling analysis configuration
    pub scaling_analysis: ScalingAnalysisConfig,
    /// Performance analysis configuration
    pub performance_analysis: PerformanceAnalysisConfig,
    /// Data storage configuration
    pub data_storage: DataStorageConfig,
    /// Regression detection sensitivity (0.0-1.0)
    pub regression_detection_sensitivity: f64,
}

impl Default for ProfilingConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            cpu_profiling: CpuProfilingConfig::default(),
            memory_profiling: MemoryProfilingConfig::default(),
            bottleneck_detection: BottleneckDetectionConfig::default(),
            scaling_analysis: ScalingAnalysisConfig::default(),
            performance_analysis: PerformanceAnalysisConfig::default(),
            data_storage: DataStorageConfig::default(),
            regression_detection_sensitivity: 0.1,
        }
    }
}

/// CPU profiling configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CpuProfilingConfig {
    /// Enable CPU profiling
    pub enabled: bool,
    /// Sampling rate in Hz (samples per second)
    pub sampling_rate: u32,
    /// Maximum stack depth to capture
    pub max_stack_depth: usize,
    /// Sampling duration in seconds
    pub sampling_duration_seconds: u64,
    /// Enable flame graph generation
    pub enable_flame_graph: bool,
    /// Flame graph output directory
    pub flame_graph_output_dir: String,
}

impl Default for CpuProfilingConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            sampling_rate: 1000, // 1000 Hz
            max_stack_depth: 128,
            sampling_duration_seconds: 60,
            enable_flame_graph: true,
            flame_graph_output_dir: "/tmp/flamegraphs".to_string(),
        }
    }
}

/// Memory profiling configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryProfilingConfig {
    /// Enable memory profiling
    pub enabled: bool,
    /// Sampling interval in milliseconds
    pub sampling_interval_ms: u64,
    /// Track allocations above this size threshold (bytes)
    pub allocation_threshold_bytes: usize,
    /// Enable memory leak detection
    pub enable_leak_detection: bool,
    /// Leak detection sensitivity (0.0-1.0)
    pub leak_detection_sensitivity: f64,
    /// Maximum memory usage tracking history
    pub max_memory_history: usize,
}

impl Default for MemoryProfilingConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            sampling_interval_ms: 1000,
            allocation_threshold_bytes: 1024, // 1KB
            enable_leak_detection: true,
            leak_detection_sensitivity: 0.05,
            max_memory_history: 1000,
        }
    }
}

/// Bottleneck detection configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BottleneckDetectionConfig {
    /// Enable bottleneck detection
    pub enabled: bool,
    /// CPU usage threshold for bottleneck detection (%)
    pub cpu_threshold_percent: f64,
    /// Memory usage threshold for bottleneck detection (%)
    pub memory_threshold_percent: f64,
    /// Queue depth threshold for bottleneck detection
    pub queue_depth_threshold: u64,
    /// Database query time threshold (ms)
    pub db_query_threshold_ms: f64,
    /// Network I/O threshold (bytes/sec)
    pub network_io_threshold_bytes_per_sec: u64,
    /// Thread contention threshold
    pub thread_contention_threshold: u32,
    /// Detection interval in seconds
    pub detection_interval_seconds: u64,
}

impl Default for BottleneckDetectionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            cpu_threshold_percent: 80.0,
            memory_threshold_percent: 85.0,
            queue_depth_threshold: 100,
            db_query_threshold_ms: 1000.0,
            network_io_threshold_bytes_per_sec: 100_000_000, // 100MB/s
            thread_contention_threshold: 10,
            detection_interval_seconds: 30,
        }
    }
}

/// Scaling analysis configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScalingAnalysisConfig {
    /// Enable scaling analysis
    pub enabled: bool,
    /// CPU scaling threshold (%)
    pub cpu_scaling_threshold_percent: f64,
    /// Memory scaling threshold (%)
    pub memory_scaling_threshold_percent: f64,
    /// Queue depth scaling threshold
    pub queue_scaling_threshold: u64,
    /// Analysis window in minutes
    pub analysis_window_minutes: u64,
    /// Minimum data points for analysis
    pub min_data_points: usize,
    /// Scaling recommendation confidence threshold
    pub confidence_threshold: f64,
}

impl Default for ScalingAnalysisConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            cpu_scaling_threshold_percent: 70.0,
            memory_scaling_threshold_percent: 75.0,
            queue_scaling_threshold: 50,
            analysis_window_minutes: 60,
            min_data_points: 10,
            confidence_threshold: 0.8,
        }
    }
}

/// Performance analysis configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceAnalysisConfig {
    /// Enable performance analysis
    pub enabled: bool,
    /// Trend analysis window in hours
    pub trend_analysis_window_hours: u64,
    /// Seasonal pattern detection window in days
    pub seasonal_detection_window_days: u32,
    /// Performance regression threshold (%)
    pub regression_threshold_percent: f64,
    /// Anomaly detection sensitivity (0.0-1.0)
    pub anomaly_detection_sensitivity: f64,
    /// Comparative analysis baseline period in days
    pub baseline_period_days: u32,
}

impl Default for PerformanceAnalysisConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            trend_analysis_window_hours: 24,
            seasonal_detection_window_days: 7,
            regression_threshold_percent: 10.0,
            anomaly_detection_sensitivity: 0.05,
            baseline_period_days: 7,
        }
    }
}

/// Data storage configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataStorageConfig {
    /// Maximum number of profiling snapshots to keep in memory
    pub max_snapshots: usize,
    /// Archive snapshots older than this many hours
    pub archive_after_hours: u64,
    /// Data retention period in days
    pub retention_days: u32,
    /// Enable data compression
    pub enable_compression: bool,
    /// Compression level (1-9)
    pub compression_level: u32,
    /// Storage directory for archived data
    pub storage_directory: String,
}

impl Default for DataStorageConfig {
    fn default() -> Self {
        Self {
            max_snapshots: 1000,
            archive_after_hours: 24,
            retention_days: 30,
            enable_compression: true,
            compression_level: 6,
            storage_directory: "/tmp/profiling_data".to_string(),
        }
    }
}

/// Export configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExportConfig {
    /// Enable data export
    pub enabled: bool,
    /// Export formats
    pub formats: Vec<ExportFormat>,
    /// Export interval in minutes
    pub export_interval_minutes: u64,
    /// Export directory
    pub export_directory: String,
    /// Maximum export file size in MB
    pub max_file_size_mb: u64,
}

impl Default for ExportConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            formats: vec![ExportFormat::JSON, ExportFormat::CSV],
            export_interval_minutes: 60,
            export_directory: "/tmp/profiling_exports".to_string(),
            max_file_size_mb: 100,
        }
    }
}

/// Export format enumeration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExportFormat {
    /// JSON format
    JSON,
    /// CSV format
    CSV,
    /// Prometheus format
    Prometheus,
    /// InfluxDB format
    InfluxDB,
    /// Parquet format
    Parquet,
}

/// API configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiConfig {
    /// Enable profiling API
    pub enabled: bool,
    /// API base path
    pub base_path: String,
    /// Enable real-time streaming
    pub enable_streaming: bool,
    /// Streaming update interval in seconds
    pub streaming_interval_seconds: u64,
    /// Maximum concurrent connections
    pub max_concurrent_connections: usize,
    /// API authentication required
    pub require_authentication: bool,
}

impl Default for ApiConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            base_path: "/api/profiling".to_string(),
            enable_streaming: true,
            streaming_interval_seconds: 5,
            max_concurrent_connections: 100,
            require_authentication: false,
        }
    }
}

/// Alerting integration configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertingIntegrationConfig {
    /// Enable alerting integration
    pub enabled: bool,
    /// Performance alert thresholds
    pub alert_thresholds: AlertThresholds,
    /// Alert cooldown period in minutes
    pub alert_cooldown_minutes: u64,
    /// Maximum alerts per hour
    pub max_alerts_per_hour: u32,
}

impl Default for AlertingIntegrationConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            alert_thresholds: AlertThresholds::default(),
            alert_cooldown_minutes: 15,
            max_alerts_per_hour: 10,
        }
    }
}

/// Alert thresholds
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertThresholds {
    /// Critical CPU usage threshold (%)
    pub critical_cpu_percent: f64,
    /// Warning CPU usage threshold (%)
    pub warning_cpu_percent: f64,
    /// Critical memory usage threshold (%)
    pub critical_memory_percent: f64,
    /// Warning memory usage threshold (%)
    pub warning_memory_percent: f64,
    /// Critical queue depth threshold
    pub critical_queue_depth: u64,
    /// Warning queue depth threshold
    pub warning_queue_depth: u64,
    /// Critical response time threshold (ms)
    pub critical_response_time_ms: f64,
    /// Warning response time threshold (ms)
    pub warning_response_time_ms: f64,
}

impl Default for AlertThresholds {
    fn default() -> Self {
        Self {
            critical_cpu_percent: 90.0,
            warning_cpu_percent: 80.0,
            critical_memory_percent: 95.0,
            warning_memory_percent: 85.0,
            critical_queue_depth: 1000,
            warning_queue_depth: 500,
            critical_response_time_ms: 5000.0,
            warning_response_time_ms: 2000.0,
        }
    }
}