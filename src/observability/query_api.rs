//! # Unified Observability Query API
//!
//! This module provides a comprehensive API for querying observability data
//! across metrics, traces, logs, alerts, and performance data with advanced
//! filtering, aggregation, and real-time capabilities.

use crate::api::{ApiResponse, PaginationParams};
use crate::observability::{ObservabilityManager, custom_metrics::*};
use axum::{
    extract::{Path, Query, State, WebSocketUpgrade},
    http::StatusCode,
    response::Json,
    routing::{get, post},
    Router,
};
use chrono::{DateTime, Utc};
use futures_util::{SinkExt, StreamExt};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use uuid::Uuid;

/// Query API configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryApiConfig {
    /// Enable query caching
    pub enable_caching: bool,
    /// Cache TTL in seconds
    pub cache_ttl_seconds: u64,
    /// Maximum concurrent queries
    pub max_concurrent_queries: usize,
    /// Query timeout in seconds
    pub query_timeout_seconds: u64,
    /// Enable real-time streaming
    pub enable_realtime: bool,
    /// WebSocket heartbeat interval
    pub websocket_heartbeat_seconds: u64,
}

/// Unified observability query request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ObservabilityQuery {
    /// Query ID (auto-generated if not provided)
    pub id: Option<String>,
    /// Data types to query
    pub data_types: Vec<DataType>,
    /// Time range for the query
    pub time_range: TimeRange,
    /// Filters to apply
    pub filters: Vec<QueryFilter>,
    /// Aggregation operations
    pub aggregations: Vec<Aggregation>,
    /// Grouping fields
    pub group_by: Vec<String>,
    /// Sorting configuration
    pub sort: Option<SortConfig>,
    /// Pagination
    pub pagination: Option<PaginationParams>,
    /// Query options
    pub options: QueryOptions,
}

/// Data types available for querying
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum DataType {
    Metrics,
    Traces,
    Logs,
    Alerts,
    Performance,
    Security,
}

/// Time range specification
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimeRange {
    /// Start time (ISO 8601 or relative like "1h", "30m", "1d")
    pub start: String,
    /// End time (ISO 8601 or relative)
    pub end: String,
    /// Timezone (IANA timezone name)
    pub timezone: Option<String>,
}

/// Query filter
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryFilter {
    /// Field to filter on
    pub field: String,
    /// Filter operator
    pub operator: FilterOperator,
    /// Filter value
    pub value: serde_json::Value,
    /// Logical operator for combining filters
    pub logical_op: Option<LogicalOperator>,
}

/// Filter operators
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum FilterOperator {
    Eq,
    Ne,
    Gt,
    Gte,
    Lt,
    Lte,
    Contains,
    NotContains,
    Regex,
    In,
    NotIn,
    Exists,
    NotExists,
}

/// Logical operators for combining filters
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum LogicalOperator {
    And,
    Or,
}

/// Aggregation operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Aggregation {
    /// Field to aggregate
    pub field: String,
    /// Aggregation function
    pub function: AggregationFunction,
    /// Alias for the aggregated field
    pub alias: Option<String>,
}

/// Aggregation functions
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum AggregationFunction {
    Count,
    Sum,
    Avg,
    Min,
    Max,
    Percentile(f64),
    Rate,
    Increase,
}

/// Sort configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SortConfig {
    /// Field to sort by
    pub field: String,
    /// Sort order
    pub order: crate::api::SortOrder,
}

/// Query options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryOptions {
    /// Include metadata in response
    pub include_metadata: bool,
    /// Include statistics in response
    pub include_stats: bool,
    /// Timeout override
    pub timeout_seconds: Option<u64>,
    /// Result format
    pub format: ResultFormat,
    /// Enable caching for this query
    pub enable_cache: Option<bool>,
}

/// Result format
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum ResultFormat {
    Json,
    Csv,
    Prometheus,
    InfluxDb,
}

/// Unified query response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryResponse {
    /// Query ID
    pub query_id: String,
    /// Query execution time in milliseconds
    pub execution_time_ms: u64,
    /// Total number of results
    pub total_results: u64,
    /// Results data
    pub data: Vec<QueryResult>,
    /// Metadata
    pub metadata: Option<QueryMetadata>,
    /// Statistics
    pub statistics: Option<QueryStatistics>,
    /// Pagination info
    pub pagination: Option<PaginationInfo>,
}

/// Individual query result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryResult {
    /// Timestamp
    pub timestamp: DateTime<Utc>,
    /// Data type
    pub data_type: DataType,
    /// Labels/tags
    pub labels: HashMap<String, String>,
    /// Metric values
    pub values: HashMap<String, serde_json::Value>,
    /// Raw data (optional)
    pub raw_data: Option<serde_json::Value>,
}

/// Query metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryMetadata {
    /// Data sources queried
    pub data_sources: Vec<String>,
    /// Query execution plan
    pub execution_plan: String,
    /// Cache hit/miss
    pub cache_status: Option<String>,
    /// Warnings
    pub warnings: Vec<String>,
}

/// Query statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryStatistics {
    /// Number of data points processed
    pub data_points_processed: u64,
    /// Number of data sources queried
    pub data_sources_queried: usize,
    /// Memory usage in bytes
    pub memory_usage_bytes: u64,
    /// CPU time used
    pub cpu_time_ms: u64,
}

/// Query metrics for monitoring
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct QueryMetrics {
    pub execution_time_ms: f64,
    pub data_points_processed: usize,
    pub cache_hits: usize,
    pub cache_misses: usize,
    pub filters_applied: usize,
    pub aggregations_performed: usize,
}

/// Pagination information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationInfo {
    /// Current page
    pub page: u64,
    /// Items per page
    pub size: u64,
    /// Total pages
    pub total_pages: u64,
    /// Has next page
    pub has_next: bool,
    /// Has previous page
    pub has_prev: bool,
}

/// Query template
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryTemplate {
    /// Template ID
    pub id: String,
    /// Template name
    pub name: String,
    /// Description
    pub description: String,
    /// Template query
    pub query: ObservabilityQuery,
    /// Template variables
    pub variables: HashMap<String, TemplateVariable>,
    /// Created by
    pub created_by: String,
    /// Created at
    pub created_at: DateTime<Utc>,
    /// Updated at
    pub updated_at: DateTime<Utc>,
}

/// Template variable
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateVariable {
    /// Variable type
    pub var_type: VariableType,
    /// Default value
    pub default_value: Option<serde_json::Value>,
    /// Description
    pub description: String,
    /// Required
    pub required: bool,
}

/// Variable types
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum VariableType {
    String,
    Number,
    Boolean,
    DateTime,
    Duration,
    List,
}

/// Saved query
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SavedQuery {
    /// Query ID
    pub id: String,
    /// Query name
    pub name: String,
    /// Description
    pub description: String,
    /// Query definition
    pub query: ObservabilityQuery,
    /// Owner
    pub owner: String,
    /// Tags
    pub tags: Vec<String>,
    /// Created at
    pub created_at: DateTime<Utc>,
    /// Updated at
    pub updated_at: DateTime<Utc>,
    /// Last executed at
    pub last_executed_at: Option<DateTime<Utc>>,
    /// Execution count
    pub execution_count: u64,
}

/// Query execution context
#[derive(Debug, Clone)]
pub struct QueryContext {
    /// Query ID
    pub query_id: String,
    /// User ID
    pub user_id: String,
    /// User roles
    pub roles: Vec<String>,
    /// Start time
    pub start_time: DateTime<Utc>,
    /// Timeout
    pub timeout: std::time::Duration,
}

/// Query engine for processing observability queries
pub struct QueryEngine {
    observability_manager: Arc<ObservabilityManager>,
    config: QueryApiConfig,
    query_cache: Arc<RwLock<HashMap<String, CachedQueryResult>>>,
    active_queries: Arc<RwLock<HashMap<String, QueryContext>>>,
    templates: Arc<RwLock<HashMap<String, QueryTemplate>>>,
    saved_queries: Arc<RwLock<HashMap<String, SavedQuery>>>,
}

/// Cached query result
#[derive(Debug, Clone)]
pub struct CachedQueryResult {
    /// Query result
    pub result: QueryResponse,
    /// Cache timestamp
    pub cached_at: DateTime<Utc>,
    /// TTL in seconds
    pub ttl_seconds: u64,
}

impl QueryEngine {
    /// Create a new query engine
    pub fn new(observability_manager: Arc<ObservabilityManager>, config: QueryApiConfig) -> Self {
        Self {
            observability_manager,
            config,
            query_cache: Arc::new(RwLock::new(HashMap::new())),
            active_queries: Arc::new(RwLock::new(HashMap::new())),
            templates: Arc::new(RwLock::new(HashMap::new())),
            saved_queries: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Execute an observability query
    pub async fn execute_query(
        &self,
        query: ObservabilityQuery,
        context: QueryContext,
    ) -> Result<QueryResponse, Box<dyn std::error::Error + Send + Sync>> {
        let start_time = std::time::Instant::now();

        // Check cache first
        if self.config.enable_caching && query.options.enable_cache.unwrap_or(true) {
            if let Some(cached) = self.check_cache(&query).await {
                return Ok(cached);
            }
        }

        // Register active query
        {
            let mut active = self.active_queries.write().await;
            active.insert(context.query_id.clone(), context.clone());
        }

        // Execute query based on data types
        let mut results = Vec::new();
        let mut metadata = QueryMetadata {
            data_sources: Vec::new(),
            execution_plan: "parallel".to_string(),
            cache_status: Some("miss".to_string()),
            warnings: Vec::new(),
        };

        for data_type in &query.data_types {
            match data_type {
                DataType::Metrics => {
                    let metrics_results = self.query_metrics(&query, &context).await?;
                    results.extend(metrics_results);
                    metadata.data_sources.push("metrics".to_string());
                }
                DataType::Traces => {
                    let trace_results = self.query_traces(&query, &context).await?;
                    results.extend(trace_results);
                    metadata.data_sources.push("traces".to_string());
                }
                DataType::Logs => {
                    let log_results = self.query_logs(&query, &context).await?;
                    results.extend(log_results);
                    metadata.data_sources.push("logs".to_string());
                }
                DataType::Alerts => {
                    let alert_results = self.query_alerts(&query, &context).await?;
                    results.extend(alert_results);
                    metadata.data_sources.push("alerts".to_string());
                }
                DataType::Performance => {
                    let perf_results = self.query_performance(&query, &context).await?;
                    results.extend(perf_results);
                    metadata.data_sources.push("performance".to_string());
                }
                DataType::Security => {
                    let security_results = self.query_security(&query, &context).await?;
                    results.extend(security_results);
                    metadata.data_sources.push("security".to_string());
                }
            }
        }

        // Apply aggregations and grouping
        if !query.aggregations.is_empty() || !query.group_by.is_empty() {
            results = self.apply_aggregations_enhanced(results, &query).await?;
            results = self.apply_aggregations(results, &query).await?;
        }

        // Apply sorting
        if let Some(sort) = &query.sort {
            results.sort_by(|a, b| {
                match sort.order {
                    crate::api::SortOrder::Asc => {
                        // Simple string comparison for now
                        a.values.get(&sort.field)
                            .unwrap_or(&serde_json::Value::Null)
                            .to_string()
                            .cmp(&b.values.get(&sort.field)
                                .unwrap_or(&serde_json::Value::Null)
                                .to_string())
                    }
                    crate::api::SortOrder::Desc => {
                        b.values.get(&sort.field)
                            .unwrap_or(&serde_json::Value::Null)
                            .to_string()
                            .cmp(&a.values.get(&sort.field)
                                .unwrap_or(&serde_json::Value::Null)
                                .to_string())
                    }
                }
            });
        }

        // Apply pagination
        let total_results = results.len() as u64;
        let pagination_info = if let Some(pagination) = &query.pagination {
            let start_idx = (pagination.page * pagination.size) as usize;
            let end_idx = ((pagination.page + 1) * pagination.size) as usize;
            let total_pages = (total_results as f64 / pagination.size as f64).ceil() as u64;

            results = results.into_iter()
                .skip(start_idx)
                .take(pagination.size as usize)
                .collect();

            Some(PaginationInfo {
                page: pagination.page,
                size: pagination.size,
                total_pages,
                has_next: pagination.page + 1 < total_pages,
                has_prev: pagination.page > 0,
            })
        } else {
            None
        };

        let execution_time = start_time.elapsed().as_millis() as u64;

        let response = QueryResponse {
            query_id: context.query_id,
            execution_time_ms: execution_time,
            total_results,
            data: results,
            metadata: if query.options.include_metadata { Some(metadata) } else { None },
            statistics: if query.options.include_stats {
                Some(QueryStatistics {
                    data_points_processed: total_results,
                    data_sources_queried: query.data_types.len(),
                    memory_usage_bytes: 0, // TODO: Implement memory tracking
                    cpu_time_ms: execution_time,
                })
            } else { None },
            pagination: pagination_info,
        };

        // Cache the result
        if self.config.enable_caching && query.options.enable_cache.unwrap_or(true) {
            self.cache_result(&query, &response).await;
        }

        // Remove from active queries
        {
            let mut active = self.active_queries.write().await;
            active.remove(&context.query_id);
        }

        Ok(response)
    }

    /// Query metrics data
    async fn query_metrics(
        &self,
        query: &ObservabilityQuery,
        _context: &QueryContext,
    ) -> Result<Vec<QueryResult>, Box<dyn std::error::Error + Send + Sync>> {
        // Parse time range
        let time_range = self.parse_time_range(&query.time_range)?;
        let mut results = Vec::new();

        // Get license detection metrics
        let license_metrics = self.observability_manager.get_license_detection_metrics().await;
        results.push(QueryResult {
            timestamp: Utc::now(),
            data_type: DataType::Metrics,
            labels: HashMap::from([
                ("metric_type".to_string(), "license_detection".to_string()),
            ]),
            values: HashMap::from([
                ("total_predictions".to_string(), serde_json::json!(license_metrics.total_predictions)),
                ("precision".to_string(), serde_json::json!(license_metrics.precision)),
                ("recall".to_string(), serde_json::json!(license_metrics.recall)),
                ("f1_score".to_string(), serde_json::json!(license_metrics.f1_score)),
            ]),
            raw_data: Some(serde_json::to_value(&license_metrics)?),
        });

        // Get system health metrics
        let system_health = self.observability_manager.get_system_health_metrics().await;
        results.push(QueryResult {
            timestamp: Utc::now(),
            data_type: DataType::Metrics,
            labels: HashMap::from([
                ("metric_type".to_string(), "system_health".to_string()),
            ]),
            values: HashMap::from([
                ("cpu_usage_percent".to_string(), serde_json::json!(system_health.resource_utilization.cpu_usage_percent)),
                ("memory_usage_percent".to_string(), serde_json::json!(system_health.resource_utilization.memory_usage_percent)),
                ("service_availability".to_string(), serde_json::json!(system_health.service_availability)),
            ]),
            raw_data: Some(serde_json::to_value(&system_health)?),
        });

        // Get performance metrics
        let performance = self.observability_manager.get_performance_metrics().await;
        results.push(QueryResult {
            timestamp: Utc::now(),
            data_type: DataType::Metrics,
            labels: HashMap::from([
                ("metric_type".to_string(), "performance".to_string()),
            ]),
            values: HashMap::from([
                ("avg_processing_time_ms".to_string(), serde_json::json!(performance.avg_processing_time_ms)),
                ("request_queue_depth".to_string(), serde_json::json!(performance.request_queue_depth)),
            ]),
            raw_data: Some(serde_json::to_value(&performance)?),
        });

        // Apply time range filtering
        results.retain(|result| {
            result.timestamp >= time_range.start && result.timestamp <= time_range.end
        });

        Ok(results)
    }

    /// Query traces data
    async fn query_traces(
        &self,
        _query: &ObservabilityQuery,
        _context: &QueryContext,
    ) -> Result<Vec<QueryResult>, Box<dyn std::error::Error + Send + Sync>> {
        // TODO: Implement trace querying
        // For now, return empty results
        Ok(Vec::new())
    }

    /// Query logs data
    async fn query_logs(
        &self,
        _query: &ObservabilityQuery,
        _context: &QueryContext,
    ) -> Result<Vec<QueryResult>, Box<dyn std::error::Error + Send + Sync>> {
        // TODO: Implement log querying
        // For now, return empty results
        Ok(Vec::new())
    }

    /// Query alerts data
    async fn query_alerts(
        &self,
        _query: &ObservabilityQuery,
        _context: &QueryContext,
    ) -> Result<Vec<QueryResult>, Box<dyn std::error::Error + Send + Sync>> {
        let mut results = Vec::new();

        // Get alerts from the alerting system if available
        if let Some(alerting) = &self.observability_manager.alerting_framework() {
            // Get active alerts
            let active_alerts = alerting.alert_manager().get_active_alerts();

            for alert in active_alerts {
                results.push(QueryResult {
                    timestamp: alert.created_at,
                    data_type: DataType::Alerts,
                    labels: HashMap::from([
                        ("alert_id".to_string(), alert.id.clone()),
                        ("severity".to_string(), format!("{:?}", alert.severity).to_lowercase()),
                        ("category".to_string(), alert.category.as_str().to_string()),
                        ("status".to_string(), format!("{:?}", alert.status).to_lowercase()),
                        ("source".to_string(), alert.source.clone()),
                    ]),
                    values: HashMap::from([
                        ("count".to_string(), serde_json::json!(alert.count)),
                        ("acknowledged".to_string(), serde_json::json!(alert.acknowledged_at.is_some())),
                        ("resolved".to_string(), serde_json::json!(alert.resolved_at.is_some())),
                        ("silenced".to_string(), serde_json::json!(alert.silenced_until.is_some())),
                    ]),
                    raw_data: Some(serde_json::to_value(alert)?),
                });
            }

            // Get all alerts for historical data
            let all_alerts = alerting.alert_manager().get_alerts();
            for alert in all_alerts {
                if !results.iter().any(|r| r.labels.get("alert_id") == Some(&alert.id)) {
                    results.push(QueryResult {
                        timestamp: alert.created_at,
                        data_type: DataType::Alerts,
                        labels: HashMap::from([
                            ("alert_id".to_string(), alert.id.clone()),
                            ("severity".to_string(), format!("{:?}", alert.severity).to_lowercase()),
                            ("category".to_string(), alert.category.as_str().to_string()),
                            ("status".to_string(), format!("{:?}", alert.status).to_lowercase()),
                            ("source".to_string(), alert.source.clone()),
                        ]),
                        values: HashMap::from([
                            ("count".to_string(), serde_json::json!(alert.count)),
                            ("acknowledged".to_string(), serde_json::json!(alert.acknowledged_at.is_some())),
                            ("resolved".to_string(), serde_json::json!(alert.resolved_at.is_some())),
                            ("silenced".to_string(), serde_json::json!(alert.silenced_until.is_some())),
                        ]),
                        raw_data: Some(serde_json::to_value(alert)?),
                    });
                }
            }
        }

        Ok(results)
    }

    /// Query security data
    async fn query_security(
        &self,
        _query: &ObservabilityQuery,
        _context: &QueryContext,
    ) -> Result<Vec<QueryResult>, Box<dyn std::error::Error + Send + Sync>> {
        let mut results = Vec::new();

        // Get security metrics from the security monitoring system if available
        if let Some(security_monitoring) = &self.observability_manager.security_monitoring_system() {
            // Get current security metrics
            let current_metrics = security_monitoring.metrics_collector().get_current_metrics();

            // Authentication metrics
            results.push(QueryResult {
                timestamp: current_metrics.timestamp,
                data_type: DataType::Security,
                labels: HashMap::from([
                    ("metric_type".to_string(), "authentication".to_string()),
                    ("service".to_string(), "infinitium-signal".to_string()),
                ]),
                values: HashMap::from([
                    ("total_authentication_attempts".to_string(), serde_json::json!(current_metrics.authentication_metrics.total_authentication_attempts)),
                    ("successful_authentications".to_string(), serde_json::json!(current_metrics.authentication_metrics.successful_authentications)),
                    ("failed_authentications".to_string(), serde_json::json!(current_metrics.authentication_metrics.failed_authentications)),
                    ("authentication_success_rate".to_string(), serde_json::json!(current_metrics.authentication_metrics.authentication_success_rate)),
                    ("failed_auth_rate".to_string(), serde_json::json!(current_metrics.authentication_metrics.failed_auth_rate)),
                    ("brute_force_attempts_detected".to_string(), serde_json::json!(current_metrics.authentication_metrics.brute_force_attempts_detected)),
                    ("multi_factor_auth_usage".to_string(), serde_json::json!(current_metrics.authentication_metrics.multi_factor_auth_usage)),
                ]),
                raw_data: Some(serde_json::to_value(&current_metrics.authentication_metrics)?),
            });

            // Authorization metrics
            results.push(QueryResult {
                timestamp: current_metrics.timestamp,
                data_type: DataType::Security,
                labels: HashMap::from([
                    ("metric_type".to_string(), "authorization".to_string()),
                    ("service".to_string(), "infinitium-signal".to_string()),
                ]),
                values: HashMap::from([
                    ("total_authorization_requests".to_string(), serde_json::json!(current_metrics.authorization_metrics.total_authorization_requests)),
                    ("successful_authorizations".to_string(), serde_json::json!(current_metrics.authorization_metrics.successful_authorizations)),
                    ("failed_authorizations".to_string(), serde_json::json!(current_metrics.authorization_metrics.failed_authorizations)),
                    ("authorization_success_rate".to_string(), serde_json::json!(current_metrics.authorization_metrics.authorization_success_rate)),
                    ("privilege_escalation_attempts".to_string(), serde_json::json!(current_metrics.authorization_metrics.privilege_escalation_attempts)),
                    ("role_based_access_violations".to_string(), serde_json::json!(current_metrics.authorization_metrics.role_based_access_violations)),
                    ("resource_access_denials".to_string(), serde_json::json!(current_metrics.authorization_metrics.resource_access_denials)),
                ]),
                raw_data: Some(serde_json::to_value(&current_metrics.authorization_metrics)?),
            });

            // Threat detection metrics
            results.push(QueryResult {
                timestamp: current_metrics.timestamp,
                data_type: DataType::Security,
                labels: HashMap::from([
                    ("metric_type".to_string(), "threat_detection".to_string()),
                    ("service".to_string(), "infinitium-signal".to_string()),
                ]),
                values: HashMap::from([
                    ("threats_detected".to_string(), serde_json::json!(current_metrics.threat_detection_metrics.threats_detected)),
                    ("incidents_created".to_string(), serde_json::json!(current_metrics.threat_detection_metrics.incidents_created)),
                    ("active_incidents".to_string(), serde_json::json!(current_metrics.threat_detection_metrics.active_incidents)),
                    ("resolved_incidents".to_string(), serde_json::json!(current_metrics.threat_detection_metrics.resolved_incidents)),
                    ("false_positives".to_string(), serde_json::json!(current_metrics.threat_detection_metrics.false_positives)),
                    ("detection_accuracy".to_string(), serde_json::json!(current_metrics.threat_detection_metrics.detection_accuracy)),
                    ("average_response_time".to_string(), serde_json::json!(current_metrics.threat_detection_metrics.average_response_time)),
                ]),
                raw_data: Some(serde_json::to_value(&current_metrics.threat_detection_metrics)?),
            });

            // Compliance metrics
            results.push(QueryResult {
                timestamp: current_metrics.timestamp,
                data_type: DataType::Security,
                labels: HashMap::from([
                    ("metric_type".to_string(), "compliance".to_string()),
                    ("service".to_string(), "infinitium-signal".to_string()),
                ]),
                values: HashMap::from([
                    ("compliance_scans_performed".to_string(), serde_json::json!(current_metrics.compliance_metrics.compliance_scans_performed)),
                    ("compliance_violations_found".to_string(), serde_json::json!(current_metrics.compliance_metrics.compliance_violations_found)),
                    ("compliance_violations_resolved".to_string(), serde_json::json!(current_metrics.compliance_metrics.compliance_violations_resolved)),
                    ("compliance_score".to_string(), serde_json::json!(current_metrics.compliance_metrics.compliance_score)),
                    ("audit_trail_integrity_score".to_string(), serde_json::json!(current_metrics.compliance_metrics.audit_trail_integrity_score)),
                    ("data_retention_compliance_score".to_string(), serde_json::json!(current_metrics.compliance_metrics.data_retention_compliance_score)),
                ]),
                raw_data: Some(serde_json::to_value(&current_metrics.compliance_metrics)?),
            });

            // System health metrics
            results.push(QueryResult {
                timestamp: current_metrics.timestamp,
                data_type: DataType::Security,
                labels: HashMap::from([
                    ("metric_type".to_string(), "system_health".to_string()),
                    ("service".to_string(), "infinitium-signal".to_string()),
                ]),
                values: HashMap::from([
                    ("system_uptime".to_string(), serde_json::json!(current_metrics.system_health_metrics.system_uptime)),
                    ("security_service_availability".to_string(), serde_json::json!(current_metrics.system_health_metrics.security_service_availability)),
                    ("cpu_usage_percent".to_string(), serde_json::json!(current_metrics.system_health_metrics.resource_utilization.cpu_usage_percent)),
                    ("memory_usage_percent".to_string(), serde_json::json!(current_metrics.system_health_metrics.resource_utilization.memory_usage_percent)),
                    ("disk_usage_percent".to_string(), serde_json::json!(current_metrics.system_health_metrics.resource_utilization.disk_usage_percent)),
                    ("network_bandwidth_usage".to_string(), serde_json::json!(current_metrics.system_health_metrics.resource_utilization.network_bandwidth_usage)),
                    ("error_rate".to_string(), serde_json::json!(current_metrics.system_health_metrics.error_rate)),
                    ("performance_degradation_score".to_string(), serde_json::json!(current_metrics.system_health_metrics.performance_degradation_score)),
                ]),
                raw_data: Some(serde_json::to_value(&current_metrics.system_health_metrics)?),
            });

            // Incident response metrics
            results.push(QueryResult {
                timestamp: current_metrics.timestamp,
                data_type: DataType::Security,
                labels: HashMap::from([
                    ("metric_type".to_string(), "incident_response".to_string()),
                    ("service".to_string(), "infinitium-signal".to_string()),
                ]),
                values: HashMap::from([
                    ("average_detection_time".to_string(), serde_json::json!(current_metrics.incident_response_metrics.average_detection_time)),
                    ("average_response_time".to_string(), serde_json::json!(current_metrics.incident_response_metrics.average_response_time)),
                    ("average_resolution_time".to_string(), serde_json::json!(current_metrics.incident_response_metrics.average_resolution_time)),
                    ("automated_response_rate".to_string(), serde_json::json!(current_metrics.incident_response_metrics.automated_response_rate)),
                    ("manual_intervention_rate".to_string(), serde_json::json!(current_metrics.incident_response_metrics.manual_intervention_rate)),
                ]),
                raw_data: Some(serde_json::to_value(&current_metrics.incident_response_metrics)?),
            });

            // Get security health score
            if let Some(health_score) = security_monitoring.metrics_collector().get_latest_health_score() {
                results.push(QueryResult {
                    timestamp: health_score.last_updated,
                    data_type: DataType::Security,
                    labels: HashMap::from([
                        ("metric_type".to_string(), "health_score".to_string()),
                        ("service".to_string(), "infinitium-signal".to_string()),
                    ]),
                    values: HashMap::from([
                        ("overall_score".to_string(), serde_json::json!(health_score.overall_score)),
                        ("trend".to_string(), serde_json::json!(format!("{:?}", health_score.trend))),
                    ]),
                    raw_data: Some(serde_json::to_value(&health_score)?),
                });
            }
        }

        Ok(results)
    }

    /// Query alerts data
    async fn query_alerts(
        &self,
        _query: &ObservabilityQuery,
        _context: &QueryContext,
    ) -> Result<Vec<QueryResult>, Box<dyn std::error::Error + Send + Sync>> {
        // TODO: Implement alert querying
        // For now, return empty results
        Ok(Vec::new())
    }

    /// Query performance data
    async fn query_performance(
        &self,
        _query: &ObservabilityQuery,
        _context: &QueryContext,
    ) -> Result<Vec<QueryResult>, Box<dyn std::error::Error + Send + Sync>> {
        // TODO: Implement performance data querying
        // For now, return empty results
        Ok(Vec::new())
    }

    /// Query security data
    async fn query_security(
        &self,
        _query: &ObservabilityQuery,
        _context: &QueryContext,
    ) -> Result<Vec<QueryResult>, Box<dyn std::error::Error + Send + Sync>> {
        // TODO: Implement security data querying
        // For now, return empty results
        Ok(Vec::new())
    }

    /// Apply aggregations and grouping
    async fn apply_aggregations(
        &self,
        results: Vec<QueryResult>,
        query: &ObservabilityQuery,
    ) -> Result<Vec<QueryResult>, Box<dyn std::error::Error + Send + Sync>> {
        // TODO: Implement aggregation logic
        // For now, return results as-is
        Ok(results)
    }

    /// Check query cache
    async fn check_cache(&self, query: &ObservabilityQuery) -> Option<QueryResponse> {
        let cache_key = self.generate_cache_key(query);
        let cache = self.query_cache.read().await;

        if let Some(cached) = cache.get(&cache_key) {
            let elapsed = Utc::now().signed_duration_since(cached.cached_at);
            if elapsed.num_seconds() < cached.ttl_seconds as i64 {
                let mut result = cached.result.clone();
                if let Some(metadata) = &mut result.metadata {
                    metadata.cache_status = Some("hit".to_string());
                }
                return Some(result);
            }
        }
        None
    }

    /// Cache query result
    async fn cache_result(&self, query: &ObservabilityQuery, result: &QueryResponse) {
        let cache_key = self.generate_cache_key(query);
        let cached = CachedQueryResult {
            result: result.clone(),
            cached_at: Utc::now(),
            ttl_seconds: self.config.cache_ttl_seconds,
        };

        let mut cache = self.query_cache.write().await;
        cache.insert(cache_key, cached);
    }

    /// Generate cache key for query
    fn generate_cache_key(&self, query: &ObservabilityQuery) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        query.hash(&mut hasher);
        format!("query_{}", hasher.finish())
    }

    /// Save query template
    pub async fn save_template(&self, template: QueryTemplate) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut templates = self.templates.write().await;
        templates.insert(template.id.clone(), template);
        Ok(())
    }

    /// Get query template
    pub async fn get_template(&self, template_id: &str) -> Option<QueryTemplate> {
        let templates = self.templates.read().await;
        templates.get(template_id).cloned()
    }

    /// List query templates
    pub async fn list_templates(&self) -> Vec<QueryTemplate> {
        let templates = self.templates.read().await;
        templates.values().cloned().collect()
    }

    /// Save query
    pub async fn save_query(&self, query: SavedQuery) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut saved = self.saved_queries.write().await;
        saved.insert(query.id.clone(), query);
        Ok(())
    }

    /// Get saved query
    pub async fn get_saved_query(&self, query_id: &str) -> Option<SavedQuery> {
        let saved = self.saved_queries.read().await;
        saved.get(query_id).cloned()
    }

    /// List saved queries
    pub async fn list_saved_queries(&self) -> Vec<SavedQuery> {
        let saved = self.saved_queries.read().await;
        saved.values().cloned().collect()
    }
}

/// Query API server
pub struct QueryApiServer {
    config: QueryApiConfig,
    query_engine: Arc<QueryEngine>,
}

impl QueryApiServer {
    /// Create a new query API server
    pub fn new(config: QueryApiConfig, observability_manager: Arc<ObservabilityManager>) -> Self {
        let query_engine = Arc::new(QueryEngine::new(observability_manager, config.clone()));
        Self {
            config,
            query_engine,
        }
    }

    /// Create API routes
    pub fn create_routes(&self) -> Router {
        let state = QueryApiState {
            config: self.config.clone(),
            query_engine: self.query_engine.clone(),
        };

        Router::new()
            .route("/query", post(execute_query))
            .route("/query/templates", get(list_templates).post(create_template))
            .route("/query/templates/:template_id", get(get_template).put(update_template).delete(delete_template))
            .route("/query/saved", get(list_saved_queries).post(save_query))
            .route("/query/saved/:query_id", get(get_saved_query).put(update_saved_query).delete(delete_saved_query))
            .route("/query/stream", get(websocket_stream))
            .route("/query/stats", get(query_stats))
            .with_state(state)
    }
}

/// Shared state for query API handlers
#[derive(Clone)]
struct QueryApiState {
    config: QueryApiConfig,
    query_engine: Arc<QueryEngine>,
}

/// Execute observability query
async fn execute_query(
    State(state): State<QueryApiState>,
    Json(query): Json<ObservabilityQuery>,
) -> Result<Json<ApiResponse<QueryResponse>>, StatusCode> {
    // Generate query ID if not provided
    let query_id = query.id.clone().unwrap_or_else(|| Uuid::new_v4().to_string());

    // Create query context
    let context = QueryContext {
        query_id: query_id.clone(),
        user_id: "anonymous".to_string(), // TODO: Get from authentication
        roles: vec!["user".to_string()], // TODO: Get from authentication
        start_time: Utc::now(),
        timeout: std::time::Duration::from_secs(state.config.query_timeout_seconds),
    };

    // Execute query
    match state.query_engine.execute_query(query, context).await {
        Ok(response) => Ok(Json(ApiResponse::success(response))),
        Err(e) => {
            tracing::error!("Query execution failed: {}", e);
            Ok(Json(ApiResponse::error(format!("Query execution failed: {}", e))))
        }
    }
}

/// List query templates
async fn list_templates(
    State(state): State<QueryApiState>,
) -> Json<ApiResponse<Vec<QueryTemplate>>> {
    let templates = state.query_engine.list_templates().await;
    Json(ApiResponse::success(templates))
}

/// Create query template
async fn create_template(
    State(state): State<QueryApiState>,
    Json(template): Json<QueryTemplate>,
) -> Json<ApiResponse<QueryTemplate>> {
    match state.query_engine.save_template(template.clone()).await {
        Ok(_) => Json(ApiResponse::success(template)),
        Err(e) => Json(ApiResponse::error(format!("Failed to save template: {}", e))),
    }
}

/// Get query template
async fn get_template(
    State(state): State<QueryApiState>,
    Path(template_id): Path<String>,
) -> Json<ApiResponse<QueryTemplate>> {
    match state.query_engine.get_template(&template_id) {
        Some(template) => Json(ApiResponse::success(template)),
        None => Json(ApiResponse::error("Template not found".to_string())),
    }
}

/// Update query template
async fn update_template(
    State(state): State<QueryApiState>,
    Path(template_id): Path<String>,
    Json(template): Json<QueryTemplate>,
) -> Json<ApiResponse<QueryTemplate>> {
    // TODO: Implement template update
    Json(ApiResponse::error("Template update not implemented".to_string()))
}

/// Delete query template
async fn delete_template(
    State(state): State<QueryApiState>,
    Path(template_id): Path<String>,
) -> Json<ApiResponse<String>> {
    // TODO: Implement template deletion
    Json(ApiResponse::error("Template deletion not implemented".to_string()))
}

/// List saved queries
async fn list_saved_queries(
    State(state): State<QueryApiState>,
) -> Json<ApiResponse<Vec<SavedQuery>>> {
    let queries = state.query_engine.list_saved_queries().await;
    Json(ApiResponse::success(queries))
}

/// Save query
async fn save_query(
    State(state): State<QueryApiState>,
    Json(query): Json<SavedQuery>,
) -> Json<ApiResponse<SavedQuery>> {
    match state.query_engine.save_query(query.clone()).await {
        Ok(_) => Json(ApiResponse::success(query)),
        Err(e) => Json(ApiResponse::error(format!("Failed to save query: {}", e))),
    }
}

/// Get saved query
async fn get_saved_query(
    State(state): State<QueryApiState>,
    Path(query_id): Path<String>,
) -> Json<ApiResponse<SavedQuery>> {
    match state.query_engine.get_saved_query(&query_id) {
        Some(query) => Json(ApiResponse::success(query)),
        None => Json(ApiResponse::error("Saved query not found".to_string())),
    }
}

/// Update saved query
async fn update_saved_query(
    State(state): State<QueryApiState>,
    Path(query_id): Path<String>,
    Json(query): Json<SavedQuery>,
) -> Json<ApiResponse<SavedQuery>> {
    // TODO: Implement saved query update
    Json(ApiResponse::error("Saved query update not implemented".to_string()))
}

/// Delete saved query
async fn delete_saved_query(
    State(state): State<QueryApiState>,
    Path(query_id): Path<String>,
) -> Json<ApiResponse<String>> {
    // TODO: Implement saved query deletion
    Json(ApiResponse::error("Saved query deletion not implemented".to_string()))
}

/// WebSocket streaming endpoint
async fn websocket_stream(
    State(state): State<QueryApiState>,
    ws: WebSocketUpgrade,
) -> axum::response::Response {
    ws.on_upgrade(move |socket| handle_websocket_stream(socket, state.query_engine.clone()))
}

/// Handle WebSocket connection for real-time query streaming
async fn handle_websocket_stream(
    socket: axum::extract::ws::WebSocket,
    _query_engine: Arc<QueryEngine>,
) {
    let (sender, receiver) = socket.split();
    let (tx, rx) = tokio::sync::mpsc::unbounded_channel();

    // Handle incoming messages
    let receive_task = tokio::spawn(async move {
        let mut receiver = receiver;
        while let Some(msg) = receiver.next().await {
            match msg {
                Ok(axum::extract::ws::Message::Close(_)) => break,
                Ok(axum::extract::ws::Message::Text(text)) => {
                    // TODO: Parse and execute streaming query
                    tracing::info!("Received WebSocket message: {}", text);
                }
                Ok(_) => {} // Handle other messages if needed
                Err(_) => break,
            }
        }
    });

    // Handle outgoing messages
    let send_task = tokio::spawn(async move {
        let mut rx = tokio_stream::wrappers::UnboundedReceiverStream::new(rx);
        let mut sender = sender;
        while let Some(msg) = rx.next().await {
            if sender.send(axum::extract::ws::Message::Text(msg)).await.is_err() {
                break;
            }
        }
    });

    // Wait for either task to complete
    tokio::select! {
        _ = receive_task => {}
        _ = send_task => {}
    }
}

/// Get query statistics
async fn query_stats(
    State(state): State<QueryApiState>,
) -> Json<ApiResponse<serde_json::Value>> {
    // TODO: Implement query statistics
    let stats = serde_json::json!({
        "active_queries": 0,
        "cached_queries": 0,
        "total_queries_executed": 0,
        "average_execution_time_ms": 0,
        "cache_hit_rate": 0.0
    });
    Json(ApiResponse::success(stats))
}

/// Default configuration for query API
impl Default for QueryApiConfig {
    fn default() -> Self {
        Self {
            enable_caching: true,
            cache_ttl_seconds: 300, // 5 minutes
            max_concurrent_queries: 10,
            query_timeout_seconds: 30,
            enable_realtime: true,
            websocket_heartbeat_seconds: 30,
        }
    }
}

impl std::hash::Hash for ObservabilityQuery {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        self.data_types.hash(state);
        self.time_range.start.hash(state);
        self.time_range.end.hash(state);
        self.filters.hash(state);
        self.aggregations.hash(state);
        self.group_by.hash(state);
        if let Some(sort) = &self.sort {
            sort.field.hash(state);
            match sort.order {
                crate::api::SortOrder::Asc => 0.hash(state),
                crate::api::SortOrder::Desc => 1.hash(state),
            }
        }
        if let Some(pagination) = &self.pagination {
            pagination.page.hash(state);
            pagination.size.hash(state);
        }
    }
}

impl std::hash::Hash for DataType {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        match self {
            DataType::Metrics => 0.hash(state),
            DataType::Traces => 1.hash(state),
            DataType::Logs => 2.hash(state),
            DataType::Alerts => 3.hash(state),
            DataType::Performance => 4.hash(state),
            DataType::Security => 5.hash(state),
        }
    }
}

impl std::hash::Hash for QueryFilter {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        self.field.hash(state);
        self.value.to_string().hash(state);
    }
}

impl std::hash::Hash for Aggregation {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        self.field.hash(state);
        self.function.hash(state);
        if let Some(alias) = &self.alias {
            alias.hash(state);
        }
    }
}

impl std::hash::Hash for AggregationFunction {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        match self {
            AggregationFunction::Count => 0.hash(state),
            AggregationFunction::Sum => 1.hash(state),
            AggregationFunction::Avg => 2.hash(state),
            AggregationFunction::Min => 3.hash(state),
            AggregationFunction::Max => 4.hash(state),
            AggregationFunction::Percentile(p) => {
                5.hash(state);
                (*p as u64).hash(state);
            }
            AggregationFunction::Rate => 6.hash(state),
            AggregationFunction::Increase => 7.hash(state),
        }
    }
}

impl QueryEngine {
    /// Parse time range from query
    fn parse_time_range(&self, time_range: &TimeRange) -> Result<std::ops::Range<DateTime<Utc>>, Box<dyn std::error::Error + Send + Sync>> {
        let start = self.parse_time_string(&time_range.start)?;
        let end = self.parse_time_string(&time_range.end)?;
        Ok(start..end)
    }

    /// Get query performance statistics
    async fn get_performance_stats(&self) -> QueryPerformanceStats {
        let active_queries = self.active_queries.read().await.len();
        let cache_size = self.query_cache.read().await.len();

        QueryPerformanceStats {
            active_queries,
            cache_size,
            cache_hit_rate: 0.0, // TODO: Implement cache hit rate calculation
            average_query_time_ms: 0.0, // TODO: Implement average query time tracking
            total_queries_executed: 0, // TODO: Implement query count tracking
            concurrent_query_limit: self.config.max_concurrent_queries,
        }
    }

    /// Clear query cache
    async fn clear_cache(&self) {
        let mut cache = self.query_cache.write().await;
        cache.clear();
    }

    /// Get cache statistics
    async fn get_cache_stats(&self) -> CacheStats {
        let cache = self.query_cache.read().await;
        let total_entries = cache.len();
        let total_size_bytes = cache.values()
            .map(|cached| std::mem::size_of_val(cached) + cached.result.data.len() * std::mem::size_of::<QueryResult>())
            .sum();

        CacheStats {
            total_entries,
            total_size_bytes,
            average_entry_size_bytes: if total_entries > 0 { total_size_bytes / total_entries } else { 0 },
            cache_hit_ratio: 0.0, // TODO: Implement hit ratio calculation
        }
    }

    /// Optimize query execution plan
    fn optimize_query_plan(&self, query: &ObservabilityQuery) -> QueryExecutionPlan {
        let mut plan = QueryExecutionPlan {
            data_sources: query.data_types.clone(),
            execution_order: Vec::new(),
            parallel_groups: Vec::new(),
            estimated_cost: 0.0,
            optimization_hints: Vec::new(),
        };

        // Determine execution order based on data source complexity
        let mut ordered_sources = query.data_types.clone();
        ordered_sources.sort_by(|a, b| {
            // Metrics are fastest, then alerts, then security, then logs/traces
            let a_priority = match a {
                DataType::Metrics => 1,
                DataType::Alerts => 2,
                DataType::Security => 3,
                DataType::Logs => 4,
                DataType::Traces => 4,
                DataType::Performance => 2,
            };
            let b_priority = match b {
                DataType::Metrics => 1,
                DataType::Alerts => 2,
                DataType::Security => 3,
                DataType::Logs => 4,
                DataType::Traces => 4,
                DataType::Performance => 2,
            };
            a_priority.cmp(&b_priority)
        });

        plan.execution_order = ordered_sources;

        // Group parallel execution
        let mut current_group = Vec::new();
        for source in &plan.execution_order {
            match source {
                DataType::Metrics | DataType::Alerts => {
                    // These can run in parallel
                    current_group.push(source.clone());
                }
                _ => {
                    // Others run sequentially
                    if !current_group.is_empty() {
                        plan.parallel_groups.push(current_group.clone());
                        current_group.clear();
                    }
                    plan.parallel_groups.push(vec![source.clone()]);
                }
            }
        }
        if !current_group.is_empty() {
            plan.parallel_groups.push(current_group);
        }

        // Estimate cost
        plan.estimated_cost = query.data_types.len() as f64 * 10.0; // Simple cost estimation

        // Add optimization hints
        if query.data_types.len() > 3 {
            plan.optimization_hints.push("Consider splitting query into smaller chunks".to_string());
        }
        if query.aggregations.len() > 5 {
            plan.optimization_hints.push("Complex aggregations detected - consider simplifying".to_string());
        }

        plan
    }
}

/// Query performance statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryPerformanceStats {
    pub active_queries: usize,
    pub cache_size: usize,
    pub cache_hit_rate: f64,
    pub average_query_time_ms: f64,
    pub total_queries_executed: u64,
    pub concurrent_query_limit: usize,
}

/// Cache statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStats {
    pub total_entries: usize,
    pub total_size_bytes: usize,
    pub average_entry_size_bytes: usize,
    pub cache_hit_ratio: f64,
}

/// Query execution plan
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryExecutionPlan {
    pub data_sources: Vec<DataType>,
    pub execution_order: Vec<DataType>,
    pub parallel_groups: Vec<Vec<DataType>>,
    pub estimated_cost: f64,
    pub optimization_hints: Vec<String>,
}

/// Enhanced query execution with performance monitoring
impl QueryEngine {
    /// Execute query with performance monitoring
    pub async fn execute_query_with_monitoring(
        &self,
        query: ObservabilityQuery,
        context: QueryContext,
    ) -> Result<MonitoredQueryResponse, Box<dyn std::error::Error + Send + Sync>> {
        let start_time = std::time::Instant::now();

        // Create execution plan
        let execution_plan = self.optimize_query_plan(&query);

        // Execute the query
        let results = self.execute_query_plan(execution_plan, context).await?;

        let duration = start_time.elapsed();

        Ok(MonitoredQueryResponse {
            results,
            execution_time: duration,
            metrics: QueryMetrics::default(),
        })
    }

    /// Export query results in specified format
    pub async fn export_results(
        &self,
        results: &[QueryResult],
        format: &ResultFormat,
    ) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        match format {
            ResultFormat::Json => self.export_as_json(results),
            ResultFormat::Csv => self.export_as_csv(results),
            ResultFormat::Prometheus => self.export_as_prometheus(results),
            ResultFormat::InfluxDb => self.export_as_influxdb(results),
        }
    }

    /// Export results as JSON
    fn export_as_json(&self, results: &[QueryResult]) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let export_data = serde_json::json!({
            "results": results,
            "export_timestamp": chrono::Utc::now(),
            "total_results": results.len(),
            "format": "json"
        });
        Ok(serde_json::to_string_pretty(&export_data)?)
    }

    /// Export results as CSV
    fn export_as_csv(&self, results: &[QueryResult]) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        if results.is_empty() {
            return Ok("timestamp,data_type,labels,values\n".to_string());
        }

        let mut csv_output = String::new();

        // CSV header
        csv_output.push_str("timestamp,data_type");

        // Collect all unique label keys and value keys
        let mut all_label_keys = std::collections::HashSet::new();
        let mut all_value_keys = std::collections::HashSet::new();

        for result in results {
            all_label_keys.extend(result.labels.keys().cloned());
            all_value_keys.extend(result.values.keys().cloned());
        }

        let label_keys: Vec<String> = all_label_keys.into_iter().collect();
        let value_keys: Vec<String> = all_value_keys.into_iter().collect();

        // Add label columns
        for key in &label_keys {
            csv_output.push_str(&format!(",label_{}", key));
        }

        // Add value columns
        for key in &value_keys {
            csv_output.push_str(&format!(",value_{}", key));
        }

        csv_output.push('\n');

        // CSV data rows
        for result in results {
            csv_output.push_str(&result.timestamp.to_rfc3339());
            csv_output.push(',');
            csv_output.push_str(&format!("{:?}", result.data_type).to_lowercase());

            // Label values
            for key in &label_keys {
                csv_output.push(',');
                if let Some(value) = result.labels.get(key) {
                    csv_output.push_str(&format!("\"{}\"", value.replace("\"", "\"\"")));
                }
            }

            // Value data
            for key in &value_keys {
                csv_output.push(',');
                if let Some(value) = result.values.get(key) {
                    csv_output.push_str(&value.to_string());
                }
            }

            csv_output.push('\n');
        }

        Ok(csv_output)
    }

    /// Export results as Prometheus format
    fn export_as_prometheus(&self, results: &[QueryResult]) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let mut prometheus_output = String::new();
        prometheus_output.push_str("# Infinitium Signal Observability Query Results\n");
        prometheus_output.push_str(&format!("# Export timestamp: {}\n", chrono::Utc::now().to_rfc3339()));

        for result in results {
            for (key, value) in &result.values {
                if let Some(num_value) = value.as_f64() {
                    // Create Prometheus metric name
                    let metric_name = format!("infinitium_{}_{}",
                        format!("{:?}", result.data_type).to_lowercase(),
                        key.replace("-", "_")
                    );

                    // Add labels
                    let mut labels = Vec::new();
                    for (label_key, label_value) in &result.labels {
                        labels.push(format!("{}=\"{}\"", label_key, label_value));
                    }

                    let label_str = if labels.is_empty() {
                        String::new()
                    } else {
                        format!("{{{}}}", labels.join(","))
                    };

                    prometheus_output.push_str(&format!("{}{{timestamp=\"{}\"{}}} {}\n",
                        metric_name,
                        result.timestamp.to_rfc3339(),
                        if label_str.is_empty() { String::new() } else { format!(",{}", label_str) },
                        num_value
                    ));
                }
            }
        }

        Ok(prometheus_output)
    }

    /// Export results as InfluxDB line protocol
    fn export_as_influxdb(&self, results: &[QueryResult]) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let mut influx_output = String::new();

        for result in results {
            // Measurement name
            let measurement = format!("infinitium_{}", format!("{:?}", result.data_type).to_lowercase());

            // Tags
            let mut tags = Vec::new();
            for (key, value) in &result.labels {
                tags.push(format!("{}={}", key, value.replace(" ", "\\ ")));
            }
            let tags_str = tags.join(",");

            // Fields
            let mut fields = Vec::new();
            for (key, value) in &result.values {
                if let Some(num_value) = value.as_f64() {
                    fields.push(format!("{}= {}", key, num_value));
                } else if let Some(str_value) = value.as_str() {
                    fields.push(format!("{}= \"{}\"", key, str_value.replace("\"", "\\\"")));
                } else {
                    fields.push(format!("{}= \"{}\"", key, value.to_string()));
                }
            }
            let fields_str = fields.join(",");

            // Timestamp (nanoseconds since epoch)
            let timestamp_ns = result.timestamp.timestamp_nanos_opt().unwrap_or(0);

            influx_output.push_str(&format!("{},{} {} {}\n",
                measurement,
                tags_str,
                fields_str,
                timestamp_ns
            ));
        }

        Ok(influx_output)
    }
}
/// Query security manager
pub struct QuerySecurityManager {
    access_policies: Arc<RwLock<HashMap<String, AccessPolicy>>>,
    rate_limiters: Arc<RwLock<HashMap<String, UserRateLimiter>>>,
    audit_logger: Arc<QueryAuditLogger>,
    data_masking_rules: Arc<RwLock<Vec<DataMaskingRule>>>,
}

/// Access policy for data types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessPolicy {
    pub role: String,
    pub data_types: Vec<DataType>,
    pub allowed_operations: Vec<QueryOperation>,
    pub field_restrictions: HashMap<String, FieldAccess>,
    pub time_restrictions: Option<TimeRestriction>,
    pub ip_restrictions: Vec<String>,
    pub query_complexity_limit: u32,
}

/// Query operations
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum QueryOperation {
    Read,
    Export,
    Aggregate,
    Stream,
    Admin,
}

/// Field access level
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FieldAccess {
    Allow,
    Deny,
    Mask,
    Hash,
}

/// Time restrictions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimeRestriction {
    pub allowed_hours: Vec<u32>, // 0-23
    pub allowed_days: Vec<String>, // Monday, Tuesday, etc.
    pub timezone: String,
}

/// Data masking rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataMaskingRule {
    pub field_pattern: String,
    pub data_type: DataType,
    pub mask_type: MaskType,
    pub roles_allowed_full_access: Vec<String>,
}

/// Mask types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MaskType {
    FullMask,
    PartialMask { visible_chars: usize, mask_char: char },
    Hash,
    Nullify,
}

/// User rate limiter
#[derive(Debug, Clone)]
pub struct UserRateLimiter {
    user_id: String,
    requests_per_minute: u32,
    requests_per_hour: u32,
    burst_limit: u32,
    request_count_minute: Arc<RwLock<u32>>,
    request_count_hour: Arc<RwLock<u32>>,
    last_reset_minute: Arc<RwLock<chrono::DateTime<chrono::Utc>>>,
    last_reset_hour: Arc<RwLock<chrono::DateTime<chrono::Utc>>>,
}

/// Query audit logger
pub struct QueryAuditLogger {
    audit_entries: Arc<RwLock<Vec<QueryAuditEntry>>>,
    max_entries: usize,
}

/// Query audit entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryAuditEntry {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub user_id: String,
    pub user_ip: Option<String>,
    pub user_agent: Option<String>,
    pub query_id: String,
    pub query_type: String,
    pub data_types_accessed: Vec<String>,
    pub result_count: u64,
    pub execution_time_ms: u64,
    pub success: bool,
    pub error_message: Option<String>,
    pub security_violations: Vec<String>,
}

impl QuerySecurityManager {
    /// Create a new query security manager
    pub fn new() -> Self {
        Self {
            access_policies: Arc::new(RwLock::new(HashMap::new())),
            rate_limiters: Arc::new(RwLock::new(HashMap::new())),
            audit_logger: Arc::new(QueryAuditLogger::new(10000)),
            data_masking_rules: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// Authorize query execution
    pub async fn authorize_query(
        &self,
        query: &ObservabilityQuery,
        user_id: &str,
        user_roles: &[String],
        user_ip: Option<&str>,
    ) -> Result<AuthorizationResult, Box<dyn std::error::Error + Send + Sync>> {
        // Check rate limits first
        if let Err(e) = self.check_rate_limits(user_id).await {
            return Ok(AuthorizationResult::Denied {
                reason: format!("Rate limit exceeded: {}", e),
                violation_type: SecurityViolation::RateLimitExceeded,
            });
        }

        // Find applicable access policy
        let applicable_policies = self.get_applicable_policies(user_roles).await;

        if applicable_policies.is_empty() {
            return Ok(AuthorizationResult::Denied {
                reason: "No applicable access policies found".to_string(),
                violation_type: SecurityViolation::InsufficientPermissions,
            });
        }

        // Check data type access
        for data_type in &query.data_types {
            if !self.has_data_type_access(&applicable_policies, data_type) {
                return Ok(AuthorizationResult::Denied {
                    reason: format!("Access denied to data type: {:?}", data_type),
                    violation_type: SecurityViolation::DataTypeAccessDenied,
                });
            }
        }

        // Check operation permissions
        if !self.has_operation_access(&applicable_policies, &QueryOperation::Read) {
            return Ok(AuthorizationResult::Denied {
                reason: "Read operation not permitted".to_string(),
                violation_type: SecurityViolation::OperationAccessDenied,
            });
        }

        // Check time restrictions
        if let Some(violation) = self.check_time_restrictions(&applicable_policies).await {
            return Ok(AuthorizationResult::Denied {
                reason: violation,
                violation_type: SecurityViolation::TimeRestrictionViolation,
            });
        }

        // Check IP restrictions
        if let Some(violation) = self.check_ip_restrictions(&applicable_policies, user_ip).await {
            return Ok(AuthorizationResult::Denied {
                reason: violation,
                violation_type: SecurityViolation::IPRestrictionViolation,
            });
        }

        // Check query complexity
        if let Some(violation) = self.check_query_complexity(&applicable_policies, query).await {
            return Ok(AuthorizationResult::Denied {
                reason: violation,
                violation_type: SecurityViolation::QueryComplexityExceeded,
            });
        }

        Ok(AuthorizationResult::Allowed {
            policies: applicable_policies,
            masked_fields: self.get_masked_fields(query, user_roles).await,
        })
    }

    /// Check rate limits
    async fn check_rate_limits(&self, user_id: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut rate_limiters = self.rate_limiters.write().await;

        let limiter = rate_limiters.entry(user_id.to_string()).or_insert_with(|| {
            UserRateLimiter::new(user_id.to_string(), 60, 1000, 10) // 60 req/min, 1000 req/hour, burst 10
        });

        limiter.check_and_increment().await
    }

    /// Get applicable access policies
    async fn get_applicable_policies(&self, user_roles: &[String]) -> Vec<AccessPolicy> {
        let policies = self.access_policies.read().await;
        let mut applicable = Vec::new();

        for role in user_roles {
            if let Some(policy) = policies.get(role) {
                applicable.push(policy.clone());
            }
        }

        applicable
    }

    /// Check data type access
    fn has_data_type_access(&self, policies: &[AccessPolicy], data_type: &DataType) -> bool {
        policies.iter().any(|policy| policy.data_types.contains(data_type))
    }

    /// Check operation access
    fn has_operation_access(&self, policies: &[AccessPolicy], operation: &QueryOperation) -> bool {
        policies.iter().any(|policy| policy.allowed_operations.contains(operation))
    }

    /// Check time restrictions
    async fn check_time_restrictions(&self, policies: &[AccessPolicy]) -> Option<String> {
        let now = chrono::Utc::now();
        let current_hour = now.hour();
        let current_day = now.format("%A").to_string();

        for policy in policies {
            if let Some(restrictions) = &policy.time_restrictions {
                if !restrictions.allowed_hours.contains(&current_hour) {
                    return Some(format!("Access not allowed at hour {}", current_hour));
                }
                if !restrictions.allowed_days.contains(&current_day) {
                    return Some(format!("Access not allowed on {}", current_day));
                }
            }
        }

        None
    }

    /// Check IP restrictions
    async fn check_ip_restrictions(&self, policies: &[AccessPolicy], user_ip: Option<&str>) -> Option<String> {
        if let Some(ip) = user_ip {
            for policy in policies {
                if !policy.ip_restrictions.is_empty() && !policy.ip_restrictions.contains(&ip.to_string()) {
                    return Some(format!("Access denied from IP: {}", ip));
                }
            }
        }

        None
    }

    /// Check query complexity
    async fn check_query_complexity(&self, policies: &[AccessPolicy], query: &ObservabilityQuery) -> Option<String> {
        let complexity = self.calculate_query_complexity(query);

        for policy in policies {
            if complexity > policy.query_complexity_limit as u64 {
                return Some(format!("Query complexity {} exceeds limit {}", complexity, policy.query_complexity_limit));
            }
        }

        None
    }

    /// Calculate query complexity score
    fn calculate_query_complexity(&self, query: &ObservabilityQuery) -> u64 {
        let mut complexity = 0u64;

        // Data types factor
        complexity += query.data_types.len() as u64 * 10;

        // Filters factor
        complexity += query.filters.len() as u64 * 5;

        // Aggregations factor
        complexity += query.aggregations.len() as u64 * 15;

        // Group by factor
        complexity += query.group_by.len() as u64 * 8;

        complexity
    }

    /// Get masked fields for user
    async fn get_masked_fields(&self, query: &ObservabilityQuery, user_roles: &[String]) -> HashMap<String, MaskType> {
        let masking_rules = self.data_masking_rules.read().await;
        let mut masked_fields = HashMap::new();

        for rule in masking_rules.iter() {
            if query.data_types.contains(&rule.data_type) {
                // Check if user has full access to this field
                let has_full_access = user_roles.iter().any(|role| rule.roles_allowed_full_access.contains(role));

                if !has_full_access {
                    masked_fields.insert(rule.field_pattern.clone(), rule.mask_type.clone());
                }
            }
        }

        masked_fields
    }

    /// Apply data masking to results
    pub async fn apply_data_masking(
        &self,
        results: &mut [QueryResult],
        masked_fields: &HashMap<String, MaskType>,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        for result in results.iter_mut() {
            for (field_pattern, mask_type) in masked_fields {
                // Apply masking to matching fields
                for (field_name, field_value) in result.values.iter_mut() {
                    if field_name.contains(field_pattern) {
                        *field_value = self.apply_mask_to_value(field_value, mask_type);
                    }
                }

                // Apply masking to labels
                for (label_name, label_value) in result.labels.iter_mut() {
                    if label_name.contains(field_pattern) {
                        *label_value = self.apply_mask_to_string(label_value, mask_type);
                    }
                }
            }
        }

        Ok(())
    }

    /// Apply mask to JSON value
    fn apply_mask_to_value(&self, value: &serde_json::Value, mask_type: &MaskType) -> serde_json::Value {
        match value {
            serde_json::Value::String(s) => serde_json::Value::String(self.apply_mask_to_string(s, mask_type)),
            _ => value.clone(), // For non-string values, return as-is for now
        }
    }

    /// Apply mask to string
    fn apply_mask_to_string(&self, input: &str, mask_type: &MaskType) -> String {
        match mask_type {
            MaskType::FullMask => "*".repeat(input.len()),
            MaskType::PartialMask { visible_chars, mask_char } => {
                if input.len() <= *visible_chars {
                    input.to_string()
                } else {
                    format!("{}{}", &input[..*visible_chars], mask_char.to_string().repeat(input.len() - visible_chars))
                }
            }
            MaskType::Hash => {
                use std::collections::hash_map::DefaultHasher;
                use std::hash::{Hash, Hasher};
                let mut hasher = DefaultHasher::new();
                input.hash(&mut hasher);
                format!("{:x}", hasher.finish())
            }
            MaskType::Nullify => "[REDACTED]".to_string(),
        }
    }

    /// Log query audit entry
    pub async fn log_query_audit(
        &self,
        entry: QueryAuditEntry,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        self.audit_logger.log_entry(entry).await;
        Ok(())
    }

    /// Get audit entries for user
    pub async fn get_user_audit_entries(&self, user_id: &str, limit: usize) -> Vec<QueryAuditEntry> {
        self.audit_logger.get_user_entries(user_id, limit).await
    }

    /// Add access policy
    pub async fn add_access_policy(&self, role: String, policy: AccessPolicy) {
        let mut policies = self.access_policies.write().await;
        policies.insert(role, policy);
    }

    /// Add data masking rule
    pub async fn add_data_masking_rule(&self, rule: DataMaskingRule) {
        let mut rules = self.data_masking_rules.write().await;
        rules.push(rule);
    }

    /// Get security statistics
    pub async fn get_security_stats(&self) -> SecurityStats {
        let policies = self.access_policies.read().await;
        let rate_limiters = self.rate_limiters.read().await;
        let masking_rules = self.data_masking_rules.read().await;

        SecurityStats {
            total_policies: policies.len(),
            total_rate_limiters: rate_limiters.len(),
            total_masking_rules: masking_rules.len(),
            audit_entries_count: self.audit_logger.get_total_entries().await,
        }
    }
}

/// Authorization result
#[derive(Debug, Clone)]
pub enum AuthorizationResult {
    Allowed {
        policies: Vec<AccessPolicy>,
        masked_fields: HashMap<String, MaskType>,
    },
    Denied {
        reason: String,
        violation_type: SecurityViolation,
    },
}

/// Security violation types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SecurityViolation {
    RateLimitExceeded,
    InsufficientPermissions,
    DataTypeAccessDenied,
    OperationAccessDenied,
    TimeRestrictionViolation,
    IPRestrictionViolation,
    QueryComplexityExceeded,
}

/// Security statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityStats {
    pub total_policies: usize,
    pub total_rate_limiters: usize,
    pub total_masking_rules: usize,
    pub audit_entries_count: usize,
}

impl UserRateLimiter {
    /// Create a new user rate limiter
    pub fn new(user_id: String, requests_per_minute: u32, requests_per_hour: u32, burst_limit: u32) -> Self {
        Self {
            user_id,
            requests_per_minute,
            requests_per_hour,
            burst_limit,
            request_count_minute: Arc::new(RwLock::new(0)),
            request_count_hour: Arc::new(RwLock::new(0)),
            last_reset_minute: Arc::new(RwLock::new(chrono::Utc::now())),
            last_reset_hour: Arc::new(RwLock::new(chrono::Utc::now())),
        }
    }

    /// Check and increment rate limits
    pub async fn check_and_increment(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let now = chrono::Utc::now();

        // Check minute limit
        {
            let mut count = self.request_count_minute.write().await;
            let mut last_reset = self.last_reset_minute.write().await;

            // Reset counter if minute has passed
            if now.signed_duration_since(*last_reset).num_minutes() >= 1 {
                *count = 0;
                *last_reset = now;
            }

            if *count >= self.requests_per_minute {
                return Err(format!("Minute rate limit exceeded: {} requests", self.requests_per_minute).into());
            }

            *count += 1;
        }

        // Check hour limit
        {
            let mut count = self.request_count_hour.write().await;
            let mut last_reset = self.last_reset_hour.write().await;

            // Reset counter if hour has passed
            if now.signed_duration_since(*last_reset).num_hours() >= 1 {
                *count = 0;
                *last_reset = now;
            }

            if *count >= self.requests_per_hour {
                return Err(format!("Hour rate limit exceeded: {} requests", self.requests_per_hour).into());
            }

            *count += 1;
        }

        Ok(())
    }
}

impl QueryAuditLogger {
    /// Create a new audit logger
    pub fn new(max_entries: usize) -> Self {
        Self {
            audit_entries: Arc::new(RwLock::new(Vec::new())),
            max_entries,
        }
    }

    /// Log audit entry
    pub async fn log_entry(&self, entry: QueryAuditEntry) {
        let mut entries = self.audit_entries.write().await;
        entries.push(entry);

        // Keep only the most recent entries
        if entries.len() > self.max_entries {
            entries.remove(0);
        }
    }

    /// Get audit entries for user
    pub async fn get_user_entries(&self, user_id: &str, limit: usize) -> Vec<QueryAuditEntry> {
        let entries = self.audit_entries.read().await;
        entries.iter()
            .filter(|entry| entry.user_id == user_id)
            .take(limit)
            .cloned()
            .collect()
    }

    /// Get total entries count
    pub async fn get_total_entries(&self) -> usize {
        let entries = self.audit_entries.read().await;
        entries.len()
    }
}

/// Webhook payload
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebhookPayload {
    pub query_id: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub result_count: usize,
    pub results: Vec<QueryResult>,
    pub source: String,
}

/// Periodic export configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PeriodicExportConfig {
    pub query: ObservabilityQuery,
    pub format: ResultFormat,
    pub interval_seconds: u64,
    pub destination: ExportDestination,
    pub enabled: bool,
}

/// Export destination
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExportDestination {
    File { path: String },
    S3 { bucket: String, key_prefix: String },
    Http { url: String, headers: HashMap<String, String> },
}

/// Dashboard integration configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DashboardIntegrationConfig {
    pub dashboard_type: DashboardType,
    pub endpoint: String,
    pub api_key: Option<String>,
    pub dashboard_id: String,
    pub index_name: String,
    pub authentication: Option<DashboardAuth>,
}

/// Dashboard types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DashboardType {
    Grafana,
    Kibana,
    Custom,
}

/// Dashboard authentication
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DashboardAuth {
    pub username: String,
    pub password: String,
    pub token: Option<String>,
}

/// Monitored query response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoredQueryResponse {
    pub response: QueryResponse,
    pub execution_plan: QueryExecutionPlan,
    pub performance_metrics: QueryPerformanceMetrics,
}

/// Query performance metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryPerformanceMetrics {
    pub execution_time_ms: u64,
    pub memory_usage_bytes: usize,
    pub data_points_processed: u64,
    pub cache_used: bool,
    pub parallel_execution_used: bool,
}

/// Query result streaming
pub struct QueryResultStreamer {
    results: Vec<QueryResult>,
    batch_size: usize,
    current_index: usize,
}

impl QueryResultStreamer {
    /// Create a new result streamer
    pub fn new(results: Vec<QueryResult>, batch_size: usize) -> Self {
        Self {
            results,
            batch_size,
            current_index: 0,
        }
    }

    /// Get next batch of results
    pub fn next_batch(&mut self) -> Option<&[QueryResult]> {
        if self.current_index >= self.results.len() {
            return None;
        }

        let start = self.current_index;
        let end = (start + self.batch_size).min(self.results.len());

        self.current_index = end;
        Some(&self.results[start..end])
    }

    /// Check if there are more batches
    pub fn has_more(&self) -> bool {
        self.current_index < self.results.len()
    }

    /// Get total number of batches
    pub fn total_batches(&self) -> usize {
        (self.results.len() + self.batch_size - 1) / self.batch_size
    }

    /// Get current batch number
    pub fn current_batch(&self) -> usize {
        self.current_index / self.batch_size
    }
}

impl QueryEngine {
    fn parse_time_string(&self, time_str: &str) -> Result<DateTime<Utc>, Box<dyn std::error::Error + Send + Sync>> {
        // Try ISO 8601 first
        if let Ok(dt) = DateTime::parse_from_rfc3339(time_str) {
            return Ok(dt.with_timezone(&Utc));
        }

        // Handle relative time expressions
        let now = Utc::now();
        match time_str {
            "now" => Ok(now),
            s if s.ends_with('s') => {
                let seconds: i64 = s.trim_end_matches('s').parse()?;
                Ok(now - chrono::Duration::seconds(seconds))
            }
            s if s.ends_with('m') => {
                let minutes: i64 = s.trim_end_matches('m').parse()?;
                Ok(now - chrono::Duration::minutes(minutes))
            }
            s if s.ends_with('h') => {
                let hours: i64 = s.trim_end_matches('h').parse()?;
                Ok(now - chrono::Duration::hours(hours))
            }
            s if s.ends_with('d') => {
                let days: i64 = s.trim_end_matches('d').parse()?;
                Ok(now - chrono::Duration::days(days))
            }
            s if s.ends_with('w') => {
                let weeks: i64 = s.trim_end_matches('w').parse()?;
                Ok(now - chrono::Duration::weeks(weeks))
            }
            _ => Err(format!("Invalid time format: {}", time_str).into())
        }
    }

    /// Apply filters to query result
    fn apply_filters(&self, result: &QueryResult, filters: &[QueryFilter]) -> bool {
        for filter in filters {
            if !self.apply_single_filter(result, filter) {
                return false;
            }
        }
        true
    }

    /// Apply a single filter to query result
    fn apply_single_filter(&self, result: &QueryResult, filter: &QueryFilter) -> bool {
        // Check labels first
        if let Some(label_value) = result.labels.get(&filter.field) {
            return self.compare_values(label_value, &filter.operator, &filter.value);
        }

        // Check values
        if let Some(value) = result.values.get(&filter.field) {
            return self.compare_values(value, &filter.operator, &filter.value);
        }

        // Field not found - depends on operator
        matches!(filter.operator, FilterOperator::NotExists)
    }

    /// Compare values based on operator
    fn compare_values(&self, actual: &serde_json::Value, operator: &FilterOperator, expected: &serde_json::Value) -> bool {
        match operator {
            FilterOperator::Eq => actual == expected,
            FilterOperator::Ne => actual != expected,
            FilterOperator::Gt => self.numeric_compare(actual, expected, |a, b| a > b),
            FilterOperator::Gte => self.numeric_compare(actual, expected, |a, b| a >= b),
            FilterOperator::Lt => self.numeric_compare(actual, expected, |a, b| a < b),
            FilterOperator::Lte => self.numeric_compare(actual, expected, |a, b| a <= b),
            FilterOperator::Contains => {
                if let (Some(actual_str), Some(expected_str)) = (actual.as_str(), expected.as_str()) {
                    actual_str.contains(expected_str)
                } else {
                    false
                }
            }
            FilterOperator::NotContains => {
                if let (Some(actual_str), Some(expected_str)) = (actual.as_str(), expected.as_str()) {
                    !actual_str.contains(expected_str)
                } else {
                    true
                }
            }
            FilterOperator::Regex => {
                if let (Some(actual_str), Some(pattern)) = (actual.as_str(), expected.as_str()) {
                    regex::Regex::new(pattern).map_or(false, |re| re.is_match(actual_str))
                } else {
                    false
                }
            }
            FilterOperator::In => {
                if let Some(array) = expected.as_array() {
                    array.contains(actual)
                } else {
                    false
                }
            }
            FilterOperator::NotIn => {
                if let Some(array) = expected.as_array() {
                    !array.contains(actual)
                } else {
                    true
                }
            }
            FilterOperator::Exists => !actual.is_null(),
            FilterOperator::NotExists => actual.is_null(),
        }
    }

    /// Compare numeric values
    fn numeric_compare<F>(&self, actual: &serde_json::Value, expected: &serde_json::Value, cmp: F) -> bool
    where
        F: Fn(f64, f64) -> bool,
    {
        match (actual.as_f64(), expected.as_f64()) {
            (Some(a), Some(b)) => cmp(a, b),
            _ => false,
        }
    }

    /// Apply aggregations to results
    async fn apply_aggregations_enhanced(
        &self,
        results: Vec<QueryResult>,
        query: &ObservabilityQuery,
    ) -> Result<Vec<QueryResult>, Box<dyn std::error::Error + Send + Sync>> {
        if query.aggregations.is_empty() && query.group_by.is_empty() {
            return Ok(results);
        }

        let mut aggregated = HashMap::new();

        for result in results {
            let group_key = if query.group_by.is_empty() {
                "all".to_string()
            } else {
                query.group_by.iter()
                    .map(|field| {
                        result.labels.get(field)
                            .or_else(|| result.values.get(field))
                            .map(|v| v.to_string())
                            .unwrap_or_else(|| "null".to_string())
                    })
                    .collect::<Vec<_>>()
                    .join("_")
            };

            let entry = aggregated.entry(group_key).or_insert_with(Vec::new);
            entry.push(result);
        }

        let mut final_results = Vec::new();

        for (group_key, group_results) in aggregated {
            let mut aggregated_result = QueryResult {
                timestamp: Utc::now(),
                data_type: DataType::Metrics,
                labels: HashMap::new(),
                values: HashMap::new(),
                raw_data: None,
            };

            // Set group labels
            if !query.group_by.is_empty() {
                for (i, field) in query.group_by.iter().enumerate() {
                    if let Some(first_result) = group_results.first() {
                        if let Some(value) = first_result.labels.get(field).or_else(|| first_result.values.get(field)) {
                            aggregated_result.labels.insert(field.clone(), value.to_string());
                        }
                    }
                }
            }

            // Apply aggregations
            for aggregation in &query.aggregations {
                let values: Vec<f64> = group_results.iter()
                    .filter_map(|r| r.values.get(&aggregation.field))
                    .filter_map(|v| v.as_f64())
                    .collect();

                if values.is_empty() {
                    continue;
                }

                let aggregated_value = match aggregation.function {
                    AggregationFunction::Count => values.len() as f64,
                    AggregationFunction::Sum => values.iter().sum(),
                    AggregationFunction::Avg => values.iter().sum::<f64>() / values.len() as f64,
                    AggregationFunction::Min => values.iter().fold(f64::INFINITY, |a, &b| a.min(b)),
                    AggregationFunction::Max => values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b)),
                    AggregationFunction::Percentile(p) => {
                        let mut sorted = values.clone();
                        sorted.sort_by(|a, b| a.partial_cmp(b).unwrap());
                        let index = ((sorted.len() - 1) as f64 * p / 100.0) as usize;
                        sorted.get(index).copied().unwrap_or(0.0)
                    }
                    AggregationFunction::Rate => {
                        // Simple rate calculation - would need time-based data for proper implementation
                        0.0
                    }
                    AggregationFunction::Increase => {
                        // Simple increase calculation
                        if values.len() >= 2 {
                            values.last().unwrap() - values.first().unwrap()
                        } else {
                            0.0
                        }
                    }
                };

                let field_name = aggregation.alias.clone().unwrap_or_else(|| format!("{}_{:?}", aggregation.field, aggregation.function).to_lowercase());
                aggregated_result.values.insert(field_name, serde_json::json!(aggregated_value));
            }

            final_results.push(aggregated_result);
        }

        Ok(final_results)
    }
}