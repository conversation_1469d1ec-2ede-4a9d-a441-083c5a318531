//! # Resource Management and Throttling Framework
//!
//! This module provides comprehensive resource management and throttling mechanisms
//! to ensure observability system stability under varying load conditions.

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// Resource management configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ResourceManagementConfig {
    /// Enable resource management
    pub enabled: bool,
    /// Resource limits configuration
    pub limits: ResourceLimits,
    /// Throttling configuration
    pub throttling: ThrottlingConfig,
    /// Auto-scaling configuration
    pub auto_scaling: AutoScalingConfig,
    /// Resource monitoring configuration
    pub monitoring: ResourceMonitoringConfig,
}

/// Resource limits configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ResourceLimits {
    /// Maximum CPU usage percentage
    pub max_cpu_percent: f64,
    /// Maximum memory usage percentage
    pub max_memory_percent: f64,
    /// Maximum disk usage percentage
    pub max_disk_percent: f64,
    /// Maximum network bandwidth (MB/s)
    pub max_network_mbps: f64,
    /// Maximum concurrent connections
    pub max_concurrent_connections: usize,
    /// Maximum queue size for requests
    pub max_queue_size: usize,
    /// Maximum request rate per second
    pub max_request_rate_per_second: u64,
}

/// Throttling configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThrottlingConfig {
    /// Enable request throttling
    pub enabled: bool,
    /// Throttling strategy
    pub strategy: ThrottlingStrategy,
    /// Rate limit per second
    pub rate_limit_per_second: u64,
    /// Burst limit
    pub burst_limit: u64,
    /// Throttling window in seconds
    pub window_seconds: u64,
    /// Service-specific limits
    pub service_limits: HashMap<String, ServiceThrottleLimit>,
}

/// Throttling strategies
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ThrottlingStrategy {
    TokenBucket,
    LeakyBucket,
    FixedWindow,
    SlidingWindow,
}

/// Service-specific throttle limits
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceThrottleLimit {
    pub rate_limit_per_second: u64,
    pub burst_limit: u64,
    pub priority: ThrottlePriority,
}

/// Throttle priority levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ThrottlePriority {
    High,
    Medium,
    Low,
}

/// Auto-scaling configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutoScalingConfig {
    /// Enable auto-scaling
    pub enabled: bool,
    /// Scale up threshold (percentage)
    pub scale_up_threshold_percent: f64,
    /// Scale down threshold (percentage)
    pub scale_down_threshold_percent: f64,
    /// Minimum instances
    pub min_instances: usize,
    /// Maximum instances
    pub max_instances: usize,
    /// Cooldown period in seconds
    pub cooldown_seconds: u64,
    /// Scale up factor
    pub scale_up_factor: f64,
    /// Scale down factor
    pub scale_down_factor: f64,
}

/// Resource monitoring configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceMonitoringConfig {
    /// Enable resource monitoring
    pub enabled: bool,
    /// Monitoring interval in seconds
    pub monitoring_interval_seconds: u64,
    /// Alert thresholds
    pub alert_thresholds: ResourceAlertThresholds,
    /// Metrics collection enabled
    pub metrics_collection_enabled: bool,
}

/// Resource alert thresholds
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceAlertThresholds {
    pub cpu_warning_percent: f64,
    pub cpu_critical_percent: f64,
    pub memory_warning_percent: f64,
    pub memory_critical_percent: f64,
    pub disk_warning_percent: f64,
    pub disk_critical_percent: f64,
}

/// Resource usage metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceUsage {
    pub cpu_percent: f64,
    pub memory_percent: f64,
    pub memory_bytes: u64,
    pub disk_percent: f64,
    pub disk_bytes: u64,
    pub network_mbps: f64,
    pub active_connections: usize,
    pub queue_size: usize,
    pub request_rate_per_second: f64,
    pub timestamp: DateTime<Utc>,
}

/// Throttling decision
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ThrottlingDecision {
    Allow,
    Throttle { wait_time_ms: u64, reason: String },
    Reject { reason: String },
}

/// Resource alert
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceAlert {
    pub id: String,
    pub resource_type: ResourceType,
    pub severity: AlertSeverity,
    pub current_value: f64,
    pub threshold_value: f64,
    pub message: String,
    pub timestamp: DateTime<Utc>,
}

/// Resource types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ResourceType {
    CPU,
    Memory,
    Disk,
    Network,
    Connections,
    Queue,
}

/// Alert severity levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AlertSeverity {
    Warning,
    Critical,
}

/// Token bucket for rate limiting
#[derive(Debug)]
struct TokenBucket {
    capacity: u64,
    tokens: f64,
    refill_rate: f64, // tokens per second
    last_refill: Instant,
}

impl TokenBucket {
    fn new(capacity: u64, refill_rate: f64) -> Self {
        Self {
            capacity,
            tokens: capacity as f64,
            refill_rate,
            last_refill: Instant::now(),
        }
    }

    fn consume(&mut self, tokens: u64) -> bool {
        self.refill();

        if self.tokens >= tokens as f64 {
            self.tokens -= tokens as f64;
            true
        } else {
            false
        }
    }

    fn refill(&mut self) {
        let now = Instant::now();
        let elapsed = now.duration_since(self.last_refill).as_secs_f64();
        let tokens_to_add = elapsed * self.refill_rate;

        self.tokens = (self.tokens + tokens_to_add).min(self.capacity as f64);
        self.last_refill = now;
    }

    fn available_tokens(&mut self) -> u64 {
        self.refill();
        self.tokens as u64
    }
}

/// Throttle manager for handling request throttling
#[derive(Debug)]
pub struct ThrottleManager {
    config: ThrottlingConfig,
    token_buckets: Arc<RwLock<HashMap<String, TokenBucket>>>,
}

impl ThrottleManager {
    /// Create a new throttle manager
    pub fn new(config: ThrottlingConfig) -> Self {
        Self {
            config,
            token_buckets: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Check if request should be allowed
    pub async fn check_request(&self, service_name: &str, user_id: Option<&str>) -> ThrottlingDecision {
        if !self.config.enabled {
            return ThrottlingDecision::Allow;
        }

        let key = user_id.map(|id| format!("{}_{}", service_name, id))
            .unwrap_or_else(|| service_name.to_string());

        let mut buckets = self.token_buckets.write().await;
        let bucket = buckets.entry(key.clone())
            .or_insert_with(|| {
                let rate = self.get_rate_limit_for_service(service_name);
                TokenBucket::new(self.config.burst_limit, rate as f64)
            });

        if bucket.consume(1) {
            ThrottlingDecision::Allow
        } else {
            let available_tokens = bucket.available_tokens();
            let wait_time = if available_tokens == 0 {
                ((self.config.burst_limit as f64) / self.get_rate_limit_for_service(service_name) as f64 * 1000.0) as u64
            } else {
                0
            };

            ThrottlingDecision::Throttle {
                wait_time_ms: wait_time,
                reason: format!("Rate limit exceeded for service: {}", service_name),
            }
        }
    }

    /// Get rate limit for a specific service
    fn get_rate_limit_for_service(&self, service_name: &str) -> u64 {
        self.config.service_limits
            .get(service_name)
            .map(|limit| limit.rate_limit_per_second)
            .unwrap_or(self.config.rate_limit_per_second)
    }

    /// Reset throttle for a specific key
    pub async fn reset_throttle(&self, key: &str) {
        let mut buckets = self.token_buckets.write().await;
        if let Some(bucket) = buckets.get_mut(key) {
            bucket.tokens = bucket.capacity as f64;
        }
    }

    /// Get throttle statistics
    pub async fn get_throttle_stats(&self) -> HashMap<String, ThrottleStats> {
        let buckets = self.token_buckets.read().await;
        let mut stats = HashMap::new();

        for (key, bucket) in buckets.iter() {
            let mut bucket_clone = bucket.clone();
            let available = bucket_clone.available_tokens();

            stats.insert(key.clone(), ThrottleStats {
                available_tokens: available,
                capacity: bucket.capacity,
                refill_rate: bucket.refill_rate,
            });
        }

        stats
    }
}

/// Throttle statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThrottleStats {
    pub available_tokens: u64,
    pub capacity: u64,
    pub refill_rate: f64,
}

/// Resource monitor for tracking resource usage
#[derive(Debug)]
pub struct ResourceMonitor {
    config: ResourceMonitoringConfig,
    current_usage: Arc<RwLock<ResourceUsage>>,
    alerts: Arc<RwLock<Vec<ResourceAlert>>>,
}

impl ResourceMonitor {
    /// Create a new resource monitor
    pub fn new(config: ResourceMonitoringConfig) -> Self {
        Self {
            config,
            current_usage: Arc::new(RwLock::new(ResourceUsage::default())),
            alerts: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// Update resource usage
    pub async fn update_usage(&self, usage: ResourceUsage) {
        {
            let mut current = self.current_usage.write().await;
            *current = usage;
        }

        if self.config.enabled {
            self.check_alerts(&usage).await;
        }
    }

    /// Get current resource usage
    pub async fn get_current_usage(&self) -> ResourceUsage {
        self.current_usage.read().await.clone()
    }

    /// Check for resource alerts
    async fn check_alerts(&self, usage: &ResourceUsage) {
        let mut alerts = Vec::new();

        // CPU alerts
        if usage.cpu_percent >= self.config.alert_thresholds.cpu_critical_percent {
            alerts.push(self.create_alert(
                ResourceType::CPU,
                AlertSeverity::Critical,
                usage.cpu_percent,
                self.config.alert_thresholds.cpu_critical_percent,
                "CPU usage is critically high",
            ));
        } else if usage.cpu_percent >= self.config.alert_thresholds.cpu_warning_percent {
            alerts.push(self.create_alert(
                ResourceType::CPU,
                AlertSeverity::Warning,
                usage.cpu_percent,
                self.config.alert_thresholds.cpu_warning_percent,
                "CPU usage is high",
            ));
        }

        // Memory alerts
        if usage.memory_percent >= self.config.alert_thresholds.memory_critical_percent {
            alerts.push(self.create_alert(
                ResourceType::Memory,
                AlertSeverity::Critical,
                usage.memory_percent,
                self.config.alert_thresholds.memory_critical_percent,
                "Memory usage is critically high",
            ));
        } else if usage.memory_percent >= self.config.alert_thresholds.memory_warning_percent {
            alerts.push(self.create_alert(
                ResourceType::Memory,
                AlertSeverity::Warning,
                usage.memory_percent,
                self.config.alert_thresholds.memory_warning_percent,
                "Memory usage is high",
            ));
        }

        // Disk alerts
        if usage.disk_percent >= self.config.alert_thresholds.disk_critical_percent {
            alerts.push(self.create_alert(
                ResourceType::Disk,
                AlertSeverity::Critical,
                usage.disk_percent,
                self.config.alert_thresholds.disk_critical_percent,
                "Disk usage is critically high",
            ));
        } else if usage.disk_percent >= self.config.alert_thresholds.disk_warning_percent {
            alerts.push(self.create_alert(
                ResourceType::Disk,
                AlertSeverity::Warning,
                usage.disk_percent,
                self.config.alert_thresholds.disk_warning_percent,
                "Disk usage is high",
            ));
        }

        if !alerts.is_empty() {
            let mut current_alerts = self.alerts.write().await;
            current_alerts.extend(alerts);
        }
    }

    /// Create a resource alert
    fn create_alert(
        &self,
        resource_type: ResourceType,
        severity: AlertSeverity,
        current_value: f64,
        threshold_value: f64,
        message: &str,
    ) -> ResourceAlert {
        ResourceAlert {
            id: format!("alert_{}_{}_{}", resource_type as u8, severity as u8, Utc::now().timestamp()),
            resource_type,
            severity,
            current_value,
            threshold_value,
            message: message.to_string(),
            timestamp: Utc::now(),
        }
    }

    /// Get active alerts
    pub async fn get_active_alerts(&self) -> Vec<ResourceAlert> {
        self.alerts.read().await.clone()
    }

    /// Clear alerts for a specific resource type
    pub async fn clear_alerts(&self, resource_type: ResourceType) {
        let mut alerts = self.alerts.write().await;
        alerts.retain(|alert| alert.resource_type != resource_type);
    }
}

/// Auto-scaling manager
#[derive(Debug)]
pub struct AutoScalingManager {
    config: AutoScalingConfig,
    current_instances: Arc<RwLock<usize>>,
    last_scale_time: Arc<RwLock<Instant>>,
}

impl AutoScalingManager {
    /// Create a new auto-scaling manager
    pub fn new(config: AutoScalingConfig, initial_instances: usize) -> Self {
        Self {
            config,
            current_instances: Arc::new(RwLock::new(initial_instances)),
            last_scale_time: Arc::new(RwLock::new(Instant::now())),
        }
    }

    /// Check if scaling is needed based on resource usage
    pub async fn check_scaling(&self, resource_usage: &ResourceUsage) -> Option<ScalingDecision> {
        if !self.config.enabled {
            return None;
        }

        let current_instances = *self.current_instances.read().await;
        let last_scale = *self.last_scale_time.read().await;

        // Check cooldown period
        if last_scale.elapsed() < Duration::from_secs(self.config.cooldown_seconds) {
            return None;
        }

        let avg_resource_usage = (resource_usage.cpu_percent + resource_usage.memory_percent) / 2.0;

        if avg_resource_usage >= self.config.scale_up_threshold_percent
            && current_instances < self.config.max_instances {
            let new_instances = ((current_instances as f64) * self.config.scale_up_factor) as usize;
            let target_instances = new_instances.min(self.config.max_instances);

            if target_instances > current_instances {
                return Some(ScalingDecision::ScaleUp(target_instances));
            }
        } else if avg_resource_usage <= self.config.scale_down_threshold_percent
            && current_instances > self.config.min_instances {
            let new_instances = ((current_instances as f64) * self.config.scale_down_factor) as usize;
            let target_instances = new_instances.max(self.config.min_instances);

            if target_instances < current_instances {
                return Some(ScalingDecision::ScaleDown(target_instances));
            }
        }

        None
    }

    /// Execute scaling decision
    pub async fn execute_scaling(&self, decision: ScalingDecision) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut current_instances = self.current_instances.write().await;
        let mut last_scale = self.last_scale_time.write().await;

        match decision {
            ScalingDecision::ScaleUp(target) => {
                *current_instances = target;
                *last_scale = Instant::now();
                println!("Scaled up to {} instances", target);
            }
            ScalingDecision::ScaleDown(target) => {
                *current_instances = target;
                *last_scale = Instant::now();
                println!("Scaled down to {} instances", target);
            }
        }

        Ok(())
    }

    /// Get current instance count
    pub async fn get_current_instances(&self) -> usize {
        *self.current_instances.read().await
    }
}

/// Scaling decision
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ScalingDecision {
    ScaleUp(usize),
    ScaleDown(usize),
}

/// Main resource management manager
#[derive(Debug)]
pub struct ResourceManagementManager {
    config: ResourceManagementConfig,
    throttle_manager: ThrottleManager,
    resource_monitor: ResourceMonitor,
    auto_scaling_manager: AutoScalingManager,
    resource_limits: ResourceLimits,
}

impl ResourceManagementManager {
    /// Create a new resource management manager
    pub fn new(config: ResourceManagementConfig) -> Self {
        let throttle_manager = ThrottleManager::new(config.throttling.clone());
        let resource_monitor = ResourceMonitor::new(config.monitoring.clone());
        let auto_scaling_manager = AutoScalingManager::new(
            config.auto_scaling.clone(),
            config.auto_scaling.min_instances,
        );

        Self {
            config,
            throttle_manager,
            resource_monitor,
            auto_scaling_manager,
            resource_limits: ResourceLimits::default(),
        }
    }

    /// Check if request should be throttled
    pub async fn check_throttle(&self, service_name: &str, user_id: Option<&str>) -> ThrottlingDecision {
        self.throttle_manager.check_request(service_name, user_id).await
    }

    /// Update resource usage
    pub async fn update_resource_usage(&self, usage: ResourceUsage) {
        self.resource_monitor.update_usage(usage.clone()).await;

        // Check for auto-scaling
        if let Some(scaling_decision) = self.auto_scaling_manager.check_scaling(&usage).await {
            let _ = self.auto_scaling_manager.execute_scaling(scaling_decision).await;
        }
    }

    /// Check if resource limits are exceeded
    pub fn check_resource_limits(&self, usage: &ResourceUsage) -> Vec<ResourceLimitViolation> {
        let mut violations = Vec::new();

        if usage.cpu_percent > self.resource_limits.max_cpu_percent {
            violations.push(ResourceLimitViolation {
                resource_type: ResourceType::CPU,
                current_value: usage.cpu_percent,
                limit_value: self.resource_limits.max_cpu_percent,
                message: "CPU usage exceeds limit".to_string(),
            });
        }

        if usage.memory_percent > self.resource_limits.max_memory_percent {
            violations.push(ResourceLimitViolation {
                resource_type: ResourceType::Memory,
                current_value: usage.memory_percent,
                limit_value: self.resource_limits.max_memory_percent,
                message: "Memory usage exceeds limit".to_string(),
            });
        }

        if usage.disk_percent > self.resource_limits.max_disk_percent {
            violations.push(ResourceLimitViolation {
                resource_type: ResourceType::Disk,
                current_value: usage.disk_percent,
                limit_value: self.resource_limits.max_disk_percent,
                message: "Disk usage exceeds limit".to_string(),
            });
        }

        if usage.active_connections > self.resource_limits.max_concurrent_connections {
            violations.push(ResourceLimitViolation {
                resource_type: ResourceType::Connections,
                current_value: usage.active_connections as f64,
                limit_value: self.resource_limits.max_concurrent_connections as f64,
                message: "Active connections exceed limit".to_string(),
            });
        }

        if usage.queue_size > self.resource_limits.max_queue_size {
            violations.push(ResourceLimitViolation {
                resource_type: ResourceType::Queue,
                current_value: usage.queue_size as f64,
                limit_value: self.resource_limits.max_queue_size as f64,
                message: "Queue size exceeds limit".to_string(),
            });
        }

        violations
    }

    /// Get current resource usage
    pub async fn get_resource_usage(&self) -> ResourceUsage {
        self.resource_monitor.get_current_usage().await
    }

    /// Get active resource alerts
    pub async fn get_resource_alerts(&self) -> Vec<ResourceAlert> {
        self.resource_monitor.get_active_alerts().await
    }

    /// Get throttle statistics
    pub async fn get_throttle_stats(&self) -> HashMap<String, ThrottleStats> {
        self.throttle_manager.get_throttle_stats().await
    }

    /// Get current instance count
    pub async fn get_current_instances(&self) -> usize {
        self.auto_scaling_manager.get_current_instances().await
    }

    /// Reset throttle for a specific key
    pub async fn reset_throttle(&self, key: &str) {
        self.throttle_manager.reset_throttle(key).await;
    }
}

/// Resource limit violation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceLimitViolation {
    pub resource_type: ResourceType,
    pub current_value: f64,
    pub limit_value: f64,
    pub message: String,
}

impl Default for ResourceUsage {
    fn default() -> Self {
        Self {
            cpu_percent: 0.0,
            memory_percent: 0.0,
            memory_bytes: 0,
            disk_percent: 0.0,
            disk_bytes: 0,
            network_mbps: 0.0,
            active_connections: 0,
            queue_size: 0,
            request_rate_per_second: 0.0,
            timestamp: Utc::now(),
        }
    }
}

impl Default for ResourceLimits {
    fn default() -> Self {
        Self {
            max_cpu_percent: 80.0,
            max_memory_percent: 85.0,
            max_disk_percent: 90.0,
            max_network_mbps: 100.0,
            max_concurrent_connections: 1000,
            max_queue_size: 10000,
            max_request_rate_per_second: 1000,
        }
    }
}

impl Default for ResourceManagementConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            limits: ResourceLimits::default(),
            throttling: ThrottlingConfig {
                enabled: true,
                strategy: ThrottlingStrategy::TokenBucket,
                rate_limit_per_second: 100,
                burst_limit: 200,
                window_seconds: 60,
                service_limits: HashMap::new(),
            },
            auto_scaling: AutoScalingConfig {
                enabled: true,
                scale_up_threshold_percent: 70.0,
                scale_down_threshold_percent: 30.0,
                min_instances: 1,
                max_instances: 10,
                cooldown_seconds: 300,
                scale_up_factor: 1.5,
                scale_down_factor: 0.7,
            },
            monitoring: ResourceMonitoringConfig {
                enabled: true,
                monitoring_interval_seconds: 30,
                alert_thresholds: ResourceAlertThresholds {
                    cpu_warning_percent: 60.0,
                    cpu_critical_percent: 80.0,
                    memory_warning_percent: 70.0,
                    memory_critical_percent: 85.0,
                    disk_warning_percent: 75.0,
                    disk_critical_percent: 90.0,
                },
                metrics_collection_enabled: true,
            },
        }
    }
}