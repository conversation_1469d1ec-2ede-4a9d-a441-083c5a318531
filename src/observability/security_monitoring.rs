//! # Comprehensive Security Monitoring System
//!
//! This module provides enterprise-grade security monitoring capabilities for SRE-grade
//! compliance and threat detection. It builds upon the existing alerting and observability
//! infrastructure to provide comprehensive security monitoring.
//!
//! ## Features
//!
//! - **Security Event Monitoring**: Authentication, authorization, access patterns, session monitoring
//! - **Compliance Monitoring**: Security compliance tracking, audit trail verification
//! - **Threat Detection Engine**: Brute force detection, data exfiltration monitoring
//! - **Security Metrics & KPIs**: Failed auth rates, security event analysis, health scores
//! - **Security Alerting**: Real-time alerts, incident escalation, automated responses
//! - **Security Dashboard**: Event timelines, threat intelligence, compliance status
//! - **Security Data Protection**: Secure storage, privacy-preserving analytics
//! - **Security Monitoring API**: REST endpoints for security data and management
//! - **Advanced Security Analytics**: Behavioral analysis, risk scoring, trend analysis

pub mod event_monitoring;
pub mod compliance_monitoring;
pub mod threat_detection;
pub mod security_metrics;
pub mod security_alerting;
pub mod security_dashboard;
pub mod security_analytics;
pub mod security_api;
pub mod security_config;
pub mod security_storage;

use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, Duration};

use crate::observability::alerting::{Alert, AlertSeverity, AlertStatus, AlertCategory, AlertingFramework};
use crate::observability::custom_metrics::CustomMetricsManager;

/// Main security monitoring orchestrator
#[derive(Clone)]
pub struct SecurityMonitoringSystem {
    config: SecurityMonitoringConfig,
    event_monitor: Arc<RwLock<event_monitoring::SecurityEventMonitor>>,
    compliance_monitor: Arc<RwLock<compliance_monitoring::ComplianceMonitor>>,
    threat_detector: Arc<RwLock<threat_detection::ThreatDetectionEngine>>,
    metrics_collector: Arc<RwLock<security_metrics::SecurityMetricsCollector>>,
    alerting_system: Arc<RwLock<security_alerting::SecurityAlertingSystem>>,
    analytics_engine: Arc<RwLock<security_analytics::SecurityAnalyticsEngine>>,
    storage_manager: Arc<RwLock<security_storage::SecurityStorageManager>>,
    dashboard_manager: Arc<RwLock<security_dashboard::SecurityDashboardManager>>,
    api_server: Option<Arc<RwLock<security_api::SecurityMonitoringApi>>>,
}

impl SecurityMonitoringSystem {
    /// Create a new security monitoring system
    pub fn new(config: SecurityMonitoringConfig) -> Self {
        let event_monitor = Arc::new(RwLock::new(
            event_monitoring::SecurityEventMonitor::new(config.event_monitoring.clone())
        ));

        let compliance_monitor = Arc::new(RwLock::new(
            compliance_monitoring::ComplianceMonitor::new(config.compliance_monitoring.clone())
        ));

        let threat_detector = Arc::new(RwLock::new(
            threat_detection::ThreatDetectionEngine::new(config.threat_detection.clone())
        ));

        let metrics_collector = Arc::new(RwLock::new(
            security_metrics::SecurityMetricsCollector::new(config.metrics.clone())
        ));

        let alerting_system = Arc::new(RwLock::new(
            security_alerting::SecurityAlertingSystem::new(config.alerting.clone())
        ));

        let analytics_engine = Arc::new(RwLock::new(
            security_analytics::SecurityAnalyticsEngine::new(config.analytics.clone())
        ));

        let storage_manager = Arc::new(RwLock::new(
            security_storage::SecurityStorageManager::new(config.storage.clone())
        ));

        let dashboard_manager = Arc::new(RwLock::new(
            security_dashboard::SecurityDashboardManager::new(config.dashboard.clone())
        ));

        Self {
            config,
            event_monitor,
            compliance_monitor,
            threat_detector,
            metrics_collector,
            alerting_system,
            analytics_engine,
            storage_manager,
            dashboard_manager,
            api_server: None,
        }
    }

    /// Initialize the security monitoring system
    pub async fn initialize(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Initialize all components
        {
            let mut event_monitor = self.event_monitor.write().await;
            event_monitor.initialize().await?;
        }

        {
            let mut compliance_monitor = self.compliance_monitor.write().await;
            compliance_monitor.initialize().await?;
        }

        {
            let mut threat_detector = self.threat_detector.write().await;
            threat_detector.initialize().await?;
        }

        {
            let mut metrics_collector = self.metrics_collector.write().await;
            metrics_collector.initialize().await?;
        }

        {
            let mut alerting_system = self.alerting_system.write().await;
            alerting_system.initialize().await?;
        }

        {
            let mut analytics_engine = self.analytics_engine.write().await;
            analytics_engine.initialize().await?;
        }

        {
            let mut storage_manager = self.storage_manager.write().await;
            storage_manager.initialize().await?;
        }

        {
            let mut dashboard_manager = self.dashboard_manager.write().await;
            dashboard_manager.initialize().await?;
        }

        // Initialize API server if enabled
        if self.config.api.enabled {
            let api_config = security_api::SecurityApiConfig {
                enabled: true,
                host: self.config.api.host.clone(),
                port: self.config.api.port,
                tls_enabled: self.config.api.tls_enabled,
                cors_enabled: self.config.api.cors_enabled,
                rate_limiting: self.config.api.rate_limiting.clone(),
            };

            let api_server = Arc::new(RwLock::new(
                security_api::SecurityMonitoringApi::new(
                    api_config,
                    Arc::clone(&self.event_monitor),
                    Arc::clone(&self.compliance_monitor),
                    Arc::clone(&self.threat_detector),
                    Arc::clone(&self.metrics_collector),
                    Arc::clone(&self.analytics_engine),
                )
            ));

            {
                let mut api = api_server.write().await;
                api.initialize().await?;
            }

            self.api_server = Some(api_server);
        }

        Ok(())
    }

    /// Start the security monitoring system
    pub async fn start(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Start background monitoring tasks
        self.start_monitoring_tasks().await?;

        // Start API server if available
        if let Some(api_server) = &self.api_server {
            let api = api_server.read().await;
            api.start().await?;
        }

        Ok(())
    }

    /// Start background monitoring tasks
    async fn start_monitoring_tasks(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let event_monitor = Arc::clone(&self.event_monitor);
        let compliance_monitor = Arc::clone(&self.compliance_monitor);
        let threat_detector = Arc::clone(&self.threat_detector);
        let metrics_collector = Arc::clone(&self.metrics_collector);
        let alerting_system = Arc::clone(&self.alerting_system);
        let analytics_engine = Arc::clone(&self.analytics_engine);

        // Start event monitoring task
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(
                std::time::Duration::from_secs(60)
            );

            loop {
                interval.tick().await;
                if let Err(e) = event_monitor.write().await.process_events().await {
                    eprintln!("Error in event monitoring task: {}", e);
                }
            }
        });

        // Start compliance monitoring task
        let compliance_monitor_clone = Arc::clone(&compliance_monitor);
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(
                std::time::Duration::from_secs(300) // 5 minutes
            );

            loop {
                interval.tick().await;
                if let Err(e) = compliance_monitor_clone.write().await.check_compliance().await {
                    eprintln!("Error in compliance monitoring task: {}", e);
                }
            }
        });

        // Start threat detection task
        let threat_detector_clone = Arc::clone(&threat_detector);
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(
                std::time::Duration::from_secs(30)
            );

            loop {
                interval.tick().await;
                if let Err(e) = threat_detector_clone.write().await.detect_threats().await {
                    eprintln!("Error in threat detection task: {}", e);
                }
            }
        });

        // Start metrics collection task
        let metrics_collector_clone = Arc::clone(&metrics_collector);
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(
                std::time::Duration::from_secs(60)
            );

            loop {
                interval.tick().await;
                if let Err(e) = metrics_collector_clone.write().await.collect_metrics().await {
                    eprintln!("Error in metrics collection task: {}", e);
                }
            }
        });

        // Start alerting task
        let alerting_system_clone = Arc::clone(&alerting_system);
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(
                std::time::Duration::from_secs(30)
            );

            loop {
                interval.tick().await;
                if let Err(e) = alerting_system_clone.write().await.process_alerts().await {
                    eprintln!("Error in alerting task: {}", e);
                }
            }
        });

        // Start analytics task
        let analytics_engine_clone = Arc::clone(&analytics_engine);
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(
                std::time::Duration::from_secs(600) // 10 minutes
            );

            loop {
                interval.tick().await;
                if let Err(e) = analytics_engine_clone.write().await.perform_analytics().await {
                    eprintln!("Error in analytics task: {}", e);
                }
            }
        });

        Ok(())
    }

    /// Record a security event
    pub async fn record_security_event(&self, event: event_monitoring::SecurityEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut event_monitor = self.event_monitor.write().await;
        event_monitor.record_event(event).await
    }

    /// Get security health score
    pub async fn get_security_health_score(&self) -> Result<f64, Box<dyn std::error::Error + Send + Sync>> {
        let metrics = self.metrics_collector.read().await;
        metrics.calculate_health_score().await
    }

    /// Get security dashboard data
    pub async fn get_security_dashboard(&self) -> Result<security_dashboard::SecurityDashboardData, Box<dyn std::error::Error + Send + Sync>> {
        let dashboard = self.dashboard_manager.read().await;
        dashboard.get_dashboard_data().await
    }

    /// Shutdown the security monitoring system
    pub async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Shutdown API server
        if let Some(api_server) = &self.api_server {
            let api = api_server.read().await;
            api.shutdown().await?;
        }

        // Shutdown components
        {
            let mut event_monitor = self.event_monitor.write().await;
            event_monitor.shutdown().await?;
        }

        {
            let mut compliance_monitor = self.compliance_monitor.write().await;
            compliance_monitor.shutdown().await?;
        }

        {
            let mut threat_detector = self.threat_detector.write().await;
            threat_detector.shutdown().await?;
        }

        {
            let mut metrics_collector = self.metrics_collector.write().await;
            metrics_collector.shutdown().await?;
        }

        {
            let mut alerting_system = self.alerting_system.write().await;
            alerting_system.shutdown().await?;
        }

        {
            let mut analytics_engine = self.analytics_engine.write().await;
            analytics_engine.shutdown().await?;
        }

        {
            let mut storage_manager = self.storage_manager.write().await;
            storage_manager.shutdown().await?;
        }

        {
            let mut dashboard_manager = self.dashboard_manager.write().await;
            dashboard_manager.shutdown().await?;
        }

        Ok(())
    }

    // Getters for individual components
    pub fn event_monitor(&self) -> Arc<RwLock<event_monitoring::SecurityEventMonitor>> {
        Arc::clone(&self.event_monitor)
    }

    pub fn compliance_monitor(&self) -> Arc<RwLock<compliance_monitoring::ComplianceMonitor>> {
        Arc::clone(&self.compliance_monitor)
    }

    pub fn threat_detector(&self) -> Arc<RwLock<threat_detection::ThreatDetectionEngine>> {
        Arc::clone(&self.threat_detector)
    }

    pub fn metrics_collector(&self) -> Arc<RwLock<security_metrics::SecurityMetricsCollector>> {
        Arc::clone(&self.metrics_collector)
    }

    pub fn alerting_system(&self) -> Arc<RwLock<security_alerting::SecurityAlertingSystem>> {
        Arc::clone(&self.alerting_system)
    }

    pub fn analytics_engine(&self) -> Arc<RwLock<security_analytics::SecurityAnalyticsEngine>> {
        Arc::clone(&self.analytics_engine)
    }

    pub fn storage_manager(&self) -> Arc<RwLock<security_storage::SecurityStorageManager>> {
        Arc::clone(&self.storage_manager)
    }

    pub fn dashboard_manager(&self) -> Arc<RwLock<security_dashboard::SecurityDashboardManager>> {
        Arc::clone(&self.dashboard_manager)
    }

    pub fn api_server(&self) -> Option<Arc<RwLock<security_api::SecurityMonitoringApi>>> {
        self.api_server.clone()
    }
}

/// Main configuration for the security monitoring system
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityMonitoringConfig {
    pub enabled: bool,
    pub event_monitoring: event_monitoring::SecurityEventMonitoringConfig,
    pub compliance_monitoring: compliance_monitoring::ComplianceMonitoringConfig,
    pub threat_detection: threat_detection::ThreatDetectionConfig,
    pub metrics: security_metrics::SecurityMetricsConfig,
    pub alerting: security_alerting::SecurityAlertingConfig,
    pub analytics: security_analytics::SecurityAnalyticsConfig,
    pub storage: security_storage::SecurityStorageConfig,
    pub dashboard: security_dashboard::SecurityDashboardConfig,
    pub api: SecurityApiConfig,
}

impl Default for SecurityMonitoringConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            event_monitoring: event_monitoring::SecurityEventMonitoringConfig::default(),
            compliance_monitoring: compliance_monitoring::ComplianceMonitoringConfig::default(),
            threat_detection: threat_detection::ThreatDetectionConfig::default(),
            metrics: security_metrics::SecurityMetricsConfig::default(),
            alerting: security_alerting::SecurityAlertingConfig::default(),
            analytics: security_analytics::SecurityAnalyticsConfig::default(),
            storage: security_storage::SecurityStorageConfig::default(),
            dashboard: security_dashboard::SecurityDashboardConfig::default(),
            api: SecurityApiConfig::default(),
        }
    }
}

/// API configuration for security monitoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityApiConfig {
    pub enabled: bool,
    pub host: String,
    pub port: u16,
    pub tls_enabled: bool,
    pub cors_enabled: bool,
    pub rate_limiting: security_api::RateLimitingConfig,
}

impl Default for SecurityApiConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            host: "0.0.0.0".to_string(),
            port: 8081,
            tls_enabled: false,
            cors_enabled: true,
            rate_limiting: security_api::RateLimitingConfig::default(),
        }
    }
}