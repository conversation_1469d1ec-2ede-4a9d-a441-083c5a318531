//! # Security Event Monitoring
//!
//! Monitors security-related events including authentication, authorization,
//! access patterns, session management, and privilege escalation detection.

use std::collections::{HashMap, HashSet, VecDeque};
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, Duration};

use crate::observability::alerting::{<PERSON>ert, AlertSeverity, AlertStatus, AlertCategory};

/// Configuration for security event monitoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityEventMonitoringConfig {
    pub enabled: bool,
    pub monitoring_interval_seconds: u64,
    pub max_events_buffer: usize,
    pub auth_failure_threshold: u32,
    pub suspicious_activity_threshold: u32,
    pub session_anomaly_threshold: f64,
    pub privilege_escalation_detection: bool,
    pub access_pattern_analysis: bool,
    pub behavioral_analysis_enabled: bool,
    pub ip_geolocation_enabled: bool,
    pub user_agent_analysis: bool,
    pub retention_period_days: u32,
}

impl Default for SecurityEventMonitoringConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            monitoring_interval_seconds: 60,
            max_events_buffer: 10000,
            auth_failure_threshold: 5,
            suspicious_activity_threshold: 10,
            session_anomaly_threshold: 2.0,
            privilege_escalation_detection: true,
            access_pattern_analysis: true,
            behavioral_analysis_enabled: true,
            ip_geolocation_enabled: true,
            user_agent_analysis: true,
            retention_period_days: 90,
        }
    }
}

/// Security event types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum SecurityEventType {
    AuthenticationSuccess,
    AuthenticationFailure,
    AuthorizationSuccess,
    AuthorizationFailure,
    SessionCreated,
    SessionDestroyed,
    SessionExpired,
    SessionAnomaly,
    AccessPatternAnomaly,
    PrivilegeEscalation,
    DataAccess,
    DataModification,
    DataDeletion,
    SuspiciousActivity,
    BruteForceAttempt,
    UnauthorizedAccess,
    DataExfiltration,
    ComplianceViolation,
    AuditLogAccess,
    ConfigurationChange,
    UserCreated,
    UserModified,
    UserDeleted,
    RoleAssigned,
    RoleRevoked,
    PolicyViolation,
    NetworkAnomaly,
    SystemAccess,
    AdminAction,
}

impl SecurityEventType {
    pub fn as_str(&self) -> &'static str {
        match self {
            SecurityEventType::AuthenticationSuccess => "authentication_success",
            SecurityEventType::AuthenticationFailure => "authentication_failure",
            SecurityEventType::AuthorizationSuccess => "authorization_success",
            SecurityEventType::AuthorizationFailure => "authorization_failure",
            SecurityEventType::SessionCreated => "session_created",
            SecurityEventType::SessionDestroyed => "session_destroyed",
            SecurityEventType::SessionExpired => "session_expired",
            SecurityEventType::SessionAnomaly => "session_anomaly",
            SecurityEventType::AccessPatternAnomaly => "access_pattern_anomaly",
            SecurityEventType::PrivilegeEscalation => "privilege_escalation",
            SecurityEventType::DataAccess => "data_access",
            SecurityEventType::DataModification => "data_modification",
            SecurityEventType::DataDeletion => "data_deletion",
            SecurityEventType::SuspiciousActivity => "suspicious_activity",
            SecurityEventType::BruteForceAttempt => "brute_force_attempt",
            SecurityEventType::UnauthorizedAccess => "unauthorized_access",
            SecurityEventType::DataExfiltration => "data_exfiltration",
            SecurityEventType::ComplianceViolation => "compliance_violation",
            SecurityEventType::AuditLogAccess => "audit_log_access",
            SecurityEventType::ConfigurationChange => "configuration_change",
            SecurityEventType::UserCreated => "user_created",
            SecurityEventType::UserModified => "user_modified",
            SecurityEventType::UserDeleted => "user_deleted",
            SecurityEventType::RoleAssigned => "role_assigned",
            SecurityEventType::RoleRevoked => "role_revoked",
            SecurityEventType::PolicyViolation => "policy_violation",
            SecurityEventType::NetworkAnomaly => "network_anomaly",
            SecurityEventType::SystemAccess => "system_access",
            SecurityEventType::AdminAction => "admin_action",
        }
    }

    pub fn severity(&self) -> AlertSeverity {
        match self {
            SecurityEventType::AuthenticationFailure |
            SecurityEventType::AuthorizationFailure |
            SecurityEventType::SessionAnomaly |
            SecurityEventType::AccessPatternAnomaly |
            SecurityEventType::SuspiciousActivity |
            SecurityEventType::BruteForceAttempt |
            SecurityEventType::UnauthorizedAccess |
            SecurityEventType::DataExfiltration |
            SecurityEventType::ComplianceViolation |
            SecurityEventType::PolicyViolation => AlertSeverity::Warning,

            SecurityEventType::PrivilegeEscalation |
            SecurityEventType::DataDeletion => AlertSeverity::Critical,

            _ => AlertSeverity::Info,
        }
    }
}

/// Security event
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityEvent {
    pub id: String,
    pub event_type: SecurityEventType,
    pub timestamp: DateTime<Utc>,
    pub source_ip: Option<String>,
    pub user_id: Option<String>,
    pub session_id: Option<String>,
    pub resource: Option<String>,
    pub action: Option<String>,
    pub user_agent: Option<String>,
    pub geolocation: Option<GeoLocation>,
    pub severity: AlertSeverity,
    pub description: String,
    pub metadata: HashMap<String, String>,
    pub confidence_score: f64,
    pub risk_score: f64,
    pub tags: Vec<String>,
}

impl SecurityEvent {
    pub fn new(
        event_type: SecurityEventType,
        source_ip: Option<String>,
        user_id: Option<String>,
        description: String,
    ) -> Self {
        let id = format!("sec_evt_{}_{}", event_type.as_str(), Utc::now().timestamp_nanos_opt().unwrap_or(0));

        Self {
            id,
            event_type: event_type.clone(),
            timestamp: Utc::now(),
            source_ip,
            user_id,
            session_id: None,
            resource: None,
            action: None,
            user_agent: None,
            geolocation: None,
            severity: event_type.severity(),
            description,
            metadata: HashMap::new(),
            confidence_score: 1.0,
            risk_score: 0.0,
            tags: Vec::new(),
        }
    }
}

/// Geographic location information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeoLocation {
    pub country: Option<String>,
    pub region: Option<String>,
    pub city: Option<String>,
    pub latitude: Option<f64>,
    pub longitude: Option<f64>,
    pub timezone: Option<String>,
}

/// User session information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserSession {
    pub session_id: String,
    pub user_id: String,
    pub created_at: DateTime<Utc>,
    pub last_activity: DateTime<Utc>,
    pub source_ip: String,
    pub user_agent: String,
    pub is_active: bool,
    pub anomaly_score: f64,
}

/// Access pattern analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessPattern {
    pub user_id: String,
    pub resource_pattern: Vec<String>,
    pub time_pattern: Vec<DateTime<Utc>>,
    pub ip_pattern: Vec<String>,
    pub anomaly_score: f64,
    pub last_updated: DateTime<Utc>,
}

/// Security event monitor
pub struct SecurityEventMonitor {
    config: SecurityEventMonitoringConfig,
    events: VecDeque<SecurityEvent>,
    active_sessions: HashMap<String, UserSession>,
    access_patterns: HashMap<String, AccessPattern>,
    ip_geolocation_cache: HashMap<String, GeoLocation>,
    alerting_framework: Option<Arc<RwLock<dyn AlertingInterface>>>,
}

#[async_trait::async_trait]
pub trait AlertingInterface: Send + Sync {
    async fn create_alert(&self, alert: Alert) -> Result<(), Box<dyn std::error::Error + Send + Sync>>;
}

impl SecurityEventMonitor {
    /// Create a new security event monitor
    pub fn new(config: SecurityEventMonitoringConfig) -> Self {
        Self {
            config: config.clone(),
            events: VecDeque::with_capacity(config.max_events_buffer),
            active_sessions: HashMap::new(),
            access_patterns: HashMap::new(),
            ip_geolocation_cache: HashMap::new(),
            alerting_framework: None,
        }
    }

    /// Initialize the event monitor
    pub async fn initialize(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Clean up old events
        self.cleanup_old_events().await;

        // Initialize geolocation service if enabled
        if self.config.ip_geolocation_enabled {
            self.initialize_geolocation_service().await?;
        }

        Ok(())
    }

    /// Record a security event
    pub async fn record_event(&mut self, mut event: SecurityEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Enrich event with additional information
        self.enrich_event(&mut event).await?;

        // Store event
        if self.events.len() >= self.config.max_events_buffer {
            self.events.pop_front();
        }
        self.events.push_back(event.clone());

        // Analyze event for anomalies
        self.analyze_event(&event).await?;

        // Update session information if applicable
        self.update_session_info(&event).await?;

        // Update access patterns
        if self.config.access_pattern_analysis {
            self.update_access_patterns(&event).await?;
        }

        Ok(())
    }

    /// Process events (called periodically)
    pub async fn process_events(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Clean up expired sessions
        self.cleanup_expired_sessions().await?;

        // Analyze patterns for anomalies
        self.analyze_patterns().await?;

        // Clean up old events
        self.cleanup_old_events().await;

        Ok(())
    }

    /// Enrich event with additional information
    async fn enrich_event(&mut self, event: &mut SecurityEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Add geolocation if IP is available and enabled
        if self.config.ip_geolocation_enabled {
            if let Some(ip) = &event.source_ip {
                if let Some(geo) = self.ip_geolocation_cache.get(ip) {
                    event.geolocation = Some(geo.clone());
                } else {
                    // In a real implementation, this would call a geolocation service
                    let geo = self.lookup_geolocation(ip).await?;
                    self.ip_geolocation_cache.insert(ip.clone(), geo.clone());
                    event.geolocation = Some(geo);
                }
            }
        }

        // Calculate risk score based on event characteristics
        event.risk_score = self.calculate_risk_score(event);

        // Add relevant tags
        self.add_event_tags(event);

        Ok(())
    }

    /// Analyze event for security anomalies
    async fn analyze_event(&self, event: &SecurityEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut alerts = Vec::new();

        match event.event_type {
            SecurityEventType::AuthenticationFailure => {
                let failed_attempts = self.count_recent_auth_failures(&event.source_ip, &event.user_id).await;
                if failed_attempts >= self.config.auth_failure_threshold {
                    alerts.push(self.create_brute_force_alert(event, failed_attempts));
                }
            }
            SecurityEventType::SessionAnomaly => {
                if event.confidence_score >= self.config.session_anomaly_threshold {
                    alerts.push(self.create_session_anomaly_alert(event));
                }
            }
            SecurityEventType::PrivilegeEscalation => {
                if self.config.privilege_escalation_detection {
                    alerts.push(self.create_privilege_escalation_alert(event));
                }
            }
            SecurityEventType::SuspiciousActivity => {
                let suspicious_count = self.count_recent_suspicious_activity(&event.source_ip).await;
                if suspicious_count >= self.config.suspicious_activity_threshold {
                    alerts.push(self.create_suspicious_activity_alert(event, suspicious_count));
                }
            }
            SecurityEventType::UnauthorizedAccess => {
                alerts.push(self.create_unauthorized_access_alert(event));
            }
            SecurityEventType::DataExfiltration => {
                alerts.push(self.create_data_exfiltration_alert(event));
            }
            _ => {}
        }

        // Send alerts
        for alert in alerts {
            if let Some(alerting) = &self.alerting_framework {
                let alerting = alerting.read().await;
                alerting.create_alert(alert).await?;
            }
        }

        Ok(())
    }

    /// Update session information
    async fn update_session_info(&mut self, event: &SecurityEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        match event.event_type {
            SecurityEventType::SessionCreated => {
                if let (Some(session_id), Some(user_id), Some(source_ip)) =
                    (&event.session_id, &event.user_id, &event.source_ip) {
                    let session = UserSession {
                        session_id: session_id.clone(),
                        user_id: user_id.clone(),
                        created_at: event.timestamp,
                        last_activity: event.timestamp,
                        source_ip: source_ip.clone(),
                        user_agent: event.user_agent.clone().unwrap_or_default(),
                        is_active: true,
                        anomaly_score: 0.0,
                    };
                    self.active_sessions.insert(session_id.clone(), session);
                }
            }
            SecurityEventType::SessionDestroyed => {
                if let Some(session_id) = &event.session_id {
                    if let Some(session) = self.active_sessions.get_mut(session_id) {
                        session.is_active = false;
                    }
                }
            }
            _ => {
                // Update last activity for existing sessions
                if let Some(session_id) = &event.session_id {
                    if let Some(session) = self.active_sessions.get_mut(session_id) {
                        session.last_activity = event.timestamp;
                    }
                }
            }
        }

        Ok(())
    }

    /// Update access patterns for behavioral analysis
    async fn update_access_patterns(&mut self, event: &SecurityEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if let Some(user_id) = &event.user_id {
            let pattern = self.access_patterns.entry(user_id.clone()).or_insert_with(|| {
                AccessPattern {
                    user_id: user_id.clone(),
                    resource_pattern: Vec::new(),
                    time_pattern: Vec::new(),
                    ip_pattern: Vec::new(),
                    anomaly_score: 0.0,
                    last_updated: Utc::now(),
                }
            });

            // Update patterns
            if let Some(resource) = &event.resource {
                pattern.resource_pattern.push(resource.clone());
                if pattern.resource_pattern.len() > 100 {
                    pattern.resource_pattern.remove(0);
                }
            }

            pattern.time_pattern.push(event.timestamp);
            if pattern.time_pattern.len() > 100 {
                pattern.time_pattern.remove(0);
            }

            if let Some(ip) = &event.source_ip {
                pattern.ip_pattern.push(ip.clone());
                if pattern.ip_pattern.len() > 100 {
                    pattern.ip_pattern.remove(0);
                }
            }

            pattern.last_updated = Utc::now();

            // Calculate anomaly score
            pattern.anomaly_score = self.calculate_access_pattern_anomaly(pattern);
        }

        Ok(())
    }

    /// Analyze patterns for anomalies
    async fn analyze_patterns(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        for pattern in self.access_patterns.values() {
            if pattern.anomaly_score >= 2.0 {
                // Create anomaly alert
                let alert = self.create_access_pattern_anomaly_alert(pattern);
                if let Some(alerting) = &self.alerting_framework {
                    let alerting = alerting.read().await;
                    alerting.create_alert(alert).await?;
                }
            }
        }

        Ok(())
    }

    /// Count recent authentication failures
    async fn count_recent_auth_failures(&self, source_ip: &Option<String>, user_id: &Option<String>) -> u32 {
        let cutoff = Utc::now() - Duration::minutes(15);
        let mut count = 0;

        for event in &self.events {
            if event.timestamp >= cutoff &&
               event.event_type == SecurityEventType::AuthenticationFailure {
                let ip_match = source_ip.as_ref()
                    .map(|ip| event.source_ip.as_ref() == Some(ip))
                    .unwrap_or(true);
                let user_match = user_id.as_ref()
                    .map(|user| event.user_id.as_ref() == Some(user))
                    .unwrap_or(true);

                if ip_match && user_match {
                    count += 1;
                }
            }
        }

        count
    }

    /// Count recent suspicious activity
    async fn count_recent_suspicious_activity(&self, source_ip: &Option<String>) -> u32 {
        let cutoff = Utc::now() - Duration::minutes(30);
        let mut count = 0;

        for event in &self.events {
            if event.timestamp >= cutoff &&
               matches!(event.event_type, SecurityEventType::SuspiciousActivity | SecurityEventType::UnauthorizedAccess) {
                if let (Some(ip), Some(event_ip)) = (source_ip, &event.source_ip) {
                    if ip == event_ip {
                        count += 1;
                    }
                }
            }
        }

        count
    }

    /// Calculate risk score for an event
    fn calculate_risk_score(&self, event: &SecurityEvent) -> f64 {
        let mut score = 0.0;

        // Base score from event type
        score += match event.event_type {
            SecurityEventType::AuthenticationFailure => 2.0,
            SecurityEventType::AuthorizationFailure => 3.0,
            SecurityEventType::PrivilegeEscalation => 8.0,
            SecurityEventType::DataExfiltration => 9.0,
            SecurityEventType::UnauthorizedAccess => 7.0,
            SecurityEventType::SuspiciousActivity => 4.0,
            _ => 1.0,
        };

        // Increase score for unusual locations
        if let Some(geo) = &event.geolocation {
            if let Some(country) = &geo.country {
                if !self.is_common_country(country) {
                    score += 2.0;
                }
            }
        }

        // Increase score for unusual times
        let hour = event.timestamp.hour();
        if hour < 6 || hour > 22 {
            score += 1.0;
        }

        score.min(10.0)
    }

    /// Add relevant tags to event
    fn add_event_tags(&self, event: &mut SecurityEvent) {
        // Add severity tag
        event.tags.push(format!("severity:{}", event.severity.as_str()));

        // Add event type tag
        event.tags.push(format!("event_type:{}", event.event_type.as_str()));

        // Add geographic tags
        if let Some(geo) = &event.geolocation {
            if let Some(country) = &geo.country {
                event.tags.push(format!("country:{}", country));
            }
        }

        // Add time-based tags
        let hour = event.timestamp.hour();
        if hour < 6 {
            event.tags.push("time:early_morning".to_string());
        } else if hour < 12 {
            event.tags.push("time:morning".to_string());
        } else if hour < 18 {
            event.tags.push("time:afternoon".to_string());
        } else {
            event.tags.push("time:evening".to_string());
        }
    }

    /// Calculate access pattern anomaly score
    fn calculate_access_pattern_anomaly(&self, pattern: &AccessPattern) -> f64 {
        let mut score = 0.0;

        // Check for unusual resource access patterns
        if pattern.resource_pattern.len() >= 10 {
            let unique_resources: HashSet<_> = pattern.resource_pattern.iter().collect();
            let diversity_ratio = unique_resources.len() as f64 / pattern.resource_pattern.len() as f64;
            if diversity_ratio > 0.8 {
                score += 2.0; // High diversity might indicate scanning
            }
        }

        // Check for unusual timing patterns
        if pattern.time_pattern.len() >= 10 {
            // Simple check for regular intervals (might indicate automated access)
            let mut intervals = Vec::new();
            for i in 1..pattern.time_pattern.len() {
                let interval = pattern.time_pattern[i].signed_duration_since(pattern.time_pattern[i-1]);
                intervals.push(interval.num_seconds());
            }

            if let Ok(std_dev) = self.calculate_std_deviation(&intervals) {
                if std_dev < 60.0 { // Very regular intervals
                    score += 1.5;
                }
            }
        }

        // Check for unusual IP patterns
        if pattern.ip_pattern.len() >= 5 {
            let unique_ips: HashSet<_> = pattern.ip_pattern.iter().collect();
            if unique_ips.len() as f64 / pattern.ip_pattern.len() as f64 > 0.7 {
                score += 1.0; // Many different IPs
            }
        }

        score
    }

    /// Helper function to calculate standard deviation
    fn calculate_std_deviation(&self, values: &[i64]) -> Result<f64, Box<dyn std::error::Error + Send + Sync>> {
        if values.is_empty() {
            return Ok(0.0);
        }

        let mean = values.iter().sum::<i64>() as f64 / values.len() as f64;
        let variance = values.iter()
            .map(|&x| (x as f64 - mean).powi(2))
            .sum::<f64>() / values.len() as f64;

        Ok(variance.sqrt())
    }

    /// Check if country is commonly accessed
    fn is_common_country(&self, country: &str) -> bool {
        // This would be configurable in a real implementation
        matches!(country, "US" | "CA" | "GB" | "DE" | "FR" | "JP" | "AU")
    }

    /// Initialize geolocation service
    async fn initialize_geolocation_service(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // In a real implementation, this would initialize a geolocation service
        Ok(())
    }

    /// Lookup geolocation for IP
    async fn lookup_geolocation(&self, ip: &str) -> Result<GeoLocation, Box<dyn std::error::Error + Send + Sync>> {
        // In a real implementation, this would call a geolocation API
        // For now, return a mock location
        Ok(GeoLocation {
            country: Some("US".to_string()),
            region: Some("CA".to_string()),
            city: Some("San Francisco".to_string()),
            latitude: Some(37.7749),
            longitude: Some(-122.4194),
            timezone: Some("America/Los_Angeles".to_string()),
        })
    }

    /// Clean up expired sessions
    async fn cleanup_expired_sessions(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let cutoff = Utc::now() - Duration::hours(24); // Sessions expire after 24 hours of inactivity
        self.active_sessions.retain(|_, session| {
            session.is_active && session.last_activity > cutoff
        });
        Ok(())
    }

    /// Clean up old events
    async fn cleanup_old_events(&mut self) {
        let cutoff = Utc::now() - Duration::days(self.config.retention_period_days as i64);
        while let Some(event) = self.events.front() {
            if event.timestamp < cutoff {
                self.events.pop_front();
            } else {
                break;
            }
        }
    }

    /// Create alert methods
    fn create_brute_force_alert(&self, event: &SecurityEvent, count: u32) -> Alert {
        use crate::observability::alerting::{Alert, AlertCategory, AlertStatus};

        Alert {
            id: format!("sec_brute_force_{}", Utc::now().timestamp()),
            title: "Brute Force Attack Detected".to_string(),
            description: format!("{} failed authentication attempts detected from IP {}", count, event.source_ip.as_deref().unwrap_or("unknown")),
            severity: AlertSeverity::Critical,
            category: AlertCategory::Security,
            status: AlertStatus::Active,
            source: "security_event_monitor".to_string(),
            labels: HashMap::from([
                ("event_type".to_string(), "brute_force".to_string()),
                ("ip".to_string(), event.source_ip.clone().unwrap_or_default()),
            ]),
            annotations: HashMap::from([
                ("attempt_count".to_string(), count.to_string()),
                ("user_id".to_string(), event.user_id.clone().unwrap_or_default()),
            ]),
            value: Some(count as f64),
            threshold: Some(self.config.auth_failure_threshold as f64),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("brute_force_detection".to_string()),
            fingerprint: format!("brute_force_{}", event.source_ip.as_deref().unwrap_or("unknown")),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    fn create_session_anomaly_alert(&self, event: &SecurityEvent) -> Alert {
        use crate::observability::alerting::{Alert, AlertCategory, AlertStatus};

        Alert {
            id: format!("sec_session_anomaly_{}", Utc::now().timestamp()),
            title: "Session Anomaly Detected".to_string(),
            description: format!("Suspicious session activity: {}", event.description),
            severity: AlertSeverity::Warning,
            category: AlertCategory::Security,
            status: AlertStatus::Active,
            source: "security_event_monitor".to_string(),
            labels: HashMap::from([
                ("event_type".to_string(), "session_anomaly".to_string()),
                ("user_id".to_string(), event.user_id.clone().unwrap_or_default()),
            ]),
            annotations: HashMap::from([
                ("confidence_score".to_string(), event.confidence_score.to_string()),
                ("description".to_string(), event.description.clone()),
            ]),
            value: Some(event.confidence_score),
            threshold: Some(self.config.session_anomaly_threshold),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("session_anomaly_detection".to_string()),
            fingerprint: format!("session_anomaly_{}", event.user_id.as_deref().unwrap_or("unknown")),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    fn create_privilege_escalation_alert(&self, event: &SecurityEvent) -> Alert {
        use crate::observability::alerting::{Alert, AlertCategory, AlertStatus};

        Alert {
            id: format!("sec_privilege_escalation_{}", Utc::now().timestamp()),
            title: "Privilege Escalation Detected".to_string(),
            description: format!("Privilege escalation attempt: {}", event.description),
            severity: AlertSeverity::Critical,
            category: AlertCategory::Security,
            status: AlertStatus::Active,
            source: "security_event_monitor".to_string(),
            labels: HashMap::from([
                ("event_type".to_string(), "privilege_escalation".to_string()),
                ("user_id".to_string(), event.user_id.clone().unwrap_or_default()),
            ]),
            annotations: HashMap::from([
                ("description".to_string(), event.description.clone()),
                ("resource".to_string(), event.resource.clone().unwrap_or_default()),
            ]),
            value: None,
            threshold: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("privilege_escalation_detection".to_string()),
            fingerprint: format!("privilege_escalation_{}", event.user_id.as_deref().unwrap_or("unknown")),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    fn create_suspicious_activity_alert(&self, event: &SecurityEvent, count: u32) -> Alert {
        use crate::observability::alerting::{Alert, AlertCategory, AlertStatus};

        Alert {
            id: format!("sec_suspicious_activity_{}", Utc::now().timestamp()),
            title: "Suspicious Activity Detected".to_string(),
            description: format!("{} suspicious activities detected from IP {}", count, event.source_ip.as_deref().unwrap_or("unknown")),
            severity: AlertSeverity::Warning,
            category: AlertCategory::Security,
            status: AlertStatus::Active,
            source: "security_event_monitor".to_string(),
            labels: HashMap::from([
                ("event_type".to_string(), "suspicious_activity".to_string()),
                ("ip".to_string(), event.source_ip.clone().unwrap_or_default()),
            ]),
            annotations: HashMap::from([
                ("activity_count".to_string(), count.to_string()),
                ("description".to_string(), event.description.clone()),
            ]),
            value: Some(count as f64),
            threshold: Some(self.config.suspicious_activity_threshold as f64),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("suspicious_activity_detection".to_string()),
            fingerprint: format!("suspicious_activity_{}", event.source_ip.as_deref().unwrap_or("unknown")),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    fn create_unauthorized_access_alert(&self, event: &SecurityEvent) -> Alert {
        use crate::observability::alerting::{Alert, AlertCategory, AlertStatus};

        Alert {
            id: format!("sec_unauthorized_access_{}", Utc::now().timestamp()),
            title: "Unauthorized Access Attempt".to_string(),
            description: format!("Unauthorized access attempt: {}", event.description),
            severity: AlertSeverity::Critical,
            category: AlertCategory::Security,
            status: AlertStatus::Active,
            source: "security_event_monitor".to_string(),
            labels: HashMap::from([
                ("event_type".to_string(), "unauthorized_access".to_string()),
                ("ip".to_string(), event.source_ip.clone().unwrap_or_default()),
                ("resource".to_string(), event.resource.clone().unwrap_or_default()),
            ]),
            annotations: HashMap::from([
                ("description".to_string(), event.description.clone()),
                ("user_id".to_string(), event.user_id.clone().unwrap_or_default()),
            ]),
            value: None,
            threshold: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("unauthorized_access_detection".to_string()),
            fingerprint: format!("unauthorized_access_{}_{}", event.source_ip.as_deref().unwrap_or("unknown"), event.resource.as_deref().unwrap_or("unknown")),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    fn create_data_exfiltration_alert(&self, event: &SecurityEvent) -> Alert {
        use crate::observability::alerting::{Alert, AlertCategory, AlertStatus};

        Alert {
            id: format!("sec_data_exfiltration_{}", Utc::now().timestamp()),
            title: "Data Exfiltration Detected".to_string(),
            description: format!("Potential data exfiltration: {}", event.description),
            severity: AlertSeverity::Critical,
            category: AlertCategory::Security,
            status: AlertStatus::Active,
            source: "security_event_monitor".to_string(),
            labels: HashMap::from([
                ("event_type".to_string(), "data_exfiltration".to_string()),
                ("user_id".to_string(), event.user_id.clone().unwrap_or_default()),
            ]),
            annotations: HashMap::from([
                ("description".to_string(), event.description.clone()),
                ("resource".to_string(), event.resource.clone().unwrap_or_default()),
            ]),
            value: None,
            threshold: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("data_exfiltration_detection".to_string()),
            fingerprint: format!("data_exfiltration_{}", event.user_id.as_deref().unwrap_or("unknown")),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    fn create_access_pattern_anomaly_alert(&self, pattern: &AccessPattern) -> Alert {
        use crate::observability::alerting::{Alert, AlertCategory, AlertStatus};

        Alert {
            id: format!("sec_access_pattern_{}", Utc::now().timestamp()),
            title: "Access Pattern Anomaly Detected".to_string(),
            description: format!("Unusual access pattern detected for user {}", pattern.user_id),
            severity: AlertSeverity::Warning,
            category: AlertCategory::Security,
            status: AlertStatus::Active,
            source: "security_event_monitor".to_string(),
            labels: HashMap::from([
                ("event_type".to_string(), "access_pattern_anomaly".to_string()),
                ("user_id".to_string(), pattern.user_id.clone()),
            ]),
            annotations: HashMap::from([
                ("anomaly_score".to_string(), pattern.anomaly_score.to_string()),
                ("resource_count".to_string(), pattern.resource_pattern.len().to_string()),
                ("ip_count".to_string(), pattern.ip_pattern.len().to_string()),
            ]),
            value: Some(pattern.anomaly_score),
            threshold: Some(2.0),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            silenced_until: None,
            alert_rule_id: Some("access_pattern_anomaly".to_string()),
            fingerprint: format!("access_pattern_{}", pattern.user_id),
            count: 1,
            last_occurrence: Utc::now(),
        }
    }

    /// Set alerting framework reference
    pub fn set_alerting_framework(&mut self, framework: Arc<RwLock<dyn AlertingInterface>>) {
        self.alerting_framework = Some(framework);
    }

    /// Get recent events
    pub fn get_recent_events(&self, limit: usize) -> Vec<&SecurityEvent> {
        self.events.iter().rev().take(limit).collect()
    }

    /// Get active sessions
    pub fn get_active_sessions(&self) -> Vec<&UserSession> {
        self.active_sessions.values().filter(|s| s.is_active).collect()
    }

    /// Get access patterns
    pub fn get_access_patterns(&self) -> Vec<&AccessPattern> {
        self.access_patterns.values().collect()
    }

    /// Shutdown the event monitor
    pub async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Clean up resources
        Ok(())
    }
}