//! # Security Alerting System
//!
//! Real-time security alert generation, security incident escalation procedures,
//! automated security response triggers, and security alert correlation and deduplication.

use std::collections::{HashMap, HashSet, VecDeque};
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, Duration};
use crate::observability::alerting::{AlertSeverity, AlertCategory};

/// Configuration for security alerting
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityAlertingConfig {
    pub enabled: bool,
    pub real_time_alerts_enabled: bool,
    pub alert_deduplication_enabled: bool,
    pub incident_escalation_enabled: bool,
    pub automated_response_enabled: bool,
    pub alert_correlation_enabled: bool,
    pub notification_channels: Vec<NotificationChannel>,
    pub escalation_policies: Vec<EscalationPolicy>,
    pub alert_rules: Vec<SecurityAlertRule>,
    pub alert_suppression_rules: Vec<AlertSuppressionRule>,
}

impl Default for SecurityAlertingConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            real_time_alerts_enabled: true,
            alert_deduplication_enabled: true,
            incident_escalation_enabled: true,
            automated_response_enabled: false, // Disabled by default for safety
            alert_correlation_enabled: true,
            notification_channels: vec![
                NotificationChannel {
                    id: "email".to_string(),
                    channel_type: NotificationChannelType::Email,
                    enabled: true,
                    config: HashMap::from([
                        ("recipients".to_string(), "<EMAIL>".to_string()),
                    ]),
                },
                NotificationChannel {
                    id: "slack".to_string(),
                    channel_type: NotificationChannelType::Slack,
                    enabled: false,
                    config: HashMap::from([
                        ("webhook_url".to_string(), "".to_string()),
                        ("channel".to_string(), "#security-alerts".to_string()),
                    ]),
                },
            ],
            escalation_policies: vec![
                EscalationPolicy {
                    id: "critical_incident".to_string(),
                    name: "Critical Incident Escalation".to_string(),
                    trigger_severity: AlertSeverity::Critical,
                    escalation_steps: vec![
                        EscalationStep {
                            delay_minutes: 0,
                            channels: vec!["email".to_string()],
                            message_template: "CRITICAL: {alert_title} - {alert_description}".to_string(),
                        },
                        EscalationStep {
                            delay_minutes: 15,
                            channels: vec!["email".to_string(), "slack".to_string()],
                            message_template: "ESCALATION: {alert_title} still active after 15 minutes".to_string(),
                        },
                    ],
                },
            ],
            alert_rules: vec![],
            alert_suppression_rules: vec![],
        }
    }
}

/// Notification channel
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationChannel {
    pub id: String,
    pub channel_type: NotificationChannelType,
    pub enabled: bool,
    pub config: HashMap<String, String>,
}

/// Notification channel type
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum NotificationChannelType {
    Email,
    Slack,
    Teams,
    PagerDuty,
    Webhook,
    SMS,
}

/// Escalation policy
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EscalationPolicy {
    pub id: String,
    pub name: String,
    pub trigger_severity: AlertSeverity,
    pub escalation_steps: Vec<EscalationStep>,
}

/// Escalation step
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EscalationStep {
    pub delay_minutes: u32,
    pub channels: Vec<String>,
    pub message_template: String,
}

/// Security alert rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityAlertRule {
    pub id: String,
    pub name: String,
    pub description: String,
    pub enabled: bool,
    pub conditions: Vec<AlertCondition>,
    pub severity: AlertSeverity,
    pub category: AlertCategory,
    pub aggregation_window_minutes: u32,
    pub threshold: AlertThreshold,
    pub cooldown_minutes: u32,
    pub actions: Vec<AlertAction>,
}

/// Alert condition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertCondition {
    pub metric_name: String,
    pub operator: ThresholdOperator,
    pub value: f64,
    pub time_window_minutes: u32,
}

/// Threshold operator
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum ThresholdOperator {
    GreaterThan,
    GreaterThanOrEqual,
    LessThan,
    LessThanOrEqual,
    Equal,
    NotEqual,
}

/// Alert threshold
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertThreshold {
    pub count: Option<u32>,
    pub percentage: Option<f64>,
    pub absolute_value: Option<f64>,
}

/// Alert action
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertAction {
    Notify(Vec<String>), // Channel IDs
    CreateIncident,
    RunPlaybook(String), // Playbook ID
    SuppressRelated(String), // Related alert pattern
    Escalate,
}

/// Alert suppression rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertSuppressionRule {
    pub id: String,
    pub name: String,
    pub conditions: Vec<SuppressionCondition>,
    pub suppression_duration_minutes: u32,
    pub reason: String,
    pub enabled: bool,
}

/// Suppression condition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SuppressionCondition {
    pub field: String,
    pub operator: String,
    pub value: String,
}

/// Security alert
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityAlert {
    pub id: String,
    pub title: String,
    pub description: String,
    pub severity: AlertSeverity,
    pub category: AlertCategory,
    pub status: AlertStatus,
    pub source: String,
    pub rule_id: Option<String>,
    pub incident_id: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub resolved_at: Option<DateTime<Utc>>,
    pub acknowledged_at: Option<DateTime<Utc>>,
    pub acknowledged_by: Option<String>,
    pub tags: Vec<String>,
    pub metadata: HashMap<String, String>,
    pub escalation_level: u32,
    pub last_notification: Option<DateTime<Utc>>,
    pub suppression_until: Option<DateTime<Utc>>,
}

/// Alert status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum AlertStatus {
    Active,
    Acknowledged,
    Resolved,
    Suppressed,
    Escalated,
}

/// Alert correlation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertCorrelation {
    pub correlation_id: String,
    pub alert_ids: Vec<String>,
    pub correlation_type: CorrelationType,
    pub confidence_score: f64,
    pub description: String,
    pub created_at: DateTime<Utc>,
    pub related_incident_id: Option<String>,
}

/// Correlation type
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum CorrelationType {
    Temporal,      // Alerts occurring close together in time
    Attacker,      // Alerts from same attacker/source
    Target,        // Alerts targeting same resource/user
    Pattern,       // Alerts matching known attack patterns
    Custom(String),
}

/// Automated response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutomatedResponse {
    pub id: String,
    pub name: String,
    pub trigger_conditions: Vec<AutomatedResponseCondition>,
    pub actions: Vec<AutomatedAction>,
    pub enabled: bool,
    pub risk_assessment_required: bool,
    pub approval_required: bool,
    pub max_execution_frequency: u32, // per hour
}

/// Automated response condition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutomatedResponseCondition {
    pub alert_severity: Option<AlertSeverity>,
    pub alert_category: Option<AlertCategory>,
    pub alert_source: Option<String>,
    pub confidence_threshold: Option<f64>,
}

/// Automated action
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AutomatedAction {
    BlockIP(String),           // IP address
    SuspendUser(String),       // User ID
    QuarantineResource(String), // Resource ID
    RunCommand(String),        // Command to execute
    SendNotification(String),  // Notification message
    CreateTicket(String),      // Ticket description
}

/// Security alerting system
pub struct SecurityAlertingSystem {
    config: SecurityAlertingConfig,
    active_alerts: HashMap<String, SecurityAlert>,
    alert_history: VecDeque<SecurityAlert>,
    correlations: Vec<AlertCorrelation>,
    automated_responses: Vec<AutomatedResponse>,
    suppression_rules: HashMap<String, AlertSuppressionRule>,
    escalation_timers: HashMap<String, tokio::task::JoinHandle<()>>,
    notification_manager: Option<Arc<RwLock<dyn NotificationManager>>>,
}

#[async_trait::async_trait]
pub trait NotificationManager: Send + Sync {
    async fn send_notification(&self, channel_id: &str, message: &str, alert: &SecurityAlert) -> Result<(), Box<dyn std::error::Error + Send + Sync>>;
}

/// Security alerting system implementation
impl SecurityAlertingSystem {
    /// Create a new security alerting system
    pub fn new(config: SecurityAlertingConfig) -> Self {
        Self {
            config,
            active_alerts: HashMap::new(),
            alert_history: VecDeque::with_capacity(10000),
            correlations: Vec::new(),
            automated_responses: Vec::new(),
            suppression_rules: HashMap::new(),
            escalation_timers: HashMap::new(),
            notification_manager: None,
        }
    }

    /// Initialize the alerting system
    pub async fn initialize(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Load suppression rules
        for rule in &self.config.alert_suppression_rules {
            self.suppression_rules.insert(rule.id.clone(), rule.clone());
        }

        // Initialize automated responses
        self.initialize_automated_responses().await?;

        Ok(())
    }

    /// Process alerts (main processing loop)
    pub async fn process_alerts(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Process alert rules
        self.process_alert_rules().await?;

        // Process correlations
        if self.config.alert_correlation_enabled {
            self.process_correlations().await?;
        }

        // Process escalations
        self.process_escalations().await?;

        // Execute automated responses
        if self.config.automated_response_enabled {
            self.execute_automated_responses().await?;
        }

        // Clean up old alerts
        self.cleanup_old_alerts().await?;

        Ok(())
    }

    /// Create security alert
    pub async fn create_alert(&mut self, title: String, description: String, severity: AlertSeverity, category: AlertCategory, source: String, metadata: HashMap<String, String>) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let alert_id = format!("alert_{}", Utc::now().timestamp());

        // Check for suppression
        if self.is_alert_suppressed(&metadata).await? {
            return Ok("suppressed".to_string());
        }

        // Check for deduplication
        if self.config.alert_deduplication_enabled {
            if let Some(existing_id) = self.find_duplicate_alert(&title, &source, &metadata).await? {
                self.update_existing_alert(&existing_id, &description, &metadata).await?;
                return Ok(existing_id);
            }
        }

        let alert = SecurityAlert {
            id: alert_id.clone(),
            title,
            description,
            severity: severity.clone(),
            category: category.clone(),
            status: AlertStatus::Active,
            source,
            rule_id: None,
            incident_id: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            acknowledged_at: None,
            acknowledged_by: None,
            tags: vec![],
            metadata,
            escalation_level: 0,
            last_notification: None,
            suppression_until: None,
        };

        self.active_alerts.insert(alert_id.clone(), alert.clone());
        self.alert_history.push_back(alert.clone());

        // Send initial notifications
        if self.config.real_time_alerts_enabled {
            self.send_alert_notifications(&alert_id).await?;
        }

        // Start escalation timer if needed
        if self.config.incident_escalation_enabled {
            self.start_escalation_timer(&alert_id, severity).await?;
        }

        Ok(alert_id)
    }

    /// Acknowledge alert
    pub async fn acknowledge_alert(&mut self, alert_id: &str, user: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if let Some(alert) = self.active_alerts.get_mut(alert_id) {
            alert.status = AlertStatus::Acknowledged;
            alert.acknowledged_at = Some(Utc::now());
            alert.acknowledged_by = Some(user.to_string());
            alert.updated_at = Utc::now();

            // Cancel escalation timer
            if let Some(handle) = self.escalation_timers.remove(alert_id) {
                handle.abort();
            }
        }

        Ok(())
    }

    /// Resolve alert
    pub async fn resolve_alert(&mut self, alert_id: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if let Some(alert) = self.active_alerts.get_mut(alert_id) {
            alert.status = AlertStatus::Resolved;
            alert.resolved_at = Some(Utc::now());
            alert.updated_at = Utc::now();

            // Cancel escalation timer
            if let Some(handle) = self.escalation_timers.remove(alert_id) {
                handle.abort();
            }

            // Move to history
            self.active_alerts.remove(alert_id);
        }

        Ok(())
    }

    /// Suppress alert
    pub async fn suppress_alert(&mut self, alert_id: &str, duration_minutes: u32, reason: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if let Some(alert) = self.active_alerts.get_mut(alert_id) {
            alert.status = AlertStatus::Suppressed;
            alert.suppression_until = Some(Utc::now() + Duration::minutes(duration_minutes as i64));
            alert.updated_at = Utc::now();

            // Add suppression metadata
            alert.metadata.insert("suppression_reason".to_string(), reason.to_string());
            alert.metadata.insert("suppressed_at".to_string(), Utc::now().to_rfc3339());
        }

        Ok(())
    }

    /// Get active alerts
    pub fn get_active_alerts(&self) -> Vec<&SecurityAlert> {
        self.active_alerts.values().collect()
    }

    /// Get alert by ID
    pub fn get_alert(&self, alert_id: &str) -> Option<&SecurityAlert> {
        self.active_alerts.get(alert_id)
    }

    /// Get alerts by severity
    pub fn get_alerts_by_severity(&self, severity: &AlertSeverity) -> Vec<&SecurityAlert> {
        self.active_alerts.values()
            .filter(|a| &a.severity == severity)
            .collect()
    }

    /// Get alert history
    pub fn get_alert_history(&self, limit: usize) -> Vec<&SecurityAlert> {
        self.alert_history.iter().rev().take(limit).collect()
    }

    /// Set notification manager
    pub fn set_notification_manager(&mut self, manager: Arc<RwLock<dyn NotificationManager>>) {
        self.notification_manager = Some(manager);
    }

    /// Shutdown the alerting system
    pub async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Cancel all escalation timers
        for handle in self.escalation_timers.values() {
            handle.abort();
        }

        Ok(())
    }

    // Private helper methods

    async fn process_alert_rules(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        for rule in &self.config.alert_rules {
            if !rule.enabled {
                continue;
            }

            if self.evaluate_alert_rule(rule).await? {
                self.create_alert_from_rule(rule).await?;
            }
        }

        Ok(())
    }

    async fn evaluate_alert_rule(&self, rule: &SecurityAlertRule) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        // This would evaluate the rule conditions against current metrics
        // For now, return false (placeholder)
        Ok(false)
    }

    async fn create_alert_from_rule(&mut self, rule: &SecurityAlertRule) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let title = format!("Security Alert: {}", rule.name);
        let description = rule.description.clone();
        let metadata = HashMap::from([
            ("rule_id".to_string(), rule.id.clone()),
            ("rule_name".to_string(), rule.name.clone()),
        ]);

        self.create_alert(
            title,
            description,
            rule.severity.clone(),
            rule.category.clone(),
            "alert_rule".to_string(),
            metadata,
        ).await?;

        Ok(())
    }

    async fn process_correlations(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let active_alerts: Vec<_> = self.active_alerts.values().collect();

        for i in 0..active_alerts.len() {
            for j in (i + 1)..active_alerts.len() {
                if let Some(correlation) = self.find_alert_correlation(active_alerts[i], active_alerts[j]).await? {
                    self.correlations.push(correlation);
                }
            }
        }

        Ok(())
    }

    async fn find_alert_correlation(&self, alert1: &SecurityAlert, alert2: &SecurityAlert) -> Result<Option<AlertCorrelation>, Box<dyn std::error::Error + Send + Sync>> {
        // Check temporal correlation (alerts within 5 minutes)
        let time_diff = (alert1.created_at - alert2.created_at).num_minutes().abs();
        if time_diff <= 5 {
            return Ok(Some(AlertCorrelation {
                correlation_id: format!("corr_{}_{}", alert1.id, alert2.id),
                alert_ids: vec![alert1.id.clone(), alert2.id.clone()],
                correlation_type: CorrelationType::Temporal,
                confidence_score: 0.8,
                description: format!("Alerts occurred {} minutes apart", time_diff),
                created_at: Utc::now(),
                related_incident_id: None,
            }));
        }

        // Check attacker correlation (same source IP)
        if let (Some(ip1), Some(ip2)) = (
            alert1.metadata.get("source_ip"),
            alert2.metadata.get("source_ip")
        ) {
            if ip1 == ip2 {
                return Ok(Some(AlertCorrelation {
                    correlation_id: format!("corr_attacker_{}_{}", alert1.id, alert2.id),
                    alert_ids: vec![alert1.id.clone(), alert2.id.clone()],
                    correlation_type: CorrelationType::Attacker,
                    confidence_score: 0.9,
                    description: format!("Both alerts from same source IP: {}", ip1),
                    created_at: Utc::now(),
                    related_incident_id: None,
                }));
            }
        }

        Ok(None)
    }

    async fn process_escalations(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let now = Utc::now();

        for alert in self.active_alerts.values_mut() {
            if alert.status == AlertStatus::Active && alert.acknowledged_at.is_none() {
                let active_duration = (now - alert.created_at).num_minutes();

                // Find applicable escalation policy
                for policy in &self.config.escalation_policies {
                    if alert.severity >= policy.trigger_severity {
                        for (step_index, step) in policy.escalation_steps.iter().enumerate() {
                            let step_delay = step.delay_minutes as i64;
                            if active_duration >= step_delay && (alert.escalation_level as usize) <= step_index {
                                self.execute_escalation_step(alert, step, step_index as u32).await?;
                                alert.escalation_level = step_index as u32 + 1;
                                break;
                            }
                        }
                    }
                }
            }
        }

        Ok(())
    }

    async fn execute_escalation_step(&self, alert: &SecurityAlert, step: &EscalationStep, level: u32) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let message = self.format_escalation_message(&step.message_template, alert);

        for channel_id in &step.channels {
            if let Some(manager) = &self.notification_manager {
                let manager = manager.read().await;
                manager.send_notification(channel_id, &message, alert).await?;
            }
        }

        Ok(())
    }

    fn format_escalation_message(&self, template: &str, alert: &SecurityAlert) -> String {
        template
            .replace("{alert_title}", &alert.title)
            .replace("{alert_description}", &alert.description)
            .replace("{alert_severity}", &format!("{:?}", alert.severity))
            .replace("{alert_source}", &alert.source)
    }

    async fn execute_automated_responses(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        for response in &self.automated_responses {
            if !response.enabled {
                continue;
            }

            if self.should_execute_automated_response(response).await? {
                self.execute_automated_response(response).await?;
            }
        }

        Ok(())
    }

    async fn should_execute_automated_response(&self, response: &AutomatedResponse) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        // Check if any active alerts match the response conditions
        for alert in self.active_alerts.values() {
            if self.alert_matches_response_conditions(alert, response) {
                return Ok(true);
            }
        }

        Ok(false)
    }

    fn alert_matches_response_conditions(&self, alert: &SecurityAlert, response: &AutomatedResponse) -> bool {
        for condition in &response.trigger_conditions {
            let mut matches = true;

            if let Some(severity) = &condition.alert_severity {
                if &alert.severity != severity {
                    matches = false;
                }
            }

            if let Some(category) = &condition.alert_category {
                if &alert.category != category {
                    matches = false;
                }
            }

            if let Some(source) = &condition.alert_source {
                if &alert.source != source {
                    matches = false;
                }
            }

            if matches {
                return true;
            }
        }

        false
    }

    async fn execute_automated_response(&self, response: &AutomatedResponse) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        for action in &response.actions {
            match action {
                AutomatedAction::BlockIP(ip) => {
                    println!("Automated response: Blocking IP {}", ip);
                    // Implement IP blocking logic
                }
                AutomatedAction::SuspendUser(user_id) => {
                    println!("Automated response: Suspending user {}", user_id);
                    // Implement user suspension logic
                }
                AutomatedAction::SendNotification(message) => {
                    println!("Automated response: Sending notification: {}", message);
                    // Implement notification logic
                }
                _ => {
                    println!("Automated response: {:?}", action);
                    // Implement other automated actions
                }
            }
        }

        Ok(())
    }

    async fn send_alert_notifications(&self, alert_id: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if let Some(alert) = self.active_alerts.get(alert_id) {
            let message = format!("🚨 {}: {}", alert.title, alert.description);

            for channel in &self.config.notification_channels {
                if channel.enabled {
                    if let Some(manager) = &self.notification_manager {
                        let manager = manager.read().await;
                        manager.send_notification(&channel.id, &message, alert).await?;
                    }
                }
            }
        }

        Ok(())
    }

    async fn start_escalation_timer(&mut self, alert_id: &str, severity: AlertSeverity) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let alert_id = alert_id.to_string();

        let handle = tokio::spawn(async move {
            // This would implement the escalation timer logic
            // For now, just sleep for demonstration
            tokio::time::sleep(tokio::time::Duration::from_secs(300)).await; // 5 minutes
            println!("Escalation timer triggered for alert {}", alert_id);
        });

        self.escalation_timers.insert(alert_id, handle);

        Ok(())
    }

    async fn is_alert_suppressed(&self, metadata: &HashMap<String, String>) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        for rule in self.suppression_rules.values() {
            if !rule.enabled {
                continue;
            }

            if self.matches_suppression_rule(metadata, rule) {
                return Ok(true);
            }
        }

        Ok(false)
    }

    fn matches_suppression_rule(&self, metadata: &HashMap<String, String>, rule: &AlertSuppressionRule) -> bool {
        for condition in &rule.conditions {
            if let Some(value) = metadata.get(&condition.field) {
                match condition.operator.as_str() {
                    "equals" => if value != &condition.value { return false; },
                    "contains" => if !value.contains(&condition.value) { return false; },
                    _ => return false,
                }
            } else {
                return false;
            }
        }

        true
    }

    async fn find_duplicate_alert(&self, title: &str, source: &str, metadata: &HashMap<String, String>) -> Result<Option<String>, Box<dyn std::error::Error + Send + Sync>> {
        for (alert_id, alert) in &self.active_alerts {
            if alert.title == title && alert.source == *source {
                // Check if metadata is similar
                let mut similar = true;
                for (key, value) in metadata {
                    if let Some(existing_value) = alert.metadata.get(key) {
                        if existing_value != value {
                            similar = false;
                            break;
                        }
                    }
                }

                if similar {
                    return Ok(Some(alert_id.clone()));
                }
            }
        }

        Ok(None)
    }

    async fn update_existing_alert(&mut self, alert_id: &str, description: &str, metadata: &HashMap<String, String>) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if let Some(alert) = self.active_alerts.get_mut(alert_id) {
            alert.description = description.to_string();
            alert.metadata.extend(metadata.clone());
            alert.updated_at = Utc::now();
        }

        Ok(())
    }

    async fn cleanup_old_alerts(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let cutoff = Utc::now() - Duration::days(30);

        // Remove old alerts from history
        while let Some(alert) = self.alert_history.front() {
            if alert.created_at < cutoff {
                self.alert_history.pop_front();
            } else {
                break;
            }
        }

        // Clean up old correlations
        self.correlations.retain(|c| c.created_at >= cutoff);
    }

    async fn initialize_automated_responses(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Initialize default automated responses
        self.automated_responses = vec![
            AutomatedResponse {
                id: "block_brute_force_ip".to_string(),
                name: "Block Brute Force IP".to_string(),
                trigger_conditions: vec![
                    AutomatedResponseCondition {
                        alert_severity: Some(AlertSeverity::Critical),
                        alert_category: Some(AlertCategory::Security),
                        alert_source: Some("brute_force_detection".to_string()),
                        confidence_threshold: Some(0.8),
                    },
                ],
                actions: vec![
                    AutomatedAction::BlockIP("source_ip".to_string()),
                    AutomatedAction::SendNotification("Blocked IP due to brute force attack".to_string()),
                ],
                enabled: false, // Disabled by default for safety
                risk_assessment_required: true,
                approval_required: true,
                max_execution_frequency: 10,
            },
        ];

        Ok(())
    }
}