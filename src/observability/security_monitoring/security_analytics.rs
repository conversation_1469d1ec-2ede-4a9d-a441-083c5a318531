//! # Advanced Security Analytics
//!
//! Behavioral analysis for user activity, risk scoring and threat assessment,
//! security trend analysis and forecasting, comparative security analysis across time periods.

use std::collections::{HashMap, HashSet, VecDeque};
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, Duration};
use crate::observability::security_monitoring::security_metrics::RiskFactor;

/// Configuration for security analytics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityAnalyticsConfig {
    pub enabled: bool,
    pub behavioral_analysis_enabled: bool,
    pub risk_scoring_enabled: bool,
    pub trend_analysis_enabled: bool,
    pub predictive_analytics_enabled: bool,
    pub comparative_analysis_enabled: bool,
    pub anomaly_detection_enabled: bool,
    pub machine_learning_enabled: bool,
    pub analysis_interval_seconds: u64,
    pub historical_data_window_days: u32,
    pub risk_scoring_model: RiskScoringModel,
    pub behavioral_model: BehavioralModel,
    pub trend_analysis_parameters: TrendAnalysisParameters,
}

impl Default for SecurityAnalyticsConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            behavioral_analysis_enabled: true,
            risk_scoring_enabled: true,
            trend_analysis_enabled: true,
            predictive_analytics_enabled: false, // Disabled by default
            comparative_analysis_enabled: true,
            anomaly_detection_enabled: true,
            machine_learning_enabled: false, // Disabled by default
            analysis_interval_seconds: 300, // 5 minutes
            historical_data_window_days: 90,
            risk_scoring_model: RiskScoringModel::WeightedFactor,
            behavioral_model: BehavioralModel::MarkovChain,
            trend_analysis_parameters: TrendAnalysisParameters::default(),
        }
    }
}

/// Risk scoring model
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum RiskScoringModel {
    WeightedFactor,
    MachineLearning,
    RuleBased,
    Hybrid,
}

/// Behavioral model
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum BehavioralModel {
    MarkovChain,
    Statistical,
    MachineLearning,
    Hybrid,
}

/// Trend analysis parameters
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrendAnalysisParameters {
    pub moving_average_window_hours: u32,
    pub seasonal_analysis_enabled: bool,
    pub outlier_detection_sensitivity: f64,
    pub trend_detection_threshold: f64,
    pub forecasting_horizon_hours: u32,
}

impl Default for TrendAnalysisParameters {
    fn default() -> Self {
        Self {
            moving_average_window_hours: 24,
            seasonal_analysis_enabled: true,
            outlier_detection_sensitivity: 2.0,
            trend_detection_threshold: 0.1,
            forecasting_horizon_hours: 168, // 1 week
        }
    }
}

/// User behavioral profile
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserBehavioralProfile {
    pub user_id: String,
    pub baseline_metrics: BehavioralMetrics,
    pub current_metrics: BehavioralMetrics,
    pub anomaly_score: f64,
    pub risk_level: RiskLevel,
    pub last_updated: DateTime<Utc>,
    pub behavioral_patterns: Vec<BehavioralPattern>,
    pub risk_factors: Vec<String>,
}

/// Behavioral metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BehavioralMetrics {
    pub login_frequency: f64,              // logins per day
    pub session_duration_avg: f64,         // minutes
    pub access_pattern_entropy: f64,       // randomness of access patterns
    pub time_based_anomaly_score: f64,     // unusual timing patterns
    pub resource_access_diversity: f64,    // variety of resources accessed
    pub failed_authentication_rate: f64,   // failed auth attempts per login
    pub privilege_escalation_attempts: u32,
    pub data_access_volume: u64,           // bytes of data accessed
    pub network_anomaly_score: f64,
}

/// Behavioral pattern
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BehavioralPattern {
    pub pattern_id: String,
    pub pattern_type: PatternType,
    pub confidence: f64,
    pub first_observed: DateTime<Utc>,
    pub last_observed: DateTime<Utc>,
    pub frequency: u32,
    pub description: String,
}

/// Pattern type
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum PatternType {
    Normal,
    Suspicious,
    Anomalous,
    Malicious,
}

/// Risk level
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
    Critical,
}

/// Security trend analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityTrendAnalysis {
    pub analysis_period: (DateTime<Utc>, DateTime<Utc>),
    pub overall_trend: TrendDirection,
    pub trend_confidence: f64,
    pub key_findings: Vec<String>,
    pub metric_trends: HashMap<String, MetricTrend>,
    pub anomaly_summary: AnomalySummary,
    pub predictive_insights: Vec<PredictiveInsight>,
    pub recommendations: Vec<String>,
}

/// Trend direction
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum TrendDirection {
    Improving,
    Stable,
    Declining,
    Volatile,
}

/// Metric trend
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricTrend {
    pub metric_name: String,
    pub current_value: f64,
    pub previous_value: f64,
    pub change_percentage: f64,
    pub trend_direction: TrendDirection,
    pub volatility: f64,
    pub seasonal_pattern: Option<String>,
}

/// Anomaly summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnomalySummary {
    pub total_anomalies: u32,
    pub anomaly_types: HashMap<String, u32>,
    pub anomaly_severity_distribution: HashMap<String, u32>,
    pub most_common_anomalies: Vec<String>,
    pub anomaly_trend: TrendDirection,
}

/// Predictive insight
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PredictiveInsight {
    pub insight_id: String,
    pub insight_type: InsightType,
    pub title: String,
    pub description: String,
    pub confidence: f64,
    pub time_horizon: String,
    pub affected_entities: Vec<String>,
    pub recommended_actions: Vec<String>,
    pub risk_score: f64,
}

/// Insight type
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum InsightType {
    ThreatPrediction,
    RiskAssessment,
    CapacityPlanning,
    SecurityOptimization,
    ComplianceForecast,
}

/// Comparative analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComparativeAnalysis {
    pub baseline_period: (DateTime<Utc>, DateTime<Utc>),
    pub comparison_period: (DateTime<Utc>, DateTime<Utc>),
    pub comparison_type: ComparisonType,
    pub key_differences: Vec<MetricComparison>,
    pub significant_changes: Vec<SignificantChange>,
    pub insights: Vec<String>,
}

/// Comparison type
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum ComparisonType {
    WeekOverWeek,
    MonthOverMonth,
    YearOverYear,
    Custom,
}

/// Metric comparison
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricComparison {
    pub metric_name: String,
    pub baseline_value: f64,
    pub comparison_value: f64,
    pub difference: f64,
    pub percentage_change: f64,
    pub statistical_significance: f64,
}

/// Significant change
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SignificantChange {
    pub change_type: ChangeType,
    pub description: String,
    pub impact: ChangeImpact,
    pub confidence: f64,
    pub related_metrics: Vec<String>,
}

/// Change type
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum ChangeType {
    Increase,
    Decrease,
    NewPattern,
    PatternDisappearance,
    AnomalySpike,
}

/// Change impact
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum ChangeImpact {
    Low,
    Medium,
    High,
    Critical,
}

/// Risk assessment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAssessment {
    pub entity_id: String,
    pub entity_type: String,
    pub overall_risk_score: f64,
    pub risk_factors: Vec<RiskFactor>,
    pub risk_trend: TrendDirection,
    pub mitigation_status: HashMap<String, String>,
    pub recommended_actions: Vec<String>,
    pub assessment_date: DateTime<Utc>,
}

/// Security analytics engine
pub struct SecurityAnalyticsEngine {
    config: SecurityAnalyticsConfig,
    user_profiles: HashMap<String, UserBehavioralProfile>,
    historical_metrics: VecDeque<super::security_metrics::SecurityMetricsSnapshot>,
    trend_analysis_cache: Option<SecurityTrendAnalysis>,
    risk_assessments: HashMap<String, RiskAssessment>,
    anomaly_detector: AnomalyDetector,
    predictive_model: Option<PredictiveModel>,
}

impl SecurityAnalyticsEngine {
    /// Create a new security analytics engine
    pub fn new(config: SecurityAnalyticsConfig) -> Self {
        Self {
            config,
            user_profiles: HashMap::new(),
            historical_metrics: VecDeque::with_capacity(10000),
            trend_analysis_cache: None,
            risk_assessments: HashMap::new(),
            anomaly_detector: AnomalyDetector::new(),
            predictive_model: None,
        }
    }

    /// Initialize the analytics engine
    pub async fn initialize(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Initialize predictive model if enabled
        if self.config.predictive_analytics_enabled && self.config.machine_learning_enabled {
            self.predictive_model = Some(PredictiveModel::new());
        }

        Ok(())
    }

    /// Perform analytics (main processing loop)
    pub async fn perform_analytics(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Update behavioral profiles
        if self.config.behavioral_analysis_enabled {
            self.update_behavioral_profiles().await?;
        }

        // Perform risk scoring
        if self.config.risk_scoring_enabled {
            self.perform_risk_scoring().await?;
        }

        // Analyze trends
        if self.config.trend_analysis_enabled {
            self.analyze_trends().await?;
        }

        // Perform comparative analysis
        if self.config.comparative_analysis_enabled {
            self.perform_comparative_analysis().await?;
        }

        // Generate predictive insights
        if self.config.predictive_analytics_enabled {
            self.generate_predictive_insights().await?;
        }

        // Detect anomalies
        if self.config.anomaly_detection_enabled {
            self.detect_anomalies().await?;
        }

        Ok(())
    }

    /// Update behavioral profiles
    async fn update_behavioral_profiles(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // This would analyze recent security events to update user behavioral profiles
        // For now, this is a placeholder implementation

        // Example: Update a user profile
        let user_id = "user123";
        let profile = self.user_profiles.entry(user_id.to_string())
            .or_insert_with(|| UserBehavioralProfile {
                user_id: user_id.to_string(),
                baseline_metrics: BehavioralMetrics::default(),
                current_metrics: BehavioralMetrics::default(),
                anomaly_score: 0.0,
                risk_level: RiskLevel::Low,
                last_updated: Utc::now(),
                behavioral_patterns: vec![],
                risk_factors: vec![],
            });

        // Update current metrics (placeholder)
        profile.current_metrics.login_frequency = 2.5; // logins per day
        profile.current_metrics.session_duration_avg = 45.0; // minutes
        profile.anomaly_score = self.calculate_anomaly_score(&profile.baseline_metrics, &profile.current_metrics);
        profile.risk_level = self.determine_risk_level(profile.anomaly_score);
        profile.last_updated = Utc::now();

        Ok(())
    }

    /// Perform risk scoring
    async fn perform_risk_scoring(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        for (user_id, profile) in &self.user_profiles {
            let risk_assessment = RiskAssessment {
                entity_id: user_id.clone(),
                entity_type: "user".to_string(),
                overall_risk_score: profile.anomaly_score * 10.0, // Convert to 0-10 scale
                risk_factors: profile.risk_factors.clone(),
                risk_trend: TrendDirection::Stable, // Would be calculated from historical data
                mitigation_status: HashMap::new(),
                recommended_actions: vec![
                    "Monitor user activity closely".to_string(),
                    "Review recent authentication patterns".to_string(),
                ],
                assessment_date: Utc::now(),
            };

            self.risk_assessments.insert(user_id.clone(), risk_assessment);
        }

        Ok(())
    }

    /// Analyze trends
    async fn analyze_trends(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if self.historical_metrics.len() < 2 {
            return Ok(());
        }

        let analysis = SecurityTrendAnalysis {
            analysis_period: (
                self.historical_metrics.front().unwrap().timestamp,
                self.historical_metrics.back().unwrap().timestamp,
            ),
            overall_trend: TrendDirection::Stable,
            trend_confidence: 0.8,
            key_findings: vec![
                "Authentication success rate is stable".to_string(),
                "Threat detection rate has improved by 5%".to_string(),
            ],
            metric_trends: self.calculate_metric_trends(),
            anomaly_summary: self.calculate_anomaly_summary(),
            predictive_insights: vec![],
            recommendations: vec![
                "Continue monitoring authentication patterns".to_string(),
                "Review threat detection improvements".to_string(),
            ],
        };

        self.trend_analysis_cache = Some(analysis);

        Ok(())
    }

    /// Perform comparative analysis
    async fn perform_comparative_analysis(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // This would compare current metrics with historical baselines
        // For now, this is a placeholder
        Ok(())
    }

    /// Generate predictive insights
    async fn generate_predictive_insights(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if let Some(model) = &self.predictive_model {
            // Generate predictive insights using the model
            let insights = model.generate_insights().await?;
            if let Some(analysis) = &mut self.trend_analysis_cache {
                analysis.predictive_insights = insights;
            }
        }

        Ok(())
    }

    /// Detect anomalies
    async fn detect_anomalies(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        for profile in self.user_profiles.values_mut() {
            let anomalies = self.anomaly_detector.detect_anomalies(&profile.current_metrics, &profile.baseline_metrics).await?;
            profile.anomaly_score = anomalies.iter().map(|a| a.severity_score).sum::<f64>() / anomalies.len() as f64;
        }

        Ok(())
    }

    /// Get user behavioral profile
    pub fn get_user_profile(&self, user_id: &str) -> Option<&UserBehavioralProfile> {
        self.user_profiles.get(user_id)
    }

    /// Get all user profiles
    pub fn get_all_user_profiles(&self) -> Vec<&UserBehavioralProfile> {
        self.user_profiles.values().collect()
    }

    /// Get risk assessment for entity
    pub fn get_risk_assessment(&self, entity_id: &str) -> Option<&RiskAssessment> {
        self.risk_assessments.get(entity_id)
    }

    /// Get latest trend analysis
    pub fn get_latest_trend_analysis(&self) -> Option<&SecurityTrendAnalysis> {
        self.trend_analysis_cache.as_ref()
    }

    /// Get behavioral analytics for user
    pub fn get_behavioral_analytics(&self, user_id: &str) -> Option<BehavioralAnalytics> {
        self.user_profiles.get(user_id).map(|profile| BehavioralAnalytics {
            user_id: user_id.to_string(),
            current_risk_level: profile.risk_level.clone(),
            anomaly_score: profile.anomaly_score,
            behavioral_patterns: profile.behavioral_patterns.clone(),
            risk_factors: profile.risk_factors.clone(),
            recommendations: self.generate_user_recommendations(profile),
        })
    }

    /// Get security insights
    pub fn get_security_insights(&self) -> Vec<SecurityInsight> {
        let mut insights = Vec::new();

        // Generate insights from current analytics
        if let Some(trend_analysis) = &self.trend_analysis_cache {
            for finding in &trend_analysis.key_findings {
                insights.push(SecurityInsight {
                    insight_type: InsightType::SecurityOptimization,
                    title: "Trend Analysis Finding".to_string(),
                    description: finding.clone(),
                    confidence: trend_analysis.trend_confidence,
                    severity: InsightSeverity::Info,
                    affected_entities: vec![],
                    recommended_actions: vec![],
                });
            }
        }

        insights
    }

    /// Shutdown the analytics engine
    pub async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Clean up resources
        Ok(())
    }

    // Private helper methods

    fn calculate_anomaly_score(&self, baseline: &BehavioralMetrics, current: &BehavioralMetrics) -> f64 {
        let mut score = 0.0;
        let mut factors = 0;

        // Compare login frequency
        if baseline.login_frequency > 0.0 {
            let freq_diff = (current.login_frequency - baseline.login_frequency).abs() / baseline.login_frequency;
            score += freq_diff.min(1.0);
            factors += 1;
        }

        // Compare session duration
        if baseline.session_duration_avg > 0.0 {
            let duration_diff = (current.session_duration_avg - baseline.session_duration_avg).abs() / baseline.session_duration_avg;
            score += duration_diff.min(1.0);
            factors += 1;
        }

        // Compare failed auth rate
        if current.failed_authentication_rate > 0.1 { // More than 10% failure rate
            score += (current.failed_authentication_rate - 0.1) * 2.0;
            factors += 1;
        }

        if factors > 0 {
            score / factors as f64
        } else {
            0.0
        }
    }

    fn determine_risk_level(&self, anomaly_score: f64) -> RiskLevel {
        match anomaly_score {
            s if s < 0.3 => RiskLevel::Low,
            s if s < 0.6 => RiskLevel::Medium,
            s if s < 0.8 => RiskLevel::High,
            _ => RiskLevel::Critical,
        }
    }

    fn calculate_metric_trends(&self) -> HashMap<String, MetricTrend> {
        let mut trends = HashMap::new();

        if self.historical_metrics.len() >= 2 {
            let current = self.historical_metrics.back().unwrap();
            let previous = self.historical_metrics.get(self.historical_metrics.len() - 2).unwrap();

            // Calculate trends for key metrics
            trends.insert("authentication_attempts".to_string(), MetricTrend {
                metric_name: "authentication_attempts".to_string(),
                current_value: current.authentication_metrics.total_authentication_attempts as f64,
                previous_value: previous.authentication_metrics.total_authentication_attempts as f64,
                change_percentage: self.calculate_percentage_change(
                    previous.authentication_metrics.total_authentication_attempts as f64,
                    current.authentication_metrics.total_authentication_attempts as f64,
                ),
                trend_direction: TrendDirection::Stable,
                volatility: 0.1,
                seasonal_pattern: None,
            });
        }

        trends
    }

    fn calculate_percentage_change(&self, old_value: f64, new_value: f64) -> f64 {
        if old_value == 0.0 {
            if new_value > 0.0 { 100.0 } else { 0.0 }
        } else {
            ((new_value - old_value) / old_value) * 100.0
        }
    }

    fn calculate_anomaly_summary(&self) -> AnomalySummary {
        // Placeholder implementation
        AnomalySummary {
            total_anomalies: 5,
            anomaly_types: HashMap::from([
                ("behavioral".to_string(), 3),
                ("network".to_string(), 2),
            ]),
            anomaly_severity_distribution: HashMap::from([
                ("low".to_string(), 2),
                ("medium".to_string(), 2),
                ("high".to_string(), 1),
            ]),
            most_common_anomalies: vec![
                "Unusual login time".to_string(),
                "Abnormal data access pattern".to_string(),
            ],
            anomaly_trend: TrendDirection::Stable,
        }
    }

    fn generate_user_recommendations(&self, profile: &UserBehavioralProfile) -> Vec<String> {
        let mut recommendations = Vec::new();

        if profile.anomaly_score > 0.5 {
            recommendations.push("Review recent user activity for anomalies".to_string());
        }

        if profile.current_metrics.failed_authentication_rate > 0.1 {
            recommendations.push("Investigate frequent authentication failures".to_string());
        }

        if profile.current_metrics.privilege_escalation_attempts > 0 {
            recommendations.push("Review privilege escalation attempts".to_string());
        }

        recommendations
    }
}

/// Behavioral analytics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BehavioralAnalytics {
    pub user_id: String,
    pub current_risk_level: RiskLevel,
    pub anomaly_score: f64,
    pub behavioral_patterns: Vec<BehavioralPattern>,
    pub risk_factors: Vec<String>,
    pub recommendations: Vec<String>,
}

/// Security insight
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityInsight {
    pub insight_type: InsightType,
    pub title: String,
    pub description: String,
    pub confidence: f64,
    pub severity: InsightSeverity,
    pub affected_entities: Vec<String>,
    pub recommended_actions: Vec<String>,
}

/// Insight severity
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum InsightSeverity {
    Info,
    Warning,
    Critical,
}

/// Anomaly detector
pub struct AnomalyDetector {
    // Anomaly detection logic would go here
}

impl AnomalyDetector {
    pub fn new() -> Self {
        Self {}
    }

    async fn detect_anomalies(&self, _current: &BehavioralMetrics, _baseline: &BehavioralMetrics) -> Result<Vec<Anomaly>, Box<dyn std::error::Error + Send + Sync>> {
        // Placeholder anomaly detection
        Ok(vec![
            Anomaly {
                anomaly_type: "behavioral".to_string(),
                severity_score: 0.3,
                description: "Slight deviation from normal behavior".to_string(),
                confidence: 0.8,
            }
        ])
    }
}

/// Anomaly
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Anomaly {
    pub anomaly_type: String,
    pub severity_score: f64,
    pub description: String,
    pub confidence: f64,
}

/// Predictive model
pub struct PredictiveModel {
    // Predictive modeling logic would go here
}

impl PredictiveModel {
    pub fn new() -> Self {
        Self {}
    }

    async fn generate_insights(&self) -> Result<Vec<PredictiveInsight>, Box<dyn std::error::Error + Send + Sync>> {
        // Placeholder predictive insights
        Ok(vec![
            PredictiveInsight {
                insight_id: "pred_001".to_string(),
                insight_type: InsightType::ThreatPrediction,
                title: "Potential Brute Force Attack".to_string(),
                description: "Based on current patterns, there may be an increased risk of brute force attacks in the next 24 hours".to_string(),
                confidence: 0.75,
                time_horizon: "24 hours".to_string(),
                affected_entities: vec!["authentication_system".to_string()],
                recommended_actions: vec![
                    "Enable rate limiting".to_string(),
                    "Monitor authentication logs closely".to_string(),
                ],
                risk_score: 6.5,
            }
        ])
    }
}

/// Default implementations
impl Default for BehavioralMetrics {
    fn default() -> Self {
        Self {
            login_frequency: 1.0,
            session_duration_avg: 30.0,
            access_pattern_entropy: 0.5,
            time_based_anomaly_score: 0.0,
            resource_access_diversity: 0.3,
            failed_authentication_rate: 0.05,
            privilege_escalation_attempts: 0,
            data_access_volume: 1024,
            network_anomaly_score: 0.0,
        }
    }
}