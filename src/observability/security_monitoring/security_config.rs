//! # Security Monitoring Configuration
//!
//! Comprehensive configuration system for all security monitoring features.

use serde::{Deserialize, Serialize};

/// Main security monitoring configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SecurityMonitoringConfig {
    pub enabled: bool,
    pub event_monitoring: super::event_monitoring::SecurityEventMonitoringConfig,
    pub compliance_monitoring: super::compliance_monitoring::ComplianceMonitoringConfig,
    pub threat_detection: super::threat_detection::ThreatDetectionConfig,
    pub metrics: super::security_metrics::SecurityMetricsConfig,
    pub alerting: super::security_alerting::SecurityAlertingConfig,
    pub analytics: super::security_analytics::SecurityAnalyticsConfig,
    pub storage: super::security_storage::SecurityStorageConfig,
    pub dashboard: super::security_dashboard::SecurityDashboardConfig,
    pub api: super::SecurityApiConfig,
}

impl Default for SecurityMonitoringConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            event_monitoring: super::event_monitoring::SecurityEventMonitoringConfig::default(),
            compliance_monitoring: super::compliance_monitoring::ComplianceMonitoringConfig::default(),
            threat_detection: super::threat_detection::ThreatDetectionConfig::default(),
            metrics: super::security_metrics::SecurityMetricsConfig::default(),
            alerting: super::security_alerting::SecurityAlertingConfig::default(),
            analytics: super::security_analytics::SecurityAnalyticsConfig::default(),
            storage: super::security_storage::SecurityStorageConfig::default(),
            dashboard: super::security_dashboard::SecurityDashboardConfig::default(),
            api: super::SecurityApiConfig::default(),
        }
    }
}