//! # Security Metrics and KPIs
//!
//! Comprehensive security metrics collection, failed authentication rate monitoring,
//! security event frequency analysis, compliance violation tracking, and security health score calculation.

use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, Duration};

/// Configuration for security metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityMetricsConfig {
    pub enabled: bool,
    pub collection_interval_seconds: u64,
    pub retention_period_days: u32,
    pub health_score_weights: HealthScoreWeights,
    pub alert_thresholds: MetricsAlertThresholds,
    pub custom_metrics: Vec<CustomMetricDefinition>,
}

impl Default for SecurityMetricsConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            collection_interval_seconds: 60,
            retention_period_days: 90,
            health_score_weights: HealthScoreWeights::default(),
            alert_thresholds: MetricsAlertThresholds::default(),
            custom_metrics: vec![],
        }
    }
}

/// Health score weights
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct HealthScoreWeights {
    pub authentication_weight: f64,
    pub authorization_weight: f64,
    pub threat_detection_weight: f64,
    pub compliance_weight: f64,
    pub system_health_weight: f64,
    pub incident_response_weight: f64,
}

impl Default for HealthScoreWeights {
    fn default() -> Self {
        Self {
            authentication_weight: 0.2,
            authorization_weight: 0.2,
            threat_detection_weight: 0.25,
            compliance_weight: 0.15,
            system_health_weight: 0.1,
            incident_response_weight: 0.1,
        }
    }
}

/// Metrics alert thresholds
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricsAlertThresholds {
    pub failed_auth_rate_threshold: f64,
    pub suspicious_activity_threshold: u64,
    pub compliance_violation_threshold: u32,
    pub health_score_threshold: f64,
    pub incident_response_time_threshold: u64, // seconds
}

impl Default for MetricsAlertThresholds {
    fn default() -> Self {
        Self {
            failed_auth_rate_threshold: 0.1, // 10% failure rate
            suspicious_activity_threshold: 10,
            compliance_violation_threshold: 5,
            health_score_threshold: 70.0,
            incident_response_time_threshold: 3600, // 1 hour
        }
    }
}

/// Custom metric definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CustomMetricDefinition {
    pub name: String,
    pub description: String,
    pub metric_type: MetricType,
    pub collection_query: String,
    pub unit: String,
    pub enabled: bool,
}

/// Metric type
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum MetricType {
    Counter,
    Gauge,
    Histogram,
    Summary,
}

/// Security metrics snapshot
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityMetricsSnapshot {
    pub timestamp: DateTime<Utc>,
    pub authentication_metrics: AuthenticationMetrics,
    pub authorization_metrics: AuthorizationMetrics,
    pub threat_detection_metrics: ThreatDetectionMetrics,
    pub compliance_metrics: ComplianceMetrics,
    pub system_health_metrics: SystemHealthMetrics,
    pub incident_response_metrics: IncidentResponseMetrics,
    pub custom_metrics: HashMap<String, MetricValue>,
}

/// Authentication metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthenticationMetrics {
    pub total_authentication_attempts: u64,
    pub successful_authentications: u64,
    pub failed_authentications: u64,
    pub authentication_success_rate: f64,
    pub failed_auth_rate: f64,
    pub brute_force_attempts_detected: u64,
    pub multi_factor_auth_usage: f64,
    pub authentication_methods: HashMap<String, u64>,
}

/// Authorization metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthorizationMetrics {
    pub total_authorization_requests: u64,
    pub successful_authorizations: u64,
    pub failed_authorizations: u64,
    pub authorization_success_rate: f64,
    pub privilege_escalation_attempts: u64,
    pub role_based_access_violations: u64,
    pub resource_access_denials: u64,
}

/// Threat detection metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreatDetectionMetrics {
    pub threats_detected: u64,
    pub incidents_created: u64,
    pub active_incidents: u64,
    pub resolved_incidents: u64,
    pub false_positives: u64,
    pub threat_types: HashMap<String, u64>,
    pub detection_accuracy: f64,
    pub average_response_time: f64,
}

/// Compliance metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceMetrics {
    pub compliance_scans_performed: u64,
    pub compliance_violations_found: u64,
    pub compliance_violations_resolved: u64,
    pub compliance_score: f64,
    pub frameworks_assessed: Vec<String>,
    pub audit_trail_integrity_score: f64,
    pub data_retention_compliance_score: f64,
}

/// System health metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemHealthMetrics {
    pub system_uptime: u64,
    pub security_service_availability: f64,
    pub resource_utilization: ResourceUtilization,
    pub error_rate: f64,
    pub performance_degradation_score: f64,
}

/// Resource utilization
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceUtilization {
    pub cpu_usage_percent: f64,
    pub memory_usage_percent: f64,
    pub disk_usage_percent: f64,
    pub network_bandwidth_usage: f64,
}

/// Incident response metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IncidentResponseMetrics {
    pub average_detection_time: f64,
    pub average_response_time: f64,
    pub average_resolution_time: f64,
    pub incidents_by_severity: HashMap<String, u64>,
    pub automated_response_rate: f64,
    pub manual_intervention_rate: f64,
}

/// Metric value
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricValue {
    pub value: f64,
    pub timestamp: DateTime<Utc>,
    pub labels: HashMap<String, String>,
}

/// Security health score
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityHealthScore {
    pub overall_score: f64,
    pub component_scores: HashMap<String, f64>,
    pub risk_factors: Vec<RiskFactor>,
    pub recommendations: Vec<String>,
    pub trend: HealthScoreTrend,
    pub last_updated: DateTime<Utc>,
}

/// Risk factor
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskFactor {
    pub factor: String,
    pub impact: f64,
    pub description: String,
    pub mitigation_status: String,
}

/// Health score trend
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum HealthScoreTrend {
    Improving,
    Stable,
    Declining,
    Critical,
}

/// Security metrics collector
pub struct SecurityMetricsCollector {
    config: SecurityMetricsConfig,
    current_metrics: SecurityMetricsSnapshot,
    historical_metrics: Vec<SecurityMetricsSnapshot>,
    health_scores: Vec<SecurityHealthScore>,
    custom_metric_collectors: HashMap<String, Box<dyn CustomMetricCollector>>,
}

#[async_trait::async_trait]
pub trait CustomMetricCollector: Send + Sync {
    async fn collect_metric(&self) -> Result<MetricValue, Box<dyn std::error::Error + Send + Sync>>;
}

/// Security metrics collector implementation
impl SecurityMetricsCollector {
    /// Create a new security metrics collector
    pub fn new(config: SecurityMetricsConfig) -> Self {
        Self {
            config,
            current_metrics: SecurityMetricsSnapshot::default(),
            historical_metrics: Vec::new(),
            health_scores: Vec::new(),
            custom_metric_collectors: HashMap::new(),
        }
    }

    /// Initialize the metrics collector
    pub async fn initialize(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Initialize custom metric collectors
        for metric_def in &self.config.custom_metrics {
            if metric_def.enabled {
                let collector = self.create_custom_metric_collector(metric_def).await?;
                self.custom_metric_collectors.insert(metric_def.name.clone(), collector);
            }
        }

        Ok(())
    }

    /// Collect security metrics
    pub async fn collect_metrics(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut metrics = SecurityMetricsSnapshot {
            timestamp: Utc::now(),
            authentication_metrics: self.collect_authentication_metrics().await?,
            authorization_metrics: self.collect_authorization_metrics().await?,
            threat_detection_metrics: self.collect_threat_detection_metrics().await?,
            compliance_metrics: self.collect_compliance_metrics().await?,
            system_health_metrics: self.collect_system_health_metrics().await?,
            incident_response_metrics: self.collect_incident_response_metrics().await?,
            custom_metrics: HashMap::new(),
        };

        // Collect custom metrics
        for (name, collector) in &self.custom_metric_collectors {
            match collector.collect_metric().await {
                Ok(value) => {
                    metrics.custom_metrics.insert(name.clone(), value);
                }
                Err(e) => {
                    eprintln!("Error collecting custom metric {}: {}", name, e);
                }
            }
        }

        // Store current metrics
        self.current_metrics = metrics;

        // Store in historical data
        self.historical_metrics.push(self.current_metrics.clone());

        // Keep historical data size manageable
        let max_history = (self.config.retention_period_days as usize * 24 * 60) / self.config.collection_interval_seconds as usize;
        if self.historical_metrics.len() > max_history {
            self.historical_metrics.remove(0);
        }

        // Calculate and store health score
        let health_score = self.calculate_health_score().await?;
        self.health_scores.push(health_score);

        // Keep health scores manageable
        if self.health_scores.len() > 1000 {
            self.health_scores.remove(0);
        }

        Ok(())
    }

    /// Get current metrics
    pub fn get_current_metrics(&self) -> &SecurityMetricsSnapshot {
        &self.current_metrics
    }

    /// Get historical metrics
    pub fn get_historical_metrics(&self, days: u32) -> Vec<SecurityMetricsSnapshot> {
        let cutoff = Utc::now() - Duration::days(days as i64);
        self.historical_metrics.iter()
            .filter(|m| m.timestamp >= cutoff)
            .cloned()
            .collect()
    }

    /// Calculate security health score
    pub async fn calculate_health_score(&self) -> Result<SecurityHealthScore, Box<dyn std::error::Error + Send + Sync>> {
        let weights = &self.config.health_score_weights;

        // Calculate component scores
        let auth_score = self.calculate_authentication_score();
        let authz_score = self.calculate_authorization_score();
        let threat_score = self.calculate_threat_detection_score();
        let compliance_score = self.calculate_compliance_score();
        let system_score = self.calculate_system_health_score();
        let incident_score = self.calculate_incident_response_score();

        // Calculate weighted overall score
        let overall_score = (
            auth_score * weights.authentication_weight +
            authz_score * weights.authorization_weight +
            threat_score * weights.threat_detection_weight +
            compliance_score * weights.compliance_weight +
            system_score * weights.system_health_weight +
            incident_score * weights.incident_response_weight
        );

        let component_scores = HashMap::from([
            ("authentication".to_string(), auth_score),
            ("authorization".to_string(), authz_score),
            ("threat_detection".to_string(), threat_score),
            ("compliance".to_string(), compliance_score),
            ("system_health".to_string(), system_score),
            ("incident_response".to_string(), incident_score),
        ]);

        // Determine risk factors
        let risk_factors = self.identify_risk_factors(&component_scores);

        // Generate recommendations
        let recommendations = self.generate_recommendations(&component_scores);

        // Determine trend
        let trend = self.calculate_health_trend();

        Ok(SecurityHealthScore {
            overall_score,
            component_scores,
            risk_factors,
            recommendations,
            trend,
            last_updated: Utc::now(),
        })
    }

    /// Get latest health score
    pub fn get_latest_health_score(&self) -> Option<&SecurityHealthScore> {
        self.health_scores.last()
    }

    /// Get health score history
    pub fn get_health_score_history(&self, days: u32) -> Vec<&SecurityHealthScore> {
        let cutoff = Utc::now() - Duration::days(days as i64);
        self.health_scores.iter()
            .filter(|h| h.last_updated >= cutoff)
            .collect()
    }

    /// Get metrics summary
    pub fn get_metrics_summary(&self) -> MetricsSummary {
        MetricsSummary {
            total_events: self.current_metrics.authentication_metrics.total_authentication_attempts +
                         self.current_metrics.authorization_metrics.total_authorization_requests,
            failed_auth_rate: self.current_metrics.authentication_metrics.failed_auth_rate,
            active_threats: self.current_metrics.threat_detection_metrics.active_incidents,
            compliance_score: self.current_metrics.compliance_metrics.compliance_score,
            system_health: self.current_metrics.system_health_metrics.security_service_availability,
            last_updated: self.current_metrics.timestamp,
        }
    }

    /// Shutdown the metrics collector
    pub async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Clean up resources
        Ok(())
    }

    // Private helper methods

    async fn collect_authentication_metrics(&self) -> Result<AuthenticationMetrics, Box<dyn std::error::Error + Send + Sync>> {
        // This would collect actual authentication metrics from the system
        // For now, return placeholder data
        Ok(AuthenticationMetrics {
            total_authentication_attempts: 1000,
            successful_authentications: 950,
            failed_authentications: 50,
            authentication_success_rate: 0.95,
            failed_auth_rate: 0.05,
            brute_force_attempts_detected: 2,
            multi_factor_auth_usage: 0.8,
            authentication_methods: HashMap::from([
                ("password".to_string(), 800),
                ("oauth".to_string(), 150),
                ("mfa".to_string(), 50),
            ]),
        })
    }

    async fn collect_authorization_metrics(&self) -> Result<AuthorizationMetrics, Box<dyn std::error::Error + Send + Sync>> {
        // Placeholder implementation
        Ok(AuthorizationMetrics {
            total_authorization_requests: 5000,
            successful_authorizations: 4800,
            failed_authorizations: 200,
            authorization_success_rate: 0.96,
            privilege_escalation_attempts: 5,
            role_based_access_violations: 10,
            resource_access_denials: 200,
        })
    }

    async fn collect_threat_detection_metrics(&self) -> Result<ThreatDetectionMetrics, Box<dyn std::error::Error + Send + Sync>> {
        // Placeholder implementation
        Ok(ThreatDetectionMetrics {
            threats_detected: 25,
            incidents_created: 8,
            active_incidents: 3,
            resolved_incidents: 5,
            false_positives: 2,
            threat_types: HashMap::from([
                ("brute_force".to_string(), 10),
                ("unauthorized_access".to_string(), 8),
                ("data_exfiltration".to_string(), 7),
            ]),
            detection_accuracy: 0.92,
            average_response_time: 180.0, // seconds
        })
    }

    async fn collect_compliance_metrics(&self) -> Result<ComplianceMetrics, Box<dyn std::error::Error + Send + Sync>> {
        // Placeholder implementation
        Ok(ComplianceMetrics {
            compliance_scans_performed: 12,
            compliance_violations_found: 3,
            compliance_violations_resolved: 2,
            compliance_score: 87.5,
            frameworks_assessed: vec!["GDPR".to_string(), "CCPA".to_string(), "HIPAA".to_string()],
            audit_trail_integrity_score: 98.5,
            data_retention_compliance_score: 92.0,
        })
    }

    async fn collect_system_health_metrics(&self) -> Result<SystemHealthMetrics, Box<dyn std::error::Error + Send + Sync>> {
        // Placeholder implementation
        Ok(SystemHealthMetrics {
            system_uptime: 86400, // 24 hours in seconds
            security_service_availability: 99.9,
            resource_utilization: ResourceUtilization {
                cpu_usage_percent: 45.0,
                memory_usage_percent: 60.0,
                disk_usage_percent: 35.0,
                network_bandwidth_usage: 25.0,
            },
            error_rate: 0.02,
            performance_degradation_score: 2.0,
        })
    }

    async fn collect_incident_response_metrics(&self) -> Result<IncidentResponseMetrics, Box<dyn std::error::Error + Send + Sync>> {
        // Placeholder implementation
        Ok(IncidentResponseMetrics {
            average_detection_time: 120.0, // seconds
            average_response_time: 300.0,  // seconds
            average_resolution_time: 1800.0, // seconds
            incidents_by_severity: HashMap::from([
                ("low".to_string(), 5),
                ("medium".to_string(), 8),
                ("high".to_string(), 3),
                ("critical".to_string(), 1),
            ]),
            automated_response_rate: 0.75,
            manual_intervention_rate: 0.25,
        })
    }

    async fn create_custom_metric_collector(&self, metric_def: &CustomMetricDefinition) -> Result<Box<dyn CustomMetricCollector>, Box<dyn std::error::Error + Send + Sync>> {
        // This would create custom metric collectors based on the definition
        // For now, return a placeholder
        Ok(Box::new(PlaceholderMetricCollector::new(metric_def.clone())))
    }

    fn calculate_authentication_score(&self) -> f64 {
        let auth = &self.current_metrics.authentication_metrics;

        if auth.total_authentication_attempts == 0 {
            return 100.0;
        }

        // Lower failed auth rate = higher score
        let failed_rate_score = (1.0 - auth.failed_auth_rate) * 100.0;

        // Multi-factor auth usage bonus
        let mfa_bonus = auth.multi_factor_auth_usage * 10.0;

        // Brute force penalty
        let brute_force_penalty = if auth.brute_force_attempts_detected > 0 {
            (auth.brute_force_attempts_detected as f64 * 5.0).min(20.0)
        } else {
            0.0
        };

        (failed_rate_score + mfa_bonus - brute_force_penalty).max(0.0).min(100.0)
    }

    fn calculate_authorization_score(&self) -> f64 {
        let authz = &self.current_metrics.authorization_metrics;

        if authz.total_authorization_requests == 0 {
            return 100.0;
        }

        // Higher success rate = higher score
        let success_rate_score = authz.authorization_success_rate * 100.0;

        // Privilege escalation penalty
        let escalation_penalty = (authz.privilege_escalation_attempts as f64 * 10.0).min(30.0);

        // Access violation penalty
        let violation_penalty = ((authz.role_based_access_violations + authz.resource_access_denials) as f64 * 2.0).min(20.0);

        (success_rate_score - escalation_penalty - violation_penalty).max(0.0).min(100.0)
    }

    fn calculate_threat_detection_score(&self) -> f64 {
        let threats = &self.current_metrics.threat_detection_metrics;

        // Lower active incidents = higher score
        let incident_score = (1.0 - (threats.active_incidents as f64 / 10.0).min(1.0)) * 60.0;

        // Higher detection accuracy = higher score
        let accuracy_score = threats.detection_accuracy * 30.0;

        // Lower false positive rate = higher score
        let false_positive_rate = if threats.threats_detected > 0 {
            threats.false_positives as f64 / threats.threats_detected as f64
        } else {
            0.0
        };
        let false_positive_score = (1.0 - false_positive_rate) * 10.0;

        (incident_score + accuracy_score + false_positive_score).min(100.0)
    }

    fn calculate_compliance_score(&self) -> f64 {
        let compliance = &self.current_metrics.compliance_metrics;
        compliance.compliance_score
    }

    fn calculate_system_health_score(&self) -> f64 {
        let system = &self.current_metrics.system_health_metrics;

        // Higher availability = higher score
        let availability_score = system.security_service_availability;

        // Lower resource utilization = higher score (with some tolerance)
        let resource_score = 100.0 - (
            system.resource_utilization.cpu_usage_percent +
            system.resource_utilization.memory_usage_percent +
            system.resource_utilization.disk_usage_percent
        ) / 3.0 * 0.5; // 50% weight

        // Lower error rate = higher score
        let error_score = (1.0 - system.error_rate) * 100.0 * 0.3; // 30% weight

        (availability_score * 0.2 + resource_score * 0.5 + error_score * 0.3).min(100.0)
    }

    fn calculate_incident_response_score(&self) -> f64 {
        let incident = &self.current_metrics.incident_response_metrics;

        // Faster response times = higher score
        let response_time_score = (1.0 - (incident.average_response_time / 3600.0).min(1.0)) * 50.0;

        // Higher automated response rate = higher score
        let automation_score = incident.automated_response_rate * 30.0;

        // Lower manual intervention rate for routine incidents = higher score
        let manual_score = (1.0 - incident.manual_intervention_rate) * 20.0;

        (response_time_score + automation_score + manual_score).min(100.0)
    }

    fn identify_risk_factors(&self, component_scores: &HashMap<String, f64>) -> Vec<RiskFactor> {
        let mut risk_factors = Vec::new();

        for (component, score) in component_scores {
            if *score < 70.0 {
                let (factor, impact, description) = match component.as_str() {
                    "authentication" => ("Weak authentication controls", 8.0, "Authentication success rate is below acceptable threshold"),
                    "authorization" => ("Authorization issues", 7.0, "Authorization failures indicate access control problems"),
                    "threat_detection" => ("Threat detection gaps", 9.0, "Insufficient threat detection capabilities"),
                    "compliance" => ("Compliance violations", 6.0, "Non-compliance with regulatory requirements"),
                    "system_health" => ("System health issues", 5.0, "Security system performance degradation"),
                    "incident_response" => ("Slow incident response", 7.0, "Delayed incident response times"),
                    _ => ("Unknown risk", 5.0, "Unidentified risk factor"),
                };

                risk_factors.push(RiskFactor {
                    factor: factor.to_string(),
                    impact,
                    description: description.to_string(),
                    mitigation_status: "In progress".to_string(),
                });
            }
        }

        risk_factors
    }

    fn generate_recommendations(&self, component_scores: &HashMap<String, f64>) -> Vec<String> {
        let mut recommendations = Vec::new();

        for (component, score) in component_scores {
            if *score < 80.0 {
                let recommendation = match component.as_str() {
                    "authentication" => "Implement stronger authentication policies and multi-factor authentication",
                    "authorization" => "Review and strengthen access control policies",
                    "threat_detection" => "Enhance threat detection capabilities and update detection rules",
                    "compliance" => "Address compliance violations and improve audit processes",
                    "system_health" => "Optimize system performance and resource utilization",
                    "incident_response" => "Improve incident response processes and automation",
                    _ => "Review and improve security controls",
                };
                recommendations.push(recommendation.to_string());
            }
        }

        if recommendations.is_empty() {
            recommendations.push("Security posture is strong. Continue monitoring and maintenance.".to_string());
        }

        recommendations
    }

    fn calculate_health_trend(&self) -> HealthScoreTrend {
        if self.health_scores.len() < 2 {
            return HealthScoreTrend::Stable;
        }

        let recent_scores: Vec<f64> = self.health_scores.iter()
            .rev()
            .take(10)
            .map(|h| h.overall_score)
            .collect();

        if recent_scores.len() < 2 {
            return HealthScoreTrend::Stable;
        }

        let current_avg = recent_scores.iter().sum::<f64>() / recent_scores.len() as f64;
        let previous_avg = recent_scores.iter().skip(1).sum::<f64>() / (recent_scores.len() - 1) as f64;

        let change = current_avg - previous_avg;

        if change > 5.0 {
            HealthScoreTrend::Improving
        } else if change < -5.0 {
            HealthScoreTrend::Declining
        } else if current_avg < 50.0 {
            HealthScoreTrend::Critical
        } else {
            HealthScoreTrend::Stable
        }
    }
}

/// Placeholder metric collector
pub struct PlaceholderMetricCollector {
    definition: CustomMetricDefinition,
}

impl PlaceholderMetricCollector {
    pub fn new(definition: CustomMetricDefinition) -> Self {
        Self { definition }
    }
}

#[async_trait::async_trait]
impl CustomMetricCollector for PlaceholderMetricCollector {
    async fn collect_metric(&self) -> Result<MetricValue, Box<dyn std::error::Error + Send + Sync>> {
        // Placeholder implementation
        Ok(MetricValue {
            value: 42.0,
            timestamp: Utc::now(),
            labels: HashMap::new(),
        })
    }
}

/// Metrics summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricsSummary {
    pub total_events: u64,
    pub failed_auth_rate: f64,
    pub active_threats: u64,
    pub compliance_score: f64,
    pub system_health: f64,
    pub last_updated: DateTime<Utc>,
}

/// Default implementations
impl Default for SecurityMetricsSnapshot {
    fn default() -> Self {
        Self {
            timestamp: Utc::now(),
            authentication_metrics: AuthenticationMetrics::default(),
            authorization_metrics: AuthorizationMetrics::default(),
            threat_detection_metrics: ThreatDetectionMetrics::default(),
            compliance_metrics: ComplianceMetrics::default(),
            system_health_metrics: SystemHealthMetrics::default(),
            incident_response_metrics: IncidentResponseMetrics::default(),
            custom_metrics: HashMap::new(),
        }
    }
}

impl Default for AuthenticationMetrics {
    fn default() -> Self {
        Self {
            total_authentication_attempts: 0,
            successful_authentications: 0,
            failed_authentications: 0,
            authentication_success_rate: 0.0,
            failed_auth_rate: 0.0,
            brute_force_attempts_detected: 0,
            multi_factor_auth_usage: 0.0,
            authentication_methods: HashMap::new(),
        }
    }
}

impl Default for AuthorizationMetrics {
    fn default() -> Self {
        Self {
            total_authorization_requests: 0,
            successful_authorizations: 0,
            failed_authorizations: 0,
            authorization_success_rate: 0.0,
            privilege_escalation_attempts: 0,
            role_based_access_violations: 0,
            resource_access_denials: 0,
        }
    }
}

impl Default for ThreatDetectionMetrics {
    fn default() -> Self {
        Self {
            threats_detected: 0,
            incidents_created: 0,
            active_incidents: 0,
            resolved_incidents: 0,
            false_positives: 0,
            threat_types: HashMap::new(),
            detection_accuracy: 0.0,
            average_response_time: 0.0,
        }
    }
}

impl Default for ComplianceMetrics {
    fn default() -> Self {
        Self {
            compliance_scans_performed: 0,
            compliance_violations_found: 0,
            compliance_violations_resolved: 0,
            compliance_score: 0.0,
            frameworks_assessed: vec![],
            audit_trail_integrity_score: 0.0,
            data_retention_compliance_score: 0.0,
        }
    }
}

impl Default for SystemHealthMetrics {
    fn default() -> Self {
        Self {
            system_uptime: 0,
            security_service_availability: 0.0,
            resource_utilization: ResourceUtilization::default(),
            error_rate: 0.0,
            performance_degradation_score: 0.0,
        }
    }
}

impl Default for ResourceUtilization {
    fn default() -> Self {
        Self {
            cpu_usage_percent: 0.0,
            memory_usage_percent: 0.0,
            disk_usage_percent: 0.0,
            network_bandwidth_usage: 0.0,
        }
    }
}

impl Default for IncidentResponseMetrics {
    fn default() -> Self {
        Self {
            average_detection_time: 0.0,
            average_response_time: 0.0,
            average_resolution_time: 0.0,
            incidents_by_severity: HashMap::new(),
            automated_response_rate: 0.0,
            manual_intervention_rate: 0.0,
        }
    }
}