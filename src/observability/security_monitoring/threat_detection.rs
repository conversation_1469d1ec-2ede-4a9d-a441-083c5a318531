//! # Threat Detection Engine
//!
//! Advanced threat detection with brute force detection, data exfiltration monitoring,
//! unauthorized access detection, and security incident correlation.

use std::collections::{HashMap, HashSet, VecDeque};
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, Duration};

/// Configuration for threat detection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreatDetectionConfig {
    pub enabled: bool,
    pub detection_interval_seconds: u64,
    pub brute_force_detection_enabled: bool,
    pub data_exfiltration_detection_enabled: bool,
    pub unauthorized_access_detection_enabled: bool,
    pub anomaly_detection_enabled: bool,
    pub incident_correlation_enabled: bool,
    pub threat_intelligence_enabled: bool,
    pub behavioral_analysis_enabled: bool,
    pub alert_thresholds: ThreatAlertThresholds,
    pub detection_rules: Vec<DetectionRule>,
    pub correlation_rules: Vec<CorrelationRule>,
}

impl Default for ThreatDetectionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            detection_interval_seconds: 30,
            brute_force_detection_enabled: true,
            data_exfiltration_detection_enabled: true,
            unauthorized_access_detection_enabled: true,
            anomaly_detection_enabled: true,
            incident_correlation_enabled: true,
            threat_intelligence_enabled: true,
            behavioral_analysis_enabled: true,
            alert_thresholds: ThreatAlertThresholds::default(),
            detection_rules: vec![],
            correlation_rules: vec![],
        }
    }
}

/// Threat alert thresholds
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreatAlertThresholds {
    pub brute_force_attempts_threshold: u32,
    pub suspicious_login_threshold: u32,
    pub data_exfiltration_size_threshold: u64,
    pub unauthorized_access_threshold: u32,
    pub anomaly_score_threshold: f64,
    pub correlation_confidence_threshold: f64,
}

impl Default for ThreatAlertThresholds {
    fn default() -> Self {
        Self {
            brute_force_attempts_threshold: 5,
            suspicious_login_threshold: 3,
            data_exfiltration_size_threshold: 1024 * 1024, // 1MB
            unauthorized_access_threshold: 3,
            anomaly_score_threshold: 2.5,
            correlation_confidence_threshold: 0.7,
        }
    }
}

/// Detection rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetectionRule {
    pub id: String,
    pub name: String,
    pub threat_type: ThreatType,
    pub conditions: Vec<DetectionCondition>,
    pub severity: ThreatSeverity,
    pub enabled: bool,
    pub description: String,
}

/// Detection condition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetectionCondition {
    pub field: String,
    pub operator: String,
    pub value: String,
    pub weight: f64,
}

/// Correlation rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CorrelationRule {
    pub id: String,
    pub name: String,
    pub conditions: Vec<CorrelationCondition>,
    pub time_window_minutes: u32,
    pub confidence_threshold: f64,
    pub description: String,
    pub enabled: bool,
}

/// Correlation condition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CorrelationCondition {
    pub event_type: String,
    pub field: String,
    pub operator: String,
    pub value: String,
    pub weight: f64,
}

/// Threat type
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum ThreatType {
    BruteForce,
    DataExfiltration,
    UnauthorizedAccess,
    PrivilegeEscalation,
    Malware,
    Phishing,
    DDoS,
    InsiderThreat,
    Custom(String),
}

/// Threat severity
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum ThreatSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// Security incident
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityIncident {
    pub id: String,
    pub title: String,
    pub description: String,
    pub threat_type: ThreatType,
    pub severity: ThreatSeverity,
    pub status: IncidentStatus,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub resolved_at: Option<DateTime<Utc>>,
    pub assigned_to: Option<String>,
    pub affected_assets: Vec<String>,
    pub indicators: Vec<SecurityIndicator>,
    pub timeline: Vec<IncidentEvent>,
    pub mitigation_steps: Vec<String>,
    pub risk_score: f64,
    pub confidence_score: f64,
}

/// Incident status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum IncidentStatus {
    Open,
    Investigating,
    Mitigated,
    Resolved,
    Closed,
}

/// Security indicator
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityIndicator {
    pub indicator_type: String,
    pub value: String,
    pub confidence: f64,
    pub source: String,
    pub first_seen: DateTime<Utc>,
    pub last_seen: DateTime<Utc>,
}

/// Incident event
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IncidentEvent {
    pub timestamp: DateTime<Utc>,
    pub event_type: String,
    pub description: String,
    pub source: String,
    pub metadata: HashMap<String, String>,
}

/// Threat pattern
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreatPattern {
    pub pattern_id: String,
    pub pattern_type: ThreatType,
    pub description: String,
    pub indicators: Vec<String>,
    pub confidence: f64,
    pub first_detected: DateTime<Utc>,
    pub last_detected: DateTime<Utc>,
    pub frequency: u32,
}

/// Brute force attempt
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BruteForceAttempt {
    pub ip_address: String,
    pub username: Option<String>,
    pub attempt_count: u32,
    pub time_window_start: DateTime<Utc>,
    pub time_window_end: DateTime<Utc>,
    pub blocked: bool,
    pub user_agents: HashSet<String>,
}

/// Data exfiltration attempt
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataExfiltrationAttempt {
    pub source_ip: String,
    pub destination_ip: Option<String>,
    pub user_id: Option<String>,
    pub data_size: u64,
    pub data_types: Vec<String>,
    pub timestamp: DateTime<Utc>,
    pub blocked: bool,
    pub risk_score: f64,
}

/// Unauthorized access attempt
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UnauthorizedAccessAttempt {
    pub ip_address: String,
    pub user_id: Option<String>,
    pub resource: String,
    pub action: String,
    pub timestamp: DateTime<Utc>,
    pub blocked: bool,
    pub risk_score: f64,
}

/// Threat detection engine
pub struct ThreatDetectionEngine {
    config: ThreatDetectionConfig,
    active_incidents: HashMap<String, SecurityIncident>,
    threat_patterns: HashMap<String, ThreatPattern>,
    brute_force_attempts: HashMap<String, BruteForceAttempt>,
    data_exfiltration_attempts: VecDeque<DataExfiltrationAttempt>,
    unauthorized_access_attempts: VecDeque<UnauthorizedAccessAttempt>,
    recent_events: VecDeque<super::event_monitoring::SecurityEvent>,
    correlation_engine: CorrelationEngine,
}

impl ThreatDetectionEngine {
    /// Create a new threat detection engine
    pub fn new(config: ThreatDetectionConfig) -> Self {
        Self {
            config,
            active_incidents: HashMap::new(),
            threat_patterns: HashMap::new(),
            brute_force_attempts: HashMap::new(),
            data_exfiltration_attempts: VecDeque::new(),
            unauthorized_access_attempts: VecDeque::new(),
            recent_events: VecDeque::with_capacity(10000),
            correlation_engine: CorrelationEngine::new(),
        }
    }

    /// Initialize the threat detection engine
    pub async fn initialize(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Load detection rules
        self.load_detection_rules().await?;

        // Load correlation rules
        self.load_correlation_rules().await?;

        // Initialize threat intelligence feeds
        if self.config.threat_intelligence_enabled {
            self.initialize_threat_intelligence().await?;
        }

        Ok(())
    }

    /// Detect threats (main processing loop)
    pub async fn detect_threats(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Detect brute force attacks
        if self.config.brute_force_detection_enabled {
            self.detect_brute_force_attacks().await?;
        }

        // Detect data exfiltration
        if self.config.data_exfiltration_detection_enabled {
            self.detect_data_exfiltration().await?;
        }

        // Detect unauthorized access
        if self.config.unauthorized_access_detection_enabled {
            self.detect_unauthorized_access().await?;
        }

        // Detect anomalies
        if self.config.anomaly_detection_enabled {
            self.detect_anomalies().await?;
        }

        // Correlate incidents
        if self.config.incident_correlation_enabled {
            self.correlate_incidents().await?;
        }

        // Update threat patterns
        self.update_threat_patterns().await?;

        // Clean up old data
        self.cleanup_old_data().await?;

        Ok(())
    }

    /// Record security event for analysis
    pub async fn record_security_event(&mut self, event: super::event_monitoring::SecurityEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        self.recent_events.push_back(event.clone());

        // Keep buffer size manageable
        if self.recent_events.len() > 10000 {
            self.recent_events.pop_front();
        }

        // Analyze event immediately
        self.analyze_security_event(event).await?;

        Ok(())
    }

    /// Create security incident
    pub async fn create_incident(&mut self, title: String, description: String, threat_type: ThreatType, severity: ThreatSeverity, indicators: Vec<SecurityIndicator>) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let incident_id = format!("incident_{}", Utc::now().timestamp());

        let incident = SecurityIncident {
            id: incident_id.clone(),
            title,
            description,
            threat_type,
            severity,
            status: IncidentStatus::Open,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            resolved_at: None,
            assigned_to: None,
            affected_assets: vec![],
            indicators,
            timeline: vec![IncidentEvent {
                timestamp: Utc::now(),
                event_type: "incident_created".to_string(),
                description: "Security incident created".to_string(),
                source: "threat_detection_engine".to_string(),
                metadata: HashMap::new(),
            }],
            mitigation_steps: vec![],
            risk_score: self.calculate_risk_score(&threat_type, &severity),
            confidence_score: 0.8,
        };

        self.active_incidents.insert(incident_id.clone(), incident);

        Ok(incident_id)
    }

    /// Update incident status
    pub async fn update_incident_status(&mut self, incident_id: &str, status: IncidentStatus, assigned_to: Option<String>) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if let Some(incident) = self.active_incidents.get_mut(incident_id) {
            incident.status = status;
            incident.assigned_to = assigned_to;
            incident.updated_at = Utc::now();

            if matches!(status, IncidentStatus::Resolved | IncidentStatus::Closed) {
                incident.resolved_at = Some(Utc::now());
            }

            // Add timeline event
            incident.timeline.push(IncidentEvent {
                timestamp: Utc::now(),
                event_type: "status_updated".to_string(),
                description: format!("Incident status updated to {:?}", status),
                source: "threat_detection_engine".to_string(),
                metadata: HashMap::new(),
            });
        }

        Ok(())
    }

    /// Get active incidents
    pub fn get_active_incidents(&self) -> Vec<SecurityIncident> {
        self.active_incidents.values()
            .filter(|i| matches!(i.status, IncidentStatus::Open | IncidentStatus::Investigating))
            .cloned()
            .collect()
    }

    /// Get incident by ID
    pub fn get_incident(&self, incident_id: &str) -> Option<&SecurityIncident> {
        self.active_incidents.get(incident_id)
    }

    /// Get incidents by threat type
    pub fn get_incidents_by_threat_type(&self, threat_type: &ThreatType) -> Vec<SecurityIncident> {
        self.active_incidents.values()
            .filter(|i| &i.threat_type == threat_type)
            .cloned()
            .collect()
    }

    /// Get threat patterns
    pub fn get_threat_patterns(&self) -> Vec<&ThreatPattern> {
        self.threat_patterns.values().collect()
    }

    /// Shutdown the threat detection engine
    pub async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Clean up resources
        Ok(())
    }

    // Private helper methods

    async fn load_detection_rules(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Load default detection rules
        self.config.detection_rules = vec![
            DetectionRule {
                id: "brute_force_detection".to_string(),
                name: "Brute Force Attack Detection".to_string(),
                threat_type: ThreatType::BruteForce,
                conditions: vec![
                    DetectionCondition {
                        field: "event_type".to_string(),
                        operator: "equals".to_string(),
                        value: "failed_authentication".to_string(),
                        weight: 1.0,
                    },
                    DetectionCondition {
                        field: "attempt_count".to_string(),
                        operator: "greater_than".to_string(),
                        value: "5".to_string(),
                        weight: 0.8,
                    },
                ],
                severity: ThreatSeverity::High,
                enabled: true,
                description: "Detect multiple failed authentication attempts from same source".to_string(),
            },
            DetectionRule {
                id: "data_exfiltration_detection".to_string(),
                name: "Data Exfiltration Detection".to_string(),
                threat_type: ThreatType::DataExfiltration,
                conditions: vec![
                    DetectionCondition {
                        field: "data_size".to_string(),
                        operator: "greater_than".to_string(),
                        value: "1048576".to_string(), // 1MB
                        weight: 1.0,
                    },
                    DetectionCondition {
                        field: "destination".to_string(),
                        operator: "not_internal".to_string(),
                        value: "".to_string(),
                        weight: 0.7,
                    },
                ],
                severity: ThreatSeverity::Critical,
                enabled: true,
                description: "Detect large data transfers to external destinations".to_string(),
            },
        ];

        Ok(())
    }

    async fn load_correlation_rules(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Load default correlation rules
        self.config.correlation_rules = vec![
            CorrelationRule {
                id: "lateral_movement_correlation".to_string(),
                name: "Lateral Movement Detection".to_string(),
                conditions: vec![
                    CorrelationCondition {
                        event_type: "unauthorized_access".to_string(),
                        field: "user_id".to_string(),
                        operator: "equals".to_string(),
                        value: "any".to_string(),
                        weight: 1.0,
                    },
                    CorrelationCondition {
                        event_type: "privilege_escalation".to_string(),
                        field: "user_id".to_string(),
                        operator: "equals".to_string(),
                        value: "any".to_string(),
                        weight: 0.8,
                    },
                ],
                time_window_minutes: 30,
                confidence_threshold: 0.7,
                description: "Correlate unauthorized access with privilege escalation".to_string(),
                enabled: true,
            },
        ];

        Ok(())
    }

    async fn initialize_threat_intelligence(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Initialize threat intelligence feeds
        Ok(())
    }

    async fn detect_brute_force_attacks(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let cutoff = Utc::now() - Duration::minutes(15);

        // Group failed authentication events by IP
        let mut failed_attempts: HashMap<String, Vec<&super::event_monitoring::SecurityEvent>> = HashMap::new();

        for event in &self.recent_events {
            if event.timestamp >= cutoff &&
               matches!(event.event_type, super::event_monitoring::SecurityEventType::AuthenticationFailure) {
                if let Some(ip) = &event.source_ip {
                    failed_attempts.entry(ip.clone()).or_insert_with(Vec::new).push(event);
                }
            }
        }

        // Check for brute force patterns
        for (ip, attempts) in failed_attempts {
            if attempts.len() >= self.config.alert_thresholds.brute_force_attempts_threshold as usize {
                let attempt = BruteForceAttempt {
                    ip_address: ip.clone(),
                    username: attempts.first().and_then(|e| e.user_id.clone()),
                    attempt_count: attempts.len() as u32,
                    time_window_start: cutoff,
                    time_window_end: Utc::now(),
                    blocked: false,
                    user_agents: attempts.iter().filter_map(|e| e.user_agent.clone()).collect(),
                };

                self.brute_force_attempts.insert(ip.clone(), attempt);

                // Create incident if threshold exceeded
                if attempts.len() >= self.config.alert_thresholds.brute_force_attempts_threshold as usize {
                    let indicators = vec![SecurityIndicator {
                        indicator_type: "ip_address".to_string(),
                        value: ip.clone(),
                        confidence: 0.9,
                        source: "brute_force_detection".to_string(),
                        first_seen: attempts.first().unwrap().timestamp,
                        last_seen: attempts.last().unwrap().timestamp,
                    }];

                    self.create_incident(
                        "Brute Force Attack Detected".to_string(),
                        format!("Multiple failed authentication attempts from IP {}", ip),
                        ThreatType::BruteForce,
                        ThreatSeverity::High,
                        indicators,
                    ).await?;
                }
            }
        }

        Ok(())
    }

    async fn detect_data_exfiltration(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Analyze data access patterns for exfiltration indicators
        let cutoff = Utc::now() - Duration::hours(1);

        for event in &self.recent_events {
            if event.timestamp >= cutoff {
                match event.event_type {
                    super::event_monitoring::SecurityEventType::DataAccess |
                    super::event_monitoring::SecurityEventType::DataModification |
                    super::event_monitoring::SecurityEventType::DataDeletion => {
                        // Check for large data transfers or unusual patterns
                        // This is a simplified implementation
                        if let Some(metadata) = event.metadata.get("data_size") {
                            if let Ok(size) = metadata.parse::<u64>() {
                                if size >= self.config.alert_thresholds.data_exfiltration_size_threshold {
                                    let attempt = DataExfiltrationAttempt {
                                        source_ip: event.source_ip.clone().unwrap_or_default(),
                                        destination_ip: None,
                                        user_id: event.user_id.clone(),
                                        data_size: size,
                                        data_types: vec!["unknown".to_string()],
                                        timestamp: event.timestamp,
                                        blocked: false,
                                        risk_score: 8.0,
                                    };

                                    self.data_exfiltration_attempts.push_back(attempt);

                                    // Keep buffer size manageable
                                    if self.data_exfiltration_attempts.len() > 1000 {
                                        self.data_exfiltration_attempts.pop_front();
                                    }

                                    // Create incident
                                    let indicators = vec![SecurityIndicator {
                                        indicator_type: "data_transfer".to_string(),
                                        value: format!("{} bytes", size),
                                        confidence: 0.8,
                                        source: "data_exfiltration_detection".to_string(),
                                        first_seen: event.timestamp,
                                        last_seen: event.timestamp,
                                    }];

                                    self.create_incident(
                                        "Data Exfiltration Detected".to_string(),
                                        format!("Large data transfer of {} bytes detected", size),
                                        ThreatType::DataExfiltration,
                                        ThreatSeverity::Critical,
                                        indicators,
                                    ).await?;
                                }
                            }
                        }
                    }
                    _ => {}
                }
            }
        }

        Ok(())
    }

    async fn detect_unauthorized_access(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let cutoff = Utc::now() - Duration::hours(1);

        // Group unauthorized access events by IP
        let mut unauthorized_attempts: HashMap<String, Vec<&super::event_monitoring::SecurityEvent>> = HashMap::new();

        for event in &self.recent_events {
            if event.timestamp >= cutoff &&
               matches!(event.event_type, super::event_monitoring::SecurityEventType::UnauthorizedAccess) {
                if let Some(ip) = &event.source_ip {
                    unauthorized_attempts.entry(ip.clone()).or_insert_with(Vec::new).push(event);
                }
            }
        }

        // Check for patterns
        for (ip, attempts) in unauthorized_attempts {
            if attempts.len() >= self.config.alert_thresholds.unauthorized_access_threshold as usize {
                let attempt = UnauthorizedAccessAttempt {
                    ip_address: ip.clone(),
                    user_id: attempts.first().and_then(|e| e.user_id.clone()),
                    resource: attempts.first().and_then(|e| e.resource.clone()).unwrap_or_default(),
                    action: "unauthorized_access".to_string(),
                    timestamp: attempts.first().unwrap().timestamp,
                    blocked: true,
                    risk_score: 7.0,
                };

                self.unauthorized_access_attempts.push_back(attempt);

                // Keep buffer size manageable
                if self.unauthorized_access_attempts.len() > 1000 {
                    self.unauthorized_access_attempts.pop_front();
                }

                // Create incident
                let indicators = vec![SecurityIndicator {
                    indicator_type: "ip_address".to_string(),
                    value: ip.clone(),
                    confidence: 0.85,
                    source: "unauthorized_access_detection".to_string(),
                    first_seen: attempts.first().unwrap().timestamp,
                    last_seen: attempts.last().unwrap().timestamp,
                }];

                self.create_incident(
                    "Unauthorized Access Pattern Detected".to_string(),
                    format!("Multiple unauthorized access attempts from IP {}", ip),
                    ThreatType::UnauthorizedAccess,
                    ThreatSeverity::High,
                    indicators,
                ).await?;
            }
        }

        Ok(())
    }

    async fn detect_anomalies(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // This would integrate with the analytics engine for anomaly detection
        // For now, this is a placeholder
        Ok(())
    }

    async fn correlate_incidents(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Apply correlation rules to detect attack chains
        for rule in &self.config.correlation_rules {
            if !rule.enabled {
                continue;
            }

            let correlated_incidents = self.correlation_engine.correlate_incidents(rule, &self.active_incidents).await?;

            if !correlated_incidents.is_empty() {
                // Create correlated incident
                self.create_correlated_incident(rule, &correlated_incidents).await?;
            }
        }

        Ok(())
    }

    async fn analyze_security_event(&mut self, event: super::event_monitoring::SecurityEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Apply detection rules to the event
        for rule in &self.config.detection_rules {
            if !rule.enabled {
                continue;
            }

            if self.matches_detection_rule(&event, rule) {
                // Rule matched - create or update incident
                self.handle_rule_match(&event, rule).await?;
            }
        }

        Ok(())
    }

    fn matches_detection_rule(&self, event: &super::event_monitoring::SecurityEvent, rule: &DetectionRule) -> bool {
        let mut score = 0.0;

        for condition in &rule.conditions {
            if self.matches_detection_condition(event, condition) {
                score += condition.weight;
            }
        }

        score >= 1.0 // At least one condition must match with full weight
    }

    fn matches_detection_condition(&self, event: &super::event_monitoring::SecurityEvent, condition: &DetectionCondition) -> bool {
        match condition.field.as_str() {
            "event_type" => {
                let event_type_str = match event.event_type {
                    super::event_monitoring::SecurityEventType::AuthenticationFailure => "failed_authentication",
                    super::event_monitoring::SecurityEventType::UnauthorizedAccess => "unauthorized_access",
                    super::event_monitoring::SecurityEventType::DataAccess => "data_access",
                    _ => "other",
                };
                self.matches_condition_value(event_type_str, &condition.operator, &condition.value)
            }
            "source_ip" => {
                event.source_ip.as_ref()
                    .map(|ip| self.matches_condition_value(ip, &condition.operator, &condition.value))
                    .unwrap_or(false)
            }
            "user_id" => {
                event.user_id.as_ref()
                    .map(|user| self.matches_condition_value(user, &condition.operator, &condition.value))
                    .unwrap_or(false)
            }
            _ => false,
        }
    }

    fn matches_condition_value(&self, actual: &str, operator: &str, expected: &str) -> bool {
        match operator {
            "equals" => actual == expected,
            "contains" => actual.contains(expected),
            "greater_than" => actual.parse::<f64>().unwrap_or(0.0) > expected.parse::<f64>().unwrap_or(0.0),
            "less_than" => actual.parse::<f64>().unwrap_or(0.0) < expected.parse::<f64>().unwrap_or(0.0),
            _ => false,
        }
    }

    async fn handle_rule_match(&mut self, event: &super::event_monitoring::SecurityEvent, rule: &DetectionRule) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Create incident based on rule match
        let indicators = vec![SecurityIndicator {
            indicator_type: "rule_match".to_string(),
            value: rule.id.clone(),
            confidence: 0.8,
            source: "threat_detection_engine".to_string(),
            first_seen: event.timestamp,
            last_seen: event.timestamp,
        }];

        self.create_incident(
            rule.name.clone(),
            rule.description.clone(),
            rule.threat_type.clone(),
            rule.severity.clone(),
            indicators,
        ).await?;

        Ok(())
    }

    async fn create_correlated_incident(&mut self, rule: &CorrelationRule, incident_ids: &[String]) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let incidents: Vec<_> = incident_ids.iter()
            .filter_map(|id| self.active_incidents.get(id))
            .collect();

        if incidents.is_empty() {
            return Ok(());
        }

        let indicators: Vec<_> = incidents.iter()
            .flat_map(|i| i.indicators.clone())
            .collect();

        self.create_incident(
            format!("Correlated: {}", rule.name),
            rule.description.clone(),
            ThreatType::Custom("correlated_attack".to_string()),
            ThreatSeverity::High,
            indicators,
        ).await?;

        Ok(())
    }

    async fn update_threat_patterns(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Update threat patterns based on recent incidents
        for incident in self.active_incidents.values() {
            let pattern_id = format!("pattern_{}_{}", incident.threat_type.as_str(), incident.id);

            let pattern = self.threat_patterns.entry(pattern_id.clone())
                .or_insert_with(|| ThreatPattern {
                    pattern_id,
                    pattern_type: incident.threat_type.clone(),
                    description: format!("Pattern for {} incidents", incident.threat_type.as_str()),
                    indicators: vec![],
                    confidence: 0.0,
                    first_detected: incident.created_at,
                    last_detected: incident.created_at,
                    frequency: 0,
                });

            pattern.last_detected = incident.created_at;
            pattern.frequency += 1;
            pattern.confidence = (pattern.frequency as f64 / 10.0).min(1.0); // Simple confidence calculation
        }

        Ok(())
    }

    async fn cleanup_old_data(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let cutoff = Utc::now() - Duration::days(7);

        // Clean up old brute force attempts
        self.brute_force_attempts.retain(|_, attempt| attempt.time_window_end >= cutoff);

        // Clean up old data exfiltration attempts
        while let Some(attempt) = self.data_exfiltration_attempts.front() {
            if attempt.timestamp < cutoff {
                self.data_exfiltration_attempts.pop_front();
            } else {
                break;
            }
        }

        // Clean up old unauthorized access attempts
        while let Some(attempt) = self.unauthorized_access_attempts.front() {
            if attempt.timestamp < cutoff {
                self.unauthorized_access_attempts.pop_front();
            } else {
                break;
            }
        }

        // Clean up old events
        while let Some(event) = self.recent_events.front() {
            if event.timestamp < cutoff {
                self.recent_events.pop_front();
            } else {
                break;
            }
        }

        Ok(())
    }

    fn calculate_risk_score(&self, threat_type: &ThreatType, severity: &ThreatSeverity) -> f64 {
        let base_score = match threat_type {
            ThreatType::BruteForce => 6.0,
            ThreatType::DataExfiltration => 9.0,
            ThreatType::UnauthorizedAccess => 7.0,
            ThreatType::PrivilegeEscalation => 8.0,
            ThreatType::Malware => 9.0,
            ThreatType::Phishing => 5.0,
            ThreatType::DDoS => 7.0,
            ThreatType::InsiderThreat => 8.0,
            ThreatType::Custom(_) => 5.0,
        };

        let severity_multiplier = match severity {
            ThreatSeverity::Low => 0.5,
            ThreatSeverity::Medium => 0.75,
            ThreatSeverity::High => 1.0,
            ThreatSeverity::Critical => 1.5,
        };

        (base_score * severity_multiplier).min(10.0)
    }
}

impl ThreatType {
    pub fn as_str(&self) -> &str {
        match self {
            ThreatType::BruteForce => "brute_force",
            ThreatType::DataExfiltration => "data_exfiltration",
            ThreatType::UnauthorizedAccess => "unauthorized_access",
            ThreatType::PrivilegeEscalation => "privilege_escalation",
            ThreatType::Malware => "malware",
            ThreatType::Phishing => "phishing",
            ThreatType::DDoS => "ddos",
            ThreatType::InsiderThreat => "insider_threat",
            ThreatType::Custom(s) => s.as_str(),
        }
    }
}

/// Correlation engine
pub struct CorrelationEngine {
    // Correlation logic would go here
}

impl CorrelationEngine {
    pub fn new() -> Self {
        Self {}
    }

    async fn correlate_incidents(&self, _rule: &CorrelationRule, _incidents: &HashMap<String, SecurityIncident>) -> Result<Vec<String>, Box<dyn std::error::Error + Send + Sync>> {
        // Placeholder correlation logic
        Ok(vec![])
    }
}