//! # Custom Span Processors
//!
//! This module provides custom span processors for trace enrichment,
//! filtering, and aggregation capabilities.

use opentelemetry_sdk::trace::SpanProcessor;
use opentelemetry::trace::SpanRef;
use opentelemetry::KeyValue;
use std::collections::HashMap;
use std::sync::Arc;

/// Enrichment span processor that adds additional context to spans
#[derive(Debug)]
pub struct EnrichmentSpanProcessor {
    enrichments: HashMap<String, Vec<KeyValue>>,
}

impl EnrichmentSpanProcessor {
    /// Create a new enrichment span processor
    pub fn new() -> Self {
        let mut enrichments = HashMap::new();

        // Add default enrichments for different span types
        enrichments.insert(
            "http_request".to_string(),
            vec![
                KeyValue::new("enriched", true),
                KeyValue::new("enrichment.type", "http_context"),
            ],
        );

        enrichments.insert(
            "db_operation".to_string(),
            vec![
                KeyValue::new("enriched", true),
                KeyValue::new("enrichment.type", "database_context"),
            ],
        );

        enrichments.insert(
            "external_call".to_string(),
            vec![
                KeyValue::new("enriched", true),
                KeyValue::new("enrichment.type", "external_context"),
            ],
        );

        Self { enrichments }
    }

    /// Add custom enrichment for a span name pattern
    pub fn add_enrichment(&mut self, span_pattern: &str, attributes: Vec<KeyValue>) {
        self.enrichments.insert(span_pattern.to_string(), attributes);
    }
}

impl SpanProcessor for EnrichmentSpanProcessor {
    fn on_start(&self, span: &mut opentelemetry_sdk::trace::Span, _cx: &opentelemetry::Context) {
        let span_name = span.name();

        // Apply enrichments based on span name patterns
        for (pattern, attributes) in &self.enrichments {
            if span_name.contains(pattern) {
                for attr in attributes {
                    span.set_attribute(attr.clone());
                }
            }
        }

        // Add common enrichments
        span.set_attribute(KeyValue::new("processor.enriched_at", chrono::Utc::now().timestamp()));
        span.set_attribute(KeyValue::new("processor.version", env!("CARGO_PKG_VERSION")));
    }

    fn on_end(&self, _span: opentelemetry_sdk::export::trace::SpanData) {
        // No action needed on span end for enrichment
    }

    fn force_flush(&self) -> opentelemetry_sdk::export::trace::ExportResult {
        Ok(())
    }

    fn shutdown(&mut self) -> opentelemetry_sdk::export::trace::ExportResult {
        Ok(())
    }
}

/// Filtering span processor that can filter out spans based on criteria
#[derive(Debug)]
pub struct FilteringSpanProcessor {
    filters: Vec<Box<dyn SpanFilter + Send + Sync>>,
}

impl FilteringSpanProcessor {
    /// Create a new filtering span processor
    pub fn new() -> Self {
        Self {
            filters: Vec::new(),
        }
    }

    /// Add a filter to the processor
    pub fn add_filter<F: SpanFilter + Send + Sync + 'static>(&mut self, filter: F) {
        self.filters.push(Box::new(filter));
    }
}

impl SpanProcessor for FilteringSpanProcessor {
    fn on_start(&self, span: &mut opentelemetry_sdk::trace::Span, _cx: &opentelemetry::Context) {
        // Check if span should be filtered
        for filter in &self.filters {
            if filter.should_filter(span) {
                // Mark span for filtering by setting a special attribute
                span.set_attribute(KeyValue::new("filtered", true));
                span.set_attribute(KeyValue::new("filter.reason", filter.filter_reason()));
                break;
            }
        }
    }

    fn on_end(&self, _span: opentelemetry_sdk::export::trace::SpanData) {
        // No action needed on span end for filtering
    }

    fn force_flush(&self) -> opentelemetry_sdk::export::trace::ExportResult {
        Ok(())
    }

    fn shutdown(&mut self) -> opentelemetry_sdk::export::trace::ExportResult {
        Ok(())
    }
}

/// Trait for span filtering logic
pub trait SpanFilter {
    /// Determine if a span should be filtered
    fn should_filter(&self, span: &SpanRef<'_>) -> bool;

    /// Get the reason for filtering
    fn filter_reason(&self) -> &'static str;
}

/// Filter spans by duration threshold
pub struct DurationFilter {
    max_duration_ms: u64,
}

impl DurationFilter {
    pub fn new(max_duration_ms: u64) -> Self {
        Self { max_duration_ms }
    }
}

impl SpanFilter for DurationFilter {
    fn should_filter(&self, _span: &SpanRef<'_>) -> bool {
        // In a real implementation, you'd check the span duration
        // For now, this is a placeholder
        false
    }

    fn filter_reason(&self) -> &'static str {
        "duration_exceeded"
    }
}

/// Filter spans by error status
pub struct ErrorFilter;

impl ErrorFilter {
    pub fn new() -> Self {
        Self
    }
}

impl SpanFilter for ErrorFilter {
    fn should_filter(&self, span: &SpanRef<'_>) -> bool {
        // Check if span has error status
        span.status().map_or(false, |status| status.is_error())
    }

    fn filter_reason(&self) -> &'static str {
        "error_span"
    }
}

/// Aggregation span processor for collecting span statistics
#[derive(Debug)]
pub struct AggregationSpanProcessor {
    span_count: std::sync::atomic::AtomicU64,
    error_count: std::sync::atomic::AtomicU64,
    total_duration_ns: std::sync::atomic::AtomicU64,
}

impl AggregationSpanProcessor {
    /// Create a new aggregation span processor
    pub fn new() -> Self {
        Self {
            span_count: std::sync::atomic::AtomicU64::new(0),
            error_count: std::sync::atomic::AtomicU64::new(0),
            total_duration_ns: std::sync::atomic::AtomicU64::new(0),
        }
    }

    /// Get total span count
    pub fn span_count(&self) -> u64 {
        self.span_count.load(std::sync::atomic::Ordering::Relaxed)
    }

    /// Get error span count
    pub fn error_count(&self) -> u64 {
        self.error_count.load(std::sync::atomic::Ordering::Relaxed)
    }

    /// Get average span duration in nanoseconds
    pub fn average_duration_ns(&self) -> f64 {
        let count = self.span_count.load(std::sync::atomic::Ordering::Relaxed);
        let total = self.total_duration_ns.load(std::sync::atomic::Ordering::Relaxed);

        if count == 0 {
            0.0
        } else {
            total as f64 / count as f64
        }
    }
}

impl SpanProcessor for AggregationSpanProcessor {
    fn on_start(&self, _span: &mut opentelemetry_sdk::trace::Span, _cx: &opentelemetry::Context) {
        self.span_count.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
    }

    fn on_end(&self, span: opentelemetry_sdk::export::trace::SpanData) {
        // Check for errors
        if span.status().map_or(false, |status| status.is_error()) {
            self.error_count.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
        }

        // In a real implementation, you'd track span duration
        // For now, this is a placeholder
    }

    fn force_flush(&self) -> opentelemetry_sdk::export::trace::ExportResult {
        Ok(())
    }

    fn shutdown(&mut self) -> opentelemetry_sdk::export::trace::ExportResult {
        Ok(())
    }
}

/// Performance monitoring span processor
#[derive(Debug)]
pub struct PerformanceSpanProcessor {
    slow_span_threshold_ms: u64,
    slow_span_count: std::sync::atomic::AtomicU64,
}

impl PerformanceSpanProcessor {
    /// Create a new performance span processor
    pub fn new(slow_span_threshold_ms: u64) -> Self {
        Self {
            slow_span_threshold_ms,
            slow_span_count: std::sync::atomic::AtomicU64::new(0),
        }
    }

    /// Get count of slow spans
    pub fn slow_span_count(&self) -> u64 {
        self.slow_span_count.load(std::sync::atomic::Ordering::Relaxed)
    }
}

impl SpanProcessor for PerformanceSpanProcessor {
    fn on_start(&self, span: &mut opentelemetry_sdk::trace::Span, _cx: &opentelemetry::Context) {
        // Mark span start time
        span.set_attribute(KeyValue::new("performance.start_time", chrono::Utc::now().timestamp_nanos()));
    }

    fn on_end(&self, span: opentelemetry_sdk::export::trace::SpanData) {
        // Calculate span duration and check if it's slow
        if let Some(start_time) = span.attributes().iter().find(|attr| attr.key == "performance.start_time") {
            if let Some(start_ns) = start_time.value.as_i64() {
                let end_ns = chrono::Utc::now().timestamp_nanos();
                let duration_ns = end_ns - start_ns;
                let duration_ms = duration_ns / 1_000_000;

                if duration_ms > self.slow_span_threshold_ms as i64 {
                    self.slow_span_count.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
                    // Mark slow span
                    // Note: In a real implementation, you'd modify the span here
                    // but SpanRef doesn't allow modification in on_end
                }
            }
        }
    }

    fn force_flush(&self) -> opentelemetry_sdk::export::trace::ExportResult {
        Ok(())
    }

    fn shutdown(&mut self) -> opentelemetry_sdk::export::trace::ExportResult {
        Ok(())
    }
}

/// Factory for creating custom span processors
pub struct SpanProcessorFactory;

impl SpanProcessorFactory {
    /// Create enrichment processor
    pub fn create_enrichment_processor() -> EnrichmentSpanProcessor {
        EnrichmentSpanProcessor::new()
    }

    /// Create filtering processor with default filters
    pub fn create_filtering_processor() -> FilteringSpanProcessor {
        let mut processor = FilteringSpanProcessor::new();
        processor.add_filter(ErrorFilter::new());
        processor
    }

    /// Create aggregation processor
    pub fn create_aggregation_processor() -> AggregationSpanProcessor {
        AggregationSpanProcessor::new()
    }

    /// Create performance processor
    pub fn create_performance_processor(threshold_ms: u64) -> PerformanceSpanProcessor {
        PerformanceSpanProcessor::new(threshold_ms)
    }
}