//! # Structured Logging Module
//!
//! This module provides enhanced structured logging macros and utilities
//! that integrate with the log aggregation and correlation system.

use crate::observability::{log_aggregation::CorrelatedLogEntry, ObservabilityManager};
use std::collections::HashMap;
use tracing::{error, info, warn, Level};
use uuid::Uuid;

/// Enhanced structured logging utilities
pub struct StructuredLogger {
    observability_manager: std::sync::Arc<ObservabilityManager>,
}

impl StructuredLogger {
    /// Create a new structured logger
    pub fn new(observability_manager: std::sync::Arc<ObservabilityManager>) -> Self {
        Self {
            observability_manager,
        }
    }

    /// Log an entry with full correlation context
    pub async fn log_entry(&self, entry: CorrelatedLogEntry) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Log to tracing for immediate output
        self.log_to_tracing(&entry);

        // Send to log aggregation system
        if let Some(log_manager) = self.observability_manager.get_log_aggregation_manager() {
            log_manager.log_entry(entry).await?;
        }

        Ok(())
    }

    /// Log to tracing system for immediate output
    fn log_to_tracing(&self, entry: &CorrelatedLogEntry) {
        let level = match entry.level.as_str() {
            "ERROR" => Level::ERROR,
            "WARN" => Level::WARN,
            "INFO" => Level::INFO,
            "DEBUG" => Level::DEBUG,
            "TRACE" => Level::TRACE,
            _ => Level::INFO,
        };

        // Create structured log message
        let mut fields = Vec::new();

        if let Some(ref trace_id) = entry.trace_id {
            fields.push(format!("trace_id={}", trace_id));
        }
        if let Some(ref span_id) = entry.span_id {
            fields.push(format!("span_id={}", span_id));
        }
        if let Some(ref request_id) = entry.request_id {
            fields.push(format!("request_id={}", request_id));
        }
        if let Some(ref user_id) = entry.user_id {
            fields.push(format!("user_id={}", user_id));
        }
        if let Some(ref error_type) = entry.error_type {
            fields.push(format!("error_type={}", error_type));
        }

        let fields_str = if fields.is_empty() {
            String::new()
        } else {
            format!(" {}", fields.join(" "))
        };

        let message = format!("[{}] {}{}", entry.component, entry.message, fields_str);

        match level {
            Level::ERROR => error!("{}", message),
            Level::WARN => warn!("{}", message),
            Level::INFO => info!("{}", message),
            Level::DEBUG => tracing::debug!("{}", message),
            Level::TRACE => tracing::trace!("{}", message),
        }
    }

    /// Create a correlated log entry builder
    pub fn entry_builder(&self, level: &str, component: &str, message: &str) -> LogEntryBuilder {
        LogEntryBuilder::new(level, component, message)
    }
}

/// Builder for creating correlated log entries
pub struct LogEntryBuilder {
    entry: CorrelatedLogEntry,
}

impl LogEntryBuilder {
    /// Create a new log entry builder
    pub fn new(level: &str, component: &str, message: &str) -> Self {
        let mut entry = CorrelatedLogEntry {
            id: Uuid::new_v4().to_string(),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_millis() as u64,
            level: level.to_string(),
            message: message.to_string(),
            component: component.to_string(),
            trace_id: None,
            span_id: None,
            request_id: None,
            user_id: None,
            session_id: None,
            error_type: None,
            error_code: None,
            business_context: HashMap::new(),
            performance_context: None,
            security_context: None,
            metadata: HashMap::new(),
        };

        // Auto-populate trace and span IDs if available
        if let Ok(context) = opentelemetry::Context::current().try_with_current(|ctx| {
            if let Some(span) = ctx.span().span_context() {
                entry.trace_id = Some(span.trace_id().to_string());
                entry.span_id = Some(span.span_id().to_string());
            }
        }) {
            // Context extraction successful
        }

        Self { entry }
    }

    /// Set request ID
    pub fn request_id(mut self, request_id: &str) -> Self {
        self.entry.request_id = Some(request_id.to_string());
        self
    }

    /// Set user ID
    pub fn user_id(mut self, user_id: &str) -> Self {
        self.entry.user_id = Some(user_id.to_string());
        self
    }

    /// Set session ID
    pub fn session_id(mut self, session_id: &str) -> Self {
        self.entry.session_id = Some(session_id.to_string());
        self
    }

    /// Set error type
    pub fn error_type(mut self, error_type: &str) -> Self {
        self.entry.error_type = Some(error_type.to_string());
        self
    }

    /// Set error code
    pub fn error_code(mut self, error_code: &str) -> Self {
        self.entry.error_code = Some(error_code.to_string());
        self
    }

    /// Add business context
    pub fn business_context(mut self, key: &str, value: &str) -> Self {
        self.entry.business_context.insert(key.to_string(), value.to_string());
        self
    }

    /// Add metadata
    pub fn metadata(mut self, key: &str, value: opentelemetry::Value) -> Self {
        self.entry.metadata.insert(key.to_string(), value);
        self
    }

    /// Build the log entry
    pub fn build(self) -> CorrelatedLogEntry {
        self.entry
    }
}

/// Enhanced logging macros that integrate with structured logging

#[macro_export]
macro_rules! log_structured {
    ($logger:expr, $level:expr, $component:expr, $message:expr) => {
        let entry = $logger.entry_builder($level, $component, $message).build();
        tokio::spawn(async move {
            if let Err(e) = $logger.log_entry(entry).await {
                eprintln!("Failed to log structured entry: {}", e);
            }
        });
    };
    ($logger:expr, $level:expr, $component:expr, $message:expr, $($key:expr => $value:expr),*) => {
        let mut builder = $logger.entry_builder($level, $component, $message);
        $(
            builder = builder.business_context($key, &$value.to_string());
        )*
        let entry = builder.build();
        tokio::spawn(async move {
            if let Err(e) = $logger.log_entry(entry).await {
                eprintln!("Failed to log structured entry: {}", e);
            }
        });
    };
}

#[macro_export]
macro_rules! log_error_structured {
    ($logger:expr, $component:expr, $error:expr) => {
        let entry = $logger
            .entry_builder("ERROR", $component, &$error.to_string())
            .error_type("generic_error")
            .build();
        tokio::spawn(async move {
            if let Err(e) = $logger.log_entry(entry).await {
                eprintln!("Failed to log structured error: {}", e);
            }
        });
    };
    ($logger:expr, $component:expr, $error:expr, $error_type:expr) => {
        let entry = $logger
            .entry_builder("ERROR", $component, &$error.to_string())
            .error_type($error_type)
            .build();
        tokio::spawn(async move {
            if let Err(e) = $logger.log_entry(entry).await {
                eprintln!("Failed to log structured error: {}", e);
            }
        });
    };
}

#[macro_export]
macro_rules! log_performance {
    ($logger:expr, $component:expr, $operation:expr, $duration_ms:expr) => {
        use crate::observability::log_aggregation::{PerformanceContext, CorrelatedLogEntry};
        use std::collections::HashMap;

        let mut entry = $logger.entry_builder("INFO", $component, &format!("{} completed", $operation)).build();
        entry.performance_context = Some(PerformanceContext {
            duration_ms: Some($duration_ms),
            memory_usage_bytes: None,
            cpu_usage_percent: None,
            queue_depth: None,
            db_connections: None,
        });

        tokio::spawn(async move {
            if let Err(e) = $logger.log_entry(entry).await {
                eprintln!("Failed to log performance: {}", e);
            }
        });
    };
}

#[macro_export]
macro_rules! log_security_event {
    ($logger:expr, $component:expr, $event_type:expr, $message:expr) => {
        use crate::observability::log_aggregation::{SecurityContext, CorrelatedLogEntry};

        let mut entry = $logger.entry_builder("WARN", $component, $message).build();
        entry.security_context = Some(SecurityContext {
            event_type: $event_type.to_string(),
            user_role: None,
            ip_address: None,
            user_agent: None,
            security_level: "medium".to_string(),
        });

        tokio::spawn(async move {
            if let Err(e) = $logger.log_entry(entry).await {
                eprintln!("Failed to log security event: {}", e);
            }
        });
    };
    ($logger:expr, $component:expr, $event_type:expr, $message:expr, $user_role:expr, $ip:expr) => {
        use crate::observability::log_aggregation::{SecurityContext, CorrelatedLogEntry};

        let mut entry = $logger.entry_builder("WARN", $component, $message).build();
        entry.security_context = Some(SecurityContext {
            event_type: $event_type.to_string(),
            user_role: Some($user_role.to_string()),
            ip_address: Some($ip.to_string()),
            user_agent: None,
            security_level: "medium".to_string(),
        });

        tokio::spawn(async move {
            if let Err(e) = $logger.log_entry(entry).await {
                eprintln!("Failed to log security event: {}", e);
            }
        });
    };
}

#[macro_export]
macro_rules! log_business_event {
    ($logger:expr, $component:expr, $event:expr, $entity_type:expr, $entity_id:expr) => {
        let entry = $logger
            .entry_builder("INFO", $component, &format!("Business event: {}", $event))
            .business_context("entity_type", $entity_type)
            .business_context("entity_id", $entity_id)
            .build();

        tokio::spawn(async move {
            if let Err(e) = $logger.log_entry(entry).await {
                eprintln!("Failed to log business event: {}", e);
            }
        });
    };
}

/// Performance monitoring timer with automatic structured logging
pub struct PerformanceTimer {
    start: std::time::Instant,
    component: String,
    operation: String,
    logger: std::sync::Arc<StructuredLogger>,
}

impl PerformanceTimer {
    pub fn new(component: &str, operation: &str, logger: std::sync::Arc<StructuredLogger>) -> Self {
        Self {
            start: std::time::Instant::now(),
            component: component.to_string(),
            operation: operation.to_string(),
            logger,
        }
    }

    pub fn elapsed_ms(&self) -> f64 {
        self.start.elapsed().as_secs_f64() * 1000.0
    }
}

impl Drop for PerformanceTimer {
    fn drop(&mut self) {
        let duration_ms = self.elapsed_ms();
        log_performance!(self.logger, &self.component, &self.operation, duration_ms);
    }
}