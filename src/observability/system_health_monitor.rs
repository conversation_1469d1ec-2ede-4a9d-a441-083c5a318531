//! # System Health Monitoring Framework
//!
//! This module provides comprehensive self-monitoring capabilities for the observability system,
//! including health checks, performance monitoring, and failure detection.

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// System health monitoring configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemHealthMonitorConfig {
    /// Enable system health monitoring
    pub enabled: bool,
    /// Health check interval in seconds
    pub health_check_interval_seconds: u64,
    /// Component health check timeout in seconds
    pub health_check_timeout_seconds: u64,
    /// Failure threshold for marking unhealthy
    pub failure_threshold: u32,
    /// Recovery threshold for marking healthy
    pub recovery_threshold: u32,
    /// Alert configuration
    pub alerting: HealthAlertConfig,
    /// Performance monitoring configuration
    pub performance: PerformanceMonitorConfig,
}

/// Health alert configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct HealthAlertConfig {
    /// Enable health alerts
    pub enabled: bool,
    /// Alert on component failure
    pub alert_on_component_failure: bool,
    /// Alert on performance degradation
    pub alert_on_performance_degradation: bool,
    /// Alert on system overload
    pub alert_on_system_overload: bool,
    /// Alert cooldown period in seconds
    pub alert_cooldown_seconds: u64,
}

/// Performance monitoring configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMonitorConfig {
    /// Enable performance monitoring
    pub enabled: bool,
    /// Performance metrics collection interval
    pub metrics_interval_seconds: u64,
    /// Performance degradation threshold
    pub degradation_threshold_percent: f64,
    /// Memory usage warning threshold
    pub memory_warning_threshold_percent: f64,
    /// CPU usage warning threshold
    pub cpu_warning_threshold_percent: f64,
}

/// Component health status
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ComponentHealthStatus {
    Healthy,
    Degraded,
    Unhealthy,
    Unknown,
}

/// Component type
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ComponentType {
    Tracing,
    Metrics,
    Logging,
    Alerting,
    Dashboard,
    Security,
    CircuitBreaker,
    DataRedundancy,
    ServiceRedundancy,
    ErrorRecovery,
    ResourceManagement,
}

/// Component health information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentHealth {
    pub component_type: ComponentType,
    pub component_name: String,
    pub status: ComponentHealthStatus,
    pub last_check: DateTime<Utc>,
    pub consecutive_failures: u32,
    pub response_time_ms: Option<u64>,
    pub error_message: Option<String>,
    pub metadata: HashMap<String, String>,
}

/// System health status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemHealthStatus {
    pub overall_status: ComponentHealthStatus,
    pub component_health: HashMap<ComponentType, ComponentHealth>,
    pub system_uptime_seconds: u64,
    pub last_full_check: DateTime<Utc>,
    pub total_components: usize,
    pub healthy_components: usize,
    pub degraded_components: usize,
    pub unhealthy_components: usize,
}

/// Health check result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckResult {
    pub component_type: ComponentType,
    pub component_name: String,
    pub success: bool,
    pub response_time_ms: u64,
    pub error_message: Option<String>,
    pub timestamp: DateTime<Utc>,
    pub metadata: HashMap<String, String>,
}

/// Performance metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub cpu_usage_percent: f64,
    pub memory_usage_percent: f64,
    pub memory_usage_bytes: u64,
    pub thread_count: usize,
    pub active_connections: usize,
    pub request_queue_depth: usize,
    pub average_response_time_ms: f64,
    pub requests_per_second: f64,
    pub timestamp: DateTime<Utc>,
}

/// Health alert
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthAlert {
    pub id: String,
    pub alert_type: HealthAlertType,
    pub severity: AlertSeverity,
    pub component_type: ComponentType,
    pub component_name: String,
    pub message: String,
    pub details: HashMap<String, String>,
    pub timestamp: DateTime<Utc>,
    pub resolved: bool,
    pub resolved_at: Option<DateTime<Utc>>,
}

/// Health alert types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum HealthAlertType {
    ComponentFailure,
    PerformanceDegradation,
    SystemOverload,
    ResourceExhaustion,
    ConfigurationError,
}

/// Alert severity levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AlertSeverity {
    Info,
    Warning,
    Error,
    Critical,
}

/// System health monitor
#[derive(Debug)]
pub struct SystemHealthMonitor {
    config: SystemHealthMonitorConfig,
    component_health: Arc<RwLock<HashMap<ComponentType, ComponentHealth>>>,
    health_check_results: Arc<RwLock<Vec<HealthCheckResult>>>,
    alerts: Arc<RwLock<Vec<HealthAlert>>>,
    performance_history: Arc<RwLock<Vec<PerformanceMetrics>>>,
    system_start_time: Instant,
    last_alert_times: Arc<RwLock<HashMap<String, Instant>>>,
}

impl SystemHealthMonitor {
    /// Create a new system health monitor
    pub fn new(config: SystemHealthMonitorConfig) -> Self {
        Self {
            config,
            component_health: Arc::new(RwLock::new(HashMap::new())),
            health_check_results: Arc::new(RwLock::new(Vec::new())),
            alerts: Arc::new(RwLock::new(Vec::new())),
            performance_history: Arc::new(RwLock::new(Vec::new())),
            system_start_time: Instant::now(),
            last_alert_times: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Register a component for health monitoring
    pub async fn register_component(&self, component_type: ComponentType, component_name: &str) {
        let component_health = ComponentHealth {
            component_type,
            component_name: component_name.to_string(),
            status: ComponentHealthStatus::Unknown,
            last_check: Utc::now(),
            consecutive_failures: 0,
            response_time_ms: None,
            error_message: None,
            metadata: HashMap::new(),
        };

        let mut health = self.component_health.write().await;
        health.insert(component_type, component_health);
    }

    /// Perform health check on a component
    pub async fn perform_health_check(
        &self,
        component_type: ComponentType,
        check_fn: Box<dyn Fn() -> Result<HashMap<String, String>, String> + Send + Sync>,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let start_time = Instant::now();

        let result = tokio::time::timeout(
            Duration::from_secs(self.config.health_check_timeout_seconds),
            tokio::spawn(async move { check_fn() })
        ).await;

        let response_time = start_time.elapsed().as_millis() as u64;

        let (success, error_message, metadata) = match result {
            Ok(Ok(Ok(meta))) => (true, None, meta),
            Ok(Ok(Err(err))) => (false, Some(err), HashMap::new()),
            Ok(Err(_)) => (false, Some("Health check task panicked".to_string()), HashMap::new()),
            Err(_) => (false, Some("Health check timeout".to_string()), HashMap::new()),
        };

        let component_name = {
            let health = self.component_health.read().await;
            health.get(&component_type)
                .map(|c| c.component_name.clone())
                .unwrap_or_else(|| format!("{:?}", component_type))
        };

        // Record health check result
        let health_result = HealthCheckResult {
            component_type,
            component_name: component_name.clone(),
            success,
            response_time_ms: response_time,
            error_message: error_message.clone(),
            timestamp: Utc::now(),
            metadata,
        };

        {
            let mut results = self.health_check_results.write().await;
            results.push(health_result);

            // Keep only last 1000 results
            if results.len() > 1000 {
                results.remove(0);
            }
        }

        // Update component health
        {
            let mut health = self.component_health.write().await;
            if let Some(component) = health.get_mut(&component_type) {
                component.last_check = Utc::now();
                component.response_time_ms = Some(response_time);

                if success {
                    component.consecutive_failures = 0;
                    component.error_message = None;
                    if component.consecutive_failures == 0 && component.status != ComponentHealthStatus::Healthy {
                        component.status = ComponentHealthStatus::Healthy;
                    }
                } else {
                    component.consecutive_failures += 1;
                    component.error_message = error_message;

                    if component.consecutive_failures >= self.config.failure_threshold {
                        component.status = ComponentHealthStatus::Unhealthy;
                    } else if component.consecutive_failures > 0 {
                        component.status = ComponentHealthStatus::Degraded;
                    }
                }
            }
        }

        // Check for alerts
        if !success && self.config.alerting.enabled && self.config.alerting.alert_on_component_failure {
            self.generate_component_failure_alert(component_type, &component_name, error_message).await;
        }

        Ok(())
    }

    /// Record performance metrics
    pub async fn record_performance_metrics(&self, metrics: PerformanceMetrics) {
        let mut history = self.performance_history.write().await;
        history.push(metrics.clone());

        // Keep only last 1000 metrics
        if history.len() > 1000 {
            history.remove(0);
        }

        // Check for performance alerts
        if self.config.alerting.enabled && self.config.alerting.alert_on_performance_degradation {
            self.check_performance_alerts(&metrics).await;
        }
    }

    /// Get system health status
    pub async fn get_system_health_status(&self) -> SystemHealthStatus {
        let health = self.component_health.read().await;
        let total_components = health.len();

        let mut healthy_components = 0;
        let mut degraded_components = 0;
        let mut unhealthy_components = 0;

        for component in health.values() {
            match component.status {
                ComponentHealthStatus::Healthy => healthy_components += 1,
                ComponentHealthStatus::Degraded => degraded_components += 1,
                ComponentHealthStatus::Unhealthy => unhealthy_components += 1,
                ComponentHealthStatus::Unknown => {}
            }
        }

        let overall_status = if unhealthy_components > 0 {
            ComponentHealthStatus::Unhealthy
        } else if degraded_components > 0 {
            ComponentHealthStatus::Degraded
        } else if healthy_components == total_components && total_components > 0 {
            ComponentHealthStatus::Healthy
        } else {
            ComponentHealthStatus::Unknown
        };

        SystemHealthStatus {
            overall_status,
            component_health: health.clone(),
            system_uptime_seconds: self.system_start_time.elapsed().as_secs(),
            last_full_check: Utc::now(),
            total_components,
            healthy_components,
            degraded_components,
            unhealthy_components,
        }
    }

    /// Get component health
    pub async fn get_component_health(&self, component_type: ComponentType) -> Option<ComponentHealth> {
        let health = self.component_health.read().await;
        health.get(&component_type).cloned()
    }

    /// Get recent health check results
    pub async fn get_recent_health_checks(&self, limit: usize) -> Vec<HealthCheckResult> {
        let results = self.health_check_results.read().await;
        results.iter().rev().take(limit).cloned().collect()
    }

    /// Get active alerts
    pub async fn get_active_alerts(&self) -> Vec<HealthAlert> {
        let alerts = self.alerts.read().await;
        alerts.iter()
            .filter(|alert| !alert.resolved)
            .cloned()
            .collect()
    }

    /// Get performance history
    pub async fn get_performance_history(&self, limit: usize) -> Vec<PerformanceMetrics> {
        let history = self.performance_history.read().await;
        history.iter().rev().take(limit).cloned().collect()
    }

    /// Resolve an alert
    pub async fn resolve_alert(&self, alert_id: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut alerts = self.alerts.write().await;
        if let Some(alert) = alerts.iter_mut().find(|a| a.id == alert_id) {
            alert.resolved = true;
            alert.resolved_at = Some(Utc::now());
        }
        Ok(())
    }

    /// Generate component failure alert
    async fn generate_component_failure_alert(
        &self,
        component_type: ComponentType,
        component_name: &str,
        error_message: Option<String>,
    ) {
        let alert_key = format!("component_failure_{:?}_{}", component_type, component_name);

        // Check cooldown
        {
            let last_alerts = self.last_alert_times.read().await;
            if let Some(last_time) = last_alerts.get(&alert_key) {
                if last_time.elapsed() < Duration::from_secs(self.config.alerting.alert_cooldown_seconds) {
                    return;
                }
            }
        }

        let alert = HealthAlert {
            id: format!("alert_{}", Utc::now().timestamp()),
            alert_type: HealthAlertType::ComponentFailure,
            severity: AlertSeverity::Error,
            component_type,
            component_name: component_name.to_string(),
            message: format!("Component {} failed health check", component_name),
            details: {
                let mut details = HashMap::new();
                if let Some(error) = error_message {
                    details.insert("error_message".to_string(), error);
                }
                details
            },
            timestamp: Utc::now(),
            resolved: false,
            resolved_at: None,
        };

        {
            let mut alerts = self.alerts.write().await;
            alerts.push(alert);
        }

        {
            let mut last_alerts = self.last_alert_times.write().await;
            last_alerts.insert(alert_key, Instant::now());
        }
    }

    /// Check for performance alerts
    async fn check_performance_alerts(&self, metrics: &PerformanceMetrics) {
        let mut alerts = Vec::new();

        // Memory usage alert
        if metrics.memory_usage_percent >= self.config.performance.memory_warning_threshold_percent {
            alerts.push(self.create_performance_alert(
                HealthAlertType::SystemOverload,
                AlertSeverity::Warning,
                "High memory usage detected",
                format!("Memory usage: {:.1}%", metrics.memory_usage_percent),
            ));
        }

        // CPU usage alert
        if metrics.cpu_usage_percent >= self.config.performance.cpu_warning_threshold_percent {
            alerts.push(self.create_performance_alert(
                HealthAlertType::PerformanceDegradation,
                AlertSeverity::Warning,
                "High CPU usage detected",
                format!("CPU usage: {:.1}%", metrics.cpu_usage_percent),
            ));
        }

        // Request queue depth alert
        if metrics.request_queue_depth > 100 {
            alerts.push(self.create_performance_alert(
                HealthAlertType::SystemOverload,
                AlertSeverity::Warning,
                "High request queue depth",
                format!("Queue depth: {}", metrics.request_queue_depth),
            ));
        }

        for alert in alerts {
            let alert_key = format!("perf_{:?}_{}", alert.alert_type, alert.severity as u8);

            // Check cooldown
            {
                let last_alerts = self.last_alert_times.read().await;
                if let Some(last_time) = last_alerts.get(&alert_key) {
                    if last_time.elapsed() < Duration::from_secs(self.config.alerting.alert_cooldown_seconds) {
                        continue;
                    }
                }
            }

            {
                let mut alerts_store = self.alerts.write().await;
                alerts_store.push(alert);
            }

            {
                let mut last_alerts = self.last_alert_times.write().await;
                last_alerts.insert(alert_key, Instant::now());
            }
        }
    }

    /// Create performance alert
    fn create_performance_alert(
        &self,
        alert_type: HealthAlertType,
        severity: AlertSeverity,
        message: &str,
        details: String,
    ) -> HealthAlert {
        HealthAlert {
            id: format!("perf_alert_{}", Utc::now().timestamp()),
            alert_type,
            severity,
            component_type: ComponentType::ResourceManagement,
            component_name: "System".to_string(),
            message: message.to_string(),
            details: {
                let mut map = HashMap::new();
                map.insert("details".to_string(), details);
                map
            },
            timestamp: Utc::now(),
            resolved: false,
            resolved_at: None,
        }
    }

    /// Get health monitoring statistics
    pub async fn get_monitoring_stats(&self) -> SystemHealthMonitoringStats {
        let health = self.component_health.read().await;
        let results = self.health_check_results.read().await;
        let alerts = self.alerts.read().await;
        let performance = self.performance_history.read().await;

        let total_checks = results.len();
        let successful_checks = results.iter().filter(|r| r.success).count();
        let failed_checks = total_checks - successful_checks;

        let active_alerts = alerts.iter().filter(|a| !a.resolved).count();
        let total_alerts = alerts.len();

        let avg_response_time = if !results.is_empty() {
            results.iter().map(|r| r.response_time_ms).sum::<u64>() / results.len() as u64
        } else {
            0
        };

        SystemHealthMonitoringStats {
            total_components: health.len(),
            total_health_checks: total_checks,
            successful_health_checks: successful_checks,
            failed_health_checks: failed_checks,
            active_alerts,
            total_alerts,
            average_response_time_ms: avg_response_time,
            performance_metrics_count: performance.len(),
        }
    }
}

/// System health monitoring statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemHealthMonitoringStats {
    pub total_components: usize,
    pub total_health_checks: usize,
    pub successful_health_checks: usize,
    pub failed_health_checks: usize,
    pub active_alerts: usize,
    pub total_alerts: usize,
    pub average_response_time_ms: u64,
    pub performance_metrics_count: usize,
}

impl Default for SystemHealthMonitorConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            health_check_interval_seconds: 30,
            health_check_timeout_seconds: 10,
            failure_threshold: 3,
            recovery_threshold: 2,
            alerting: HealthAlertConfig {
                enabled: true,
                alert_on_component_failure: true,
                alert_on_performance_degradation: true,
                alert_on_system_overload: true,
                alert_cooldown_seconds: 300,
            },
            performance: PerformanceMonitorConfig {
                enabled: true,
                metrics_interval_seconds: 60,
                degradation_threshold_percent: 80.0,
                memory_warning_threshold_percent: 75.0,
                cpu_warning_threshold_percent: 70.0,
            },
        }
    }
}