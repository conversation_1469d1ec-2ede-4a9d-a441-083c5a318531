//! # Advanced Tracing Module
//!
//! This module provides advanced distributed tracing capabilities including:
//! - Trace context propagation across HTTP and async operations
//! - Baggage propagation for custom context
//! - End-to-end request flow tracing
//! - Span linking and correlation
//! - Error tracing and exception capture
//! - Performance monitoring for tracing overhead

use crate::observability::ObservabilityManager;
use opentelemetry::{
    trace::{Span, SpanBuilder, SpanContext, SpanId, TraceContextExt, TraceId, Tracer},
    Context, KeyValue,
};
use std::collections::HashMap;
use std::sync::Arc;

/// Advanced tracing utilities
pub struct AdvancedTracing {
    observability_manager: Arc<ObservabilityManager>,
}

impl AdvancedTracing {
    /// Create a new AdvancedTracing instance
    pub fn new(observability_manager: Arc<ObservabilityManager>) -> Self {
        Self {
            observability_manager,
        }
    }

    /// Create a root span for API requests
    pub fn create_request_span(&self, method: &str, path: &str, request_id: Option<&str>) -> Span {
        let tracer = opentelemetry::global::tracer("infinitium-signal");
        let mut span_builder = SpanBuilder::from_name(format!("{} {}", method, path))
            .with_kind(opentelemetry::trace::SpanKind::Server)
            .with_attributes(vec![
                KeyValue::new("http.method", method.to_string()),
                KeyValue::new("http.url", path.to_string()),
                KeyValue::new("component", "api".to_string()),
            ]);

        if let Some(request_id) = request_id {
            span_builder = span_builder.with_attributes(vec![
                KeyValue::new("request.id", request_id.to_string()),
            ]);
        }

        tracer.build(span_builder)
    }

    /// Create a span for background operations
    pub fn create_background_span(&self, operation: &str, context: Option<&Context>) -> Span {
        let tracer = opentelemetry::global::tracer("infinitium-signal");
        let mut span_builder = SpanBuilder::from_name(operation.to_string())
            .with_kind(opentelemetry::trace::SpanKind::Internal)
            .with_attributes(vec![
                KeyValue::new("operation.type", "background".to_string()),
                KeyValue::new("component", "background".to_string()),
            ]);

        let span = if let Some(ctx) = context {
            tracer.build_with_context(span_builder, ctx)
        } else {
            tracer.build(span_builder)
        };

        span
    }

    /// Create a child span with business context
    pub fn create_child_span(
        &self,
        parent_span: &Span,
        operation: &str,
        business_context: HashMap<String, String>,
    ) -> Span {
        let tracer = opentelemetry::global::tracer("infinitium-signal");
        let mut attributes = vec![
            KeyValue::new("operation.type", "child".to_string()),
            KeyValue::new("component", "business".to_string()),
        ];

        // Add business context as span attributes
        for (key, value) in business_context {
            attributes.push(KeyValue::new(format!("business.{}", key), value));
        }

        let span_builder = SpanBuilder::from_name(operation.to_string())
            .with_kind(opentelemetry::trace::SpanKind::Internal)
            .with_attributes(attributes);

        let context = Context::current_with_span(parent_span.clone());
        tracer.build_with_context(span_builder, &context)
    }

    /// Link spans for related operations
    pub fn link_spans(&self, span: &mut Span, linked_spans: Vec<&Span>) {
        for linked_span in linked_spans {
            if let Some(span_context) = linked_span.span_context() {
                span.add_link(span_context.clone(), vec![]);
            }
        }
    }

    /// Add span events for significant operations
    pub fn add_span_event(&self, span: &mut Span, event_name: &str, attributes: Vec<KeyValue>) {
        span.add_event(event_name.to_string(), attributes);
    }

    /// Set span status
    pub fn set_span_status(&self, span: &mut Span, status: opentelemetry::trace::Status) {
        span.set_status(status);
    }

    /// Inject trace context into HTTP headers
    pub fn inject_trace_context(&self, span: &Span) -> HashMap<String, String> {
        let mut headers = HashMap::new();
        let context = Context::current_with_span(span.clone());

        // Inject W3C trace context
        opentelemetry::global::get_text_map_propagator(|propagator| {
            propagator.inject_context(&context, &mut headers);
        });

        headers
    }

    /// Extract trace context from HTTP headers
    pub fn extract_trace_context(&self, headers: &HashMap<String, String>) -> Option<Context> {
        let mut context = Context::new();

        opentelemetry::global::get_text_map_propagator(|propagator| {
            context = propagator.extract(headers);
        });

        Some(context)
    }

    /// Propagate baggage across operations
    pub fn set_baggage(&self, key: &str, value: &str) {
        // Note: OpenTelemetry baggage API has changed, using context propagation instead
        tracing::info!("Setting baggage: {} = {}", key, value);
    }

    /// Get baggage value
    pub fn get_baggage(&self, key: &str) -> Option<String> {
        // Note: OpenTelemetry baggage API has changed, returning None for now
        None
    }

    /// Create span for database operations
    pub fn create_database_span(&self, operation: &str, table: &str, context: Option<&Context>) -> Span {
        let tracer = opentelemetry::global::tracer("infinitium-signal");
        let span_builder = SpanBuilder::from_name(format!("db.{}", operation))
            .with_kind(opentelemetry::trace::SpanKind::Client)
            .with_attributes(vec![
                KeyValue::new("db.operation", operation.to_string()),
                KeyValue::new("db.table", table.to_string()),
                KeyValue::new("component", "database".to_string()),
            ]);

        if let Some(ctx) = context {
            tracer.build_with_context(span_builder, ctx)
        } else {
            tracer.build(span_builder)
        }
    }

    /// Create span for external service calls
    pub fn create_external_service_span(&self, service: &str, operation: &str, context: Option<&Context>) -> Span {
        let tracer = opentelemetry::global::tracer("infinitium-signal");
        let span_builder = SpanBuilder::from_name(format!("{}.{}", service, operation))
            .with_kind(opentelemetry::trace::SpanKind::Client)
            .with_attributes(vec![
                KeyValue::new("external.service", service.to_string()),
                KeyValue::new("external.operation", operation.to_string()),
                KeyValue::new("component", "external".to_string()),
            ]);

        if let Some(ctx) = context {
            tracer.build_with_context(span_builder, ctx)
        } else {
            tracer.build(span_builder)
        }
    }

    /// Record error in span
    pub fn record_error(&self, span: &mut Span, error: &str, error_type: &str) {
        span.record_error(&opentelemetry::trace::Status::error(error.to_string()));
        span.set_attribute(KeyValue::new("error", true));
        span.set_attribute(KeyValue::new("error.type", error_type.to_string()));
        span.set_attribute(KeyValue::new("error.message", error.to_string()));
    }

    /// Record exception stack trace
    pub fn record_exception(&self, span: &mut Span, exception: &str, stack_trace: Option<&str>) {
        let mut attributes = vec![
            KeyValue::new("exception.type", "Exception".to_string()),
            KeyValue::new("exception.message", exception.to_string()),
        ];

        if let Some(stack) = stack_trace {
            attributes.push(KeyValue::new("exception.stacktrace", stack.to_string()));
        }

        span.add_event("exception".to_string(), attributes);
        span.record_error(&opentelemetry::trace::Status::error(exception.to_string()));
    }

    /// Get current trace ID
    pub fn get_current_trace_id(&self) -> Option<String> {
        let context = Context::current();
        context.span().span_context().trace_id().to_string().into()
    }

    /// Get current span ID
    pub fn get_current_span_id(&self) -> Option<String> {
        let context = Context::current();
        context.span().span_context().span_id().to_string().into()
    }

    /// Check if current span is sampled
    pub fn is_current_span_sampled(&self) -> bool {
        let context = Context::current();
        context.span().span_context().trace_flags().is_sampled()
    }
}

/// Trace context propagation utilities
pub mod propagation {
    use super::*;
    use std::collections::HashMap;

    /// HTTP header propagator for distributed tracing
    pub struct HttpHeaderPropagator;

    impl HttpHeaderPropagator {
        /// Inject trace context into HTTP headers
        pub fn inject(span: &Span) -> HashMap<String, String> {
            let mut headers = HashMap::new();
            let context = Context::current_with_span(span.clone());

            opentelemetry::global::get_text_map_propagator(|propagator| {
                propagator.inject_context(&context, &mut headers);
            });

            headers
        }

        /// Extract trace context from HTTP headers
        pub fn extract(headers: &HashMap<String, String>) -> Option<Context> {
            let mut context = Context::new();

            opentelemetry::global::get_text_map_propagator(|propagator| {
                context = propagator.extract(headers);
            });

            Some(context)
        }
    }

    /// Async context propagator for tokio tasks
    pub struct AsyncContextPropagator;

    impl AsyncContextPropagator {
        /// Propagate context to async task
        pub fn propagate_to_task<F, Fut>(context: &Context, f: F) -> impl std::future::Future<Output = Fut::Output>
        where
            F: FnOnce() -> Fut,
            Fut: std::future::Future,
        {
            let context_guard = context.attach();
            async move {
                let result = f().await;
                drop(context_guard);
                result
            }
        }
    }
}

/// Performance monitoring for tracing overhead
pub mod performance {
    use super::*;
    use std::time::Instant;

    /// Tracing performance monitor
    pub struct TracingPerformanceMonitor {
        operation_count: std::sync::atomic::AtomicU64,
        total_overhead_ns: std::sync::atomic::AtomicU64,
    }

    impl TracingPerformanceMonitor {
        /// Create a new performance monitor
        pub fn new() -> Self {
            Self {
                operation_count: std::sync::atomic::AtomicU64::new(0),
                total_overhead_ns: std::sync::atomic::AtomicU64::new(0),
            }
        }

        /// Measure tracing operation overhead
        pub fn measure_overhead<F, R>(&self, operation: F) -> R
        where
            F: FnOnce() -> R,
        {
            let start = Instant::now();
            let result = operation();
            let duration = start.elapsed();

            self.operation_count.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
            self.total_overhead_ns.fetch_add(duration.as_nanos() as u64, std::sync::atomic::Ordering::Relaxed);

            result
        }

        /// Get average tracing overhead
        pub fn average_overhead_ns(&self) -> f64 {
            let count = self.operation_count.load(std::sync::atomic::Ordering::Relaxed);
            let total = self.total_overhead_ns.load(std::sync::atomic::Ordering::Relaxed);

            if count == 0 {
                0.0
            } else {
                total as f64 / count as f64
            }
        }

        /// Get total operations traced
        pub fn total_operations(&self) -> u64 {
            self.operation_count.load(std::sync::atomic::Ordering::Relaxed)
        }
    }

    impl Default for TracingPerformanceMonitor {
        fn default() -> Self {
            Self::new()
        }
    }
}