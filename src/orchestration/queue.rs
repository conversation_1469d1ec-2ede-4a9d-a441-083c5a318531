//! Message queue implementation for job processing and inter-service communication

use crate::error::{InfinitumError, Result};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use tokio::sync::{Notify, RwLock};
use tokio::time::{timeout, Duration};
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// Message priority levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum MessagePriority {
    Low = 1,
    Normal = 2,
    High = 3,
    Critical = 4,
}

/// Queue message
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueueMessage {
    pub id: Uuid,
    pub queue_name: String,
    pub priority: MessagePriority,
    pub payload: serde_json::Value,
    pub retry_count: u32,
    pub max_retries: u32,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub scheduled_at: chrono::DateTime<chrono::Utc>,
    pub expires_at: Option<chrono::DateTime<chrono::Utc>>,
}

impl QueueMessage {
    pub fn new(queue_name: String, payload: serde_json::Value) -> Self {
        let now = chrono::Utc::now();
        Self {
            id: Uuid::new_v4(),
            queue_name,
            priority: MessagePriority::Normal,
            payload,
            retry_count: 0,
            max_retries: 3,
            created_at: now,
            scheduled_at: now,
            expires_at: None,
        }
    }

    pub fn with_priority(mut self, priority: MessagePriority) -> Self {
        self.priority = priority;
        self
    }

    pub fn with_delay(mut self, delay: chrono::Duration) -> Self {
        self.scheduled_at = chrono::Utc::now() + delay;
        self
    }

    pub fn with_expiration(mut self, expires_at: chrono::DateTime<chrono::Utc>) -> Self {
        self.expires_at = Some(expires_at);
        self
    }

    pub fn with_max_retries(mut self, max_retries: u32) -> Self {
        self.max_retries = max_retries;
        self
    }

    pub fn is_expired(&self) -> bool {
        if let Some(expires_at) = self.expires_at {
            chrono::Utc::now() > expires_at
        } else {
            false
        }
    }

    pub fn is_ready(&self) -> bool {
        chrono::Utc::now() >= self.scheduled_at && !self.is_expired()
    }

    pub fn can_retry(&self) -> bool {
        self.retry_count < self.max_retries
    }

    pub fn increment_retry(&mut self) {
        self.retry_count += 1;
        // Exponential backoff for retries
        let delay_seconds = 2_u64.pow(self.retry_count.min(10)) * 5; // Max 5120 seconds
        self.scheduled_at = chrono::Utc::now() + chrono::Duration::seconds(delay_seconds as i64);
    }
}

/// Message processing result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ProcessingResult {
    Success,
    Retry(String),
    Discard(String),
}

/// Message handler trait
#[async_trait::async_trait]
pub trait MessageHandler: Send + Sync {
    async fn handle(&self, message: &QueueMessage) -> Result<ProcessingResult>;
}

/// Queue configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueueConfig {
    pub max_size: usize,
    pub consumer_timeout_seconds: u64,
    pub cleanup_interval_seconds: u64,
    pub dead_letter_queue: bool,
}

impl Default for QueueConfig {
    fn default() -> Self {
        Self {
            max_size: 10000,
            consumer_timeout_seconds: 30,
            cleanup_interval_seconds: 300,
            dead_letter_queue: true,
        }
    }
}

/// In-memory message queue
pub struct MessageQueue {
    config: QueueConfig,
    queues: RwLock<HashMap<String, VecDeque<QueueMessage>>>,
    dead_letter_queue: RwLock<VecDeque<QueueMessage>>,
    handlers: RwLock<HashMap<String, Arc<dyn MessageHandler>>>,
    notify: Arc<Notify>,
}

impl MessageQueue {
    /// Create a new message queue
    pub fn new(config: QueueConfig) -> Self {
        Self {
            config,
            queues: RwLock::new(HashMap::new()),
            dead_letter_queue: RwLock::new(VecDeque::new()),
            handlers: RwLock::new(HashMap::new()),
            notify: Arc::new(Notify::new()),
        }
    }

    /// Register a message handler for a queue
    pub async fn register_handler(&self, queue_name: String, handler: Arc<dyn MessageHandler>) {
        let mut handlers = self.handlers.write().await;
        info!("Registered handler for queue: {}", queue_name);
        handlers.insert(queue_name, handler);
    }

    /// Send a message to a queue
    pub async fn send(&self, message: QueueMessage) -> Result<()> {
        if message.is_expired() {
            return Err(InfinitumError::QueueError {
                message: "Message is expired".to_string(),
            });
        }

        let queue_name = message.queue_name.clone();

        {
            let mut queues = self.queues.write().await;
            let queue = queues
                .entry(queue_name.clone())
                .or_insert_with(VecDeque::new);

            if queue.len() >= self.config.max_size {
                return Err(InfinitumError::QueueError {
                    message: "Queue is full".to_string(),
                });
            }

            // Insert message in priority order
            let insert_pos = queue
                .iter()
                .position(|m| m.priority < message.priority)
                .unwrap_or(queue.len());

            queue.insert(insert_pos, message.clone());
        }

        debug!("Message sent to queue {}: {}", queue_name, message.id);
        self.notify.notify_waiters();

        Ok(())
    }

    /// Send a simple message
    pub async fn send_simple(
        &self,
        queue_name: String,
        payload: serde_json::Value,
    ) -> Result<Uuid> {
        let message = QueueMessage::new(queue_name, payload);
        let message_id = message.id;
        self.send(message).await?;
        Ok(message_id)
    }

    /// Receive a message from a queue
    pub async fn receive(&self, queue_name: &str) -> Option<QueueMessage> {
        let mut queues = self.queues.write().await;
        let queue = queues.get_mut(queue_name)?;

        // Find the first ready message
        let pos = queue.iter().position(|m| m.is_ready())?;
        queue.remove(pos)
    }

    /// Start processing messages
    pub async fn start_processing(&self) -> Result<()> {
        info!("Starting message queue processing");

        let cleanup_handle = self.start_cleanup_task();
        let processing_handle = self.start_processing_loop();

        tokio::select! {
            result = cleanup_handle => {
                error!("Cleanup task ended: {:?}", result);
                result
            }
            result = processing_handle => {
                error!("Processing loop ended: {:?}", result);
                result
            }
        }
    }

    /// Start the main processing loop
    async fn start_processing_loop(&self) -> Result<()> {
        loop {
            let mut processed_any = false;

            // Get all queue names
            let queue_names: Vec<String> = {
                let queues = self.queues.read().await;
                queues.keys().cloned().collect()
            };

            // Process messages from each queue
            for queue_name in queue_names {
                if let Some(message) = self.receive(&queue_name).await {
                    if let Err(e) = self.process_message(message).await {
                        error!("Error processing message: {}", e);
                    }
                    processed_any = true;
                }
            }

            if !processed_any {
                // Wait for new messages
                timeout(Duration::from_secs(1), self.notify.notified())
                    .await
                    .ok();
            }
        }
    }

    /// Process a single message
    async fn process_message(&self, message: QueueMessage) -> Result<()> {
        debug!(
            "Processing message: {} from queue: {}",
            message.id, message.queue_name
        );

        let handler = {
            let handlers = self.handlers.read().await;
            handlers.get(&message.queue_name).cloned()
        };

        if let Some(handler) = handler {
            let processing_timeout = Duration::from_secs(self.config.consumer_timeout_seconds);

            match timeout(processing_timeout, handler.handle(&message)).await {
                Ok(Ok(ProcessingResult::Success)) => {
                    debug!("Message processed successfully: {}", message.id);
                }
                Ok(Ok(ProcessingResult::Retry(reason))) => {
                    warn!(
                        "Message processing failed, will retry: {} - {}",
                        message.id, reason
                    );
                    self.handle_retry(message).await?;
                }
                Ok(Ok(ProcessingResult::Discard(reason))) => {
                    warn!("Message discarded: {} - {}", message.id, reason);
                    self.send_to_dead_letter_queue(message).await?;
                }
                Ok(Err(e)) => {
                    error!("Handler error for message {}: {}", message.id, e);
                    self.handle_retry(message).await?;
                }
                Err(_) => {
                    error!("Message processing timed out: {}", message.id);
                    self.handle_retry(message).await?;
                }
            }
        } else {
            warn!("No handler registered for queue: {}", message.queue_name);
            self.send_to_dead_letter_queue(message).await?;
        }

        Ok(())
    }

    /// Handle message retry
    async fn handle_retry(&self, mut message: QueueMessage) -> Result<()> {
        if message.can_retry() {
            message.increment_retry();
            info!(
                "Retrying message: {} (attempt {})",
                message.id, message.retry_count
            );
            self.send(message).await?;
        } else {
            warn!("Message exceeded max retries: {}", message.id);
            self.send_to_dead_letter_queue(message).await?;
        }
        Ok(())
    }

    /// Send message to dead letter queue
    async fn send_to_dead_letter_queue(&self, message: QueueMessage) -> Result<()> {
        if self.config.dead_letter_queue {
            let mut dlq = self.dead_letter_queue.write().await;
            dlq.push_back(message.clone());
            warn!("Message sent to dead letter queue: {}", message.id);
        }
        Ok(())
    }

    /// Get queue statistics
    pub async fn get_queue_stats(&self, queue_name: &str) -> QueueStats {
        let queues = self.queues.read().await;
        let queue = queues.get(queue_name);

        let (total_messages, ready_messages, scheduled_messages) = if let Some(queue) = queue {
            let total = queue.len();
            let ready = queue.iter().filter(|m| m.is_ready()).count();
            let scheduled = total - ready;
            (total, ready, scheduled)
        } else {
            (0, 0, 0)
        };

        QueueStats {
            queue_name: queue_name.to_string(),
            total_messages,
            ready_messages,
            scheduled_messages,
            dead_letter_messages: self.dead_letter_queue.read().await.len(),
        }
    }

    /// Get all queue names
    pub async fn get_queue_names(&self) -> Vec<String> {
        let queues = self.queues.read().await;
        queues.keys().cloned().collect()
    }

    /// Purge a queue
    pub async fn purge_queue(&self, queue_name: &str) -> Result<usize> {
        let mut queues = self.queues.write().await;
        if let Some(queue) = queues.get_mut(queue_name) {
            let count = queue.len();
            queue.clear();
            info!("Purged {} messages from queue: {}", count, queue_name);
            Ok(count)
        } else {
            Ok(0)
        }
    }

    /// Start cleanup task
    async fn start_cleanup_task(&self) -> Result<()> {
        let mut interval =
            tokio::time::interval(Duration::from_secs(self.config.cleanup_interval_seconds));

        loop {
            interval.tick().await;

            if let Err(e) = self.cleanup_expired_messages().await {
                error!("Error during message cleanup: {}", e);
            }
        }
    }

    /// Clean up expired messages
    async fn cleanup_expired_messages(&self) -> Result<()> {
        let mut total_cleaned = 0;

        {
            let mut queues = self.queues.write().await;
            for (queue_name, queue) in queues.iter_mut() {
                let original_len = queue.len();
                queue.retain(|message| !message.is_expired());
                let cleaned = original_len - queue.len();

                if cleaned > 0 {
                    info!(
                        "Cleaned {} expired messages from queue: {}",
                        cleaned, queue_name
                    );
                    total_cleaned += cleaned;
                }
            }
        }

        // Clean up dead letter queue
        {
            let mut dlq = self.dead_letter_queue.write().await;
            let original_len = dlq.len();
            dlq.retain(|message| !message.is_expired());
            let cleaned = original_len - dlq.len();

            if cleaned > 0 {
                info!(
                    "Cleaned {} expired messages from dead letter queue",
                    cleaned
                );
                total_cleaned += cleaned;
            }
        }

        if total_cleaned > 0 {
            debug!("Total expired messages cleaned: {}", total_cleaned);
        }

        Ok(())
    }
}

/// Queue statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueueStats {
    pub queue_name: String,
    pub total_messages: usize,
    pub ready_messages: usize,
    pub scheduled_messages: usize,
    pub dead_letter_messages: usize,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[allow(dead_code)]
    struct TestHandler;

    #[async_trait::async_trait]
    impl MessageHandler for TestHandler {
        async fn handle(&self, _message: &QueueMessage) -> Result<ProcessingResult> {
            Ok(ProcessingResult::Success)
        }
    }

    #[tokio::test]
    async fn test_message_creation() {
        let message = QueueMessage::new(
            "test_queue".to_string(),
            serde_json::json!({"test": "data"}),
        );

        assert_eq!(message.queue_name, "test_queue");
        assert_eq!(message.priority, MessagePriority::Normal);
        assert_eq!(message.retry_count, 0);
    }

    #[tokio::test]
    async fn test_queue_send_receive() {
        let config = QueueConfig::default();
        let queue = MessageQueue::new(config);

        let message = QueueMessage::new(
            "test_queue".to_string(),
            serde_json::json!({"test": "data"}),
        );

        queue.send(message.clone()).await.unwrap();

        let received = queue.receive("test_queue").await;
        assert!(received.is_some());
        assert_eq!(received.unwrap().id, message.id);
    }

    #[tokio::test]
    async fn test_message_priority() {
        let config = QueueConfig::default();
        let queue = MessageQueue::new(config);

        let low_priority = QueueMessage::new(
            "test_queue".to_string(),
            serde_json::json!({"priority": "low"}),
        )
        .with_priority(MessagePriority::Low);

        let high_priority = QueueMessage::new(
            "test_queue".to_string(),
            serde_json::json!({"priority": "high"}),
        )
        .with_priority(MessagePriority::High);

        queue.send(low_priority).await.unwrap();
        queue.send(high_priority.clone()).await.unwrap();

        let received = queue.receive("test_queue").await.unwrap();
        assert_eq!(received.id, high_priority.id);
    }
}
