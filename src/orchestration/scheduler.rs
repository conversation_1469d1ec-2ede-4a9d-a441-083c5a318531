//! Job scheduler for managing scan workflows and periodic tasks

use crate::error::{InfinitumError, Result};
use chrono::{DateTime, Duration, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::sync::{mpsc, RwLock};
use tokio::time::{interval, sleep, Instant};
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// Job priority levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum JobPriority {
    Low = 1,
    Normal = 2,
    High = 3,
    Critical = 4,
}

/// Job status
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum JobStatus {
    Pending,
    Running,
    Completed,
    Failed,
    Cancelled,
    Retrying,
}

/// Job type enumeration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum JobType {
    SbomScan,
    HbomScan,
    VulnerabilityScan,
    ComplianceReport,
    BlockchainCommit,
    DatabaseCleanup,
    ReportGeneration,
    Custom(String),
}

/// Scheduled job definition
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ScheduledJob {
    pub id: Uuid,
    pub job_type: JobType,
    pub name: String,
    pub description: Option<String>,
    pub priority: JobPriority,
    pub status: JobStatus,
    pub payload: serde_json::Value,
    pub scheduled_at: DateTime<Utc>,
    pub started_at: Option<DateTime<Utc>>,
    pub completed_at: Option<DateTime<Utc>>,
    pub retry_count: u32,
    pub max_retries: u32,
    pub timeout_seconds: u64,
    pub error_message: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl ScheduledJob {
    pub fn new(
        job_type: JobType,
        name: String,
        payload: serde_json::Value,
        scheduled_at: DateTime<Utc>,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            job_type,
            name,
            description: None,
            priority: JobPriority::Normal,
            status: JobStatus::Pending,
            payload,
            scheduled_at,
            started_at: None,
            completed_at: None,
            retry_count: 0,
            max_retries: 3,
            timeout_seconds: 300,
            error_message: None,
            created_at: now,
            updated_at: now,
        }
    }

    pub fn with_priority(mut self, priority: JobPriority) -> Self {
        self.priority = priority;
        self
    }

    pub fn with_max_retries(mut self, max_retries: u32) -> Self {
        self.max_retries = max_retries;
        self
    }

    pub fn with_timeout(mut self, timeout_seconds: u64) -> Self {
        self.timeout_seconds = timeout_seconds;
        self
    }

    pub fn mark_running(&mut self) {
        self.status = JobStatus::Running;
        self.started_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    pub fn mark_completed(&mut self) {
        self.status = JobStatus::Completed;
        self.completed_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    pub fn mark_failed(&mut self, error: String) {
        self.status = JobStatus::Failed;
        self.completed_at = Some(Utc::now());
        self.error_message = Some(error);
        self.updated_at = Utc::now();
    }

    pub fn mark_retrying(&mut self) {
        self.status = JobStatus::Retrying;
        self.retry_count += 1;
        self.updated_at = Utc::now();
    }

    pub fn can_retry(&self) -> bool {
        self.retry_count < self.max_retries && self.status == JobStatus::Failed
    }
}

/// Job execution result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JobResult {
    pub job_id: Uuid,
    pub success: bool,
    pub result_data: Option<serde_json::Value>,
    pub error_message: Option<String>,
    pub execution_time_ms: u64,
}

/// Job scheduler configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SchedulerConfig {
    pub max_concurrent_jobs: usize,
    pub poll_interval_seconds: u64,
    pub job_timeout_seconds: u64,
    pub cleanup_interval_hours: u64,
    pub max_job_history: usize,
}

impl Default for SchedulerConfig {
    fn default() -> Self {
        Self {
            max_concurrent_jobs: 10,
            poll_interval_seconds: 5,
            job_timeout_seconds: 300,
            cleanup_interval_hours: 24,
            max_job_history: 1000,
        }
    }
}

/// Job scheduler
pub struct JobScheduler {
    config: SchedulerConfig,
    jobs: RwLock<HashMap<Uuid, ScheduledJob>>,
    running_jobs: RwLock<HashMap<Uuid, Instant>>,
    job_sender: mpsc::UnboundedSender<ScheduledJob>,
    #[allow(dead_code)]
    result_receiver: RwLock<Option<mpsc::UnboundedReceiver<JobResult>>>,
}

impl JobScheduler {
    /// Create a new job scheduler
    pub fn new(config: SchedulerConfig) -> Self {
        let (job_sender, _job_receiver) = mpsc::unbounded_channel();
        let (_result_sender, result_receiver) = mpsc::unbounded_channel();

        Self {
            config,
            jobs: RwLock::new(HashMap::new()),
            running_jobs: RwLock::new(HashMap::new()),
            job_sender,
            result_receiver: RwLock::new(Some(result_receiver)),
        }
    }

    /// Start the scheduler
    pub async fn start(&self) -> Result<()> {
        info!("Starting job scheduler");

        // Start the main scheduler loop
        let scheduler_handle = self.start_scheduler_loop();

        // Start the cleanup task
        let cleanup_handle = self.start_cleanup_task();

        // Wait for both tasks
        tokio::select! {
            result = scheduler_handle => {
                error!("Scheduler loop ended: {:?}", result);
                result
            }
            result = cleanup_handle => {
                error!("Cleanup task ended: {:?}", result);
                result
            }
        }
    }

    /// Schedule a new job
    pub async fn schedule_job(&self, job: ScheduledJob) -> Result<Uuid> {
        let job_id = job.id;
        info!("Scheduling job: {} ({})", job.name, job_id);

        {
            let mut jobs = self.jobs.write().await;
            jobs.insert(job_id, job.clone());
        }

        self.job_sender
            .send(job)
            .map_err(|e| InfinitumError::SchedulerError {
                message: format!("Failed to queue job: {}", e),
            })?;

        Ok(job_id)
    }

    /// Schedule a job to run immediately
    pub async fn schedule_immediate(
        &self,
        job_type: JobType,
        name: String,
        payload: serde_json::Value,
    ) -> Result<Uuid> {
        let job = ScheduledJob::new(job_type, name, payload, Utc::now());
        self.schedule_job(job).await
    }

    /// Schedule a job to run at a specific time
    pub async fn schedule_at(
        &self,
        job_type: JobType,
        name: String,
        payload: serde_json::Value,
        scheduled_at: DateTime<Utc>,
    ) -> Result<Uuid> {
        let job = ScheduledJob::new(job_type, name, payload, scheduled_at);
        self.schedule_job(job).await
    }

    /// Schedule a job to run after a delay
    pub async fn schedule_delayed(
        &self,
        job_type: JobType,
        name: String,
        payload: serde_json::Value,
        delay: Duration,
    ) -> Result<Uuid> {
        let scheduled_at = Utc::now() + delay;
        let job = ScheduledJob::new(job_type, name, payload, scheduled_at);
        self.schedule_job(job).await
    }

    /// Cancel a scheduled job
    pub async fn cancel_job(&self, job_id: Uuid) -> Result<bool> {
        let mut jobs = self.jobs.write().await;

        if let Some(job) = jobs.get_mut(&job_id) {
            if job.status == JobStatus::Pending || job.status == JobStatus::Retrying {
                job.status = JobStatus::Cancelled;
                job.updated_at = Utc::now();
                info!("Job cancelled: {}", job_id);
                Ok(true)
            } else {
                warn!("Cannot cancel job in status: {:?}", job.status);
                Ok(false)
            }
        } else {
            warn!("Job not found: {}", job_id);
            Ok(false)
        }
    }

    /// Get job status
    pub async fn get_job_status(&self, job_id: Uuid) -> Option<JobStatus> {
        let jobs = self.jobs.read().await;
        jobs.get(&job_id).map(|job| job.status.clone())
    }

    /// Get all jobs
    pub async fn get_all_jobs(&self) -> Vec<ScheduledJob> {
        let jobs = self.jobs.read().await;
        jobs.values().cloned().collect()
    }

    /// Get jobs by status
    pub async fn get_jobs_by_status(&self, status: JobStatus) -> Vec<ScheduledJob> {
        let jobs = self.jobs.read().await;
        jobs.values()
            .filter(|job| job.status == status)
            .cloned()
            .collect()
    }

    /// Start the main scheduler loop
    async fn start_scheduler_loop(&self) -> Result<()> {
        let mut interval = interval(std::time::Duration::from_secs(
            self.config.poll_interval_seconds,
        ));

        loop {
            interval.tick().await;

            if let Err(e) = self.process_pending_jobs().await {
                error!("Error processing jobs: {}", e);
            }

            if let Err(e) = self.check_running_jobs().await {
                error!("Error checking running jobs: {}", e);
            }
        }
    }

    /// Process pending jobs
    async fn process_pending_jobs(&self) -> Result<()> {
        let now = Utc::now();
        let running_count = self.running_jobs.read().await.len();

        if running_count >= self.config.max_concurrent_jobs {
            debug!("Max concurrent jobs reached: {}", running_count);
            return Ok(());
        }

        let mut jobs_to_run = Vec::new();

        {
            let jobs = self.jobs.read().await;
            for job in jobs.values() {
                if (job.status == JobStatus::Pending || job.status == JobStatus::Retrying)
                    && job.scheduled_at <= now
                    && jobs_to_run.len() < (self.config.max_concurrent_jobs - running_count)
                {
                    jobs_to_run.push(job.clone());
                }
            }
        }

        // Sort by priority and scheduled time
        jobs_to_run.sort_by(|a, b| {
            b.priority
                .cmp(&a.priority)
                .then_with(|| a.scheduled_at.cmp(&b.scheduled_at))
        });

        for job in jobs_to_run {
            if let Err(e) = self.start_job(job).await {
                error!("Failed to start job: {}", e);
            }
        }

        Ok(())
    }

    /// Start executing a job
    async fn start_job(&self, mut job: ScheduledJob) -> Result<()> {
        info!("Starting job: {} ({})", job.name, job.id);

        job.mark_running();

        {
            let mut jobs = self.jobs.write().await;
            jobs.insert(job.id, job.clone());
        }

        {
            let mut running_jobs = self.running_jobs.write().await;
            running_jobs.insert(job.id, Instant::now());
        }

        // In a real implementation, you would spawn the actual job execution here
        // For now, we'll simulate job completion
        let job_id = job.id;
        tokio::spawn(async move {
            // Simulate job execution
            sleep(std::time::Duration::from_secs(1)).await;

            // This would be replaced with actual job execution logic
            info!("Job completed: {}", job_id);
        });

        Ok(())
    }

    /// Check running jobs for timeouts
    async fn check_running_jobs(&self) -> Result<()> {
        let timeout_duration = std::time::Duration::from_secs(self.config.job_timeout_seconds);
        let mut timed_out_jobs = Vec::new();

        {
            let running_jobs = self.running_jobs.read().await;
            for (&job_id, &start_time) in running_jobs.iter() {
                if start_time.elapsed() > timeout_duration {
                    timed_out_jobs.push(job_id);
                }
            }
        }

        for job_id in timed_out_jobs {
            warn!("Job timed out: {}", job_id);
            self.handle_job_timeout(job_id).await?;
        }

        Ok(())
    }

    /// Handle job timeout
    async fn handle_job_timeout(&self, job_id: Uuid) -> Result<()> {
        {
            let mut running_jobs = self.running_jobs.write().await;
            running_jobs.remove(&job_id);
        }

        {
            let mut jobs = self.jobs.write().await;
            if let Some(job) = jobs.get_mut(&job_id) {
                job.mark_failed("Job timed out".to_string());

                if job.can_retry() {
                    job.mark_retrying();
                    job.scheduled_at = Utc::now() + Duration::seconds(30); // Retry after 30 seconds
                    info!(
                        "Job will be retried: {} (attempt {})",
                        job_id,
                        job.retry_count + 1
                    );
                }
            }
        }

        Ok(())
    }

    /// Start the cleanup task
    async fn start_cleanup_task(&self) -> Result<()> {
        let mut interval = interval(std::time::Duration::from_secs(
            self.config.cleanup_interval_hours * 3600,
        ));

        loop {
            interval.tick().await;

            if let Err(e) = self.cleanup_old_jobs().await {
                error!("Error during cleanup: {}", e);
            }
        }
    }

    /// Clean up old completed jobs
    async fn cleanup_old_jobs(&self) -> Result<()> {
        info!("Starting job cleanup");

        let cutoff_time = Utc::now() - Duration::hours(self.config.cleanup_interval_hours as i64);
        let mut jobs_to_remove = Vec::new();

        {
            let jobs = self.jobs.read().await;
            let mut completed_jobs: Vec<_> = jobs
                .values()
                .filter(|job| {
                    matches!(
                        job.status,
                        JobStatus::Completed | JobStatus::Failed | JobStatus::Cancelled
                    )
                })
                .collect();

            // Sort by completion time
            completed_jobs.sort_by_key(|job| job.completed_at);

            // Remove old jobs beyond the history limit
            if completed_jobs.len() > self.config.max_job_history {
                let excess_count = completed_jobs.len() - self.config.max_job_history;
                for job in completed_jobs.iter().take(excess_count) {
                    jobs_to_remove.push(job.id);
                }
            }

            // Remove jobs older than cutoff time
            for job in completed_jobs.iter() {
                if let Some(completed_at) = job.completed_at {
                    if completed_at < cutoff_time {
                        jobs_to_remove.push(job.id);
                    }
                }
            }
        }

        if !jobs_to_remove.is_empty() {
            let mut jobs = self.jobs.write().await;
            for job_id in &jobs_to_remove {
                jobs.remove(job_id);
            }
            info!("Cleaned up {} old jobs", jobs_to_remove.len());
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_job_creation() {
        let job = ScheduledJob::new(
            JobType::SbomScan,
            "Test Job".to_string(),
            serde_json::json!({"test": "data"}),
            Utc::now(),
        );

        assert_eq!(job.status, JobStatus::Pending);
        assert_eq!(job.retry_count, 0);
        assert_eq!(job.max_retries, 3);
    }

    #[tokio::test]
    async fn test_scheduler_creation() {
        let config = SchedulerConfig::default();
        let scheduler = JobScheduler::new(config);

        let jobs = scheduler.get_all_jobs().await;
        assert!(jobs.is_empty());
    }
}
