use crate::{config::ScanningConfig, error::Result};
use serde::{Deserialize, Serialize};
use std::{
    collections::{HashMap, HashSet},
    path::Path,
};
use tracing::{info, instrument};

/// Dependency resolver for building dependency trees and analyzing relationships
pub struct DependencyResolver {
    #[allow(dead_code)]
    config: ScanningConfig,
}

/// Dependency tree structure
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DependencyTree {
    /// Root dependencies (direct dependencies)
    pub root_dependencies: Vec<Dependency>,
    /// All dependencies (including transitive)
    pub all_dependencies: HashMap<String, Dependency>,
    /// Dependency relationships
    pub relationships: Vec<DependencyRelationship>,
    /// Dependency conflicts
    pub conflicts: Vec<DependencyConflict>,
    /// Circular dependencies
    pub circular_dependencies: Vec<Vec<String>>,
    /// Dependency statistics
    pub statistics: DependencyStatistics,
}

/// Individual dependency information
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Dependency {
    /// Dependency name
    pub name: String,
    /// Dependency version
    pub version: String,
    /// Package type (e.g., "crate", "npm", "pypi")
    pub package_type: String,
    /// Dependency scope
    pub scope: DependencyScope,
    /// Direct dependencies of this dependency
    pub dependencies: Vec<String>,
    /// Dependency metadata
    pub metadata: HashMap<String, String>,
    /// License information
    pub license: Option<String>,
    /// Repository URL
    pub repository: Option<String>,
    /// Description
    pub description: Option<String>,
    /// Dependency depth in the tree
    pub depth: u32,
    /// Whether this is a direct dependency
    pub is_direct: bool,
    /// Whether this dependency is optional
    pub is_optional: bool,
}

/// Dependency scope
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
#[serde(rename_all = "lowercase")]
pub enum DependencyScope {
    Runtime,
    Development,
    Test,
    Build,
    Optional,
    Peer,
    Unknown,
}

/// Dependency relationship
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyRelationship {
    /// Parent dependency
    pub parent: String,
    /// Child dependency
    pub child: String,
    /// Relationship type
    pub relationship_type: RelationshipType,
    /// Version constraint
    pub version_constraint: Option<String>,
}

/// Relationship types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum RelationshipType {
    /// Direct dependency
    Direct,
    /// Transitive dependency
    Transitive,
    /// Optional dependency
    Optional,
    /// Peer dependency
    Peer,
    /// Development dependency
    Development,
}

/// Dependency conflict information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyConflict {
    /// Conflicting dependency name
    pub dependency_name: String,
    /// Conflicting versions
    pub conflicting_versions: Vec<String>,
    /// Dependencies that require different versions
    pub required_by: Vec<String>,
    /// Conflict severity
    pub severity: ConflictSeverity,
    /// Suggested resolution
    pub suggested_resolution: Option<String>,
}

/// Conflict severity levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ConflictSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// Dependency statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyStatistics {
    /// Total number of dependencies
    pub total_dependencies: u32,
    /// Number of direct dependencies
    pub direct_dependencies: u32,
    /// Number of transitive dependencies
    pub transitive_dependencies: u32,
    /// Maximum dependency depth
    pub max_depth: u32,
    /// Dependencies by scope
    pub dependencies_by_scope: HashMap<DependencyScope, u32>,
    /// Dependencies by package type
    pub dependencies_by_type: HashMap<String, u32>,
    /// Unique licenses found
    pub unique_licenses: HashSet<String>,
}

impl DependencyResolver {
    /// Create new dependency resolver
    pub fn new(config: &ScanningConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }

    /// Resolve dependencies for a project
    #[instrument(skip(self), fields(project_path = %project_path.display()))]
    pub async fn resolve_dependencies(&self, project_path: &Path) -> Result<DependencyTree> {
        info!("Starting dependency resolution");

        let mut dependency_tree = DependencyTree {
            root_dependencies: Vec::new(),
            all_dependencies: HashMap::new(),
            relationships: Vec::new(),
            conflicts: Vec::new(),
            circular_dependencies: Vec::new(),
            statistics: DependencyStatistics {
                total_dependencies: 0,
                direct_dependencies: 0,
                transitive_dependencies: 0,
                max_depth: 0,
                dependencies_by_scope: HashMap::new(),
                dependencies_by_type: HashMap::new(),
                unique_licenses: HashSet::new(),
            },
        };

        // Detect project type and resolve dependencies accordingly
        if project_path.join("Cargo.toml").exists() {
            self.resolve_rust_dependencies(project_path, &mut dependency_tree)
                .await?;
        } else if project_path.join("package.json").exists() {
            self.resolve_javascript_dependencies(project_path, &mut dependency_tree)
                .await?;
        } else if project_path.join("requirements.txt").exists()
            || project_path.join("pyproject.toml").exists()
        {
            self.resolve_python_dependencies(project_path, &mut dependency_tree)
                .await?;
        }

        // Analyze dependency relationships
        self.analyze_relationships(&mut dependency_tree).await?;

        // Detect conflicts
        self.detect_conflicts(&mut dependency_tree).await?;

        // Detect circular dependencies
        self.detect_circular_dependencies(&mut dependency_tree)
            .await?;

        // Calculate statistics
        self.calculate_statistics(&mut dependency_tree).await?;

        info!(
            total_deps = dependency_tree.statistics.total_dependencies,
            direct_deps = dependency_tree.statistics.direct_dependencies,
            conflicts = dependency_tree.conflicts.len(),
            "Dependency resolution completed"
        );

        Ok(dependency_tree)
    }

    /// Resolve Rust dependencies from Cargo.toml and Cargo.lock
    async fn resolve_rust_dependencies(
        &self,
        project_path: &Path,
        tree: &mut DependencyTree,
    ) -> Result<()> {
        let cargo_toml_path = project_path.join("Cargo.toml");
        let cargo_lock_path = project_path.join("Cargo.lock");

        // Parse Cargo.toml for direct dependencies
        if cargo_toml_path.exists() {
            let cargo_toml_content = tokio::fs::read_to_string(&cargo_toml_path).await?;
            let cargo_toml: toml::Value = toml::from_str(&cargo_toml_content)?;

            // Parse dependencies
            if let Some(dependencies) = cargo_toml.get("dependencies").and_then(|d| d.as_table()) {
                for (name, dep_info) in dependencies {
                    let dependency = self.parse_rust_dependency(
                        name,
                        dep_info,
                        DependencyScope::Runtime,
                        0,
                        true,
                    )?;
                    tree.root_dependencies.push(dependency.clone());
                    tree.all_dependencies.insert(name.clone(), dependency);
                }
            }

            // Parse dev-dependencies
            if let Some(dev_dependencies) = cargo_toml
                .get("dev-dependencies")
                .and_then(|d| d.as_table())
            {
                for (name, dep_info) in dev_dependencies {
                    let dependency = self.parse_rust_dependency(
                        name,
                        dep_info,
                        DependencyScope::Development,
                        0,
                        true,
                    )?;
                    tree.all_dependencies.insert(name.clone(), dependency);
                }
            }

            // Parse build-dependencies
            if let Some(build_dependencies) = cargo_toml
                .get("build-dependencies")
                .and_then(|d| d.as_table())
            {
                for (name, dep_info) in build_dependencies {
                    let dependency = self.parse_rust_dependency(
                        name,
                        dep_info,
                        DependencyScope::Build,
                        0,
                        true,
                    )?;
                    tree.all_dependencies.insert(name.clone(), dependency);
                }
            }
        }

        // Parse Cargo.lock for complete dependency tree
        if cargo_lock_path.exists() {
            let cargo_lock_content = tokio::fs::read_to_string(&cargo_lock_path).await?;
            let cargo_lock: toml::Value = toml::from_str(&cargo_lock_content)?;

            if let Some(packages) = cargo_lock.get("package").and_then(|p| p.as_array()) {
                for package in packages {
                    if let Some(package_table) = package.as_table() {
                        if let (Some(name), Some(version)) = (
                            package_table.get("name").and_then(|n| n.as_str()),
                            package_table.get("version").and_then(|v| v.as_str()),
                        ) {
                            // Update or create dependency with exact version
                            let mut dependency =
                                tree.all_dependencies.get(name).cloned().unwrap_or_else(|| {
                                    Dependency {
                                        name: name.to_string(),
                                        version: version.to_string(),
                                        package_type: "crate".to_string(),
                                        scope: DependencyScope::Runtime,
                                        dependencies: Vec::new(),
                                        metadata: HashMap::new(),
                                        license: None,
                                        repository: None,
                                        description: None,
                                        depth: 1,
                                        is_direct: false,
                                        is_optional: false,
                                    }
                                });

                            dependency.version = version.to_string();

                            // Parse dependencies of this package
                            if let Some(deps) =
                                package_table.get("dependencies").and_then(|d| d.as_array())
                            {
                                for dep in deps {
                                    if let Some(dep_str) = dep.as_str() {
                                        let dep_name =
                                            dep_str.split_whitespace().next().unwrap_or(dep_str);
                                        dependency.dependencies.push(dep_name.to_string());
                                    }
                                }
                            }

                            tree.all_dependencies.insert(name.to_string(), dependency);
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// Parse Rust dependency from Cargo.toml
    fn parse_rust_dependency(
        &self,
        name: &str,
        dep_info: &toml::Value,
        scope: DependencyScope,
        depth: u32,
        is_direct: bool,
    ) -> Result<Dependency> {
        let version = match dep_info {
            toml::Value::String(version) => version.clone(),
            toml::Value::Table(table) => table
                .get("version")
                .and_then(|v| v.as_str())
                .unwrap_or("*")
                .to_string(),
            _ => "*".to_string(),
        };

        let is_optional = match dep_info {
            toml::Value::Table(table) => table
                .get("optional")
                .and_then(|o| o.as_bool())
                .unwrap_or(false),
            _ => false,
        };

        Ok(Dependency {
            name: name.to_string(),
            version,
            package_type: "crate".to_string(),
            scope,
            dependencies: Vec::new(),
            metadata: HashMap::new(),
            license: None,
            repository: None,
            description: None,
            depth,
            is_direct,
            is_optional,
        })
    }

    /// Resolve JavaScript dependencies (placeholder)
    async fn resolve_javascript_dependencies(
        &self,
        _project_path: &Path,
        _tree: &mut DependencyTree,
    ) -> Result<()> {
        // TODO: Implement JavaScript dependency resolution
        Ok(())
    }

    /// Resolve Python dependencies (placeholder)
    async fn resolve_python_dependencies(
        &self,
        _project_path: &Path,
        _tree: &mut DependencyTree,
    ) -> Result<()> {
        // TODO: Implement Python dependency resolution
        Ok(())
    }

    /// Analyze dependency relationships
    async fn analyze_relationships(&self, tree: &mut DependencyTree) -> Result<()> {
        for (parent_name, parent_dep) in &tree.all_dependencies {
            for child_name in &parent_dep.dependencies {
                let relationship_type = if parent_dep.is_direct {
                    if parent_dep.scope == DependencyScope::Development {
                        RelationshipType::Development
                    } else if parent_dep.is_optional {
                        RelationshipType::Optional
                    } else {
                        RelationshipType::Direct
                    }
                } else {
                    RelationshipType::Transitive
                };

                tree.relationships.push(DependencyRelationship {
                    parent: parent_name.clone(),
                    child: child_name.clone(),
                    relationship_type,
                    version_constraint: None,
                });
            }
        }

        Ok(())
    }

    /// Detect dependency conflicts
    async fn detect_conflicts(&self, tree: &mut DependencyTree) -> Result<()> {
        let mut version_map: HashMap<String, Vec<(String, String)>> = HashMap::new();

        // Collect all versions for each dependency
        for (name, dep) in &tree.all_dependencies {
            version_map
                .entry(dep.name.clone())
                .or_default()
                .push((name.clone(), dep.version.clone()));
        }

        // Find conflicts
        for (dep_name, versions) in version_map {
            let unique_versions: HashSet<String> =
                versions.iter().map(|(_, v)| v.clone()).collect();

            if unique_versions.len() > 1 {
                let conflict = DependencyConflict {
                    dependency_name: dep_name,
                    conflicting_versions: unique_versions.into_iter().collect(),
                    required_by: versions.into_iter().map(|(name, _)| name).collect(),
                    severity: ConflictSeverity::Medium,
                    suggested_resolution: None,
                };
                tree.conflicts.push(conflict);
            }
        }

        Ok(())
    }

    /// Detect circular dependencies
    async fn detect_circular_dependencies(&self, tree: &mut DependencyTree) -> Result<()> {
        let mut visited = HashSet::new();
        let mut rec_stack = HashSet::new();
        let mut path = Vec::new();

        for dep_name in tree.all_dependencies.keys() {
            if !visited.contains(dep_name) {
                self.dfs_circular_detection(
                    dep_name,
                    &tree.all_dependencies,
                    &mut visited,
                    &mut rec_stack,
                    &mut path,
                    &mut tree.circular_dependencies,
                );
            }
        }

        Ok(())
    }

    /// DFS for circular dependency detection
    #[allow(clippy::only_used_in_recursion)]
    fn dfs_circular_detection(
        &self,
        node: &str,
        dependencies: &HashMap<String, Dependency>,
        visited: &mut HashSet<String>,
        rec_stack: &mut HashSet<String>,
        path: &mut Vec<String>,
        circular_deps: &mut Vec<Vec<String>>,
    ) {
        visited.insert(node.to_string());
        rec_stack.insert(node.to_string());
        path.push(node.to_string());

        if let Some(dep) = dependencies.get(node) {
            for child in &dep.dependencies {
                if !visited.contains(child) {
                    self.dfs_circular_detection(
                        child,
                        dependencies,
                        visited,
                        rec_stack,
                        path,
                        circular_deps,
                    );
                } else if rec_stack.contains(child) {
                    // Found a cycle
                    if let Some(cycle_start) = path.iter().position(|x| x == child) {
                        let cycle = path[cycle_start..].to_vec();
                        circular_deps.push(cycle);
                    }
                }
            }
        }

        path.pop();
        rec_stack.remove(node);
    }

    /// Calculate dependency statistics
    async fn calculate_statistics(&self, tree: &mut DependencyTree) -> Result<()> {
        let stats = &mut tree.statistics;

        stats.total_dependencies = tree.all_dependencies.len() as u32;
        stats.direct_dependencies = tree.root_dependencies.len() as u32;
        stats.transitive_dependencies = stats.total_dependencies - stats.direct_dependencies;

        // Calculate max depth and other statistics
        for dep in tree.all_dependencies.values() {
            stats.max_depth = stats.max_depth.max(dep.depth);

            *stats
                .dependencies_by_scope
                .entry(dep.scope.clone())
                .or_insert(0) += 1;
            *stats
                .dependencies_by_type
                .entry(dep.package_type.clone())
                .or_insert(0) += 1;

            if let Some(license) = &dep.license {
                stats.unique_licenses.insert(license.clone());
            }
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_dependency_creation() {
        let dep = Dependency {
            name: "serde".to_string(),
            version: "1.0.0".to_string(),
            package_type: "crate".to_string(),
            scope: DependencyScope::Runtime,
            dependencies: vec!["serde_derive".to_string()],
            metadata: HashMap::new(),
            license: Some("MIT".to_string()),
            repository: None,
            description: None,
            depth: 0,
            is_direct: true,
            is_optional: false,
        };

        assert_eq!(dep.name, "serde");
        assert_eq!(dep.scope, DependencyScope::Runtime);
        assert!(dep.is_direct);
    }

    #[test]
    fn test_conflict_severity() {
        let conflict = DependencyConflict {
            dependency_name: "test".to_string(),
            conflicting_versions: vec!["1.0.0".to_string(), "2.0.0".to_string()],
            required_by: vec!["dep1".to_string(), "dep2".to_string()],
            severity: ConflictSeverity::High,
            suggested_resolution: None,
        };

        assert_eq!(conflict.severity, ConflictSeverity::High);
        assert_eq!(conflict.conflicting_versions.len(), 2);
    }
}
