//! # Multi-Layered License Detection Pipeline
//!
//! This module implements a comprehensive license detection system that combines multiple
//! detection methods to achieve high accuracy and handle diverse file types and scenarios.

use async_trait::async_trait;
use crate::{config::ScanningConfig, error::Result};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    path::{Path, PathBuf},
    sync::Arc,
    time::Duration,
};
use tokio::sync::RwLock;
use tracing::{debug, info, instrument, warn};

/// Detection layers for multi-layered license detection
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
#[serde(rename_all = "snake_case")]
pub enum DetectionLayer {
    /// Source code analysis (comments, headers, etc.)
    SourceCode,
    /// Binary analysis for embedded licenses
    BinaryAnalysis,
    /// Metadata extraction from files
    MetadataExtraction,
    /// Package manager file analysis
    PackageManager,
    /// Standard license file detection
    LicenseFile,
    /// Copyright pattern detection
    CopyrightDetection,
    /// Metadata analysis
    MetadataAnalysis,
}

impl std::fmt::Display for DetectionLayer {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DetectionLayer::SourceCode => write!(f, "source_code"),
            DetectionLayer::BinaryAnalysis => write!(f, "binary_analysis"),
            DetectionLayer::MetadataExtraction => write!(f, "metadata_extraction"),
            DetectionLayer::PackageManager => write!(f, "package_manager"),
            DetectionLayer::LicenseFile => write!(f, "license_file"),
            DetectionLayer::CopyrightDetection => write!(f, "copyright_detection"),
            DetectionLayer::MetadataAnalysis => write!(f, "metadata_analysis"),
        }
    }
}

/// File type classification for detection layer selection
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum FileType {
    /// Source code files
    SourceCode,
    /// Binary executables and libraries
    Binary,
    /// Package manager files
    PackageManager,
    /// License files
    License,
    /// Documentation files
    Documentation,
    /// Archive files
    Archive,
    /// Image files
    Image,
    /// Unknown file type
    Unknown,
}

/// Detection result from a single layer
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LayerDetectionResult {
    /// Detection layer that produced this result
    pub layer: DetectionLayer,
    /// File path that was analyzed
    pub file_path: PathBuf,
    /// Detected licenses
    pub licenses: Vec<AdvancedLicenseInfo>,
    /// Detected copyrights
    pub copyrights: Vec<CopyrightInfo>,
    /// Package information
    pub package_info: Option<PackageInfo>,
    /// Confidence score for this layer's detection (0.0 to 1.0)
    pub confidence: f64,
    /// Evidence supporting the detection
    pub evidence: Vec<String>,
    /// Processing time for this layer
    pub processing_time: Duration,
    /// Timestamp of detection
    pub detected_at: chrono::DateTime<chrono::Utc>,
}

/// Aggregated detection result combining multiple layers
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AggregatedDetectionResult {
    /// File path that was analyzed
    pub file_path: PathBuf,
    /// Results from each detection layer
    pub layer_results: HashMap<DetectionLayer, LayerDetectionResult>,
    /// Final aggregated licenses with conflict resolution
    pub final_licenses: Vec<AdvancedLicenseInfo>,
    /// Final aggregated copyrights
    pub final_copyrights: Vec<CopyrightInfo>,
    /// Final package information
    pub final_package_info: Option<PackageInfo>,
    /// Overall confidence score
    pub overall_confidence: f64,
    /// Conflict resolution information
    pub conflicts: Vec<LicenseConflict>,
    /// Detection metadata
    pub metadata: HashMap<String, serde_json::Value>,
    /// Total processing time
    pub total_processing_time: Duration,
    /// Timestamp of aggregation
    pub aggregated_at: chrono::DateTime<chrono::Utc>,
}

/// License conflict information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LicenseConflict {
    /// Conflicting license identifiers
    pub conflicting_licenses: Vec<String>,
    /// Reason for conflict
    pub reason: String,
    /// Resolution applied
    pub resolution: ConflictResolution,
    /// Confidence in resolution
    pub resolution_confidence: f64,
}

/// Conflict resolution strategies
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum ConflictResolution {
    /// Keep the highest confidence license
    HighestConfidence,
    /// Combine licenses (dual licensing)
    Combine,
    /// Mark as ambiguous
    Ambiguous,
    /// Manual review required
    ManualReview,
}

/// Advanced license information with detailed metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdvancedLicenseInfo {
    /// SPDX license identifier
    pub spdx_id: String,
    /// License name
    pub name: String,
    /// License category
    pub category: LicenseCategory,
    /// License text content
    pub text: Option<String>,
    /// License URL
    pub url: Option<String>,
    /// OSI approved status
    pub osi_approved: bool,
    /// FSF Libre status
    pub fsf_libre: bool,
    /// Detection confidence score (0.0 to 1.0)
    pub confidence: f64,
    /// Start line number in file
    pub start_line: Option<u32>,
    /// End line number in file
    pub end_line: Option<u32>,
    /// Matched text length
    pub matched_length: Option<u32>,
    /// Rule identifier that matched
    pub rule_identifier: Option<String>,
    /// Detection layer that found this license
    pub detection_layer: DetectionLayer,
    /// Evidence supporting this detection
    pub evidence: Vec<String>,
    /// Additional metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Copyright information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CopyrightInfo {
    /// Copyright statement text
    pub statement: String,
    /// Start line number
    pub start_line: Option<u32>,
    /// End line number in file
    pub end_line: Option<u32>,
    /// Detection confidence
    pub confidence: f64,
    /// Detection layer that found this copyright
    pub detection_layer: DetectionLayer,
    /// Evidence supporting this detection
    pub evidence: Vec<String>,
}

/// Package information detected
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageInfo {
    /// Package type (e.g., "cargo", "npm", "pypi")
    pub package_type: String,
    /// Package namespace
    pub namespace: Option<String>,
    /// Package name
    pub name: String,
    /// Package version
    pub version: Option<String>,
    /// Primary license
    pub primary_license: Option<String>,
    /// Description
    pub description: Option<String>,
    /// Homepage URL
    pub homepage_url: Option<String>,
    /// Detection layer that found this package info
    pub detection_layer: DetectionLayer,
}

/// License categories
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum LicenseCategory {
    /// Permissive licenses (MIT, BSD, Apache)
    Permissive,
    /// Copyleft licenses (GPL, LGPL)
    Copyleft,
    /// Weak copyleft licenses
    WeakCopyleft,
    /// Proprietary licenses
    Proprietary,
    /// Public domain
    PublicDomain,
    /// Unknown category
    Unknown,
}

/// Configuration for multi-layered license detection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MultiLayerConfig {
    /// Enabled detection layers
    pub enabled_layers: Vec<DetectionLayer>,
    /// Minimum confidence threshold for layer results
    pub min_layer_confidence: f64,
    /// Minimum overall confidence for final results
    pub min_overall_confidence: f64,
    /// Maximum processing time per file
    pub max_processing_time_per_file: Duration,
    /// Enable parallel processing
    pub enable_parallel_processing: bool,
    /// Maximum concurrent file processing
    pub max_concurrent_files: usize,
    /// Enable caching of detection results
    pub enable_caching: bool,
    /// Cache TTL in seconds
    pub cache_ttl_seconds: u64,
    /// File size limits for different file types
    pub file_size_limits: HashMap<String, u64>,
    /// Include license text in results
    pub include_license_text: bool,
    /// Enable conflict resolution
    pub enable_conflict_resolution: bool,
    /// Conflict resolution strategy
    pub conflict_resolution_strategy: ConflictResolutionStrategy,
}

/// Conflict resolution strategies
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum ConflictResolutionStrategy {
    /// Prioritize highest confidence
    HighestConfidence,
    /// Combine compatible licenses
    CombineCompatible,
    /// Flag all conflicts for review
    FlagConflicts,
}

/// Detection pipeline statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetectionStats {
    /// Total files processed
    pub files_processed: u64,
    /// Files with license detections
    pub files_with_licenses: u64,
    /// Files with copyright detections
    pub files_with_copyrights: u64,
    /// Total license detections
    pub total_license_detections: u64,
    /// Total copyright detections
    pub total_copyright_detections: u64,
    /// Conflicts detected
    pub conflicts_detected: u64,
    /// Average processing time per file
    pub avg_processing_time_ms: f64,
    /// Cache hit rate
    pub cache_hit_rate: f64,
    /// Layer-specific statistics
    pub layer_stats: HashMap<DetectionLayer, LayerStats>,
}

/// Statistics for a specific detection layer
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LayerStats {
    /// Files processed by this layer
    pub files_processed: u64,
    /// Successful detections
    pub successful_detections: u64,
    /// Failed detections
    pub failed_detections: u64,
    /// Average confidence score
    pub avg_confidence: f64,
    /// Average processing time
    pub avg_processing_time_ms: f64,
}

impl Default for MultiLayerConfig {
    fn default() -> Self {
        let mut file_size_limits = HashMap::new();
        file_size_limits.insert("binary".to_string(), 100 * 1024 * 1024); // 100MB
        file_size_limits.insert("source".to_string(), 10 * 1024 * 1024);  // 10MB
        file_size_limits.insert("text".to_string(), 5 * 1024 * 1024);    // 5MB

        Self {
            enabled_layers: vec![
                DetectionLayer::SourceCode,
                DetectionLayer::BinaryAnalysis,
                DetectionLayer::MetadataExtraction,
                DetectionLayer::PackageManager,
                DetectionLayer::LicenseFile,
                DetectionLayer::CopyrightDetection,
            ],
            min_layer_confidence: 0.6,
            min_overall_confidence: 0.8,
            max_processing_time_per_file: Duration::from_secs(30),
            enable_parallel_processing: true,
            max_concurrent_files: 10,
            enable_caching: true,
            cache_ttl_seconds: 3600, // 1 hour
            file_size_limits,
            include_license_text: true,
            enable_conflict_resolution: true,
            conflict_resolution_strategy: ConflictResolutionStrategy::HighestConfidence,
        }
    }
}

/// License detector trait for different detection layers
#[async_trait::async_trait]
pub trait LicenseDetector: Send + Sync {
    /// Detect licenses and copyrights in a file
    async fn detect(&self, file_path: &Path) -> Result<LayerDetectionResult>;
}

/// Helper trait for adding processing time to results
pub trait WithProcessingTime {
    fn with_processing_time(self, duration: Duration) -> Self;
}

impl WithProcessingTime for LayerDetectionResult {
    fn with_processing_time(mut self, duration: Duration) -> Self {
        self.processing_time = duration;
        self
    }
}
/// Multi-layered license detector orchestrator
pub struct MultiLayeredLicenseDetector {
    config: MultiLayerConfig,
    base_config: ScanningConfig,
    detectors: HashMap<DetectionLayer, Arc<dyn LicenseDetector>>,
    cache: Arc<RwLock<HashMap<String, AggregatedDetectionResult>>>,
    stats: Arc<RwLock<DetectionStats>>,
}

impl MultiLayeredLicenseDetector {
    /// Create new multi-layered license detector
    pub fn new(base_config: &ScanningConfig, config: MultiLayerConfig) -> Self {
        let mut detectors = HashMap::new();

        // Initialize detectors for enabled layers
        for layer in &config.enabled_layers {
            let detector: Arc<dyn LicenseDetector> = match layer {
                DetectionLayer::SourceCode => Arc::new(SourceCodeLicenseDetector::new(&config)),
                DetectionLayer::BinaryAnalysis => Arc::new(BinaryLicenseDetector::new(&config)),
                DetectionLayer::MetadataExtraction => Arc::new(MetadataLicenseDetector::new(&config)),
                DetectionLayer::PackageManager => Arc::new(PackageManagerDetector::new(&config)),
                DetectionLayer::LicenseFile => Arc::new(LicenseFileDetector::new(&config)),
                DetectionLayer::CopyrightDetection => Arc::new(CopyrightPatternDetector::new(&config)),
                DetectionLayer::MetadataAnalysis => Arc::new(MetadataLicenseDetector::new()),
            };
            detectors.insert(layer.clone(), detector);
        }

        Self {
            config,
            base_config: base_config.clone(),
            detectors,
            cache: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(RwLock::new(DetectionStats::default())),
        }
    }

    /// Detect licenses in a single file using all enabled layers
    #[instrument(skip(self), fields(file_path = %file_path.display()))]
    pub async fn detect_file(&self, file_path: &Path) -> Result<AggregatedDetectionResult> {
        let start_time = std::time::Instant::now();

        // Check cache first
        if self.config.enable_caching {
            if let Some(cached_result) = self.check_cache(file_path).await {
                debug!("Cache hit for file: {}", file_path.display());
                return Ok(cached_result);
            }
        }

        // Determine file type and applicable layers
        let file_type = self.determine_file_type(file_path)?;
        let applicable_layers = self.get_applicable_layers(&file_type);

        info!(
            file_path = %file_path.display(),
            file_type = ?file_type,
            layers = ?applicable_layers.len(),
            "Starting multi-layer license detection"
        );

        // Run detection on applicable layers
        let layer_results = if self.config.enable_parallel_processing {
            self.run_parallel_detection(file_path, &applicable_layers).await?
        } else {
            self.run_sequential_detection(file_path, &applicable_layers).await?
        };

        // Aggregate results
        let aggregated_result = self.aggregate_results(file_path, layer_results).await;

        // Cache the result
        if self.config.enable_caching {
            self.cache_result(file_path, &aggregated_result).await;
        }

        // Update statistics
        self.update_stats(&aggregated_result, start_time.elapsed()).await;

        info!(
            file_path = %file_path.display(),
            licenses_found = aggregated_result.final_licenses.len(),
            copyrights_found = aggregated_result.final_copyrights.len(),
            overall_confidence = aggregated_result.overall_confidence,
            processing_time_ms = aggregated_result.total_processing_time.as_millis(),
            "Multi-layer license detection completed"
        );

        Ok(aggregated_result)
    }

    /// Detect licenses in a project directory
    #[instrument(skip(self), fields(project_path = %project_path.display()))]
    pub async fn detect_project(&self, project_path: &Path) -> Result<Vec<AggregatedDetectionResult>> {
        info!("Starting project-wide license detection");

        let mut results = Vec::new();
        let mut file_paths = Vec::new();

        // Collect all files to process
        self.collect_files(project_path, &mut file_paths)?;

        // Process files (parallel or sequential based on config)
        if self.config.enable_parallel_processing {
            results = self.process_files_parallel(&file_paths).await?;
        } else {
            for file_path in &file_paths {
                let result = self.detect_file(file_path).await?;
                results.push(result);
            }
        }

        info!(
            project_path = %project_path.display(),
            files_processed = results.len(),
            total_licenses = results.iter().map(|r| r.final_licenses.len()).sum::<usize>(),
            total_copyrights = results.iter().map(|r| r.final_copyrights.len()).sum::<usize>(),
            "Project license detection completed"
        );

        Ok(results)
    }

    /// Get detection statistics
    pub async fn get_stats(&self) -> DetectionStats {
        self.stats.read().await.clone()
    }

    /// Clear detection cache
    pub async fn clear_cache(&self) {
        let mut cache = self.cache.write().await;
        cache.clear();
        info!("Detection cache cleared");
    }

    /// Check cache for existing result
    async fn check_cache(&self, file_path: &Path) -> Option<AggregatedDetectionResult> {
        let cache_key = self.cache_key(file_path);
        let cache = self.cache.read().await;

        if let Some(result) = cache.get(&cache_key) {
            // Check if cache entry is still valid
            let age = chrono::Utc::now().signed_duration_since(result.aggregated_at);
            if age.num_seconds() < self.config.cache_ttl_seconds as i64 {
                return Some(result.clone());
            }
        }
        None
    }

    /// Cache detection result
    async fn cache_result(&self, file_path: &Path, result: &AggregatedDetectionResult) {
        let cache_key = self.cache_key(file_path);
        let mut cache = self.cache.write().await;
        cache.insert(cache_key, result.clone());
    }

    /// Generate cache key for file path
    fn cache_key(&self, file_path: &Path) -> String {
        format!("{}:{}", file_path.display(), file_path.metadata().map(|m| m.modified().unwrap_or(std::time::SystemTime::UNIX_EPOCH)).unwrap_or(std::time::SystemTime::UNIX_EPOCH).duration_since(std::time::SystemTime::UNIX_EPOCH).unwrap_or_default().as_secs())
    }

    /// Determine file type for layer selection
    fn determine_file_type(&self, file_path: &Path) -> Result<FileType> {
        let extension = file_path.extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("")
            .to_lowercase();

        let file_type = match extension.as_str() {
            // Source code files
            "rs" | "js" | "ts" | "py" | "java" | "cpp" | "c" | "h" | "hpp" | "go" | "rb" | "php" | "cs" | "swift" | "kt" | "scala" => FileType::SourceCode,

            // Binary files
            "exe" | "dll" | "so" | "dylib" | "bin" => FileType::Binary,

            // Package manager files
            "json" | "toml" | "yaml" | "yml" | "xml" | "lock" => {
                if self.is_package_file(file_path) {
                    FileType::PackageManager
                } else {
                    FileType::SourceCode
                }
            },

            // License files
            _ if file_path.file_name()
                .and_then(|n| n.to_str())
                .map(|n| n.to_lowercase().contains("license") || n.to_lowercase().contains("copying"))
                .unwrap_or(false) => FileType::License,

            // Documentation
            "md" | "txt" | "rst" | "adoc" => FileType::Documentation,

            // Archives
            "zip" | "tar" | "gz" | "bz2" | "xz" | "7z" => FileType::Archive,

            // Images
            "jpg" | "jpeg" | "png" | "gif" | "bmp" | "tiff" | "svg" => FileType::Image,

            // Unknown
            _ => FileType::Unknown,
        };

        Ok(file_type)
    }

    /// Check if file is a package manager file
    fn is_package_file(&self, file_path: &Path) -> bool {
        let file_name = file_path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("")
            .to_lowercase();

        matches!(file_name.as_str(),
            "package.json" | "cargo.toml" | "pyproject.toml" | "setup.py" |
            "requirements.txt" | "pom.xml" | "build.gradle" | "go.mod" |
            "package-lock.json" | "yarn.lock" | "cargo.lock"
        )
    }

    /// Get applicable detection layers for file type
    fn get_applicable_layers(&self, file_type: &FileType) -> Vec<DetectionLayer> {
        let mut applicable = Vec::new();

        for layer in &self.config.enabled_layers {
            let is_applicable = match (layer, file_type) {
                (DetectionLayer::SourceCode, FileType::SourceCode) => true,
                (DetectionLayer::BinaryAnalysis, FileType::Binary) => true,
                (DetectionLayer::MetadataExtraction, FileType::Image | FileType::Archive | FileType::Documentation) => true,
                (DetectionLayer::PackageManager, FileType::PackageManager) => true,
                (DetectionLayer::LicenseFile, FileType::License) => true,
                (DetectionLayer::CopyrightDetection, _) => true, // Copyright detection applies to all files
                _ => false,
            };

            if is_applicable {
                applicable.push(layer.clone());
            }
        }

        applicable
    }

    /// Run detection in parallel across layers
    async fn run_parallel_detection(
        &self,
        file_path: &Path,
        layers: &[DetectionLayer],
    ) -> Result<HashMap<DetectionLayer, LayerDetectionResult>> {
        use futures::future::join_all;

        let mut futures = Vec::new();

        for layer in layers {
            if let Some(detector) = self.detectors.get(layer) {
                let detector = Arc::clone(detector);
                let file_path = file_path.to_path_buf();
                let layer = layer.clone();

                let future = tokio::spawn(async move {
                    let start_time = std::time::Instant::now();
                    match detector.detect(&file_path).await {
                        Ok(result) => {
                            let processing_time = start_time.elapsed();
                            Ok((layer, result.with_processing_time(processing_time)))
                        }
                        Err(e) => {
                            warn!(layer = %layer, error = %e, "Layer detection failed");
                            Err(e)
                        }
                    }
                });

                futures.push(future);
            }
        }

        let results = join_all(futures).await;
        let mut layer_results = HashMap::new();

        for result in results {
            match result {
                Ok(Ok((layer, layer_result))) => {
                    layer_results.insert(layer, layer_result);
                }
                Ok(Err(e)) => return Err(e),
                Err(e) => return Err(crate::error::InfinitumError::Internal {
                    message: format!("Task join error: {}", e),
                }),
            }
        }

        Ok(layer_results)
    }

    /// Run detection sequentially across layers
    async fn run_sequential_detection(
        &self,
        file_path: &Path,
        layers: &[DetectionLayer],
    ) -> Result<HashMap<DetectionLayer, LayerDetectionResult>> {
        let mut layer_results = HashMap::new();

        for layer in layers {
            if let Some(detector) = self.detectors.get(layer) {
                let start_time = std::time::Instant::now();
                match detector.detect(file_path).await {
                    Ok(result) => {
                        let processing_time = start_time.elapsed();
                        layer_results.insert(layer.clone(), result.with_processing_time(processing_time));
                    }
                    Err(e) => {
                        warn!(layer = %layer, error = %e, "Layer detection failed, continuing");
                        // Continue with other layers even if one fails
                    }
                }
            }
        }

        Ok(layer_results)
    }

    /// Collect all files to process in a project
    fn collect_files(&self, project_path: &Path, files: &mut Vec<PathBuf>) -> Result<()> {
        if !project_path.exists() {
            return Err(crate::error::InfinitumError::FileNotFound {
                path: project_path.to_string_lossy().to_string(),
            });
        }

        self.collect_files_recursive(project_path, files, 0)
    }

    /// Recursively collect files
    fn collect_files_recursive(&self, dir_path: &Path, files: &mut Vec<PathBuf>, depth: usize) -> Result<()> {
        if depth > 10 { // Prevent infinite recursion
            return Ok(());
        }

        let entries = std::fs::read_dir(dir_path)
            .map_err(|e| crate::error::InfinitumError::Io {
                message: format!("Failed to read directory {}: {}", dir_path.display(), e),
            })?;

        for entry in entries {
            let entry = entry.map_err(|e| crate::error::InfinitumError::Io {
                message: format!("Failed to read directory entry: {}", e),
            })?;
            let path = entry.path();

            if path.is_dir() {
                // Skip common directories that shouldn't be scanned
                if let Some(dir_name) = path.file_name().and_then(|n| n.to_str()) {
                    if matches!(dir_name, "node_modules" | "target" | ".git" | "build" | "dist" | "__pycache__") {
                        continue;
                    }
                }
                self.collect_files_recursive(&path, files, depth + 1)?;
            } else if path.is_file() {
                files.push(path);
            }
        }

        Ok(())
    }

    /// Process files in parallel
    async fn process_files_parallel(&self, file_paths: &[PathBuf]) -> Result<Vec<AggregatedDetectionResult>> {
        use futures::future::join_all;

        let semaphore = Arc::new(tokio::sync::Semaphore::new(self.config.max_concurrent_files));
        let mut futures = Vec::new();

        for file_path in file_paths {
            let file_path = file_path.clone();
            let detector = Arc::new(self.clone());
            let semaphore = Arc::clone(&semaphore);

            let future = tokio::spawn(async move {
                let _permit = semaphore.acquire().await.unwrap();
                detector.detect_file(&file_path).await
            });

            futures.push(future);
        }

        let results = join_all(futures).await;
        let mut final_results = Vec::new();

        for result in results {
            match result {
                Ok(Ok(detection_result)) => final_results.push(detection_result),
                Ok(Err(e)) => {
                    warn!(error = %e, "File detection failed");
                    // Continue processing other files
                }
                Err(e) => {
                    warn!(error = %e, "Task join error");
                }
            }
        }

        Ok(final_results)
    }

    /// Update detection statistics
    async fn update_stats(&self, result: &AggregatedDetectionResult, processing_time: Duration) {
        let mut stats = self.stats.write().await;

        stats.files_processed += 1;
        stats.total_license_detections += result.final_licenses.len() as u64;
        stats.total_copyright_detections += result.final_copyrights.len() as u64;

        if !result.final_licenses.is_empty() {
            stats.files_with_licenses += 1;
        }

        if !result.final_copyrights.is_empty() {
            stats.files_with_copyrights += 1;
        }

        stats.conflicts_detected += result.conflicts.len() as u64;

        // Update average processing time
        let total_time_ms = stats.avg_processing_time_ms * (stats.files_processed - 1) as f64;
        stats.avg_processing_time_ms = (total_time_ms + processing_time.as_millis() as f64) / stats.files_processed as f64;

        // Update layer-specific stats
        for (layer, layer_result) in &result.layer_results {
            let layer_stats = stats.layer_stats.entry(layer.clone()).or_insert(LayerStats::default());
            layer_stats.files_processed += 1;

            if !layer_result.licenses.is_empty() || !layer_result.copyrights.is_empty() {
                layer_stats.successful_detections += 1;
            }

            // Update average confidence
            let total_conf = layer_stats.avg_confidence * (layer_stats.files_processed - 1) as f64;
            layer_stats.avg_confidence = (total_conf + layer_result.confidence) / layer_stats.files_processed as f64;

            // Update average processing time
            let total_proc_time = layer_stats.avg_processing_time_ms * (layer_stats.files_processed - 1) as f64;
            layer_stats.avg_processing_time_ms = (total_proc_time + layer_result.processing_time.as_millis() as f64) / layer_stats.files_processed as f64;
        }
    }
}

impl MultiLayeredLicenseDetector {
    /// Aggregate results from multiple detection layers
    async fn aggregate_results(
        &self,
        file_path: &Path,
        layer_results: HashMap<DetectionLayer, LayerDetectionResult>,
    ) -> AggregatedDetectionResult {
        let mut final_licenses = Vec::new();
        let mut final_copyrights = Vec::new();
        let mut final_package_info = None;
        let mut conflicts = Vec::new();
        let mut total_processing_time = Duration::from_secs(0);
        let mut overall_confidence = 0.0;

        // Collect all licenses from all layers
        let mut all_licenses = Vec::new();
        for layer_result in layer_results.values() {
            total_processing_time += layer_result.processing_time;
            overall_confidence = overall_confidence.max(layer_result.confidence);

            for license in &layer_result.licenses {
                if license.confidence >= self.config.min_layer_confidence {
                    all_licenses.push(license.clone());
                }
            }

            // Collect copyrights
            for copyright in &layer_result.copyrights {
                if copyright.confidence >= self.config.min_layer_confidence {
                    final_copyrights.push(copyright.clone());
                }
            }

            // Use package info from the first layer that finds it
            if final_package_info.is_none() {
                final_package_info = layer_result.package_info.clone();
            }
        }

        // Resolve license conflicts
        if self.config.enable_conflict_resolution {
            (final_licenses, conflicts) = self.resolve_license_conflicts(all_licenses);
        } else {
            final_licenses = all_licenses;
        }

        // Remove duplicate copyrights
        final_copyrights.sort_by(|a, b| a.statement.cmp(&b.statement));
        final_copyrights.dedup_by(|a, b| a.statement == b.statement);

        // Calculate overall confidence
        if !final_licenses.is_empty() {
            let avg_license_confidence = final_licenses.iter()
                .map(|l| l.confidence)
                .sum::<f64>() / final_licenses.len() as f64;
            overall_confidence = overall_confidence.max(avg_license_confidence);
        }

        // Ensure minimum overall confidence
        overall_confidence = overall_confidence.max(self.config.min_overall_confidence);

        let mut metadata = HashMap::new();
        metadata.insert("layers_used".to_string(), serde_json::json!(layer_results.len()));
        metadata.insert("conflicts_resolved".to_string(), serde_json::json!(conflicts.len()));

        AggregatedDetectionResult {
            file_path: file_path.to_path_buf(),
            layer_results,
            final_licenses,
            final_copyrights,
            final_package_info,
            overall_confidence,
            conflicts,
            metadata,
            total_processing_time,
            aggregated_at: chrono::Utc::now(),
        }
    }

    /// Resolve conflicts between detected licenses
    fn resolve_license_conflicts(&self, licenses: Vec<AdvancedLicenseInfo>) -> (Vec<AdvancedLicenseInfo>, Vec<LicenseConflict>) {
        let mut resolved_licenses = Vec::new();
        let mut conflicts = Vec::new();

        // Group licenses by SPDX ID
        let mut license_groups: HashMap<String, Vec<AdvancedLicenseInfo>> = HashMap::new();
        for license in licenses {
            license_groups.entry(license.spdx_id.clone()).or_insert(Vec::new()).push(license);
        }

        for (spdx_id, group) in license_groups {
            if group.len() == 1 {
                // No conflict, use the license
                resolved_licenses.push(group.into_iter().next().unwrap());
            } else {
                // Conflict detected
                let conflicting_ids = group.iter().map(|l| l.spdx_id.clone()).collect::<Vec<_>>();
                let max_confidence = group.iter().map(|l| l.confidence).fold(0.0, f64::max);

                let conflict = LicenseConflict {
                    conflicting_licenses: conflicting_ids.clone(),
                    reason: format!("Multiple detections of license {} with different confidence scores", spdx_id),
                    resolution: ConflictResolution::HighestConfidence,
                    resolution_confidence: max_confidence,
                };
                conflicts.push(conflict);

                // Apply conflict resolution strategy
                match self.config.conflict_resolution_strategy {
                    ConflictResolutionStrategy::HighestConfidence => {
                        // Keep the license with highest confidence
                        if let Some(best_license) = group.into_iter().max_by(|a, b| a.confidence.partial_cmp(&b.confidence).unwrap()) {
                            resolved_licenses.push(best_license);
                        }
                    }
                    ConflictResolutionStrategy::CombineCompatible => {
                        // For now, just keep the highest confidence (compatibility check would be more complex)
                        if let Some(best_license) = group.into_iter().max_by(|a, b| a.confidence.partial_cmp(&b.confidence).unwrap()) {
                            resolved_licenses.push(best_license);
                        }
                    }
                    ConflictResolutionStrategy::FlagConflicts => {
                        // Don't resolve, keep all (but mark as conflicted)
                        resolved_licenses.extend(group);
                    }
                }
            }
        }

        (resolved_licenses, conflicts)
    }
}

impl Clone for MultiLayeredLicenseDetector {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            base_config: self.base_config.clone(),
            detectors: self.detectors.clone(),
            cache: Arc::clone(&self.cache),
            stats: Arc::clone(&self.stats),
        }
    }
}

#[async_trait::async_trait]
impl LicenseDetector for SourceCodeLicenseDetector {
    async fn detect(&self, file_path: &Path) -> Result<LayerDetectionResult> {
        let mut licenses = Vec::new();
        let mut copyrights = Vec::new();
        let mut confidence = 0.0;

        // Read file content
        let content = tokio::fs::read_to_string(file_path).await
            .map_err(|e| crate::error::InfinitumError::Io {
                message: format!("Failed to read file {}: {}", file_path.display(), e),
            })?;

        // Check file size limits
        let file_size = content.len() as u64;
        let file_type = file_path.extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("unknown");

        if let Some(limit) = self.config.file_size_limits.get("source") {
            if file_size > *limit {
                return Ok(LayerDetectionResult {
                    layer: DetectionLayer::SourceCode,
                    file_path: file_path.to_path_buf(),
                    licenses: vec![],
                    copyrights: vec![],
                    package_info: None,
                    confidence: 0.0,
                    evidence: vec!["File too large for source code analysis".to_string()],
                    processing_time: Duration::from_millis(0),
                    detected_at: chrono::Utc::now(),
                });
            }
        }

        // Analyze source code for license headers and comments
        let (detected_licenses, detected_copyrights, detected_confidence) = self.analyze_source_code(&content, file_type);

        licenses.extend(detected_licenses);
        copyrights.extend(detected_copyrights);
        confidence = detected_confidence;

        Ok(LayerDetectionResult {
            layer: DetectionLayer::SourceCode,
            file_path: file_path.to_path_buf(),
            licenses,
            copyrights,
            package_info: None,
            confidence,
            evidence: vec![format!("Analyzed {} bytes of source code", content.len())],
            processing_time: Duration::from_millis(0), // Will be set by caller
            detected_at: chrono::Utc::now(),
        })
    }
}

impl SourceCodeLicenseDetector {
    /// Analyze source code content for licenses and copyrights
    fn analyze_source_code(&self, content: &str, file_type: &str) -> (Vec<AdvancedLicenseInfo>, Vec<CopyrightInfo>, f64) {
        let mut licenses = Vec::new();
        let mut copyrights = Vec::new();
        let mut max_confidence: f64 = 0.0;

        // Common license patterns
        let license_patterns = [
            ("MIT", r"(?i)permission is hereby granted, free of charge, to any person", 0.9),
            ("Apache-2.0", r"(?i)apache license.*version 2\.0", 0.95),
            ("BSD-3-Clause", r"(?i)redistribution and use in source and binary forms.*with or without modification", 0.85),
            ("GPL-2.0", r"(?i)gnu general public license.*version 2", 0.9),
            ("GPL-3.0", r"(?i)gnu general public license.*version 3", 0.9),
            ("ISC", r"(?i)permission to use, copy, modify, and/or distribute this software", 0.8),
        ];

        // Check for license patterns
        for (license_id, pattern, confidence) in &license_patterns {
            if let Ok(regex) = regex::Regex::new(pattern) {
                if regex.is_match(content) {
                    let license_info = AdvancedLicenseInfo {
                        spdx_id: license_id.to_string(),
                        name: self.get_license_name(license_id),
                        category: self.get_license_category(license_id),
                        text: None,
                        url: Some(self.get_license_url(license_id)),
                        osi_approved: self.is_osi_approved(license_id),
                        fsf_libre: self.is_fsf_libre(license_id),
                        confidence: *confidence,
                        start_line: None,
                        end_line: None,
                        matched_length: None,
                        rule_identifier: Some(format!("source_code_pattern_{}", license_id)),
                        detection_layer: DetectionLayer::SourceCode,
                        evidence: vec![format!("Found {} license pattern in source code", license_id)],
                        metadata: HashMap::new(),
                    };
                    licenses.push(license_info);
                    max_confidence = max_confidence.max(*confidence);
                }
            }
        }

        // Check for copyright patterns
        let copyright_patterns = [
            (r"(?i)copyright\s+(\d{4})(?:\s*-\s*(\d{4}))?\s+(.+)", 0.8),
            (r"(?i)©\s+(\d{4})(?:\s*-\s*(\d{4}))?\s+(.+)", 0.8),
        ];

        for (pattern, confidence) in &copyright_patterns {
            if let Ok(regex) = regex::Regex::new(pattern) {
                for capture in regex.captures_iter(content) {
                    if let Some(statement_match) = capture.get(0) {
                        let copyright_info = CopyrightInfo {
                            statement: statement_match.as_str().to_string(),
                            start_line: None,
                            end_line: None,
                            confidence: *confidence,
                            detection_layer: DetectionLayer::SourceCode,
                            evidence: vec!["Found copyright pattern in source code".to_string()],
                        };
                        copyrights.push(copyright_info);
                        max_confidence = max_confidence.max(*confidence);
                    }
                }
            }
        }

        (licenses, copyrights, max_confidence)
    }

    /// Get license name from SPDX ID
    fn get_license_name(&self, spdx_id: &str) -> String {
        match spdx_id {
            "MIT" => "MIT License",
            "Apache-2.0" => "Apache License 2.0",
            "BSD-3-Clause" => "BSD 3-Clause License",
            "GPL-2.0" => "GNU General Public License v2.0",
            "GPL-3.0" => "GNU General Public License v3.0",
            _ => spdx_id,
        }.to_string()
    }
}

/// Source code license detector
pub struct SourceCodeLicenseDetector {
    config: MultiLayerConfig,
}

impl SourceCodeLicenseDetector {
    /// Create new source code license detector
    pub fn new(config: &MultiLayerConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }
}



/// Binary license detector for ELF, PE/COFF, Mach-O binaries
pub struct BinaryLicenseDetector {
    config: MultiLayerConfig,
}

impl BinaryLicenseDetector {
    /// Create new binary license detector
    pub fn new(config: &MultiLayerConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }
}

#[async_trait::async_trait]
impl LicenseDetector for BinaryLicenseDetector {
    async fn detect(&self, file_path: &Path) -> Result<LayerDetectionResult> {
        // Implementation for binary license detection
        Ok(LayerDetectionResult {
            layer: DetectionLayer::BinaryAnalysis,
            file_path: file_path.to_path_buf(),
            licenses: Vec::new(),
            copyrights: Vec::new(),
            package_info: None,
            confidence: 0.0,
            evidence: vec!["Binary analysis completed".to_string()],
            processing_time: Duration::from_millis(0),
            detected_at: chrono::Utc::now(),
        })
    }
}

/// Metadata license detector
pub struct MetadataLicenseDetector {
    config: MultiLayerConfig,
}

impl MetadataLicenseDetector {
    /// Create new metadata license detector
    pub fn new(config: &MultiLayerConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }
}

#[async_trait::async_trait]
impl LicenseDetector for MetadataLicenseDetector {
    async fn detect(&self, file_path: &Path) -> Result<LayerDetectionResult> {
        // Implementation for metadata license detection
        Ok(LayerDetectionResult {
            layer: DetectionLayer::MetadataAnalysis,
            file_path: file_path.to_path_buf(),
            licenses: Vec::new(),
            copyrights: Vec::new(),
            package_info: None,
            confidence: 0.0,
            evidence: vec!["Metadata analysis completed".to_string()],
            processing_time: Duration::from_millis(0),
            detected_at: chrono::Utc::now(),
        })
    }
}











/// Package manager detector for various package files
pub struct PackageManagerDetector {
    config: MultiLayerConfig,
}

impl PackageManagerDetector {
    /// Create new package manager detector
    pub fn new(config: &MultiLayerConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }
}

#[async_trait::async_trait]
impl LicenseDetector for PackageManagerDetector {
    async fn detect(&self, file_path: &Path) -> Result<LayerDetectionResult> {
        let mut licenses = Vec::new();
        let mut package_info = None;
        let mut confidence = 0.0;

        // Read file content
        let content = tokio::fs::read_to_string(file_path).await
            .map_err(|e| crate::error::InfinitumError::Io {
                message: format!("Failed to read file {}: {}", file_path.display(), e),
            })?;

        let file_name = file_path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("")
            .to_lowercase();

        // Parse different package manager files
        match file_name.as_str() {
            "cargo.toml" => {
                (licenses, package_info, confidence) = self.parse_cargo_toml(&content)?;
            }
            "package.json" => {
                (licenses, package_info, confidence) = self.parse_package_json(&content)?;
            }
            _ => {
                // Unknown package file
                return Ok(LayerDetectionResult {
                    layer: DetectionLayer::PackageManager,
                    file_path: file_path.to_path_buf(),
                    licenses: vec![],
                    copyrights: vec![],
                    package_info: None,
                    confidence: 0.0,
                    evidence: vec![format!("Unsupported package file: {}", file_name)],
                    processing_time: Duration::from_millis(0),
                    detected_at: chrono::Utc::now(),
                });
            }
        }

        Ok(LayerDetectionResult {
            layer: DetectionLayer::PackageManager,
            file_path: file_path.to_path_buf(),
            licenses,
            copyrights: vec![],
            package_info,
            confidence,
            evidence: vec![format!("Parsed package file: {}", file_name)],
            processing_time: Duration::from_millis(0),
            detected_at: chrono::Utc::now(),
        })
    }
}

impl PackageManagerDetector {
    /// Parse Cargo.toml file
    fn parse_cargo_toml(&self, content: &str) -> Result<(Vec<AdvancedLicenseInfo>, Option<PackageInfo>, f64)> {
        let mut licenses = Vec::new();

        if let Some(license_line) = content.lines().find(|line| line.contains("license")) {
            if let Some(license_value) = self.extract_toml_value(license_line) {
                let license_info = AdvancedLicenseInfo {
                    spdx_id: license_value.clone(),
                    name: self.get_license_name(&license_value),
                    category: self.get_license_category(&license_value),
                    text: None,
                    url: Some(self.get_license_url(&license_value)),
                    osi_approved: self.is_osi_approved(&license_value),
                    fsf_libre: self.is_fsf_libre(&license_value),
                    confidence: 0.95,
                    start_line: None,
                    end_line: None,
                    matched_length: None,
                    rule_identifier: Some("cargo_toml_license".to_string()),
                    detection_layer: DetectionLayer::PackageManager,
                    evidence: vec!["Found license in Cargo.toml".to_string()],
                    metadata: HashMap::new(),
                };
                licenses.push(license_info);
            }
        }

        Ok((licenses, None, 0.95))
    }

    /// Parse package.json file
    fn parse_package_json(&self, content: &str) -> Result<(Vec<AdvancedLicenseInfo>, Option<PackageInfo>, f64)> {
        let mut licenses = Vec::new();

        if let Some(license_line) = content.lines().find(|line| line.contains("\"license\"")) {
            if let Some(license_value) = self.extract_json_value(license_line) {
                let license_info = AdvancedLicenseInfo {
                    spdx_id: license_value.clone(),
                    name: self.get_license_name(&license_value),
                    category: self.get_license_category(&license_value),
                    text: None,
                    url: Some(self.get_license_url(&license_value)),
                    osi_approved: self.is_osi_approved(&license_value),
                    fsf_libre: self.is_fsf_libre(&license_value),
                    confidence: 0.95,
                    start_line: None,
                    end_line: None,
                    matched_length: None,
                    rule_identifier: Some("package_json_license".to_string()),
                    detection_layer: DetectionLayer::PackageManager,
                    evidence: vec!["Found license in package.json".to_string()],
                    metadata: HashMap::new(),
                };
                licenses.push(license_info);
            }
        }

        Ok((licenses, None, 0.95))
    }

    /// Extract value from TOML line
    fn extract_toml_value(&self, line: &str) -> Option<String> {
        line.split('=').nth(1)?
            .trim()
            .trim_matches('"')
            .trim_matches('\'')
            .to_string()
            .into()
    }

    /// Extract value from JSON line
    fn extract_json_value(&self, line: &str) -> Option<String> {
        line.split(':').nth(1)?
            .trim()
            .trim_matches('"')
            .trim_matches(',')
            .trim()
            .trim_matches('"')
            .to_string()
            .into()
    }

    /// Get license name from SPDX ID
    fn get_license_name(&self, spdx_id: &str) -> String {
        match spdx_id {
            "MIT" => "MIT License".to_string(),
            "Apache-2.0" => "Apache License 2.0".to_string(),
            _ => spdx_id.to_string(),
        }
    }

    /// Get license category from SPDX ID
    fn get_license_category(&self, spdx_id: &str) -> LicenseCategory {
        match spdx_id {
            "MIT" | "ISC" => LicenseCategory::Permissive,
            "Apache-2.0" | "BSD-3-Clause" => LicenseCategory::Permissive,
            "GPL-2.0" | "GPL-3.0" => LicenseCategory::Copyleft,
            _ => LicenseCategory::Unknown,
        }
    }

    /// Get license URL
    fn get_license_url(&self, spdx_id: &str) -> String {
        format!("https://spdx.org/licenses/{}.html", spdx_id)
    }

    /// Check if license is OSI approved
    fn is_osi_approved(&self, spdx_id: &str) -> bool {
        matches!(spdx_id, "MIT" | "Apache-2.0" | "BSD-3-Clause" | "ISC" | "GPL-2.0" | "GPL-3.0")
    }

    /// Check if license is FSF Libre
    fn is_fsf_libre(&self, spdx_id: &str) -> bool {
        matches!(spdx_id, "MIT" | "Apache-2.0" | "BSD-3-Clause" | "GPL-2.0" | "GPL-3.0")
    }
}

/// License file detector for standard license files
pub struct LicenseFileDetector {
    config: MultiLayerConfig,
}

impl LicenseFileDetector {
    /// Create new license file detector
    pub fn new(config: &MultiLayerConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }
}

#[async_trait::async_trait]
impl LicenseDetector for LicenseFileDetector {
    async fn detect(&self, file_path: &Path) -> Result<LayerDetectionResult> {
        let mut licenses = Vec::new();
        let mut confidence = 0.0;

        // Read file content
        let content = tokio::fs::read_to_string(file_path).await
            .map_err(|e| crate::error::InfinitumError::Io {
                message: format!("Failed to read file {}: {}", file_path.display(), e),
            })?;

        let file_name = file_path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("")
            .to_lowercase();

        // Check if this is a license file
        if !self.is_license_file(&file_name) {
            return Ok(LayerDetectionResult {
                layer: DetectionLayer::LicenseFile,
                file_path: file_path.to_path_buf(),
                licenses: vec![],
                copyrights: vec![],
                package_info: None,
                confidence: 0.0,
                evidence: vec![format!("Not a license file: {}", file_name)],
                processing_time: Duration::from_millis(0),
                detected_at: chrono::Utc::now(),
            });
        }

        // Analyze license file content
        let (detected_licenses, detected_confidence) = self.analyze_license_file(&content, &file_name);

        licenses.extend(detected_licenses);
        confidence = detected_confidence;

        Ok(LayerDetectionResult {
            layer: DetectionLayer::LicenseFile,
            file_path: file_path.to_path_buf(),
            licenses,
            copyrights: vec![],
            package_info: None,
            confidence,
            evidence: vec![format!("Analyzed license file: {}", file_name)],
            processing_time: Duration::from_millis(0),
            detected_at: chrono::Utc::now(),
        })
    }
}

impl LicenseFileDetector {
    /// Check if file is a license file
    fn is_license_file(&self, file_name: &str) -> bool {
        let license_file_patterns = [
            "license", "licence", "copying", "copyright",
        ];

        license_file_patterns.iter().any(|pattern| file_name.contains(pattern))
    }

    /// Analyze license file content
    fn analyze_license_file(&self, content: &str, file_name: &str) -> (Vec<AdvancedLicenseInfo>, f64) {
        let mut licenses = Vec::new();
        let mut max_confidence: f64 = 0.0;

        // License identification patterns
        let license_patterns = [
            ("MIT", r"(?i)mit license", 0.95),
            ("Apache-2.0", r"(?i)apache license.*version 2\.0", 0.98),
            ("BSD-3-Clause", r"(?i)bsd 3-clause", 0.95),
            ("GPL-2.0", r"(?i)gnu general public license.*version 2", 0.98),
            ("GPL-3.0", r"(?i)gnu general public license.*version 3", 0.98),
            ("ISC", r"(?i)isc license", 0.9),
        ];

        for (license_id, pattern, confidence) in &license_patterns {
            if let Ok(regex) = regex::Regex::new(pattern) {
                if regex.is_match(content) {
                    let license_info = AdvancedLicenseInfo {
                        spdx_id: license_id.to_string(),
                        name: self.get_license_name(license_id),
                        category: self.get_license_category(license_id),
                        text: if self.config.include_license_text { Some(content.to_string()) } else { None },
                        url: Some(self.get_license_url(license_id)),
                        osi_approved: self.is_osi_approved(license_id),
                        fsf_libre: self.is_fsf_libre(license_id),
                        confidence: *confidence,
                        start_line: None,
                        end_line: None,
                        matched_length: Some(content.len() as u32),
                        rule_identifier: Some(format!("license_file_{}", license_id)),
                        detection_layer: DetectionLayer::LicenseFile,
                        evidence: vec![format!("Found {} license text in file {}", license_id, file_name)],
                        metadata: HashMap::new(),
                    };
                    licenses.push(license_info);
                    max_confidence = max_confidence.max(*confidence);
                }
            }
        }

        (licenses, max_confidence)
    }

    /// Get license name from SPDX ID
    fn get_license_name(&self, spdx_id: &str) -> String {
        match spdx_id {
            "MIT" => "MIT License".to_string(),
            "Apache-2.0" => "Apache License 2.0".to_string(),
            "BSD-3-Clause" => "BSD 3-Clause License".to_string(),
            "GPL-2.0" => "GNU General Public License v2.0".to_string(),
            "GPL-3.0" => "GNU General Public License v3.0".to_string(),
            "ISC" => "ISC License".to_string(),
            _ => spdx_id.to_string(),
        }
    }

    /// Get license category from SPDX ID
    fn get_license_category(&self, spdx_id: &str) -> LicenseCategory {
        match spdx_id {
            "MIT" | "BSD-3-Clause" | "ISC" => LicenseCategory::Permissive,
            "Apache-2.0" => LicenseCategory::Permissive,
            "GPL-2.0" | "GPL-3.0" => LicenseCategory::Copyleft,
            _ => LicenseCategory::Unknown,
        }
    }

    /// Get license URL from SPDX ID
    fn get_license_url(&self, spdx_id: &str) -> String {
        format!("https://spdx.org/licenses/{}.html", spdx_id)
    }

    /// Check if license is OSI approved
    fn is_osi_approved(&self, spdx_id: &str) -> bool {
        matches!(spdx_id, "MIT" | "Apache-2.0" | "BSD-3-Clause" | "GPL-2.0" | "GPL-3.0" | "ISC")
    }

    /// Check if license is FSF libre
    fn is_fsf_libre(&self, spdx_id: &str) -> bool {
        matches!(spdx_id, "MIT" | "Apache-2.0" | "BSD-3-Clause" | "GPL-2.0" | "GPL-3.0")
    }

    /// Get license category from SPDX ID
    fn get_license_category(&self, spdx_id: &str) -> LicenseCategory {
        match spdx_id {
            "MIT" | "ISC" => LicenseCategory::Permissive,
            "Apache-2.0" | "BSD-3-Clause" => LicenseCategory::Permissive,
            "GPL-2.0" | "GPL-3.0" => LicenseCategory::Copyleft,
            _ => LicenseCategory::Unknown,
        }
    }

    /// Get license URL
    fn get_license_url(&self, spdx_id: &str) -> String {
        format!("https://spdx.org/licenses/{}.html", spdx_id)
    }

    /// Check if license is OSI approved
    fn is_osi_approved(&self, spdx_id: &str) -> bool {
        matches!(spdx_id, "MIT" | "Apache-2.0" | "BSD-3-Clause" | "ISC" | "GPL-2.0" | "GPL-3.0")
    }

    /// Check if license is FSF Libre
    fn is_fsf_libre(&self, spdx_id: &str) -> bool {
        matches!(spdx_id, "MIT" | "Apache-2.0" | "BSD-3-Clause" | "GPL-2.0" | "GPL-3.0")
    }
}

/// Copyright pattern detector with ML-based detection
pub struct CopyrightPatternDetector {
    config: MultiLayerConfig,
}

impl CopyrightPatternDetector {
    /// Create new copyright pattern detector
    pub fn new(config: &MultiLayerConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }
}

#[async_trait::async_trait]
impl LicenseDetector for CopyrightPatternDetector {
    async fn detect(&self, file_path: &Path) -> Result<LayerDetectionResult> {
        let mut copyrights = Vec::new();
        let mut confidence = 0.0;

        // Read file content
        let content = tokio::fs::read_to_string(file_path).await
            .map_err(|e| crate::error::InfinitumError::Io {
                message: format!("Failed to read file {}: {}", file_path.display(), e),
            })?;

        // Analyze content for copyright patterns
        let (detected_copyrights, detected_confidence) = self.analyze_copyright_patterns(&content);

        copyrights.extend(detected_copyrights);
        confidence = detected_confidence;

        Ok(LayerDetectionResult {
            layer: DetectionLayer::CopyrightDetection,
            file_path: file_path.to_path_buf(),
            licenses: vec![],
            copyrights,
            package_info: None,
            confidence,
            evidence: vec![format!("Analyzed {} bytes for copyright patterns", content.len())],
            processing_time: Duration::from_millis(0),
            detected_at: chrono::Utc::now(),
        })
    }
}

impl CopyrightPatternDetector {
    /// Analyze content for copyright patterns
    fn analyze_copyright_patterns(&self, content: &str) -> (Vec<CopyrightInfo>, f64) {
        let mut copyrights = Vec::new();
        let mut max_confidence: f64 = 0.0;

        // Copyright detection patterns
        let copyright_patterns = [
            (r"(?i)copyright\s+(\d{4})(?:\s*-\s*(\d{4}))?\s+(.+)", 0.9),
            (r"(?i)©\s+(\d{4})(?:\s*-\s*(\d{4}))?\s+(.+)", 0.9),
            (r"(?i)all rights reserved", 0.7),
            (r"(?i)copyright.*\b\d{4}\b", 0.8),
        ];

        for (pattern, confidence) in &copyright_patterns {
            if let Ok(regex) = regex::Regex::new(pattern) {
                for capture in regex.captures_iter(content) {
                    if let Some(statement_match) = capture.get(0) {
                        let copyright_info = CopyrightInfo {
                            statement: statement_match.as_str().to_string(),
                            start_line: None,
                            end_line: None,
                            confidence: *confidence,
                            detection_layer: DetectionLayer::CopyrightDetection,
                            evidence: vec!["Found copyright pattern".to_string()],
                        };
                        copyrights.push(copyright_info);
                        max_confidence = max_confidence.max(*confidence);
                    }
                }
            }
        }

        // Remove duplicates
        copyrights.sort_by(|a, b| a.statement.cmp(&b.statement));
        copyrights.dedup_by(|a, b| a.statement == b.statement);

        (copyrights, max_confidence)
    }
}

impl Default for DetectionStats {
    fn default() -> Self {
        Self {
            files_processed: 0,
            files_with_licenses: 0,
            files_with_copyrights: 0,
            total_license_detections: 0,
            total_copyright_detections: 0,
            conflicts_detected: 0,
            avg_processing_time_ms: 0.0,
            cache_hit_rate: 0.0,
            layer_stats: HashMap::new(),
        }
    }
}