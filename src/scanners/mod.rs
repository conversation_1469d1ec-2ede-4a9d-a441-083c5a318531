//! # Scanners Module
//!
//! This module contains all scanning functionality for the Infinitium Signal platform.
//! It provides SBOM (Software Bill of Materials), HBOM (Hardware Bill of Materials),
//! repository analysis, and dependency resolution capabilities.

pub mod dependency_resolver;
pub mod hbom_scanner;
pub mod repo_analyzer;
pub mod sbom_scanner;
pub mod license_detector;
pub mod scancode_scanner;
pub mod osv_scanner;

use crate::{config::ScanningConfig, error::Result};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, path::Path, time::Duration};
use uuid::Uuid;

/// Re-export scanner types
pub use dependency_resolver::{DependencyResolver, DependencyTree};
pub use hbom_scanner::{HardwareComponent, HbomScanner};
pub use repo_analyzer::{RepoAnalyzer, RepositoryInfo};
pub use license_detector::{
    AggregatedDetectionResult, AdvancedLicenseInfo, CopyrightInfo, DetectionLayer, FileType,
    LayerDetectionResult, LicenseCategory, LicenseConflict, MultiLayerConfig,
    MultiLayeredLicenseDetector, PackageInfo,
};
pub use sbom_scanner::{ComponentScope, SbomScanner, SoftwareComponent};
pub use scancode_scanner::{
    DetectionMethod, LicenseDetectionResult,
    LicenseScannerConfig, ScanCodeConfig, ScanCodeIntegrationService, ScanCodeOutputFormat,
    ScanCodeSummary,
};
pub use osv_scanner::{
    OsvScanner, OsvScannerImpl, OsvScanResult, OsvVulnerability, OsvScannerConfig,
};

/// Scan types supported by the platform
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ScanType {
    /// Software Bill of Materials
    Sbom,
    /// Hardware Bill of Materials
    Hbom,
    /// Repository analysis
    Repository,
    /// Dependency analysis
    Dependencies,
    /// License analysis using ScanCode
    License,
    /// Vulnerability analysis using OSV
    Osv,
    /// Combined scan (all types)
    Combined,
}

impl std::fmt::Display for ScanType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ScanType::Sbom => write!(f, "sbom"),
            ScanType::Hbom => write!(f, "hbom"),
            ScanType::Repository => write!(f, "repository"),
            ScanType::Dependencies => write!(f, "dependencies"),
            ScanType::License => write!(f, "license"),
            ScanType::Osv => write!(f, "osv"),
            ScanType::Combined => write!(f, "combined"),
        }
    }
}

/// Scan request configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScanRequest {
    /// Unique scan identifier
    pub id: Uuid,
    /// Type of scan to perform
    pub scan_type: ScanType,
    /// Target path or URL to scan
    pub target: String,
    /// Scan options
    pub options: ScanOptions,
    /// Metadata for the scan
    pub metadata: HashMap<String, String>,
}

/// Scan configuration options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScanOptions {
    /// Include development dependencies
    pub include_dev_dependencies: bool,
    /// Maximum scan depth for dependency resolution
    pub max_depth: u32,
    /// Scan timeout in seconds
    pub timeout: u64,
    /// Include transitive dependencies
    pub include_transitive: bool,
    /// Exclude patterns (glob patterns)
    pub exclude_patterns: Vec<String>,
    /// Include patterns (glob patterns)
    pub include_patterns: Vec<String>,
    /// Output format preferences
    pub output_formats: Vec<OutputFormat>,
    /// Enable vulnerability scanning
    pub enable_vulnerability_scan: bool,
    /// Enable license scanning
    pub enable_license_scan: bool,
}

/// Output formats for scan results
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum OutputFormat {
    /// CycloneDX format
    CycloneDx,
    /// SPDX format
    Spdx,
    /// JSON format
    Json,
    /// XML format
    Xml,
    /// CSV format
    Csv,
    /// YAML format
    Yaml,
}

/// Scan result containing all discovered information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScanResult {
    /// Scan request that generated this result
    pub request: ScanRequest,
    /// Scan status
    pub status: ScanStatus,
    /// Start time
    pub started_at: chrono::DateTime<chrono::Utc>,
    /// End time
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
    /// Scan duration
    pub duration: Option<Duration>,
    /// Software components found
    pub software_components: Vec<SoftwareComponent>,
    /// Hardware components found
    pub hardware_components: Vec<HardwareComponent>,
    /// Repository information
    pub repository_info: Option<RepositoryInfo>,
    /// Dependency tree
    pub dependency_tree: Option<DependencyTree>,
    /// Vulnerabilities found
    pub vulnerabilities: Vec<VulnerabilityInfo>,
    /// License information
    pub licenses: Vec<LicenseInfo>,
    /// Advanced license detection results from ScanCode
    pub license_detections: Vec<LicenseDetectionResult>,
    /// OSV vulnerability scan results
    pub osv_scan_result: Option<OsvScanResult>,
    /// Scan errors and warnings
    pub issues: Vec<ScanIssue>,
    /// Additional metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Scan execution status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ScanStatus {
    /// Scan is queued
    Queued,
    /// Scan is in progress
    InProgress,
    /// Scan completed successfully
    Completed,
    /// Scan failed
    Failed,
    /// Scan was cancelled
    Cancelled,
    /// Scan timed out
    TimedOut,
}

/// Vulnerability information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VulnerabilityInfo {
    /// CVE identifier
    pub cve_id: String,
    /// Vulnerability severity
    pub severity: String,
    /// CVSS score
    pub cvss_score: Option<f64>,
    /// Description
    pub description: String,
    /// Affected component
    pub component: String,
    /// Fixed version (if available)
    pub fixed_version: Option<String>,
    /// References
    pub references: Vec<String>,
}

/// License information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LicenseInfo {
    /// License identifier (SPDX)
    pub id: String,
    /// License name
    pub name: String,
    /// License text URL
    pub url: Option<String>,
    /// Component using this license
    pub component: String,
    /// License compatibility
    pub compatibility: LicenseCompatibility,
}

/// License compatibility levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum LicenseCompatibility {
    /// Compatible with commercial use
    Commercial,
    /// Open source compatible
    OpenSource,
    /// Copyleft license
    Copyleft,
    /// Proprietary license
    Proprietary,
    /// Unknown compatibility
    Unknown,
}

/// Scan issues (errors, warnings, info)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScanIssue {
    /// Issue severity
    pub severity: IssueSeverity,
    /// Issue message
    pub message: String,
    /// File or component where issue occurred
    pub location: Option<String>,
    /// Issue code for categorization
    pub code: Option<String>,
}

/// Issue severity levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum IssueSeverity {
    /// Informational message
    Info,
    /// Warning message
    Warning,
    /// Error message
    Error,
    /// Critical error
    Critical,
}

/// Main scanner orchestrator
pub struct ScannerOrchestrator {
    dependency_resolver: DependencyResolver,
    scancode_scanner: ScanCodeIntegrationService,
    multi_layer_detector: MultiLayeredLicenseDetector,
    #[allow(dead_code)]
    config: ScanningConfig,
    sbom_scanner: SbomScanner,
    hbom_scanner: HbomScanner,
    repo_analyzer: RepoAnalyzer,
    osv_scanner: OsvScannerImpl,
}

impl ScannerOrchestrator {
    /// Create new scanner orchestrator
    pub fn new(config: ScanningConfig) -> Self {
        Self {
            dependency_resolver: DependencyResolver::new(&config),
            scancode_scanner: ScanCodeIntegrationService::new(&config, config.license_scanner.clone()),
            multi_layer_detector: MultiLayeredLicenseDetector::new(&config, MultiLayerConfig::default()),
            sbom_scanner: SbomScanner::new(&config),
            hbom_scanner: HbomScanner::new(&config),
            repo_analyzer: RepoAnalyzer::new(&config),
            osv_scanner: OsvScannerImpl::new(&config),
            config,
        }
    }

    /// Execute a scan request
    pub async fn execute_scan(&self, request: ScanRequest) -> Result<ScanResult> {
        let start_time = chrono::Utc::now();

        let mut result = ScanResult {
            request: request.clone(),
            status: ScanStatus::InProgress,
            started_at: start_time,
            completed_at: None,
            duration: None,
            software_components: Vec::new(),
            hardware_components: Vec::new(),
            repository_info: None,
            dependency_tree: None,
            vulnerabilities: Vec::new(),
            licenses: Vec::new(),
            license_detections: Vec::new(),
            osv_scan_result: None,
            issues: Vec::new(),
            metadata: HashMap::new(),
        };

        // Execute scan based on type
        let scan_result = match request.scan_type {
            ScanType::Sbom => self.execute_sbom_scan(&request, &mut result).await,
            ScanType::Hbom => self.execute_hbom_scan(&request, &mut result).await,
            ScanType::Repository => self.execute_repo_scan(&request, &mut result).await,
            ScanType::Dependencies => self.execute_dependency_scan(&request, &mut result).await,
            ScanType::License => self.execute_license_scan(&request, &mut result).await,
            ScanType::Osv => self.execute_osv_scan(&request, &mut result).await,
            ScanType::Combined => self.execute_combined_scan(&request, &mut result).await,
        };

        // Handle scan result
        match scan_result {
            Ok(_) => {
                let end_time = chrono::Utc::now();
                result.completed_at = Some(end_time);
                result.duration = Some(Duration::from_millis(
                    (end_time - start_time).num_milliseconds() as u64,
                ));
                result.status = ScanStatus::Completed;
                Ok(result)
            }
            Err(e) => {
                result.status = ScanStatus::Failed;
                result.issues.push(ScanIssue {
                    severity: IssueSeverity::Error,
                    message: format!("Scan failed: {}", e),
                    location: Some(request.target.clone()),
                    code: Some("SCAN_FAILED".to_string()),
                });
                Err(e)
            }
        }
    }

    /// Execute SBOM scan
    async fn execute_sbom_scan(
        &self,
        request: &ScanRequest,
        result: &mut ScanResult,
    ) -> Result<()> {
        let target_path = Path::new(&request.target);
        let components = self.sbom_scanner.scan_project(target_path).await?;
        result.software_components = components;
        Ok(())
    }

    /// Execute HBOM scan
    async fn execute_hbom_scan(
        &self,
        request: &ScanRequest,
        result: &mut ScanResult,
    ) -> Result<()> {
        let target_path = Path::new(&request.target);
        let components = self.hbom_scanner.scan_hardware(target_path).await?;
        result.hardware_components = components;
        Ok(())
    }

    /// Execute repository scan
    async fn execute_repo_scan(
        &self,
        request: &ScanRequest,
        result: &mut ScanResult,
    ) -> Result<()> {
        let target_path = Path::new(&request.target);
        let repo_info = self.repo_analyzer.analyze_repository(target_path).await?;
        result.repository_info = Some(repo_info);
        Ok(())
    }

    /// Execute dependency scan
    async fn execute_dependency_scan(
        &self,
        request: &ScanRequest,
        result: &mut ScanResult,
    ) -> Result<()> {
        let target_path = Path::new(&request.target);
        let dependency_tree = self
            .dependency_resolver
            .resolve_dependencies(target_path)
            .await?;
        result.dependency_tree = Some(dependency_tree);
        Ok(())
    }

    /// Execute license scan using multi-layered detection
    async fn execute_license_scan(
        &self,
        request: &ScanRequest,
        result: &mut ScanResult,
    ) -> Result<()> {
        let target_path = Path::new(&request.target);

        // Use multi-layered detector for comprehensive license detection
        let detection_results = self.multi_layer_detector.detect_project(target_path).await?;

        // Convert results to the existing format for compatibility
        for detection_result in detection_results {
            // Add licenses
            for license in detection_result.final_licenses {
                result.licenses.push(LicenseInfo {
                    id: license.spdx_id,
                    name: license.name,
                    url: license.url,
                    component: detection_result.file_path.display().to_string(),
                    compatibility: match license.category {
                        LicenseCategory::Permissive => LicenseCompatibility::Commercial,
                        LicenseCategory::Copyleft => LicenseCompatibility::Copyleft,
                        LicenseCategory::WeakCopyleft => LicenseCompatibility::Copyleft,
                        LicenseCategory::Proprietary => LicenseCompatibility::Proprietary,
                        _ => LicenseCompatibility::Unknown,
                    },
                });
            }

            // Add license detections (keep existing ScanCode format for compatibility)
            for layer_result in detection_result.layer_results.values() {
                for license in &layer_result.licenses {
                    result.license_detections.push(LicenseDetectionResult {
                        file_path: layer_result.file_path.clone(),
                        licenses: vec![AdvancedLicenseInfo {
                            spdx_id: license.spdx_id.clone(),
                            name: license.name.clone(),
                            category: license.category,
                            text: license.text.clone(),
                            url: license.url.clone(),
                            osi_approved: license.osi_approved,
                            fsf_libre: license.fsf_libre,
                            confidence: license.confidence,
                            start_line: license.start_line,
                            end_line: license.end_line,
                            matched_length: license.matched_length,
                            rule_identifier: license.rule_identifier.clone(),
                            metadata: license.metadata.clone(),
                        }],
                        copyrights: layer_result.copyrights.clone(),
                        package_info: layer_result.package_info.clone(),
                        scanned_at: layer_result.detected_at,
                        detection_method: match layer_result.layer {
                            DetectionLayer::SourceCode => DetectionMethod::RuleBased,
                            DetectionLayer::BinaryAnalysis => DetectionMethod::TextMining,
                            DetectionLayer::MetadataExtraction => DetectionMethod::TextMining,
                            DetectionLayer::PackageManager => DetectionMethod::RuleBased,
                            DetectionLayer::LicenseFile => DetectionMethod::SpdxMatching,
                            DetectionLayer::CopyrightDetection => DetectionMethod::TextMining,
                        },
                        overall_confidence: layer_result.confidence,
                    });
                }
            }
        }

        Ok(())
    }

    /// Execute license scan
    async fn execute_license_scan(
        &self,
        request: &ScanRequest,
        result: &mut ScanResult,
    ) -> Result<()> {
        let target_path = Path::new(&request.target);
        let license_detections = self.scancode_scanner.scan_project(target_path).await?;
        result.license_detections = license_detections;
        Ok(())
    }

    /// Execute OSV vulnerability scan
    async fn execute_osv_scan(
        &self,
        request: &ScanRequest,
        result: &mut ScanResult,
    ) -> Result<()> {
        let target_path = Path::new(&request.target);

        // First, scan for software components if not already done
        if result.software_components.is_empty() {
            let components = self.sbom_scanner.scan_project(target_path).await?;
            result.software_components = components;
        }

        // Scan components for vulnerabilities
        let osv_result = self.osv_scanner.scan_components(&result.software_components).await?;
        result.osv_scan_result = Some(osv_result);

        Ok(())
    }

    /// Execute combined scan (all scan types)
    async fn execute_combined_scan(
        &self,
        request: &ScanRequest,
        result: &mut ScanResult,
    ) -> Result<()> {
        // Execute all scan types
        self.execute_sbom_scan(request, result).await?;
        self.execute_hbom_scan(request, result).await?;
        self.execute_repo_scan(request, result).await?;
        self.execute_dependency_scan(request, result).await?;
        self.execute_license_scan(request, result).await?;
        self.execute_osv_scan(request, result).await?;
        Ok(())
    }
}

impl Default for ScanOptions {
    fn default() -> Self {
        Self {
            include_dev_dependencies: false,
            max_depth: 10,
            timeout: 300,
            include_transitive: true,
            exclude_patterns: vec![
                "**/node_modules/**".to_string(),
                "**/target/**".to_string(),
                "**/.git/**".to_string(),
            ],
            include_patterns: vec!["**/*".to_string()],
            output_formats: vec![OutputFormat::Json, OutputFormat::CycloneDx],
            enable_vulnerability_scan: true,
            enable_license_scan: true,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_scan_type_display() {
        assert_eq!(ScanType::Sbom.to_string(), "sbom");
        assert_eq!(ScanType::Hbom.to_string(), "hbom");
        assert_eq!(ScanType::Repository.to_string(), "repository");
        assert_eq!(ScanType::Dependencies.to_string(), "dependencies");
        assert_eq!(ScanType::License.to_string(), "license");
        assert_eq!(ScanType::Osv.to_string(), "osv");
        assert_eq!(ScanType::Combined.to_string(), "combined");
    }

    #[test]
    fn test_scan_options_default() {
        let options = ScanOptions::default();
        assert!(!options.include_dev_dependencies);
        assert_eq!(options.max_depth, 10);
        assert_eq!(options.timeout, 300);
        assert!(options.include_transitive);
        assert!(options.enable_vulnerability_scan);
        assert!(options.enable_license_scan);
    }

    #[test]
    fn test_scan_request_creation() {
        let request = ScanRequest {
            id: Uuid::new_v4(),
            scan_type: ScanType::Sbom,
            target: "/path/to/project".to_string(),
            options: ScanOptions::default(),
            metadata: HashMap::new(),
        };

        assert_eq!(request.scan_type, ScanType::Sbom);
        assert_eq!(request.target, "/path/to/project");
    }
}
