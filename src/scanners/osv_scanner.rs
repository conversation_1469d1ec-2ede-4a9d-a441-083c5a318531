//! OSV (Open Source Vulnerabilities) Scanner Module
//!
//! This module provides vulnerability scanning capabilities using the OSV database.
//! It integrates with the OSV API to query vulnerabilities for software components.

use crate::{
    config::{OsvConfig, ScanningConfig},
    error::Result,
    logging::audit::{get_audit_logger, AuditEventType, AuditSeverity},
    observability::circuit_breaker::{CircuitBreaker, CircuitBreakerConfig, CircuitBreakerError},
    scanners::SoftwareComponent,
    utils::cache::{ConcurrentCache, CacheConfig},
};
use async_trait::async_trait;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::{collections::{HashMap, HashSet}, sync::Arc, time::Duration};
use tokio::sync::RwLock;
use tracing::{debug, error, info, instrument, warn};
use uuid::Uuid;

/// Performance optimization configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PerformanceConfig {
    /// Enable incremental scanning
    pub enable_incremental_scanning: bool,
    /// Enable parallel processing
    pub enable_parallel_processing: bool,
    /// Maximum concurrent requests for parallel processing
    pub max_parallel_requests: usize,
    /// Memory limit in MB
    pub memory_limit_mb: usize,
    /// Rate limit (requests per second)
    pub rate_limit_per_second: usize,
    /// Enable connection pooling
    pub enable_connection_pooling: bool,
    /// Connection pool size
    pub connection_pool_size: usize,
    /// Request timeout for performance
    pub request_timeout_seconds: u64,
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            enable_incremental_scanning: true,
            enable_parallel_processing: true,
            max_parallel_requests: 10,
            memory_limit_mb: 512,
            rate_limit_per_second: 50,
            enable_connection_pooling: true,
            connection_pool_size: 20,
            request_timeout_seconds: 10,
        }
    }
}

/// Rate limiter for API calls
pub struct RateLimiter {
    /// Tokens available
    tokens: std::sync::atomic::AtomicU64,
    /// Maximum tokens
    max_tokens: u64,
    /// Refill rate per second
    refill_rate: u64,
    /// Last refill time
    last_refill: std::sync::Mutex<std::time::Instant>,
}

impl RateLimiter {
    /// Create new rate limiter
    pub fn new(max_tokens: u64, refill_rate: u64) -> Self {
        Self {
            tokens: std::sync::atomic::AtomicU64::new(max_tokens),
            max_tokens,
            refill_rate,
            last_refill: std::sync::Mutex::new(std::time::Instant::now()),
        }
    }

    /// Acquire a token (blocking)
    pub async fn acquire(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        loop {
            // Refill tokens
            {
                let mut last_refill = self.last_refill.lock().unwrap();
                let now = std::time::Instant::now();
                let elapsed = now.duration_since(*last_refill);
                let tokens_to_add = (elapsed.as_secs() * self.refill_rate).min(self.max_tokens);

                if tokens_to_add > 0 {
                    let current = self.tokens.load(std::sync::atomic::Ordering::Relaxed);
                    let new_tokens = (current + tokens_to_add).min(self.max_tokens);
                    self.tokens.store(new_tokens, std::sync::atomic::Ordering::Relaxed);
                    *last_refill = now;
                }
            }

            // Try to acquire token
            let current = self.tokens.load(std::sync::atomic::Ordering::Relaxed);
            if current > 0 {
                let new_value = current - 1;
                if self.tokens.compare_exchange(current, new_value, std::sync::atomic::Ordering::AcqRel, std::sync::atomic::Ordering::Relaxed).is_ok() {
                    return Ok(());
                }
            }

            // Wait before retrying
            tokio::time::sleep(std::time::Duration::from_millis(10)).await;
        }
    }
}

use semver::Version;

/// False positive filters configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FalsePositiveFilters {
    /// Enable false positive filtering
    pub enabled: bool,
    /// Allowlisted packages (package:version patterns)
    pub allowlist: HashSet<String>,
    /// Minimum severity threshold (low, medium, high, critical)
    pub min_severity: String,
    /// Maximum CVSS score threshold (0.0-10.0)
    pub max_cvss_score: Option<f64>,
    /// Enable version validation
    pub enable_version_validation: bool,
    /// Skip vulnerabilities without fixed versions
    pub skip_unfixed_vulnerabilities: bool,
    /// Skip vulnerabilities older than specified days
    pub skip_vulnerabilities_older_than_days: Option<u64>,
}

impl Default for FalsePositiveFilters {
    fn default() -> Self {
        Self {
            enabled: true,
            allowlist: HashSet::new(),
            min_severity: "low".to_string(),
            max_cvss_score: None,
            enable_version_validation: true,
            skip_unfixed_vulnerabilities: false,
            skip_vulnerabilities_older_than_days: None,
        }
    }
}

/// OSV Scanner trait
#[async_trait]
pub trait OsvScanner: Send + Sync {
    /// Scan components for vulnerabilities
    async fn scan_components(&self, components: &[SoftwareComponent]) -> Result<OsvScanResult>;

    /// Query vulnerabilities for a specific package
    async fn query_package_vulnerabilities(
        &self,
        package_name: &str,
        ecosystem: &str,
        version: Option<&str>,
    ) -> Result<Vec<OsvVulnerability>>;

    /// Get scanner health status
    async fn health_check(&self) -> Result<bool>;
}

/// OSV Scanner configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OsvScannerConfig {
    /// API base URL
    pub api_url: String,
    /// Request timeout in seconds
    pub timeout_seconds: u64,
    /// Maximum concurrent requests
    pub max_concurrent_requests: usize,
    /// Cache TTL in seconds
    pub cache_ttl_seconds: u64,
    /// Circuit breaker configuration
    pub circuit_breaker: CircuitBreakerConfig,
    /// Enable caching
    pub enable_caching: bool,
    /// Batch size for API requests
    pub batch_size: usize,
    /// False positive filters
    pub false_positive_filters: FalsePositiveFilters,
    /// Performance configuration
    pub performance_config: PerformanceConfig,
}

impl Default for OsvScannerConfig {
    fn default() -> Self {
        Self {
            api_url: "https://api.osv.dev".to_string(),
            timeout_seconds: 30,
            max_concurrent_requests: 10,
            cache_ttl_seconds: 3600, // 1 hour
            circuit_breaker: CircuitBreakerConfig {
                service_name: "osv_api".to_string(),
                failure_threshold: 5,
                recovery_timeout_seconds: 60,
                success_threshold: 3,
                timeout_seconds: 30,
                monitoring_window_seconds: 300,
                enable_auto_recovery: true,
                max_concurrent_requests: 10,
            },
            enable_caching: true,
            batch_size: 50,
            false_positive_filters: FalsePositiveFilters::default(),
            performance_config: PerformanceConfig::default(),
        }
    }
}

/// OSV scan result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OsvScanResult {
    /// Scan timestamp
    pub scanned_at: chrono::DateTime<chrono::Utc>,
    /// Components scanned
    pub components_scanned: usize,
    /// Vulnerabilities found
    pub vulnerabilities: Vec<OsvVulnerability>,
    /// Scan duration
    pub scan_duration: Duration,
    /// API calls made
    pub api_calls: usize,
    /// Cache hits
    pub cache_hits: usize,
    /// Errors encountered
    pub errors: Vec<String>,
}

/// OSV vulnerability information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OsvVulnerability {
    /// Vulnerability ID
    pub id: String,
    /// Summary
    pub summary: String,
    /// Details
    pub details: Option<String>,
    /// Severity
    pub severity: Option<String>,
    /// CVSS score
    pub cvss_score: Option<f64>,
    /// Affected package
    pub package: OsvPackage,
    /// Affected versions
    pub affected_versions: Vec<String>,
    /// Fixed versions
    pub fixed_versions: Vec<String>,
    /// References
    pub references: Vec<String>,
    /// Published date
    pub published: Option<chrono::DateTime<chrono::Utc>>,
    /// Modified date
    pub modified: Option<chrono::DateTime<chrono::Utc>>,
}

/// OSV package information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OsvPackage {
    /// Package name
    pub name: String,
    /// Ecosystem
    pub ecosystem: String,
    /// Package URL
    pub purl: Option<String>,
}

/// OSV API query request
#[derive(Debug, Serialize)]
struct OsvQueryRequest {
    /// Package information
    package: OsvPackageQuery,
    /// Version (optional)
    version: Option<String>,
}

/// OSV package query
#[derive(Debug, Serialize)]
struct OsvPackageQuery {
    /// Package name
    name: String,
    /// Ecosystem
    ecosystem: String,
}

/// OSV API response
#[derive(Debug, Deserialize)]
struct OsvQueryResponse {
    /// Vulnerabilities
    vulns: Vec<OsvVulnResponse>,
}

/// OSV vulnerability response
#[derive(Debug, Deserialize)]
struct OsvVulnResponse {
    /// Vulnerability ID
    id: String,
    /// Summary
    summary: String,
    /// Details
    details: Option<String>,
    /// Severity
    severity: Option<Vec<OsvSeverity>>,
    /// Affected packages
    affected: Vec<OsvAffected>,
    /// References
    references: Option<Vec<OsvReference>>,
    /// Published date
    published: Option<String>,
    /// Modified date
    modified: Option<String>,
}

/// OSV severity
#[derive(Debug, Deserialize)]
struct OsvSeverity {
    /// Type
    #[serde(rename = "type")]
    severity_type: String,
    /// Score
    score: String,
}

/// OSV affected package
#[derive(Debug, Deserialize)]
struct OsvAffected {
    /// Package
    package: OsvPackageResponse,
    /// Ranges
    ranges: Vec<OsvRange>,
}

/// OSV package response
#[derive(Debug, Deserialize)]
struct OsvPackageResponse {
    /// Name
    name: String,
    /// Ecosystem
    ecosystem: String,
    /// PURL
    purl: Option<String>,
}

/// OSV range
#[derive(Debug, Deserialize)]
struct OsvRange {
    /// Type
    #[serde(rename = "type")]
    range_type: String,
    /// Events
    events: Vec<OsvEvent>,
}

/// OSV event
#[derive(Debug, Deserialize)]
struct OsvEvent {
    /// Introduced
    introduced: Option<String>,
    /// Fixed
    fixed: Option<String>,
}

/// OSV reference
#[derive(Debug, Deserialize)]
struct OsvReference {
    /// Type
    #[serde(rename = "type")]
    ref_type: String,
    /// URL
    url: String,
}

/// OSV Scanner implementation
pub struct OsvScannerImpl {
    /// HTTP client
    client: Client,
    /// Configuration
    config: OsvScannerConfig,
    /// Circuit breaker
    circuit_breaker: Arc<RwLock<CircuitBreaker>>,
    /// Cache for API responses
    cache: Option<ConcurrentCache<String, Vec<OsvVulnerability>>>,
}

impl OsvScannerImpl {
    /// Create new OSV scanner
    pub fn new(config: &ScanningConfig) -> Self {
        let osv_config = config.osv_scanner.clone();
        let client = Client::builder()
            .timeout(Duration::from_secs(osv_config.timeout_seconds))
            .build()
            .expect("Failed to create HTTP client");

        let circuit_breaker = Arc::new(RwLock::new(CircuitBreaker::new(osv_config.circuit_breaker.clone())));

        let cache = if osv_config.enable_caching {
            let cache_config = CacheConfig {
                default_ttl: Some(Duration::from_secs(osv_config.cache_ttl_seconds)),
                max_entries: 10000,
                cleanup_interval: Duration::from_secs(300),
                enable_stats: true,
                redis_url: None, // TODO: Add Redis support if needed
            };
            Some(ConcurrentCache::new("osv_vulnerabilities".to_string(), cache_config))
        } else {
            None
        };

        Self {
            client,
            config: osv_config,
            circuit_breaker,
            cache,
        }
    }

    /// Map package type to OSV ecosystem
    fn map_package_type_to_ecosystem(&self, package_type: &str) -> Option<&'static str> {
        match package_type.to_lowercase().as_str() {
            "npm" => Some("npm"),
            "cargo" | "crate" => Some("crates.io"),
            "pypi" => Some("PyPI"),
            "maven" => Some("Maven"),
            "nuget" => Some("NuGet"),
            "golang" | "go" => Some("Go"),
            "packagist" => Some("Packagist"),
            "rubygems" => Some("RubyGems"),
            "hex" => Some("Hex"),
            _ => None,
        }
    }

    /// Create cache key for package
    fn create_cache_key(&self, package_name: &str, ecosystem: &str, version: Option<&str>) -> String {
        if let Some(version) = version {
            format!("{}:{}:{}", ecosystem, package_name, version)
        } else {
            format!("{}:{}", ecosystem, package_name)
        }
    }

    /// Query OSV API for vulnerabilities
    async fn query_osv_api(
        &self,
        package_name: &str,
        ecosystem: &str,
        version: Option<&str>,
    ) -> Result<Vec<OsvVulnerability>> {
        let query = OsvQueryRequest {
            package: OsvPackageQuery {
                name: package_name.to_string(),
                ecosystem: ecosystem.to_string(),
            },
            version: version.map(|v| v.to_string()),
        };

        let url = format!("{}/v1/query", self.config.api_url);
        debug!("Querying OSV API: {} for package {}:{}", url, ecosystem, package_name);

        let response = self.client
            .post(&url)
            .json(&query)
            .send()
            .await
            .map_err(|e| {
                error!("OSV API request failed: {}", e);
                e
            })?;

        if !response.status().is_success() {
            let status = response.status();
            let body = response.text().await.unwrap_or_default();
            error!("OSV API returned error {}: {}", status, body);
            return Err(anyhow::anyhow!("OSV API error: {} - {}", status, body));
        }

        let osv_response: OsvQueryResponse = response.json().await?;
        let vulnerabilities = self.convert_osv_response(osv_response, package_name, ecosystem);

        Ok(vulnerabilities)
    }

    /// Convert OSV API response to internal format
    fn convert_osv_response(
        &self,
        response: OsvQueryResponse,
        package_name: &str,
        ecosystem: &str,
    ) -> Vec<OsvVulnerability> {
        response.vulns.into_iter().map(|vuln| {
            let severity = vuln.severity.as_ref().and_then(|s| s.first()).map(|s| s.score.clone());
            let cvss_score = severity.as_ref().and_then(|s| s.parse::<f64>().ok());

            let affected_versions = vuln.affected.iter()
                .flat_map(|affected| {
                    affected.ranges.iter()
                        .flat_map(|range| {
                            range.events.iter()
                                .filter_map(|event| event.introduced.as_ref())
                                .cloned()
                        })
                })
                .collect();

            let fixed_versions = vuln.affected.iter()
                .flat_map(|affected| {
                    affected.ranges.iter()
                        .flat_map(|range| {
                            range.events.iter()
                                .filter_map(|event| event.fixed.as_ref())
                                .cloned()
                        })
                })
                .collect();

            let references = vuln.references.as_ref()
                .map(|refs| refs.iter().map(|r| r.url.clone()).collect())
                .unwrap_or_default();

            let published = vuln.published.as_ref()
                .and_then(|p| chrono::DateTime::parse_from_rfc3339(p).ok())
                .map(|dt| dt.with_timezone(&chrono::Utc));

            let modified = vuln.modified.as_ref()
                .and_then(|m| chrono::DateTime::parse_from_rfc3339(m).ok())
                .map(|dt| dt.with_timezone(&chrono::Utc));

            OsvVulnerability {
                id: vuln.id,
                summary: vuln.summary,
                details: vuln.details,
                severity,
                cvss_score,
                package: OsvPackage {
                    name: package_name.to_string(),
                    ecosystem: ecosystem.to_string(),
                    purl: vuln.affected.first().and_then(|a| a.package.purl.clone()),
                },
                affected_versions,
                fixed_versions,
                references,
                published,
                modified,
            }
        }).collect()
    }

    /// Apply false positive filters to vulnerabilities
    fn apply_false_positive_filters(&self, vulnerabilities: Vec<OsvVulnerability>) -> Vec<OsvVulnerability> {
        if !self.config.false_positive_filters.enabled {
            return vulnerabilities;
        }

        let filters = &self.config.false_positive_filters;

        vulnerabilities.into_iter().filter(|vuln| {
            // Check allowlist
            if !filters.allowlist.is_empty() {
                let package_pattern = format!("{}:{}", vuln.package.name, vuln.package.ecosystem);
                if !filters.allowlist.contains(&package_pattern) {
                    debug!("Filtering out vulnerability {} due to allowlist", vuln.id);
                    return false;
                }
            }

            // Check severity threshold
            if let Some(severity) = &vuln.severity {
                if !self.is_severity_above_threshold(severity, &filters.min_severity) {
                    debug!("Filtering out vulnerability {} due to severity threshold", vuln.id);
                    return false;
                }
            }

            // Check CVSS score threshold
            if let Some(max_score) = filters.max_cvss_score {
                if let Some(cvss_score) = vuln.cvss_score {
                    if cvss_score > max_score {
                        debug!("Filtering out vulnerability {} due to CVSS score threshold", vuln.id);
                        return false;
                    }
                }
            }

            // Check for unfixed vulnerabilities
            if filters.skip_unfixed_vulnerabilities && vuln.fixed_versions.is_empty() {
                debug!("Filtering out unfixed vulnerability {}", vuln.id);
                return false;
            }

            // Check vulnerability age
            if let Some(max_age_days) = filters.skip_vulnerabilities_older_than_days {
                if let Some(published) = vuln.published {
                    let age = chrono::Utc::now().signed_duration_since(published);
                    if age.num_days() > max_age_days as i64 {
                        debug!("Filtering out old vulnerability {} ({} days old)", vuln.id, age.num_days());
                        return false;
                    }
                }
            }

            // Version validation
            if filters.enable_version_validation {
                if !self.validate_vulnerability_versions(vuln) {
                    debug!("Filtering out vulnerability {} due to version validation", vuln.id);
                    return false;
                }
            }

            true
        }).collect()
    }

    /// Check if severity is above the minimum threshold
    fn is_severity_above_threshold(&self, severity: &str, min_severity: &str) -> bool {
        let severity_levels = ["low", "medium", "high", "critical"];
        let vuln_level = severity_levels.iter().position(|&s| s == severity.to_lowercase());
        let min_level = severity_levels.iter().position(|&s| s == min_severity.to_lowercase());

        match (vuln_level, min_level) {
            (Some(v), Some(m)) => v >= m,
            _ => true, // If severity not recognized, include it
        }
    }

    /// Validate vulnerability versions
    fn validate_vulnerability_versions(&self, vuln: &OsvVulnerability) -> bool {
        // Basic validation: check if affected versions are valid semver
        for version in &vuln.affected_versions {
            if Version::parse(version).is_err() {
                debug!("Invalid version format: {}", version);
                return false;
            }
        }

        for version in &vuln.fixed_versions {
            if Version::parse(version).is_err() {
                debug!("Invalid fixed version format: {}", version);
                return false;
            }
        }

        true
    }

    /// Process a batch of components
    async fn process_batch(
        &self,
        batch: &[SoftwareComponent],
    ) -> Vec<Result<(Vec<OsvVulnerability>, usize, usize)>> {
        let mut results = Vec::new();

        for component in batch {
            let ecosystem = match self.map_package_type_to_ecosystem(&component.package_type) {
                Some(eco) => eco,
                None => {
                    debug!("Unsupported package type: {} for component {}", component.package_type, component.name);
                    continue;
                }
            };

            let result = self.query_package_vulnerabilities(
                &component.name,
                ecosystem,
                Some(&component.version),
            ).await;

            match result {
                Ok(vulns) => {
                    let api_calls = if self.cache.is_some() { 0 } else { 1 }; // Simplified
                    let cache_hits = if vulns.is_empty() && self.cache.is_some() { 1 } else { 0 }; // Simplified
                    results.push(Ok((vulns, api_calls, cache_hits)));
                }
                Err(e) => {
                    warn!("Failed to query vulnerabilities for {}: {}", component.name, e);
                    results.push(Err(e));
                }
            }
        }

        results
    }

    /// Process components with parallel processing
    async fn process_batch_parallel(
        &self,
        batch: &[SoftwareComponent],
    ) -> Result<Vec<(Vec<OsvVulnerability>, usize, usize)>> {
        if !self.config.performance_config.enable_parallel_processing {
            return self.process_batch_sequential(batch).await;
        }

        let semaphore = Arc::new(tokio::sync::Semaphore::new(
            self.config.performance_config.max_parallel_requests
        ));

        let mut tasks = Vec::new();

        for component in batch {
            let semaphore = semaphore.clone();
            let component = component.clone();
            let scanner = self.clone_for_parallel();

            let task = tokio::spawn(async move {
                let _permit = semaphore.acquire().await.unwrap();
                scanner.process_single_component(&component).await
            });

            tasks.push(task);
        }

        let mut results = Vec::new();
        for task in tasks {
            match task.await {
                Ok(result) => results.push(result),
                Err(e) => {
                    warn!("Task join error: {}", e);
                    results.push(Err(anyhow::anyhow!("Task failed: {}", e)));
                }
            }
        }

        results.into_iter().collect()
    }

    /// Process components sequentially (fallback)
    async fn process_batch_sequential(
        &self,
        batch: &[SoftwareComponent],
    ) -> Result<Vec<(Vec<OsvVulnerability>, usize, usize)>> {
        let mut results = Vec::new();

        for component in batch {
            let result = self.process_single_component(component).await;
            results.push(result);
        }

        Ok(results)
    }

    /// Process a single component
    async fn process_single_component(
        &self,
        component: &SoftwareComponent,
    ) -> Result<(Vec<OsvVulnerability>, usize, usize)> {
        let ecosystem = match self.map_package_type_to_ecosystem(&component.package_type) {
            Some(eco) => eco,
            None => {
                debug!("Unsupported package type: {} for component {}", component.package_type, component.name);
                return Ok((Vec::new(), 0, 0));
            }
        };

        let result = self.query_package_vulnerabilities(
            &component.name,
            ecosystem,
            Some(&component.version),
        ).await;

        match result {
            Ok(vulns) => {
                let api_calls = if self.cache.is_some() { 0 } else { 1 };
                let cache_hits = if vulns.is_empty() && self.cache.is_some() { 1 } else { 0 };
                Ok((vulns, api_calls, cache_hits))
            }
            Err(e) => {
                warn!("Failed to query vulnerabilities for {}: {}", component.name, e);
                Err(e)
            }
        }
    }

    /// Create a clone for parallel processing
    fn clone_for_parallel(&self) -> Arc<OsvScannerImpl> {
        Arc::new(OsvScannerImpl {
            client: self.client.clone(),
            config: self.config.clone(),
            circuit_breaker: self.circuit_breaker.clone(),
            cache: self.cache.clone(),
        })
    }

    /// Check if incremental scan is needed
    fn should_skip_component(&self, component: &SoftwareComponent, last_scan: Option<chrono::DateTime<chrono::Utc>>) -> bool {
        if !self.config.performance_config.enable_incremental_scanning {
            return false;
        }

        if let Some(last_scan_time) = last_scan {
            // Skip if component was scanned recently (within cache TTL)
            let cache_age = chrono::Utc::now().signed_duration_since(last_scan_time);
            cache_age.num_seconds() < self.config.cache_ttl_seconds as i64
        } else {
            false
        }
    }

    /// Monitor memory usage
    fn check_memory_usage(&self) -> Result<()> {
        // Simple memory check - in a real implementation, you'd use system APIs
        // For now, just return Ok
        Ok(())
    }

    /// Apply rate limiting
    async fn apply_rate_limit(&self) -> Result<()> {
        if self.config.performance_config.rate_limit_per_second > 0 {
            // Simple rate limiting - sleep for appropriate interval
            let interval_ms = 1000 / self.config.performance_config.rate_limit_per_second as u64;
            tokio::time::sleep(Duration::from_millis(interval_ms)).await;
        }
        Ok(())
    }
}

#[async_trait]
impl OsvScanner for OsvScannerImpl {
    #[instrument(skip(self, components), fields(component_count = components.len()))]
    async fn scan_components(&self, components: &[SoftwareComponent]) -> Result<OsvScanResult> {
        let start_time = std::time::Instant::now();
        let scan_id = Uuid::new_v4().to_string();
        let mut all_vulnerabilities = Vec::new();
        let mut api_calls = 0;
        let mut cache_hits = 0;
        let mut errors = Vec::new();

        // Audit log scan started
        if let Some(audit_logger) = get_audit_logger() {
            audit_logger.log_osv_scan_started(&scan_id, components.len(), "system");
        }

        info!("Starting OSV scan for {} components", components.len());

        // Process components in batches
        for batch in components.chunks(self.config.batch_size) {
            let batch_results = self.process_batch(batch).await;
            for result in batch_results {
                match result {
                    Ok((vulns, calls, hits)) => {
                        all_vulnerabilities.extend(vulns);
                        api_calls += calls;
                        cache_hits += hits;
                    }
                    Err(e) => {
                        errors.push(format!("Batch processing error: {}", e));
                    }
                }
            }
        }

        // Apply false positive filters
        let filtered_vulnerabilities = self.apply_false_positive_filters(all_vulnerabilities);

        let scan_duration = start_time.elapsed();

        // Audit log scan completed
        if let Some(audit_logger) = get_audit_logger() {
            audit_logger.log_osv_scan_completed(&scan_id, filtered_vulnerabilities.len(), scan_duration.as_millis() as u64, "system");
        }

        info!(
            vulnerabilities_found = filtered_vulnerabilities.len(),
            vulnerabilities_filtered = all_vulnerabilities.len() - filtered_vulnerabilities.len(),
            api_calls,
            cache_hits,
            duration_ms = scan_duration.as_millis(),
            "OSV scan completed with filtering"
        );

        Ok(OsvScanResult {
            scanned_at: chrono::Utc::now(),
            components_scanned: components.len(),
            vulnerabilities: filtered_vulnerabilities,
            scan_duration,
            api_calls,
            cache_hits,
            errors,
        })
    }

    #[instrument(skip(self), fields(package = package_name, ecosystem = ecosystem))]
    async fn query_package_vulnerabilities(
        &self,
        package_name: &str,
        ecosystem: &str,
        version: Option<&str>,
    ) -> Result<Vec<OsvVulnerability>> {
        let cache_key = self.create_cache_key(package_name, ecosystem, version);

        // Check cache first
        if let Some(cache) = &self.cache {
            if let Some(cached) = cache.get(&cache_key).await {
                debug!("Cache hit for {}", cache_key);
                return Ok(cached);
            }
        }

        // Query API with circuit breaker
        let mut breaker = self.circuit_breaker.write().await;
        let result = breaker.execute(|| {
            self.query_osv_api(package_name, ecosystem, version)
        }).await;

        match result {
            Ok(vulnerabilities) => {
                // Cache the result
                if let Some(cache) = &self.cache {
                    let _ = cache.insert(cache_key, vulnerabilities.clone()).await;
                }
                Ok(vulnerabilities)
            }
            Err(CircuitBreakerError::OperationFailed(e)) => {
                Err(e)
            }
            Err(e) => {
                Err(anyhow::anyhow!("Circuit breaker error: {:?}", e))
            }
        }
    }

    async fn health_check(&self) -> Result<bool> {
        // Simple health check by querying a known package
        match self.query_package_vulnerabilities("test", "npm", Some("1.0.0")).await {
            Ok(_) => Ok(true),
            Err(_) => Ok(false), // OSV API might return empty results, which is fine
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::ScanningConfig;

    #[test]
    fn test_map_package_type_to_ecosystem() {
        let config = ScanningConfig::default();
        let scanner = OsvScannerImpl::new(&config);

        assert_eq!(scanner.map_package_type_to_ecosystem("npm"), Some("npm"));
        assert_eq!(scanner.map_package_type_to_ecosystem("cargo"), Some("crates.io"));
        assert_eq!(scanner.map_package_type_to_ecosystem("pypi"), Some("PyPI"));
        assert_eq!(scanner.map_package_type_to_ecosystem("unknown"), None);
    }

    #[test]
    fn test_create_cache_key() {
        let config = ScanningConfig::default();
        let scanner = OsvScannerImpl::new(&config);

        let key1 = scanner.create_cache_key("lodash", "npm", Some("4.17.21"));
        assert_eq!(key1, "npm:lodash:4.17.21");

        let key2 = scanner.create_cache_key("lodash", "npm", None);
        assert_eq!(key2, "npm:lodash");
    }

    #[test]
    fn test_osv_scanner_config_default() {
        let config = OsvScannerConfig::default();
        assert_eq!(config.api_url, "https://api.osv.dev");
        assert_eq!(config.timeout_seconds, 30);
        assert!(config.enable_caching);
    }
}