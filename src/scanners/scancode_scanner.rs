//! # ScanCode Toolkit Integration
//!
//! This module provides integration with the ScanCode Toolkit for advanced license detection
//! and analysis. It uses CLI integration since no official Rust bindings exist.

use crate::{config::ScanningConfig, error::Result, metrics::ScanMetrics};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    path::{Path, PathBuf},
    process::Stdio,
    time::Duration,
};
use tokio::{process::Command as TokioCommand, time::timeout};
use tracing::{debug, info, instrument, warn};

/// Advanced license information with detailed metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdvancedLicenseInfo {
    /// SPDX license identifier
    pub spdx_id: String,
    /// License name
    pub name: String,
    /// License category (permissive, copyleft, proprietary, etc.)
    pub category: LicenseCategory,
    /// License text content
    pub text: Option<String>,
    /// License URL
    pub url: Option<String>,
    /// OSI approved status
    pub osi_approved: bool,
    /// FSF Libre status
    pub fsf_libre: bool,
    /// Detection confidence score (0.0 to 1.0)
    pub confidence: f64,
    /// Start line number in file
    pub start_line: Option<u32>,
    /// End line number in file
    pub end_line: Option<u32>,
    /// Matched text length
    pub matched_length: Option<u32>,
    /// Rule identifier that matched
    pub rule_identifier: Option<String>,
    /// Additional metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// License detection result from ScanCode
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LicenseDetectionResult {
    /// File path where license was detected
    pub file_path: PathBuf,
    /// Detected licenses in this file
    pub licenses: Vec<AdvancedLicenseInfo>,
    /// Copyright statements found
    pub copyrights: Vec<CopyrightInfo>,
    /// Package information if detected
    pub package_info: Option<PackageInfo>,
    /// Scan timestamp
    pub scanned_at: chrono::DateTime<chrono::Utc>,
    /// Detection method used
    pub detection_method: DetectionMethod,
    /// Overall confidence score
    pub overall_confidence: f64,
}

/// Copyright information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CopyrightInfo {
    /// Copyright statement text
    pub statement: String,
    /// Start line number
    pub start_line: Option<u32>,
    /// End line number
    pub end_line: Option<u32>,
    /// Detection confidence
    pub confidence: f64,
}

/// Package information detected by ScanCode
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageInfo {
    /// Package type (e.g., "cargo", "npm", "pypi")
    pub package_type: String,
    /// Package namespace
    pub namespace: Option<String>,
    /// Package name
    pub name: String,
    /// Package version
    pub version: Option<String>,
    /// Primary license
    pub primary_license: Option<String>,
    /// Description
    pub description: Option<String>,
    /// Homepage URL
    pub homepage_url: Option<String>,
}

/// Detection method used
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum DetectionMethod {
    /// Rule-based detection
    RuleBased,
    /// SPDX license list matching
    SpdxMatching,
    /// Text mining
    TextMining,
    /// Combined methods
    Combined,
}

/// License categories
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum LicenseCategory {
    /// Permissive licenses (MIT, BSD, Apache)
    Permissive,
    /// Copyleft licenses (GPL, LGPL)
    Copyleft,
    /// Weak copyleft licenses
    WeakCopyleft,
    /// Proprietary licenses
    Proprietary,
    /// Public domain
    PublicDomain,
    /// Unknown category
    Unknown,
}

/// ScanCode scan summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScanCodeSummary {
    /// Total files scanned
    pub files_count: u32,
    /// Files with licenses detected
    pub license_files_count: u32,
    /// Files with copyrights detected
    pub copyright_files_count: u32,
    /// Unique licenses found
    pub unique_licenses: Vec<String>,
    /// Total license detections
    pub total_license_detections: u32,
    /// Scan duration
    pub scan_duration: Duration,
    /// ScanCode version used
    pub scancode_version: String,
    /// Errors encountered
    pub errors: Vec<String>,
}

/// ScanCode scanner configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScanCodeConfig {
    /// Path to scancode executable
    pub executable_path: String,
    /// Default timeout for scans (seconds)
    pub timeout_seconds: u64,
    /// Maximum file size to scan (bytes)
    pub max_file_size: u64,
    /// Include license text in results
    pub include_license_text: bool,
    /// Include copyright statements
    pub include_copyrights: bool,
    /// Include package information
    pub include_packages: bool,
    /// Minimum confidence threshold (0.0 to 1.0)
    pub min_confidence: f64,
    /// Output format
    pub output_format: ScanCodeOutputFormat,
    /// Additional CLI arguments
    pub extra_args: Vec<String>,
}

/// Output format for ScanCode
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ScanCodeOutputFormat {
    /// JSON format
    Json,
    /// SPDX format
    Spdx,
    /// CycloneDX format
    CycloneDx,
}

/// License scanner configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LicenseScannerConfig {
    /// ScanCode configuration
    pub scancode: ScanCodeConfig,
    /// Enable license scanning
    pub enabled: bool,
    /// Scan depth for directory traversal
    pub max_depth: u32,
    /// File patterns to include
    pub include_patterns: Vec<String>,
    /// File patterns to exclude
    pub exclude_patterns: Vec<String>,
    /// License categories to flag
    pub flagged_categories: Vec<LicenseCategory>,
    /// Minimum confidence for flagging
    pub flag_threshold: f64,
}

impl Default for ScanCodeConfig {
    fn default() -> Self {
        Self {
            executable_path: "scancode".to_string(),
            timeout_seconds: 300,
            max_file_size: 100 * 1024 * 1024, // 100MB
            include_license_text: true,
            include_copyrights: true,
            include_packages: true,
            min_confidence: 0.8,
            output_format: ScanCodeOutputFormat::Json,
            extra_args: vec!["--license".to_string(), "--copyright".to_string()],
        }
    }
}

impl Default for LicenseScannerConfig {
    fn default() -> Self {
        Self {
            scancode: ScanCodeConfig::default(),
            enabled: true,
            max_depth: 10,
            include_patterns: vec![
                "**/*.rs".to_string(),
                "**/*.js".to_string(),
                "**/*.ts".to_string(),
                "**/*.py".to_string(),
                "**/*.java".to_string(),
                "**/*.go".to_string(),
                "**/*.cpp".to_string(),
                "**/*.c".to_string(),
                "**/*.h".to_string(),
                "**/Cargo.toml".to_string(),
                "**/package.json".to_string(),
                "**/requirements.txt".to_string(),
                "**/pyproject.toml".to_string(),
                "**/pom.xml".to_string(),
                "**/go.mod".to_string(),
                "**/*.csproj".to_string(),
                "**/CMakeLists.txt".to_string(),
                "**/LICENSE*".to_string(),
                "**/COPYING*".to_string(),
                "**/README*".to_string(),
            ],
            exclude_patterns: vec![
                "**/node_modules/**".to_string(),
                "**/target/**".to_string(),
                "**/.git/**".to_string(),
                "**/build/**".to_string(),
                "**/dist/**".to_string(),
                "**/__pycache__/**".to_string(),
                "**/*.min.js".to_string(),
                "**/*.min.css".to_string(),
            ],
            flagged_categories: vec![LicenseCategory::Proprietary],
            flag_threshold: 0.9,
        }
    }
}

/// ScanCode Integration Service
pub struct ScanCodeIntegrationService {
    config: LicenseScannerConfig,
    base_config: ScanningConfig,
}

impl ScanCodeIntegrationService {
    /// Create new ScanCode integration service
    pub fn new(base_config: &ScanningConfig, license_config: LicenseScannerConfig) -> Self {
        Self {
            config: license_config,
            base_config: base_config.clone(),
        }
    }

    /// Scan a project for licenses using ScanCode
    #[instrument(skip(self), fields(project_path = %project_path.display()))]
    pub async fn scan_project(&self, project_path: &Path) -> Result<Vec<LicenseDetectionResult>> {
        info!("Starting ScanCode license scan");
        ScanMetrics::scan_started("scancode");

        let start_time = std::time::Instant::now();

        // Validate ScanCode installation
        self.validate_scancode_installation().await?;

        // Build scan arguments
        let scan_args = self.build_scan_arguments(project_path);

        // Execute ScanCode scan
        let output = self.execute_scancode_scan(&scan_args).await?;

        // Parse results
        let results = self.parse_scancode_output(&output).await?;

        let duration = start_time.elapsed();
        ScanMetrics::scan_completed("scancode", duration);

        info!(
            results_count = results.len(),
            duration_ms = duration.as_millis(),
            "ScanCode license scan completed"
        );

        Ok(results)
    }

    /// Scan a single file for licenses
    pub async fn scan_file(&self, file_path: &Path) -> Result<LicenseDetectionResult> {
        if !file_path.exists() {
            return Err(crate::error::InfinitumError::FileNotFound {
                path: file_path.to_string_lossy().to_string(),
            });
        }

        let file_size = tokio::fs::metadata(file_path).await?.len();
        if file_size > self.config.scancode.max_file_size {
            warn!(
                file = %file_path.display(),
                size = file_size,
                max_size = self.config.scancode.max_file_size,
                "File too large, skipping"
            );
            return Err(crate::error::InfinitumError::ScanCode {
                message: format!("File too large: {} bytes", file_size),
            });
        }

        let scan_args = self.build_file_scan_arguments(file_path);
        let output = self.execute_scancode_scan(&scan_args).await?;
        let results = self.parse_scancode_output(&output).await?;

        if results.is_empty() {
            // Create empty result for file with no detections
            Ok(LicenseDetectionResult {
                file_path: file_path.to_path_buf(),
                licenses: vec![],
                copyrights: vec![],
                package_info: None,
                scanned_at: chrono::Utc::now(),
                detection_method: DetectionMethod::Combined,
                overall_confidence: 0.0,
            })
        } else {
            Ok(results.into_iter().next().unwrap())
        }
    }

    /// Validate ScanCode installation
    async fn validate_scancode_installation(&self) -> Result<()> {
        debug!("Validating ScanCode installation");

        let output = TokioCommand::new(&self.config.scancode.executable_path)
            .arg("--version")
            .output()
            .await
            .map_err(|e| {
                if e.kind() == std::io::ErrorKind::NotFound {
                    crate::error::InfinitumError::ScanCodeNotFound {
                        path: self.config.scancode.executable_path.clone(),
                    }
                } else {
                    crate::error::InfinitumError::ScanCode {
                        message: format!("Failed to execute ScanCode: {}", e),
                    }
                }
            })?;

        if !output.status.success() {
            return Err(crate::error::InfinitumError::ScanCode {
                message: format!(
                    "ScanCode version check failed: {}",
                    String::from_utf8_lossy(&output.stderr)
                ),
            });
        }

        let version = String::from_utf8_lossy(&output.stdout);
        debug!(version = %version.trim(), "ScanCode version validated");

        Ok(())
    }

    /// Build scan arguments for project scan
    fn build_scan_arguments(&self, project_path: &Path) -> Vec<String> {
        let mut args = vec![
            project_path.to_string_lossy().to_string(),
            "--json".to_string(),
            "--license".to_string(),
        ];

        if self.config.scancode.include_copyrights {
            args.push("--copyright".to_string());
        }

        if self.config.scancode.include_packages {
            args.push("--package".to_string());
        }

        if self.config.scancode.include_license_text {
            args.push("--license-text".to_string());
        }

        // Add depth limit
        args.push("--max-depth".to_string());
        args.push(self.config.max_depth.to_string());

        // Add include patterns
        for pattern in &self.config.include_patterns {
            args.push("--include".to_string());
            args.push(pattern.clone());
        }

        // Add exclude patterns
        for pattern in &self.config.exclude_patterns {
            args.push("--exclude".to_string());
            args.push(pattern.clone());
        }

        // Add extra arguments
        args.extend(self.config.scancode.extra_args.clone());

        args
    }

    /// Build scan arguments for single file scan
    fn build_file_scan_arguments(&self, file_path: &Path) -> Vec<String> {
        let mut args = vec![
            file_path.to_string_lossy().to_string(),
            "--json".to_string(),
            "--license".to_string(),
        ];

        if self.config.scancode.include_copyrights {
            args.push("--copyright".to_string());
        }

        if self.config.scancode.include_license_text {
            args.push("--license-text".to_string());
        }

        // Add extra arguments
        args.extend(self.config.scancode.extra_args.clone());

        args
    }

    /// Execute ScanCode scan with timeout
    async fn execute_scancode_scan(&self, args: &[String]) -> Result<String> {
        debug!(args = ?args, "Executing ScanCode scan");

        let timeout_duration = Duration::from_secs(self.config.scancode.timeout_seconds);

        let output = timeout(timeout_duration, async {
            TokioCommand::new(&self.config.scancode.executable_path)
                .args(args)
                .stdout(Stdio::piped())
                .stderr(Stdio::piped())
                .output()
                .await
        })
        .await
        .map_err(|_| crate::error::InfinitumError::ScanCodeTimeout {
            timeout: self.config.scancode.timeout_seconds,
        })?
        .map_err(|e| crate::error::InfinitumError::ScanCode {
            message: format!("Failed to execute ScanCode: {}", e),
        })?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(crate::error::InfinitumError::ScanCode {
                message: format!("ScanCode execution failed: {}", stderr),
            });
        }

        let stdout = String::from_utf8_lossy(&output.stdout).to_string();
        debug!(output_size = stdout.len(), "ScanCode scan completed successfully");

        Ok(stdout)
    }

    /// Parse ScanCode JSON output
    async fn parse_scancode_output(&self, output: &str) -> Result<Vec<LicenseDetectionResult>> {
        debug!("Parsing ScanCode JSON output");

        let scancode_result: serde_json::Value = serde_json::from_str(output)
            .map_err(|e| crate::error::InfinitumError::ScanCodeParse {
                message: format!("Failed to parse ScanCode JSON: {}", e),
            })?;

        let mut results = Vec::new();

        if let Some(files) = scancode_result.get("files").and_then(|f| f.as_array()) {
            for file in files {
                if let Some(result) = self.parse_file_result(file)? {
                    results.push(result);
                }
            }
        }

        debug!(parsed_results = results.len(), "Parsed ScanCode results");
        Ok(results)
    }

    /// Parse individual file result from ScanCode output
    fn parse_file_result(&self, file_value: &serde_json::Value) -> Result<Option<LicenseDetectionResult>> {
        let file_path = file_value
            .get("path")
            .and_then(|p| p.as_str())
            .ok_or_else(|| crate::error::InfinitumError::ScanCodeParse {
                message: "Missing file path in ScanCode result".to_string(),
            })?;

        let mut licenses = Vec::new();
        let mut copyrights = Vec::new();
        let mut overall_confidence: f64 = 0.0;

        // Parse licenses
        if let Some(license_detections) = file_value.get("licenses").and_then(|l| l.as_array()) {
            for license in license_detections {
                if let Some(license_info) = self.parse_license_info(license)? {
                    if license_info.confidence >= self.config.scancode.min_confidence {
                        licenses.push(license_info.clone());
                        overall_confidence = overall_confidence.max(license_info.confidence);
                    }
                }
            }
        }

        // Parse copyrights
        if let Some(copyright_detections) = file_value.get("copyrights").and_then(|c| c.as_array()) {
            for copyright in copyright_detections {
                if let Some(copyright_info) = self.parse_copyright_info(copyright)? {
                    copyrights.push(copyright_info);
                }
            }
        }

        // Parse package info
        let package_info = self.parse_package_info(file_value)?;

        // Skip files with no detections if they don't meet minimum criteria
        if licenses.is_empty() && copyrights.is_empty() && package_info.is_none() {
            return Ok(None);
        }

        Ok(Some(LicenseDetectionResult {
            file_path: PathBuf::from(file_path),
            licenses,
            copyrights,
            package_info,
            scanned_at: chrono::Utc::now(),
            detection_method: DetectionMethod::Combined,
            overall_confidence,
        }))
    }

    /// Parse license information from ScanCode result
    fn parse_license_info(&self, license_value: &serde_json::Value) -> Result<Option<AdvancedLicenseInfo>> {
        let spdx_id = license_value
            .get("spdx_license_key")
            .or_else(|| license_value.get("license_expression"))
            .and_then(|s| s.as_str())
            .unwrap_or("Unknown");

        let name = license_value
            .get("spdx_name")
            .and_then(|n| n.as_str())
            .unwrap_or(spdx_id);

        let confidence = license_value
            .get("score")
            .and_then(|s| s.as_f64())
            .unwrap_or(0.0);

        let category = self.determine_license_category(spdx_id);

        let license_info = AdvancedLicenseInfo {
            spdx_id: spdx_id.to_string(),
            name: name.to_string(),
            category,
            text: license_value
                .get("matched_text")
                .and_then(|t| t.as_str())
                .map(|s| s.to_string()),
            url: license_value
                .get("homepage_url")
                .and_then(|u| u.as_str())
                .map(|s| s.to_string()),
            osi_approved: license_value
                .get("is_osi_approved")
                .and_then(|o| o.as_bool())
                .unwrap_or(false),
            fsf_libre: license_value
                .get("is_fsf_libre")
                .and_then(|f| f.as_bool())
                .unwrap_or(false),
            confidence,
            start_line: license_value
                .get("start_line")
                .and_then(|l| l.as_u64())
                .map(|l| l as u32),
            end_line: license_value
                .get("end_line")
                .and_then(|l| l.as_u64())
                .map(|l| l as u32),
            matched_length: license_value
                .get("matched_length")
                .and_then(|l| l.as_u64())
                .map(|l| l as u32),
            rule_identifier: license_value
                .get("rule_identifier")
                .and_then(|r| r.as_str())
                .map(|s| s.to_string()),
            metadata: HashMap::new(),
        };

        Ok(Some(license_info))
    }

    /// Parse copyright information from ScanCode result
    fn parse_copyright_info(&self, copyright_value: &serde_json::Value) -> Result<Option<CopyrightInfo>> {
        let statement = copyright_value
            .get("copyright")
            .and_then(|c| c.as_str())
            .ok_or_else(|| crate::error::InfinitumError::ScanCodeParse {
                message: "Missing copyright statement".to_string(),
            })?;

        let confidence = copyright_value
            .get("score")
            .and_then(|s| s.as_f64())
            .unwrap_or(1.0);

        let copyright_info = CopyrightInfo {
            statement: statement.to_string(),
            start_line: copyright_value
                .get("start_line")
                .and_then(|l| l.as_u64())
                .map(|l| l as u32),
            end_line: copyright_value
                .get("end_line")
                .and_then(|l| l.as_u64())
                .map(|l| l as u32),
            confidence,
        };

        Ok(Some(copyright_info))
    }

    /// Parse package information from ScanCode result
    fn parse_package_info(&self, file_value: &serde_json::Value) -> Result<Option<PackageInfo>> {
        if let Some(packages) = file_value.get("packages").and_then(|p| p.as_array()) {
            if let Some(package) = packages.first() {
                let package_info = PackageInfo {
                    package_type: package
                        .get("type")
                        .and_then(|t| t.as_str())
                        .unwrap_or("unknown")
                        .to_string(),
                    namespace: package
                        .get("namespace")
                        .and_then(|n| n.as_str())
                        .map(|s| s.to_string()),
                    name: package
                        .get("name")
                        .and_then(|n| n.as_str())
                        .unwrap_or("unknown")
                        .to_string(),
                    version: package
                        .get("version")
                        .and_then(|v| v.as_str())
                        .map(|s| s.to_string()),
                    primary_license: package
                        .get("primary_license")
                        .and_then(|l| l.as_str())
                        .map(|s| s.to_string()),
                    description: package
                        .get("description")
                        .and_then(|d| d.as_str())
                        .map(|s| s.to_string()),
                    homepage_url: package
                        .get("homepage_url")
                        .and_then(|h| h.as_str())
                        .map(|s| s.to_string()),
                };

                return Ok(Some(package_info));
            }
        }

        Ok(None)
    }

    /// Determine license category from SPDX ID
    fn determine_license_category(&self, spdx_id: &str) -> LicenseCategory {
        match spdx_id {
            "MIT" | "BSD-2-Clause" | "BSD-3-Clause" | "ISC" | "Apache-2.0" => LicenseCategory::Permissive,
            "GPL-2.0" | "GPL-3.0" | "LGPL-2.1" | "LGPL-3.0" | "AGPL-3.0" => LicenseCategory::Copyleft,
            "LGPL-2.0" | "CDDL-1.0" | "EPL-2.0" => LicenseCategory::WeakCopyleft,
            "Proprietary" | "LicenseRef-Proprietary" => LicenseCategory::Proprietary,
            "CC0-1.0" | "Unlicense" => LicenseCategory::PublicDomain,
            _ => LicenseCategory::Unknown,
        }
    }

    /// Get scan summary
    pub async fn get_scan_summary(&self, results: &[LicenseDetectionResult]) -> ScanCodeSummary {
        let files_count = results.len();
        let license_files_count = results
            .iter()
            .filter(|r| !r.licenses.is_empty())
            .count();
        let copyright_files_count = results
            .iter()
            .filter(|r| !r.copyrights.is_empty())
            .count();

        let mut unique_licenses = std::collections::HashSet::new();
        let mut total_license_detections = 0;

        for result in results {
            for license in &result.licenses {
                unique_licenses.insert(license.spdx_id.clone());
                total_license_detections += 1;
            }
        }

        ScanCodeSummary {
            files_count: files_count as u32,
            license_files_count: license_files_count as u32,
            copyright_files_count: copyright_files_count as u32,
            unique_licenses: unique_licenses.into_iter().collect(),
            total_license_detections: total_license_detections as u32,
            scan_duration: Duration::from_secs(0), // Would be set by caller
            scancode_version: "Unknown".to_string(), // Would be retrieved separately
            errors: vec![], // Would be collected during scan
        }
    }
}