//! High-performance concurrent caching utilities

use dashmap::DashMap;
use redis::{AsyncCommands, Client};
use std::hash::Hash;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;

/// Cache entry with metadata
#[derive(Debug, Clone)]
pub struct CacheEntry<T> {
    /// Cached value
    pub value: T,
    /// When the entry was created
    pub created_at: Instant,
    /// Time-to-live duration
    pub ttl: Option<Duration>,
    /// Access count for LRU eviction
    pub access_count: u64,
    /// Last accessed time
    pub last_accessed: Instant,
}

impl<T> CacheEntry<T> {
    /// Create a new cache entry
    pub fn new(value: T, ttl: Option<Duration>) -> Self {
        let now = Instant::now();
        Self {
            value,
            created_at: now,
            ttl,
            access_count: 0,
            last_accessed: now,
        }
    }

    /// Check if the entry is expired
    pub fn is_expired(&self) -> bool {
        if let Some(ttl) = self.ttl {
            self.created_at.elapsed() > ttl
        } else {
            false
        }
    }

    /// Mark entry as accessed
    pub fn mark_accessed(&mut self) {
        self.access_count += 1;
        self.last_accessed = Instant::now();
    }

    /// Get the value and mark as accessed
    pub fn get_value(&mut self) -> &T {
        self.mark_accessed();
        &self.value
    }
}

/// Cache configuration
#[derive(Debug, Clone)]
pub struct CacheConfig {
    /// Default TTL for cache entries
    pub default_ttl: Option<Duration>,
    /// Maximum number of entries
    pub max_entries: usize,
    /// Cleanup interval
    pub cleanup_interval: Duration,
    /// Enable statistics collection
    pub enable_stats: bool,
    /// Redis URL for distributed caching
    pub redis_url: Option<String>,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            default_ttl: Some(Duration::from_secs(300)), // 5 minutes
            max_entries: 10000,
            cleanup_interval: Duration::from_secs(60), // 1 minute
            enable_stats: true,
            redis_url: None,
        }
    }
}

/// Cache statistics
#[derive(Debug, Clone)]
pub struct CacheStats {
    /// Total number of entries
    pub entries: usize,
    /// Number of hits
    pub hits: u64,
    /// Number of misses
    pub misses: u64,
    /// Hit rate percentage
    pub hit_rate: f64,
    /// Total memory usage estimate
    pub memory_usage_bytes: u64,
    /// Last cleanup time
    pub last_cleanup: Instant,
}

/// High-performance concurrent cache
pub struct ConcurrentCache<K, V> {
    /// Underlying DashMap for concurrent access
    map: Arc<DashMap<K, CacheEntry<V>>>,
    /// Cache configuration
    config: CacheConfig,
    /// Cache statistics
    stats: Arc<RwLock<CacheStats>>,
    /// Cache name for identification
    name: String,
    /// Redis client for distributed caching
    redis_client: Option<Client>,
}

impl<K, V> ConcurrentCache<K, V>
where
    K: Eq + Hash + Clone + Send + Sync + 'static,
    V: Clone + Send + Sync + 'static,
{
    /// Create a new concurrent cache
    pub fn new(name: String, config: CacheConfig) -> Self {
        let stats = CacheStats {
            entries: 0,
            hits: 0,
            misses: 0,
            hit_rate: 0.0,
            memory_usage_bytes: 0,
            last_cleanup: Instant::now(),
        };

        let redis_client = config.redis_url.as_ref().and_then(|url| {
            Client::open(url.clone()).ok()
        });

        Self {
            map: Arc::new(DashMap::new()),
            config,
            stats: Arc::new(RwLock::new(stats)),
            name,
            redis_client,
        }
    }

    /// Get a value from the cache
    pub async fn get(&self, key: &K) -> Option<V> {
        if let Some(mut entry) = self.map.get_mut(key) {
            if entry.is_expired() {
                // Remove expired entry
                drop(entry);
                self.map.remove(key);
                self.update_stats(false).await;
                return None;
            }

            let value = entry.get_value().clone();
            self.update_stats(true).await;
            Some(value)
        } else {
            self.update_stats(false).await;
            None
        }
    }

    /// Insert a value into the cache
    pub async fn insert(&self, key: K, value: V) -> Option<V> {
        let ttl = self.config.default_ttl;
        let entry = CacheEntry::new(value, ttl);

        // Check if we need to evict entries
        if self.map.len() >= self.config.max_entries {
            self.evict_lru().await;
        }

        let old_value = self.map.insert(key, entry).map(|e| e.value);
        self.update_entries_count().await;
        old_value
    }

    /// Insert with custom TTL
    pub async fn insert_with_ttl(&self, key: K, value: V, ttl: Duration) -> Option<V> {
        let entry = CacheEntry::new(value, Some(ttl));

        if self.map.len() >= self.config.max_entries {
            self.evict_lru().await;
        }

        let old_value = self.map.insert(key, entry).map(|e| e.value);
        self.update_entries_count().await;
        old_value
    }

    /// Remove a value from the cache
    pub async fn remove(&self, key: &K) -> Option<V> {
        let result = self.map.remove(key).map(|(_, entry)| entry.value);
        self.update_entries_count().await;
        result
    }

    /// Clear all entries
    pub async fn clear(&self) {
        self.map.clear();
        self.update_entries_count().await;
    }

    /// Get cache size
    pub fn len(&self) -> usize {
        self.map.len()
    }

    /// Check if cache is empty
    pub fn is_empty(&self) -> bool {
        self.map.is_empty()
    }

    /// Get cache statistics
    pub async fn stats(&self) -> CacheStats {
        let mut stats = self.stats.read().await.clone();
        let total_requests = stats.hits + stats.misses;
        stats.hit_rate = if total_requests > 0 {
            (stats.hits as f64 / total_requests as f64) * 100.0
        } else {
            0.0
        };
        stats.entries = self.map.len();
        stats
    }

    /// Update cache statistics
    async fn update_stats(&self, is_hit: bool) {
        if !self.config.enable_stats {
            return;
        }

        let mut stats = self.stats.write().await;
        if is_hit {
            stats.hits += 1;
        } else {
            stats.misses += 1;
        }
    }

    /// Update entries count in statistics
    async fn update_entries_count(&self) {
        if !self.config.enable_stats {
            return;
        }

        let mut stats = self.stats.write().await;
        stats.entries = self.map.len();
    }

    /// Evict least recently used entries
    async fn evict_lru(&self) {
        let mut entries: Vec<_> = self.map.iter().collect();
        entries.sort_by_key(|entry| entry.last_accessed);

        // Remove oldest 10% of entries
        let to_remove = (self.config.max_entries / 10).max(1);
        for entry in entries.iter().take(to_remove) {
            self.map.remove(entry.key());
        }

        tracing::debug!("Evicted {} entries from cache {}", to_remove, self.name);
    }

    /// Start cleanup task for expired entries
    pub async fn start_cleanup_task(&self) {
        let map = Arc::clone(&self.map);
        let config = self.config.clone();
        let stats = Arc::clone(&self.stats);
        let name = self.name.clone();

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(config.cleanup_interval);

            loop {
                interval.tick().await;

                let mut expired_count = 0;
                map.retain(|_, entry| {
                    if entry.is_expired() {
                        expired_count += 1;
                        false
                    } else {
                        true
                    }
                });

                if expired_count > 0 {
                    tracing::debug!("Cleaned up {} expired entries from cache {}", expired_count, name);
                    if config.enable_stats {
                        let mut stats = stats.write().await;
                        stats.last_cleanup = Instant::now();
                    }
                }
            }
        });
    }

    /// Get value from Redis if available
    async fn get_from_redis(&self, key: &str) -> Option<V>
    where
        V: for<'de> serde::Deserialize<'de>,
    {
        if let Some(client) = &self.redis_client {
            if let Ok(mut conn) = client.get_multiplexed_async_connection().await {
                if let Ok(value_str) = conn.get::<_, String>(key).await {
                    if let Ok(value) = serde_json::from_str(&value_str) {
                        return Some(value);
                    }
                }
            }
        }
        None
    }

    /// Set value in Redis
    async fn set_in_redis(&self, key: &str, value: &V, ttl_seconds: Option<u64>)
    where
        V: serde::Serialize,
    {
        if let Some(client) = &self.redis_client {
            if let Ok(mut conn) = client.get_multiplexed_async_connection().await {
                if let Ok(value_str) = serde_json::to_string(value) {
                    let _: Result<(), _> = if let Some(ttl) = ttl_seconds {
                        conn.set_ex(key, value_str, ttl).await
                    } else {
                        conn.set(key, value_str).await
                    };
                }
            }
        }
    }

    /// Get value with Redis fallback
    pub async fn get_with_redis(&self, key: &K) -> Option<V>
    where
        K: ToString,
        V: for<'de> serde::Deserialize<'de>,
    {
        // Try in-memory cache first
        if let Some(value) = self.get(key).await {
            return Some(value);
        }

        // Try Redis
        let redis_key = format!("{}:{}", self.name, key.to_string());
        if let Some(value) = self.get_from_redis(&redis_key).await {
            // Cache in memory for future use
            let _ = self.insert(key.clone(), value.clone()).await;
            self.update_stats(true).await;
            return Some(value);
        }

        self.update_stats(false).await;
        None
    }

    /// Insert with Redis persistence
    pub async fn insert_with_redis(&self, key: K, value: V) -> Option<V>
    where
        K: ToString,
        V: serde::Serialize,
    {
        let result = self.insert(key.clone(), value.clone()).await;

        // Also store in Redis
        let redis_key = format!("{}:{}", self.name, key.to_string());
        let ttl_seconds = self.config.default_ttl.map(|d| d.as_secs());
        self.set_in_redis(&redis_key, &value, ttl_seconds).await;

        result
    }
}

/// Specialized cache for API responses
pub type ApiResponseCache = ConcurrentCache<String, serde_json::Value>;

/// Specialized cache for database query results
pub type QueryResultCache = ConcurrentCache<String, Vec<serde_json::Value>>;

/// Specialized cache for file metadata
pub type FileMetadataCache = ConcurrentCache<String, std::fs::Metadata>;

/// Create API response cache
pub fn create_api_cache(name: &str) -> ApiResponseCache {
    let config = CacheConfig {
        default_ttl: Some(Duration::from_secs(300)), // 5 minutes
        max_entries: 5000,
        ..Default::default()
    };
    // Note: In a real application, you'd start the cleanup task
    // cache.start_cleanup_task().await;
    ApiResponseCache::new(name.to_string(), config)
}

/// Create database query cache
pub fn create_query_cache(name: &str) -> QueryResultCache {
    let config = CacheConfig {
        default_ttl: Some(Duration::from_secs(60)), // 1 minute
        max_entries: 2000,
        ..Default::default()
    };
    QueryResultCache::new(name.to_string(), config)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_cache_operations() {
        let cache = ConcurrentCache::new("test".to_string(), CacheConfig::default());

        // Test insert and get
        cache.insert("key1".to_string(), "value1".to_string()).await;
        let value = cache.get(&"key1".to_string()).await;
        assert_eq!(value, Some("value1".to_string()));

        // Test cache miss
        let miss = cache.get(&"nonexistent".to_string()).await;
        assert_eq!(miss, None);

        // Test remove
        let removed = cache.remove(&"key1".to_string()).await;
        assert_eq!(removed, Some("value1".to_string()));

        // Verify removal
        let after_remove = cache.get(&"key1".to_string()).await;
        assert_eq!(after_remove, None);
    }

    #[tokio::test]
    async fn test_cache_ttl() {
        let config = CacheConfig {
            default_ttl: Some(Duration::from_millis(100)),
            ..Default::default()
        };
        let cache = ConcurrentCache::new("ttl_test".to_string(), config);

        cache.insert("short".to_string(), "value".to_string()).await;

        // Should be available immediately
        let immediate = cache.get(&"short".to_string()).await;
        assert_eq!(immediate, Some("value".to_string()));

        // Wait for expiration
        tokio::time::sleep(Duration::from_millis(150)).await;

        // Should be expired
        let expired = cache.get(&"short".to_string()).await;
        assert_eq!(expired, None);
    }

    #[test]
    fn test_cache_config() {
        let config = CacheConfig::default();
        assert_eq!(config.default_ttl, Some(Duration::from_secs(300)));
        assert_eq!(config.max_entries, 10000);
    }
}