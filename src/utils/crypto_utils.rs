//! Cryptographic utilities for secure operations

use crate::error::Result;
use base64::{engine::general_purpose, Engine as _};
use aes_gcm::{
    aead::{Aead, KeyInit},
    Aes256Gcm, Nonce,
};
use ring::{
    hmac,
    rand::{self, SecureRandom},
    signature::{self, KeyPair},
};
use serde::{Deserialize, Serialize};
use sha2::{Digest, Sha256, Sha512};

/// Hash algorithms supported by the system
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum HashAlgorithm {
    Sha256,
    Sha512,
}

/// Digital signature algorithms
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SignatureAlgorithm {
    Ed25519,
    EcdsaP256,
}

/// Cryptographic hash result
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct HashResult {
    pub algorithm: HashAlgorithm,
    pub hash: String,
    pub hex: String,
}

/// Digital signature result
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SignatureResult {
    pub algorithm: SignatureAlgorithm,
    pub signature: String,
    pub public_key: String,
}

/// Generate a cryptographic hash of the input data
pub fn hash_data(data: &[u8], algorithm: HashAlgorithm) -> Result<HashResult> {
    let (hash_bytes, algo) = match algorithm {
        HashAlgorithm::Sha256 => {
            let mut hasher = Sha256::new();
            hasher.update(data);
            (hasher.finalize().to_vec(), HashAlgorithm::Sha256)
        }
        HashAlgorithm::Sha512 => {
            let mut hasher = Sha512::new();
            hasher.update(data);
            (hasher.finalize().to_vec(), HashAlgorithm::Sha512)
        }
    };

    Ok(HashResult {
        algorithm: algo,
        hash: general_purpose::STANDARD.encode(&hash_bytes),
        hex: hex::encode(&hash_bytes),
    })
}

/// Generate a secure random string
pub fn generate_random_string(length: usize) -> Result<String> {
    let rng = rand::SystemRandom::new();
    let mut bytes = vec![0u8; length];
    rng.fill(&mut bytes)?;
    Ok(general_purpose::STANDARD.encode(&bytes))
}

/// Generate HMAC for message authentication
pub fn generate_hmac(key: &[u8], message: &[u8]) -> Result<String> {
    let key = hmac::Key::new(hmac::HMAC_SHA256, key);
    let signature = hmac::sign(&key, message);
    Ok(general_purpose::STANDARD.encode(signature.as_ref()))
}

/// Verify HMAC signature
pub fn verify_hmac(key: &[u8], message: &[u8], signature: &str) -> Result<bool> {
    let key = hmac::Key::new(hmac::HMAC_SHA256, key);
    let signature_bytes = general_purpose::STANDARD.decode(signature)?;

    Ok(hmac::verify(&key, message, &signature_bytes).is_ok())
}

/// Generate Ed25519 key pair
pub fn generate_ed25519_keypair() -> Result<(Vec<u8>, Vec<u8>)> {
    let rng = rand::SystemRandom::new();
    let pkcs8_bytes = signature::Ed25519KeyPair::generate_pkcs8(&rng)?;
    let key_pair = signature::Ed25519KeyPair::from_pkcs8(pkcs8_bytes.as_ref())?;

    Ok((
        pkcs8_bytes.as_ref().to_vec(),
        key_pair.public_key().as_ref().to_vec(),
    ))
}

/// Sign data with Ed25519 private key
pub fn sign_ed25519(private_key: &[u8], data: &[u8]) -> Result<SignatureResult> {
    let key_pair = signature::Ed25519KeyPair::from_pkcs8(private_key)?;
    let signature = key_pair.sign(data);

    Ok(SignatureResult {
        algorithm: SignatureAlgorithm::Ed25519,
        signature: general_purpose::STANDARD.encode(signature.as_ref()),
        public_key: general_purpose::STANDARD.encode(key_pair.public_key().as_ref()),
    })
}

/// Verify Ed25519 signature
pub fn verify_ed25519(public_key: &[u8], data: &[u8], signature: &[u8]) -> Result<bool> {
    let public_key = signature::UnparsedPublicKey::new(&signature::ED25519, public_key);

    match public_key.verify(data, signature) {
        Ok(()) => Ok(true),
        Err(_) => Ok(false),
    }
}

/// Calculate file hash for integrity verification
pub fn calculate_file_hash(file_path: &std::path::Path) -> Result<HashResult> {
    let data = std::fs::read(file_path)?;
    hash_data(&data, HashAlgorithm::Sha256)
}

/// Generate secure API key
pub fn generate_api_key() -> Result<String> {
    generate_random_string(32)
}

/// Constant-time string comparison to prevent timing attacks
pub fn secure_compare(a: &str, b: &str) -> bool {
    if a.len() != b.len() {
        return false;
    }

    let mut result = 0u8;
    for (byte_a, byte_b) in a.bytes().zip(b.bytes()) {
        result |= byte_a ^ byte_b;
    }

    result == 0
}

/// Encrypt data using AES-GCM
pub fn encrypt_aes_gcm(key: &[u8], plaintext: &[u8]) -> Result<(Vec<u8>, Vec<u8>)> {
    let cipher = Aes256Gcm::new_from_slice(key)?;
    let mut nonce_bytes = [0u8; 12];
    rand::SystemRandom::new().fill(&mut nonce_bytes)?;
    let nonce = Nonce::from_slice(&nonce_bytes);
    let ciphertext = cipher.encrypt(nonce, plaintext)?;

    Ok((ciphertext, nonce_bytes.to_vec()))
}

/// Decrypt data using AES-GCM
pub fn decrypt_aes_gcm(key: &[u8], ciphertext: &[u8], nonce: &[u8]) -> Result<Vec<u8>> {
    let cipher = Aes256Gcm::new_from_slice(key)?;
    let nonce = Nonce::from_slice(nonce);
    let plaintext = cipher.decrypt(nonce, ciphertext)?;

    Ok(plaintext)
}

/// Generate a secure AES key
pub fn generate_aes_key() -> Result<Vec<u8>> {
    let mut key = vec![0u8; 32]; // 256-bit key
    rand::SystemRandom::new().fill(&mut key)?;
    Ok(key)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_hash_data() {
        let data = b"test data";
        let result = hash_data(data, HashAlgorithm::Sha256).unwrap();
        assert_eq!(result.algorithm, HashAlgorithm::Sha256);
        assert!(!result.hash.is_empty());
        assert!(!result.hex.is_empty());
    }

    #[test]
    fn test_generate_random_string() {
        let random1 = generate_random_string(16).unwrap();
        let random2 = generate_random_string(16).unwrap();
        assert_ne!(random1, random2);
        assert!(!random1.is_empty());
    }

    #[test]
    fn test_hmac_generation_and_verification() {
        let key = b"secret key";
        let message = b"test message";

        let signature = generate_hmac(key, message).unwrap();
        let is_valid = verify_hmac(key, message, &signature).unwrap();

        assert!(is_valid);
    }

    #[test]
    fn test_secure_compare() {
        assert!(secure_compare("hello", "hello"));
        assert!(!secure_compare("hello", "world"));
        assert!(!secure_compare("hello", "hello world"));
    }

    #[test]
    fn test_aes_gcm_encrypt_decrypt() {
        let key = generate_aes_key().unwrap();
        let plaintext = b"Hello, world!";

        let (ciphertext, nonce) = encrypt_aes_gcm(&key, plaintext).unwrap();
        let decrypted = decrypt_aes_gcm(&key, &ciphertext, &nonce).unwrap();

        assert_eq!(plaintext, decrypted.as_slice());
    }

    #[test]
    fn test_generate_aes_key() {
        let key1 = generate_aes_key().unwrap();
        let key2 = generate_aes_key().unwrap();

        assert_eq!(key1.len(), 32);
        assert_eq!(key2.len(), 32);
        assert_ne!(key1, key2);
    }
}
