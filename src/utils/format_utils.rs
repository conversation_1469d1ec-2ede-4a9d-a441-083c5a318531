//! Formatting utilities for consistent data presentation

use crate::error::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::fmt;

/// Supported output formats
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OutputFormat {
    Json,
    Yaml,
    Xml,
    Csv,
    Table,
    Plain,
}

/// Size units for human-readable formatting
#[derive(Debu<PERSON>, <PERSON><PERSON>, Copy)]
pub enum SizeUnit {
    Bytes,
    Kilobytes,
    Megabytes,
    Gigabytes,
    Terabytes,
}

impl fmt::Display for SizeUnit {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            SizeUnit::Bytes => write!(f, "B"),
            SizeUnit::Kilobytes => write!(f, "KB"),
            SizeUnit::Megabytes => write!(f, "MB"),
            SizeUnit::Gigabytes => write!(f, "GB"),
            SizeUnit::Terabytes => write!(f, "TB"),
        }
    }
}

/// Format bytes into human-readable size
pub fn format_bytes(bytes: u64) -> String {
    const UNITS: &[SizeUnit] = &[
        SizeUnit::Terabytes,
        SizeUnit::Gigabytes,
        SizeUnit::Megabytes,
        SizeUnit::Kilobytes,
        SizeUnit::Bytes,
    ];

    const THRESHOLDS: &[u64] = &[
        1_099_511_627_776, // 1 TB
        1_073_741_824,     // 1 GB
        1_048_576,         // 1 MB
        1_024,             // 1 KB
        1,                 // 1 B
    ];

    for (i, &threshold) in THRESHOLDS.iter().enumerate() {
        if bytes >= threshold {
            let value = bytes as f64 / threshold as f64;
            return if threshold == 1 {
                format!("{} {}", bytes, UNITS[i])
            } else {
                format!("{:.2} {}", value, UNITS[i])
            };
        }
    }

    format!("{} B", bytes)
}

/// Format duration in human-readable format
pub fn format_duration(duration: std::time::Duration) -> String {
    let total_seconds = duration.as_secs();

    if total_seconds < 60 {
        format!("{}s", total_seconds)
    } else if total_seconds < 3600 {
        let minutes = total_seconds / 60;
        let seconds = total_seconds % 60;
        format!("{}m {}s", minutes, seconds)
    } else if total_seconds < 86400 {
        let hours = total_seconds / 3600;
        let minutes = (total_seconds % 3600) / 60;
        format!("{}h {}m", hours, minutes)
    } else {
        let days = total_seconds / 86400;
        let hours = (total_seconds % 86400) / 3600;
        format!("{}d {}h", days, hours)
    }
}

/// Format timestamp to ISO 8601 string
pub fn format_timestamp(timestamp: DateTime<Utc>) -> String {
    timestamp.to_rfc3339()
}

/// Parse ISO 8601 timestamp string
pub fn parse_timestamp(timestamp_str: &str) -> Result<DateTime<Utc>> {
    DateTime::parse_from_rfc3339(timestamp_str)
        .map(|dt| dt.with_timezone(&Utc))
        .map_err(|e| crate::error::InfinitumError::ParseError(e.to_string()))
}

/// Format percentage with specified decimal places
pub fn format_percentage(value: f64, decimal_places: usize) -> String {
    format!("{:.prec$}%", value * 100.0, prec = decimal_places)
}

/// Format number with thousands separators
pub fn format_number(number: u64) -> String {
    let number_str = number.to_string();
    let mut result = String::new();

    for (i, ch) in number_str.chars().rev().enumerate() {
        if i > 0 && i % 3 == 0 {
            result.push(',');
        }
        result.push(ch);
    }

    result.chars().rev().collect()
}

/// Truncate string to specified length with ellipsis
pub fn truncate_string(s: &str, max_length: usize) -> String {
    if s.len() <= max_length {
        s.to_string()
    } else if max_length <= 3 {
        "...".to_string()
    } else {
        format!("{}...", &s[..max_length - 3])
    }
}

/// Pad string to specified width
pub fn pad_string(s: &str, width: usize, align: Alignment) -> String {
    if s.len() >= width {
        return s.to_string();
    }

    let padding = width - s.len();

    match align {
        Alignment::Left => format!("{}{}", s, " ".repeat(padding)),
        Alignment::Right => format!("{}{}", " ".repeat(padding), s),
        Alignment::Center => {
            let left_padding = padding / 2;
            let right_padding = padding - left_padding;
            format!(
                "{}{}{}",
                " ".repeat(left_padding),
                s,
                " ".repeat(right_padding)
            )
        }
    }
}

/// String alignment options
#[derive(Debug, Clone, Copy)]
pub enum Alignment {
    Left,
    Right,
    Center,
}

/// Format severity level with color coding (for terminal output)
pub fn format_severity(severity: &str) -> String {
    match severity.to_lowercase().as_str() {
        "critical" => format!("\x1b[91m{}\x1b[0m", severity), // Bright red
        "high" => format!("\x1b[31m{}\x1b[0m", severity),     // Red
        "medium" => format!("\x1b[33m{}\x1b[0m", severity),   // Yellow
        "low" => format!("\x1b[32m{}\x1b[0m", severity),      // Green
        "info" => format!("\x1b[36m{}\x1b[0m", severity),     // Cyan
        _ => severity.to_string(),
    }
}

/// Remove ANSI color codes from string
pub fn strip_ansi_codes(s: &str) -> String {
    let re = regex::Regex::new(r"\x1b\[[0-9;]*m").unwrap();
    re.replace_all(s, "").to_string()
}

/// Format table row with proper column alignment
pub fn format_table_row(columns: &[String], widths: &[usize], alignments: &[Alignment]) -> String {
    let mut row = String::new();

    for (i, (column, &width)) in columns.iter().zip(widths.iter()).enumerate() {
        let alignment = alignments.get(i).copied().unwrap_or(Alignment::Left);
        let padded = pad_string(column, width, alignment);

        if i > 0 {
            row.push_str(" | ");
        }
        row.push_str(&padded);
    }

    row
}

/// Create table separator line
pub fn create_table_separator(widths: &[usize]) -> String {
    let mut separator = String::new();

    for (i, &width) in widths.iter().enumerate() {
        if i > 0 {
            separator.push_str("-+-");
        }
        separator.push_str(&"-".repeat(width));
    }

    separator
}

/// Format JSON with proper indentation
pub fn format_json<T: Serialize>(data: &T, pretty: bool) -> Result<String> {
    if pretty {
        serde_json::to_string_pretty(data)
    } else {
        serde_json::to_string(data)
    }
    .map_err(|e| crate::error::InfinitumError::SerializationError(e.to_string()))
}

/// Format YAML
pub fn format_yaml<T: Serialize>(data: &T) -> Result<String> {
    serde_yaml::to_string(data)
        .map_err(|e| crate::error::InfinitumError::SerializationError(e.to_string()))
}

/// Escape string for CSV output
pub fn escape_csv_field(field: &str) -> String {
    if field.contains(',') || field.contains('"') || field.contains('\n') {
        format!("\"{}\"", field.replace('"', "\"\""))
    } else {
        field.to_string()
    }
}

/// Format CSV row
pub fn format_csv_row(fields: &[String]) -> String {
    fields
        .iter()
        .map(|field| escape_csv_field(field))
        .collect::<Vec<_>>()
        .join(",")
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_format_bytes() {
        assert_eq!(format_bytes(0), "0 B");
        assert_eq!(format_bytes(512), "512 B");
        assert_eq!(format_bytes(1024), "1.00 KB");
        assert_eq!(format_bytes(1048576), "1.00 MB");
        assert_eq!(format_bytes(1073741824), "1.00 GB");
    }

    #[test]
    fn test_format_duration() {
        assert_eq!(format_duration(std::time::Duration::from_secs(30)), "30s");
        assert_eq!(
            format_duration(std::time::Duration::from_secs(90)),
            "1m 30s"
        );
        assert_eq!(
            format_duration(std::time::Duration::from_secs(3661)),
            "1h 1m"
        );
    }

    #[test]
    fn test_format_number() {
        assert_eq!(format_number(1000), "1,000");
        assert_eq!(format_number(1234567), "1,234,567");
        assert_eq!(format_number(123), "123");
    }

    #[test]
    fn test_truncate_string() {
        assert_eq!(truncate_string("hello world", 5), "he...");
        assert_eq!(truncate_string("hello", 10), "hello");
        assert_eq!(truncate_string("hi", 2), "hi");
    }

    #[test]
    fn test_pad_string() {
        assert_eq!(pad_string("test", 8, Alignment::Left), "test    ");
        assert_eq!(pad_string("test", 8, Alignment::Right), "    test");
        assert_eq!(pad_string("test", 8, Alignment::Center), "  test  ");
    }

    #[test]
    fn test_escape_csv_field() {
        assert_eq!(escape_csv_field("simple"), "simple");
        assert_eq!(escape_csv_field("with,comma"), "\"with,comma\"");
        assert_eq!(escape_csv_field("with\"quote"), "\"with\"\"quote\"");
    }
}
