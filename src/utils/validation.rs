//! Input validation utilities for security and data integrity

use regex::Regex;
use serde::{Deserialize, Serialize};

use url::Url;

/// Validation result with detailed error information
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ValidationResult {
    pub is_valid: bool,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
}

impl Default for ValidationResult {
    fn default() -> Self {
        Self::new()
    }
}

impl ValidationResult {
    pub fn new() -> Self {
        Self {
            is_valid: true,
            errors: Vec::new(),
            warnings: Vec::new(),
        }
    }

    pub fn add_error(&mut self, error: String) {
        self.is_valid = false;
        self.errors.push(error);
    }

    pub fn add_warning(&mut self, warning: String) {
        self.warnings.push(warning);
    }

    pub fn merge(&mut self, other: ValidationResult) {
        if !other.is_valid {
            self.is_valid = false;
        }
        self.errors.extend(other.errors);
        self.warnings.extend(other.warnings);
    }
}

/// Email validation
pub fn validate_email(email: &str) -> ValidationResult {
    let mut result = ValidationResult::new();

    if email.is_empty() {
        result.add_error("Email cannot be empty".to_string());
        return result;
    }

    let email_regex = Regex::new(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$").unwrap();

    if !email_regex.is_match(email) {
        result.add_error("Invalid email format".to_string());
    }

    if email.len() > 254 {
        result.add_error("Email too long (max 254 characters)".to_string());
    }

    result
}

/// URL validation
pub fn validate_url(url_str: &str) -> ValidationResult {
    let mut result = ValidationResult::new();

    if url_str.is_empty() {
        result.add_error("URL cannot be empty".to_string());
        return result;
    }

    match Url::parse(url_str) {
        Ok(url) => {
            if !["http", "https"].contains(&url.scheme()) {
                result.add_warning("URL should use HTTP or HTTPS scheme".to_string());
            }
        }
        Err(e) => {
            result.add_error(format!("Invalid URL format: {}", e));
        }
    }

    result
}

/// API key validation
pub fn validate_api_key(api_key: &str) -> ValidationResult {
    let mut result = ValidationResult::new();

    if api_key.is_empty() {
        result.add_error("API key cannot be empty".to_string());
        return result;
    }

    if api_key.len() < 16 {
        result.add_error("API key too short (minimum 16 characters)".to_string());
    }

    if api_key.len() > 128 {
        result.add_error("API key too long (maximum 128 characters)".to_string());
    }

    // Check for basic alphanumeric + some special characters
    let api_key_regex = Regex::new(r"^[a-zA-Z0-9\-_+=/.]+$").unwrap();
    if !api_key_regex.is_match(api_key) {
        result.add_error("API key contains invalid characters".to_string());
    }

    result
}

/// File path validation (security-focused)
pub fn validate_file_path(path: &str) -> ValidationResult {
    let mut result = ValidationResult::new();

    if path.is_empty() {
        result.add_error("File path cannot be empty".to_string());
        return result;
    }

    // Check for directory traversal attempts
    if path.contains("..") {
        result.add_error("Path contains directory traversal attempt".to_string());
    }

    // Check for absolute paths (might be security risk)
    if path.starts_with('/') || path.contains(':') {
        result.add_warning("Absolute paths may pose security risks".to_string());
    }

    // Check for null bytes
    if path.contains('\0') {
        result.add_error("Path contains null bytes".to_string());
    }

    // Check path length
    if path.len() > 4096 {
        result.add_error("Path too long (maximum 4096 characters)".to_string());
    }

    result
}

/// CVE ID validation
pub fn validate_cve_id(cve_id: &str) -> ValidationResult {
    let mut result = ValidationResult::new();

    if cve_id.is_empty() {
        result.add_error("CVE ID cannot be empty".to_string());
        return result;
    }

    let cve_regex = Regex::new(r"^CVE-\d{4}-\d{4,}$").unwrap();

    if !cve_regex.is_match(cve_id) {
        result.add_error("Invalid CVE ID format (expected CVE-YYYY-NNNN)".to_string());
    }

    result
}

/// Package name validation
pub fn validate_package_name(package_name: &str) -> ValidationResult {
    let mut result = ValidationResult::new();

    if package_name.is_empty() {
        result.add_error("Package name cannot be empty".to_string());
        return result;
    }

    if package_name.len() > 214 {
        result.add_error("Package name too long (maximum 214 characters)".to_string());
    }

    // Basic package name validation (covers most package managers)
    let package_regex = Regex::new(r"^[a-zA-Z0-9._@/-]+$").unwrap();
    if !package_regex.is_match(package_name) {
        result.add_error("Package name contains invalid characters".to_string());
    }

    result
}

/// Version string validation
pub fn validate_version(version: &str) -> ValidationResult {
    let mut result = ValidationResult::new();

    if version.is_empty() {
        result.add_error("Version cannot be empty".to_string());
        return result;
    }

    // Semantic versioning pattern
    let semver_regex = Regex::new(r"^(\d+)\.(\d+)\.(\d+)(?:-([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?(?:\+([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?$").unwrap();

    if !semver_regex.is_match(version) {
        result.add_warning("Version does not follow semantic versioning".to_string());
    }

    result
}

/// Hash validation (SHA-256, SHA-512, etc.)
pub fn validate_hash(hash: &str, expected_length: Option<usize>) -> ValidationResult {
    let mut result = ValidationResult::new();

    if hash.is_empty() {
        result.add_error("Hash cannot be empty".to_string());
        return result;
    }

    // Check if it's a valid hexadecimal string
    let hex_regex = Regex::new(r"^[a-fA-F0-9]+$").unwrap();
    if !hex_regex.is_match(hash) {
        result.add_error("Hash must be a valid hexadecimal string".to_string());
    }

    // Check length if specified
    if let Some(expected_len) = expected_length {
        if hash.len() != expected_len {
            result.add_error(format!(
                "Hash length mismatch (expected {}, got {})",
                expected_len,
                hash.len()
            ));
        }
    }

    result
}

/// IP address validation
pub fn validate_ip_address(ip: &str) -> ValidationResult {
    let mut result = ValidationResult::new();

    if ip.is_empty() {
        result.add_error("IP address cannot be empty".to_string());
        return result;
    }

    match ip.parse::<std::net::IpAddr>() {
        Ok(addr) => {
            if addr.is_loopback() {
                result.add_warning("IP address is a loopback address".to_string());
            }
            if addr.is_multicast() {
                result.add_warning("IP address is a multicast address".to_string());
            }
        }
        Err(_) => {
            result.add_error("Invalid IP address format".to_string());
        }
    }

    result
}

/// Port number validation
pub fn validate_port(port: u16) -> ValidationResult {
    let mut result = ValidationResult::new();

    if port == 0 {
        result.add_error("Port number cannot be 0".to_string());
    }

    if port < 1024 {
        result.add_warning("Port number is in the well-known range (< 1024)".to_string());
    }

    result
}

/// JSON validation
pub fn validate_json(json_str: &str) -> ValidationResult {
    let mut result = ValidationResult::new();

    if json_str.is_empty() {
        result.add_error("JSON string cannot be empty".to_string());
        return result;
    }

    match serde_json::from_str::<serde_json::Value>(json_str) {
        Ok(_) => {}
        Err(e) => {
            result.add_error(format!("Invalid JSON format: {}", e));
        }
    }

    result
}

/// Sanitize string for safe output
pub fn sanitize_string(input: &str) -> String {
    input
        .chars()
        .filter(|c| c.is_ascii_graphic() || c.is_ascii_whitespace())
        .collect()
}

/// Check for SQL injection patterns
pub fn check_sql_injection(input: &str) -> ValidationResult {
    let mut result = ValidationResult::new();

    let dangerous_patterns = [
        r"(?i)\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b",
        r"(?i)(\-\-|\#|\/\*|\*\/)",
        r"(?i)(\bor\b|\band\b).*(\=|\<|\>)",
        r"(?i)\b(script|javascript|vbscript)\b",
    ];

    for pattern in &dangerous_patterns {
        let regex = Regex::new(pattern).unwrap();
        if regex.is_match(input) {
            result.add_error("Input contains potentially dangerous SQL patterns".to_string());
            break;
        }
    }

    result
}

/// Validate multiple fields at once
pub fn validate_fields(validations: Vec<(&str, ValidationResult)>) -> ValidationResult {
    let mut combined_result = ValidationResult::new();

    for (field_name, field_result) in validations {
        if !field_result.is_valid {
            combined_result.is_valid = false;
            for error in field_result.errors {
                combined_result.add_error(format!("{}: {}", field_name, error));
            }
        }
        for warning in field_result.warnings {
            combined_result.add_warning(format!("{}: {}", field_name, warning));
        }
    }

    combined_result
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_validate_email() {
        assert!(validate_email("<EMAIL>").is_valid);
        assert!(!validate_email("invalid-email").is_valid);
        assert!(!validate_email("").is_valid);
    }

    #[test]
    fn test_validate_url() {
        assert!(validate_url("https://example.com").is_valid);
        assert!(!validate_url("not-a-url").is_valid);
        assert!(!validate_url("").is_valid);
    }

    #[test]
    fn test_validate_cve_id() {
        assert!(validate_cve_id("CVE-2023-1234").is_valid);
        assert!(!validate_cve_id("CVE-23-1234").is_valid);
        assert!(!validate_cve_id("invalid").is_valid);
    }

    #[test]
    fn test_validate_file_path() {
        assert!(validate_file_path("safe/path/file.txt").is_valid);
        assert!(!validate_file_path("../unsafe/path").is_valid);
        assert!(!validate_file_path("path\0with\0nulls").is_valid);
    }

    #[test]
    fn test_check_sql_injection() {
        assert!(check_sql_injection("normal input").is_valid);
        assert!(!check_sql_injection("'; DROP TABLE users; --").is_valid);
        assert!(!check_sql_injection("1 OR 1=1").is_valid);
    }
}
