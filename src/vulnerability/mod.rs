//! # Vulnerability Assessment Module
//!
//! This module provides comprehensive vulnerability assessment capabilities including
//! CVE matching, risk calculation, VEX processing, and integration with multiple
//! vulnerability databases (NVD, Snyk, GitHub Security Advisories, OSV).

pub mod cve_matcher;
pub mod nvd_client;
pub mod osv_client;
pub mod risk_calculator;
pub mod snyk_client;
pub mod vex_processor;

use crate::{config::VulnerabilityConfig, error::Result, scanners::SoftwareComponent};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

/// Re-export vulnerability types
pub use cve_matcher::{CveMatch, CveMatcher};
pub use nvd_client::{NvdClient, NvdVulnerability};
pub use osv_client::{OsvClient, OsvVulnerability};
pub use risk_calculator::{RiskCalculator, RiskScore};
pub use snyk_client::{SnykClient, SnykVulnerability};
pub use vex_processor::{VexDocument, VexProcessor};

/// Vulnerability assessment request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VulnerabilityRequest {
    /// Unique request identifier
    pub id: Uuid,
    /// Components to assess
    pub components: Vec<SoftwareComponent>,
    /// Assessment options
    pub options: AssessmentOptions,
    /// Additional metadata
    pub metadata: HashMap<String, String>,
}

/// Vulnerability assessment options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AssessmentOptions {
    /// Severity threshold (vulnerabilities below this are filtered out)
    pub severity_threshold: VulnerabilitySeverity,
    /// Include historical vulnerabilities
    pub include_historical: bool,
    /// Include proof-of-concept exploits
    pub include_poc_exploits: bool,
    /// Include EPSS (Exploit Prediction Scoring System) scores
    pub include_epss: bool,
    /// Maximum age of vulnerabilities to include (days)
    pub max_age_days: Option<u32>,
    /// Vulnerability sources to query
    pub sources: Vec<VulnerabilitySource>,
    /// Enable VEX processing
    pub enable_vex: bool,
}

/// Vulnerability sources
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
#[serde(rename_all = "lowercase")]
pub enum VulnerabilitySource {
    /// National Vulnerability Database
    Nvd,
    /// Snyk vulnerability database
    Snyk,
    /// GitHub Security Advisories
    Github,
    /// OSV (Open Source Vulnerabilities)
    Osv,
    /// RustSec Advisory Database
    Rustsec,
    /// npm Security Advisories
    Npm,
    /// PyPI Safety Database
    Pypi,
}

/// Vulnerability assessment result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VulnerabilityAssessment {
    /// Assessment request
    pub request: VulnerabilityRequest,
    /// Assessment status
    pub status: AssessmentStatus,
    /// Assessment timestamp
    pub assessed_at: chrono::DateTime<chrono::Utc>,
    /// Vulnerabilities found
    pub vulnerabilities: Vec<Vulnerability>,
    /// Risk assessment
    pub risk_assessment: RiskAssessment,
    /// VEX statements (if enabled)
    pub vex_statements: Vec<VexStatement>,
    /// Assessment summary
    pub summary: AssessmentSummary,
    /// Source attribution
    pub sources_used: Vec<VulnerabilitySource>,
}

/// Assessment status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum AssessmentStatus {
    /// Assessment in progress
    InProgress,
    /// Assessment completed successfully
    Completed,
    /// Assessment failed
    Failed,
    /// Assessment partially completed
    PartiallyCompleted,
}

/// Vulnerability information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Vulnerability {
    /// Vulnerability identifier (CVE, GHSA, etc.)
    pub id: String,
    /// Vulnerability aliases
    pub aliases: Vec<String>,
    /// Summary description
    pub summary: String,
    /// Detailed description
    pub details: Option<String>,
    /// Severity assessment
    pub severity: VulnerabilitySeverity,
    /// CVSS scores
    pub cvss_scores: Vec<CvssScore>,
    /// EPSS score (Exploit Prediction Scoring System)
    pub epss_score: Option<f64>,
    /// CWE (Common Weakness Enumeration) identifiers
    pub cwes: Vec<String>,
    /// Affected packages/components
    pub affected: Vec<AffectedPackage>,
    /// References and advisories
    pub references: Vec<VulnerabilityReference>,
    /// Publication date
    pub published: chrono::DateTime<chrono::Utc>,
    /// Last modified date
    pub modified: chrono::DateTime<chrono::Utc>,
    /// Withdrawn date (if applicable)
    pub withdrawn: Option<chrono::DateTime<chrono::Utc>>,
    /// Source of vulnerability information
    pub source: VulnerabilitySource,
    /// Exploit information
    pub exploits: Vec<ExploitInfo>,
    /// Patch information
    pub patches: Vec<PatchInfo>,
}

/// Vulnerability severity levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord, Hash)]
#[serde(rename_all = "lowercase")]
pub enum VulnerabilitySeverity {
    /// Informational
    Info,
    /// Low severity
    Low,
    /// Medium severity
    Medium,
    /// High severity
    High,
    /// Critical severity
    Critical,
    /// Unknown severity
    Unknown,
}

/// CVSS score information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CvssScore {
    /// CVSS version
    pub version: CvssVersion,
    /// Base score
    pub base_score: f64,
    /// Temporal score
    pub temporal_score: Option<f64>,
    /// Environmental score
    pub environmental_score: Option<f64>,
    /// Vector string
    pub vector_string: String,
    /// Severity rating
    pub severity: VulnerabilitySeverity,
}

/// CVSS versions
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum CvssVersion {
    /// CVSS v2.0
    V2,
    /// CVSS v3.0
    V3,
    /// CVSS v3.1
    V31,
    /// CVSS v4.0
    V4,
}

/// Affected package information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AffectedPackage {
    /// Package name
    pub package: String,
    /// Ecosystem (npm, cargo, pypi, etc.)
    pub ecosystem: String,
    /// Affected version ranges
    pub ranges: Vec<VersionRange>,
    /// Specific affected versions
    pub versions: Vec<String>,
    /// Database-specific information
    pub database_specific: HashMap<String, serde_json::Value>,
}

/// Version range specification
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VersionRange {
    /// Range type
    pub range_type: RangeType,
    /// Events (introduced, fixed, etc.)
    pub events: Vec<RangeEvent>,
}

/// Range types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "UPPERCASE")]
pub enum RangeType {
    /// Semantic versioning
    Semver,
    /// Git commit ranges
    Git,
    /// Ecosystem-specific versioning
    Ecosystem,
}

/// Range event
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RangeEvent {
    /// Event type
    pub event_type: EventType,
    /// Version
    pub version: String,
}

/// Range event types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum EventType {
    /// Vulnerability introduced
    Introduced,
    /// Vulnerability fixed
    Fixed,
    /// Last affected version
    LastAffected,
    /// Limit (upper bound)
    Limit,
}

/// Vulnerability reference
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VulnerabilityReference {
    /// Reference type
    pub reference_type: ReferenceType,
    /// URL
    pub url: String,
}

/// Reference types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "UPPERCASE")]
pub enum ReferenceType {
    /// Advisory
    Advisory,
    /// Article
    Article,
    /// Detection
    Detection,
    /// Discussion
    Discussion,
    /// Report
    Report,
    /// Fix
    Fix,
    /// Git commit
    Git,
    /// Package information
    Package,
    /// Evidence
    Evidence,
    /// Web page
    Web,
}

/// Exploit information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExploitInfo {
    /// Exploit type
    pub exploit_type: ExploitType,
    /// Exploit maturity
    pub maturity: ExploitMaturity,
    /// Public availability
    pub public: bool,
    /// Exploit URL/reference
    pub reference: Option<String>,
    /// Description
    pub description: Option<String>,
}

/// Exploit types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ExploitType {
    /// Proof of concept
    Poc,
    /// Functional exploit
    Functional,
    /// High-quality exploit
    High,
    /// Weaponized exploit
    Weaponized,
}

/// Exploit maturity levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ExploitMaturity {
    /// Unproven
    Unproven,
    /// Proof of concept
    ProofOfConcept,
    /// Functional
    Functional,
    /// High
    High,
    /// Not defined
    NotDefined,
}

/// Patch information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatchInfo {
    /// Fixed version
    pub fixed_version: String,
    /// Patch URL
    pub patch_url: Option<String>,
    /// Commit hash
    pub commit_hash: Option<String>,
    /// Patch description
    pub description: Option<String>,
}

/// Risk assessment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAssessment {
    /// Overall risk score (0-100)
    pub overall_risk_score: f64,
    /// Risk level
    pub risk_level: RiskLevel,
    /// Risk factors
    pub risk_factors: Vec<RiskFactor>,
    /// Exploitability assessment
    pub exploitability: ExploitabilityAssessment,
    /// Business impact assessment
    pub business_impact: BusinessImpactAssessment,
}

/// Risk levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum RiskLevel {
    /// Very low risk
    VeryLow,
    /// Low risk
    Low,
    /// Medium risk
    Medium,
    /// High risk
    High,
    /// Critical risk
    Critical,
}

/// Risk factor
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskFactor {
    /// Factor name
    pub name: String,
    /// Factor weight
    pub weight: f64,
    /// Factor score
    pub score: f64,
    /// Factor description
    pub description: String,
}

/// Exploitability assessment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExploitabilityAssessment {
    /// Exploitability score (0-10)
    pub score: f64,
    /// Public exploits available
    pub public_exploits: bool,
    /// Exploit complexity
    pub complexity: ExploitComplexity,
    /// Attack vector
    pub attack_vector: AttackVector,
}

/// Exploit complexity
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ExploitComplexity {
    /// Low complexity
    Low,
    /// High complexity
    High,
}

/// Attack vector
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum AttackVector {
    /// Network attack vector
    Network,
    /// Adjacent network
    Adjacent,
    /// Local attack vector
    Local,
    /// Physical attack vector
    Physical,
}

/// Business impact assessment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BusinessImpactAssessment {
    /// Impact score (0-10)
    pub score: f64,
    /// Confidentiality impact
    pub confidentiality_impact: ImpactLevel,
    /// Integrity impact
    pub integrity_impact: ImpactLevel,
    /// Availability impact
    pub availability_impact: ImpactLevel,
}

/// Impact levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ImpactLevel {
    /// No impact
    None,
    /// Low impact
    Low,
    /// High impact
    High,
}

/// VEX statement
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VexStatement {
    /// Vulnerability identifier
    pub vulnerability_id: String,
    /// Product identifier
    pub product_id: String,
    /// VEX status
    pub status: VexStatus,
    /// Justification
    pub justification: Option<VexJustification>,
    /// Impact statement
    pub impact_statement: Option<String>,
    /// Action statement
    pub action_statement: Option<String>,
    /// Timestamp
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// VEX status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum VexStatus {
    /// Not affected
    NotAffected,
    /// Affected
    Affected,
    /// Fixed
    Fixed,
    /// Under investigation
    UnderInvestigation,
}

/// VEX justification
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum VexJustification {
    /// Component not present
    ComponentNotPresent,
    /// Vulnerable code not present
    VulnerableCodeNotPresent,
    /// Vulnerable code not in execute path
    VulnerableCodeNotInExecutePath,
    /// Vulnerable code cannot be controlled by adversary
    VulnerableCodeCannotBeControlledByAdversary,
    /// Inline mitigations already exist
    InlineMitigationsAlreadyExist,
}

/// Assessment summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AssessmentSummary {
    /// Total vulnerabilities found
    pub total_vulnerabilities: u32,
    /// Vulnerabilities by severity
    pub by_severity: HashMap<VulnerabilitySeverity, u32>,
    /// Vulnerabilities by source
    pub by_source: HashMap<VulnerabilitySource, u32>,
    /// Components assessed
    pub components_assessed: u32,
    /// Components with vulnerabilities
    pub components_with_vulnerabilities: u32,
    /// Assessment duration
    pub assessment_duration: std::time::Duration,
}

/// Main vulnerability orchestrator
pub struct VulnerabilityOrchestrator {
    #[allow(dead_code)]
    config: VulnerabilityConfig,
    nvd_client: NvdClient,
    osv_client: OsvClient,
    snyk_client: SnykClient,
    #[allow(dead_code)]
    cve_matcher: CveMatcher,
    risk_calculator: RiskCalculator,
    vex_processor: VexProcessor,
}

impl VulnerabilityOrchestrator {
    /// Create new vulnerability orchestrator
    pub fn new(config: VulnerabilityConfig) -> Self {
        Self {
            nvd_client: NvdClient::new(&config),
            osv_client: OsvClient::new(&config),
            snyk_client: SnykClient::new(&config),
            cve_matcher: CveMatcher::new(&config),
            risk_calculator: RiskCalculator::new(&config),
            vex_processor: VexProcessor::new(&config),
            config,
        }
    }

    /// Perform vulnerability assessment
    pub async fn assess_vulnerabilities(
        &self,
        request: VulnerabilityRequest,
    ) -> Result<VulnerabilityAssessment> {
        let start_time = std::time::Instant::now();

        let mut assessment = VulnerabilityAssessment {
            request: request.clone(),
            status: AssessmentStatus::InProgress,
            assessed_at: chrono::Utc::now(),
            vulnerabilities: Vec::new(),
            risk_assessment: RiskAssessment {
                overall_risk_score: 0.0,
                risk_level: RiskLevel::Low,
                risk_factors: Vec::new(),
                exploitability: ExploitabilityAssessment {
                    score: 0.0,
                    public_exploits: false,
                    complexity: ExploitComplexity::High,
                    attack_vector: AttackVector::Network,
                },
                business_impact: BusinessImpactAssessment {
                    score: 0.0,
                    confidentiality_impact: ImpactLevel::None,
                    integrity_impact: ImpactLevel::None,
                    availability_impact: ImpactLevel::None,
                },
            },
            vex_statements: Vec::new(),
            summary: AssessmentSummary {
                total_vulnerabilities: 0,
                by_severity: HashMap::new(),
                by_source: HashMap::new(),
                components_assessed: request.components.len() as u32,
                components_with_vulnerabilities: 0,
                assessment_duration: std::time::Duration::from_secs(0),
            },
            sources_used: request.options.sources.clone(),
        };

        // Query vulnerability sources
        for source in &request.options.sources {
            match source {
                VulnerabilitySource::Nvd => {
                    let nvd_vulns = self
                        .nvd_client
                        .query_vulnerabilities(&request.components)
                        .await?;
                    assessment.vulnerabilities.extend(nvd_vulns);
                }
                VulnerabilitySource::Snyk => {
                    let snyk_vulns = self
                        .snyk_client
                        .query_vulnerabilities(&request.components)
                        .await?;
                    assessment.vulnerabilities.extend(snyk_vulns);
                }
                VulnerabilitySource::Osv => {
                    let osv_vulns = self
                        .osv_client
                        .query_vulnerabilities(&request.components)
                        .await?;
                    assessment.vulnerabilities.extend(osv_vulns);
                }
                _ => {
                    // TODO: Implement other sources
                }
            }
        }

        // Calculate risk assessment
        assessment.risk_assessment = self
            .risk_calculator
            .calculate_risk(&assessment.vulnerabilities)
            .await?;

        // Process VEX statements if enabled
        if request.options.enable_vex {
            assessment.vex_statements = self
                .vex_processor
                .process_vulnerabilities(&assessment.vulnerabilities)
                .await?;
        }

        // Calculate summary
        assessment.summary = self.calculate_summary(&assessment, start_time.elapsed());
        assessment.status = AssessmentStatus::Completed;

        Ok(assessment)
    }

    /// Calculate assessment summary
    fn calculate_summary(
        &self,
        assessment: &VulnerabilityAssessment,
        duration: std::time::Duration,
    ) -> AssessmentSummary {
        let mut by_severity = HashMap::new();
        let mut by_source = HashMap::new();

        for vuln in &assessment.vulnerabilities {
            *by_severity.entry(vuln.severity.clone()).or_insert(0) += 1;
            *by_source.entry(vuln.source.clone()).or_insert(0) += 1;
        }

        AssessmentSummary {
            total_vulnerabilities: assessment.vulnerabilities.len() as u32,
            by_severity,
            by_source,
            components_assessed: assessment.request.components.len() as u32,
            components_with_vulnerabilities: 0, // TODO: Calculate actual value
            assessment_duration: duration,
        }
    }
}

impl Default for AssessmentOptions {
    fn default() -> Self {
        Self {
            severity_threshold: VulnerabilitySeverity::Low,
            include_historical: true,
            include_poc_exploits: true,
            include_epss: true,
            max_age_days: None,
            sources: vec![
                VulnerabilitySource::Nvd,
                VulnerabilitySource::Github,
                VulnerabilitySource::Osv,
            ],
            enable_vex: true,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_vulnerability_severity_ordering() {
        assert!(VulnerabilitySeverity::Critical > VulnerabilitySeverity::High);
        assert!(VulnerabilitySeverity::High > VulnerabilitySeverity::Medium);
        assert!(VulnerabilitySeverity::Medium > VulnerabilitySeverity::Low);
        assert!(VulnerabilitySeverity::Low > VulnerabilitySeverity::Info);
    }

    #[test]
    fn test_assessment_options_default() {
        let options = AssessmentOptions::default();
        assert_eq!(options.severity_threshold, VulnerabilitySeverity::Low);
        assert!(options.include_historical);
        assert!(options.enable_vex);
        assert_eq!(options.sources.len(), 3);
    }

    #[test]
    fn test_vulnerability_source_serialization() {
        let source = VulnerabilitySource::Nvd;
        let serialized = serde_json::to_string(&source).unwrap();
        assert_eq!(serialized, "\"nvd\"");
    }
}
