//! OSV Client for vulnerability assessment
//!
//! This module provides an OSV client that integrates with the vulnerability assessment
//! framework to query vulnerabilities from the OSV database.

use crate::{
    config::VulnerabilityConfig,
    error::Result,
    scanners::{OsvScanner, OsvScannerImpl, SoftwareComponent},
    vulnerability::{Vulnerability, VulnerabilitySeverity, VulnerabilitySource},
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// OSV client for vulnerability queries
pub struct OsvClient {
    scanner: OsvScannerImpl,
}

impl OsvClient {
    /// Create a new OSV client
    pub fn new(config: &VulnerabilityConfig) -> Self {
        // Create a basic scanning config for the OSV scanner
        // In a real implementation, this might need more sophisticated config mapping
        let scanning_config = crate::config::ScanningConfig::default();
        let scanner = OsvScannerImpl::new(&scanning_config);

        Self { scanner }
    }

    /// Query vulnerabilities for the given components
    pub async fn query_vulnerabilities(
        &self,
        components: &[SoftwareComponent],
    ) -> Result<Vec<Vulnerability>> {
        let osv_result = self.scanner.scan_components(components).await?;

        let mut vulnerabilities = Vec::new();

        for osv_vuln in osv_result.vulnerabilities {
            let vulnerability = self.convert_osv_to_vulnerability(osv_vuln);
            vulnerabilities.push(vulnerability);
        }

        Ok(vulnerabilities)
    }

    /// Convert OSV vulnerability to internal Vulnerability format
    fn convert_osv_to_vulnerability(
        &self,
        osv_vuln: crate::scanners::OsvVulnerability,
    ) -> Vulnerability {
        let severity = match osv_vuln.severity.as_ref() {
            Some(sev) => match sev.to_lowercase().as_str() {
                "critical" => VulnerabilitySeverity::Critical,
                "high" => VulnerabilitySeverity::High,
                "medium" => VulnerabilitySeverity::Medium,
                "low" => VulnerabilitySeverity::Low,
                _ => VulnerabilitySeverity::Unknown,
            },
            None => VulnerabilitySeverity::Unknown,
        };

        let cvss_scores = osv_vuln.cvss_score.map(|score| {
            vec![crate::vulnerability::CvssScore {
                version: crate::vulnerability::CvssVersion::V31, // OSV typically uses CVSS v3.1
                base_score: score,
                temporal_score: None,
                environmental_score: None,
                severity,
            }]
        }).unwrap_or_default();

        let affected_packages = vec![crate::vulnerability::AffectedPackage {
            package: osv_vuln.package.name.clone(),
            ecosystem: osv_vuln.package.ecosystem.clone(),
            ranges: osv_vuln.affected_versions.into_iter().map(|version| {
                crate::vulnerability::VersionRange {
                    range_type: crate::vulnerability::RangeType::Semver,
                    events: vec![crate::vulnerability::RangeEvent {
                        event_type: crate::vulnerability::EventType::Introduced,
                        version,
                    }],
                }
            }).collect(),
            versions: osv_vuln.affected_versions,
            database_specific: HashMap::new(),
        }];

        let references = osv_vuln.references.into_iter().map(|url| {
            crate::vulnerability::VulnerabilityReference {
                reference_type: crate::vulnerability::ReferenceType::Advisory,
                url,
            }
        }).collect();

        Vulnerability {
            id: osv_vuln.id,
            aliases: Vec::new(), // OSV doesn't provide aliases in this format
            summary: osv_vuln.summary,
            details: osv_vuln.details,
            severity,
            cvss_scores,
            epss_score: None, // OSV doesn't provide EPSS scores
            cwes: Vec::new(), // OSV doesn't provide CWE in this format
            affected: affected_packages,
            references,
            published: osv_vuln.published,
            modified: osv_vuln.modified,
            withdrawn: None,
            source: VulnerabilitySource::Osv,
            exploits: Vec::new(), // OSV doesn't provide exploit info in this format
            patches: osv_vuln.fixed_versions.into_iter().map(|version| {
                crate::vulnerability::PatchInfo {
                    fixed_version: version,
                    patch_url: None,
                    commit_hash: None,
                    description: None,
                }
            }).collect(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::VulnerabilityConfig;

    #[tokio::test]
    async fn test_osv_client_creation() {
        let config = VulnerabilityConfig::default();
        let client = OsvClient::new(&config);

        // Basic smoke test - client should be created without errors
        assert!(true); // If we reach here, creation succeeded
    }
}