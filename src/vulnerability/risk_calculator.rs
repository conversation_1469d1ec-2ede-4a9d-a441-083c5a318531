use crate::{
    config::VulnerabilityConfig,
    error::Result,
    vulnerability::{
        AttackVector, BusinessImpactAssessment, ExploitComplexity, ExploitabilityAssessment,
        ImpactLevel, RiskAssessment, RiskFactor, RiskLevel, Vulnerability, VulnerabilitySeverity,
    },
};
use serde::{Deserialize, Serialize};

use tracing::{debug, info, instrument};

/// Risk calculator for vulnerability assessment
pub struct RiskCalculator {
    #[allow(dead_code)]
    config: VulnerabilityConfig,
    risk_weights: RiskWeights,
}

/// Risk calculation weights
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskWeights {
    /// CVSS base score weight
    pub cvss_weight: f64,
    /// Exploitability weight
    pub exploitability_weight: f64,
    /// Business impact weight
    pub business_impact_weight: f64,
    /// Asset criticality weight
    pub asset_criticality_weight: f64,
    /// Threat intelligence weight
    pub threat_intelligence_weight: f64,
}

/// Risk score breakdown
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>ize, Deserialize)]
pub struct RiskScore {
    /// Overall risk score (0-100)
    pub overall_score: f64,
    /// Risk level
    pub risk_level: RiskLevel,
    /// Component scores
    pub component_scores: RiskComponentScores,
    /// Risk factors
    pub risk_factors: Vec<RiskFactor>,
    /// Confidence in the assessment
    pub confidence: f64,
}

/// Individual component scores
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskComponentScores {
    /// CVSS-based score
    pub cvss_score: f64,
    /// Exploitability score
    pub exploitability_score: f64,
    /// Business impact score
    pub business_impact_score: f64,
    /// Asset criticality score
    pub asset_criticality_score: f64,
    /// Threat intelligence score
    pub threat_intelligence_score: f64,
}

/// Asset criticality levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum AssetCriticality {
    /// Critical business asset
    Critical,
    /// High importance asset
    High,
    /// Medium importance asset
    Medium,
    /// Low importance asset
    Low,
    /// Development/test asset
    Development,
}

/// Threat intelligence data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreatIntelligence {
    /// Active exploitation detected
    pub active_exploitation: bool,
    /// Exploit kits available
    pub exploit_kits: bool,
    /// Malware families using this vulnerability
    pub malware_families: Vec<String>,
    /// Threat actor groups exploiting
    pub threat_actors: Vec<String>,
    /// Geographic targeting
    pub geographic_targeting: Vec<String>,
    /// Industry targeting
    pub industry_targeting: Vec<String>,
}

/// Environmental factors affecting risk
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnvironmentalFactors {
    /// Network exposure
    pub network_exposure: NetworkExposure,
    /// Security controls in place
    pub security_controls: Vec<SecurityControl>,
    /// Compensating controls
    pub compensating_controls: Vec<String>,
    /// Asset criticality
    pub asset_criticality: AssetCriticality,
    /// Data sensitivity
    pub data_sensitivity: DataSensitivity,
}

/// Network exposure levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum NetworkExposure {
    /// Internet-facing
    Internet,
    /// DMZ
    Dmz,
    /// Internal network
    Internal,
    /// Isolated network
    Isolated,
    /// Air-gapped
    AirGapped,
}

/// Security control
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityControl {
    /// Control type
    pub control_type: SecurityControlType,
    /// Control effectiveness
    pub effectiveness: ControlEffectiveness,
    /// Control description
    pub description: String,
}

/// Security control types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum SecurityControlType {
    /// Web Application Firewall
    Waf,
    /// Intrusion Detection System
    Ids,
    /// Intrusion Prevention System
    Ips,
    /// Endpoint Detection and Response
    Edr,
    /// Network segmentation
    NetworkSegmentation,
    /// Access controls
    AccessControl,
    /// Encryption
    Encryption,
    /// Monitoring
    Monitoring,
    /// Patch management
    PatchManagement,
}

/// Control effectiveness levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ControlEffectiveness {
    /// Highly effective
    High,
    /// Moderately effective
    Medium,
    /// Low effectiveness
    Low,
    /// Ineffective
    None,
}

/// Data sensitivity levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum DataSensitivity {
    /// Highly sensitive (PII, financial, health)
    High,
    /// Moderately sensitive (business confidential)
    Medium,
    /// Low sensitivity (public information)
    Low,
    /// No sensitive data
    None,
}

impl RiskCalculator {
    /// Create new risk calculator
    pub fn new(config: &VulnerabilityConfig) -> Self {
        Self {
            config: config.clone(),
            risk_weights: RiskWeights::default(),
        }
    }

    /// Calculate risk for a list of vulnerabilities
    #[instrument(skip(self, vulnerabilities))]
    pub async fn calculate_risk(
        &self,
        vulnerabilities: &[Vulnerability],
    ) -> Result<RiskAssessment> {
        info!(
            "Calculating risk for {} vulnerabilities",
            vulnerabilities.len()
        );

        if vulnerabilities.is_empty() {
            return Ok(RiskAssessment {
                overall_risk_score: 0.0,
                risk_level: RiskLevel::VeryLow,
                risk_factors: Vec::new(),
                exploitability: ExploitabilityAssessment {
                    score: 0.0,
                    public_exploits: false,
                    complexity: ExploitComplexity::High,
                    attack_vector: AttackVector::Network,
                },
                business_impact: BusinessImpactAssessment {
                    score: 0.0,
                    confidentiality_impact: ImpactLevel::None,
                    integrity_impact: ImpactLevel::None,
                    availability_impact: ImpactLevel::None,
                },
            });
        }

        // Calculate individual risk scores
        let mut risk_scores = Vec::new();
        for vulnerability in vulnerabilities {
            let risk_score = self.calculate_vulnerability_risk(vulnerability).await?;
            risk_scores.push(risk_score);
        }

        // Calculate overall risk assessment
        let overall_risk_score = self.calculate_overall_risk_score(&risk_scores);
        let risk_level = self.determine_risk_level(overall_risk_score);
        let risk_factors = self.identify_risk_factors(vulnerabilities, &risk_scores);
        let exploitability = self.assess_exploitability(vulnerabilities);
        let business_impact = self.assess_business_impact(vulnerabilities);

        debug!(
            overall_risk_score = overall_risk_score,
            risk_level = ?risk_level,
            "Risk calculation completed"
        );

        Ok(RiskAssessment {
            overall_risk_score,
            risk_level,
            risk_factors,
            exploitability,
            business_impact,
        })
    }

    /// Calculate risk for a single vulnerability
    async fn calculate_vulnerability_risk(
        &self,
        vulnerability: &Vulnerability,
    ) -> Result<RiskScore> {
        // Get base CVSS score
        let cvss_score = vulnerability
            .cvss_scores
            .iter()
            .map(|score| score.base_score)
            .fold(0.0, f64::max);

        // Calculate component scores
        let component_scores = RiskComponentScores {
            cvss_score: self.normalize_cvss_score(cvss_score),
            exploitability_score: self.calculate_exploitability_score(vulnerability),
            business_impact_score: self.calculate_business_impact_score(vulnerability),
            asset_criticality_score: self.calculate_asset_criticality_score(),
            threat_intelligence_score: self.calculate_threat_intelligence_score(vulnerability),
        };

        // Calculate weighted overall score
        let overall_score = (component_scores.cvss_score * self.risk_weights.cvss_weight)
            + (component_scores.exploitability_score * self.risk_weights.exploitability_weight)
            + (component_scores.business_impact_score * self.risk_weights.business_impact_weight)
            + (component_scores.asset_criticality_score
                * self.risk_weights.asset_criticality_weight)
            + (component_scores.threat_intelligence_score
                * self.risk_weights.threat_intelligence_weight);

        let risk_level = self.determine_risk_level(overall_score);
        let risk_factors = self.generate_risk_factors(vulnerability, &component_scores);
        let confidence = self.calculate_confidence(vulnerability);

        Ok(RiskScore {
            overall_score,
            risk_level,
            component_scores,
            risk_factors,
            confidence,
        })
    }

    /// Normalize CVSS score to 0-100 scale
    fn normalize_cvss_score(&self, cvss_score: f64) -> f64 {
        (cvss_score / 10.0) * 100.0
    }

    /// Calculate exploitability score
    fn calculate_exploitability_score(&self, vulnerability: &Vulnerability) -> f64 {
        let mut score = 0.0;

        // Base exploitability from CVSS
        if let Some(cvss) = vulnerability.cvss_scores.first() {
            score += cvss.base_score * 5.0; // Scale to 0-50
        }

        // Public exploits available
        if !vulnerability.exploits.is_empty() {
            score += 30.0;
        }

        // EPSS score if available
        if let Some(epss) = vulnerability.epss_score {
            score += epss * 20.0; // Scale to 0-20
        }

        score.min(100.0)
    }

    /// Calculate business impact score
    fn calculate_business_impact_score(&self, vulnerability: &Vulnerability) -> f64 {
        let mut score: f32 = 0.0;

        // Base impact from severity
        match vulnerability.severity {
            VulnerabilitySeverity::Critical => score += 90.0,
            VulnerabilitySeverity::High => score += 70.0,
            VulnerabilitySeverity::Medium => score += 50.0,
            VulnerabilitySeverity::Low => score += 30.0,
            VulnerabilitySeverity::Info => score += 10.0,
            VulnerabilitySeverity::Unknown => score += 25.0,
        }

        // Adjust based on CWE types
        for cwe in &vulnerability.cwes {
            if self.is_high_impact_cwe(cwe) {
                score += 10.0;
            }
        }

        score.min(100.0) as f64
    }

    /// Calculate asset criticality score (placeholder - would be configurable)
    fn calculate_asset_criticality_score(&self) -> f64 {
        // Default to medium criticality
        50.0
    }

    /// Calculate threat intelligence score
    fn calculate_threat_intelligence_score(&self, vulnerability: &Vulnerability) -> f64 {
        let mut score: f32 = 0.0;

        // Check if vulnerability is being actively exploited
        // This would typically come from threat intelligence feeds

        // For now, use publication date as a proxy
        let days_since_publication = (chrono::Utc::now() - vulnerability.published).num_days();

        if days_since_publication < 30 {
            score += 20.0; // Recent vulnerabilities are higher risk
        } else if days_since_publication < 90 {
            score += 10.0;
        }

        // Check for known exploits
        if !vulnerability.exploits.is_empty() {
            score += 30.0;
        }

        score.min(100.0) as f64
    }

    /// Calculate overall risk score from individual scores
    fn calculate_overall_risk_score(&self, risk_scores: &[RiskScore]) -> f64 {
        if risk_scores.is_empty() {
            return 0.0;
        }

        // Use the highest individual risk score as the overall score
        // In practice, you might want a more sophisticated aggregation
        risk_scores
            .iter()
            .map(|score| score.overall_score)
            .fold(0.0, f64::max)
    }

    /// Determine risk level from score
    fn determine_risk_level(&self, score: f64) -> RiskLevel {
        match score {
            s if s >= 90.0 => RiskLevel::Critical,
            s if s >= 70.0 => RiskLevel::High,
            s if s >= 40.0 => RiskLevel::Medium,
            s if s >= 20.0 => RiskLevel::Low,
            _ => RiskLevel::VeryLow,
        }
    }

    /// Identify key risk factors
    fn identify_risk_factors(
        &self,
        vulnerabilities: &[Vulnerability],
        _risk_scores: &[RiskScore],
    ) -> Vec<RiskFactor> {
        let mut factors = Vec::new();

        // High severity vulnerabilities
        let critical_count = vulnerabilities
            .iter()
            .filter(|v| v.severity == VulnerabilitySeverity::Critical)
            .count();

        if critical_count > 0 {
            factors.push(RiskFactor {
                name: "Critical Vulnerabilities".to_string(),
                weight: 0.3,
                score: critical_count as f64 * 10.0,
                description: format!("{} critical severity vulnerabilities found", critical_count),
            });
        }

        // Public exploits available
        let exploits_count = vulnerabilities
            .iter()
            .filter(|v| !v.exploits.is_empty())
            .count();

        if exploits_count > 0 {
            factors.push(RiskFactor {
                name: "Public Exploits".to_string(),
                weight: 0.25,
                score: exploits_count as f64 * 15.0,
                description: format!("{} vulnerabilities have public exploits", exploits_count),
            });
        }

        // Recent vulnerabilities
        let recent_count = vulnerabilities
            .iter()
            .filter(|v| (chrono::Utc::now() - v.published).num_days() < 30)
            .count();

        if recent_count > 0 {
            factors.push(RiskFactor {
                name: "Recent Vulnerabilities".to_string(),
                weight: 0.2,
                score: recent_count as f64 * 8.0,
                description: format!(
                    "{} vulnerabilities published in the last 30 days",
                    recent_count
                ),
            });
        }

        factors
    }

    /// Assess overall exploitability
    fn assess_exploitability(&self, vulnerabilities: &[Vulnerability]) -> ExploitabilityAssessment {
        let public_exploits = vulnerabilities.iter().any(|v| !v.exploits.is_empty());

        let avg_cvss = vulnerabilities
            .iter()
            .filter_map(|v| v.cvss_scores.first())
            .map(|score| score.base_score)
            .sum::<f64>()
            / vulnerabilities.len() as f64;

        let complexity = if avg_cvss > 7.0 {
            ExploitComplexity::Low
        } else {
            ExploitComplexity::High
        };

        ExploitabilityAssessment {
            score: avg_cvss,
            public_exploits,
            complexity,
            attack_vector: AttackVector::Network, // Default assumption
        }
    }

    /// Assess business impact
    fn assess_business_impact(
        &self,
        vulnerabilities: &[Vulnerability],
    ) -> BusinessImpactAssessment {
        let high_impact_count = vulnerabilities
            .iter()
            .filter(|v| {
                matches!(
                    v.severity,
                    VulnerabilitySeverity::Critical | VulnerabilitySeverity::High
                )
            })
            .count();

        let impact_level = if high_impact_count > 0 {
            ImpactLevel::High
        } else {
            ImpactLevel::Low
        };

        BusinessImpactAssessment {
            score: (high_impact_count as f64 / vulnerabilities.len() as f64) * 10.0,
            confidentiality_impact: impact_level.clone(),
            integrity_impact: impact_level.clone(),
            availability_impact: impact_level,
        }
    }

    /// Generate risk factors for a vulnerability
    fn generate_risk_factors(
        &self,
        vulnerability: &Vulnerability,
        scores: &RiskComponentScores,
    ) -> Vec<RiskFactor> {
        let mut factors = Vec::new();

        if scores.cvss_score > 70.0 {
            factors.push(RiskFactor {
                name: "High CVSS Score".to_string(),
                weight: 0.3,
                score: scores.cvss_score,
                description: format!("CVSS base score: {:.1}", scores.cvss_score / 10.0),
            });
        }

        if !vulnerability.exploits.is_empty() {
            factors.push(RiskFactor {
                name: "Public Exploits Available".to_string(),
                weight: 0.25,
                score: 80.0,
                description: format!("{} public exploits found", vulnerability.exploits.len()),
            });
        }

        factors
    }

    /// Calculate confidence in the risk assessment
    fn calculate_confidence(&self, vulnerability: &Vulnerability) -> f64 {
        let mut confidence: f32 = 0.5; // Base confidence

        // Higher confidence if we have CVSS scores
        if !vulnerability.cvss_scores.is_empty() {
            confidence += 0.2;
        }

        // Higher confidence if we have detailed information
        if vulnerability.details.is_some() {
            confidence += 0.1;
        }

        // Higher confidence if vulnerability is not too old
        let days_old = (chrono::Utc::now() - vulnerability.published).num_days();
        if days_old < 365 {
            confidence += 0.1;
        }

        // Higher confidence if we have CWE information
        if !vulnerability.cwes.is_empty() {
            confidence += 0.1;
        }

        confidence.min(1.0) as f64
    }

    /// Check if CWE represents high business impact
    fn is_high_impact_cwe(&self, cwe: &str) -> bool {
        // Common high-impact CWEs
        matches!(
            cwe,
            "CWE-79" |  // Cross-site Scripting
            "CWE-89" |  // SQL Injection
            "CWE-78" |  // OS Command Injection
            "CWE-22" |  // Path Traversal
            "CWE-94" |  // Code Injection
            "CWE-502" | // Deserialization
            "CWE-611" // XML External Entity
        )
    }
}

impl Default for RiskWeights {
    fn default() -> Self {
        Self {
            cvss_weight: 0.3,
            exploitability_weight: 0.25,
            business_impact_weight: 0.2,
            asset_criticality_weight: 0.15,
            threat_intelligence_weight: 0.1,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_risk_level_determination() {
        let config = VulnerabilityConfig::default();
        let calculator = RiskCalculator::new(&config);

        assert_eq!(calculator.determine_risk_level(95.0), RiskLevel::Critical);
        assert_eq!(calculator.determine_risk_level(75.0), RiskLevel::High);
        assert_eq!(calculator.determine_risk_level(50.0), RiskLevel::Medium);
        assert_eq!(calculator.determine_risk_level(25.0), RiskLevel::Low);
        assert_eq!(calculator.determine_risk_level(10.0), RiskLevel::VeryLow);
    }

    #[test]
    fn test_cvss_normalization() {
        let config = VulnerabilityConfig::default();
        let calculator = RiskCalculator::new(&config);

        assert_eq!(calculator.normalize_cvss_score(10.0), 100.0);
        assert_eq!(calculator.normalize_cvss_score(5.0), 50.0);
        assert_eq!(calculator.normalize_cvss_score(0.0), 0.0);
    }

    #[test]
    fn test_high_impact_cwe() {
        let config = VulnerabilityConfig::default();
        let calculator = RiskCalculator::new(&config);

        assert!(calculator.is_high_impact_cwe("CWE-79"));
        assert!(calculator.is_high_impact_cwe("CWE-89"));
        assert!(!calculator.is_high_impact_cwe("CWE-999"));
    }
}
