use crate::{
    config::VulnerabilityConfig,
    error::Result,
    scanners::SoftwareComponent,
    vulnerability::{Vulnerability, VulnerabilitySeverity, VulnerabilitySource},
};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::time::{sleep, Duration};
use tracing::{debug, info, instrument, warn};

/// Snyk API client for vulnerability scanning
pub struct SnykClient {
    client: Client,
    api_token: Option<String>,
    base_url: String,
    org_id: Option<String>,
}

/// Snyk vulnerability response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SnykVulnerability {
    pub id: String,
    pub title: String,
    pub description: String,
    pub severity: String,
    #[serde(rename = "cvssScore")]
    pub cvss_score: Option<f64>,
    #[serde(rename = "CVSSv3")]
    pub cvss_v3: Option<String>,
    #[serde(rename = "identifiers")]
    pub identifiers: SnykIdentifiers,
    #[serde(rename = "packageName")]
    pub package_name: String,
    #[serde(rename = "packageManager")]
    pub package_manager: String,
    #[serde(rename = "publicationTime")]
    pub publication_time: chrono::DateTime<chrono::Utc>,
    #[serde(rename = "modificationTime")]
    pub modification_time: chrono::DateTime<chrono::Utc>,
    #[serde(rename = "disclosureTime")]
    pub disclosure_time: Option<chrono::DateTime<chrono::Utc>>,
    pub references: Vec<SnykReference>,
    #[serde(rename = "semver")]
    pub semver: SnykSemver,
    #[serde(rename = "exploit")]
    pub exploit: Option<String>,
    #[serde(rename = "patches")]
    pub patches: Vec<SnykPatch>,
}

/// Snyk identifiers
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SnykIdentifiers {
    #[serde(rename = "CVE")]
    pub cve: Vec<String>,
    #[serde(rename = "CWE")]
    pub cwe: Vec<String>,
    #[serde(rename = "GHSA")]
    pub ghsa: Vec<String>,
}

/// Snyk reference
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SnykReference {
    pub title: String,
    pub url: String,
}

/// Snyk semantic version information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SnykSemver {
    pub vulnerable: Vec<String>,
    #[serde(rename = "unaffected")]
    pub unaffected: Option<Vec<String>>,
}

/// Snyk patch information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SnykPatch {
    pub id: String,
    pub urls: Vec<String>,
    pub version: String,
    #[serde(rename = "modificationTime")]
    pub modification_time: chrono::DateTime<chrono::Utc>,
    pub comments: Vec<String>,
}

/// Snyk test response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SnykTestResponse {
    pub ok: bool,
    pub issues: SnykIssues,
    #[serde(rename = "dependencyCount")]
    pub dependency_count: u32,
    pub org: SnykOrg,
    #[serde(rename = "licensesPolicy")]
    pub licenses_policy: Option<serde_json::Value>,
    #[serde(rename = "packageManager")]
    pub package_manager: String,
}

/// Snyk issues
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SnykIssues {
    pub vulnerabilities: Vec<SnykVulnerability>,
    pub licenses: Vec<SnykLicense>,
}

/// Snyk license issue
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SnykLicense {
    pub id: String,
    pub title: String,
    pub license: String,
    #[serde(rename = "packageName")]
    pub package_name: String,
    pub severity: String,
}

/// Snyk organization
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SnykOrg {
    pub id: String,
    pub name: String,
}

impl SnykClient {
    /// Create new Snyk client
    pub fn new(_config: &VulnerabilityConfig) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .user_agent("infinitum-signal/0.1.0")
            .build()
            .expect("Failed to create HTTP client");

        Self {
            client,
            api_token: std::env::var("SNYK_TOKEN").ok(),
            base_url: "https://api.snyk.io/v1".to_string(),
            org_id: std::env::var("SNYK_ORG_ID").ok(),
        }
    }

    /// Query vulnerabilities for given components
    #[instrument(skip(self, components))]
    pub async fn query_vulnerabilities(
        &self,
        components: &[SoftwareComponent],
    ) -> Result<Vec<Vulnerability>> {
        if self.api_token.is_none() {
            warn!("Snyk API token not configured, skipping Snyk scan");
            return Ok(Vec::new());
        }

        info!("Querying Snyk for {} components", components.len());

        let mut vulnerabilities = Vec::new();

        for component in components {
            debug!("Querying Snyk for component: {}", component.name);

            match self.test_package(component).await {
                Ok(mut vulns) => vulnerabilities.append(&mut vulns),
                Err(e) => {
                    warn!("Failed to query Snyk for {}: {}", component.name, e);
                    continue;
                }
            }

            // Rate limiting - Snyk allows 2000 requests per hour
            sleep(Duration::from_millis(2000)).await;
        }

        info!("Found {} vulnerabilities from Snyk", vulnerabilities.len());
        Ok(vulnerabilities)
    }

    /// Test a package for vulnerabilities
    async fn test_package(&self, component: &SoftwareComponent) -> Result<Vec<Vulnerability>> {
        let package_manager = self.map_package_manager(&component.package_manager);

        let url = format!(
            "{}/test/{}/{}@{}",
            self.base_url, package_manager, component.name, component.version
        );

        let mut request = self.client.get(&url);

        if let Some(token) = &self.api_token {
            request = request.header("Authorization", format!("token {}", token));
        }

        if let Some(org_id) = &self.org_id {
            request = request.query(&[("org", org_id)]);
        }

        let response = request.send().await?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_default();
            return Err(crate::error::InfinitumError::SnykApi {
                message: format!("Snyk API error {}: {}", status, error_text),
            });
        }

        let snyk_response: SnykTestResponse = response.json().await?;

        let vulnerabilities = snyk_response
            .issues
            .vulnerabilities
            .into_iter()
            .map(|snyk_vuln| self.convert_snyk_vulnerability(snyk_vuln))
            .collect();

        Ok(vulnerabilities)
    }

    /// Convert Snyk vulnerability to internal format
    fn convert_snyk_vulnerability(&self, snyk_vuln: SnykVulnerability) -> Vulnerability {
        // Extract CVE IDs
        let cve_ids = snyk_vuln.identifiers.cve;
        let primary_id = cve_ids
            .first()
            .cloned()
            .unwrap_or_else(|| snyk_vuln.id.clone());

        // Map severity
        let severity = self.map_severity(&snyk_vuln.severity);

        // Create CVSS scores
        let mut cvss_scores = Vec::new();
        if let Some(score) = snyk_vuln.cvss_score {
            cvss_scores.push(crate::vulnerability::CvssScore {
                version: crate::vulnerability::CvssVersion::V31,
                base_score: score,
                temporal_score: None,
                environmental_score: None,
                vector_string: snyk_vuln.cvss_v3.unwrap_or_default(),
                severity: severity.clone(),
            });
        }

        // Extract references
        let references = snyk_vuln
            .references
            .into_iter()
            .map(|ref_item| crate::vulnerability::VulnerabilityReference {
                reference_type: crate::vulnerability::ReferenceType::Web,
                url: ref_item.url,
            })
            .collect();

        // Extract exploits
        let exploits = if snyk_vuln.exploit.is_some() {
            vec![crate::vulnerability::ExploitInfo {
                exploit_type: crate::vulnerability::ExploitType::Poc,
                maturity: crate::vulnerability::ExploitMaturity::ProofOfConcept,
                public: true,
                reference: snyk_vuln.exploit,
                description: Some("Exploit available via Snyk".to_string()),
            }]
        } else {
            Vec::new()
        };

        // Extract patches
        let patches = snyk_vuln
            .patches
            .into_iter()
            .map(|patch| crate::vulnerability::PatchInfo {
                fixed_version: patch.version,
                patch_url: patch.urls.first().cloned(),
                commit_hash: None,
                description: patch.comments.join("; ").into(),
            })
            .collect();

        // Create affected packages
        let affected = vec![crate::vulnerability::AffectedPackage {
            package: snyk_vuln.package_name,
            ecosystem: snyk_vuln.package_manager,
            ranges: snyk_vuln
                .semver
                .vulnerable
                .into_iter()
                .map(|version_range| crate::vulnerability::VersionRange {
                    range_type: crate::vulnerability::RangeType::Semver,
                    events: vec![crate::vulnerability::RangeEvent {
                        event_type: crate::vulnerability::EventType::Introduced,
                        version: version_range,
                    }],
                })
                .collect(),
            versions: Vec::new(),
            database_specific: HashMap::new(),
        }];

        Vulnerability {
            id: primary_id,
            aliases: cve_ids,
            summary: snyk_vuln.title,
            details: Some(snyk_vuln.description),
            severity,
            cvss_scores,
            epss_score: None,
            cwes: snyk_vuln.identifiers.cwe,
            affected,
            references,
            published: snyk_vuln.publication_time,
            modified: snyk_vuln.modification_time,
            withdrawn: None,
            source: VulnerabilitySource::Snyk,
            exploits,
            patches,
        }
    }

    /// Map package manager names
    fn map_package_manager(&self, package_manager: &str) -> &str {
        match package_manager.to_lowercase().as_str() {
            "cargo" => "cargo",
            "npm" => "npm",
            "pip" | "pypi" => "pip",
            "maven" => "maven",
            "gradle" => "gradle",
            "composer" => "composer",
            "nuget" => "nuget",
            "rubygems" => "rubygems",
            _ => "npm", // Default fallback
        }
    }

    /// Map Snyk severity to internal severity
    fn map_severity(&self, severity: &str) -> VulnerabilitySeverity {
        match severity.to_lowercase().as_str() {
            "critical" => VulnerabilitySeverity::Critical,
            "high" => VulnerabilitySeverity::High,
            "medium" => VulnerabilitySeverity::Medium,
            "low" => VulnerabilitySeverity::Low,
            _ => VulnerabilitySeverity::Unknown,
        }
    }

    /// Get organization information
    pub async fn get_organization(&self) -> Result<Option<SnykOrg>> {
        if self.api_token.is_none() {
            return Ok(None);
        }

        let url = format!("{}/orgs", self.base_url);

        let mut request = self.client.get(&url);

        if let Some(token) = &self.api_token {
            request = request.header("Authorization", format!("token {}", token));
        }

        let response = request.send().await?;

        if !response.status().is_success() {
            return Ok(None);
        }

        let orgs: Vec<SnykOrg> = response.json().await?;
        Ok(orgs.into_iter().next())
    }

    /// Monitor a project for new vulnerabilities
    pub async fn monitor_project(&self, project_path: &str) -> Result<String> {
        if self.api_token.is_none() {
            return Err(crate::error::InfinitumError::SnykApi {
                message: "Snyk API token not configured".to_string(),
            });
        }

        let url = format!("{}/monitor", self.base_url);

        let mut request = self.client.post(&url);

        if let Some(token) = &self.api_token {
            request = request.header("Authorization", format!("token {}", token));
        }

        if let Some(org_id) = &self.org_id {
            request = request.query(&[("org", org_id)]);
        }

        // TODO: Implement project monitoring payload
        let payload = serde_json::json!({
            "target": {
                "remoteUrl": project_path
            }
        });

        let response = request.json(&payload).send().await?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_default();
            return Err(crate::error::InfinitumError::SnykApi {
                message: format!("Snyk monitor API error {}: {}", status, error_text),
            });
        }

        let result: serde_json::Value = response.json().await?;
        Ok(result
            .get("id")
            .and_then(|id| id.as_str())
            .unwrap_or("unknown")
            .to_string())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::VulnerabilityConfig;

    #[test]
    fn test_snyk_client_creation() {
        let config = VulnerabilityConfig::default();
        let client = SnykClient::new(&config);
        assert_eq!(client.base_url, "https://api.snyk.io/v1");
    }

    #[test]
    fn test_package_manager_mapping() {
        let config = VulnerabilityConfig::default();
        let client = SnykClient::new(&config);

        assert_eq!(client.map_package_manager("cargo"), "cargo");
        assert_eq!(client.map_package_manager("npm"), "npm");
        assert_eq!(client.map_package_manager("pip"), "pip");
        assert_eq!(client.map_package_manager("pypi"), "pip");
        assert_eq!(client.map_package_manager("unknown"), "npm");
    }

    #[test]
    fn test_severity_mapping() {
        let config = VulnerabilityConfig::default();
        let client = SnykClient::new(&config);

        assert_eq!(
            client.map_severity("critical"),
            VulnerabilitySeverity::Critical
        );
        assert_eq!(client.map_severity("high"), VulnerabilitySeverity::High);
        assert_eq!(client.map_severity("medium"), VulnerabilitySeverity::Medium);
        assert_eq!(client.map_severity("low"), VulnerabilitySeverity::Low);
        assert_eq!(
            client.map_severity("unknown"),
            VulnerabilitySeverity::Unknown
        );
    }
}
