#!/usr/bin/env rust-script
//! ```cargo
//! [dependencies]
//! clap = { version = "4.0", features = ["derive"] }
//! tokio = { version = "1.0", features = ["full"] }
//! anyhow = "1.0"
//! ```

use anyhow::Result;
use clap::{Parser, Subcommand};
use std::path::PathBuf;
use std::process::Command;

#[derive(Parser)]
#[command(name = "infinitum-demo")]
#[command(about = "Infinitium Signal Demo - Showcase cybersecurity capabilities")]
#[command(version = "0.1.0")]
struct Cli {
    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// Run interactive demo
    Run {
        /// Target path to scan (optional)
        #[arg(value_name = "PATH")]
        target: Option<PathBuf>,
    },
    /// Generate SBOM for a project
    Sbom {
        /// Target path to scan
        #[arg(value_name = "PATH")]
        target: PathBuf,
        
        /// Output format
        #[arg(short, long, default_value = "cyclonedx-json")]
        format: String,
    },
    /// Scan for vulnerabilities
    Scan {
        /// Target path to scan
        #[arg(value_name = "PATH")]
        target: PathBuf,
        
        /// Output format
        #[arg(short, long, default_value = "json")]
        format: String,
    },
}

fn check_tool_availability(tool: &str) -> bool {
    Command::new(tool)
        .arg("--version")
        .output()
        .map(|output| output.status.success())
        .unwrap_or(false)
}

fn run_demo(target: Option<&PathBuf>) -> Result<()> {
    println!("🚀 Starting Infinitium Signal Demo");
    println!("📋 Platform: Infinitium Signal v0.1.0");
    println!("{}", "=".repeat(50));

    // Check tool availability
    println!("🔧 Checking external tools:");
    let tools = [
        ("Trivy", "trivy"),
        ("Syft", "syft"),
        ("Grype", "grype"),
    ];

    for (name, cmd) in &tools {
        if check_tool_availability(cmd) {
            println!("✅ {} is available", name);
        } else {
            println!("❌ {} is not available", name);
        }
    }
    println!();

    // Demo basic operations
    println!("📁 Demonstrating file operations:");
    let temp_dir = std::env::temp_dir();
    println!("✅ Temporary directory: {:?}", temp_dir);
    
    let current_dir = std::env::current_dir()?;
    println!("✅ Current directory: {:?}", current_dir);
    println!();

    // Demo crypto operations (simple)
    println!("🔐 Demonstrating cryptographic operations:");
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};
    
    let data = "demo-data";
    let mut hasher = DefaultHasher::new();
    data.hash(&mut hasher);
    let hash = hasher.finish();
    println!("✅ Generated hash for '{}': {:x}", data, hash);
    println!();

    // External tool integration (if path provided)
    if let Some(path) = target {
        if path.exists() {
            println!("🔍 External tool integration:");
            
            // SBOM generation
            if check_tool_availability("syft") {
                println!("📄 Generating SBOM...");
                match Command::new("syft")
                    .arg(path.to_string_lossy().as_ref())
                    .arg("-o")
                    .arg("cyclonedx-json")
                    .output()
                {
                    Ok(output) if output.status.success() => {
                        println!("✅ SBOM generated successfully ({} bytes)", output.stdout.len());
                    }
                    Ok(output) => {
                        let error = String::from_utf8_lossy(&output.stderr);
                        println!("⚠️  SBOM generation failed: {}", error.lines().next().unwrap_or("Unknown error"));
                    }
                    Err(e) => {
                        println!("❌ Failed to run Syft: {}", e);
                    }
                }
            }

            // Vulnerability scanning
            if check_tool_availability("trivy") {
                println!("🔍 Scanning for vulnerabilities...");
                match Command::new("trivy")
                    .arg("fs")
                    .arg("--format")
                    .arg("json")
                    .arg("--quiet")
                    .arg(path.to_string_lossy().as_ref())
                    .output()
                {
                    Ok(output) if output.status.success() => {
                        println!("✅ Vulnerability scan completed ({} bytes)", output.stdout.len());
                    }
                    Ok(output) => {
                        let error = String::from_utf8_lossy(&output.stderr);
                        println!("⚠️  Vulnerability scan failed: {}", error.lines().next().unwrap_or("Unknown error"));
                    }
                    Err(e) => {
                        println!("❌ Failed to run Trivy: {}", e);
                    }
                }
            }
            println!();
        } else {
            println!("⚠️  Target path does not exist: {}", path.display());
        }
    } else {
        println!("ℹ️  No target path provided, skipping external tool demos");
        println!("   Use: ./standalone_demo.rs run /path/to/scan");
    }

    println!("{}", "=".repeat(50));
    println!("🎉 Demo completed successfully!");
    println!();
    println!("🔧 Available tools:");
    for (name, cmd) in &tools {
        if check_tool_availability(cmd) {
            println!("   • {}: Available ✅", name);
        } else {
            println!("   • {}: Not installed ❌", name);
        }
    }
    println!();
    println!("📚 Next steps:");
    println!("   • Install missing tools if needed");
    println!("   • Run SBOM generation: ./standalone_demo.rs sbom /path/to/project");
    println!("   • Run vulnerability scan: ./standalone_demo.rs scan /path/to/project");
    println!("   • Set up full Infinitium Signal platform");

    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    let cli = Cli::parse();

    match cli.command {
        Commands::Run { target } => {
            run_demo(target.as_ref())?;
        }
        Commands::Sbom { target, format } => {
            println!("🔍 Generating SBOM for: {}", target.display());
            println!("📄 Format: {}", format);
            
            let output = Command::new("syft")
                .arg(target.to_string_lossy().as_ref())
                .arg("-o")
                .arg(&format)
                .output();
                
            match output {
                Ok(output) if output.status.success() => {
                    let sbom = String::from_utf8_lossy(&output.stdout);
                    println!("{}", sbom);
                }
                Ok(output) => {
                    let error = String::from_utf8_lossy(&output.stderr);
                    eprintln!("❌ SBOM generation failed: {}", error);
                    std::process::exit(1);
                }
                Err(e) => {
                    eprintln!("❌ Failed to run Syft: {}", e);
                    std::process::exit(1);
                }
            }
        }
        Commands::Scan { target, format } => {
            println!("🔍 Scanning for vulnerabilities: {}", target.display());
            println!("📄 Format: {}", format);
            
            let output = Command::new("trivy")
                .arg("fs")
                .arg("--format")
                .arg(&format)
                .arg(target.to_string_lossy().as_ref())
                .output();
                
            match output {
                Ok(output) if output.status.success() => {
                    let scan_result = String::from_utf8_lossy(&output.stdout);
                    println!("{}", scan_result);
                }
                Ok(output) => {
                    let error = String::from_utf8_lossy(&output.stderr);
                    eprintln!("❌ Vulnerability scan failed: {}", error);
                    std::process::exit(1);
                }
                Err(e) => {
                    eprintln!("❌ Failed to run Trivy: {}", e);
                    std::process::exit(1);
                }
            }
        }
    }

    Ok(())
}
