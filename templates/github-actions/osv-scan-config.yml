# OSV Scanner Configuration Template for GitHub Actions
# Copy this file to .github/workflows/ and customize as needed

name: OSV Vulnerability Scan

on:
  # Trigger on pull requests
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'Cargo.toml'
      - 'Cargo.lock'
      - 'src/**'
      - 'package.json'
      - 'package-lock.json'
      - 'requirements.txt'
      - 'Pipfile'
      - 'Pipfile.lock'
      - 'pyproject.toml'
      - 'poetry.lock'
      - '.github/workflows/**'

  # Trigger on pushes to main branches
  push:
    branches: [ main, develop ]
    paths:
      - 'Cargo.toml'
      - 'Cargo.lock'
      - 'src/**'
      - 'package.json'
      - 'package-lock.json'
      - 'requirements.txt'
      - 'Pipfile'
      - 'Pipfile.lock'
      - 'pyproject.toml'
      - 'poetry.lock'

  # Manual trigger
  workflow_dispatch:
    inputs:
      scan_target:
        description: 'Path to scan (default: .)'
        required: false
        default: '.'
        type: string
      fail_on_vulnerabilities:
        description: 'Fail build if vulnerabilities found'
        required: false
        default: true
        type: boolean

  # Scheduled scans (customize cron schedule as needed)
  schedule:
    # Run daily at 6 AM UTC
    - cron: '0 6 * * *'
    # Run weekly on Mondays at 6 AM UTC
    - cron: '0 6 * * 1'

# Environment variables
env:
  RUST_LOG: info
  SCAN_FORMAT: json
  FAIL_ON_VULNERABILITIES: ${{ github.event.inputs.fail_on_vulnerabilities != 'false' }}

jobs:
  osv-scan:
    name: OSV Vulnerability Scan
    runs-on: ubuntu-latest

    # Set permissions for security scanning
    permissions:
      contents: read
      pull-requests: write
      issues: write
      security-events: write

    strategy:
      matrix:
        # Add more runners if you want to test on multiple environments
        include:
          - rust-version: stable
            target: x86_64-unknown-linux-gnu

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        # For pull requests, fetch the base branch for comparison
        ref: ${{ github.event.pull_request.head.sha || github.sha }}

    - name: Setup Rust
      uses: dtolnay/rust-toolchain@stable
      with:
        targets: ${{ matrix.target }}

    - name: Cache Rust dependencies
      uses: Swatinem/rust-cache@v2
      with:
        workspaces: "./ -> target"

    - name: Setup Node.js (if needed)
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
      if: hashFiles('package.json') != ''

    - name: Setup Python (if needed)
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
      if: hashFiles('requirements.txt') != '' || hashFiles('pyproject.toml') != ''

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y jq curl build-essential pkg-config libssl-dev

    - name: Build project
      run: |
        if [ -f "Cargo.toml" ]; then
          echo "Building Rust project..."
          cargo build --release
        elif [ -f "package.json" ]; then
          echo "Installing Node.js dependencies..."
          npm ci
        elif [ -f "requirements.txt" ] || [ -f "pyproject.toml" ]; then
          echo "Installing Python dependencies..."
          pip install -r requirements.txt 2>/dev/null || pip install .
        fi

    - name: Extract dependency information
      id: deps
      run: |
        echo "Extracting dependency information..."

        DEP_COUNT=0
        if [ -f "Cargo.toml" ]; then
          DEP_COUNT=$(cargo metadata --format-version 1 2>/dev/null | jq '.packages[] | select(.name == "infinitum-signal") | .dependencies | length' 2>/dev/null || echo "0")
        elif [ -f "package.json" ]; then
          DEP_COUNT=$(jq '.dependencies // {} | keys | length' package.json 2>/dev/null || echo "0")
        fi

        echo "dependencies=$DEP_COUNT" >> $GITHUB_OUTPUT
        echo "Found $DEP_COUNT dependencies"

    - name: Run OSV Scan
      id: osv-scan
      run: |
        echo "## OSV Vulnerability Scan Results" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Repository:** ${{ github.repository }}" >> $GITHUB_STEP_SUMMARY
        echo "**Branch:** ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
        echo "**Commit:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "**Triggered by:** ${{ github.event_name }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        SCAN_TARGET="${{ github.event.inputs.scan_target || '.' }}"

        # Run the scan (customize this based on your scanner)
        if [ -f "./target/release/infinitum-signal" ]; then
          SCAN_OUTPUT=$(./target/release/infinitum-signal scan "$SCAN_TARGET" --format json 2>&1 || echo '{"error": "Scan failed"}')
        else
          # Fallback: use external OSV scanner
          SCAN_OUTPUT=$(curl -s "https://api.osv.dev/v1/query" -H "Content-Type: application/json" -d '{"package": {"name": "test"}}' || echo '{"error": "External scan failed"}')
        fi

        # Save scan results
        echo "$SCAN_OUTPUT" > osv_scan_results.json

        # Parse and format results
        if echo "$SCAN_OUTPUT" | jq -e '.error' >/dev/null 2>&1; then
          echo "❌ OSV scan failed: $(echo "$SCAN_OUTPUT" | jq -r '.error')" >> $GITHUB_STEP_SUMMARY
          echo "scan_failed=true" >> $GITHUB_OUTPUT
          echo "vuln_count=0" >> $GITHUB_OUTPUT
          echo "high_severity=0" >> $GITHUB_OUTPUT
        else
          VULN_COUNT=$(echo "$SCAN_OUTPUT" | jq '.vulnerabilities | length' 2>/dev/null || echo "0")
          HIGH_SEVERITY=$(echo "$SCAN_OUTPUT" | jq '[.vulnerabilities[] | select(.severity == "HIGH" or (.cvss_score // 0) >= 7.0)] | length' 2>/dev/null || echo "0")
          MEDIUM_SEVERITY=$(echo "$SCAN_OUTPUT" | jq '[.vulnerabilities[] | select(.severity == "MEDIUM" or ((.cvss_score // 0) >= 4.0 and (.cvss_score // 0) < 7.0))] | length' 2>/dev/null || echo "0")

          echo "🔍 Scan Summary:" >> $GITHUB_STEP_SUMMARY
          echo "- Dependencies scanned: ${{ steps.deps.outputs.dependencies }}" >> $GITHUB_STEP_SUMMARY
          echo "- Total vulnerabilities: $VULN_COUNT" >> $GITHUB_STEP_SUMMARY
          echo "- High severity: $HIGH_SEVERITY" >> $GITHUB_STEP_SUMMARY
          echo "- Medium severity: $MEDIUM_SEVERITY" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          if [ "$VULN_COUNT" -gt 0 ]; then
            echo "### 🚨 Vulnerabilities Found:" >> $GITHUB_STEP_SUMMARY
            echo "$SCAN_OUTPUT" | jq -r '.vulnerabilities[] | "- **\(.id)** (\(.severity // "Unknown")): \(.summary)"' >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
          else
            echo "✅ No vulnerabilities found" >> $GITHUB_STEP_SUMMARY
          fi

          echo "scan_failed=false" >> $GITHUB_OUTPUT
          echo "vuln_count=$VULN_COUNT" >> $GITHUB_OUTPUT
          echo "high_severity=$HIGH_SEVERITY" >> $GITHUB_OUTPUT
        fi

    - name: Upload scan results
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: osv-scan-results-${{ github.run_id }}
        path: osv_scan_results.json
        retention-days: 30

    - name: Comment on PR
      if: github.event_name == 'pull_request' && always()
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const summary = fs.readFileSync(process.env.GITHUB_STEP_SUMMARY, 'utf8');

          const body = `## 🔒 OSV Vulnerability Scan

${summary}

*Scan performed on: \`${process.env.GITHUB_SHA.substring(0, 7)}\`*
*Workflow: \`${process.env.GITHUB_WORKFLOW}\`*`;

          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: body
          });

    - name: Create security issue on high severity vulnerabilities
      if: steps.osv-scan.outputs.high_severity > 0
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const scanResults = JSON.parse(fs.readFileSync('osv_scan_results.json', 'utf8'));

          const highSeverityVulns = scanResults.vulnerabilities.filter(v =>
            v.severity === 'HIGH' || (v.cvss_score && v.cvss_score >= 7.0)
          );

          if (highSeverityVulns.length > 0) {
            const issueBody = `## 🚨 High Severity Vulnerabilities Detected

**Repository:** ${process.env.GITHUB_REPOSITORY}
**Branch:** ${process.env.GITHUB_REF_NAME}
**Commit:** ${process.env.GITHUB_SHA}
**Scan Date:** ${new Date().toISOString()}

### Summary
- Total high severity vulnerabilities: ${highSeverityVulns.length}
- Total vulnerabilities found: ${scanResults.vulnerabilities.length}

### High Severity Vulnerabilities
${highSeverityVulns.map(v => `#### ${v.id}
- **Severity:** ${v.severity || 'Unknown'}
- **CVSS Score:** ${v.cvss_score || 'N/A'}
- **Package:** ${v.package.name}@${v.package.version}
- **Summary:** ${v.summary}
- **Affected Versions:** ${v.affected_versions.join(', ') || 'N/A'}
- **Fixed Versions:** ${v.fixed_versions.join(', ') || 'N/A'}

`).join('')}

### Recommended Actions
1. Review the vulnerabilities listed above
2. Update affected dependencies to fixed versions
3. Test thoroughly after updates
4. Consider security implications for production deployment

### References
${highSeverityVulns.flatMap(v => v.references || []).map(ref => `- ${ref}`).join('\n')}

*This issue was automatically created by the OSV vulnerability scan workflow.*`;

            // Check if similar issue already exists
            const existingIssues = await github.rest.issues.listForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: ['security', 'vulnerability', 'osv-scan'],
              state: 'open'
            });

            const similarIssue = existingIssues.data.find(issue =>
              issue.title.includes('High Severity Vulnerabilities Detected') &&
              issue.body.includes(process.env.GITHUB_SHA.substring(0, 7))
            );

            if (!similarIssue) {
              await github.rest.issues.create({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: `🚨 High Severity Vulnerabilities Detected (${highSeverityVulns.length}) - ${process.env.GITHUB_SHA.substring(0, 7)}`,
                body: issueBody,
                labels: ['security', 'vulnerability', 'osv-scan', 'auto-generated']
              });
            }
          }

    - name: Fail build on vulnerabilities
      if: env.FAIL_ON_VULNERABILITIES == 'true' && steps.osv-scan.outputs.scan_failed == 'true'
      run: |
        echo "❌ Build failed due to OSV scan findings"
        echo "Review the scan results above and address any high severity vulnerabilities"
        exit 1

    - name: Success notification
      if: steps.osv-scan.outputs.scan_failed == 'false'
      run: |
        echo "✅ OSV vulnerability scan completed successfully"
        echo "No critical vulnerabilities found or all issues have been addressed"