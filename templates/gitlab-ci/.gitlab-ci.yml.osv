# OSV Vulnerability Scan GitLab CI/CD Template
# Copy this file to .gitlab-ci.yml or include it in your existing pipeline

stages:
  - build
  - test
  - security
  - deploy

variables:
  # OSV Scanner configuration
  OSV_SCAN_ENABLED: "true"
  OSV_SCAN_FORMAT: "json"
  OSV_SCAN_TIMEOUT: "1800"  # 30 minutes
  OSV_FAIL_ON_VULNERABILITIES: "true"
  OSV_MAX_VULNERABILITIES: "0"
  OSV_MAX_HIGH_SEVERITY: "0"

  # Build configuration
  CARGO_HOME: $CI_PROJECT_DIR/.cargo
  RUSTUP_HOME: $CI_PROJECT_DIR/.rustup
  DOCKER_DRIVER: overlay2

# Cache configuration
cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - .cargo/
    - target/
    - node_modules/
    - .npm/

# Template for OSV scanning job
.osv_scan_template: &osv_scan_template
  stage: security
  image: rust:1.70-slim
  before_script:
    - apt-get update && apt-get install -y jq curl git build-essential pkg-config libssl-dev ca-certificates
    - rustc --version
    - cargo --version
  dependencies:
    - build
  artifacts:
    reports:
      # SARIF format for GitLab Security tab
      sast: osv_scan_results.sarif
    paths:
      - osv_scan_results.json
      - osv_scan_report.html
    expire_in: 1 week
  only:
    - merge_requests
    - main
    - develop
  except:
    - tags

# Build job
build:
  stage: build
  image: rust:1.70-slim
  before_script:
    - apt-get update && apt-get install -y build-essential pkg-config libssl-dev
  script:
    - echo "Building project..."
    - cargo build --release
    - echo "Build completed successfully"
  artifacts:
    paths:
      - target/release/
    expire_in: 1 hour
  only:
    - merge_requests
    - main
    - develop

# Unit tests
test:unit:
  stage: test
  image: rust:1.70-slim
  before_script:
    - apt-get update && apt-get install -y build-essential pkg-config libssl-dev
  script:
    - echo "Running unit tests..."
    - cargo test --lib
    - echo "Unit tests completed"
  dependencies:
    - build
  coverage: '/^\d+\.\d+% coverage/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: cobertura.xml
      junit: target/test-results.xml
    paths:
      - target/test-results/
    expire_in: 1 week

# Integration tests
test:integration:
  stage: test
  image: rust:1.70-slim
  before_script:
    - apt-get update && apt-get install -y build-essential pkg-config libssl-dev
  script:
    - echo "Running integration tests..."
    - cargo test --test integration
    - echo "Integration tests completed"
  dependencies:
    - build
  artifacts:
    reports:
      junit: target/integration-test-results.xml
    paths:
      - target/test-results/
    expire_in: 1 week

# OSV Vulnerability Scan
osv_scan:
  <<: *osv_scan_template
  script:
    - echo "Starting OSV vulnerability scan..."
    - echo "Branch: $CI_COMMIT_REF_NAME"
    - echo "Commit: $CI_COMMIT_SHA"
    - echo "Pipeline: $CI_PIPELINE_ID"

    # Extract dependency information
    - |
      echo "Extracting dependencies..."
      cargo metadata --format-version 1 > cargo_metadata.json
      jq -r '.packages[] | select(.name == "infinitum-signal") | .dependencies[] | "\(.name) \(.version)"' cargo_metadata.json > dependencies.txt
      DEP_COUNT=$(wc -l < dependencies.txt)
      echo "Found $DEP_COUNT dependencies"

    # Run OSV scan
    - |
      echo "Running OSV scan..."
      SCAN_COMMAND="./target/release/infinitum-signal scan . --format $OSV_SCAN_FORMAT"
      echo "Command: $SCAN_COMMAND"

      timeout $OSV_SCAN_TIMEOUT bash -c "$SCAN_COMMAND > osv_scan_results.$OSV_SCAN_FORMAT 2>&1"
      SCAN_EXIT_CODE=$?
      echo "Scan exit code: $SCAN_EXIT_CODE"

    # Analyze results
    - |
      if [ "$OSV_SCAN_FORMAT" = "json" ]; then
        if [ -f osv_scan_results.json ]; then
          VULN_COUNT=$(jq '.vulnerabilities | length' osv_scan_results.json 2>/dev/null || echo "0")
          HIGH_SEVERITY=$(jq '[.vulnerabilities[] | select(.severity == "HIGH" or (.cvss_score // 0) >= 7.0)] | length' osv_scan_results.json 2>/dev/null || echo "0")
          MEDIUM_SEVERITY=$(jq '[.vulnerabilities[] | select(.severity == "MEDIUM" or ((.cvss_score // 0) >= 4.0 and (.cvss_score // 0) < 7.0))] | length' osv_scan_results.json 2>/dev/null || echo "0")

          echo "OSV Scan Results:"
          echo "  Dependencies scanned: $DEP_COUNT"
          echo "  Total vulnerabilities: $VULN_COUNT"
          echo "  High severity: $HIGH_SEVERITY"
          echo "  Medium severity: $MEDIUM_SEVERITY"

          # Export for use in other jobs
          echo "VULN_COUNT=$VULN_COUNT" >> osv_scan.env
          echo "HIGH_SEVERITY=$HIGH_SEVERITY" >> osv_scan.env
          echo "MEDIUM_SEVERITY=$MEDIUM_SEVERITY" >> osv_scan.env
        else
          echo "OSV scan results file not found"
          echo "VULN_COUNT=0" >> osv_scan.env
          echo "HIGH_SEVERITY=0" >> osv_scan.env
          echo "MEDIUM_SEVERITY=0" >> osv_scan.env
        fi
      fi

    # Generate SARIF report for GitLab Security tab
    - |
      echo "Generating SARIF report..."
      if [ -f osv_scan_results.json ]; then
        jq '{
          "version": "2.1.0",
          "$schema": "https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json",
          "runs": [
            {
              "tool": {
                "driver": {
                  "name": "OSV Scanner",
                  "version": "1.0.0",
                  "informationUri": "https://osv.dev"
                }
              },
              "results": [
                .vulnerabilities[]? | {
                  "ruleId": .id,
                  "level": (if .severity == "HIGH" then "error" elif .severity == "MEDIUM" then "warning" else "note" end),
                  "message": {
                    "text": .summary
                  },
                  "locations": [
                    {
                      "physicalLocation": {
                        "artifactLocation": {
                          "uri": "Cargo.toml"
                        }
                      }
                    }
                  ],
                  "properties": {
                    "cvssScore": .cvss_score,
                    "package": "\(.package.name)@\(.package.version)",
                    "ecosystem": .package.ecosystem
                  }
                }
              ]
            }
          ]
        }' osv_scan_results.json > osv_scan_results.sarif
      else
        # Create empty SARIF report
        echo '{
          "version": "2.1.0",
          "$schema": "https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json",
          "runs": []
        }' > osv_scan_results.sarif
      fi

    # Generate HTML report
    - |
      echo "Generating HTML report..."
      cat > osv_scan_report.html << 'EOF'
      <!DOCTYPE html>
      <html>
      <head>
          <title>OSV Vulnerability Scan Report - GitLab CI</title>
          <style>
              body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
              .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
              .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
              .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
              .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
              .summary-card h3 { margin: 0 0 10px 0; color: #333; }
              .summary-card .value { font-size: 2em; font-weight: bold; }
              .gitlab-info { background: #e9ecef; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
              .footer { margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: center; color: #666; }
          </style>
      </head>
      <body>
          <div class="container">
              <div class="header">
                  <h1>🔒 OSV Vulnerability Scan Report</h1>
                  <p>GitLab CI Pipeline</p>
                  <p>Generated: $(date)</p>
              </div>

              <div class="gitlab-info">
                  <h2>📋 Pipeline Information</h2>
                  <p><strong>Project:</strong> $CI_PROJECT_NAME</p>
                  <p><strong>Branch:</strong> $CI_COMMIT_REF_NAME</p>
                  <p><strong>Commit:</strong> $CI_COMMIT_SHA</p>
                  <p><strong>Pipeline:</strong> $CI_PIPELINE_ID</p>
                  <p><strong>Job:</strong> $CI_JOB_ID</p>
              </div>

              <div class="summary">
                  <div class="summary-card">
                      <h3>Dependencies Scanned</h3>
                      <div class="value">$DEP_COUNT</div>
                  </div>
                  <div class="summary-card">
                      <h3>Total Vulnerabilities</h3>
                      <div class="value">$VULN_COUNT</div>
                  </div>
                  <div class="summary-card">
                      <h3>High Severity</h3>
                      <div class="value" style="color: #dc3545;">$HIGH_SEVERITY</div>
                  </div>
                  <div class="summary-card">
                      <h3>Medium Severity</h3>
                      <div class="value" style="color: #ffc107;">$MEDIUM_SEVERITY</div>
                  </div>
              </div>
      EOF

      # Add vulnerability details
      if [ -f osv_scan_results.json ]; then
        echo '<h2>🚨 Vulnerabilities Found</h2>' >> osv_scan_report.html
        echo '<div class="vulnerabilities">' >> osv_scan_report.html

        jq -r '.vulnerabilities[]? | "<div class=\"vulnerability\"><h3>\(.id)</h3><p><strong>Severity:</strong> \(.severity // "Unknown")</p><p><strong>CVSS Score:</strong> \(.cvss_score // "N/A")</p><p><strong>Package:</strong> \(.package.name)@\(.package.version)</p><p><strong>Summary:</strong> \(.summary)</p></div>"' osv_scan_results.json >> osv_scan_report.html 2>/dev/null || echo '<p>No detailed vulnerability information available</p>' >> osv_scan_report.html

        echo '</div>' >> osv_scan_report.html
      fi

      cat >> osv_scan_report.html << 'EOF'
              <div class="footer">
                  <p>Report generated by OSV GitLab CI Pipeline</p>
                  <p>Infinitium Signal - Enterprise Cyber-Compliance Platform</p>
              </div>
          </div>
      </body>
      </html>
      EOF

    # Quality gate check
    - |
      echo "Running quality gate checks..."
      if [ "$OSV_FAIL_ON_VULNERABILITIES" = "true" ]; then
        if [ "$VULN_COUNT" -gt "$OSV_MAX_VULNERABILITIES" ] 2>/dev/null; then
          echo "❌ Quality gate failed: Total vulnerabilities ($VULN_COUNT) exceed limit ($OSV_MAX_VULNERABILITIES)"
          exit 1
        fi

        if [ "$HIGH_SEVERITY" -gt "$OSV_MAX_HIGH_SEVERITY" ] 2>/dev/null; then
          echo "❌ Quality gate failed: High severity vulnerabilities ($HIGH_SEVERITY) exceed limit ($OSV_MAX_HIGH_SEVERITY)"
          exit 1
        fi

        echo "✅ Quality gate passed"
      else
        echo "⚠️ Quality gate checks disabled"
      fi

  environment:
    name: security
  dependencies:
    - build

# Scheduled OSV scan (runs daily)
osv_scan:scheduled:
  <<: *osv_scan_template
  only:
    - schedules
  script:
    - echo "Running scheduled OSV scan..."
    - echo "Schedule: $CI_PIPELINE_SOURCE"

    # Run the same scan as regular job
    - ./target/release/infinitum-signal scan . --format json > osv_scan_results.json

    # Create GitLab issue if high severity vulnerabilities found
    - |
      if [ -f osv_scan_results.json ]; then
        HIGH_COUNT=$(jq '[.vulnerabilities[] | select(.severity == "HIGH" or (.cvss_score // 0) >= 7.0)] | length' osv_scan_results.json 2>/dev/null || echo "0")

        if [ "$HIGH_COUNT" -gt 0 ]; then
          echo "Creating GitLab issue for high severity vulnerabilities..."

          # Check if similar issue already exists
          EXISTING_ISSUE=$(curl -s -H "PRIVATE-TOKEN: $GITLAB_API_TOKEN" \
            "$CI_API_V4_URL/projects/$CI_PROJECT_ID/issues?labels=security,vulnerability,osv-scan&state=opened" | \
            jq -r '.[0].iid // empty')

          if [ -z "$EXISTING_ISSUE" ]; then
            # Create new issue
            curl -X POST -H "PRIVATE-TOKEN: $GITLAB_API_TOKEN" \
              -H "Content-Type: application/json" \
              -d "{
                \"title\": \"🚨 High Severity Vulnerabilities Detected ($HIGH_COUNT)\",
                \"description\": \"High severity vulnerabilities detected in OSV scan. See pipeline $CI_PIPELINE_ID for details.\",
                \"labels\": \"security,vulnerability,osv-scan\"
              }" \
              "$CI_API_V4_URL/projects/$CI_PROJECT_ID/issues"
          else
            # Add comment to existing issue
            curl -X POST -H "PRIVATE-TOKEN: $GITLAB_API_TOKEN" \
              -H "Content-Type: application/json" \
              -d "{\"body\": \"New scan detected $HIGH_COUNT high severity vulnerabilities. Pipeline: $CI_PIPELINE_ID\"}" \
              "$CI_API_V4_URL/projects/$CI_PROJECT_ID/issues/$EXISTING_ISSUE/notes"
          fi
        fi
      fi

# Docker-based OSV scan (alternative approach)
osv_scan:docker:
  stage: security
  image: docker:latest
  services:
    - docker:dind
  script:
    - echo "Running OSV scan using Docker..."

    # Build scanner image
    - docker build -f docker/Dockerfile.osv -t osv-scanner .

    # Run scan in container
    - |
      docker run --rm \
        -v $(pwd):/scan \
        -e RUST_LOG=info \
        osv-scanner \
        scan /scan --format json > osv_docker_results.json

    # Process results
    - |
      if [ -f osv_docker_results.json ]; then
        VULN_COUNT=$(jq '.vulnerabilities | length' osv_docker_results.json 2>/dev/null || echo "0")
        echo "Docker-based scan found $VULN_COUNT vulnerabilities"
      fi

  artifacts:
    paths:
      - osv_docker_results.json
    expire_in: 1 week
  only:
    - main
    - develop

# Deploy job (only runs if security checks pass)
deploy:
  stage: deploy
  script:
    - echo "Deploying application..."
    - echo "All security checks passed"
  dependencies:
    - osv_scan
  only:
    - main
  when: manual