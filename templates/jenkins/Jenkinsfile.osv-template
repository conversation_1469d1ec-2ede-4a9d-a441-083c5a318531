#!/usr/bin/env groovy

/**
 * OSV Vulnerability Scan Jenkins Pipeline Template
 *
 * This template provides a comprehensive Jenkins pipeline for OSV vulnerability scanning.
 * Customize the parameters and stages according to your project requirements.
 */

pipeline {
    agent {
        // Choose appropriate agent based on your infrastructure
        docker {
            image 'rust:1.70-slim'
            args '-v /var/run/docker.sock:/var/run/docker.sock -u root'
            reuseNode true
        }
    }

    parameters {
        // Basic scan parameters
        choice(name: 'SCAN_TYPE', choices: ['full', 'incremental', 'dependency-only', 'security-only'],
               description: 'Type of OSV scan to perform')
        string(name: 'BRANCH_NAME', defaultValue: 'main',
               description: 'Branch to scan (leave empty for current branch)')
        string(name: 'SCAN_PATH', defaultValue: '.',
               description: 'Path to scan within the workspace')

        // Build and environment parameters
        choice(name: 'BUILD_TOOL', choices: ['cargo', 'npm', 'pip', 'maven', 'gradle', 'auto'],
               description: 'Build tool to use')
        string(name: 'BUILD_COMMAND', defaultValue: '',
               description: 'Custom build command (overrides BUILD_TOOL)')

        // Scan configuration
        choice(name: 'SCAN_FORMAT', choices: ['json', 'sarif', 'text'],
               description: 'Output format for scan results')
        string(name: 'TIMEOUT_MINUTES', defaultValue: '30',
               description: 'Scan timeout in minutes')

        // Quality gates
        booleanParam(name: 'FAIL_ON_VULNERABILITIES', defaultValue: true,
                    description: 'Fail build if vulnerabilities are found')
        string(name: 'MAX_ALLOWED_VULNERABILITIES', defaultValue: '0',
               description: 'Maximum allowed vulnerabilities (0 = no limit)')
        string(name: 'MAX_ALLOWED_HIGH_SEVERITY', defaultValue: '0',
               description: 'Maximum allowed high severity vulnerabilities')

        // Reporting and notifications
        booleanParam(name: 'GENERATE_HTML_REPORT', defaultValue: true,
                    description: 'Generate HTML report')
        booleanParam(name: 'PUBLISH_TO_GIT', defaultValue: false,
                    description: 'Publish results to Git repository')
        string(name: 'NOTIFICATION_EMAIL', defaultValue: '',
               description: 'Email addresses for notifications (comma-separated)')
        string(name: 'SLACK_CHANNEL', defaultValue: '',
               description: 'Slack channel for notifications')

        // Advanced options
        booleanParam(name: 'ENABLE_CACHING', defaultValue: true,
                    description: 'Enable result caching')
        booleanParam(name: 'VERBOSE_LOGGING', defaultValue: false,
                    description: 'Enable verbose logging')
        string(name: 'ADDITIONAL_ARGS', defaultValue: '',
               description: 'Additional arguments for the scanner')
    }

    environment {
        // Environment configuration
        RUST_LOG = "${params.VERBOSE_LOGGING ? 'debug' : 'info'}"
        SCAN_FORMAT = "${params.SCAN_FORMAT}"
        BUILD_TOOL = "${params.BUILD_TOOL}"

        // Derived environment variables
        SCAN_BRANCH = "${params.BRANCH_NAME ?: env.BRANCH_NAME ?: 'main'}"
        WORKSPACE_PATH = "${WORKSPACE}/${params.SCAN_PATH}"
        RESULTS_DIR = "${WORKSPACE}/osv-results-${BUILD_NUMBER}"
        REPORTS_DIR = "${WORKSPACE}/reports"

        // Tool paths
        CARGO_HOME = '/usr/local/cargo'
        RUSTUP_HOME = '/usr/local/rustup'
        PATH = "${CARGO_HOME}/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
    }

    options {
        // Pipeline options
        timeout(time: Integer.parseInt(params.TIMEOUT_MINUTES), unit: 'MINUTES')
        disableConcurrentBuilds()
        ansiColor('xterm')

        // Build retention
        buildDiscarder(logRotator(
            numToKeepStr: '10',
            daysToKeepStr: '30',
            artifactNumToKeepStr: '5',
            artifactDaysToKeepStr: '30'
        ))
    }

    stages {
        stage('Checkout and Setup') {
            steps {
                script {
                    // Clean workspace
                    cleanWs()

                    // Checkout specified branch
                    if (params.BRANCH_NAME && params.BRANCH_NAME != env.BRANCH_NAME) {
                        checkout([
                            $class: 'GitSCM',
                            branches: [[name: "*/${params.BRANCH_NAME}"]],
                            doGenerateSubmoduleConfigurations: false,
                            extensions: [[$class: 'CleanCheckout']],
                            submoduleCfg: [],
                            userRemoteConfigs: [[
                                credentialsId: 'github-credentials',
                                url: env.GIT_URL
                            ]]
                        ])
                    } else {
                        checkout scm
                    }

                    // Create results directories
                    sh """
                        mkdir -p "${RESULTS_DIR}"
                        mkdir -p "${REPORTS_DIR}"
                        mkdir -p "${WORKSPACE}/artifacts"
                    """

                    // Log environment information
                    sh '''
                        echo "=== Environment Information ==="
                        echo "Jenkins Build: $BUILD_NUMBER"
                        echo "Branch: $SCAN_BRANCH"
                        echo "Commit: $(git rev-parse HEAD)"
                        echo "Scan Path: $WORKSPACE_PATH"
                        echo "Results Dir: $RESULTS_DIR"
                        echo "Build Tool: $BUILD_TOOL"
                        echo "================================"
                    '''
                }
            }
        }

        stage('Install Dependencies') {
            steps {
                script {
                    // Install system dependencies
                    sh '''
                        apt-get update && apt-get install -y \
                            jq \
                            curl \
                            git \
                            build-essential \
                            pkg-config \
                            libssl-dev \
                            ca-certificates \
                            gnupg \
                            lsb-release
                    '''

                    // Install language-specific tools based on project type
                    if (fileExists('Cargo.toml')) {
                        sh '''
                            echo "Rust project detected"
                            rustc --version || echo "Rust not installed"
                            cargo --version || echo "Cargo not installed"
                        '''
                    }

                    if (fileExists('package.json')) {
                        sh '''
                            echo "Node.js project detected"
                            curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
                            apt-get install -y nodejs
                            npm --version
                        '''
                    }

                    if (fileExists('requirements.txt') || fileExists('pyproject.toml') || fileExists('Pipfile')) {
                        sh '''
                            echo "Python project detected"
                            apt-get install -y python3 python3-pip python3-venv
                            python3 --version
                            pip3 --version
                        '''
                    }

                    if (fileExists('pom.xml')) {
                        sh '''
                            echo "Maven project detected"
                            apt-get install -y maven
                            mvn --version
                        '''
                    }

                    if (fileExists('build.gradle') || fileExists('build.gradle.kts')) {
                        sh '''
                            echo "Gradle project detected"
                            # Gradle wrapper should be committed to repo
                            ./gradlew --version || echo "Gradle wrapper not found"
                        '''
                    }
                }
            }
        }

        stage('Build Project') {
            steps {
                script {
                    dir(params.SCAN_PATH) {
                        if (params.BUILD_COMMAND) {
                            // Use custom build command
                            sh """
                                echo "Using custom build command: ${params.BUILD_COMMAND}"
                                ${params.BUILD_COMMAND}
                            """
                        } else {
                            // Auto-detect build tool
                            switch(params.BUILD_TOOL) {
                                case 'cargo':
                                    sh '''
                                        echo "Building with Cargo..."
                                        cargo build --release
                                    '''
                                    break
                                case 'npm':
                                    sh '''
                                        echo "Building with NPM..."
                                        npm ci
                                        npm run build 2>/dev/null || echo "No build script found"
                                    '''
                                    break
                                case 'pip':
                                    sh '''
                                        echo "Installing Python dependencies..."
                                        pip install -r requirements.txt 2>/dev/null || echo "No requirements.txt found"
                                        pip install . 2>/dev/null || echo "No setup.py found"
                                    '''
                                    break
                                case 'maven':
                                    sh '''
                                        echo "Building with Maven..."
                                        mvn clean compile
                                    '''
                                    break
                                case 'gradle':
                                    sh '''
                                        echo "Building with Gradle..."
                                        ./gradlew build
                                    '''
                                    break
                                case 'auto':
                                default:
                                    // Auto-detection logic
                                    sh '''
                                        if [ -f "Cargo.toml" ]; then
                                            echo "Auto-detected Rust project"
                                            cargo build --release
                                        elif [ -f "package.json" ]; then
                                            echo "Auto-detected Node.js project"
                                            npm ci
                                            npm run build 2>/dev/null || echo "No build script found"
                                        elif [ -f "requirements.txt" ]; then
                                            echo "Auto-detected Python project"
                                            pip install -r requirements.txt
                                        elif [ -f "pom.xml" ]; then
                                            echo "Auto-detected Maven project"
                                            mvn clean compile
                                        elif [ -f "build.gradle" ]; then
                                            echo "Auto-detected Gradle project"
                                            ./gradlew build
                                        else
                                            echo "No build system detected, skipping build"
                                        fi
                                    '''
                                    break
                            }
                        }
                    }
                }
            }
        }

        stage('Extract Dependencies') {
            steps {
                script {
                    dir(params.SCAN_PATH) {
                        sh """
                            echo "Extracting dependency information..."
                            mkdir -p "${RESULTS_DIR}/dependencies"
                        """

                        // Extract dependencies based on project type
                        switch(params.BUILD_TOOL) {
                            case 'cargo':
                                sh """
                                    cargo metadata --format-version 1 > "${RESULTS_DIR}/dependencies/cargo_metadata.json" || echo "Failed to extract Cargo dependencies"
                                    jq -r '.packages[] | select(.name != null) | .dependencies[] | select(.name != null) | "\(.name) \(.version)"' "${RESULTS_DIR}/dependencies/cargo_metadata.json" > "${RESULTS_DIR}/dependencies/dependencies.txt" 2>/dev/null || echo "Failed to parse dependencies"
                                """
                                break
                            case 'npm':
                                sh """
                                    npm ls --depth=0 --json > "${RESULTS_DIR}/dependencies/npm_dependencies.json" 2>/dev/null || echo "Failed to extract NPM dependencies"
                                    jq -r '.dependencies // {} | keys[]' "${RESULTS_DIR}/dependencies/npm_dependencies.json" > "${RESULTS_DIR}/dependencies/dependencies.txt" 2>/dev/null || echo "Failed to parse dependencies"
                                """
                                break
                            case 'pip':
                                sh """
                                    pip freeze > "${RESULTS_DIR}/dependencies/requirements.txt" 2>/dev/null || echo "Failed to extract Python dependencies"
                                    grep -E "^[a-zA-Z0-9_-]+==" "${RESULTS_DIR}/dependencies/requirements.txt" > "${RESULTS_DIR}/dependencies/dependencies.txt" 2>/dev/null || echo "No pinned dependencies found"
                                """
                                break
                            default:
                                sh """
                                    echo "Dependency extraction not implemented for ${params.BUILD_TOOL}" > "${RESULTS_DIR}/dependencies/dependencies.txt"
                                """
                                break
                        }

                        // Count dependencies
                        def depCount = sh(script: "wc -l < '${RESULTS_DIR}/dependencies/dependencies.txt' 2>/dev/null || echo '0'", returnStdout: true).trim()
                        env.DEP_COUNT = depCount
                        echo "Found ${depCount} dependencies to scan"
                    }
                }
            }
        }

        stage('Run OSV Scan') {
            steps {
                script {
                    dir(params.SCAN_PATH) {
                        def scanCommand = ""

                        // Determine scan command based on available tools
                        if (fileExists('target/release/infinitum-signal')) {
                            // Use built binary
                            scanCommand = "./target/release/infinitum-signal scan . --format ${params.SCAN_FORMAT}"
                        } else {
                            // Use external OSV scanner or fallback
                            scanCommand = "echo '{\\"error\\": \\"No OSV scanner available\\"}'"
                        }

                        // Add additional arguments
                        if (params.ADDITIONAL_ARGS) {
                            scanCommand += " ${params.ADDITIONAL_ARGS}"
                        }

                        // Execute scan with timeout
                        try {
                            timeout(time: Integer.parseInt(params.TIMEOUT_MINUTES), unit: 'MINUTES') {
                                sh """
                                    echo "Starting OSV scan..."
                                    echo "Command: ${scanCommand}"
                                    echo "Timeout: ${params.TIMEOUT_MINUTES} minutes"
                                    echo "Format: ${params.SCAN_FORMAT}"

                                    ${scanCommand} > "${RESULTS_DIR}/osv_scan_results.${params.SCAN_FORMAT}" 2>&1
                                    SCAN_EXIT_CODE=\$?
                                    echo "Scan exit code: \$SCAN_EXIT_CODE"

                                    # Save scan output for debugging
                                    cp "${RESULTS_DIR}/osv_scan_results.${params.SCAN_FORMAT}" "${RESULTS_DIR}/scan_output.log"
                                """
                            }
                        } catch (Exception e) {
                            echo "OSV scan timed out or failed: ${e.getMessage()}"
                            currentBuild.result = 'UNSTABLE'
                            sh """
                                echo '{"error": "Scan timeout or failure", "timeout_minutes": "${params.TIMEOUT_MINUTES}"}' > "${RESULTS_DIR}/osv_scan_results.${params.SCAN_FORMAT}"
                            """
                        }
                    }
                }
            }
        }

        stage('Analyze Results') {
            steps {
                script {
                    def resultsFile = "${RESULTS_DIR}/osv_scan_results.${params.SCAN_FORMAT}"

                    if (!fileExists(resultsFile)) {
                        error "Scan results file not found: ${resultsFile}"
                        currentBuild.result = 'FAILURE'
                        return
                    }

                    // Parse results based on format
                    if (params.SCAN_FORMAT == 'json') {
                        def scanResults = readJSON file: resultsFile

                        if (scanResults.error) {
                            echo "OSV scan failed: ${scanResults.error}"
                            currentBuild.result = 'UNSTABLE'
                            env.SCAN_FAILED = 'true'
                            env.VULN_COUNT = '0'
                            env.HIGH_SEVERITY = '0'
                            env.MEDIUM_SEVERITY = '0'
                        } else {
                            def vulnCount = scanResults.vulnerabilities ? scanResults.vulnerabilities.size() : 0
                            def highSeverity = scanResults.vulnerabilities ? scanResults.vulnerabilities.findAll { it.severity == 'HIGH' || (it.cvss_score && it.cvss_score >= 7.0) }.size() : 0
                            def mediumSeverity = scanResults.vulnerabilities ? scanResults.vulnerabilities.findAll { it.severity == 'MEDIUM' || ((it.cvss_score && it.cvss_score >= 4.0) && it.cvss_score < 7.0) }.size() : 0

                            echo "OSV Scan Results:"
                            echo "  Total vulnerabilities: ${vulnCount}"
                            echo "  High severity: ${highSeverity}"
                            echo "  Medium severity: ${mediumSeverity}"
                            echo "  Dependencies scanned: ${env.DEP_COUNT}"

                            // Set environment variables for later stages
                            env.SCAN_FAILED = 'false'
                            env.VULN_COUNT = vulnCount.toString()
                            env.HIGH_SEVERITY = highSeverity.toString()
                            env.MEDIUM_SEVERITY = mediumSeverity.toString()

                            // Write metrics for external tools
                            writeFile file: "${RESULTS_DIR}/scan_metrics.txt", text: """\
OSV_SCAN_VULNERABILITIES=${vulnCount}
OSV_SCAN_HIGH_SEVERITY=${highSeverity}
OSV_SCAN_MEDIUM_SEVERITY=${mediumSeverity}
OSV_SCAN_DEPENDENCIES=${env.DEP_COUNT}
OSV_SCAN_TIMESTAMP=${new Date().format('yyyy-MM-dd HH:mm:ss')}
OSV_SCAN_BRANCH=${env.SCAN_BRANCH}
OSV_SCAN_COMMIT=${sh(script: 'git rev-parse HEAD', returnStdout: true).trim()}
"""
                        }
                    } else {
                        echo "Results analysis not implemented for format: ${params.SCAN_FORMAT}"
                        env.SCAN_FAILED = 'false'
                        env.VULN_COUNT = 'unknown'
                        env.HIGH_SEVERITY = 'unknown'
                        env.MEDIUM_SEVERITY = 'unknown'
                    }
                }
            }
        }

        stage('Generate Reports') {
            when {
                expression { params.GENERATE_HTML_REPORT }
            }
            steps {
                script {
                    def resultsFile = "${RESULTS_DIR}/osv_scan_results.${params.SCAN_FORMAT}"
                    def htmlReport = "${REPORTS_DIR}/osv_scan_report.html"

                    sh """
                        echo "Generating HTML report..."
                        cat > "${htmlReport}" << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>OSV Vulnerability Scan Report - Jenkins Build #${BUILD_NUMBER}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .value { font-size: 2em; font-weight: bold; }
        .build-info { background: #e9ecef; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .footer { margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: center; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 OSV Vulnerability Scan Report</h1>
            <p>Jenkins Build #${BUILD_NUMBER}</p>
            <p>Generated: $(date)</p>
        </div>

        <div class="build-info">
            <h2>📋 Build Information</h2>
            <p><strong>Job:</strong> ${JOB_NAME}</p>
            <p><strong>Branch:</strong> ${SCAN_BRANCH}</p>
            <p><strong>Commit:</strong> $(git rev-parse HEAD)</p>
            <p><strong>Scan Type:</strong> ${params.SCAN_TYPE}</p>
            <p><strong>Build Tool:</strong> ${BUILD_TOOL}</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <h3>Dependencies Scanned</h3>
                <div class="value">${env.DEP_COUNT}</div>
            </div>
            <div class="summary-card">
                <h3>Total Vulnerabilities</h3>
                <div class="value">${env.VULN_COUNT}</div>
            </div>
            <div class="summary-card">
                <h3>High Severity</h3>
                <div class="value" style="color: #dc3545;">${env.HIGH_SEVERITY}</div>
            </div>
            <div class="summary-card">
                <h3>Medium Severity</h3>
                <div class="value" style="color: #ffc107;">${env.MEDIUM_SEVERITY}</div>
            </div>
        </div>
EOF
                    """

                    // Add detailed results if JSON format
                    if (params.SCAN_FORMAT == 'json' && fileExists(resultsFile)) {
                        sh """
                            echo '<h2>🚨 Detailed Results</h2>' >> "${htmlReport}"
                            echo '<div class="results">' >> "${htmlReport}"

                            if command -v jq >/dev/null 2>&1; then
                                jq -r '.vulnerabilities[]? | "<div class=\"vulnerability\"><h3>\(.id)</h3><p><strong>Severity:</strong> \(.severity // "Unknown")</p><p><strong>CVSS Score:</strong> \(.cvss_score // "N/A")</p><p><strong>Package:</strong> \(.package.name)@\(.package.version)</p><p><strong>Summary:</strong> \(.summary)</p></div>"' "${resultsFile}" >> "${htmlReport}" 2>/dev/null || echo '<p>No detailed vulnerability information available</p>' >> "${htmlReport}"
                            else
                                echo '<p>JQ not available for detailed results parsing</p>' >> "${htmlReport}"
                            fi

                            echo '</div>' >> "${htmlReport}"
                        """
                    }

                    sh """
                        cat >> "${htmlReport}" << 'EOF'
        <div class="footer">
            <p>Report generated by OSV Jenkins Pipeline</p>
            <p>Infinitium Signal - Enterprise Cyber-Compliance Platform</p>
        </div>
    </div>
</body>
</html>
EOF
                    """

                    echo "HTML report generated: ${htmlReport}"
                }
            }
        }

        stage('Quality Gate') {
            when {
                expression { params.FAIL_ON_VULNERABILITIES }
            }
            steps {
                script {
                    def maxAllowed = params.MAX_ALLOWED_VULNERABILITIES.isInteger() ? params.MAX_ALLOWED_VULNERABILITIES.toInteger() : 0
                    def maxHigh = params.MAX_ALLOWED_HIGH_SEVERITY.isInteger() ? params.MAX_ALLOWED_HIGH_SEVERITY.toInteger() : 0

                    def vulnCount = env.VULN_COUNT.isInteger() ? env.VULN_COUNT.toInteger() : 0
                    def highSeverity = env.HIGH_SEVERITY.isInteger() ? env.HIGH_SEVERITY.toInteger() : 0

                    def qualityGatePassed = true
                    def failureReasons = []

                    if (maxAllowed > 0 && vulnCount > maxAllowed) {
                        qualityGatePassed = false
                        failureReasons.add("Total vulnerabilities (${vulnCount}) exceed limit (${maxAllowed})")
                    }

                    if (maxHigh > 0 && highSeverity > maxHigh) {
                        qualityGatePassed = false
                        failureReasons.add("High severity vulnerabilities (${highSeverity}) exceed limit (${maxHigh})")
                    }

                    if (env.SCAN_FAILED == 'true') {
                        qualityGatePassed = false
                        failureReasons.add("OSV scan failed")
                    }

                    if (!qualityGatePassed) {
                        echo "❌ Quality gate failed:"
                        failureReasons.each { reason ->
                            echo "  - ${reason}"
                        }
                        currentBuild.result = 'FAILURE'
                        error("Quality gate failed: ${failureReasons.join(', ')}")
                    } else {
                        echo "✅ Quality gate passed"
                    }
                }
            }
        }

        stage('Publish Results') {
            steps {
                script {
                    // Archive artifacts
                    archiveArtifacts artifacts: "${RESULTS_DIR}/**", allowEmptyArchive: true

                    // Publish HTML report
                    if (params.GENERATE_HTML_REPORT && fileExists("${REPORTS_DIR}/osv_scan_report.html")) {
                        publishHTML([
                            allowMissing: true,
                            alwaysLinkToLastBuild: true,
                            keepAll: true,
                            reportDir: REPORTS_DIR,
                            reportFiles: 'osv_scan_report.html',
                            reportName: 'OSV Vulnerability Report'
                        ])
                    }

                    // Publish to Git if requested
                    if (params.PUBLISH_TO_GIT) {
                        sh """
                            echo "Publishing results to Git..."
                            git config user.name "Jenkins CI"
                            git config user.email "<EMAIL>"

                            cp "${RESULTS_DIR}/osv_scan_results.${params.SCAN_FORMAT}" "osv_scan_results.${params.SCAN_FORMAT}"
                            git add "osv_scan_results.${params.SCAN_FORMAT}"
                            git commit -m "OSV scan results for build ${BUILD_NUMBER}" 2>/dev/null || echo "No changes to commit"
                            git push origin HEAD 2>/dev/null || echo "Failed to push results"
                        """
                    }
                }
            }
        }

        stage('Notifications') {
            steps {
                script {
                    def buildStatus = currentBuild.result ?: 'SUCCESS'
                    def vulnCount = env.VULN_COUNT ?: 'unknown'
                    def highSeverity = env.HIGH_SEVERITY ?: 'unknown'

                    // Email notification
                    if (params.NOTIFICATION_EMAIL) {
                        emailext(
                            subject: "OSV Scan Results: ${env.JOB_NAME} #${env.BUILD_NUMBER} - ${buildStatus}",
                            body: """
OSV vulnerability scan completed for ${env.JOB_NAME} #${env.BUILD_NUMBER}

Build Status: ${buildStatus}
Branch: ${env.SCAN_BRANCH}
Commit: ${sh(script: 'git rev-parse HEAD', returnStdout: true).trim()}

Scan Results:
- Dependencies scanned: ${env.DEP_COUNT}
- Total vulnerabilities: ${vulnCount}
- High severity: ${highSeverity}

View detailed results: ${env.BUILD_URL}

${buildStatus == 'FAILURE' ? '❌ Action Required: Review scan results and address vulnerabilities' : '✅ Scan completed successfully'}
                            """,
                            recipientProviders: [[$class: 'CulpritsRecipientProvider']],
                            to: params.NOTIFICATION_EMAIL
                        )
                    }

                    // Slack notification
                    if (params.SLACK_CHANNEL) {
                        def slackColor = 'good'
                        if (buildStatus == 'FAILURE') {
                            slackColor = 'danger'
                        } else if (highSeverity.isInteger() && highSeverity.toInteger() > 0) {
                            slackColor = 'warning'
                        }

                        slackSend(
                            channel: params.SLACK_CHANNEL,
                            color: slackColor,
                            message: """
🚨 OSV Scan Results - ${env.JOB_NAME} #${env.BUILD_NUMBER}

• Status: ${buildStatus}
• Branch: ${env.SCAN_BRANCH}
• Vulnerabilities: ${vulnCount}
• High Severity: ${highSeverity}

${env.BUILD_URL}
                            """.stripIndent()
                        )
                    }
                }
            }
        }
    }

    post {
        always {
            // Cleanup
            sh """
                echo "Cleaning up workspace..."
                rm -rf "${RESULTS_DIR}" || true
                rm -rf "${REPORTS_DIR}" || true
            """

            // Log final status
            script {
                def finalStatus = currentBuild.result ?: 'SUCCESS'
                echo "Pipeline completed with status: ${finalStatus}"

                if (finalStatus == 'SUCCESS') {
                    echo "✅ OSV vulnerability scan pipeline completed successfully"
                } else if (finalStatus == 'UNSTABLE') {
                    echo "⚠️ OSV vulnerability scan completed with warnings"
                } else {
                    echo "❌ OSV vulnerability scan failed"
                }
            }
        }

        success {
            echo "✅ OSV scan pipeline succeeded"
        }

        unstable {
            echo "⚠️ OSV scan pipeline completed with instabilities"
        }

        failure {
            echo "❌ OSV scan pipeline failed"
            // Additional failure handling can be added here
        }
    }
}