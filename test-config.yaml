server:
  host: "0.0.0.0"
  port: 8081
  workers: 4
  timeout: 30
  keep_alive: 75
  max_request_size: 16777216
  cors_origins: ["*"]

database:
  url: "sqlite:infinitum_signal.db"
  max_connections: 10
  min_connections: 1
  connect_timeout: 30
  idle_timeout: 600
  max_lifetime: 3600
  enabled: false

redis:
  url: "redis://localhost:6379"
  max_connections: 10
  timeout: 5
  pool_timeout: 30

security:
  jwt_secret: "your-super-secret-jwt-key-for-testing-purposes-only"
  jwt_expiration: 3600
  jwt_refresh_expiration: 86400
  encryption_key: "your-32-byte-encryption-key-here"
  hash_rounds: 12
  api_rate_limit: 1000
  api_rate_window: 3600

scanning:
  timeout: 300
  max_concurrent: 5
  temp_dir: "/tmp/infinitum-scans"
  cleanup_interval: 3600
  max_file_size: 104857600
  supported_languages: ["rust", "javascript", "python", "java", "go", "c", "cpp"]

compliance:
  frameworks: ["cert-in", "sebi", "iso27001"]
  report_output_dir: "/tmp/infinitum-reports"
  report_retention_days: 90
  auto_cleanup: true
  template_dir: "templates"

vulnerability:
  enabled: true
  severity_threshold: "medium"
  auto_update: false
  update_interval: 86400
  sources: ["nvd", "osv"]

blockchain:
  enabled: false
  network: "hyperledger-fabric"
  channel: "compliance-channel"
  chaincode: "infinitum-compliance"
  msp_id: "InfinitumMSP"
  peer_endpoint: "grpc://localhost:7051"
  orderer_endpoint: "grpc://localhost:7050"
  cert_path: "/path/to/cert.pem"
  key_path: "/path/to/key.pem"
  ca_path: "/path/to/ca.pem"
  storage_backend: "filesystem"
  storage_path: "./blockchain_data"
  chain_id: "infinitum-chain"

logging:
  level: "info"
  format: "json"
  file: null
  max_size: "100MB"
  max_files: 10
  compress: true

metrics:
  enabled: true
  port: 9090
  path: "/metrics"
  prometheus_endpoint: null

external_services:
  nvd:
    api_key: null
    base_url: "https://services.nvd.nist.gov/rest/json"
    rate_limit: 50
    timeout: 30
  github:
    token: null
    api_url: "https://api.github.com"
    timeout: 30
  snyk:
    api_token: null
    org_id: null
    base_url: "https://api.snyk.io"
    timeout: 30
  osv:
    api_url: "https://api.osv.dev"
    timeout: 30
