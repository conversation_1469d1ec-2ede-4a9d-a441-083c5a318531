//! Diagnostic Logging System for Test Validation
//!
//! This module provides comprehensive logging and diagnostic capabilities
//! for validating system performance, diagnosing issues, and tracking
//! test execution across all test suites.

use std::collections::HashMap;
use std::fs;
use std::path::Path;
use std::sync::Arc;
use tokio::sync::RwLock;
use chrono::{DateTime, Utc};

/// Comprehensive diagnostic logger
pub struct DiagnosticLogger {
    log_entries: Arc<RwLock<Vec<LogEntry>>>,
    test_metrics: Arc<RwLock<HashMap<String, TestMetrics>>>,
    system_health: Arc<RwLock<SystemHealth>>,
    log_file_path: String,
}

#[derive(Debug, Clone)]
pub struct LogEntry {
    pub timestamp: DateTime<Utc>,
    pub level: LogLevel,
    pub component: String,
    pub test_name: String,
    pub message: String,
    pub metadata: HashMap<String, serde_json::Value>,
    pub error_details: Option<ErrorDetails>,
}

#[derive(Debug, Clone, PartialEq)]
pub enum LogLevel {
    Debug,
    Info,
    Warning,
    Error,
    Critical,
}

#[derive(Debug, Clone)]
pub struct ErrorDetails {
    pub error_type: String,
    pub error_message: String,
    pub stack_trace: Option<String>,
    pub context: HashMap<String, String>,
}

#[derive(Debug, Clone)]
pub struct TestMetrics {
    pub test_name: String,
    pub start_time: DateTime<Utc>,
    pub end_time: Option<DateTime<Utc>>,
    pub duration_ms: Option<u128>,
    pub success: Option<bool>,
    pub assertions_passed: usize,
    pub assertions_failed: usize,
    pub performance_metrics: HashMap<String, f64>,
    pub resource_usage: ResourceUsage,
}

#[derive(Debug, Clone)]
pub struct ResourceUsage {
    pub memory_mb: f64,
    pub cpu_percent: f64,
    pub network_bytes: u64,
    pub disk_io_bytes: u64,
}

#[derive(Debug, Clone)]
pub struct SystemHealth {
    pub overall_status: HealthStatus,
    pub component_health: HashMap<String, ComponentHealth>,
    pub performance_indicators: PerformanceIndicators,
    pub error_rates: HashMap<String, f64>,
    pub last_updated: DateTime<Utc>,
}

#[derive(Debug, Clone, PartialEq)]
pub enum HealthStatus {
    Healthy,
    Degraded,
    Unhealthy,
    Critical,
}

#[derive(Debug, Clone)]
pub struct ComponentHealth {
    pub name: String,
    pub status: HealthStatus,
    pub response_time_ms: f64,
    pub error_rate: f64,
    pub last_check: DateTime<Utc>,
}

#[derive(Debug, Clone)]
pub struct PerformanceIndicators {
    pub average_response_time_ms: f64,
    pub throughput_req_per_sec: f64,
    pub memory_usage_mb: f64,
    pub cpu_usage_percent: f64,
    pub error_rate_percent: f64,
}

impl DiagnosticLogger {
    /// Create new diagnostic logger
    pub fn new(log_file_path: String) -> Self {
        // Ensure log directory exists
        if let Some(parent) = Path::new(&log_file_path).parent() {
            fs::create_dir_all(parent).unwrap_or_else(|e| {
                eprintln!("Failed to create log directory: {:?}", e);
            });
        }

        Self {
            log_entries: Arc::new(RwLock::new(Vec::new())),
            test_metrics: Arc::new(RwLock::new(HashMap::new())),
            system_health: Arc::new(RwLock::new(SystemHealth {
                overall_status: HealthStatus::Healthy,
                component_health: HashMap::new(),
                performance_indicators: PerformanceIndicators {
                    average_response_time_ms: 0.0,
                    throughput_req_per_sec: 0.0,
                    memory_usage_mb: 0.0,
                    cpu_usage_percent: 0.0,
                    error_rate_percent: 0.0,
                },
                error_rates: HashMap::new(),
                last_updated: Utc::now(),
            })),
            log_file_path,
        }
    }

    /// Log a diagnostic message
    pub async fn log(&self, level: LogLevel, component: &str, test_name: &str, message: &str) {
        self.log_with_metadata(level, component, test_name, message, HashMap::new(), None).await;
    }

    /// Log with metadata
    pub async fn log_with_metadata(
        &self,
        level: LogLevel,
        component: &str,
        test_name: &str,
        message: &str,
        metadata: HashMap<String, serde_json::Value>,
        error_details: Option<ErrorDetails>,
    ) {
        let entry = LogEntry {
            timestamp: Utc::now(),
            level,
            component: component.to_string(),
            test_name: test_name.to_string(),
            message: message.to_string(),
            metadata,
            error_details,
        };

        // Add to in-memory log
        {
            let mut entries = self.log_entries.write().await;
            entries.push(entry.clone());
        }

        // Write to file immediately for critical logs
        if matches!(level, LogLevel::Error | LogLevel::Critical) {
            self.write_entry_to_file(&entry).await;
        }

        // Print to console for important logs
        if matches!(level, LogLevel::Warning | LogLevel::Error | LogLevel::Critical) {
            println!("[{}] {} - {}: {}", entry.timestamp, level_to_string(&entry.level), component, message);
        }
    }

    /// Start tracking a test
    pub async fn start_test(&self, test_name: &str) {
        let mut metrics = self.test_metrics.write().await;
        metrics.insert(test_name.to_string(), TestMetrics {
            test_name: test_name.to_string(),
            start_time: Utc::now(),
            end_time: None,
            duration_ms: None,
            success: None,
            assertions_passed: 0,
            assertions_failed: 0,
            performance_metrics: HashMap::new(),
            resource_usage: ResourceUsage {
                memory_mb: 0.0,
                cpu_percent: 0.0,
                network_bytes: 0,
                disk_io_bytes: 0,
            },
        });

        self.log(LogLevel::Info, "TestRunner", test_name, &format!("Started test: {}", test_name)).await;
    }

    /// End tracking a test
    pub async fn end_test(&self, test_name: &str, success: bool) {
        let mut metrics = self.test_metrics.write().await;
        if let Some(test_metric) = metrics.get_mut(test_name) {
            test_metric.end_time = Some(Utc::now());
            test_metric.success = Some(success);
            if let Some(end_time) = test_metric.end_time {
                test_metric.duration_ms = Some((end_time - test_metric.start_time).num_milliseconds() as u128);
            }
        }

        let status = if success { "PASSED" } else { "FAILED" };
        self.log(LogLevel::Info, "TestRunner", test_name, &format!("Completed test: {} - {}", test_name, status)).await;
    }

    /// Record assertion result
    pub async fn record_assertion(&self, test_name: &str, passed: bool, description: &str) {
        let mut metrics = self.test_metrics.write().await;
        if let Some(test_metric) = metrics.get_mut(test_name) {
            if passed {
                test_metric.assertions_passed += 1;
            } else {
                test_metric.assertions_failed += 1;
            }
        }

        let status = if passed { "PASSED" } else { "FAILED" };
        self.log(LogLevel::Debug, "Assertion", test_name, &format!("{}: {}", status, description)).await;
    }

    /// Record performance metric
    pub async fn record_performance_metric(&self, test_name: &str, metric_name: &str, value: f64) {
        let mut metrics = self.test_metrics.write().await;
        if let Some(test_metric) = metrics.get_mut(test_name) {
            test_metric.performance_metrics.insert(metric_name.to_string(), value);
        }

        self.log_with_metadata(
            LogLevel::Info,
            "Performance",
            test_name,
            &format!("Performance metric: {} = {:.2}", metric_name, value),
            HashMap::from([("metric_name".to_string(), serde_json::json!(metric_name)), ("value".to_string(), serde_json::json!(value))]),
            None,
        ).await;
    }

    /// Update system health
    pub async fn update_system_health(&self, component: &str, status: HealthStatus, response_time_ms: f64, error_rate: f64) {
        let mut health = self.system_health.write().await;

        health.component_health.insert(component.to_string(), ComponentHealth {
            name: component.to_string(),
            status: status.clone(),
            response_time_ms,
            error_rate,
            last_check: Utc::now(),
        });

        // Update overall status
        health.overall_status = self.calculate_overall_health(&health.component_health);
        health.last_updated = Utc::now();

        let status_str = match status {
            HealthStatus::Healthy => "healthy",
            HealthStatus::Degraded => "degraded",
            HealthStatus::Unhealthy => "unhealthy",
            HealthStatus::Critical => "critical",
        };

        self.log_with_metadata(
            LogLevel::Info,
            "HealthMonitor",
            "system",
            &format!("Component {} status: {}", component, status_str),
            HashMap::from([
                ("component".to_string(), serde_json::json!(component)),
                ("status".to_string(), serde_json::json!(status_str)),
                ("response_time_ms".to_string(), serde_json::json!(response_time_ms)),
                ("error_rate".to_string(), serde_json::json!(error_rate)),
            ]),
            None,
        ).await;
    }

    /// Log error with details
    pub async fn log_error(&self, component: &str, test_name: &str, error: &dyn std::error::Error, context: HashMap<String, String>) {
        let error_details = ErrorDetails {
            error_type: "TestError".to_string(),
            error_message: error.to_string(),
            stack_trace: None, // Would need backtrace crate for full stack traces
            context,
        };

        self.log_with_metadata(
            LogLevel::Error,
            component,
            test_name,
            &format!("Error occurred: {}", error),
            HashMap::new(),
            Some(error_details),
        ).await;
    }

    /// Generate diagnostic report
    pub async fn generate_diagnostic_report(&self) -> Result<String, Box<dyn std::error::Error>> {
        let entries = self.log_entries.read().await;
        let metrics = self.test_metrics.read().await;
        let health = self.system_health.read().await;

        let mut report = String::new();
        report.push_str("=== DIAGNOSTIC REPORT ===\n\n");

        // System Health Summary
        report.push_str("SYSTEM HEALTH:\n");
        report.push_str(&format!("Overall Status: {:?}\n", health.overall_status));
        report.push_str(&format!("Last Updated: {}\n", health.last_updated));
        report.push_str(&format!("Components: {}\n", health.component_health.len()));
        report.push_str("\n");

        // Test Metrics Summary
        report.push_str("TEST METRICS:\n");
        let total_tests = metrics.len();
        let passed_tests = metrics.values().filter(|m| m.success.unwrap_or(false)).count();
        let failed_tests = total_tests - passed_tests;

        report.push_str(&format!("Total Tests: {}\n", total_tests));
        report.push_str(&format!("Passed: {}\n", passed_tests));
        report.push_str(&format!("Failed: {}\n", failed_tests));
        report.push_str(&format!("Success Rate: {:.1}%\n", if total_tests > 0 { passed_tests as f64 / total_tests as f64 * 100.0 } else { 0.0 }));
        report.push_str("\n");

        // Performance Summary
        let avg_response_time = metrics.values()
            .filter_map(|m| m.performance_metrics.get("response_time_ms"))
            .sum::<f64>() / metrics.len().max(1) as f64;

        report.push_str("PERFORMANCE SUMMARY:\n");
        report.push_str(&format!("Average Response Time: {:.2}ms\n", avg_response_time));
        report.push_str("\n");

        // Recent Errors
        let recent_errors: Vec<_> = entries.iter()
            .filter(|e| matches!(e.level, LogLevel::Error | LogLevel::Critical))
            .take(10)
            .collect();

        if !recent_errors.is_empty() {
            report.push_str("RECENT ERRORS:\n");
            for error in recent_errors {
                report.push_str(&format!("[{}] {}: {}\n", error.timestamp, error.component, error.message));
            }
            report.push_str("\n");
        }

        // Recommendations
        report.push_str("RECOMMENDATIONS:\n");
        if failed_tests > 0 {
            report.push_str(&format!("• {} tests failed - review test logs for details\n", failed_tests));
        }
        if matches!(health.overall_status, HealthStatus::Degraded | HealthStatus::Unhealthy | HealthStatus::Critical) {
            report.push_str("• System health is degraded - check component statuses\n");
        }
        if avg_response_time > 1000.0 {
            report.push_str("• High average response time detected - investigate performance bottlenecks\n");
        }

        Ok(report)
    }

    /// Flush all logs to file
    pub async fn flush_logs(&self) -> Result<(), Box<dyn std::error::Error>> {
        let entries = self.log_entries.read().await;

        let mut log_content = String::new();
        for entry in entries.iter() {
            let level_str = level_to_string(&entry.level);
            log_content.push_str(&format!(
                "[{}] {} {} {}: {}\n",
                entry.timestamp, level_str, entry.component, entry.test_name, entry.message
            ));

            if !entry.metadata.is_empty() {
                log_content.push_str(&format!("  Metadata: {:?}\n", entry.metadata));
            }

            if let Some(error_details) = &entry.error_details {
                log_content.push_str(&format!("  Error: {} - {}\n", error_details.error_type, error_details.error_message));
            }
        }

        fs::write(&self.log_file_path, log_content)?;
        Ok(())
    }

    /// Write single entry to file
    async fn write_entry_to_file(&self, entry: &LogEntry) {
        let level_str = level_to_string(&entry.level);
        let log_line = format!(
            "[{}] {} {} {}: {}\n",
            entry.timestamp, level_str, entry.component, entry.test_name, entry.message
        );

        if let Ok(mut file) = fs::OpenOptions::new().append(true).create(true).open(&self.log_file_path) {
            use std::io::Write;
            let _ = file.write_all(log_line.as_bytes());
        }
    }

    /// Calculate overall system health
    fn calculate_overall_health(&self, component_health: &HashMap<String, ComponentHealth>) -> HealthStatus {
        let mut critical_count = 0;
        let mut unhealthy_count = 0;
        let mut degraded_count = 0;

        for health in component_health.values() {
            match health.status {
                HealthStatus::Critical => critical_count += 1,
                HealthStatus::Unhealthy => unhealthy_count += 1,
                HealthStatus::Degraded => degraded_count += 1,
                HealthStatus::Healthy => {}
            }
        }

        if critical_count > 0 {
            HealthStatus::Critical
        } else if unhealthy_count > 0 {
            HealthStatus::Unhealthy
        } else if degraded_count > 0 {
            HealthStatus::Degraded
        } else {
            HealthStatus::Healthy
        }
    }

    /// Get test metrics for a specific test
    pub async fn get_test_metrics(&self, test_name: &str) -> Option<TestMetrics> {
        let metrics = self.test_metrics.read().await;
        metrics.get(test_name).cloned()
    }

    /// Get all test metrics
    pub async fn get_all_test_metrics(&self) -> HashMap<String, TestMetrics> {
        let metrics = self.test_metrics.read().await;
        metrics.clone()
    }

    /// Get system health
    pub async fn get_system_health(&self) -> SystemHealth {
        let health = self.system_health.read().await;
        health.clone()
    }

    /// Get recent log entries
    pub async fn get_recent_logs(&self, limit: usize) -> Vec<LogEntry> {
        let entries = self.log_entries.read().await;
        entries.iter().rev().take(limit).cloned().collect()
    }
}

/// Convert log level to string
fn level_to_string(level: &LogLevel) -> &'static str {
    match level {
        LogLevel::Debug => "DEBUG",
        LogLevel::Info => "INFO",
        LogLevel::Warning => "WARN",
        LogLevel::Error => "ERROR",
        LogLevel::Critical => "CRIT",
    }
}

/// Global diagnostic logger instance
static mut GLOBAL_LOGGER: Option<DiagnosticLogger> = None;

/// Initialize global logger
pub fn init_global_logger(log_file_path: String) {
    unsafe {
        GLOBAL_LOGGER = Some(DiagnosticLogger::new(log_file_path));
    }
}

/// Get global logger instance
pub fn get_global_logger() -> Option<&'static DiagnosticLogger> {
    unsafe { GLOBAL_LOGGER.as_ref() }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_diagnostic_logger_creation() {
        let logger = DiagnosticLogger::new("test.log".to_string());
        assert_eq!(logger.log_file_path, "test.log");
    }

    #[tokio::test]
    async fn test_logging_functionality() {
        let logger = DiagnosticLogger::new("test.log".to_string());

        // Test basic logging
        logger.log(LogLevel::Info, "TestComponent", "test_functionality", "Test message").await;

        // Test logging with metadata
        let mut metadata = HashMap::new();
        metadata.insert("test_key".to_string(), serde_json::json!("test_value"));
        logger.log_with_metadata(LogLevel::Debug, "TestComponent", "test_metadata", "Test with metadata", metadata, None).await;

        // Check that logs were recorded
        let recent_logs = logger.get_recent_logs(10).await;
        assert!(!recent_logs.is_empty());
        assert!(recent_logs.len() >= 2);
    }

    #[tokio::test]
    async fn test_test_tracking() {
        let logger = DiagnosticLogger::new("test.log".to_string());

        // Start test
        logger.start_test("test_tracking_test").await;

        // Record some assertions
        logger.record_assertion("test_tracking_test", true, "First assertion").await;
        logger.record_assertion("test_tracking_test", false, "Second assertion").await;

        // Record performance metric
        logger.record_performance_metric("test_tracking_test", "response_time", 150.5).await;

        // End test
        logger.end_test("test_tracking_test", true).await;

        // Check metrics
        let metrics = logger.get_test_metrics("test_tracking_test").await;
        assert!(metrics.is_some());

        if let Some(m) = metrics {
            assert_eq!(m.assertions_passed, 1);
            assert_eq!(m.assertions_failed, 1);
            assert_eq!(m.performance_metrics.get("response_time"), Some(&150.5));
            assert_eq!(m.success, Some(true));
        }
    }

    #[tokio::test]
    async fn test_system_health_monitoring() {
        let logger = DiagnosticLogger::new("test.log".to_string());

        // Update component health
        logger.update_system_health("database", HealthStatus::Healthy, 50.0, 0.01).await;
        logger.update_system_health("api", HealthStatus::Degraded, 200.0, 0.05).await;

        // Check system health
        let health = logger.get_system_health().await;
        assert_eq!(health.component_health.len(), 2);
        assert_eq!(health.overall_status, HealthStatus::Degraded); // Should be degraded due to API
    }

    #[tokio::test]
    async fn test_diagnostic_report_generation() {
        let logger = DiagnosticLogger::new("test.log".to_string());

        // Add some test data
        logger.start_test("report_test").await;
        logger.record_assertion("report_test", true, "Test assertion").await;
        logger.end_test("report_test", true).await;

        // Generate report
        let report = logger.generate_diagnostic_report().await;
        assert!(report.is_ok());

        let report_content = report.unwrap();
        assert!(report_content.contains("DIAGNOSTIC REPORT"));
        assert!(report_content.contains("TEST METRICS"));
        assert!(report_content.contains("report_test"));
    }

    #[tokio::test]
    async fn test_log_flushing() {
        let temp_dir = tempfile::tempdir().unwrap();
        let log_path = temp_dir.path().join("test.log").to_str().unwrap().to_string();

        let logger = DiagnosticLogger::new(log_path.clone());

        // Add some logs
        logger.log(LogLevel::Info, "Test", "flush_test", "Test message 1").await;
        logger.log(LogLevel::Error, "Test", "flush_test", "Test message 2").await;

        // Flush logs
        let flush_result = logger.flush_logs().await;
        assert!(flush_result.is_ok());

        // Check that file was created and contains logs
        assert!(Path::new(&log_path).exists());

        let log_content = fs::read_to_string(&log_path).unwrap();
        assert!(log_content.contains("Test message 1"));
        assert!(log_content.contains("Test message 2"));
    }
}