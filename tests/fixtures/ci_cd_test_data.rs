//! CI/CD test fixtures and mock data
//!
//! This module provides comprehensive mock data for testing CI/CD integrations
//! including GitHub Actions, GitLab CI, Jenkins, and Docker configurations.

use infinitium_signal::compliance::*;
use std::collections::HashMap;

/// CI/CD workflow fixture
#[derive(Debug, Clone)]
pub struct CicdWorkflowFixture {
    pub name: String,
    pub platform: CICDPlaform,
    pub content: String,
    pub expected_outcomes: Vec<String>,
}

/// CI/CD test fixtures
pub struct CicdTestFixtures;

impl CicdTestFixtures {
    /// Get all CI/CD workflow fixtures
    pub fn all_workflows() -> Vec<CicdWorkflowFixture> {
        let mut workflows = Vec::new();
        workflows.extend(Self::github_actions_workflows());
        workflows.extend(Self::gitlab_ci_pipelines());
        workflows.extend(Self::jenkins_jobs());
        workflows.extend(Self::docker_compose_setups());
        workflows
    }

    /// GitHub Actions workflow fixtures
    pub fn github_actions_workflows() -> Vec<CicdWorkflowFixture> {
        vec![
            CicdWorkflowFixture {
                name: "basic-compliance-check".to_string(),
                platform: CICDPlaform::GitHubActions,
                content: r#"
name: Compliance Check
on: [push, pull_request]
jobs:
  compliance:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Run compliance scan
      uses: infinitum-signal/compliance-action@v1
      with:
        framework: 'CERT-In'
        scan-path: '.'
        output-format: 'json'
    - name: Upload compliance report
      uses: actions/upload-artifact@v3
      with:
        name: compliance-report
        path: compliance-report.json
"#.to_string(),
                expected_outcomes: vec![
                    "workflow_should_pass".to_string(),
                    "compliance_report_generated".to_string(),
                    "artifact_uploaded".to_string(),
                ],
            },
            CicdWorkflowFixture {
                name: "multi-framework-compliance".to_string(),
                platform: CICDPlaform::GitHubActions,
                content: r#"
name: Multi-Framework Compliance
on: [push]
jobs:
  cert-in:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: CERT-In compliance
      uses: infinitum-signal/compliance-action@v1
      with:
        framework: 'CERT-In'
        output-path: 'cert-in-report.json'
  sebi:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: SEBI compliance
      uses: infinitum-signal/compliance-action@v1
      with:
        framework: 'SEBI'
        output-path: 'sebi-report.json'
  merge-reports:
    runs-on: ubuntu-latest
    needs: [cert-in, sebi]
    steps:
    - name: Merge compliance reports
      run: |
        jq -s '.[0] * .[1]' cert-in-report.json sebi-report.json > merged-report.json
    - name: Upload merged report
      uses: actions/upload-artifact@v3
      with:
        name: merged-compliance-report
        path: merged-report.json
"#.to_string(),
                expected_outcomes: vec![
                    "all_jobs_should_pass".to_string(),
                    "multiple_reports_generated".to_string(),
                    "reports_merged_successfully".to_string(),
                ],
            },
            CicdWorkflowFixture {
                name: "scheduled-compliance-scan".to_string(),
                platform: CICDPlaform::GitHubActions,
                content: r#"
name: Scheduled Compliance Scan
on:
  schedule:
    - cron: '0 0 * * 1'  # Weekly on Mondays
  workflow_dispatch:
jobs:
  weekly-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Weekly compliance scan
      uses: infinitum-signal/compliance-action@v1
      with:
        framework: 'ISO27001'
        include-vulnerabilities: true
        output-path: 'weekly-report.json'
    - name: Store report
      uses: actions/upload-artifact@v3
      with:
        name: weekly-compliance-report
        path: weekly-report.json
    - name: Send notification
      if: failure()
      run: |
        echo "Compliance scan failed - manual review required"
"#.to_string(),
                expected_outcomes: vec![
                    "scheduled_execution_works".to_string(),
                    "vulnerability_scan_included".to_string(),
                    "notification_on_failure".to_string(),
                ],
            },
        ]
    }

    /// GitLab CI pipeline fixtures
    pub fn gitlab_ci_pipelines() -> Vec<CicdWorkflowFixture> {
        vec![
            CicdWorkflowFixture {
                name: "basic-gitlab-pipeline".to_string(),
                platform: CICDPlaform::GitLabCI,
                content: r#"
stages:
  - compliance
  - report
  - deploy

compliance_scan:
  stage: compliance
  image: infinitum-signal/compliance-scanner:latest
  script:
    - infinitum-signal scan --framework CERT-In --path . --output compliance-report.json
  artifacts:
    reports:
      compliance: compliance-report.json
    expire_in: 1 week
  only:
    - main
    - develop

generate_report:
  stage: report
  image: infinitum-signal/report-generator:latest
  script:
    - infinitum-signal report --input compliance-report.json --format PDF --output compliance-report.pdf
  dependencies:
    - compliance_scan
  artifacts:
    paths:
      - compliance-report.pdf
    expire_in: 1 week
  only:
    - main

deploy_compliant:
  stage: deploy
  image: infinitum-signal/deployer:latest
  script:
    - echo "Deploying compliant software to production"
    - ./deploy.sh
  dependencies:
    - generate_report
  only:
    - main
  when: manual
"#.to_string(),
                expected_outcomes: vec![
                    "pipeline_executes_successfully".to_string(),
                    "artifacts_generated".to_string(),
                    "manual_deploy_available".to_string(),
                ],
            },
            CicdWorkflowFixture {
                name: "parallel-compliance-pipeline".to_string(),
                platform: CICDPlaform::GitLabCI,
                content: r#"
stages:
  - compliance
  - merge
  - quality_gate

cert_in_scan:
  stage: compliance
  image: infinitum-signal/compliance-scanner:latest
  script:
    - infinitum-signal scan --framework CERT-In --path . --output cert-in-report.json
  artifacts:
    paths:
      - cert-in-report.json
    expire_in: 1 hour

sebi_scan:
  stage: compliance
  image: infinitum-signal/compliance-scanner:latest
  script:
    - infinitum-signal scan --framework SEBI --path . --output sebi-report.json
  artifacts:
    paths:
      - sebi-report.json
    expire_in: 1 hour

iso27001_scan:
  stage: compliance
  image: infinitum-signal/compliance-scanner:latest
  script:
    - infinitum-signal scan --framework ISO27001 --path . --output iso27001-report.json
  artifacts:
    paths:
      - iso27001-report.json
    expire_in: 1 hour

merge_reports:
  stage: merge
  image: infinitum-signal/report-generator:latest
  script:
    - infinitum-signal merge-reports --inputs cert-in-report.json,sebi-report.json,iso27001-report.json --output merged-report.json
  dependencies:
    - cert_in_scan
    - sebi_scan
    - iso27001_scan
  artifacts:
    paths:
      - merged-report.json
    expire_in: 1 week

quality_gate:
  stage: quality_gate
  image: infinitum-signal/quality-gate:latest
  script:
    - |
      COMPLIANCE_SCORE=$(jq '.compliance_score' merged-report.json)
      if [ $(echo "$COMPLIANCE_SCORE < 80" | bc -l) -eq 1 ]; then
        echo "Compliance score below threshold: $COMPLIANCE_SCORE"
        exit 1
      fi
      echo "Quality gate passed with score: $COMPLIANCE_SCORE"
  dependencies:
    - merge_reports
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
    expire_in: 1 week
"#.to_string(),
                expected_outcomes: vec![
                    "parallel_jobs_execute".to_string(),
                    "reports_merged_correctly".to_string(),
                    "quality_gate_enforced".to_string(),
                ],
            },
        ]
    }

    /// Jenkins job fixtures
    pub fn jenkins_jobs() -> Vec<CicdWorkflowFixture> {
        vec![
            CicdWorkflowFixture {
                name: "declarative-jenkins-pipeline".to_string(),
                platform: CICDPlaform::Jenkins,
                content: r#"
pipeline {
    agent any
    options {
        timeout(time: 30, unit: 'MINUTES')
        disableConcurrentBuilds()
    }
    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }
        stage('Compliance Scan') {
            steps {
                sh '''
                    docker run --rm \
                        -v $(pwd):/workspace \
                        infinitum-signal/compliance-scanner:latest \
                        scan --framework CERT-In --path /workspace --output /workspace/compliance-report.json
                '''
            }
        }
        stage('Generate Report') {
            steps {
                sh '''
                    docker run --rm \
                        -v $(pwd):/workspace \
                        infinitum-signal/report-generator:latest \
                        report --input /workspace/compliance-report.json --format PDF --output /workspace/compliance-report.pdf
                '''
            }
        }
        stage('Archive Results') {
            steps {
                archiveArtifacts artifacts: 'compliance-report.*', fingerprint: true
                publishHTML(target: [
                    allowMissing: false,
                    alwaysLinkToLastBuild: true,
                    keepAll: true,
                    reportDir: '.',
                    reportFiles: 'compliance-report.json',
                    reportName: 'Compliance Report'
                ])
            }
        }
    }
    post {
        always {
            cleanWs()
        }
        success {
            echo 'Compliance pipeline completed successfully'
        }
        failure {
            echo 'Compliance pipeline failed - manual review required'
            // Send notification
            emailext(
                subject: "Compliance Pipeline Failed: ${currentBuild.fullDisplayName}",
                body: "Compliance scan failed. Please review the results.",
                recipientProviders: [[$class: 'DevelopersRecipientProvider']]
            )
        }
    }
}
"#.to_string(),
                expected_outcomes: vec![
                    "pipeline_executes_declaratively".to_string(),
                    "artifacts_archived".to_string(),
                    "notifications_sent".to_string(),
                ],
            },
            CicdWorkflowFixture {
                name: "scripted-jenkins-pipeline".to_string(),
                platform: CICDPlaform::Jenkins,
                content: r#"
node {
    try {
        stage('Preparation') {
            echo 'Preparing compliance environment...'
            sh 'mkdir -p reports'
        }

        stage('Parallel Compliance Scans') {
            parallel {
                stage('CERT-In Scan') {
                    sh '''
                        docker run --rm \
                            -v $(pwd):/workspace \
                            infinitum-signal/compliance-scanner:latest \
                            scan --framework CERT-In --path /workspace --output /workspace/reports/cert-in-report.json
                    '''
                }
                stage('SEBI Scan') {
                    sh '''
                        docker run --rm \
                            -v $(pwd):/workspace \
                            infinitum-signal/compliance-scanner:latest \
                            scan --framework SEBI --path /workspace --output /workspace/reports/sebi-report.json
                    '''
                }
                stage('License Check') {
                    sh '''
                        docker run --rm \
                            -v $(pwd):/workspace \
                            infinitum-signal/compliance-scanner:latest \
                            scan --framework SPDX --path /workspace --output /workspace/reports/license-report.json
                    '''
                }
            }
        }

        stage('Merge Reports') {
            sh '''
                docker run --rm \
                    -v $(pwd):/workspace \
                    infinitum-signal/report-generator:latest \
                    merge-reports \
                    --inputs /workspace/reports/cert-in-report.json,/workspace/reports/sebi-report.json,/workspace/reports/license-report.json \
                    --output /workspace/reports/merged-report.json
            '''
        }

        stage('Quality Assessment') {
            sh '''
                COMPLIANCE_SCORE=$(jq '.compliance_score' reports/merged-report.json)
                echo "Compliance Score: $COMPLIANCE_SCORE"

                if [ $(echo "$COMPLIANCE_SCORE < 75" | bc -l) -eq 1 ]; then
                    echo "Compliance score too low: $COMPLIANCE_SCORE"
                    currentBuild.result = 'UNSTABLE'
                fi
            '''
        }

        stage('Archive and Report') {
            archiveArtifacts artifacts: 'reports/*.json,reports/*.pdf', fingerprint: true

            publishHTML(target: [
                allowMissing: false,
                alwaysLinkToLastBuild: true,
                keepAll: true,
                reportDir: 'reports',
                reportFiles: 'merged-report.json',
                reportName: 'Merged Compliance Report'
            ])
        }

    } catch (Exception e) {
        currentBuild.result = 'FAILURE'
        echo "Pipeline failed: ${e.getMessage()}"
        throw e
    } finally {
        stage('Cleanup') {
            sh 'rm -rf reports'
        }
    }
}
"#.to_string(),
                expected_outcomes: vec![
                    "scripted_execution_works".to_string(),
                    "parallel_stages_execute".to_string(),
                    "error_handling_works".to_string(),
                ],
            },
        ]
    }

    /// Docker Compose setup fixtures
    pub fn docker_compose_setups() -> Vec<CicdWorkflowFixture> {
        vec![
            CicdWorkflowFixture {
                name: "orchestrated-compliance-stack".to_string(),
                platform: CICDPlaform::Docker,
                content: r#"
version: '3.8'
services:
  compliance-scanner:
    build:
      context: .
      dockerfile: Dockerfile.compliance
    volumes:
      - ./:/workspace
      - ./reports:/workspace/reports
    environment:
      - COMPLIANCE_FRAMEWORK=CERT-In
      - SCAN_PATH=/workspace
      - OUTPUT_PATH=/workspace/reports
    networks:
      - compliance-network
    depends_on:
      - license-db

  report-generator:
    build:
      context: .
      dockerfile: Dockerfile.reporter
    volumes:
      - ./reports:/reports
    environment:
      - INPUT_PATH=/reports/compliance-report.json
      - OUTPUT_FORMAT=PDF,HTML,JSON
    networks:
      - compliance-network
    depends_on:
      - compliance-scanner

  license-db:
    image: postgres:13
    environment:
      POSTGRES_DB: compliance
      POSTGRES_USER: compliance
      POSTGRES_PASSWORD: ${COMPLIANCE_DB_PASSWORD:-secure_password}
    volumes:
      - license_db_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - compliance-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U compliance"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis-cache:
    image: redis:7-alpine
    volumes:
      - redis_cache_data:/data
    networks:
      - compliance-network
    command: redis-server --appendonly yes

  api-gateway:
    build:
      context: .
      dockerfile: Dockerfile.api
    ports:
      - "8080:8080"
    environment:
      - COMPLIANCE_SCANNER_URL=http://compliance-scanner:8080
      - REPORT_GENERATOR_URL=http://report-generator:8080
      - DATABASE_URL=*******************************************************/compliance
      - REDIS_URL=redis://redis-cache:6379
    networks:
      - compliance-network
    depends_on:
      - compliance-scanner
      - report-generator
      - license-db
      - redis-cache
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  compliance-network:
    driver: bridge

volumes:
  license_db_data:
  redis_cache_data:
"#.to_string(),
                expected_outcomes: vec![
                    "services_start_successfully".to_string(),
                    "health_checks_pass".to_string(),
                    "data_persisted".to_string(),
                ],
            },
        ]
    }
}

/// Mock CI/CD platform responses
pub struct MockCicdResponses;

impl MockCicdResponses {
    /// Mock GitHub Actions API responses
    pub fn github_actions_responses() -> HashMap<String, serde_json::Value> {
        let mut responses = HashMap::new();

        responses.insert(
            "workflow_dispatch".to_string(),
            serde_json::json!({
                "workflow_run": {
                    "id": 12345,
                    "status": "completed",
                    "conclusion": "success",
                    "html_url": "https://github.com/test/repo/actions/runs/12345"
                }
            })
        );

        responses.insert(
            "workflow_jobs".to_string(),
            serde_json::json!({
                "jobs": [
                    {
                        "id": 67890,
                        "name": "compliance",
                        "status": "completed",
                        "conclusion": "success",
                        "steps": [
                            {
                                "name": "Run compliance scan",
                                "status": "completed",
                                "conclusion": "success",
                                "number": 2
                            }
                        ]
                    }
                ]
            })
        );

        responses
    }

    /// Mock GitLab CI API responses
    pub fn gitlab_ci_responses() -> HashMap<String, serde_json::Value> {
        let mut responses = HashMap::new();

        responses.insert(
            "pipeline_status".to_string(),
            serde_json::json!({
                "id": 123,
                "status": "success",
                "ref": "main",
                "web_url": "https://gitlab.com/test/project/-/pipelines/123"
            })
        );

        responses.insert(
            "pipeline_jobs".to_string(),
            serde_json::json!({
                "jobs": [
                    {
                        "id": 456,
                        "name": "compliance_scan",
                        "status": "success",
                        "stage": "compliance",
                        "artifacts": [
                            {
                                "file_type": "archive",
                                "filename": "compliance-report.json"
                            }
                        ]
                    }
                ]
            })
        );

        responses
    }

    /// Mock Jenkins API responses
    pub fn jenkins_responses() -> HashMap<String, serde_json::Value> {
        let mut responses = HashMap::new();

        responses.insert(
            "job_status".to_string(),
            serde_json::json!({
                "building": false,
                "result": "SUCCESS",
                "duration": 120000,
                "timestamp": 1640995200000,
                "url": "https://jenkins.example.com/job/test-job/123/"
            })
        );

        responses.insert(
            "build_artifacts".to_string(),
            serde_json::json!({
                "artifacts": [
                    {
                        "fileName": "compliance-report.json",
                        "relativePath": "compliance-report.json"
                    },
                    {
                        "fileName": "compliance-report.pdf",
                        "relativePath": "compliance-report.pdf"
                    }
                ]
            })
        );

        responses
    }
}