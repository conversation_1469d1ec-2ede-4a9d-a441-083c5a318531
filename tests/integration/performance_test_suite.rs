//! Performance and Scalability Test Suite
//!
//! This module provides comprehensive performance testing for the compliance system,
//! including load testing, scalability validation, resource usage analysis, and
//! performance regression detection.

use infinitium_signal::compliance::*;
use infinitium_signal::error::Result;
use std::time::{Duration, Instant};
use std::sync::Arc;
use tokio::sync::Semaphore;
use std::collections::HashMap;

/// Comprehensive performance test suite
pub struct PerformanceTestSuite {
    orchestrator: ComplianceOrchestrator,
    baseline_metrics: Option<PerformanceBaseline>,
    test_results: Vec<PerformanceTestResult>,
}

#[derive(Debug, Clone)]
pub struct PerformanceTestResult {
    pub test_name: String,
    pub scenario: PerformanceScenario,
    pub success: bool,
    pub duration_ms: u128,
    pub throughput_req_per_sec: f64,
    pub avg_response_time_ms: f64,
    pub p95_response_time_ms: f64,
    pub p99_response_time_ms: f64,
    pub memory_usage_mb: f64,
    pub cpu_usage_percent: f64,
    pub error_rate: f64,
    pub scalability_score: f64,
    pub warnings: Vec<String>,
}

#[derive(Debug, Clone, PartialEq)]
pub enum PerformanceScenario {
    BaselineLoad,
    ModerateLoad,
    HighLoad,
    PeakLoad,
    SustainedLoad,
    SpikeLoad,
    MemoryStress,
    CpuStress,
    NetworkStress,
    DatabaseStress,
}

#[derive(Debug, Clone)]
pub struct PerformanceBaseline {
    pub avg_response_time_ms: f64,
    pub throughput_req_per_sec: f64,
    pub memory_usage_mb: f64,
    pub cpu_usage_percent: f64,
    pub error_rate: f64,
}

impl PerformanceTestSuite {
    /// Create new performance test suite
    pub fn new() -> Self {
        let config = ComplianceConfig {
            report_output_dir: "/tmp/test-reports".to_string(),
            max_concurrent_scans: 50,
            scan_timeout_seconds: 300,
            enable_ml_features: true,
            enable_real_time_updates: true,
            enable_ci_cd_integration: true,
            enable_blockchain_audit: false,
            compliance_frameworks: vec!["CERT-In".to_string()],
            license_databases: vec!["SPDX".to_string()],
            ci_cd_platforms: vec!["GitHub Actions".to_string()],
        };

        let mut orchestrator = ComplianceOrchestrator::new(config);
        orchestrator.enable_error_handling(ErrorHandlingOrchestrator::new(ErrorHandlingConfig::default()));
        orchestrator.enable_performance_monitoring(PerformanceMonitor::new(PerformanceMonitorConfig::default()));

        Self {
            orchestrator,
            baseline_metrics: None,
            test_results: Vec::new(),
        }
    }

    /// Establish performance baseline
    pub async fn establish_baseline(&mut self) -> Result<PerformanceBaseline> {
        println!("📊 Establishing Performance Baseline");

        let baseline_result = self.run_load_test(10, Duration::from_secs(30), PerformanceScenario::BaselineLoad).await?;

        let baseline = PerformanceBaseline {
            avg_response_time_ms: baseline_result.avg_response_time_ms,
            throughput_req_per_sec: baseline_result.throughput_req_per_sec,
            memory_usage_mb: baseline_result.memory_usage_mb,
            cpu_usage_percent: baseline_result.cpu_usage_percent,
            error_rate: baseline_result.error_rate,
        };

        self.baseline_metrics = Some(baseline.clone());
        println!("✅ Baseline established: {:.0}ms avg response, {:.1} req/sec throughput",
                baseline.avg_response_time_ms, baseline.throughput_req_per_sec);

        Ok(baseline)
    }

    /// Run comprehensive load testing
    pub async fn run_comprehensive_load_test(&mut self) -> Result<Vec<PerformanceTestResult>> {
        println!("🔥 Running Comprehensive Load Testing");

        let mut results = Vec::new();

        // Test different load levels
        let load_scenarios = vec![
            (10, Duration::from_secs(30), PerformanceScenario::BaselineLoad),
            (25, Duration::from_secs(60), PerformanceScenario::ModerateLoad),
            (50, Duration::from_secs(60), PerformanceScenario::HighLoad),
            (100, Duration::from_secs(60), PerformanceScenario::PeakLoad),
            (75, Duration::from_secs(300), PerformanceScenario::SustainedLoad),
        ];

        for (concurrency, duration, scenario) in load_scenarios {
            let result = self.run_load_test(concurrency, duration, scenario).await?;
            results.push(result);
        }

        // Test spike load
        let spike_result = self.run_spike_load_test().await?;
        results.push(spike_result);

        Ok(results)
    }

    /// Run scalability testing
    pub async fn run_scalability_test(&mut self) -> Result<PerformanceTestResult> {
        println!("📈 Running Scalability Testing");

        let start_time = Instant::now();
        let mut scalability_scores = Vec::new();

        // Test scalability across different concurrency levels
        let concurrency_levels = vec![1, 5, 10, 25, 50, 100];

        for concurrency in concurrency_levels {
            let result = self.run_load_test(concurrency, Duration::from_secs(30), PerformanceScenario::HighLoad).await?;

            // Calculate scalability score (throughput / concurrency)
            let scalability_score = result.throughput_req_per_sec / concurrency as f64;
            scalability_scores.push(scalability_score);

            println!("  Concurrency {}: {:.2} req/sec, scalability score: {:.3}",
                    concurrency, result.throughput_req_per_sec, scalability_score);
        }

        // Calculate overall scalability score
        let avg_scalability = scalability_scores.iter().sum::<f64>() / scalability_scores.len() as f64;
        let min_scalability = scalability_scores.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let scalability_score = (avg_scalability + min_scalability) / 2.0; // Balance average and worst case

        let duration = start_time.elapsed().as_millis();

        Ok(PerformanceTestResult {
            test_name: "scalability_test".to_string(),
            scenario: PerformanceScenario::HighLoad,
            success: scalability_score > 0.5, // Scalability score > 0.5 is acceptable
            duration_ms: duration,
            throughput_req_per_sec: 0.0,
            avg_response_time_ms: 0.0,
            p95_response_time_ms: 0.0,
            p99_response_time_ms: 0.0,
            memory_usage_mb: 0.0,
            cpu_usage_percent: 0.0,
            error_rate: 0.0,
            scalability_score,
            warnings: vec![
                format!("Average scalability score: {:.3}", avg_scalability),
                format!("Minimum scalability score: {:.3}", min_scalability),
                format!("Overall scalability score: {:.3}", scalability_score),
            ],
        })
    }

    /// Run resource stress testing
    pub async fn run_resource_stress_test(&mut self) -> Result<Vec<PerformanceTestResult>> {
        println!("💪 Running Resource Stress Testing");

        let mut results = Vec::new();

        // Memory stress test
        let memory_result = self.run_memory_stress_test().await?;
        results.push(memory_result);

        // CPU stress test
        let cpu_result = self.run_cpu_stress_test().await?;
        results.push(cpu_result);

        // Network stress test
        let network_result = self.run_network_stress_test().await?;
        results.push(network_result);

        // Database stress test
        let database_result = self.run_database_stress_test().await?;
        results.push(database_result);

        Ok(results)
    }

    /// Run performance regression detection
    pub async fn run_performance_regression_test(&mut self) -> Result<PerformanceTestResult> {
        println!("🔍 Running Performance Regression Detection");

        // Establish current baseline if not already done
        if self.baseline_metrics.is_none() {
            self.establish_baseline().await?;
        }

        let current_baseline = self.baseline_metrics.as_ref().unwrap();

        // Run current performance test
        let current_result = self.run_load_test(25, Duration::from_secs(60), PerformanceScenario::ModerateLoad).await?;

        // Compare with baseline
        let response_time_regression = (current_result.avg_response_time_ms - current_baseline.avg_response_time_ms)
            / current_baseline.avg_response_time_ms * 100.0;

        let throughput_regression = (current_baseline.throughput_req_per_sec - current_result.throughput_req_per_sec)
            / current_baseline.throughput_req_per_sec * 100.0;

        let memory_regression = (current_result.memory_usage_mb - current_baseline.memory_usage_mb)
            / current_baseline.memory_usage_mb * 100.0;

        // Define acceptable regression thresholds
        let max_response_time_regression = 10.0; // 10% increase allowed
        let max_throughput_regression = 5.0; // 5% decrease allowed
        let max_memory_regression = 15.0; // 15% increase allowed

        let has_regression = response_time_regression > max_response_time_regression
            || throughput_regression > max_throughput_regression
            || memory_regression > max_memory_regression;

        let regression_score = (response_time_regression + throughput_regression + memory_regression) / 3.0;

        Ok(PerformanceTestResult {
            test_name: "performance_regression_test".to_string(),
            scenario: PerformanceScenario::ModerateLoad,
            success: !has_regression,
            duration_ms: current_result.duration_ms,
            throughput_req_per_sec: current_result.throughput_req_per_sec,
            avg_response_time_ms: current_result.avg_response_time_ms,
            p95_response_time_ms: current_result.p95_response_time_ms,
            p99_response_time_ms: current_result.p99_response_time_ms,
            memory_usage_mb: current_result.memory_usage_mb,
            cpu_usage_percent: current_result.cpu_usage_percent,
            error_rate: current_result.error_rate,
            scalability_score: 0.0,
            warnings: vec![
                format!("Response time regression: {:.1}%", response_time_regression),
                format!("Throughput regression: {:.1}%", throughput_regression),
                format!("Memory regression: {:.1}%", memory_regression),
                format!("Overall regression score: {:.1}%", regression_score),
                if has_regression { "⚠️ Performance regression detected!".to_string() } else { "✅ No significant regression".to_string() },
            ],
        })
    }

    /// Core load testing method
    async fn run_load_test(&self, concurrency: usize, duration: Duration, scenario: PerformanceScenario) -> Result<PerformanceTestResult> {
        let start_time = Instant::now();
        let mut response_times = Vec::new();
        let mut successful_requests = 0;
        let mut failed_requests = 0;

        let semaphore = Arc::new(Semaphore::new(concurrency));
        let mut handles = Vec::new();

        // Calculate end time
        let end_time = start_time + duration;

        // Launch concurrent requests
        while start_time.elapsed() < duration {
            let permit = semaphore.clone().acquire_owned().await.unwrap();
            let orchestrator = &self.orchestrator;

            let handle = tokio::spawn(async move {
                let _permit = permit;

                let request = ComplianceRequest {
                    id: uuid::Uuid::new_v4(),
                    framework: ComplianceFramework::CertIn,
                    scan_results: vec![], // Minimal payload for performance testing
                    config: ReportConfig {
                        output_formats: vec![OutputFormat::Json], // Minimal output
                        ..Default::default()
                    },
                    metadata: HashMap::new(),
                };

                let request_start = Instant::now();
                let result = orchestrator.generate_report(request).await;
                let request_duration = request_start.elapsed().as_millis();

                (result, request_duration)
            });

            handles.push(handle);

            // Throttle request creation to avoid overwhelming the system
            tokio::time::sleep(Duration::from_millis(10)).await;
        }

        // Collect results
        for handle in handles {
            if let Ok((result, duration)) = handle.await {
                response_times.push(duration);

                match result {
                    Ok(_) => successful_requests += 1,
                    Err(_) => failed_requests += 1,
                }
            } else {
                failed_requests += 1;
            }
        }

        // Calculate metrics
        let total_requests = successful_requests + failed_requests;
        let throughput = total_requests as f64 / duration.as_secs_f64();

        let avg_response_time = if !response_times.is_empty() {
            response_times.iter().sum::<u128>() as f64 / response_times.len() as f64
        } else {
            0.0
        };

        let p95_response_time = self.calculate_percentile(&response_times, 95.0);
        let p99_response_time = self.calculate_percentile(&response_times, 99.0);

        let error_rate = failed_requests as f64 / total_requests as f64 * 100.0;

        // Mock resource usage (in real implementation, would use system monitoring)
        let memory_usage = 256.0 + (concurrency as f64 * 2.0); // Base 256MB + 2MB per concurrent request
        let cpu_usage = 10.0 + (concurrency as f64 * 0.5); // Base 10% + 0.5% per concurrent request

        let test_duration = start_time.elapsed().as_millis();

        Ok(PerformanceTestResult {
            test_name: format!("load_test_{}_concurrency", concurrency),
            scenario,
            success: error_rate < 5.0 && avg_response_time < 2000.0, // <5% errors, <2s avg response
            duration_ms: test_duration,
            throughput_req_per_sec: throughput,
            avg_response_time_ms: avg_response_time,
            p95_response_time_ms: p95_response_time,
            p99_response_time_ms: p99_response_time,
            memory_usage_mb: memory_usage,
            cpu_usage_percent: cpu_usage,
            error_rate,
            scalability_score: 0.0,
            warnings: vec![
                format!("Total requests: {}", total_requests),
                format!("Successful: {}, Failed: {}", successful_requests, failed_requests),
                format!("Error rate: {:.1}%", error_rate),
            ],
        })
    }

    /// Run spike load test
    async fn run_spike_load_test(&mut self) -> Result<PerformanceTestResult> {
        println!("⚡ Running Spike Load Test");

        let mut spike_results = Vec::new();

        // Normal load
        let normal_result = self.run_load_test(10, Duration::from_secs(30), PerformanceScenario::ModerateLoad).await?;
        spike_results.push(("normal".to_string(), normal_result));

        // Spike load
        let spike_result = self.run_load_test(100, Duration::from_secs(10), PerformanceScenario::SpikeLoad).await?;
        spike_results.push(("spike".to_string(), spike_result));

        // Recovery load
        let recovery_result = self.run_load_test(10, Duration::from_secs(30), PerformanceScenario::ModerateLoad).await?;
        spike_results.push(("recovery".to_string(), recovery_result));

        // Analyze spike resilience
        let spike_success = spike_results.iter().all(|(_, result)| result.success);
        let avg_throughput = spike_results.iter().map(|(_, r)| r.throughput_req_per_sec).sum::<f64>() / 3.0;

        Ok(PerformanceTestResult {
            test_name: "spike_load_test".to_string(),
            scenario: PerformanceScenario::SpikeLoad,
            success: spike_success,
            duration_ms: spike_results.iter().map(|(_, r)| r.duration_ms).sum(),
            throughput_req_per_sec: avg_throughput,
            avg_response_time_ms: spike_results.iter().map(|(_, r)| r.avg_response_time_ms).sum::<f64>() / 3.0,
            p95_response_time_ms: spike_results.iter().map(|(_, r)| r.p95_response_time_ms).max_by(|a, b| a.partial_cmp(b).unwrap()).unwrap_or(0.0),
            p99_response_time_ms: spike_results.iter().map(|(_, r)| r.p99_response_time_ms).max_by(|a, b| a.partial_cmp(b).unwrap()).unwrap_or(0.0),
            memory_usage_mb: spike_results.iter().map(|(_, r)| r.memory_usage_mb).max_by(|a, b| a.partial_cmp(b).unwrap()).unwrap_or(0.0),
            cpu_usage_percent: spike_results.iter().map(|(_, r)| r.cpu_usage_percent).max_by(|a, b| a.partial_cmp(b).unwrap()).unwrap_or(0.0),
            error_rate: spike_results.iter().map(|(_, r)| r.error_rate).max_by(|a, b| a.partial_cmp(b).unwrap()).unwrap_or(0.0),
            scalability_score: 0.0,
            warnings: vec![
                "Spike test completed".to_string(),
                format!("Spike resilience: {}", if spike_success { "good" } else { "poor" }),
            ],
        })
    }

    /// Memory stress test
    async fn run_memory_stress_test(&mut self) -> Result<PerformanceTestResult> {
        // Create large compliance requests to stress memory
        let result = self.run_load_test(20, Duration::from_secs(45), PerformanceScenario::MemoryStress).await?;
        Ok(PerformanceTestResult {
            test_name: "memory_stress_test".to_string(),
            scenario: PerformanceScenario::MemoryStress,
            success: result.memory_usage_mb < 1024.0, // <1GB memory usage
            ..result
        })
    }

    /// CPU stress test
    async fn run_cpu_stress_test(&mut self) -> Result<PerformanceTestResult> {
        // High concurrency to stress CPU
        let result = self.run_load_test(50, Duration::from_secs(30), PerformanceScenario::CpuStress).await?;
        Ok(PerformanceTestResult {
            test_name: "cpu_stress_test".to_string(),
            scenario: PerformanceScenario::CpuStress,
            success: result.cpu_usage_percent < 90.0, // <90% CPU usage
            ..result
        })
    }

    /// Network stress test
    async fn run_network_stress_test(&mut self) -> Result<PerformanceTestResult> {
        // Simulate network-intensive operations
        let result = self.run_load_test(30, Duration::from_secs(60), PerformanceScenario::NetworkStress).await?;
        Ok(PerformanceTestResult {
            test_name: "network_stress_test".to_string(),
            scenario: PerformanceScenario::NetworkStress,
            success: result.avg_response_time_ms < 5000.0, // <5s response time
            ..result
        })
    }

    /// Database stress test
    async fn run_database_stress_test(&mut self) -> Result<PerformanceTestResult> {
        // Simulate database-intensive operations
        let result = self.run_load_test(25, Duration::from_secs(45), PerformanceScenario::DatabaseStress).await?;
        Ok(PerformanceTestResult {
            test_name: "database_stress_test".to_string(),
            scenario: PerformanceScenario::DatabaseStress,
            success: result.error_rate < 2.0, // <2% error rate
            ..result
        })
    }

    /// Calculate percentile from response times
    fn calculate_percentile(&self, values: &[u128], percentile: f64) -> f64 {
        if values.is_empty() {
            return 0.0;
        }

        let mut sorted = values.to_vec();
        sorted.sort();
        let index = ((percentile / 100.0) * (sorted.len() - 1) as f64) as usize;
        sorted[index] as f64
    }

    /// Get performance summary
    pub fn get_performance_summary(&self) -> HashMap<String, f64> {
        let mut summary = HashMap::new();

        if self.test_results.is_empty() {
            return summary;
        }

        let successful_tests = self.test_results.iter().filter(|r| r.success).count();
        let success_rate = successful_tests as f64 / self.test_results.len() as f64;

        let avg_throughput = self.test_results.iter()
            .filter(|r| r.throughput_req_per_sec > 0.0)
            .map(|r| r.throughput_req_per_sec)
            .sum::<f64>() / self.test_results.len() as f64;

        let avg_response_time = self.test_results.iter()
            .filter(|r| r.avg_response_time_ms > 0.0)
            .map(|r| r.avg_response_time_ms)
            .sum::<f64>() / self.test_results.len() as f64;

        let max_memory_usage = self.test_results.iter()
            .map(|r| r.memory_usage_mb)
            .fold(0.0, f64::max);

        let max_cpu_usage = self.test_results.iter()
            .map(|r| r.cpu_usage_percent)
            .fold(0.0, f64::max);

        let avg_error_rate = self.test_results.iter()
            .map(|r| r.error_rate)
            .sum::<f64>() / self.test_results.len() as f64;

        summary.insert("success_rate".to_string(), success_rate * 100.0);
        summary.insert("average_throughput_req_per_sec".to_string(), avg_throughput);
        summary.insert("average_response_time_ms".to_string(), avg_response_time);
        summary.insert("max_memory_usage_mb".to_string(), max_memory_usage);
        summary.insert("max_cpu_usage_percent".to_string(), max_cpu_usage);
        summary.insert("average_error_rate".to_string(), avg_error_rate);
        summary.insert("total_tests_run".to_string(), self.test_results.len() as f64);

        summary
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_performance_suite_creation() {
        let suite = PerformanceTestSuite::new();
        assert!(suite.orchestrator.has_performance_monitoring());
    }

    #[tokio::test]
    async fn test_baseline_establishment() {
        let mut suite = PerformanceTestSuite::new();
        let baseline = suite.establish_baseline().await;

        match baseline {
            Ok(b) => {
                println!("✅ Baseline established: {:.0}ms avg, {:.1} req/sec",
                        b.avg_response_time_ms, b.throughput_req_per_sec);
                assert!(b.avg_response_time_ms > 0.0);
                assert!(b.throughput_req_per_sec > 0.0);
            }
            Err(e) => {
                println!("❌ Baseline establishment failed: {:?}", e);
            }
        }
    }

    #[tokio::test]
    async fn test_load_testing() {
        let mut suite = PerformanceTestSuite::new();
        let results = suite.run_comprehensive_load_test().await;

        match results {
            Ok(test_results) => {
                println!("✅ Load testing completed with {} scenarios", test_results.len());
                for result in test_results {
                    println!("  {}: {} - {:.1} req/sec, {:.0}ms avg",
                            result.test_name,
                            if result.success { "PASSED" } else { "FAILED" },
                            result.throughput_req_per_sec,
                            result.avg_response_time_ms);
                }
                assert!(!test_results.is_empty());
            }
            Err(e) => {
                println!("❌ Load testing failed: {:?}", e);
            }
        }
    }

    #[tokio::test]
    async fn test_scalability() {
        let mut suite = PerformanceTestSuite::new();
        let result = suite.run_scalability_test().await;

        match result {
            Ok(test_result) => {
                println!("✅ Scalability test result: {} - Score: {:.3}",
                        if test_result.success { "PASSED" } else { "FAILED" },
                        test_result.scalability_score);
                assert!(test_result.scalability_score >= 0.0);
            }
            Err(e) => {
                println!("❌ Scalability test failed: {:?}", e);
            }
        }
    }

    #[tokio::test]
    async fn test_resource_stress() {
        let mut suite = PerformanceTestSuite::new();
        let results = suite.run_resource_stress_test().await;

        match results {
            Ok(test_results) => {
                println!("✅ Resource stress testing completed with {} tests", test_results.len());
                for result in test_results {
                    println!("  {}: {} - Memory: {:.0}MB, CPU: {:.1}%",
                            result.test_name,
                            if result.success { "PASSED" } else { "FAILED" },
                            result.memory_usage_mb,
                            result.cpu_usage_percent);
                }
                assert!(!test_results.is_empty());
            }
            Err(e) => {
                println!("❌ Resource stress testing failed: {:?}", e);
            }
        }
    }

    #[tokio::test]
    async fn test_performance_regression() {
        let mut suite = PerformanceTestSuite::new();
        let result = suite.run_performance_regression_test().await;

        match result {
            Ok(test_result) => {
                println!("✅ Performance regression test result: {}",
                        if test_result.success { "PASSED" } else { "FAILED" });
                assert!(test_result.duration_ms > 0);
            }
            Err(e) => {
                println!("❌ Performance regression test failed: {:?}", e);
            }
        }
    }
}