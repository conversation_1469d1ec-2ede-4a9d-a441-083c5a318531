//! Precision Validation for 99.9% Target
//!
//! This module provides empirical testing and validation of the 99.9% precision
//! target for license detection and compliance validation.

use infinitium_signal::compliance::*;
use infinitium_signal::error::Result;
use std::time::Instant;
use std::collections::HashMap;

/// Comprehensive precision validator for 99.9% target
pub struct PrecisionValidator {
    license_detector: Option<MLIntegrationOrchestrator>,
    compliance_validator: Option<ComplianceValidator>,
    test_datasets: Vec<PrecisionTestDataset>,
    validation_results: Vec<PrecisionValidationResult>,
}

#[derive(Debug, Clone)]
pub struct PrecisionTestDataset {
    pub name: String,
    pub license_texts: Vec<LicenseTestSample>,
    pub expected_license: String,
    pub category: TestCategory,
    pub difficulty_level: DifficultyLevel,
}

#[derive(Debug, Clone)]
pub struct LicenseTestSample {
    pub text: String,
    pub source: String,
    pub confidence_score: f64,
    pub is_edge_case: bool,
    pub expected_result: ExpectedResult,
}

#[derive(Debug, Clone, PartialEq)]
pub enum TestCategory {
    Standard,
    Permissive,
    Copyleft,
    Dual,
    Proprietary,
    EdgeCase,
    Ambiguous,
    Corrupted,
}

#[derive(Debug, Clone, PartialEq)]
pub enum DifficultyLevel {
    Easy,
    Medium,
    Hard,
    Extreme,
}

#[derive(Debug, Clone, PartialEq)]
pub enum ExpectedResult {
    Correct(String), // Expected license ID
    Ambiguous(Vec<String>), // Multiple possible licenses
    Unknown, // Should not be detected
}

#[derive(Debug, Clone)]
pub struct PrecisionValidationResult {
    pub test_name: String,
    pub total_samples: usize,
    pub correct_detections: usize,
    pub incorrect_detections: usize,
    pub false_positives: usize,
    pub false_negatives: usize,
    pub precision: f64,
    pub recall: f64,
    pub f1_score: f64,
    pub accuracy: f64,
    pub target_achieved: bool,
    pub error_margin: f64,
    pub confidence_interval: ConfidenceInterval,
    pub detailed_results: Vec<DetailedTestResult>,
}

#[derive(Debug, Clone)]
pub struct DetailedTestResult {
    pub sample_text: String,
    pub expected_license: String,
    pub detected_license: Option<String>,
    pub confidence_score: f64,
    pub is_correct: bool,
    pub error_type: Option<ErrorType>,
}

#[derive(Debug, Clone, PartialEq)]
pub enum ErrorType {
    FalsePositive,
    FalseNegative,
    IncorrectLicense,
    LowConfidence,
}

#[derive(Debug, Clone)]
pub struct ConfidenceInterval {
    pub lower_bound: f64,
    pub upper_bound: f64,
    pub confidence_level: f64,
}

impl PrecisionValidator {
    /// Create new precision validator
    pub fn new() -> Self {
        let mut test_datasets = Vec::new();

        // Initialize comprehensive test datasets
        test_datasets.extend(Self::create_standard_precision_datasets());
        test_datasets.extend(Self::create_edge_case_datasets());
        test_datasets.extend(Self::create_adversarial_datasets());
        test_datasets.extend(Self::create_large_scale_datasets());

        Self {
            license_detector: Some(MLIntegrationOrchestrator::new(MLIntegrationConfig::default())),
            compliance_validator: Some(ComplianceValidator::new()),
            test_datasets,
            validation_results: Vec::new(),
        }
    }

    /// Validate 99.9% precision target through empirical testing
    pub async fn validate_precision_target(&mut self) -> Result<PrecisionValidationResult> {
        let start_time = Instant::now();
        println!("🎯 Validating 99.9% Precision Target");

        let mut total_samples = 0;
        let mut total_correct = 0;
        let mut total_incorrect = 0;
        let mut total_false_positives = 0;
        let mut total_false_negatives = 0;
        let mut all_detailed_results = Vec::new();

        // Test each dataset
        for dataset in &self.test_datasets {
            println!("  Testing dataset: {} ({:?})", dataset.name, dataset.difficulty_level);

            let dataset_result = self.test_dataset_precision(dataset).await?;
            total_samples += dataset_result.total_samples;
            total_correct += dataset_result.correct_detections;
            total_incorrect += dataset_result.incorrect_detections;
            total_false_positives += dataset_result.false_positives;
            total_false_negatives += dataset_result.false_negatives;

            all_detailed_results.extend(dataset_result.detailed_results);
            self.validation_results.push(dataset_result);
        }

        // Calculate overall precision metrics
        let precision = if (total_correct + total_false_positives) > 0 {
            total_correct as f64 / (total_correct + total_false_positives) as f64
        } else {
            0.0
        };

        let recall = if (total_correct + total_false_negatives) > 0 {
            total_correct as f64 / (total_correct + total_false_negatives) as f64
        } else {
            0.0
        };

        let f1_score = if (precision + recall) > 0.0 {
            2.0 * (precision * recall) / (precision + recall)
        } else {
            0.0
        };

        let accuracy = if total_samples > 0 {
            total_correct as f64 / total_samples as f64
        } else {
            0.0
        };

        // Check if 99.9% target is achieved
        let target_achieved = precision >= 0.999;
        let error_margin = 0.001 - (1.0 - precision); // Distance from 99.9% target

        // Calculate confidence interval
        let confidence_interval = self.calculate_confidence_interval(precision, total_samples);

        let duration = start_time.elapsed().as_millis();

        let result = PrecisionValidationResult {
            test_name: "precision_target_validation".to_string(),
            total_samples,
            correct_detections: total_correct,
            incorrect_detections: total_incorrect,
            false_positives: total_false_positives,
            false_negatives: total_false_negatives,
            precision: precision * 100.0, // Convert to percentage
            recall: recall * 100.0,
            f1_score: f1_score * 100.0,
            accuracy: accuracy * 100.0,
            target_achieved,
            error_margin,
            confidence_interval,
            detailed_results: all_detailed_results,
        };

        println!("✅ Precision validation completed in {}ms", duration);
        println!("📊 Results:");
        println!("  - Precision: {:.4}%", result.precision);
        println!("  - Recall: {:.2}%", result.recall);
        println!("  - F1 Score: {:.2}%", result.f1_score);
        println!("  - Accuracy: {:.2}%", result.accuracy);
        println!("  - Target Achieved: {}", if result.target_achieved { "YES" } else { "NO" });
        println!("  - Error Margin: {:.4}%", result.error_margin * 100.0);

        if result.target_achieved {
            println!("🎉 99.9% precision target ACHIEVED!");
        } else {
            println!("⚠️ 99.9% precision target NOT achieved. Need improvement.");
        }

        Ok(result)
    }

    /// Test precision for a single dataset
    async fn test_dataset_precision(&self, dataset: &PrecisionTestDataset) -> Result<PrecisionValidationResult> {
        let mut correct_detections = 0;
        let mut incorrect_detections = 0;
        let mut false_positives = 0;
        let mut false_negatives = 0;
        let mut detailed_results = Vec::new();

        for sample in &dataset.license_texts {
            let detection_result = self.detect_license_with_precision(&sample.text).await?;

            let detected_license = detection_result.as_ref().map(|r| r.license.clone());
            let confidence_score = detection_result.as_ref().map(|r| r.confidence).unwrap_or(0.0);

            let (is_correct, error_type) = self.evaluate_detection_result(
                &detected_license,
                &sample.expected_result,
                confidence_score,
            );

            if is_correct {
                correct_detections += 1;
            } else {
                incorrect_detections += 1;

                // Classify error type
                match error_type {
                    Some(ErrorType::FalsePositive) => false_positives += 1,
                    Some(ErrorType::FalseNegative) => false_negatives += 1,
                    _ => {} // Other error types don't affect precision calculation
                }
            }

            detailed_results.push(DetailedTestResult {
                sample_text: sample.text.clone(),
                expected_license: self.expected_result_to_string(&sample.expected_result),
                detected_license,
                confidence_score,
                is_correct,
                error_type,
            });
        }

        let total_samples = dataset.license_texts.len();
        let precision = if (correct_detections + false_positives) > 0 {
            correct_detections as f64 / (correct_detections + false_positives) as f64
        } else {
            0.0
        };

        let recall = if (correct_detections + false_negatives) > 0 {
            correct_detections as f64 / (correct_detections + false_negatives) as f64
        } else {
            0.0
        };

        let f1_score = if (precision + recall) > 0.0 {
            2.0 * (precision * recall) / (precision + recall)
        } else {
            0.0
        };

        let accuracy = correct_detections as f64 / total_samples as f64;

        Ok(PrecisionValidationResult {
            test_name: dataset.name.clone(),
            total_samples,
            correct_detections,
            incorrect_detections,
            false_positives,
            false_negatives,
            precision: precision * 100.0,
            recall: recall * 100.0,
            f1_score: f1_score * 100.0,
            accuracy: accuracy * 100.0,
            target_achieved: precision >= 0.999,
            error_margin: 0.001 - (1.0 - precision),
            confidence_interval: self.calculate_confidence_interval(precision, total_samples),
            detailed_results,
        })
    }

    /// Detect license with precision tracking
    async fn detect_license_with_precision(&self, text: &str) -> Result<Option<DetectionResult>> {
        if let Some(detector) = &self.license_detector {
            let result = detector.detect_license_hybrid(text).await?;
            Ok(result.map(|r| DetectionResult {
                license: r.detected_license,
                confidence: r.confidence_score,
                method: r.detection_method.to_string(),
            }))
        } else {
            // Fallback: simple pattern matching for testing
            Ok(self.simple_license_detection(text))
        }
    }

    /// Simple license detection for fallback
    fn simple_license_detection(&self, text: &str) -> Option<DetectionResult> {
        let text_lower = text.to_lowercase();

        if text_lower.contains("mit license") || text_lower.contains("mit") && text_lower.contains("permission") {
            Some(DetectionResult {
                license: "MIT".to_string(),
                confidence: 0.95,
                method: "pattern_matching".to_string(),
            })
        } else if text_lower.contains("apache license") || text_lower.contains("apache") && text_lower.contains("version 2") {
            Some(DetectionResult {
                license: "Apache-2.0".to_string(),
                confidence: 0.90,
                method: "pattern_matching".to_string(),
            })
        } else if text_lower.contains("gnu general public license") || text_lower.contains("gpl") {
            Some(DetectionResult {
                license: "GPL-3.0".to_string(),
                confidence: 0.85,
                method: "pattern_matching".to_string(),
            })
        } else {
            None
        }
    }

    /// Evaluate detection result
    fn evaluate_detection_result(
        &self,
        detected: &Option<String>,
        expected: &ExpectedResult,
        confidence: f64,
    ) -> (bool, Option<ErrorType>) {
        match expected {
            ExpectedResult::Correct(expected_license) => {
                match detected {
                    Some(detected_license) if detected_license == expected_license => {
                        if confidence >= 0.8 {
                            (true, None)
                        } else {
                            (false, Some(ErrorType::LowConfidence))
                        }
                    }
                    Some(detected_license) => (false, Some(ErrorType::IncorrectLicense)),
                    None => (false, Some(ErrorType::FalseNegative)),
                }
            }
            ExpectedResult::Ambiguous(possible_licenses) => {
                match detected {
                    Some(detected_license) if possible_licenses.contains(detected_license) => (true, None),
                    Some(_) => (false, Some(ErrorType::IncorrectLicense)),
                    None => (false, Some(ErrorType::FalseNegative)),
                }
            }
            ExpectedResult::Unknown => {
                match detected {
                    Some(_) => (false, Some(ErrorType::FalsePositive)),
                    None => (true, None),
                }
            }
        }
    }

    /// Convert expected result to string for reporting
    fn expected_result_to_string(&self, expected: &ExpectedResult) -> String {
        match expected {
            ExpectedResult::Correct(license) => license.clone(),
            ExpectedResult::Ambiguous(licenses) => format!("Ambiguous: {:?}", licenses),
            ExpectedResult::Unknown => "Unknown".to_string(),
        }
    }

    /// Calculate confidence interval for precision
    fn calculate_confidence_interval(&self, precision: f64, sample_size: usize) -> ConfidenceInterval {
        // Simplified confidence interval calculation using normal approximation
        let standard_error = ((precision * (1.0 - precision)) / sample_size as f64).sqrt();
        let z_score = 1.96; // 95% confidence level
        let margin_of_error = z_score * standard_error;

        ConfidenceInterval {
            lower_bound: (precision - margin_of_error).max(0.0) * 100.0,
            upper_bound: (precision + margin_of_error).min(1.0) * 100.0,
            confidence_level: 95.0,
        }
    }

    /// Run precision improvement recommendations
    pub fn get_precision_improvements(&self) -> Vec<String> {
        let mut recommendations = Vec::new();

        for result in &self.validation_results {
            if result.precision < 99.9 {
                recommendations.push(format!(
                    "Dataset '{}' has {:.4}% precision, needs improvement",
                    result.test_name, result.precision
                ));

                // Analyze error patterns
                let false_positive_rate = result.false_positives as f64 / result.total_samples as f64;
                let false_negative_rate = result.false_negatives as f64 / result.total_samples as f64;

                if false_positive_rate > 0.001 {
                    recommendations.push(format!(
                        "High false positive rate ({:.2}%) in {} - review detection thresholds",
                        false_positive_rate * 100.0, result.test_name
                    ));
                }

                if false_negative_rate > 0.001 {
                    recommendations.push(format!(
                        "High false negative rate ({:.2}%) in {} - improve pattern recognition",
                        false_negative_rate * 100.0, result.test_name
                    ));
                }
            }
        }

        if recommendations.is_empty() {
            recommendations.push("All datasets meet or exceed 99.9% precision target!".to_string());
        }

        recommendations
    }

    /// Create standard precision test datasets
    fn create_standard_precision_datasets() -> Vec<PrecisionTestDataset> {
        vec![
            PrecisionTestDataset {
                name: "MIT_License_Standard".to_string(),
                expected_license: "MIT".to_string(),
                category: TestCategory::Permissive,
                difficulty_level: DifficultyLevel::Easy,
                license_texts: vec![
                    LicenseTestSample {
                        text: "MIT License\n\nCopyright (c) 2023 Test Organization\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.".to_string(),
                        source: "SPDX Standard".to_string(),
                        confidence_score: 1.0,
                        is_edge_case: false,
                        expected_result: ExpectedResult::Correct("MIT".to_string()),
                    },
                ],
            },
            PrecisionTestDataset {
                name: "Apache_2_0_Standard".to_string(),
                expected_license: "Apache-2.0".to_string(),
                category: TestCategory::Permissive,
                difficulty_level: DifficultyLevel::Easy,
                license_texts: vec![
                    LicenseTestSample {
                        text: "Apache License\nVersion 2.0, January 2004\nhttp://www.apache.org/licenses/\n\nTERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION\n\n1. Definitions.".to_string(),
                        source: "SPDX Standard".to_string(),
                        confidence_score: 1.0,
                        is_edge_case: false,
                        expected_result: ExpectedResult::Correct("Apache-2.0".to_string()),
                    },
                ],
            },
        ]
    }

    /// Create edge case datasets
    fn create_edge_case_datasets() -> Vec<PrecisionTestDataset> {
        vec![
            PrecisionTestDataset {
                name: "MIT_Edge_Cases".to_string(),
                expected_license: "MIT".to_string(),
                category: TestCategory::EdgeCase,
                difficulty_level: DifficultyLevel::Hard,
                license_texts: vec![
                    LicenseTestSample {
                        text: "// MIT License\n// Copyright (c) 2023\n// Permission granted...".to_string(),
                        source: "Code Comments".to_string(),
                        confidence_score: 0.8,
                        is_edge_case: true,
                        expected_result: ExpectedResult::Correct("MIT".to_string()),
                    },
                    LicenseTestSample {
                        text: "/*\n * MIT Licence (note British spelling)\n * Copyright 2023\n */".to_string(),
                        source: "Typo Test".to_string(),
                        confidence_score: 0.6,
                        is_edge_case: true,
                        expected_result: ExpectedResult::Correct("MIT".to_string()),
                    },
                ],
            },
        ]
    }

    /// Create adversarial datasets
    fn create_adversarial_datasets() -> Vec<PrecisionTestDataset> {
        vec![
            PrecisionTestDataset {
                name: "Adversarial_Examples".to_string(),
                expected_license: "None".to_string(),
                category: TestCategory::Ambiguous,
                difficulty_level: DifficultyLevel::Extreme,
                license_texts: vec![
                    LicenseTestSample {
                        text: "This is not a license at all. Just some random text about software and permissions.".to_string(),
                        source: "False Positive Test".to_string(),
                        confidence_score: 0.0,
                        is_edge_case: false,
                        expected_result: ExpectedResult::Unknown,
                    },
                    LicenseTestSample {
                        text: "MIT is a great university but this text has nothing to do with licensing.".to_string(),
                        source: "Context Confusion".to_string(),
                        confidence_score: 0.0,
                        is_edge_case: false,
                        expected_result: ExpectedResult::Unknown,
                    },
                ],
            },
        ]
    }

    /// Create large-scale test datasets
    fn create_large_scale_datasets() -> Vec<PrecisionTestDataset> {
        vec![
            PrecisionTestDataset {
                name: "Large_Scale_Test".to_string(),
                expected_license: "MIT".to_string(),
                category: TestCategory::Standard,
                difficulty_level: DifficultyLevel::Medium,
                license_texts: (0..100).map(|i| LicenseTestSample {
                    text: format!("MIT License\n\nCopyright (c) 2023 Test Organization {}\n\nPermission is hereby granted...", i),
                    source: format!("Generated Sample {}", i),
                    confidence_score: 0.95,
                    is_edge_case: false,
                    expected_result: ExpectedResult::Correct("MIT".to_string()),
                }).collect(),
            },
        ]
    }

    /// Get precision summary
    pub fn get_precision_summary(&self) -> HashMap<String, f64> {
        let mut summary = HashMap::new();

        if self.validation_results.is_empty() {
            return summary;
        }

        let total_tests = self.validation_results.len();
        let target_achieved_count = self.validation_results.iter()
            .filter(|r| r.target_achieved)
            .count();

        let avg_precision = self.validation_results.iter()
            .map(|r| r.precision)
            .sum::<f64>() / total_tests as f64;

        let avg_recall = self.validation_results.iter()
            .map(|r| r.recall)
            .sum::<f64>() / total_tests as f64;

        let avg_f1 = self.validation_results.iter()
            .map(|r| r.f1_score)
            .sum::<f64>() / total_tests as f64;

        let total_samples = self.validation_results.iter()
            .map(|r| r.total_samples)
            .sum::<usize>();

        let total_correct = self.validation_results.iter()
            .map(|r| r.correct_detections)
            .sum::<usize>();

        summary.insert("target_achieved_datasets".to_string(), target_achieved_count as f64);
        summary.insert("total_datasets".to_string(), total_tests as f64);
        summary.insert("average_precision".to_string(), avg_precision);
        summary.insert("average_recall".to_string(), avg_recall);
        summary.insert("average_f1_score".to_string(), avg_f1);
        summary.insert("total_samples_tested".to_string(), total_samples as f64);
        summary.insert("total_correct_detections".to_string(), total_correct as f64);
        summary.insert("overall_accuracy".to_string(), if total_samples > 0 { total_correct as f64 / total_samples as f64 * 100.0 } else { 0.0 });

        summary
    }
}

/// Detection result structure
#[derive(Debug)]
struct DetectionResult {
    license: String,
    confidence: f64,
    method: String,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_precision_validator_creation() {
        let validator = PrecisionValidator::new();
        assert!(!validator.test_datasets.is_empty());
    }

    #[tokio::test]
    async fn test_precision_target_validation() {
        let mut validator = PrecisionValidator::new();
        let result = validator.validate_precision_target().await;

        match result {
            Ok(validation_result) => {
                println!("✅ Precision validation completed");
                println!("📊 Results:");
                println!("  - Precision: {:.4}%", validation_result.precision);
                println!("  - Recall: {:.2}%", validation_result.recall);
                println!("  - F1 Score: {:.2}%", validation_result.f1_score);
                println!("  - Accuracy: {:.2}%", validation_result.accuracy);
                println!("  - Target Achieved: {}", validation_result.target_achieved);
                println!("  - Confidence Interval: {:.2}% - {:.2}%",
                        validation_result.confidence_interval.lower_bound,
                        validation_result.confidence_interval.upper_bound);

                if validation_result.target_achieved {
                    println!("🎉 99.9% precision target ACHIEVED!");
                } else {
                    println!("⚠️ 99.9% precision target NOT achieved");
                    println!("💡 Improvement suggestions:");
                    for suggestion in validator.get_precision_improvements() {
                        println!("  - {}", suggestion);
                    }
                }

                // Basic assertions
                assert!(validation_result.total_samples > 0, "Should test some samples");
                assert!(validation_result.precision >= 0.0, "Precision should be valid");
                assert!(validation_result.confidence_interval.lower_bound <= validation_result.confidence_interval.upper_bound,
                       "Confidence interval should be valid");
            }
            Err(e) => {
                println!("❌ Precision validation failed: {:?}", e);
            }
        }
    }

    #[tokio::test]
    async fn test_precision_summary() {
        let validator = PrecisionValidator::new();
        let summary = validator.get_precision_summary();

        println!("📈 Precision Summary:");
        for (key, value) in &summary {
            println!("  {}: {:.2}", key, value);
        }

        assert!(!summary.is_empty(), "Should have summary data");
    }
}