# Vulnerable Demo Application

⚠️ **WARNING: This application contains intentional security vulnerabilities for demonstration purposes. DO NOT use in production environments.**

## Overview

This Node.js application is designed to showcase the security analysis capabilities of the Infinitium Signal cybersecurity platform. It contains multiple types of vulnerabilities that can be detected by various security tools.

## Vulnerabilities Included

### 1. Dependency Vulnerabilities
- **Outdated packages** with known CVEs
- **Vulnerable versions** of popular libraries
- **Supply chain risks** from compromised packages

### 2. Code-Level Vulnerabilities
- **SQL Injection** - Direct string concatenation in database queries
- **Cross-Site Scripting (XSS)** - Unescaped user input in HTML output
- **Command Injection** - Direct execution of user-controlled commands
- **Path Traversal** - Unrestricted file access
- **Server-Side Template Injection** - Unsafe template compilation
- **Prototype Pollution** - Unsafe object merging

### 3. Cryptographic Vulnerabilities
- **Weak encryption algorithms** (DES)
- **Hardcoded secrets** and keys
- **Predictable initialization vectors**
- **Weak password hashing**

### 4. Authentication & Authorization Issues
- **Authentication bypass** mechanisms
- **Weak JWT secrets**
- **Hardcoded credentials**
- **Insecure session management**

### 5. Configuration Vulnerabilities
- **Exposed sensitive information**
- **Insecure CORS policies**
- **Debug mode enabled**
- **Insecure file upload settings**

## Installation

```bash
# Install dependencies (contains vulnerable packages)
npm install

# Start the application
npm start
```

## API Endpoints

| Endpoint | Method | Vulnerability | Description |
|----------|--------|---------------|-------------|
| `/login` | POST | SQL Injection | Login with username/password |
| `/search` | GET | XSS | Search functionality |
| `/ping` | POST | Command Injection | Network ping utility |
| `/file/:filename` | GET | Path Traversal | File download |
| `/encrypt` | POST | Weak Crypto | Data encryption |
| `/admin` | GET | Auth Bypass | Admin panel access |
| `/debug` | GET | Info Disclosure | Debug information |
| `/upload` | POST | File Upload | Insecure file upload |
| `/template` | POST | SSTI | Template rendering |
| `/merge` | POST | Prototype Pollution | Object merging |

## Security Testing

This application is designed to be tested with:

- **Trivy** - Vulnerability scanner
- **Syft** - SBOM generator
- **Grype** - Vulnerability assessment
- **Snyk** - Security analysis
- **OWASP ZAP** - Web application scanner
- **Burp Suite** - Security testing platform

## Example Exploits

### SQL Injection
```bash
curl -X POST http://localhost:3000/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin'\'' OR 1=1 --", "password": "anything"}'
```

### XSS
```bash
curl "http://localhost:3000/search?q=<script>alert('XSS')</script>"
```

### Command Injection
```bash
curl -X POST http://localhost:3000/ping \
  -H "Content-Type: application/json" \
  -d '{"host": "127.0.0.1; cat /etc/passwd"}'
```

### Path Traversal
```bash
curl "http://localhost:3000/file/../../etc/passwd"
```

## Disclaimer

This application is created solely for educational and demonstration purposes. It contains intentional security vulnerabilities and should never be deployed in a production environment or accessible from the internet.

## License

MIT License - For educational use only.
