#!/usr/bin/env node

/**
 * DELIBERATELY VULNERABLE APPLICATION
 * 
 * WARNING: This application contains intentional security vulnerabilities
 * for demonstration purposes. DO NOT use in production environments.
 * 
 * Vulnerabilities included:
 * - SQL Injection
 * - Cross-Site Scripting (XSS)
 * - Command Injection
 * - Path Traversal
 * - Insecure Cryptography
 * - Authentication Bypass
 * - Information Disclosure
 * - Insecure Direct Object References
 */

const express = require('express');
const mysql = require('mysql');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const bodyParser = require('body-parser');
const cookieParser = require('cookie-parser');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const multer = require('multer');
const handlebars = require('handlebars');
const _ = require('lodash');

const app = express();
const PORT = process.env.PORT || 3000;

// Insecure configuration
const JWT_SECRET = 'weak_secret_123'; // Hardcoded secret
const ADMIN_PASSWORD = 'admin123'; // Hardcoded password

// Middleware setup
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(cookieParser());

// Insecure CORS configuration
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*'); // Allows any origin
    res.header('Access-Control-Allow-Headers', '*');
    res.header('Access-Control-Allow-Methods', '*');
    next();
});

// Vulnerable database connection (hardcoded credentials)
const db = mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'password123', // Hardcoded password
    database: 'vulnerable_db'
});

// File upload configuration (insecure)
const upload = multer({ 
    dest: 'uploads/',
    limits: { fileSize: 10000000 } // No file type restrictions
});

// VULNERABILITY 1: SQL Injection
app.post('/login', (req, res) => {
    const { username, password } = req.body;
    
    // Direct string concatenation - SQL injection vulnerability
    const query = `SELECT * FROM users WHERE username = '${username}' AND password = '${password}'`;
    
    db.query(query, (err, results) => {
        if (err) {
            return res.status(500).json({ error: err.message });
        }
        
        if (results.length > 0) {
            // Insecure JWT generation
            const token = jwt.sign({ userId: results[0].id }, JWT_SECRET);
            res.json({ success: true, token: token });
        } else {
            res.status(401).json({ error: 'Invalid credentials' });
        }
    });
});

// VULNERABILITY 2: Cross-Site Scripting (XSS)
app.get('/search', (req, res) => {
    const searchTerm = req.query.q;
    
    // Direct output without sanitization - XSS vulnerability
    const html = `
        <html>
            <body>
                <h1>Search Results</h1>
                <p>You searched for: ${searchTerm}</p>
                <div id="results">No results found</div>
            </body>
        </html>
    `;
    
    res.send(html);
});

// VULNERABILITY 3: Command Injection
app.post('/ping', (req, res) => {
    const { host } = req.body;
    
    // Direct command execution - Command injection vulnerability
    exec(`ping -c 4 ${host}`, (error, stdout, stderr) => {
        if (error) {
            return res.status(500).json({ error: error.message });
        }
        res.json({ output: stdout });
    });
});

// VULNERABILITY 4: Path Traversal
app.get('/file/:filename', (req, res) => {
    const filename = req.params.filename;
    
    // Direct file access without validation - Path traversal vulnerability
    const filePath = path.join(__dirname, 'files', filename);
    
    fs.readFile(filePath, 'utf8', (err, data) => {
        if (err) {
            return res.status(404).json({ error: 'File not found' });
        }
        res.send(data);
    });
});

// VULNERABILITY 5: Insecure Cryptography
app.post('/encrypt', (req, res) => {
    const { data } = req.body;
    
    // Weak encryption algorithm
    const cipher = crypto.createCipher('des', 'weak_key'); // DES is deprecated
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    res.json({ encrypted: encrypted });
});

// VULNERABILITY 6: Authentication Bypass
app.get('/admin', (req, res) => {
    const token = req.headers.authorization;
    
    // Weak token validation
    if (token === 'Bearer admin_token' || req.query.debug === 'true') {
        res.json({ 
            message: 'Welcome admin!',
            users: ['admin', 'user1', 'user2'],
            secrets: ['api_key_123', 'db_password_456']
        });
    } else {
        res.status(401).json({ error: 'Unauthorized' });
    }
});

// VULNERABILITY 7: Information Disclosure
app.get('/debug', (req, res) => {
    // Exposing sensitive information
    res.json({
        environment: process.env,
        config: {
            jwt_secret: JWT_SECRET,
            admin_password: ADMIN_PASSWORD,
            db_config: {
                host: 'localhost',
                user: 'root',
                password: 'password123'
            }
        },
        stack_trace: new Error().stack
    });
});

// VULNERABILITY 8: Insecure File Upload
app.post('/upload', upload.single('file'), (req, res) => {
    if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
    }
    
    // No file type validation - allows any file type
    const newPath = path.join(__dirname, 'uploads', req.file.originalname);
    fs.renameSync(req.file.path, newPath);
    
    res.json({ 
        message: 'File uploaded successfully',
        filename: req.file.originalname,
        path: newPath
    });
});

// VULNERABILITY 9: Server-Side Template Injection
app.post('/template', (req, res) => {
    const { template, data } = req.body;
    
    // Direct template compilation - SSTI vulnerability
    const compiledTemplate = handlebars.compile(template);
    const result = compiledTemplate(data);
    
    res.send(result);
});

// VULNERABILITY 10: Prototype Pollution
app.post('/merge', (req, res) => {
    const { source, target } = req.body;
    
    // Unsafe merge operation - Prototype pollution vulnerability
    const result = _.merge({}, target, source);
    
    res.json({ merged: result });
});

// Error handling that exposes stack traces
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({
        error: err.message,
        stack: err.stack, // Exposing stack trace
        details: err
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`Vulnerable demo app running on port ${PORT}`);
    console.log('WARNING: This application contains intentional vulnerabilities!');
    console.log('Available endpoints:');
    console.log('  POST /login - SQL injection vulnerability');
    console.log('  GET  /search?q=<term> - XSS vulnerability');
    console.log('  POST /ping - Command injection vulnerability');
    console.log('  GET  /file/<filename> - Path traversal vulnerability');
    console.log('  POST /encrypt - Weak cryptography');
    console.log('  GET  /admin - Authentication bypass');
    console.log('  GET  /debug - Information disclosure');
    console.log('  POST /upload - Insecure file upload');
    console.log('  POST /template - Server-side template injection');
    console.log('  POST /merge - Prototype pollution');
});

module.exports = app;
