/**
 * INSECURE CONFIGURATION FILE
 * Contains hardcoded secrets and insecure settings
 */

module.exports = {
    // Database configuration with hardcoded credentials
    database: {
        host: 'localhost',
        port: 3306,
        username: 'root',
        password: 'password123', // Hardcoded password
        database: 'vulnerable_db',
        ssl: false, // Insecure connection
        connectionLimit: 100
    },
    
    // JWT configuration with weak secret
    jwt: {
        secret: 'weak_secret_123', // Weak secret
        expiresIn: '24h',
        algorithm: 'HS256'
    },
    
    // API keys exposed in configuration
    apiKeys: {
        stripe: 'sk_test_4eC39HqLyjWDarjtT1zdp7dc',
        aws: 'AKIAIOSFODNN7EXAMPLE',
        google: 'AIzaSyDdVgKwhZl-aNLqx4MuYcTAA_9C_wMzCLU'
    },
    
    // Admin credentials
    admin: {
        username: 'admin',
        password: 'admin123', // Weak password
        email: '<EMAIL>'
    },
    
    // Encryption settings (insecure)
    encryption: {
        algorithm: 'des', // Deprecated algorithm
        key: 'weak_key',
        iv: '12345678' // Predictable IV
    },
    
    // Session configuration (insecure)
    session: {
        secret: 'session_secret_123',
        secure: false, // Not using HTTPS
        httpOnly: false, // Allows client-side access
        maxAge: 86400000 // 24 hours
    },
    
    // CORS configuration (too permissive)
    cors: {
        origin: '*', // Allows any origin
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: '*'
    },
    
    // File upload settings (insecure)
    upload: {
        destination: './uploads/',
        maxFileSize: 10000000, // 10MB
        allowedTypes: ['*'], // Allows any file type
        preserveOriginalName: true
    },
    
    // Logging configuration (exposes sensitive data)
    logging: {
        level: 'debug',
        logPasswords: true, // Logs passwords
        logTokens: true, // Logs JWT tokens
        logApiKeys: true, // Logs API keys
        file: './logs/app.log'
    },
    
    // External service URLs (insecure)
    services: {
        paymentGateway: 'http://payment.example.com/api', // HTTP instead of HTTPS
        userService: 'http://users.internal.com/api',
        emailService: 'http://mail.example.com/send'
    },
    
    // Feature flags
    features: {
        debugMode: true, // Debug mode enabled in production
        sqlLogging: true, // SQL queries logged
        stackTraces: true, // Stack traces exposed
        adminBypass: true // Admin bypass enabled
    }
};
