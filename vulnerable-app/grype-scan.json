{"matches": [{"vulnerability": {"id": "GHSA-3jfq-g458-7qm9", "dataSource": "https://github.com/advisories/GHSA-3jfq-g458-7qm9", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Arbitrary File Creation/Overwrite due to insufficient absolute path sanitization", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:N", "metrics": {"baseScore": 8.2, "exploitabilityScore": 1.9, "impactScore": 5.8}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-32804", "epss": 0.87249, "percentile": 0.9941, "date": "2025-08-23"}], "fix": {"versions": ["4.4.14"], "state": "fixed"}, "advisories": [], "risk": 68.49046499999999}, "relatedVulnerabilities": [{"id": "CVE-2021-32804", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2021-32804", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://cert-portal.siemens.com/productcert/pdf/ssa-389290.pdf", "https://github.com/npm/node-tar/commit/1f036ca23f64a547bdd6c79c1a44bc62e8115da4", "https://github.com/npm/node-tar/security/advisories/GHSA-3jfq-g458-7qm9", "https://www.npmjs.com/advisories/1770", "https://www.npmjs.com/package/tar", "https://www.oracle.com/security-alerts/cpuoct2021.html", "https://cert-portal.siemens.com/productcert/pdf/ssa-389290.pdf", "https://github.com/npm/node-tar/commit/1f036ca23f64a547bdd6c79c1a44bc62e8115da4", "https://github.com/npm/node-tar/security/advisories/GHSA-3jfq-g458-7qm9", "https://www.npmjs.com/advisories/1770", "https://www.npmjs.com/package/tar", "https://www.oracle.com/security-alerts/cpuoct2021.html"], "description": "The npm package \"tar\" (aka node-tar) before versions 6.1.1, 5.0.6, 4.4.14, and 3.3.2 has a arbitrary File Creation/Overwrite vulnerability due to insufficient absolute path sanitization. node-tar aims to prevent extraction of absolute file paths by turning absolute paths into relative paths when the `preservePaths` flag is not set to `true`. This is achieved by stripping the absolute path root from any absolute file paths contained in a tar file. For example `/home/<USER>/.bashrc` would turn into `home/user/.bashrc`. This logic was insufficient when file paths contained repeated path roots such as `////home/<USER>/.bashrc`. `node-tar` would only strip a single path root from such paths. When given an absolute file path with repeating path roots, the resulting path (e.g. `///home/<USER>/.bashrc`) would still resolve to an absolute path, thus allowing arbitrary file creation and overwrite. This issue was addressed in releases 3.2.2, 4.4.14, 5.0.6 and 6.1.1. Users may work around this vulnerability without upgrading by creating a custom `onentry` method which sanitizes the `entry.path` or a `filter` method which removes entries with absolute paths. See referenced GitHub Advisory for details. Be aware of CVE-2021-32803 which fixes a similar bug in later versions of tar.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:H/A:H", "metrics": {"baseScore": 8.1, "exploitabilityScore": 2.9, "impactScore": 5.2}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:M/Au:N/C:N/I:P/A:P", "metrics": {"baseScore": 5.8, "exploitabilityScore": 8.6, "impactScore": 5}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:N", "metrics": {"baseScore": 8.2, "exploitabilityScore": 1.9, "impactScore": 5.8}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-32804", "epss": 0.87249, "percentile": 0.9941, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "tar", "version": "4.4.8"}}, "found": {"vulnerabilityID": "GHSA-3jfq-g458-7qm9", "versionConstraint": ">=4.0.0,<4.4.14 (unknown)"}, "fix": {"suggestedVersion": "4.4.14"}}], "artifact": {"id": "6cfd37cbdc418b39", "name": "tar", "version": "4.4.8", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["ISC"], "cpes": ["cpe:2.3:a:tar_project:tar:4.4.8:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/tar@4.4.8", "upstreams": []}}, {"vulnerability": {"id": "GHSA-w457-6q6x-cgp9", "dataSource": "https://github.com/advisories/GHSA-w457-6q6x-cgp9", "namespace": "github:language:javascript", "severity": "Critical", "urls": [], "description": "Prototype Pollution in handlebars", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "metrics": {"baseScore": 9.8, "exploitabilityScore": 3.9, "impactScore": 5.9}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2019-19919", "epss": 0.24085, "percentile": 0.95839, "date": "2025-08-23"}], "fix": {"versions": ["4.3.0"], "state": "fixed"}, "advisories": [], "risk": 22.6399}, "relatedVulnerabilities": [{"id": "CVE-2019-19919", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2019-19919", "namespace": "nvd:cpe", "severity": "Critical", "urls": ["https://www.npmjs.com/advisories/1164", "https://www.tenable.com/security/tns-2021-14", "https://www.npmjs.com/advisories/1164", "https://www.tenable.com/security/tns-2021-14"], "description": "Versions of handlebars prior to 4.3.0 are vulnerable to Prototype Pollution leading to Remote Code Execution. Templates may alter an Object's __proto__ and __defineGetter__ properties, which may allow an attacker to execute arbitrary code through crafted payloads.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "metrics": {"baseScore": 9.8, "exploitabilityScore": 3.9, "impactScore": 5.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:P/I:P/A:P", "metrics": {"baseScore": 7.5, "exploitabilityScore": 10, "impactScore": 6.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2019-19919", "epss": 0.24085, "percentile": 0.95839, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "handlebars", "version": "4.1.2"}}, "found": {"vulnerabilityID": "GHSA-w457-6q6x-cgp9", "versionConstraint": ">=4.0.0,<4.3.0 (unknown)"}, "fix": {"suggestedVersion": "4.3.0"}}], "artifact": {"id": "d777ff7ccae5fee5", "name": "handlebars", "version": "4.1.2", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:handlebars.js_project:handlebars.js:4.1.2:*:*:*:*:node.js:*:*", "cpe:2.3:a:handlebarsjs:handlebars:4.1.2:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/handlebars@4.1.2", "upstreams": []}}, {"vulnerability": {"id": "GHSA-765h-qjxv-5f44", "dataSource": "https://github.com/advisories/GHSA-765h-qjxv-5f44", "namespace": "github:language:javascript", "severity": "Critical", "urls": [], "description": "Prototype Pollution in handlebars", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "metrics": {"baseScore": 9.8, "exploitabilityScore": 3.9, "impactScore": 5.9}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-23383", "epss": 0.05851, "percentile": 0.90196, "date": "2025-08-23"}], "fix": {"versions": ["4.7.7"], "state": "fixed"}, "advisories": [], "risk": 5.4999400000000005}, "relatedVulnerabilities": [{"id": "CVE-2021-23383", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2021-23383", "namespace": "nvd:cpe", "severity": "Critical", "urls": ["https://github.com/handlebars-lang/handlebars.js/commit/f0589701698268578199be25285b2ebea1c1e427", "https://security.netapp.com/advisory/ntap-20210618-0007/", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARS-1279031", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWER-1279032", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-1279030", "https://snyk.io/vuln/SNYK-JS-HANDLEBARS-1279029", "https://github.com/handlebars-lang/handlebars.js/commit/f0589701698268578199be25285b2ebea1c1e427", "https://security.netapp.com/advisory/ntap-20210618-0007/", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARS-1279031", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWER-1279032", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-1279030", "https://snyk.io/vuln/SNYK-JS-HANDLEBARS-1279029"], "description": "The package handlebars before 4.7.7 are vulnerable to Prototype Pollution when selecting certain compiling options to compile templates coming from an untrusted source.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "metrics": {"baseScore": 9.8, "exploitabilityScore": 3.9, "impactScore": 5.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:P/I:P/A:P", "metrics": {"baseScore": 7.5, "exploitabilityScore": 10, "impactScore": 6.5}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:L/A:L", "metrics": {"baseScore": 5.6, "exploitabilityScore": 2.3, "impactScore": 3.4}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-23383", "epss": 0.05851, "percentile": 0.90196, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "handlebars", "version": "4.1.2"}}, "found": {"vulnerabilityID": "GHSA-765h-qjxv-5f44", "versionConstraint": "<4.7.7 (unknown)"}, "fix": {"suggestedVersion": "4.7.7"}}], "artifact": {"id": "d777ff7ccae5fee5", "name": "handlebars", "version": "4.1.2", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:handlebars.js_project:handlebars.js:4.1.2:*:*:*:*:node.js:*:*", "cpe:2.3:a:handlebarsjs:handlebars:4.1.2:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/handlebars@4.1.2", "upstreams": []}}, {"vulnerability": {"id": "GHSA-72xf-g2v4-qvf3", "dataSource": "https://github.com/advisories/GHSA-72xf-g2v4-qvf3", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "tough-cookie Prototype Pollution vulnerability", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:N", "metrics": {"baseScore": 6.5, "exploitabilityScore": 3.9, "impactScore": 2.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2023-26136", "epss": 0.06872, "percentile": 0.90982, "date": "2025-08-23"}], "fix": {"versions": ["4.1.3"], "state": "fixed"}, "advisories": [], "risk": 3.9514}, "relatedVulnerabilities": [{"id": "CVE-2023-26136", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2023-26136", "namespace": "nvd:cpe", "severity": "Critical", "urls": ["https://github.com/salesforce/tough-cookie/commit/12d474791bb856004e858fdb1c47b7608d09cf6e", "https://github.com/salesforce/tough-cookie/issues/282", "https://github.com/salesforce/tough-cookie/releases/tag/v4.1.3", "https://lists.debian.org/debian-lts-announce/2023/07/msg00010.html", "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/3HUE6ZR5SL73KHL7XUPAOEL6SB7HUDT2/", "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/6PVVPNSAGSDS63HQ74PJ7MZ3MU5IYNVZ/", "https://security.netapp.com/advisory/ntap-20240621-0006/", "https://security.snyk.io/vuln/SNYK-JS-TOUGHCOOKIE-5672873", "https://github.com/salesforce/tough-cookie/commit/12d474791bb856004e858fdb1c47b7608d09cf6e", "https://github.com/salesforce/tough-cookie/issues/282", "https://github.com/salesforce/tough-cookie/releases/tag/v4.1.3", "https://lists.debian.org/debian-lts-announce/2023/07/msg00010.html", "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/3HUE6ZR5SL73KHL7XUPAOEL6SB7HUDT2/", "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/6PVVPNSAGSDS63HQ74PJ7MZ3MU5IYNVZ/", "https://security.netapp.com/advisory/ntap-20240621-0006/", "https://security.snyk.io/vuln/SNYK-JS-TOUGHCOOKIE-5672873"], "description": "Versions of the package tough-cookie before 4.1.3 are vulnerable to Prototype Pollution due to improper handling of Cookies when using CookieJar in rejectPublicSuffixes=false mode. This issue arises from the manner in which the objects are initialized.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "metrics": {"baseScore": 9.8, "exploitabilityScore": 3.9, "impactScore": 5.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:N", "metrics": {"baseScore": 6.5, "exploitabilityScore": 3.9, "impactScore": 2.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2023-26136", "epss": 0.06872, "percentile": 0.90982, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "tough-cookie", "version": "2.4.3"}}, "found": {"vulnerabilityID": "GHSA-72xf-g2v4-qvf3", "versionConstraint": "<4.1.3 (unknown)"}, "fix": {"suggestedVersion": "4.1.3"}}], "artifact": {"id": "d6c2b3af4b2f107d", "name": "tough-cookie", "version": "2.4.3", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["BSD-3-<PERSON><PERSON>"], "cpes": ["cpe:2.3:a:salesforce:tough-cookie:2.4.3:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/tough-cookie@2.4.3", "upstreams": []}}, {"vulnerability": {"id": "GHSA-f2jv-r9rf-7988", "dataSource": "https://github.com/advisories/GHSA-f2jv-r9rf-7988", "namespace": "github:language:javascript", "severity": "Critical", "urls": [], "description": "Remote code execution in handlebars when compiling templates", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "metrics": {"baseScore": 9.8, "exploitabilityScore": 3.9, "impactScore": 5.9}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-23369", "epss": 0.04041, "percentile": 0.88039, "date": "2025-08-23"}], "fix": {"versions": ["4.7.7"], "state": "fixed"}, "advisories": [], "risk": 3.79854}, "relatedVulnerabilities": [{"id": "CVE-2021-23369", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2021-23369", "namespace": "nvd:cpe", "severity": "Critical", "urls": ["https://github.com/handlebars-lang/handlebars.js/commit/b6d3de7123eebba603e321f04afdbae608e8fea8", "https://github.com/handlebars-lang/handlebars.js/commit/f0589701698268578199be25285b2ebea1c1e427", "https://security.netapp.com/advisory/ntap-20210604-0008/", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARS-1074950", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWER-1074951", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-1074952", "https://snyk.io/vuln/SNYK-JS-HANDLEBARS-1056767", "https://github.com/handlebars-lang/handlebars.js/commit/b6d3de7123eebba603e321f04afdbae608e8fea8", "https://github.com/handlebars-lang/handlebars.js/commit/f0589701698268578199be25285b2ebea1c1e427", "https://security.netapp.com/advisory/ntap-20210604-0008/", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARS-1074950", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWER-1074951", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-1074952", "https://snyk.io/vuln/SNYK-JS-HANDLEBARS-1056767"], "description": "The package handlebars before 4.7.7 are vulnerable to Remote Code Execution (RCE) when selecting certain compiling options to compile templates coming from an untrusted source.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "metrics": {"baseScore": 9.8, "exploitabilityScore": 3.9, "impactScore": 5.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:P/I:P/A:P", "metrics": {"baseScore": 7.5, "exploitabilityScore": 10, "impactScore": 6.5}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:L/A:L", "metrics": {"baseScore": 5.6, "exploitabilityScore": 2.3, "impactScore": 3.4}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-23369", "epss": 0.04041, "percentile": 0.88039, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "handlebars", "version": "4.1.2"}}, "found": {"vulnerabilityID": "GHSA-f2jv-r9rf-7988", "versionConstraint": "<4.7.7 (unknown)"}, "fix": {"suggestedVersion": "4.7.7"}}], "artifact": {"id": "d777ff7ccae5fee5", "name": "handlebars", "version": "4.1.2", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:handlebars.js_project:handlebars.js:4.1.2:*:*:*:*:node.js:*:*", "cpe:2.3:a:handlebarsjs:handlebars:4.1.2:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/handlebars@4.1.2", "upstreams": []}}, {"vulnerability": {"id": "GHSA-jf85-cpcp-j695", "dataSource": "https://github.com/advisories/GHSA-jf85-cpcp-j695", "namespace": "github:language:javascript", "severity": "Critical", "urls": [], "description": "Prototype Pollution in lodash", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:H", "metrics": {"baseScore": 9.1, "exploitabilityScore": 3.9, "impactScore": 5.2}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2019-10744", "epss": 0.0341, "percentile": 0.86957, "date": "2025-08-23"}], "fix": {"versions": ["4.17.12"], "state": "fixed"}, "advisories": [], "risk": 3.0860499999999997}, "relatedVulnerabilities": [{"id": "CVE-2019-10744", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2019-10744", "namespace": "nvd:cpe", "severity": "Critical", "urls": ["https://access.redhat.com/errata/RHSA-2019:3024", "https://security.netapp.com/advisory/ntap-20191004-0005/", "https://snyk.io/vuln/SNYK-JS-LODASH-450202", "https://support.f5.com/csp/article/K47105354?utm_source=f5support&amp%3Butm_medium=RSS", "https://www.oracle.com/security-alerts/cpujan2021.html", "https://www.oracle.com/security-alerts/cpuoct2020.html", "https://access.redhat.com/errata/RHSA-2019:3024", "https://security.netapp.com/advisory/ntap-20191004-0005/", "https://snyk.io/vuln/SNYK-JS-LODASH-450202", "https://support.f5.com/csp/article/K47105354?utm_source=f5support&amp%3Butm_medium=RSS", "https://www.oracle.com/security-alerts/cpujan2021.html", "https://www.oracle.com/security-alerts/cpuoct2020.html"], "description": "Versions of lodash lower than 4.17.12 are vulnerable to Prototype Pollution. The function defaultsDeep could be tricked into adding or modifying properties of Object.prototype using a constructor payload.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:H", "metrics": {"baseScore": 9.1, "exploitabilityScore": 3.9, "impactScore": 5.2}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:N/I:P/A:P", "metrics": {"baseScore": 6.4, "exploitabilityScore": 10, "impactScore": 5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2019-10744", "epss": 0.0341, "percentile": 0.86957, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "lodash", "version": "4.17.11"}}, "found": {"vulnerabilityID": "GHSA-jf85-cpcp-j695", "versionConstraint": "<4.17.12 (unknown)"}, "fix": {"suggestedVersion": "4.17.12"}}], "artifact": {"id": "b4550ad74bbf0b78", "name": "lodash", "version": "4.17.11", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:lodash:lodash:4.17.11:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/lodash@4.17.11", "upstreams": []}}, {"vulnerability": {"id": "GHSA-wc69-rhjr-hc9g", "dataSource": "https://github.com/advisories/GHSA-wc69-rhjr-hc9g", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Moment.js vulnerable to Inefficient Regular Expression Complexity", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-31129", "epss": 0.03437, "percentile": 0.87018, "date": "2025-08-23"}], "fix": {"versions": ["2.29.4"], "state": "fixed"}, "advisories": [], "risk": 2.57775}, "relatedVulnerabilities": [{"id": "CVE-2022-31129", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2022-31129", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/moment/moment/commit/9a3b5894f3d5d602948ac8a02e4ee528a49ca3a3", "https://github.com/moment/moment/pull/6015#issuecomment-1152961973", "https://github.com/moment/moment/security/advisories/GHSA-wc69-rhjr-hc9g", "https://huntr.dev/bounties/f0952b67-f2ff-44a9-a9cd-99e0a87cb633/", "https://lists.debian.org/debian-lts-announce/2023/01/msg00035.html", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/6QIO6YNLTK2T7SPKDS4JEL45FANLNC2Q/", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/IWY24RJA3SBJGA5N4CU4VBPHJPPPJL5O/", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/ORJX2LF6KMPIHP6B2P6KZIVKMLE3LVJ5/", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/ZMX5YHELQVCGKKQVFXIYOTBMN23YYSRO/", "https://security.netapp.com/advisory/ntap-20221014-0003/", "https://github.com/moment/moment/commit/9a3b5894f3d5d602948ac8a02e4ee528a49ca3a3", "https://github.com/moment/moment/pull/6015#issuecomment-1152961973", "https://github.com/moment/moment/security/advisories/GHSA-wc69-rhjr-hc9g", "https://huntr.dev/bounties/f0952b67-f2ff-44a9-a9cd-99e0a87cb633/", "https://lists.debian.org/debian-lts-announce/2023/01/msg00035.html", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/6QIO6YNLTK2T7SPKDS4JEL45FANLNC2Q/", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/IWY24RJA3SBJGA5N4CU4VBPHJPPPJL5O/", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/ORJX2LF6KMPIHP6B2P6KZIVKMLE3LVJ5/", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/ZMX5YHELQVCGKKQVFXIYOTBMN23YYSRO/", "https://security.netapp.com/advisory/ntap-20221014-0003/"], "description": "moment is a JavaScript date library for parsing, validating, manipulating, and formatting dates. Affected versions of moment were found to use an inefficient parsing algorithm. Specifically using string-to-date parsing in moment (more specifically rfc2822 parsing, which is tried by default) has quadratic (N^2) complexity on specific inputs. Users may notice a noticeable slowdown is observed with inputs above 10k characters. Users who pass user-provided strings without sanity length checks to moment constructor are vulnerable to (Re)DoS attacks. The problem is patched in 2.29.4, the patch can be applied to all affected versions with minimal tweaking. Users are advised to upgrade. Users unable to upgrade should consider limiting date lengths accepted from user input.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:N/I:N/A:P", "metrics": {"baseScore": 5, "exploitabilityScore": 10, "impactScore": 2.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-31129", "epss": 0.03437, "percentile": 0.87018, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "moment", "version": "2.24.0"}}, "found": {"vulnerabilityID": "GHSA-wc69-rhjr-hc9g", "versionConstraint": ">=2.18.0,<2.29.4 (unknown)"}, "fix": {"suggestedVersion": "2.29.4"}}], "artifact": {"id": "5120b2dcd543189a", "name": "moment", "version": "2.24.0", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:momentjs:moment:2.24.0:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/moment@2.24.0", "upstreams": []}}, {"vulnerability": {"id": "GHSA-hrpp-h998-j3pp", "dataSource": "https://github.com/advisories/GHSA-hrpp-h998-j3pp", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "qs vulnerable to Prototype Pollution", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-24999", "epss": 0.03424, "percentile": 0.86987, "date": "2025-08-23"}], "fix": {"versions": ["6.5.3"], "state": "fixed"}, "advisories": [], "risk": 2.568}, "relatedVulnerabilities": [{"id": "CVE-2022-24999", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2022-24999", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/expressjs/express/releases/tag/4.17.3", "https://github.com/ljharb/qs/pull/428", "https://github.com/n8tz/CVE-2022-24999", "https://lists.debian.org/debian-lts-announce/2023/01/msg00039.html", "https://security.netapp.com/advisory/ntap-20230908-0005/", "https://github.com/expressjs/express/releases/tag/4.17.3", "https://github.com/ljharb/qs/pull/428", "https://github.com/n8tz/CVE-2022-24999", "https://lists.debian.org/debian-lts-announce/2023/01/msg00039.html", "https://security.netapp.com/advisory/ntap-20230908-0005/"], "description": "qs before 6.10.3, as used in Express before 4.17.3 and other products, allows attackers to cause a Node process hang for an Express application because an __ proto__ key can be used. In many typical Express use cases, an unauthenticated remote attacker can place the attack payload in the query string of the URL that is used to visit the application, such as a[__proto__]=b&a[__proto__]&a[length]=100000000. The fix was backported to qs 6.9.7, 6.8.3, 6.7.3, 6.6.1, 6.5.3, 6.4.1, 6.3.3, and 6.2.4 (and therefore Express 4.17.3, which has \"deps: qs@6.9.7\" in its release description, is not vulnerable).", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}, {"source": "134c704f-9b21-4f2e-91b3-4a467353bcc0", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-24999", "epss": 0.03424, "percentile": 0.86987, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "qs", "version": "6.5.2"}}, "found": {"vulnerabilityID": "GHSA-hrpp-h998-j3pp", "versionConstraint": ">=6.5.0,<6.5.3 (unknown)"}, "fix": {"suggestedVersion": "6.5.3"}}], "artifact": {"id": "8958ec8f79254aa8", "name": "qs", "version": "6.5.2", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["BSD-3-<PERSON><PERSON>"], "cpes": ["cpe:2.3:a:qs_project:qs:6.5.2:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/qs@6.5.2", "upstreams": []}}, {"vulnerability": {"id": "GHSA-hrpp-h998-j3pp", "dataSource": "https://github.com/advisories/GHSA-hrpp-h998-j3pp", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "qs vulnerable to Prototype Pollution", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-24999", "epss": 0.03424, "percentile": 0.86987, "date": "2025-08-23"}], "fix": {"versions": ["6.7.3"], "state": "fixed"}, "advisories": [], "risk": 2.568}, "relatedVulnerabilities": [{"id": "CVE-2022-24999", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2022-24999", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/expressjs/express/releases/tag/4.17.3", "https://github.com/ljharb/qs/pull/428", "https://github.com/n8tz/CVE-2022-24999", "https://lists.debian.org/debian-lts-announce/2023/01/msg00039.html", "https://security.netapp.com/advisory/ntap-20230908-0005/", "https://github.com/expressjs/express/releases/tag/4.17.3", "https://github.com/ljharb/qs/pull/428", "https://github.com/n8tz/CVE-2022-24999", "https://lists.debian.org/debian-lts-announce/2023/01/msg00039.html", "https://security.netapp.com/advisory/ntap-20230908-0005/"], "description": "qs before 6.10.3, as used in Express before 4.17.3 and other products, allows attackers to cause a Node process hang for an Express application because an __ proto__ key can be used. In many typical Express use cases, an unauthenticated remote attacker can place the attack payload in the query string of the URL that is used to visit the application, such as a[__proto__]=b&a[__proto__]&a[length]=100000000. The fix was backported to qs 6.9.7, 6.8.3, 6.7.3, 6.6.1, 6.5.3, 6.4.1, 6.3.3, and 6.2.4 (and therefore Express 4.17.3, which has \"deps: qs@6.9.7\" in its release description, is not vulnerable).", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}, {"source": "134c704f-9b21-4f2e-91b3-4a467353bcc0", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-24999", "epss": 0.03424, "percentile": 0.86987, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "qs", "version": "6.7.0"}}, "found": {"vulnerabilityID": "GHSA-hrpp-h998-j3pp", "versionConstraint": ">=6.7.0,<6.7.3 (unknown)"}, "fix": {"suggestedVersion": "6.7.3"}}], "artifact": {"id": "8fd63519370ad946", "name": "qs", "version": "6.7.0", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["BSD-3-<PERSON><PERSON>"], "cpes": ["cpe:2.3:a:qs_project:qs:6.7.0:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/qs@6.7.0", "upstreams": []}}, {"vulnerability": {"id": "GHSA-r7qp-cfhv-p84w", "dataSource": "https://github.com/advisories/GHSA-r7qp-cfhv-p84w", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "Uncaught exception in engine.io", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 6.5, "exploitabilityScore": 2.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-41940", "epss": 0.03331, "percentile": 0.8677, "date": "2025-08-23"}], "fix": {"versions": ["3.6.1"], "state": "fixed"}, "advisories": [], "risk": 1.9153249999999997}, "relatedVulnerabilities": [{"id": "CVE-2022-41940", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2022-41940", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://github.com/socketio/engine.io/commit/425e833ab13373edf1dd5a0706f07100db14e3c6", "https://github.com/socketio/engine.io/commit/83c4071af871fc188298d7d591e95670bf9f9085", "https://github.com/socketio/engine.io/security/advisories/GHSA-r7qp-cfhv-p84w", "https://github.com/socketio/engine.io/commit/425e833ab13373edf1dd5a0706f07100db14e3c6", "https://github.com/socketio/engine.io/commit/83c4071af871fc188298d7d591e95670bf9f9085", "https://github.com/socketio/engine.io/security/advisories/GHSA-r7qp-cfhv-p84w"], "description": "Engine.IO is the implementation of transport-based cross-browser/cross-device bi-directional communication layer for Socket.IO. A specially crafted HTTP request can trigger an uncaught exception on the Engine.IO server, thus killing the Node.js process. This impacts all the users of the engine.io package, including those who uses depending packages like socket.io. There is no known workaround except upgrading to a safe version. There are patches for this issue released in versions 3.6.1 and 6.2.1.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 6.5, "exploitabilityScore": 2.9, "impactScore": 3.6}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:H/I:L/A:H", "metrics": {"baseScore": 7.1, "exploitabilityScore": 1.7, "impactScore": 5.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-41940", "epss": 0.03331, "percentile": 0.8677, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "engine.io", "version": "3.3.2"}}, "found": {"vulnerabilityID": "GHSA-r7qp-cfhv-p84w", "versionConstraint": "<3.6.1 (unknown)"}, "fix": {"suggestedVersion": "3.6.1"}}], "artifact": {"id": "0eb439edefac8ebb", "name": "engine.io", "version": "3.3.2", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:socket:engine.io:3.3.2:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/engine.io@3.3.2", "upstreams": []}}, {"vulnerability": {"id": "GHSA-wm7h-9275-46v2", "dataSource": "https://github.com/advisories/GHSA-wm7h-9275-46v2", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Crash in <PERSON><PERSON><PERSON><PERSON><PERSON> in dicer", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-24434", "epss": 0.02553, "percentile": 0.84914, "date": "2025-08-23"}], "fix": {"versions": [], "state": "not-fixed"}, "advisories": [], "risk": 1.9147500000000002}, "relatedVulnerabilities": [{"id": "CVE-2022-24434", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2022-24434", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/mscdex/busboy/issues/250", "https://github.com/mscdex/dicer/pull/22", "https://github.com/mscdex/dicer/pull/22/commits/b7fca2e93e8e9d4439d8acc5c02f5e54a0112dac", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-2838865", "https://snyk.io/vuln/SNYK-JS-DICER-2311764", "https://github.com/mscdex/busboy/issues/250", "https://github.com/mscdex/dicer/pull/22", "https://github.com/mscdex/dicer/pull/22/commits/b7fca2e93e8e9d4439d8acc5c02f5e54a0112dac", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-2838865", "https://snyk.io/vuln/SNYK-JS-DICER-2311764"], "description": "This affects all versions of package dicer. A malicious attacker can send a modified form to server, and crash the nodejs service. An attacker could sent the payload again and again so that the service continuously crashes.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:N/I:N/A:P", "metrics": {"baseScore": 5, "exploitabilityScore": 10, "impactScore": 2.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-24434", "epss": 0.02553, "percentile": 0.84914, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "dicer", "version": "0.2.5"}}, "found": {"vulnerabilityID": "GHSA-wm7h-9275-46v2", "versionConstraint": "<=0.3.1 (unknown)"}}], "artifact": {"id": "0c9b91804962bc0e", "name": "dicer", "version": "0.2.5", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:dicer_project:dicer:0.2.5:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/dicer@0.2.5", "upstreams": []}}, {"vulnerability": {"id": "GHSA-hxcc-f52p-wc94", "dataSource": "https://github.com/advisories/GHSA-hxcc-f52p-wc94", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Insecure serialization leading to RCE in serialize-javascript", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:H/A:H", "metrics": {"baseScore": 8.1, "exploitabilityScore": 2.3, "impactScore": 5.9}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2020-7660", "epss": 0.02351, "percentile": 0.8426, "date": "2025-08-23"}], "fix": {"versions": ["3.1.0"], "state": "fixed"}, "advisories": [], "risk": 1.8337800000000002}, "relatedVulnerabilities": [{"id": "CVE-2020-7660", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2020-7660", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/yahoo/serialize-javascript/commit/f21a6fb3ace2353413761e79717b2d210ba6ccbd", "https://github.com/yahoo/serialize-javascript/commit/f21a6fb3ace2353413761e79717b2d210ba6ccbd"], "description": "serialize-javascript prior to 3.1.0 allows remote attackers to inject arbitrary code via the function \"deleteFunctions\" within \"index.js\".", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:H/A:H", "metrics": {"baseScore": 8.1, "exploitabilityScore": 2.3, "impactScore": 5.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:M/Au:N/C:P/I:P/A:P", "metrics": {"baseScore": 6.8, "exploitabilityScore": 8.6, "impactScore": 6.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2020-7660", "epss": 0.02351, "percentile": 0.8426, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "serialize-javascript", "version": "1.7.0"}}, "found": {"vulnerabilityID": "GHSA-hxcc-f52p-wc94", "versionConstraint": "<3.1.0 (unknown)"}, "fix": {"suggestedVersion": "3.1.0"}}], "artifact": {"id": "c9ddd9c6e2384a83", "name": "serialize-javascript", "version": "1.7.0", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["BSD-3-<PERSON><PERSON>"], "cpes": ["cpe:2.3:a:verizon:serialize-javascript:1.7.0:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/serialize-javascript@1.7.0", "upstreams": []}}, {"vulnerability": {"id": "GHSA-p6mc-m468-83gw", "dataSource": "https://github.com/advisories/GHSA-p6mc-m468-83gw", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Prototype Pollution in lodash", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:N/I:H/A:H", "metrics": {"baseScore": 7.4, "exploitabilityScore": 2.3, "impactScore": 5.2}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2020-8203", "epss": 0.02439, "percentile": 0.84549, "date": "2025-08-23"}], "fix": {"versions": ["4.17.19"], "state": "fixed"}, "advisories": [], "risk": 1.8170549999999996}, "relatedVulnerabilities": [{"id": "CVE-2020-8203", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2020-8203", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/lodash/lodash/issues/4874", "https://hackerone.com/reports/712065", "https://security.netapp.com/advisory/ntap-20200724-0006/", "https://www.oracle.com//security-alerts/cpujul2021.html", "https://www.oracle.com/security-alerts/cpuApr2021.html", "https://www.oracle.com/security-alerts/cpuapr2022.html", "https://www.oracle.com/security-alerts/cpujan2022.html", "https://www.oracle.com/security-alerts/cpuoct2021.html", "https://github.com/lodash/lodash/issues/4874", "https://hackerone.com/reports/712065", "https://security.netapp.com/advisory/ntap-20200724-0006/", "https://www.oracle.com//security-alerts/cpujul2021.html", "https://www.oracle.com/security-alerts/cpuApr2021.html", "https://www.oracle.com/security-alerts/cpuapr2022.html", "https://www.oracle.com/security-alerts/cpujan2022.html", "https://www.oracle.com/security-alerts/cpuoct2021.html"], "description": "Prototype pollution attack when using _.zipObjectDeep in lodash before 4.17.20.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:N/I:H/A:H", "metrics": {"baseScore": 7.4, "exploitabilityScore": 2.3, "impactScore": 5.2}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:M/Au:N/C:N/I:P/A:P", "metrics": {"baseScore": 5.8, "exploitabilityScore": 8.6, "impactScore": 5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2020-8203", "epss": 0.02439, "percentile": 0.84549, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "lodash", "version": "4.17.11"}}, "found": {"vulnerabilityID": "GHSA-p6mc-m468-83gw", "versionConstraint": ">=3.7.0,<4.17.19 (unknown)"}, "fix": {"suggestedVersion": "4.17.19"}}], "artifact": {"id": "b4550ad74bbf0b78", "name": "lodash", "version": "4.17.11", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:lodash:lodash:4.17.11:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/lodash@4.17.11", "upstreams": []}}, {"vulnerability": {"id": "GHSA-92xj-mqp7-vmcj", "dataSource": "https://github.com/advisories/GHSA-92xj-mqp7-vmcj", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Prototype Pollution in node-forge", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H/E:P/RL:O/RC:C", "metrics": {"baseScore": 9.8, "exploitabilityScore": 3.9, "impactScore": 5.9}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2020-7720", "epss": 0.01606, "percentile": 0.80965, "date": "2025-08-23"}], "fix": {"versions": ["0.10.0"], "state": "fixed"}, "advisories": [], "risk": 1.3891900000000001}, "relatedVulnerabilities": [{"id": "CVE-2020-7720", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2020-7720", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/digitalbazaar/forge/blob/master/CHANGELOG.md", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-609293", "https://snyk.io/vuln/SNYK-JS-NODEFORGE-598677", "https://github.com/digitalbazaar/forge/blob/master/CHANGELOG.md", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-609293", "https://snyk.io/vuln/SNYK-JS-NODEFORGE-598677"], "description": "The package node-forge before 0.10.0 is vulnerable to Prototype Pollution via the util.setPath function. Note: Version 0.10.0 is a breaking change removing the vulnerable functions.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:L", "metrics": {"baseScore": 7.3, "exploitabilityScore": 3.9, "impactScore": 3.4}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:P/I:P/A:P", "metrics": {"baseScore": 7.5, "exploitabilityScore": 10, "impactScore": 6.5}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "metrics": {"baseScore": 9.8, "exploitabilityScore": 3.9, "impactScore": 5.9}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2020-7720", "epss": 0.01606, "percentile": 0.80965, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "node-forge", "version": "0.8.5"}}, "found": {"vulnerabilityID": "GHSA-92xj-mqp7-vmcj", "versionConstraint": "<0.10.0 (unknown)"}, "fix": {"suggestedVersion": "0.10.0"}}], "artifact": {"id": "087c9d217c8255bc", "name": "node-forge", "version": "0.8.5", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["(BSD-3-<PERSON><PERSON> OR GPL-2.0)"], "cpes": ["cpe:2.3:a:digitalbazaar:forge:0.8.5:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/node-forge@0.8.5", "upstreams": []}}, {"vulnerability": {"id": "GHSA-xvch-5gv4-984h", "dataSource": "https://github.com/advisories/GHSA-xvch-5gv4-984h", "namespace": "github:language:javascript", "severity": "Critical", "urls": [], "description": "Prototype Pollution in minimist", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "metrics": {"baseScore": 9.8, "exploitabilityScore": 3.9, "impactScore": 5.9}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-44906", "epss": 0.01134, "percentile": 0.7748, "date": "2025-08-23"}], "fix": {"versions": ["0.2.4"], "state": "fixed"}, "advisories": [], "risk": 1.06596}, "relatedVulnerabilities": [{"id": "CVE-2021-44906", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2021-44906", "namespace": "nvd:cpe", "severity": "Critical", "urls": ["https://github.com/Marynk/JavaScript-vulnerability-detection/blob/main/minimist%20PoC.zip", "https://github.com/substack/minimist/blob/master/index.js#L69", "https://github.com/substack/minimist/issues/164", "https://security.netapp.com/advisory/ntap-20240621-0006/", "https://snyk.io/vuln/SNYK-JS-MINIMIST-559764", "https://stackoverflow.com/questions/8588563/adding-custom-properties-to-a-function/20278068#20278068", "https://github.com/Marynk/JavaScript-vulnerability-detection/blob/main/minimist%20PoC.zip", "https://github.com/substack/minimist/blob/master/index.js#L69", "https://github.com/substack/minimist/issues/164", "https://security.netapp.com/advisory/ntap-20240621-0006/", "https://snyk.io/vuln/SNYK-JS-MINIMIST-559764", "https://stackoverflow.com/questions/8588563/adding-custom-properties-to-a-function/20278068#20278068"], "description": "Minimist <=1.2.5 is vulnerable to Prototype Pollution via file index.js, function setKey() (lines 69-95).", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "metrics": {"baseScore": 9.8, "exploitabilityScore": 3.9, "impactScore": 5.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:P/I:P/A:P", "metrics": {"baseScore": 7.5, "exploitabilityScore": 10, "impactScore": 6.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-44906", "epss": 0.01134, "percentile": 0.7748, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "minimist", "version": "0.0.10"}}, "found": {"vulnerabilityID": "GHSA-xvch-5gv4-984h", "versionConstraint": "<0.2.4 (unknown)"}, "fix": {"suggestedVersion": "0.2.4"}}], "artifact": {"id": "fec4f6e381234988", "name": "minimist", "version": "0.0.10", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:minimist:minimist:0.0.10:*:*:*:*:*:*:*"], "purl": "pkg:npm/minimist@0.0.10", "upstreams": []}}, {"vulnerability": {"id": "GHSA-xvch-5gv4-984h", "dataSource": "https://github.com/advisories/GHSA-xvch-5gv4-984h", "namespace": "github:language:javascript", "severity": "Critical", "urls": [], "description": "Prototype Pollution in minimist", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "metrics": {"baseScore": 9.8, "exploitabilityScore": 3.9, "impactScore": 5.9}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-44906", "epss": 0.01134, "percentile": 0.7748, "date": "2025-08-23"}], "fix": {"versions": ["1.2.6"], "state": "fixed"}, "advisories": [], "risk": 1.06596}, "relatedVulnerabilities": [{"id": "CVE-2021-44906", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2021-44906", "namespace": "nvd:cpe", "severity": "Critical", "urls": ["https://github.com/Marynk/JavaScript-vulnerability-detection/blob/main/minimist%20PoC.zip", "https://github.com/substack/minimist/blob/master/index.js#L69", "https://github.com/substack/minimist/issues/164", "https://security.netapp.com/advisory/ntap-20240621-0006/", "https://snyk.io/vuln/SNYK-JS-MINIMIST-559764", "https://stackoverflow.com/questions/8588563/adding-custom-properties-to-a-function/20278068#20278068", "https://github.com/Marynk/JavaScript-vulnerability-detection/blob/main/minimist%20PoC.zip", "https://github.com/substack/minimist/blob/master/index.js#L69", "https://github.com/substack/minimist/issues/164", "https://security.netapp.com/advisory/ntap-20240621-0006/", "https://snyk.io/vuln/SNYK-JS-MINIMIST-559764", "https://stackoverflow.com/questions/8588563/adding-custom-properties-to-a-function/20278068#20278068"], "description": "Minimist <=1.2.5 is vulnerable to Prototype Pollution via file index.js, function setKey() (lines 69-95).", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "metrics": {"baseScore": 9.8, "exploitabilityScore": 3.9, "impactScore": 5.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:P/I:P/A:P", "metrics": {"baseScore": 7.5, "exploitabilityScore": 10, "impactScore": 6.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-44906", "epss": 0.01134, "percentile": 0.7748, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "minimist", "version": "1.2.0"}}, "found": {"vulnerabilityID": "GHSA-xvch-5gv4-984h", "versionConstraint": ">=1.0.0,<1.2.6 (unknown)"}, "fix": {"suggestedVersion": "1.2.6"}}], "artifact": {"id": "21d1985e81603cdd", "name": "minimist", "version": "1.2.0", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:minimist:minimist:1.2.0:*:*:*:*:*:*:*"], "purl": "pkg:npm/minimist@1.2.0", "upstreams": []}}, {"vulnerability": {"id": "GHSA-xwcq-pm8m-c4vf", "dataSource": "https://github.com/advisories/GHSA-xwcq-pm8m-c4vf", "namespace": "github:language:javascript", "severity": "Critical", "urls": [], "description": "crypto-js PBKDF2 1,000 times weaker than specified in 1993 and 1.3M times weaker than current standard", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:N", "metrics": {"baseScore": 9.1, "exploitabilityScore": 3.9, "impactScore": 5.2}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2023-46233", "epss": 0.00963, "percentile": 0.75585, "date": "2025-08-23"}], "fix": {"versions": ["4.2.0"], "state": "fixed"}, "advisories": [], "risk": 0.8715149999999999}, "relatedVulnerabilities": [{"id": "CVE-2023-46233", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2023-46233", "namespace": "nvd:cpe", "severity": "Critical", "urls": ["https://github.com/brix/crypto-js/commit/421dd538b2d34e7c24a5b72cc64dc2b9167db40a", "https://github.com/brix/crypto-js/security/advisories/GHSA-xwcq-pm8m-c4vf", "https://lists.debian.org/debian-lts-announce/2023/11/msg00025.html", "https://github.com/brix/crypto-js/commit/421dd538b2d34e7c24a5b72cc64dc2b9167db40a", "https://github.com/brix/crypto-js/security/advisories/GHSA-xwcq-pm8m-c4vf", "https://lists.debian.org/debian-lts-announce/2023/11/msg00025.html"], "description": "crypto-js is a JavaScript library of crypto standards. Prior to version 4.2.0, crypto-js PBKDF2 is 1,000 times weaker than originally specified in 1993, and at least 1,300,000 times weaker than current industry standard. This is because it both defaults to SHA1, a cryptographic hash algorithm considered insecure since at least 2005, and defaults to one single iteration, a 'strength' or 'difficulty' value specified at 1,000 when specified in 1993. PBKDF2 relies on iteration count as a countermeasure to preimage and collision attacks. If used to protect passwords, the impact is high. If used to generate signatures, the impact is high. Version 4.2.0 contains a patch for this issue. As a workaround, configure crypto-js to use SHA256 with at least 250,000 iterations.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:N", "metrics": {"baseScore": 9.1, "exploitabilityScore": 3.9, "impactScore": 5.2}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:N", "metrics": {"baseScore": 9.1, "exploitabilityScore": 3.9, "impactScore": 5.2}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2023-46233", "epss": 0.00963, "percentile": 0.75585, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "crypto-js", "version": "3.1.9-1"}}, "found": {"vulnerabilityID": "GHSA-xwcq-pm8m-c4vf", "versionConstraint": "<4.2.0 (unknown)"}, "fix": {"suggestedVersion": "4.2.0"}}], "artifact": {"id": "5b2f56eb6ba8ff15", "name": "crypto-js", "version": "3.1.9-1", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:crypto-js:crypto-js:3.1.9-1:*:*:*:*:*:*:*", "cpe:2.3:a:crypto-js:crypto_js:3.1.9-1:*:*:*:*:*:*:*", "cpe:2.3:a:crypto_js:crypto-js:3.1.9-1:*:*:*:*:*:*:*", "cpe:2.3:a:crypto_js:crypto_js:3.1.9-1:*:*:*:*:*:*:*", "cpe:2.3:a:crypto:crypto-js:3.1.9-1:*:*:*:*:*:*:*", "cpe:2.3:a:crypto:crypto_js:3.1.9-1:*:*:*:*:*:*:*"], "purl": "pkg:npm/crypto-js@3.1.9-1", "upstreams": []}}, {"vulnerability": {"id": "GHSA-5955-9wpr-37jh", "dataSource": "https://github.com/advisories/GHSA-5955-9wpr-37jh", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Arbitrary File Creation/Overwrite on Windows via insufficient relative path sanitization", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:N", "metrics": {"baseScore": 8.2, "exploitabilityScore": 1.9, "impactScore": 5.8}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-37713", "epss": 0.00821, "percentile": 0.73474, "date": "2025-08-23"}], "fix": {"versions": ["4.4.18"], "state": "fixed"}, "advisories": [], "risk": 0.644485}, "relatedVulnerabilities": [{"id": "CVE-2021-37713", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2021-37713", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://cert-portal.siemens.com/productcert/pdf/ssa-389290.pdf", "https://github.com/npm/node-tar/security/advisories/GHSA-5955-9wpr-37jh", "https://www.npmjs.com/package/tar", "https://www.oracle.com/security-alerts/cpuoct2021.html", "https://cert-portal.siemens.com/productcert/pdf/ssa-389290.pdf", "https://github.com/npm/node-tar/security/advisories/GHSA-5955-9wpr-37jh", "https://www.npmjs.com/package/tar", "https://www.oracle.com/security-alerts/cpuoct2021.html"], "description": "The npm package \"tar\" (aka node-tar) before versions 4.4.18, 5.0.10, and 6.1.9 has an arbitrary file creation/overwrite and arbitrary code execution vulnerability. node-tar aims to guarantee that any file whose location would be outside of the extraction target directory is not extracted. This is, in part, accomplished by sanitizing absolute paths of entries within the archive, skipping archive entries that contain `..` path portions, and resolving the sanitized paths against the extraction target directory. This logic was insufficient on Windows systems when extracting tar files that contained a path that was not an absolute path, but specified a drive letter different from the extraction target, such as `C:some\\path`. If the drive letter does not match the extraction target, for example `D:\\extraction\\dir`, then the result of `path.resolve(extractionDirectory, entryPath)` would resolve against the current working directory on the `C:` drive, rather than the extraction target directory. Additionally, a `..` portion of the path could occur immediately after the drive letter, such as `C:../foo`, and was not properly sanitized by the logic that checked for `..` within the normalized and split portions of the path. This only affects users of `node-tar` on Windows systems. These issues were addressed in releases 4.4.18, 5.0.10 and 6.1.9. The v3 branch of node-tar has been deprecated and did not receive patches for these issues. If you are still using a v3 release we recommend you update to a more recent version of node-tar. There is no reasonable way to work around this issue without performing the same path normalization procedures that node-tar now does. Users are encouraged to upgrade to the latest patched versions of node-tar, rather than attempt to sanitize paths themselves.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:H", "metrics": {"baseScore": 8.6, "exploitabilityScore": 1.9, "impactScore": 6.1}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:L/AC:M/Au:N/C:P/I:P/A:P", "metrics": {"baseScore": 4.4, "exploitabilityScore": 3.4, "impactScore": 6.5}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:N", "metrics": {"baseScore": 8.2, "exploitabilityScore": 1.9, "impactScore": 5.8}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-37713", "epss": 0.00821, "percentile": 0.73474, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "tar", "version": "4.4.8"}}, "found": {"vulnerabilityID": "GHSA-5955-9wpr-37jh", "versionConstraint": "<4.4.18 (unknown)"}, "fix": {"suggestedVersion": "4.4.18"}}], "artifact": {"id": "6cfd37cbdc418b39", "name": "tar", "version": "4.4.8", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["ISC"], "cpes": ["cpe:2.3:a:tar_project:tar:4.4.8:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/tar@4.4.8", "upstreams": []}}, {"vulnerability": {"id": "GHSA-j4f2-536g-r55m", "dataSource": "https://github.com/advisories/GHSA-j4f2-536g-r55m", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Resource exhaustion in engine.io", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2020-36048", "epss": 0.00797, "percentile": 0.73041, "date": "2025-08-23"}], "fix": {"versions": ["3.6.0"], "state": "fixed"}, "advisories": [], "risk": 0.59775}, "relatedVulnerabilities": [{"id": "CVE-2020-36048", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2020-36048", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://blog.caller.xyz/socketio-engineio-dos/", "https://github.com/bcaller/kill-engine-io", "https://github.com/socketio/engine.io/commit/734f9d1268840722c41219e69eb58318e0b2ac6b", "https://blog.caller.xyz/socketio-engineio-dos/", "https://github.com/bcaller/kill-engine-io", "https://github.com/socketio/engine.io/commit/734f9d1268840722c41219e69eb58318e0b2ac6b"], "description": "Engine.IO before 4.0.0 allows attackers to cause a denial of service (resource consumption) via a POST request to the long polling transport.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:N/I:N/A:P", "metrics": {"baseScore": 5, "exploitabilityScore": 10, "impactScore": 2.9}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2020-36048", "epss": 0.00797, "percentile": 0.73041, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "engine.io", "version": "3.3.2"}}, "found": {"vulnerabilityID": "GHSA-j4f2-536g-r55m", "versionConstraint": "<3.6.0 (unknown)"}, "fix": {"suggestedVersion": "3.6.0"}}], "artifact": {"id": "0eb439edefac8ebb", "name": "engine.io", "version": "3.3.2", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:socket:engine.io:3.3.2:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/engine.io@3.3.2", "upstreams": []}}, {"vulnerability": {"id": "GHSA-5v2h-r2cx-5xgj", "dataSource": "https://github.com/advisories/GHSA-5v2h-r2cx-5xgj", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Inefficient Regular Expression Complexity in marked", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-21681", "epss": 0.00695, "percentile": 0.70972, "date": "2025-08-23"}], "fix": {"versions": ["4.0.10"], "state": "fixed"}, "advisories": [], "risk": 0.52125}, "relatedVulnerabilities": [{"id": "CVE-2022-21681", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2022-21681", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/markedjs/marked/commit/8f806573a3f6c6b7a39b8cdb66ab5ebb8d55a5f5", "https://github.com/markedjs/marked/security/advisories/GHSA-5v2h-r2cx-5xgj", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/AIXDMC3CSHYW3YWVSQOXAWLUYQHAO5UX/", "https://github.com/markedjs/marked/commit/8f806573a3f6c6b7a39b8cdb66ab5ebb8d55a5f5", "https://github.com/markedjs/marked/security/advisories/GHSA-5v2h-r2cx-5xgj", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/AIXDMC3CSHYW3YWVSQOXAWLUYQHAO5UX/"], "description": "Marked is a markdown parser and compiler. Prior to version 4.0.10, the regular expression `inline.reflinkSearch` may cause catastrophic backtracking against some strings and lead to a denial of service (DoS). Anyone who runs untrusted markdown through a vulnerable version of marked and does not use a worker with a time limit may be affected. This issue is patched in version 4.0.10. As a workaround, avoid running untrusted markdown through marked or run marked on a worker thread and set a reasonable time limit to prevent draining resources.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:N/I:N/A:P", "metrics": {"baseScore": 5, "exploitabilityScore": 10, "impactScore": 2.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-21681", "epss": 0.00695, "percentile": 0.70972, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "marked", "version": "0.6.2"}}, "found": {"vulnerabilityID": "GHSA-5v2h-r2cx-5xgj", "versionConstraint": "<4.0.10 (unknown)"}, "fix": {"suggestedVersion": "4.0.10"}}], "artifact": {"id": "f9d2a89384ac3c29", "name": "marked", "version": "0.6.2", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:marked_project:marked:0.6.2:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/marked@0.6.2", "upstreams": []}}, {"vulnerability": {"id": "GHSA-8hfj-j24r-96c4", "dataSource": "https://github.com/advisories/GHSA-8hfj-j24r-96c4", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Path Traversal: 'dir/../../filename' in moment.locale", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-24785", "epss": 0.00682, "percentile": 0.707, "date": "2025-08-23"}], "fix": {"versions": ["2.29.2"], "state": "fixed"}, "advisories": [], "risk": 0.5115}, "relatedVulnerabilities": [{"id": "CVE-2022-24785", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2022-24785", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/moment/moment/commit/4211bfc8f15746be4019bba557e29a7ba83d54c5", "https://github.com/moment/moment/security/advisories/GHSA-8hfj-j24r-96c4", "https://lists.debian.org/debian-lts-announce/2023/01/msg00035.html", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/6QIO6YNLTK2T7SPKDS4JEL45FANLNC2Q/", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/ORJX2LF6KMPIHP6B2P6KZIVKMLE3LVJ5/", "https://security.netapp.com/advisory/ntap-20220513-0006/", "https://www.tenable.com/security/tns-2022-09", "https://github.com/moment/moment/commit/4211bfc8f15746be4019bba557e29a7ba83d54c5", "https://github.com/moment/moment/security/advisories/GHSA-8hfj-j24r-96c4", "https://lists.debian.org/debian-lts-announce/2023/01/msg00035.html", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/6QIO6YNLTK2T7SPKDS4JEL45FANLNC2Q/", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/ORJX2LF6KMPIHP6B2P6KZIVKMLE3LVJ5/", "https://security.netapp.com/advisory/ntap-20220513-0006/", "https://www.tenable.com/security/tns-2022-09"], "description": "Moment.js is a JavaScript date library for parsing, validating, manipulating, and formatting dates. A path traversal vulnerability impacts npm (server) users of Moment.js between versions 1.0.1 and 2.29.1, especially if a user-provided locale string is directly used to switch moment locale. This problem is patched in 2.29.2, and the patch can be applied to all affected versions. As a workaround, sanitize the user-provided locale name before passing it to Moment.js.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:N/I:P/A:N", "metrics": {"baseScore": 5, "exploitabilityScore": 10, "impactScore": 2.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-24785", "epss": 0.00682, "percentile": 0.707, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "moment", "version": "2.24.0"}}, "found": {"vulnerabilityID": "GHSA-8hfj-j24r-96c4", "versionConstraint": "<2.29.2 (unknown)"}, "fix": {"suggestedVersion": "2.29.2"}}], "artifact": {"id": "5120b2dcd543189a", "name": "moment", "version": "2.24.0", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:momentjs:moment:2.24.0:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/moment@2.24.0", "upstreams": []}}, {"vulnerability": {"id": "GHSA-3h5v-q93c-6h6q", "dataSource": "https://github.com/advisories/GHSA-3h5v-q93c-6h6q", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "ws affected by a DoS when handling a request with many HTTP headers", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-37890", "epss": 0.00541, "percentile": 0.6663, "date": "2025-08-23"}], "fix": {"versions": ["6.2.3"], "state": "fixed"}, "advisories": [], "risk": 0.40575000000000006}, "relatedVulnerabilities": [{"id": "CVE-2024-37890", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2024-37890", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/websockets/ws/commit/22c28763234aa75a7e1b76f5c01c181260d7917f", "https://github.com/websockets/ws/commit/4abd8f6de4b0b65ef80b3ff081989479ed93377e", "https://github.com/websockets/ws/commit/e55e5106f10fcbaac37cfa89759e4cc0d073a52c", "https://github.com/websockets/ws/commit/eeb76d313e2a00dd5247ca3597bba7877d064a63", "https://github.com/websockets/ws/issues/2230", "https://github.com/websockets/ws/pull/2231", "https://github.com/websockets/ws/security/advisories/GHSA-3h5v-q93c-6h6q", "https://nodejs.org/api/http.html#servermaxheaderscount", "https://github.com/websockets/ws/commit/22c28763234aa75a7e1b76f5c01c181260d7917f", "https://github.com/websockets/ws/commit/4abd8f6de4b0b65ef80b3ff081989479ed93377e", "https://github.com/websockets/ws/commit/e55e5106f10fcbaac37cfa89759e4cc0d073a52c", "https://github.com/websockets/ws/commit/eeb76d313e2a00dd5247ca3597bba7877d064a63", "https://github.com/websockets/ws/issues/2230", "https://github.com/websockets/ws/pull/2231", "https://github.com/websockets/ws/security/advisories/GHSA-3h5v-q93c-6h6q", "https://nodejs.org/api/http.html#servermaxheaderscount"], "description": "ws is an open source WebSocket client and server for Node.js. A request with a number of headers exceeding theserver.maxHeadersCount threshold could be used to crash a ws server. The vulnerability was fixed in ws@8.17.1 (e55e510) and backported to ws@7.5.10 (22c2876), ws@6.2.3 (eeb76d3), and ws@5.2.4 (4abd8f6). In vulnerable versions of ws, the issue can be mitigated in the following ways: 1. Reduce the maximum allowed length of the request headers using the --max-http-header-size=size and/or the maxHeaderSize options so that no more headers than the server.maxHeadersCount limit can be sent. 2. Set server.maxHeadersCount to 0 so that no limit is applied.", "cvss": [{"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-37890", "epss": 0.00541, "percentile": 0.6663, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "ws", "version": "6.1.4"}}, "found": {"vulnerabilityID": "GHSA-3h5v-q93c-6h6q", "versionConstraint": ">=6.0.0,<6.2.3 (unknown)"}, "fix": {"suggestedVersion": "6.2.3"}}], "artifact": {"id": "4a9dff42fec13fca", "name": "ws", "version": "6.1.4", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:ws_project:ws:6.1.4:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/ws@6.1.4", "upstreams": []}}, {"vulnerability": {"id": "GHSA-3h5v-q93c-6h6q", "dataSource": "https://github.com/advisories/GHSA-3h5v-q93c-6h6q", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "ws affected by a DoS when handling a request with many HTTP headers", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-37890", "epss": 0.00541, "percentile": 0.6663, "date": "2025-08-23"}], "fix": {"versions": ["6.2.3"], "state": "fixed"}, "advisories": [], "risk": 0.40575000000000006}, "relatedVulnerabilities": [{"id": "CVE-2024-37890", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2024-37890", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/websockets/ws/commit/22c28763234aa75a7e1b76f5c01c181260d7917f", "https://github.com/websockets/ws/commit/4abd8f6de4b0b65ef80b3ff081989479ed93377e", "https://github.com/websockets/ws/commit/e55e5106f10fcbaac37cfa89759e4cc0d073a52c", "https://github.com/websockets/ws/commit/eeb76d313e2a00dd5247ca3597bba7877d064a63", "https://github.com/websockets/ws/issues/2230", "https://github.com/websockets/ws/pull/2231", "https://github.com/websockets/ws/security/advisories/GHSA-3h5v-q93c-6h6q", "https://nodejs.org/api/http.html#servermaxheaderscount", "https://github.com/websockets/ws/commit/22c28763234aa75a7e1b76f5c01c181260d7917f", "https://github.com/websockets/ws/commit/4abd8f6de4b0b65ef80b3ff081989479ed93377e", "https://github.com/websockets/ws/commit/e55e5106f10fcbaac37cfa89759e4cc0d073a52c", "https://github.com/websockets/ws/commit/eeb76d313e2a00dd5247ca3597bba7877d064a63", "https://github.com/websockets/ws/issues/2230", "https://github.com/websockets/ws/pull/2231", "https://github.com/websockets/ws/security/advisories/GHSA-3h5v-q93c-6h6q", "https://nodejs.org/api/http.html#servermaxheaderscount"], "description": "ws is an open source WebSocket client and server for Node.js. A request with a number of headers exceeding theserver.maxHeadersCount threshold could be used to crash a ws server. The vulnerability was fixed in ws@8.17.1 (e55e510) and backported to ws@7.5.10 (22c2876), ws@6.2.3 (eeb76d3), and ws@5.2.4 (4abd8f6). In vulnerable versions of ws, the issue can be mitigated in the following ways: 1. Reduce the maximum allowed length of the request headers using the --max-http-header-size=size and/or the maxHeaderSize options so that no more headers than the server.maxHeadersCount limit can be sent. 2. Set server.maxHeadersCount to 0 so that no limit is applied.", "cvss": [{"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-37890", "epss": 0.00541, "percentile": 0.6663, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "ws", "version": "6.2.1"}}, "found": {"vulnerabilityID": "GHSA-3h5v-q93c-6h6q", "versionConstraint": ">=6.0.0,<6.2.3 (unknown)"}, "fix": {"suggestedVersion": "6.2.3"}}], "artifact": {"id": "c5a6464aa9cd2ced", "name": "ws", "version": "6.2.1", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:ws_project:ws:6.2.1:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/ws@6.2.1", "upstreams": []}}, {"vulnerability": {"id": "GHSA-35jh-r3h4-6jhm", "dataSource": "https://github.com/advisories/GHSA-35jh-r3h4-6jhm", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Command Injection in lodash", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:U/C:H/I:H/A:H", "metrics": {"baseScore": 7.2, "exploitabilityScore": 1.3, "impactScore": 5.9}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-23337", "epss": 0.00551, "percentile": 0.66975, "date": "2025-08-23"}], "fix": {"versions": ["4.17.21"], "state": "fixed"}, "advisories": [], "risk": 0.404985}, "relatedVulnerabilities": [{"id": "CVE-2021-23337", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2021-23337", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://cert-portal.siemens.com/productcert/pdf/ssa-637483.pdf", "https://github.com/lodash/lodash/blob/ddfd9b11a0126db2302cb70ec9973b66baec0975/lodash.js%23L14851", "https://security.netapp.com/advisory/ntap-20210312-0006/", "https://snyk.io/vuln/SNYK-JAVA-ORGFUJIONWEBJARS-1074932", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARS-1074930", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWER-1074928", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWERGITHUBLODASH-1074931", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-1074929", "https://snyk.io/vuln/SNYK-JS-LODASH-1040724", "https://www.oracle.com//security-alerts/cpujul2021.html", "https://www.oracle.com/security-alerts/cpujan2022.html", "https://www.oracle.com/security-alerts/cpujul2022.html", "https://www.oracle.com/security-alerts/cpuoct2021.html", "https://cert-portal.siemens.com/productcert/pdf/ssa-637483.pdf", "https://github.com/lodash/lodash/blob/ddfd9b11a0126db2302cb70ec9973b66baec0975/lodash.js%23L14851", "https://security.netapp.com/advisory/ntap-20210312-0006/", "https://snyk.io/vuln/SNYK-JAVA-ORGFUJIONWEBJARS-1074932", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARS-1074930", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWER-1074928", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWERGITHUBLODASH-1074931", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-1074929", "https://snyk.io/vuln/SNYK-JS-LODASH-1040724", "https://www.oracle.com//security-alerts/cpujul2021.html", "https://www.oracle.com/security-alerts/cpujan2022.html", "https://www.oracle.com/security-alerts/cpujul2022.html", "https://www.oracle.com/security-alerts/cpuoct2021.html"], "description": "Lodash versions prior to 4.17.21 are vulnerable to Command Injection via the template function.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:U/C:H/I:H/A:H", "metrics": {"baseScore": 7.2, "exploitabilityScore": 1.3, "impactScore": 5.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:S/C:P/I:P/A:P", "metrics": {"baseScore": 6.5, "exploitabilityScore": 8, "impactScore": 6.5}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:U/C:H/I:H/A:H", "metrics": {"baseScore": 7.2, "exploitabilityScore": 1.3, "impactScore": 5.9}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-23337", "epss": 0.00551, "percentile": 0.66975, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "lodash", "version": "4.17.11"}}, "found": {"vulnerabilityID": "GHSA-35jh-r3h4-6jhm", "versionConstraint": "<4.17.21 (unknown)"}, "fix": {"suggestedVersion": "4.17.21"}}], "artifact": {"id": "b4550ad74bbf0b78", "name": "lodash", "version": "4.17.11", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:lodash:lodash:4.17.11:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/lodash@4.17.11", "upstreams": []}}, {"vulnerability": {"id": "GHSA-qwcr-r2fm-qrc7", "dataSource": "https://github.com/advisories/GHSA-qwcr-r2fm-qrc7", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "body-parser vulnerable to denial of service when url encoding is enabled", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-45590", "epss": 0.00504, "percentile": 0.65131, "date": "2025-08-23"}], "fix": {"versions": ["1.20.3"], "state": "fixed"}, "advisories": [], "risk": 0.37800000000000006}, "relatedVulnerabilities": [{"id": "CVE-2024-45590", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2024-45590", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/expressjs/body-parser/commit/b2695c4450f06ba3b0ccf48d872a229bb41c9bce", "https://github.com/expressjs/body-parser/security/advisories/GHSA-qwcr-r2fm-qrc7"], "description": "body-parser is Node.js body parsing middleware. body-parser <1.20.3 is vulnerable to denial of service when url encoding is enabled. A malicious actor using a specially crafted payload could flood the server with a large number of requests, resulting in denial of service. This issue is patched in 1.20.3.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-45590", "epss": 0.00504, "percentile": 0.65131, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "body-parser", "version": "1.18.3"}}, "found": {"vulnerabilityID": "GHSA-qwcr-r2fm-qrc7", "versionConstraint": "<1.20.3 (unknown)"}, "fix": {"suggestedVersion": "1.20.3"}}], "artifact": {"id": "74770b9aa6fd832f", "name": "body-parser", "version": "1.18.3", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:openjsf:body-parser:1.18.3:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/body-parser@1.18.3", "upstreams": []}}, {"vulnerability": {"id": "GHSA-qwcr-r2fm-qrc7", "dataSource": "https://github.com/advisories/GHSA-qwcr-r2fm-qrc7", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "body-parser vulnerable to denial of service when url encoding is enabled", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-45590", "epss": 0.00504, "percentile": 0.65131, "date": "2025-08-23"}], "fix": {"versions": ["1.20.3"], "state": "fixed"}, "advisories": [], "risk": 0.37800000000000006}, "relatedVulnerabilities": [{"id": "CVE-2024-45590", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2024-45590", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/expressjs/body-parser/commit/b2695c4450f06ba3b0ccf48d872a229bb41c9bce", "https://github.com/expressjs/body-parser/security/advisories/GHSA-qwcr-r2fm-qrc7"], "description": "body-parser is Node.js body parsing middleware. body-parser <1.20.3 is vulnerable to denial of service when url encoding is enabled. A malicious actor using a specially crafted payload could flood the server with a large number of requests, resulting in denial of service. This issue is patched in 1.20.3.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-45590", "epss": 0.00504, "percentile": 0.65131, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "body-parser", "version": "1.19.0"}}, "found": {"vulnerabilityID": "GHSA-qwcr-r2fm-qrc7", "versionConstraint": "<1.20.3 (unknown)"}, "fix": {"suggestedVersion": "1.20.3"}}], "artifact": {"id": "78ed052adbb5430f", "name": "body-parser", "version": "1.19.0", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:openjsf:body-parser:1.19.0:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/body-parser@1.19.0", "upstreams": []}}, {"vulnerability": {"id": "GHSA-rrrm-qjm4-v8hf", "dataSource": "https://github.com/advisories/GHSA-rrrm-qjm4-v8hf", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Inefficient Regular Expression Complexity in marked", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-21680", "epss": 0.0049, "percentile": 0.64536, "date": "2025-08-23"}], "fix": {"versions": ["4.0.10"], "state": "fixed"}, "advisories": [], "risk": 0.3675}, "relatedVulnerabilities": [{"id": "CVE-2022-21680", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2022-21680", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/markedjs/marked/commit/c4a3ccd344b6929afa8a1d50ac54a721e57012c0", "https://github.com/markedjs/marked/releases/tag/v4.0.10", "https://github.com/markedjs/marked/security/advisories/GHSA-rrrm-qjm4-v8hf", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/AIXDMC3CSHYW3YWVSQOXAWLUYQHAO5UX/", "https://github.com/markedjs/marked/commit/c4a3ccd344b6929afa8a1d50ac54a721e57012c0", "https://github.com/markedjs/marked/releases/tag/v4.0.10", "https://github.com/markedjs/marked/security/advisories/GHSA-rrrm-qjm4-v8hf", "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/AIXDMC3CSHYW3YWVSQOXAWLUYQHAO5UX/"], "description": "Marked is a markdown parser and compiler. Prior to version 4.0.10, the regular expression `block.def` may cause catastrophic backtracking against some strings and lead to a regular expression denial of service (ReDoS). Anyone who runs untrusted markdown through a vulnerable version of marked and does not use a worker with a time limit may be affected. This issue is patched in version 4.0.10. As a workaround, avoid running untrusted markdown through marked or run marked on a worker thread and set a reasonable time limit to prevent draining resources.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:N/I:N/A:P", "metrics": {"baseScore": 5, "exploitabilityScore": 10, "impactScore": 2.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-21680", "epss": 0.0049, "percentile": 0.64536, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "marked", "version": "0.6.2"}}, "found": {"vulnerabilityID": "GHSA-rrrm-qjm4-v8hf", "versionConstraint": "<4.0.10 (unknown)"}, "fix": {"suggestedVersion": "4.0.10"}}], "artifact": {"id": "f9d2a89384ac3c29", "name": "marked", "version": "0.6.2", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:marked_project:marked:0.6.2:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/marked@0.6.2", "upstreams": []}}, {"vulnerability": {"id": "GHSA-p8p7-x288-28g6", "dataSource": "https://github.com/advisories/GHSA-p8p7-x288-28g6", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "Server-Side Request Forgery in Request", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N", "metrics": {"baseScore": 6.1, "exploitabilityScore": 2.9, "impactScore": 2.8}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2023-28155", "epss": 0.00605, "percentile": 0.68649, "date": "2025-08-23"}], "fix": {"versions": [], "state": "not-fixed"}, "advisories": [], "risk": 0.33577499999999993}, "relatedVulnerabilities": [{"id": "CVE-2023-28155", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2023-28155", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://doyensec.com/resources/Doyensec_Advisory_RequestSSRF_Q12023.pdf", "https://github.com/request/request/issues/3442", "https://github.com/request/request/pull/3444", "https://security.netapp.com/advisory/ntap-20230413-0007/", "https://doyensec.com/resources/Doyensec_Advisory_RequestSSRF_Q12023.pdf", "https://github.com/request/request/issues/3442", "https://github.com/request/request/pull/3444", "https://security.netapp.com/advisory/ntap-20230413-0007/"], "description": "The Request package through 2.88.1 for Node.js allows a bypass of SSRF mitigations via an attacker-controller server that does a cross-protocol redirect (HTTP to HTTPS, or HTTPS to HTTP). NOTE: This vulnerability only affects products that are no longer supported by the maintainer.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N", "metrics": {"baseScore": 6.1, "exploitabilityScore": 2.9, "impactScore": 2.8}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2023-28155", "epss": 0.00605, "percentile": 0.68649, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "request", "version": "2.88.0"}}, "found": {"vulnerabilityID": "GHSA-p8p7-x288-28g6", "versionConstraint": "<=2.88.2 (unknown)"}}], "artifact": {"id": "c06c84b0600e3fa4", "name": "request", "version": "2.88.0", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["Apache-2.0"], "cpes": ["cpe:2.3:a:request_project:request:2.88.0:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/request@2.88.0", "upstreams": []}}, {"vulnerability": {"id": "GHSA-8fr3-hfg3-gpgp", "dataSource": "https://github.com/advisories/GHSA-8fr3-hfg3-gpgp", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "Open Redirect in node-forge", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N", "metrics": {"baseScore": 6.1, "exploitabilityScore": 2.9, "impactScore": 2.8}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-0122", "epss": 0.00546, "percentile": 0.6684, "date": "2025-08-23"}], "fix": {"versions": ["1.0.0"], "state": "fixed"}, "advisories": [], "risk": 0.3030299999999999}, "relatedVulnerabilities": [{"id": "CVE-2022-0122", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2022-0122", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://github.com/digitalbazaar/forge/commit/db8016c805371e72b06d8e2edfe0ace0df934a5e", "https://huntr.dev/bounties/41852c50-3c6d-4703-8c55-4db27164a4ae", "https://github.com/digitalbazaar/forge/commit/db8016c805371e72b06d8e2edfe0ace0df934a5e", "https://huntr.dev/bounties/41852c50-3c6d-4703-8c55-4db27164a4ae"], "description": "forge is vulnerable to URL Redirection to Untrusted Site", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N", "metrics": {"baseScore": 6.1, "exploitabilityScore": 2.9, "impactScore": 2.8}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:M/Au:N/C:P/I:P/A:N", "metrics": {"baseScore": 5.8, "exploitabilityScore": 8.6, "impactScore": 5}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.0", "vector": "CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-0122", "epss": 0.00546, "percentile": 0.6684, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "node-forge", "version": "0.8.5"}}, "found": {"vulnerabilityID": "GHSA-8fr3-hfg3-gpgp", "versionConstraint": "<1.0.0 (unknown)"}, "fix": {"suggestedVersion": "1.0.0"}}], "artifact": {"id": "087c9d217c8255bc", "name": "node-forge", "version": "0.8.5", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["(BSD-3-<PERSON><PERSON> OR GPL-2.0)"], "cpes": ["cpe:2.3:a:digitalbazaar:forge:0.8.5:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/node-forge@0.8.5", "upstreams": []}}, {"vulnerability": {"id": "GHSA-3w3w-pxmm-2w2j", "dataSource": "https://github.com/advisories/GHSA-3w3w-pxmm-2w2j", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "crypto-js uses insecure random numbers", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2020-36732", "epss": 0.00577, "percentile": 0.67833, "date": "2025-08-23"}], "fix": {"versions": ["3.2.1"], "state": "fixed"}, "advisories": [], "risk": 0.297155}, "relatedVulnerabilities": [{"id": "CVE-2020-36732", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2020-36732", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://github.com/brix/crypto-js/compare/3.2.0...3.2.1", "https://github.com/brix/crypto-js/issues/254", "https://github.com/brix/crypto-js/issues/256", "https://github.com/brix/crypto-js/pull/257/commits/e4ac157d8b75b962d6538fc0b996e5d4d5a9466b", "https://security.netapp.com/advisory/ntap-20230706-0003/", "https://security.snyk.io/vuln/SNYK-JS-CRYPTOJS-548472", "https://github.com/brix/crypto-js/compare/3.2.0...3.2.1", "https://github.com/brix/crypto-js/issues/254", "https://github.com/brix/crypto-js/issues/256", "https://github.com/brix/crypto-js/pull/257/commits/e4ac157d8b75b962d6538fc0b996e5d4d5a9466b", "https://security.netapp.com/advisory/ntap-20230706-0003/", "https://security.snyk.io/vuln/SNYK-JS-CRYPTOJS-548472"], "description": "The crypto-js package before 3.2.1 for Node.js generates random numbers by concatenating the string \"0.\" with an integer, which makes the output more predictable than necessary.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}, {"source": "134c704f-9b21-4f2e-91b3-4a467353bcc0", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2020-36732", "epss": 0.00577, "percentile": 0.67833, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "crypto-js", "version": "3.1.9-1"}}, "found": {"vulnerabilityID": "GHSA-3w3w-pxmm-2w2j", "versionConstraint": "<3.2.1 (unknown)"}, "fix": {"suggestedVersion": "3.2.1"}}], "artifact": {"id": "5b2f56eb6ba8ff15", "name": "crypto-js", "version": "3.1.9-1", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:crypto-js:crypto-js:3.1.9-1:*:*:*:*:*:*:*", "cpe:2.3:a:crypto-js:crypto_js:3.1.9-1:*:*:*:*:*:*:*", "cpe:2.3:a:crypto_js:crypto-js:3.1.9-1:*:*:*:*:*:*:*", "cpe:2.3:a:crypto_js:crypto_js:3.1.9-1:*:*:*:*:*:*:*", "cpe:2.3:a:crypto:crypto-js:3.1.9-1:*:*:*:*:*:*:*", "cpe:2.3:a:crypto:crypto_js:3.1.9-1:*:*:*:*:*:*:*"], "purl": "pkg:npm/crypto-js@3.1.9-1", "upstreams": []}}, {"vulnerability": {"id": "GHSA-3cqr-58rm-57f8", "dataSource": "https://github.com/advisories/GHSA-3cqr-58rm-57f8", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Arbitrary Code Execution in Handlebars", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:C/C:H/I:L/A:L", "metrics": {"baseScore": 8.1, "exploitabilityScore": 2.3, "impactScore": 5.3}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2019-20920", "epss": 0.00343, "percentile": 0.5619, "date": "2025-08-23"}], "fix": {"versions": ["4.5.3"], "state": "fixed"}, "advisories": [], "risk": 0.26754}, "relatedVulnerabilities": [{"id": "CVE-2019-20920", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2019-20920", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://snyk.io/vuln/SNYK-JS-HANDLEBARS-534478", "https://www.npmjs.com/advisories/1316", "https://www.npmjs.com/advisories/1324", "https://snyk.io/vuln/SNYK-JS-HANDLEBARS-534478", "https://www.npmjs.com/advisories/1316", "https://www.npmjs.com/advisories/1324"], "description": "Handlebars before 3.0.8 and 4.x before 4.5.3 is vulnerable to Arbitrary Code Execution. The lookup helper fails to properly validate templates, allowing attackers to submit templates that execute arbitrary JavaScript. This can be used to run arbitrary code on a server processing Handlebars templates or in a victim's browser (effectively serving as XSS).", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:C/C:H/I:L/A:L", "metrics": {"baseScore": 8.1, "exploitabilityScore": 2.3, "impactScore": 5.3}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:M/Au:N/C:P/I:P/A:P", "metrics": {"baseScore": 6.8, "exploitabilityScore": 8.6, "impactScore": 6.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2019-20920", "epss": 0.00343, "percentile": 0.5619, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "handlebars", "version": "4.1.2"}}, "found": {"vulnerabilityID": "GHSA-3cqr-58rm-57f8", "versionConstraint": ">=4.0.0,<4.5.3 (unknown)"}, "fix": {"suggestedVersion": "4.5.3"}}], "artifact": {"id": "d777ff7ccae5fee5", "name": "handlebars", "version": "4.1.2", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:handlebars.js_project:handlebars.js:4.1.2:*:*:*:*:node.js:*:*", "cpe:2.3:a:handlebarsjs:handlebars:4.1.2:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/handlebars@4.1.2", "upstreams": []}}, {"vulnerability": {"id": "GHSA-6fc8-4gx4-v693", "dataSource": "https://github.com/advisories/GHSA-6fc8-4gx4-v693", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "ReDoS in Sec-Websocket-Protocol header", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-32640", "epss": 0.00466, "percentile": 0.63425, "date": "2025-08-23"}], "fix": {"versions": ["6.2.2"], "state": "fixed"}, "advisories": [], "risk": 0.23998999999999998}, "relatedVulnerabilities": [{"id": "CVE-2021-32640", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2021-32640", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://github.com/websockets/ws/commit/00c425ec77993773d823f018f64a5c44e17023ff", "https://github.com/websockets/ws/security/advisories/GHSA-6fc8-4gx4-v693", "https://lists.apache.org/thread.html/rdfa7b6253c4d6271e31566ecd5f30b7ce1b8fb2c89d52b8c4e0f4e30%40%3Ccommits.tinkerpop.apache.org%3E", "https://security.netapp.com/advisory/ntap-20210706-0005/", "https://github.com/websockets/ws/commit/00c425ec77993773d823f018f64a5c44e17023ff", "https://github.com/websockets/ws/security/advisories/GHSA-6fc8-4gx4-v693", "https://lists.apache.org/thread.html/rdfa7b6253c4d6271e31566ecd5f30b7ce1b8fb2c89d52b8c4e0f4e30%40%3Ccommits.tinkerpop.apache.org%3E", "https://security.netapp.com/advisory/ntap-20210706-0005/"], "description": "ws is an open source WebSocket client and server library for Node.js. A specially crafted value of the `Sec-Websocket-Protocol` header can be used to significantly slow down a ws server. The vulnerability has been fixed in ws@7.4.6 (https://github.com/websockets/ws/commit/00c425ec77993773d823f018f64a5c44e17023ff). In vulnerable versions of ws, the issue can be mitigated by reducing the maximum allowed length of the request headers using the [`--max-http-header-size=size`](https://nodejs.org/api/cli.html#cli_max_http_header_size_size) and/or the [`maxHeaderSize`](https://nodejs.org/api/http.html#http_http_createserver_options_requestlistener) options.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:N/I:N/A:P", "metrics": {"baseScore": 5, "exploitabilityScore": 10, "impactScore": 2.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-32640", "epss": 0.00466, "percentile": 0.63425, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "ws", "version": "6.1.4"}}, "found": {"vulnerabilityID": "GHSA-6fc8-4gx4-v693", "versionConstraint": ">=6.0.0,<6.2.2 (unknown)"}, "fix": {"suggestedVersion": "6.2.2"}}], "artifact": {"id": "4a9dff42fec13fca", "name": "ws", "version": "6.1.4", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:ws_project:ws:6.1.4:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/ws@6.1.4", "upstreams": []}}, {"vulnerability": {"id": "GHSA-6fc8-4gx4-v693", "dataSource": "https://github.com/advisories/GHSA-6fc8-4gx4-v693", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "ReDoS in Sec-Websocket-Protocol header", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-32640", "epss": 0.00466, "percentile": 0.63425, "date": "2025-08-23"}], "fix": {"versions": ["6.2.2"], "state": "fixed"}, "advisories": [], "risk": 0.23998999999999998}, "relatedVulnerabilities": [{"id": "CVE-2021-32640", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2021-32640", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://github.com/websockets/ws/commit/00c425ec77993773d823f018f64a5c44e17023ff", "https://github.com/websockets/ws/security/advisories/GHSA-6fc8-4gx4-v693", "https://lists.apache.org/thread.html/rdfa7b6253c4d6271e31566ecd5f30b7ce1b8fb2c89d52b8c4e0f4e30%40%3Ccommits.tinkerpop.apache.org%3E", "https://security.netapp.com/advisory/ntap-20210706-0005/", "https://github.com/websockets/ws/commit/00c425ec77993773d823f018f64a5c44e17023ff", "https://github.com/websockets/ws/security/advisories/GHSA-6fc8-4gx4-v693", "https://lists.apache.org/thread.html/rdfa7b6253c4d6271e31566ecd5f30b7ce1b8fb2c89d52b8c4e0f4e30%40%3Ccommits.tinkerpop.apache.org%3E", "https://security.netapp.com/advisory/ntap-20210706-0005/"], "description": "ws is an open source WebSocket client and server library for Node.js. A specially crafted value of the `Sec-Websocket-Protocol` header can be used to significantly slow down a ws server. The vulnerability has been fixed in ws@7.4.6 (https://github.com/websockets/ws/commit/00c425ec77993773d823f018f64a5c44e17023ff). In vulnerable versions of ws, the issue can be mitigated by reducing the maximum allowed length of the request headers using the [`--max-http-header-size=size`](https://nodejs.org/api/cli.html#cli_max_http_header_size_size) and/or the [`maxHeaderSize`](https://nodejs.org/api/http.html#http_http_createserver_options_requestlistener) options.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:N/I:N/A:P", "metrics": {"baseScore": 5, "exploitabilityScore": 10, "impactScore": 2.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-32640", "epss": 0.00466, "percentile": 0.63425, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "ws", "version": "6.2.1"}}, "found": {"vulnerabilityID": "GHSA-6fc8-4gx4-v693", "versionConstraint": ">=6.0.0,<6.2.2 (unknown)"}, "fix": {"suggestedVersion": "6.2.2"}}], "artifact": {"id": "c5a6464aa9cd2ced", "name": "ws", "version": "6.2.1", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:ws_project:ws:6.2.1:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/ws@6.2.1", "upstreams": []}}, {"vulnerability": {"id": "GHSA-62gr-4qp9-h98f", "dataSource": "https://github.com/advisories/GHSA-62gr-4qp9-h98f", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Regular Expression Denial of Service in Handlebars", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2019-20922", "epss": 0.0025, "percentile": 0.48126, "date": "2025-08-23"}], "fix": {"versions": ["4.4.5"], "state": "fixed"}, "advisories": [], "risk": 0.1875}, "relatedVulnerabilities": [{"id": "CVE-2019-20922", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2019-20922", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/handlebars-lang/handlebars.js/commit/8d5530ee2c3ea9f0aee3fde310b9f36887d00b8b", "https://snyk.io/vuln/SNYK-JS-HANDLEBARS-480388", "https://www.npmjs.com/advisories/1300", "https://github.com/handlebars-lang/handlebars.js/commit/8d5530ee2c3ea9f0aee3fde310b9f36887d00b8b", "https://snyk.io/vuln/SNYK-JS-HANDLEBARS-480388", "https://www.npmjs.com/advisories/1300"], "description": "Handlebars before 4.4.5 allows Regular Expression Denial of Service (ReDoS) because of eager matching. The parser may be forced into an endless loop while processing crafted templates. This may allow attackers to exhaust system resources.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:N/I:N/A:C", "metrics": {"baseScore": 7.8, "exploitabilityScore": 10, "impactScore": 6.9}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2019-20922", "epss": 0.0025, "percentile": 0.48126, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "handlebars", "version": "4.1.2"}}, "found": {"vulnerabilityID": "GHSA-62gr-4qp9-h98f", "versionConstraint": ">=4.0.0,<4.4.5 (unknown)"}, "fix": {"suggestedVersion": "4.4.5"}}], "artifact": {"id": "d777ff7ccae5fee5", "name": "handlebars", "version": "4.1.2", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:handlebars.js_project:handlebars.js:4.1.2:*:*:*:*:node.js:*:*", "cpe:2.3:a:handlebarsjs:handlebars:4.1.2:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/handlebars@4.1.2", "upstreams": []}}, {"vulnerability": {"id": "GHSA-h9rv-jmmf-4pgx", "dataSource": "https://github.com/advisories/GHSA-h9rv-jmmf-4pgx", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "Cross-Site Scripting in serialize-javascript", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:N/I:L/A:L", "metrics": {"baseScore": 4.2, "exploitabilityScore": 1.7, "impactScore": 2.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2019-16769", "epss": 0.00406, "percentile": 0.60248, "date": "2025-08-23"}], "fix": {"versions": ["2.1.1"], "state": "fixed"}, "advisories": [], "risk": 0.18676000000000004}, "relatedVulnerabilities": [{"id": "CVE-2019-16769", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2019-16769", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://github.com/yahoo/serialize-javascript/security/advisories/GHSA-h9rv-jmmf-4pgx", "https://github.com/yahoo/serialize-javascript/security/advisories/GHSA-h9rv-jmmf-4pgx"], "description": "The serialize-javascript npm package before version 2.1.1 is vulnerable to Cross-site Scripting (XSS). It does not properly mitigate against unsafe characters in serialized regular expressions. This vulnerability is not affected on Node.js environment since Node.js's implementation of RegExp.prototype.toString() backslash-escapes all forward slashes in regular expressions. If serialized data of regular expression objects are used in an environment other than Node.js, it is affected by this vulnerability.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:R/S:C/C:L/I:L/A:N", "metrics": {"baseScore": 5.4, "exploitabilityScore": 2.3, "impactScore": 2.8}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:M/Au:S/C:N/I:P/A:N", "metrics": {"baseScore": 3.5, "exploitabilityScore": 6.9, "impactScore": 2.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:N/I:L/A:L", "metrics": {"baseScore": 4.2, "exploitabilityScore": 1.7, "impactScore": 2.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2019-16769", "epss": 0.00406, "percentile": 0.60248, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "serialize-javascript", "version": "1.7.0"}}, "found": {"vulnerabilityID": "GHSA-h9rv-jmmf-4pgx", "versionConstraint": "<2.1.1 (unknown)"}, "fix": {"suggestedVersion": "2.1.1"}}], "artifact": {"id": "c9ddd9c6e2384a83", "name": "serialize-javascript", "version": "1.7.0", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["BSD-3-<PERSON><PERSON>"], "cpes": ["cpe:2.3:a:verizon:serialize-javascript:1.7.0:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/serialize-javascript@1.7.0", "upstreams": []}}, {"vulnerability": {"id": "GHSA-r628-mhmh-qjhw", "dataSource": "https://github.com/advisories/GHSA-r628-mhmh-qjhw", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Arbitrary File Creation/Overwrite via insufficient symlink protection due to directory cache poisoning", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:N", "metrics": {"baseScore": 8.2, "exploitabilityScore": 1.9, "impactScore": 5.8}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-32803", "epss": 0.00208, "percentile": 0.43298, "date": "2025-08-23"}], "fix": {"versions": ["4.4.15"], "state": "fixed"}, "advisories": [], "risk": 0.16327999999999998}, "relatedVulnerabilities": [{"id": "CVE-2021-32803", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2021-32803", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://cert-portal.siemens.com/productcert/pdf/ssa-389290.pdf", "https://github.com/npm/node-tar/commit/9dbdeb6df8e9dbd96fa9e84341b9d74734be6c20", "https://github.com/npm/node-tar/security/advisories/GHSA-r628-mhmh-qjhw", "https://www.npmjs.com/advisories/1771", "https://www.npmjs.com/package/tar", "https://www.oracle.com/security-alerts/cpuoct2021.html", "https://cert-portal.siemens.com/productcert/pdf/ssa-389290.pdf", "https://github.com/npm/node-tar/commit/9dbdeb6df8e9dbd96fa9e84341b9d74734be6c20", "https://github.com/npm/node-tar/security/advisories/GHSA-r628-mhmh-qjhw", "https://www.npmjs.com/advisories/1771", "https://www.npmjs.com/package/tar", "https://www.oracle.com/security-alerts/cpuoct2021.html"], "description": "The npm package \"tar\" (aka node-tar) before versions 6.1.2, 5.0.7, 4.4.15, and 3.2.3 has an arbitrary File Creation/Overwrite vulnerability via insufficient symlink protection. `node-tar` aims to guarantee that any file whose location would be modified by a symbolic link is not extracted. This is, in part, achieved by ensuring that extracted directories are not symlinks. Additionally, in order to prevent unnecessary `stat` calls to determine whether a given path is a directory, paths are cached when directories are created. This logic was insufficient when extracting tar files that contained both a directory and a symlink with the same name as the directory. This order of operations resulted in the directory being created and added to the `node-tar` directory cache. When a directory is present in the directory cache, subsequent calls to mkdir for that directory are skipped. However, this is also where `node-tar` checks for symlinks occur. By first creating a directory, and then replacing that directory with a symlink, it was thus possible to bypass `node-tar` symlink checks on directories, essentially allowing an untrusted tar file to symlink into an arbitrary location and subsequently extracting arbitrary files into that location, thus allowing arbitrary file creation and overwrite. This issue was addressed in releases 3.2.3, 4.4.15, 5.0.7 and 6.1.2.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:H/A:H", "metrics": {"baseScore": 8.1, "exploitabilityScore": 2.9, "impactScore": 5.2}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:M/Au:N/C:N/I:P/A:P", "metrics": {"baseScore": 5.8, "exploitabilityScore": 8.6, "impactScore": 5}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:N", "metrics": {"baseScore": 8.2, "exploitabilityScore": 1.9, "impactScore": 5.8}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-32803", "epss": 0.00208, "percentile": 0.43298, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "tar", "version": "4.4.8"}}, "found": {"vulnerabilityID": "GHSA-r628-mhmh-qjhw", "versionConstraint": ">=4.0.0,<4.4.15 (unknown)"}, "fix": {"suggestedVersion": "4.4.15"}}], "artifact": {"id": "6cfd37cbdc418b39", "name": "tar", "version": "4.4.8", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["ISC"], "cpes": ["cpe:2.3:a:tar_project:tar:4.4.8:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/tar@4.4.8", "upstreams": []}}, {"vulnerability": {"id": "GHSA-vh95-rmgr-6w4m", "dataSource": "https://github.com/advisories/GHSA-vh95-rmgr-6w4m", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "Prototype Pollution in minimist", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:L/A:L", "metrics": {"baseScore": 5.6, "exploitabilityScore": 2.3, "impactScore": 3.4}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2020-7598", "epss": 0.00253, "percentile": 0.48458, "date": "2025-08-23"}], "fix": {"versions": ["0.2.1"], "state": "fixed"}, "advisories": [], "risk": 0.13409000000000001}, "relatedVulnerabilities": [{"id": "CVE-2020-7598", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2020-7598", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["http://lists.opensuse.org/opensuse-security-announce/2020-06/msg00024.html", "https://snyk.io/vuln/SNYK-JS-MINIMIST-559764", "http://lists.opensuse.org/opensuse-security-announce/2020-06/msg00024.html", "https://snyk.io/vuln/SNYK-JS-MINIMIST-559764"], "description": "minimist before 1.2.2 could be tricked into adding or modifying properties of Object.prototype using a \"constructor\" or \"__proto__\" payload.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:L/A:L", "metrics": {"baseScore": 5.6, "exploitabilityScore": 2.3, "impactScore": 3.4}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:M/Au:N/C:P/I:P/A:P", "metrics": {"baseScore": 6.8, "exploitabilityScore": 8.6, "impactScore": 6.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2020-7598", "epss": 0.00253, "percentile": 0.48458, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "minimist", "version": "0.0.10"}}, "found": {"vulnerabilityID": "GHSA-vh95-rmgr-6w4m", "versionConstraint": "<0.2.1 (unknown)"}, "fix": {"suggestedVersion": "0.2.1"}}], "artifact": {"id": "fec4f6e381234988", "name": "minimist", "version": "0.0.10", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:minimist:minimist:0.0.10:*:*:*:*:*:*:*"], "purl": "pkg:npm/minimist@0.0.10", "upstreams": []}}, {"vulnerability": {"id": "GHSA-vh95-rmgr-6w4m", "dataSource": "https://github.com/advisories/GHSA-vh95-rmgr-6w4m", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "Prototype Pollution in minimist", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:L/A:L", "metrics": {"baseScore": 5.6, "exploitabilityScore": 2.3, "impactScore": 3.4}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2020-7598", "epss": 0.00253, "percentile": 0.48458, "date": "2025-08-23"}], "fix": {"versions": ["1.2.3"], "state": "fixed"}, "advisories": [], "risk": 0.13409000000000001}, "relatedVulnerabilities": [{"id": "CVE-2020-7598", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2020-7598", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["http://lists.opensuse.org/opensuse-security-announce/2020-06/msg00024.html", "https://snyk.io/vuln/SNYK-JS-MINIMIST-559764", "http://lists.opensuse.org/opensuse-security-announce/2020-06/msg00024.html", "https://snyk.io/vuln/SNYK-JS-MINIMIST-559764"], "description": "minimist before 1.2.2 could be tricked into adding or modifying properties of Object.prototype using a \"constructor\" or \"__proto__\" payload.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:L/A:L", "metrics": {"baseScore": 5.6, "exploitabilityScore": 2.3, "impactScore": 3.4}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:M/Au:N/C:P/I:P/A:P", "metrics": {"baseScore": 6.8, "exploitabilityScore": 8.6, "impactScore": 6.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2020-7598", "epss": 0.00253, "percentile": 0.48458, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "minimist", "version": "1.2.0"}}, "found": {"vulnerabilityID": "GHSA-vh95-rmgr-6w4m", "versionConstraint": ">=1.0.0,<1.2.3 (unknown)"}, "fix": {"suggestedVersion": "1.2.3"}}], "artifact": {"id": "21d1985e81603cdd", "name": "minimist", "version": "1.2.0", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:minimist:minimist:1.2.0:*:*:*:*:*:*:*"], "purl": "pkg:npm/minimist@1.2.0", "upstreams": []}}, {"vulnerability": {"id": "GHSA-776f-qx25-q3cc", "dataSource": "https://github.com/advisories/GHSA-776f-qx25-q3cc", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "xml2js is vulnerable to prototype pollution", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2023-0842", "epss": 0.00251, "percentile": 0.48346, "date": "2025-08-23"}], "fix": {"versions": ["0.5.0"], "state": "fixed"}, "advisories": [], "risk": 0.129265}, "relatedVulnerabilities": [{"id": "CVE-2023-0842", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2023-0842", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://fluidattacks.com/advisories/myers/", "https://github.com/<PERSON><PERSON>-from-XIV/node-xml2js/", "https://lists.debian.org/debian-lts-announce/2024/03/msg00013.html", "https://fluidattacks.com/advisories/myers/", "https://github.com/<PERSON><PERSON>-from-XIV/node-xml2js/", "https://lists.debian.org/debian-lts-announce/2024/03/msg00013.html"], "description": "xml2js version 0.4.23 allows an external attacker to edit or add new properties to an object. This is possible because the application does not properly validate incoming JSON keys, thus allowing the __proto__ property to be edited.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}, {"source": "134c704f-9b21-4f2e-91b3-4a467353bcc0", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2023-0842", "epss": 0.00251, "percentile": 0.48346, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "xml2js", "version": "0.4.19"}}, "found": {"vulnerabilityID": "GHSA-776f-qx25-q3cc", "versionConstraint": "<0.5.0 (unknown)"}, "fix": {"suggestedVersion": "0.5.0"}}], "artifact": {"id": "f0117d293a59d8ae", "name": "xml2js", "version": "0.4.19", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:xml2js:xml2js:0.4.19:*:*:*:*:*:*:*"], "purl": "pkg:npm/xml2js@0.4.19", "upstreams": []}}, {"vulnerability": {"id": "GHSA-9wv6-86v2-598j", "dataSource": "https://github.com/advisories/GHSA-9wv6-86v2-598j", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "path-to-regexp outputs backtracking regular expressions", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-45296", "epss": 0.00167, "percentile": 0.38376, "date": "2025-08-23"}], "fix": {"versions": ["0.1.10"], "state": "fixed"}, "advisories": [], "risk": 0.12525000000000003}, "relatedVulnerabilities": [{"id": "CVE-2024-45296", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2024-45296", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/pillarjs/path-to-regexp/commit/29b96b4a1de52824e1ca0f49a701183cc4ed476f", "https://github.com/pillarjs/path-to-regexp/commit/60f2121e9b66b7b622cc01080df0aabda9eedee6", "https://github.com/pillarjs/path-to-regexp/security/advisories/GHSA-9wv6-86v2-598j", "https://security.netapp.com/advisory/ntap-20250124-0001/"], "description": "path-to-regexp turns path strings into a regular expressions. In certain cases, path-to-regexp will output a regular expression that can be exploited to cause poor performance. Because JavaScript is single threaded and regex matching runs on the main thread, poor performance will block the event loop and lead to a DoS. The bad regular expression is generated any time you have two parameters within a single segment, separated by something that is not a period (.). For users of 0.1, upgrade to 0.1.10. All other users should upgrade to 8.0.0.", "cvss": [{"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-45296", "epss": 0.00167, "percentile": 0.38376, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "path-to-regexp", "version": "0.1.7"}}, "found": {"vulnerabilityID": "GHSA-9wv6-86v2-598j", "versionConstraint": "<0.1.10 (unknown)"}, "fix": {"suggestedVersion": "0.1.10"}}], "artifact": {"id": "81361776d3c596db", "name": "path-to-regexp", "version": "0.1.7", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:path-to-regexp:path-to-regexp:0.1.7:*:*:*:*:*:*:*", "cpe:2.3:a:path-to-regexp:path_to_regexp:0.1.7:*:*:*:*:*:*:*", "cpe:2.3:a:path_to_regexp:path-to-regexp:0.1.7:*:*:*:*:*:*:*", "cpe:2.3:a:path_to_regexp:path_to_regexp:0.1.7:*:*:*:*:*:*:*", "cpe:2.3:a:path-to:path-to-regexp:0.1.7:*:*:*:*:*:*:*", "cpe:2.3:a:path-to:path_to_regexp:0.1.7:*:*:*:*:*:*:*", "cpe:2.3:a:path_to:path-to-regexp:0.1.7:*:*:*:*:*:*:*", "cpe:2.3:a:path_to:path_to_regexp:0.1.7:*:*:*:*:*:*:*", "cpe:2.3:a:path:path-to-regexp:0.1.7:*:*:*:*:*:*:*", "cpe:2.3:a:path:path_to_regexp:0.1.7:*:*:*:*:*:*:*"], "purl": "pkg:npm/path-to-regexp@0.1.7", "upstreams": []}}, {"vulnerability": {"id": "GHSA-f5x3-32g6-xq36", "dataSource": "https://github.com/advisories/GHSA-f5x3-32g6-xq36", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "Denial of service while parsing a tar file due to lack of folders count validation", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 6.5, "exploitabilityScore": 2.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-28863", "epss": 0.00205, "percentile": 0.42822, "date": "2025-08-23"}], "fix": {"versions": ["6.2.1"], "state": "fixed"}, "advisories": [], "risk": 0.11787500000000001}, "relatedVulnerabilities": [{"id": "CVE-2024-28863", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2024-28863", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://github.com/isaacs/node-tar/commit/fe8cd57da5686f8695415414bda49206a545f7f7", "https://github.com/isaacs/node-tar/security/advisories/GHSA-f5x3-32g6-xq36", "https://security.netapp.com/advisory/ntap-20240524-0005/", "https://github.com/isaacs/node-tar/commit/fe8cd57da5686f8695415414bda49206a545f7f7", "https://github.com/isaacs/node-tar/security/advisories/GHSA-f5x3-32g6-xq36", "https://security.netapp.com/advisory/ntap-20240524-0005/"], "description": "node-tar is a Tar for Node.js. node-tar prior to version 6.2.1 has no limit on the number of sub-folders created in the folder creation process. An attacker who generates a large number of sub-folders can consume memory on the system running node-tar and even crash the Node.js client within few seconds of running it using a path with too many sub-folders inside. Version 6.2.1 fixes this issue by preventing extraction in excessively deep sub-folders.", "cvss": [{"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 6.5, "exploitabilityScore": 2.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-28863", "epss": 0.00205, "percentile": 0.42822, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "tar", "version": "4.4.8"}}, "found": {"vulnerabilityID": "GHSA-f5x3-32g6-xq36", "versionConstraint": "<6.2.1 (unknown)"}, "fix": {"suggestedVersion": "6.2.1"}}], "artifact": {"id": "6cfd37cbdc418b39", "name": "tar", "version": "4.4.8", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["ISC"], "cpes": ["cpe:2.3:a:tar_project:tar:4.4.8:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/tar@4.4.8", "upstreams": []}}, {"vulnerability": {"id": "GHSA-29mw-wpgm-hmr9", "dataSource": "https://github.com/advisories/GHSA-29mw-wpgm-hmr9", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "Regular Expression Denial of Service (ReDoS) in lodash", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2020-28500", "epss": 0.00202, "percentile": 0.42601, "date": "2025-08-23"}], "fix": {"versions": ["4.17.21"], "state": "fixed"}, "advisories": [], "risk": 0.10403000000000001}, "relatedVulnerabilities": [{"id": "CVE-2020-28500", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2020-28500", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://cert-portal.siemens.com/productcert/pdf/ssa-637483.pdf", "https://github.com/lodash/lodash/blob/npm/trimEnd.js%23L8", "https://github.com/lodash/lodash/pull/5065", "https://security.netapp.com/advisory/ntap-20210312-0006/", "https://snyk.io/vuln/SNYK-JAVA-ORGFUJIONWEBJARS-1074896", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARS-1074894", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWER-1074892", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWERGITHUBLODASH-1074895", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-1074893", "https://snyk.io/vuln/SNYK-JS-LODASH-1018905", "https://www.oracle.com//security-alerts/cpujul2021.html", "https://www.oracle.com/security-alerts/cpujan2022.html", "https://www.oracle.com/security-alerts/cpujul2022.html", "https://www.oracle.com/security-alerts/cpuoct2021.html", "https://cert-portal.siemens.com/productcert/pdf/ssa-637483.pdf", "https://github.com/lodash/lodash/blob/npm/trimEnd.js%23L8", "https://github.com/lodash/lodash/pull/5065", "https://security.netapp.com/advisory/ntap-20210312-0006/", "https://snyk.io/vuln/SNYK-JAVA-ORGFUJIONWEBJARS-1074896", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARS-1074894", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWER-1074892", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWERGITHUBLODASH-1074895", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-1074893", "https://snyk.io/vuln/SNYK-JS-LODASH-1018905", "https://www.oracle.com//security-alerts/cpujul2021.html", "https://www.oracle.com/security-alerts/cpujan2022.html", "https://www.oracle.com/security-alerts/cpujul2022.html", "https://www.oracle.com/security-alerts/cpuoct2021.html"], "description": "Lodash versions prior to 4.17.21 are vulnerable to Regular Expression Denial of Service (ReDoS) via the toNumber, trim and trimEnd functions.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:N/I:N/A:P", "metrics": {"baseScore": 5, "exploitabilityScore": 10, "impactScore": 2.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2020-28500", "epss": 0.00202, "percentile": 0.42601, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "lodash", "version": "4.17.11"}}, "found": {"vulnerabilityID": "GHSA-29mw-wpgm-hmr9", "versionConstraint": "<4.17.21 (unknown)"}, "fix": {"suggestedVersion": "4.17.21"}}], "artifact": {"id": "b4550ad74bbf0b78", "name": "lodash", "version": "4.17.11", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:lodash:lodash:4.17.11:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/lodash@4.17.11", "upstreams": []}}, {"vulnerability": {"id": "GHSA-fxwf-4rqh-v8g3", "dataSource": "https://github.com/advisories/GHSA-fxwf-4rqh-v8g3", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "CORS misconfiguration in socket.io", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:N", "metrics": {"baseScore": 4.3, "exploitabilityScore": 2.9, "impactScore": 1.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2020-28481", "epss": 0.00189, "percentile": 0.41031, "date": "2025-08-23"}], "fix": {"versions": ["2.4.0"], "state": "fixed"}, "advisories": [], "risk": 0.08788499999999999}, "relatedVulnerabilities": [{"id": "CVE-2020-28481", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2020-28481", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://github.com/socketio/socket.io/issues/3671", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWER-1056358", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-1056357", "https://snyk.io/vuln/SNYK-JS-SOCKETIO-1024859", "https://github.com/socketio/socket.io/issues/3671", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSBOWER-1056358", "https://snyk.io/vuln/SNYK-JAVA-ORGWEBJARSNPM-1056357", "https://snyk.io/vuln/SNYK-JS-SOCKETIO-1024859"], "description": "The package socket.io before 2.4.0 are vulnerable to Insecure Defaults due to CORS Misconfiguration. All domains are whitelisted by default.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:N", "metrics": {"baseScore": 4.3, "exploitabilityScore": 2.9, "impactScore": 1.5}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:S/C:P/I:N/A:N", "metrics": {"baseScore": 4, "exploitabilityScore": 8, "impactScore": 2.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2020-28481", "epss": 0.00189, "percentile": 0.41031, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "socket.io", "version": "2.2.0"}}, "found": {"vulnerabilityID": "GHSA-fxwf-4rqh-v8g3", "versionConstraint": "<2.4.0 (unknown)"}, "fix": {"suggestedVersion": "2.4.0"}}], "artifact": {"id": "cbee62591384875a", "name": "socket.io", "version": "2.2.0", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:socket:socket.io:2.2.0:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/socket.io@2.2.0", "upstreams": []}}, {"vulnerability": {"id": "GHSA-x4jg-mjrx-434g", "dataSource": "https://github.com/advisories/GHSA-x4jg-mjrx-434g", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Improper Verification of Cryptographic Signature in node-forge", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-24772", "epss": 0.00086, "percentile": 0.2565, "date": "2025-08-23"}], "fix": {"versions": ["1.3.0"], "state": "fixed"}, "advisories": [], "risk": 0.0645}, "relatedVulnerabilities": [{"id": "CVE-2022-24772", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2022-24772", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/digitalbazaar/forge/commit/3f0b49a0573ef1bb7af7f5673c0cfebf00424df1", "https://github.com/digitalbazaar/forge/commit/bb822c02df0b61211836472e29b9790cc541cdb2", "https://github.com/digitalbazaar/forge/security/advisories/GHSA-x4jg-mjrx-434g", "https://github.com/digitalbazaar/forge/commit/3f0b49a0573ef1bb7af7f5673c0cfebf00424df1", "https://github.com/digitalbazaar/forge/commit/bb822c02df0b61211836472e29b9790cc541cdb2", "https://github.com/digitalbazaar/forge/security/advisories/GHSA-x4jg-mjrx-434g"], "description": "Forge (also called `node-forge`) is a native implementation of Transport Layer Security in JavaScript. Prior to version 1.3.0, RSA PKCS#1 v1.5 signature verification code does not check for tailing garbage bytes after decoding a `DigestInfo` ASN.1 structure. This can allow padding bytes to be removed and garbage data added to forge a signature when a low public exponent is being used. The issue has been addressed in `node-forge` version 1.3.0. There are currently no known workarounds.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:N/I:P/A:N", "metrics": {"baseScore": 5, "exploitabilityScore": 10, "impactScore": 2.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-24772", "epss": 0.00086, "percentile": 0.2565, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "node-forge", "version": "0.8.5"}}, "found": {"vulnerabilityID": "GHSA-x4jg-mjrx-434g", "versionConstraint": "<1.3.0 (unknown)"}, "fix": {"suggestedVersion": "1.3.0"}}], "artifact": {"id": "087c9d217c8255bc", "name": "node-forge", "version": "0.8.5", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["(BSD-3-<PERSON><PERSON> OR GPL-2.0)"], "cpes": ["cpe:2.3:a:digitalbazaar:forge:0.8.5:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/node-forge@0.8.5", "upstreams": []}}, {"vulnerability": {"id": "GHSA-25hc-qcg6-38wj", "dataSource": "https://github.com/advisories/GHSA-25hc-qcg6-38wj", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "socket.io has an unhandled 'error' event", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:L", "metrics": {"baseScore": 7.3, "exploitabilityScore": 3.9, "impactScore": 3.4}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-38355", "epss": 0.001, "percentile": 0.28342, "date": "2025-08-23"}], "fix": {"versions": ["2.5.1"], "state": "fixed"}, "advisories": [], "risk": 0.0615}, "relatedVulnerabilities": [{"id": "CVE-2024-38355", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2024-38355", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/socketio/socket.io/commit/15af22fc22bc6030fcead322c106f07640336115", "https://github.com/socketio/socket.io/commit/d30630ba10562bf987f4d2b42440fc41a828119c", "https://github.com/socketio/socket.io/security/advisories/GHSA-25hc-qcg6-38wj", "https://github.com/socketio/socket.io/commit/15af22fc22bc6030fcead322c106f07640336115", "https://github.com/socketio/socket.io/commit/d30630ba10562bf987f4d2b42440fc41a828119c", "https://github.com/socketio/socket.io/security/advisories/GHSA-25hc-qcg6-38wj", "https://www.vicarius.io/vsociety/posts/unhandled-exception-in-socketio-cve-2024-38355"], "description": "Socket.IO is an open source, real-time, bidirectional, event-based, communication framework. A specially crafted Socket.IO packet can trigger an uncaught exception on the Socket.IO server, thus killing the Node.js process. This issue is fixed by commit `15af22fc22` which has been included in `socket.io@4.6.2` (released in May 2023). The fix was backported in the 2.x branch as well with commit `d30630ba10`. Users are advised to upgrade. Users unable to upgrade may attach a listener for the \"error\" event to catch these errors.", "cvss": [{"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:L", "metrics": {"baseScore": 7.3, "exploitabilityScore": 3.9, "impactScore": 3.4}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-38355", "epss": 0.001, "percentile": 0.28342, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "socket.io", "version": "2.2.0"}}, "found": {"vulnerabilityID": "GHSA-25hc-qcg6-38wj", "versionConstraint": "<2.5.0 (unknown)"}, "fix": {"suggestedVersion": "2.5.1"}}], "artifact": {"id": "cbee62591384875a", "name": "socket.io", "version": "2.2.0", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:socket:socket.io:2.2.0:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/socket.io@2.2.0", "upstreams": []}}, {"vulnerability": {"id": "GHSA-cfm4-qjh2-4765", "dataSource": "https://github.com/advisories/GHSA-cfm4-qjh2-4765", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Improper Verification of Cryptographic Signature in node-forge", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-24771", "epss": 0.00081, "percentile": 0.24486, "date": "2025-08-23"}], "fix": {"versions": ["1.3.0"], "state": "fixed"}, "advisories": [], "risk": 0.06075}, "relatedVulnerabilities": [{"id": "CVE-2022-24771", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2022-24771", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/digitalbazaar/forge/commit/3f0b49a0573ef1bb7af7f5673c0cfebf00424df1", "https://github.com/digitalbazaar/forge/security/advisories/GHSA-cfm4-qjh2-4765", "https://github.com/digitalbazaar/forge/commit/3f0b49a0573ef1bb7af7f5673c0cfebf00424df1", "https://github.com/digitalbazaar/forge/security/advisories/GHSA-cfm4-qjh2-4765"], "description": "Forge (also called `node-forge`) is a native implementation of Transport Layer Security in JavaScript. Prior to version 1.3.0, RSA PKCS#1 v1.5 signature verification code is lenient in checking the digest algorithm structure. This can allow a crafted structure that steals padding bytes and uses unchecked portion of the PKCS#1 encoded message to forge a signature when a low public exponent is being used. The issue has been addressed in `node-forge` version 1.3.0. There are currently no known workarounds.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:N/I:P/A:N", "metrics": {"baseScore": 5, "exploitabilityScore": 10, "impactScore": 2.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-24771", "epss": 0.00081, "percentile": 0.24486, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "node-forge", "version": "0.8.5"}}, "found": {"vulnerabilityID": "GHSA-cfm4-qjh2-4765", "versionConstraint": "<1.3.0 (unknown)"}, "fix": {"suggestedVersion": "1.3.0"}}], "artifact": {"id": "087c9d217c8255bc", "name": "node-forge", "version": "0.8.5", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["(BSD-3-<PERSON><PERSON> OR GPL-2.0)"], "cpes": ["cpe:2.3:a:digitalbazaar:forge:0.8.5:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/node-forge@0.8.5", "upstreams": []}}, {"vulnerability": {"id": "GHSA-p9pc-299p-vxgp", "dataSource": "https://github.com/advisories/GHSA-p9pc-299p-vxgp", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "yargs-parser Vulnerable to Prototype Pollution", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:L", "metrics": {"baseScore": 5.3, "exploitabilityScore": 1.9, "impactScore": 3.4}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2020-7608", "epss": 0.0011, "percentile": 0.30099, "date": "2025-08-23"}], "fix": {"versions": ["13.1.2"], "state": "fixed"}, "advisories": [], "risk": 0.056650000000000006}, "relatedVulnerabilities": [{"id": "CVE-2020-7608", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2020-7608", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://snyk.io/vuln/SNYK-JS-YARGSPARSER-560381", "https://snyk.io/vuln/SNYK-JS-YARGSPARSER-560381"], "description": "yargs-parser could be tricked into adding or modifying properties of Object.prototype using a \"__proto__\" payload.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:L", "metrics": {"baseScore": 5.3, "exploitabilityScore": 1.9, "impactScore": 3.4}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:L/AC:L/Au:N/C:P/I:P/A:P", "metrics": {"baseScore": 4.6, "exploitabilityScore": 4, "impactScore": 6.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2020-7608", "epss": 0.0011, "percentile": 0.30099, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "yargs-parser", "version": "13.1.0"}}, "found": {"vulnerabilityID": "GHSA-p9pc-299p-vxgp", "versionConstraint": ">=6.0.0,<13.1.2 (unknown)"}, "fix": {"suggestedVersion": "13.1.2"}}], "artifact": {"id": "da05d78584f6bf72", "name": "yargs-parser", "version": "13.1.0", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["ISC"], "cpes": ["cpe:2.3:a:yargs:yargs-parser:13.1.0:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/yargs-parser@13.1.0", "upstreams": []}}, {"vulnerability": {"id": "GHSA-8cf7-32gw-wr33", "dataSource": "https://github.com/advisories/GHSA-8cf7-32gw-wr33", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "jsonwebtoken unrestricted key type could lead to legacy keys usage", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:N", "metrics": {"baseScore": 8.1, "exploitabilityScore": 2.9, "impactScore": 5.2}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-23539", "epss": 0.00058, "percentile": 0.18031, "date": "2025-08-23"}], "fix": {"versions": ["9.0.0"], "state": "fixed"}, "advisories": [], "risk": 0.04524}, "relatedVulnerabilities": [{"id": "CVE-2022-23539", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2022-23539", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/auth0/node-jsonwebtoken/commit/e1fa9dcc12054a8681db4e6373da1b30cf7016e3", "https://github.com/auth0/node-jsonwebtoken/security/advisories/GHSA-8cf7-32gw-wr33", "https://security.netapp.com/advisory/ntap-20240621-0007/", "https://github.com/auth0/node-jsonwebtoken/commit/e1fa9dcc12054a8681db4e6373da1b30cf7016e3", "https://github.com/auth0/node-jsonwebtoken/security/advisories/GHSA-8cf7-32gw-wr33", "https://security.netapp.com/advisory/ntap-20240621-0007/"], "description": "Versions `<=8.5.1` of `jsonwebtoken` library could be misconfigured so that legacy, insecure key types are used for signature verification. For example, DSA keys could be used with the RS256 algorithm. You are affected if you are using an algorithm and a key type other than a combination listed in the GitHub Security Advisory as unaffected. This issue has been fixed, please update to version 9.0.0. This version validates for asymmetric key type and algorithm combinations. Please refer to the above mentioned algorithm / key type combinations for the valid secure configuration. After updating to version 9.0.0, if you still intend to continue with signing or verifying tokens using invalid key type/algorithm value combinations, you’ll need to set the `allowInvalidAsymmetricKeyTypes` option  to `true` in the `sign()` and/or `verify()` functions.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:N", "metrics": {"baseScore": 8.1, "exploitabilityScore": 2.9, "impactScore": 5.2}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:H/A:N", "metrics": {"baseScore": 5.9, "exploitabilityScore": 1.7, "impactScore": 4.3}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-23539", "epss": 0.00058, "percentile": 0.18031, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "jsonwebtoken", "version": "8.5.1"}}, "found": {"vulnerabilityID": "GHSA-8cf7-32gw-wr33", "versionConstraint": "<=8.5.1 (unknown)"}, "fix": {"suggestedVersion": "9.0.0"}}], "artifact": {"id": "046b430aa2ecdcb0", "name": "jsonwebtoken", "version": "8.5.1", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:auth0:jsonwebtoken:8.5.1:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/jsonwebtoken@8.5.1", "upstreams": []}}, {"vulnerability": {"id": "GHSA-rhx6-c78j-4q9w", "dataSource": "https://github.com/advisories/GHSA-rhx6-c78j-4q9w", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "path-to-regexp contains a ReDoS", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-52798", "epss": 0.0006, "percentile": 0.18949, "date": "2025-08-23"}], "fix": {"versions": ["0.1.12"], "state": "fixed"}, "advisories": [], "risk": 0.045}, "relatedVulnerabilities": [{"id": "CVE-2024-52798", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2024-52798", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/pillarjs/path-to-regexp/commit/f01c26a013b1889f0c217c643964513acf17f6a4", "https://github.com/pillarjs/path-to-regexp/security/advisories/GHSA-rhx6-c78j-4q9w", "https://security.netapp.com/advisory/ntap-20250124-0002/"], "description": "path-to-regexp turns path strings into a regular expressions. In certain cases, path-to-regexp will output a regular expression that can be exploited to cause poor performance. The regular expression that is vulnerable to backtracking can be generated in the 0.1.x release of path-to-regexp. Upgrade to 0.1.12. This vulnerability exists because of an incomplete fix for CVE-2024-45296.", "cvss": [{"source": "<EMAIL>", "type": "Secondary", "version": "4.0", "vector": "CVSS:4.0/AV:N/AC:L/AT:N/PR:N/UI:N/VC:N/VI:N/VA:H/SC:N/SI:N/SA:N/E:P/CR:X/IR:X/AR:X/MAV:X/MAC:X/MAT:X/MPR:X/MUI:X/MVC:X/MVI:X/MVA:X/MSC:X/MSI:X/MSA:X/S:X/AU:X/R:X/V:X/RE:X/U:X", "metrics": {"baseScore": 7.7}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-52798", "epss": 0.0006, "percentile": 0.18949, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "path-to-regexp", "version": "0.1.7"}}, "found": {"vulnerabilityID": "GHSA-rhx6-c78j-4q9w", "versionConstraint": "<0.1.12 (unknown)"}, "fix": {"suggestedVersion": "0.1.12"}}], "artifact": {"id": "81361776d3c596db", "name": "path-to-regexp", "version": "0.1.7", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:path-to-regexp:path-to-regexp:0.1.7:*:*:*:*:*:*:*", "cpe:2.3:a:path-to-regexp:path_to_regexp:0.1.7:*:*:*:*:*:*:*", "cpe:2.3:a:path_to_regexp:path-to-regexp:0.1.7:*:*:*:*:*:*:*", "cpe:2.3:a:path_to_regexp:path_to_regexp:0.1.7:*:*:*:*:*:*:*", "cpe:2.3:a:path-to:path-to-regexp:0.1.7:*:*:*:*:*:*:*", "cpe:2.3:a:path-to:path_to_regexp:0.1.7:*:*:*:*:*:*:*", "cpe:2.3:a:path_to:path-to-regexp:0.1.7:*:*:*:*:*:*:*", "cpe:2.3:a:path_to:path_to_regexp:0.1.7:*:*:*:*:*:*:*", "cpe:2.3:a:path:path-to-regexp:0.1.7:*:*:*:*:*:*:*", "cpe:2.3:a:path:path_to_regexp:0.1.7:*:*:*:*:*:*:*"], "purl": "pkg:npm/path-to-regexp@0.1.7", "upstreams": []}}, {"vulnerability": {"id": "GHSA-6fx8-h7jm-663j", "dataSource": "https://github.com/advisories/GHSA-6fx8-h7jm-663j", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "parse-uri Regular expression Denial of Service (ReDoS)", "cvss": [], "epss": [{"cve": "CVE-2024-36751", "epss": 0.00081, "percentile": 0.24492, "date": "2025-08-23"}], "fix": {"versions": [], "state": "not-fixed"}, "advisories": [], "risk": 0.040499999999999994}, "relatedVulnerabilities": [{"id": "CVE-2024-36751", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2024-36751", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://gist.github.com/6en6ar/78168687da94e8aa2e0357f2456b0233", "https://github.com/Kikobeats/parse-uri/issues/14"], "description": "An issue in parse-uri v1.0.9 allows attackers to cause a Regular expression Denial of Service (ReDoS) via a crafted URL.", "cvss": [{"source": "134c704f-9b21-4f2e-91b3-4a467353bcc0", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 6.5, "exploitabilityScore": 2.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-36751", "epss": 0.00081, "percentile": 0.24492, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "parseuri", "version": "0.0.5"}}, "found": {"vulnerabilityID": "GHSA-6fx8-h7jm-663j", "versionConstraint": "<2.0.0 (unknown)"}}], "artifact": {"id": "47717323d9647050", "name": "parseuri", "version": "0.0.5", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:parseuri:parseuri:0.0.5:*:*:*:*:*:*:*"], "purl": "pkg:npm/parseuri@0.0.5", "upstreams": []}}, {"vulnerability": {"id": "GHSA-4rq4-32rv-6wp6", "dataSource": "https://github.com/advisories/GHSA-4rq4-32rv-6wp6", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Improper Privilege Management in shelljs", "cvss": [{"type": "Secondary", "version": "3.0", "vector": "CVSS:3.0/AV:L/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:H", "metrics": {"baseScore": 7.1, "exploitabilityScore": 1.9, "impactScore": 5.2}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-0144", "epss": 0.00052, "percentile": 0.15789, "date": "2025-08-23"}], "fix": {"versions": ["0.8.5"], "state": "fixed"}, "advisories": [], "risk": 0.037959999999999994}, "relatedVulnerabilities": [{"id": "CVE-2022-0144", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2022-0144", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/shelljs/shelljs/commit/d919d22dd6de385edaa9d90313075a77f74b338c", "https://huntr.dev/bounties/50996581-c08e-4eed-a90e-c0bac082679c", "https://github.com/shelljs/shelljs/commit/d919d22dd6de385edaa9d90313075a77f74b338c", "https://huntr.dev/bounties/50996581-c08e-4eed-a90e-c0bac082679c"], "description": "shelljs is vulnerable to Improper Privilege Management", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:H", "metrics": {"baseScore": 7.1, "exploitabilityScore": 1.9, "impactScore": 5.2}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:L/AC:L/Au:N/C:P/I:N/A:P", "metrics": {"baseScore": 3.6, "exploitabilityScore": 4, "impactScore": 5}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.0", "vector": "CVSS:3.0/AV:L/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:H", "metrics": {"baseScore": 7.1, "exploitabilityScore": 1.9, "impactScore": 5.2}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-0144", "epss": 0.00052, "percentile": 0.15789, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "<PERSON><PERSON>s", "version": "0.8.3"}}, "found": {"vulnerabilityID": "GHSA-4rq4-32rv-6wp6", "versionConstraint": "<0.8.5 (unknown)"}, "fix": {"suggestedVersion": "0.8.5"}}], "artifact": {"id": "c6fe094cc73a284c", "name": "<PERSON><PERSON>s", "version": "0.8.3", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["BSD-3-<PERSON><PERSON>"], "cpes": ["cpe:2.3:a:shelljs:shelljs:0.8.3:*:*:*:*:*:*:*"], "purl": "pkg:npm/shelljs@0.8.3", "upstreams": []}}, {"vulnerability": {"id": "GHSA-gxpj-cx7g-858c", "dataSource": "https://github.com/advisories/GHSA-gxpj-cx7g-858c", "namespace": "github:language:javascript", "severity": "Low", "urls": [], "description": "Regular Expression Denial of Service in debug", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:N/I:N/A:L", "metrics": {"baseScore": 3.7, "exploitabilityScore": 2.3, "impactScore": 1.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2017-16137", "epss": 0.00097, "percentile": 0.2775, "date": "2025-08-23"}], "fix": {"versions": ["4.3.1"], "state": "fixed"}, "advisories": [], "risk": 0.032494999999999996}, "relatedVulnerabilities": [{"id": "CVE-2017-16137", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2017-16137", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://github.com/visionmedia/debug/issues/501", "https://github.com/visionmedia/debug/pull/504", "https://lists.apache.org/thread.html/r8ba4c628fba7181af58817d452119481adce4ba92e889c643e4c7dd3%40%3Ccommits.netbeans.apache.org%3E", "https://lists.apache.org/thread.html/rb5ac16fad337d1f3bb7079549f97d8166d0ef3082629417c39f12d63%40%3Cnotifications.netbeans.apache.org%3E", "https://nodesecurity.io/advisories/534", "https://github.com/visionmedia/debug/issues/501", "https://github.com/visionmedia/debug/pull/504", "https://lists.apache.org/thread.html/r8ba4c628fba7181af58817d452119481adce4ba92e889c643e4c7dd3%40%3Ccommits.netbeans.apache.org%3E", "https://lists.apache.org/thread.html/rb5ac16fad337d1f3bb7079549f97d8166d0ef3082629417c39f12d63%40%3Cnotifications.netbeans.apache.org%3E", "https://nodesecurity.io/advisories/534"], "description": "The debug module is vulnerable to regular expression denial of service when untrusted user input is passed into the o formatter. It takes around 50k characters to block for 2 seconds making this a low severity issue.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.0", "vector": "CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:N/I:N/A:P", "metrics": {"baseScore": 5, "exploitabilityScore": 10, "impactScore": 2.9}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2017-16137", "epss": 0.00097, "percentile": 0.2775, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "debug", "version": "4.1.1"}}, "found": {"vulnerabilityID": "GHSA-gxpj-cx7g-858c", "versionConstraint": ">=4.0.0,<4.3.1 (unknown)"}, "fix": {"suggestedVersion": "4.3.1"}}], "artifact": {"id": "87bfc93ec5251a57", "name": "debug", "version": "4.1.1", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:debug_project:debug:4.1.1:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/debug@4.1.1", "upstreams": []}}, {"vulnerability": {"id": "GHSA-f982-mxwc-3mrx", "dataSource": "https://github.com/advisories/GHSA-f982-mxwc-3mrx", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "MySQL for Node.js Unsafe Options", "cvss": [{"type": "Secondary", "version": "3.0", "vector": "CVSS:3.0/AV:L/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N", "metrics": {"baseScore": 5.5, "exploitabilityScore": 1.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2019-14939", "epss": 0.00059, "percentile": 0.18359, "date": "2025-08-23"}], "fix": {"versions": ["2.18.0"], "state": "fixed"}, "advisories": [], "risk": 0.030975000000000003}, "relatedVulnerabilities": [{"id": "CVE-2019-14939", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2019-14939", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://github.com/mysqljs/mysql/issues/2257", "https://github.com/mysqljs/mysql/issues/2257"], "description": "An issue was discovered in the mysql (aka mysqljs) module 2.17.1 for Node.js. The LOAD DATA LOCAL INFILE option is open by default.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.0", "vector": "CVSS:3.0/AV:L/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N", "metrics": {"baseScore": 5.5, "exploitabilityScore": 1.9, "impactScore": 3.6}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:L/AC:L/Au:N/C:P/I:N/A:N", "metrics": {"baseScore": 2.1, "exploitabilityScore": 4, "impactScore": 2.9}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2019-14939", "epss": 0.00059, "percentile": 0.18359, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "mysql", "version": "2.17.1"}}, "found": {"vulnerabilityID": "GHSA-f982-mxwc-3mrx", "versionConstraint": "=2.17.1 (unknown)"}, "fix": {"suggestedVersion": "2.18.0"}}], "artifact": {"id": "0ee2757732bf16c3", "name": "mysql", "version": "2.17.1", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:mysql_project:mysql:2.17.1:*:*:*:*:node.js:*:*", "cpe:2.3:a:mysqljs:mysql:2.17.1:*:*:*:*:node.js:*:*", "cpe:2.3:a:mysql_project:mysql:2.17.1:*:*:*:*:*:*:*"], "purl": "pkg:npm/mysql@2.17.1", "upstreams": []}}, {"vulnerability": {"id": "GHSA-qgmg-gppg-76g5", "dataSource": "https://github.com/advisories/GHSA-qgmg-gppg-76g5", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "Inefficient Regular Expression Complexity in validator.js", "cvss": [{"type": "Secondary", "version": "3.0", "vector": "CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-3765", "epss": 0.00058, "percentile": 0.18299, "date": "2025-08-23"}], "fix": {"versions": ["13.7.0"], "state": "fixed"}, "advisories": [], "risk": 0.02987}, "relatedVulnerabilities": [{"id": "CVE-2021-3765", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2021-3765", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/validatorjs/validator.js/commit/496fc8b2a7f5997acaaec33cc44d0b8dba5fb5e1", "https://huntr.dev/bounties/c37e975c-21a3-4c5f-9b57-04d63b28cfc9", "https://github.com/validatorjs/validator.js/commit/496fc8b2a7f5997acaaec33cc44d0b8dba5fb5e1", "https://huntr.dev/bounties/c37e975c-21a3-4c5f-9b57-04d63b28cfc9"], "description": "validator.js is vulnerable to Inefficient Regular Expression Complexity", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:N/I:N/A:P", "metrics": {"baseScore": 5, "exploitabilityScore": 10, "impactScore": 2.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.0", "vector": "CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-3765", "epss": 0.00058, "percentile": 0.18299, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "validator", "version": "10.11.0"}}, "found": {"vulnerabilityID": "GHSA-qgmg-gppg-76g5", "versionConstraint": "<13.7.0 (unknown)"}, "fix": {"suggestedVersion": "13.7.0"}}], "artifact": {"id": "c84b484f2d0d03e0", "name": "validator", "version": "10.11.0", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:validator_project:validator:10.11.0:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/validator@10.11.0", "upstreams": []}}, {"vulnerability": {"id": "GHSA-m6fv-jmcg-4jfg", "dataSource": "https://github.com/advisories/GHSA-m6fv-jmcg-4jfg", "namespace": "github:language:javascript", "severity": "Low", "urls": [], "description": "send vulnerable to template injection that can lead to XSS", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:L/I:L/A:L", "metrics": {"baseScore": 5, "exploitabilityScore": 1.7, "impactScore": 3.4}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-43799", "epss": 0.00063, "percentile": 0.19749, "date": "2025-08-23"}], "fix": {"versions": ["0.19.0"], "state": "fixed"}, "advisories": [], "risk": 0.0252}, "relatedVulnerabilities": [{"id": "CVE-2024-43799", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2024-43799", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://github.com/pillarjs/send/commit/ae4f2989491b392ae2ef3b0015a019770ae65d35", "https://github.com/pillarjs/send/security/advisories/GHSA-m6fv-jmcg-4jfg"], "description": "Send is a library for streaming files from the file system as a http response. Send passes untrusted user input to SendStream.redirect() which executes untrusted code. This issue is patched in send 0.19.0.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:C/C:L/I:L/A:N", "metrics": {"baseScore": 4.7, "exploitabilityScore": 1.7, "impactScore": 2.8}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:L/I:L/A:L", "metrics": {"baseScore": 5, "exploitabilityScore": 1.7, "impactScore": 3.4}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-43799", "epss": 0.00063, "percentile": 0.19749, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "send", "version": "0.16.2"}}, "found": {"vulnerabilityID": "GHSA-m6fv-jmcg-4jfg", "versionConstraint": "<0.19.0 (unknown)"}, "fix": {"suggestedVersion": "0.19.0"}}], "artifact": {"id": "2367ddf89de81620", "name": "send", "version": "0.16.2", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:send_project:send:0.16.2:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/send@0.16.2", "upstreams": []}}, {"vulnerability": {"id": "GHSA-rv95-896h-c2vc", "dataSource": "https://github.com/advisories/GHSA-rv95-896h-c2vc", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "Express.js Open Redirect in malformed URLs", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N", "metrics": {"baseScore": 6.1, "exploitabilityScore": 2.9, "impactScore": 2.8}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-29041", "epss": 0.00043, "percentile": 0.11943, "date": "2025-08-23"}], "fix": {"versions": ["4.19.2"], "state": "fixed"}, "advisories": [], "risk": 0.023864999999999997}, "relatedVulnerabilities": [{"id": "CVE-2024-29041", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2024-29041", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://expressjs.com/en/4x/api.html#res.location", "https://github.com/expressjs/express/commit/0867302ddbde0e9463d0564fea5861feb708c2dd", "https://github.com/expressjs/express/commit/0b746953c4bd8e377123527db11f9cd866e39f94", "https://github.com/expressjs/express/pull/5539", "https://github.com/expressjs/express/security/advisories/GHSA-rv95-896h-c2vc", "https://github.com/koajs/koa/issues/1800", "https://expressjs.com/en/4x/api.html#res.location", "https://github.com/expressjs/express/commit/0867302ddbde0e9463d0564fea5861feb708c2dd", "https://github.com/expressjs/express/commit/0b746953c4bd8e377123527db11f9cd866e39f94", "https://github.com/expressjs/express/pull/5539", "https://github.com/expressjs/express/security/advisories/GHSA-rv95-896h-c2vc", "https://github.com/koajs/koa/issues/1800"], "description": "Express.js minimalist web framework for node. Versions of Express.js prior to 4.19.0 and all pre-release alpha and beta versions of 5.0 are affected by an open redirect vulnerability using malformed URLs. When a user of Express performs a redirect using a user-provided URL Express performs an encode [using `encodeurl`](https://github.com/pillarjs/encodeurl) on the contents before passing it to the `location` header. This can cause malformed URLs to be evaluated in unexpected ways by common redirect allow list implementations in Express applications, leading to an Open Redirect via bypass of a properly implemented allow list. The main method impacted is `res.location()` but this is also called from within `res.redirect()`. The vulnerability is fixed in 4.19.2 and 5.0.0-beta.3.", "cvss": [{"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N", "metrics": {"baseScore": 6.1, "exploitabilityScore": 2.9, "impactScore": 2.8}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-29041", "epss": 0.00043, "percentile": 0.11943, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "express", "version": "4.16.4"}}, "found": {"vulnerabilityID": "GHSA-rv95-896h-c2vc", "versionConstraint": "<4.19.2 (unknown)"}, "fix": {"suggestedVersion": "4.19.2"}}], "artifact": {"id": "7f55d687ae69c250", "name": "express", "version": "4.16.4", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:openjsf:express:4.16.4:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/express@4.16.4", "upstreams": []}}, {"vulnerability": {"id": "GHSA-qw6h-vgh9-j6wx", "dataSource": "https://github.com/advisories/GHSA-qw6h-vgh9-j6wx", "namespace": "github:language:javascript", "severity": "Low", "urls": [], "description": "express vulnerable to XSS via response.redirect()", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:L/I:L/A:L", "metrics": {"baseScore": 5, "exploitabilityScore": 1.7, "impactScore": 3.4}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-43796", "epss": 0.00058, "percentile": 0.18056, "date": "2025-08-23"}], "fix": {"versions": ["4.20.0"], "state": "fixed"}, "advisories": [], "risk": 0.0232}, "relatedVulnerabilities": [{"id": "CVE-2024-43796", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2024-43796", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://github.com/expressjs/express/commit/54271f69b511fea198471e6ff3400ab805d6b553", "https://github.com/expressjs/express/security/advisories/GHSA-qw6h-vgh9-j6wx"], "description": "Express.js minimalist web framework for node. In express < 4.20.0, passing untrusted user input - even after sanitizing it - to response.redirect() may execute untrusted code. This issue is patched in express 4.20.0.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:C/C:L/I:L/A:N", "metrics": {"baseScore": 4.7, "exploitabilityScore": 1.7, "impactScore": 2.8}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:L/I:L/A:L", "metrics": {"baseScore": 5, "exploitabilityScore": 1.7, "impactScore": 3.4}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-43796", "epss": 0.00058, "percentile": 0.18056, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "express", "version": "4.16.4"}}, "found": {"vulnerabilityID": "GHSA-qw6h-vgh9-j6wx", "versionConstraint": "<4.20.0 (unknown)"}, "fix": {"suggestedVersion": "4.20.0"}}], "artifact": {"id": "7f55d687ae69c250", "name": "express", "version": "4.16.4", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:openjsf:express:4.16.4:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/express@4.16.4", "upstreams": []}}, {"vulnerability": {"id": "GHSA-2r2c-g63r-vccr", "dataSource": "https://github.com/advisories/GHSA-2r2c-g63r-vccr", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "Improper Verification of Cryptographic Signature in `node-forge`", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-24773", "epss": 0.00045, "percentile": 0.12756, "date": "2025-08-23"}], "fix": {"versions": ["1.3.0"], "state": "fixed"}, "advisories": [], "risk": 0.023175}, "relatedVulnerabilities": [{"id": "CVE-2022-24773", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2022-24773", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://github.com/digitalbazaar/forge/commit/3f0b49a0573ef1bb7af7f5673c0cfebf00424df1", "https://github.com/digitalbazaar/forge/commit/bb822c02df0b61211836472e29b9790cc541cdb2", "https://github.com/digitalbazaar/forge/security/advisories/GHSA-2r2c-g63r-vccr", "https://github.com/digitalbazaar/forge/commit/3f0b49a0573ef1bb7af7f5673c0cfebf00424df1", "https://github.com/digitalbazaar/forge/commit/bb822c02df0b61211836472e29b9790cc541cdb2", "https://github.com/digitalbazaar/forge/security/advisories/GHSA-2r2c-g63r-vccr"], "description": "Forge (also called `node-forge`) is a native implementation of Transport Layer Security in JavaScript. Prior to version 1.3.0, RSA PKCS#1 v1.5 signature verification code does not properly check `DigestInfo` for a proper ASN.1 structure. This can lead to successful verification with signatures that contain invalid structures but a valid digest. The issue has been addressed in `node-forge` version 1.3.0. There are currently no known workarounds.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:N/AC:L/Au:N/C:N/I:P/A:N", "metrics": {"baseScore": 5, "exploitabilityScore": 10, "impactScore": 2.9}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N", "metrics": {"baseScore": 5.3, "exploitabilityScore": 3.9, "impactScore": 1.5}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-24773", "epss": 0.00045, "percentile": 0.12756, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "node-forge", "version": "0.8.5"}}, "found": {"vulnerabilityID": "GHSA-2r2c-g63r-vccr", "versionConstraint": "<1.3.0 (unknown)"}, "fix": {"suggestedVersion": "1.3.0"}}], "artifact": {"id": "087c9d217c8255bc", "name": "node-forge", "version": "0.8.5", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["(BSD-3-<PERSON><PERSON> OR GPL-2.0)"], "cpes": ["cpe:2.3:a:digitalbazaar:forge:0.8.5:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/node-forge@0.8.5", "upstreams": []}}, {"vulnerability": {"id": "GHSA-9r2w-394v-53qc", "dataSource": "https://github.com/advisories/GHSA-9r2w-394v-53qc", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Arbitrary File Creation/Overwrite via insufficient symlink protection due to directory cache poisoning using symbolic links", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:N", "metrics": {"baseScore": 8.2, "exploitabilityScore": 1.9, "impactScore": 5.8}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-37701", "epss": 0.00029, "percentile": 0.064, "date": "2025-08-23"}], "fix": {"versions": ["4.4.16"], "state": "fixed"}, "advisories": [], "risk": 0.022764999999999997}, "relatedVulnerabilities": [{"id": "CVE-2021-37701", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2021-37701", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://cert-portal.siemens.com/productcert/pdf/ssa-389290.pdf", "https://github.com/npm/node-tar/security/advisories/GHSA-9r2w-394v-53qc", "https://lists.debian.org/debian-lts-announce/2022/12/msg00023.html", "https://www.debian.org/security/2021/dsa-5008", "https://www.npmjs.com/package/tar", "https://www.oracle.com/security-alerts/cpuoct2021.html", "https://cert-portal.siemens.com/productcert/pdf/ssa-389290.pdf", "https://github.com/npm/node-tar/security/advisories/GHSA-9r2w-394v-53qc", "https://lists.debian.org/debian-lts-announce/2022/12/msg00023.html", "https://www.debian.org/security/2021/dsa-5008", "https://www.npmjs.com/package/tar", "https://www.oracle.com/security-alerts/cpuoct2021.html"], "description": "The npm package \"tar\" (aka node-tar) before versions 4.4.16, 5.0.8, and 6.1.7 has an arbitrary file creation/overwrite and arbitrary code execution vulnerability. node-tar aims to guarantee that any file whose location would be modified by a symbolic link is not extracted. This is, in part, achieved by ensuring that extracted directories are not symlinks. Additionally, in order to prevent unnecessary stat calls to determine whether a given path is a directory, paths are cached when directories are created. This logic was insufficient when extracting tar files that contained both a directory and a symlink with the same name as the directory, where the symlink and directory names in the archive entry used backslashes as a path separator on posix systems. The cache checking logic used both `\\` and `/` characters as path separators, however `\\` is a valid filename character on posix systems. By first creating a directory, and then replacing that directory with a symlink, it was thus possible to bypass node-tar symlink checks on directories, essentially allowing an untrusted tar file to symlink into an arbitrary location and subsequently extracting arbitrary files into that location, thus allowing arbitrary file creation and overwrite. Additionally, a similar confusion could arise on case-insensitive filesystems. If a tar archive contained a directory at `FOO`, followed by a symbolic link named `foo`, then on case-insensitive file systems, the creation of the symbolic link would remove the directory from the filesystem, but _not_ from the internal directory cache, as it would not be treated as a cache hit. A subsequent file entry within the `FOO` directory would then be placed in the target of the symbolic link, thinking that the directory had already been created. These issues were addressed in releases 4.4.16, 5.0.8 and 6.1.7. The v3 branch of node-tar has been deprecated and did not receive patches for these issues. If you are still using a v3 release we recommend you update to a more recent version of node-tar. If this is not possible, a workaround is available in the referenced GHSA-9r2w-394v-53qc.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:H", "metrics": {"baseScore": 8.6, "exploitabilityScore": 1.9, "impactScore": 6.1}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:L/AC:M/Au:N/C:P/I:P/A:P", "metrics": {"baseScore": 4.4, "exploitabilityScore": 3.4, "impactScore": 6.5}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:N", "metrics": {"baseScore": 8.2, "exploitabilityScore": 1.9, "impactScore": 5.8}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-37701", "epss": 0.00029, "percentile": 0.064, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "tar", "version": "4.4.8"}}, "found": {"vulnerabilityID": "GHSA-9r2w-394v-53qc", "versionConstraint": ">=3.0.0,<4.4.16 (unknown)"}, "fix": {"suggestedVersion": "4.4.16"}}], "artifact": {"id": "6cfd37cbdc418b39", "name": "tar", "version": "4.4.8", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["ISC"], "cpes": ["cpe:2.3:a:tar_project:tar:4.4.8:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/tar@4.4.8", "upstreams": []}}, {"vulnerability": {"id": "GHSA-cm22-4g7w-348p", "dataSource": "https://github.com/advisories/GHSA-cm22-4g7w-348p", "namespace": "github:language:javascript", "severity": "Low", "urls": [], "description": "serve-static vulnerable to template injection that can lead to XSS", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:L/I:L/A:L", "metrics": {"baseScore": 5, "exploitabilityScore": 1.7, "impactScore": 3.4}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-43800", "epss": 0.00056, "percentile": 0.17369, "date": "2025-08-23"}], "fix": {"versions": ["1.16.0"], "state": "fixed"}, "advisories": [], "risk": 0.0224}, "relatedVulnerabilities": [{"id": "CVE-2024-43800", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2024-43800", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://github.com/expressjs/serve-static/commit/0c11fad159898cdc69fd9ab63269b72468ecaf6b", "https://github.com/expressjs/serve-static/commit/ce730896fddce1588111d9ef6fdf20896de5c6fa", "https://github.com/expressjs/serve-static/security/advisories/GHSA-cm22-4g7w-348p"], "description": "serve-static serves static files. serve-static passes untrusted user input - even after sanitizing it - to redirect() may execute untrusted code. This issue is patched in serve-static 1.16.0.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:C/C:L/I:L/A:N", "metrics": {"baseScore": 4.7, "exploitabilityScore": 1.7, "impactScore": 2.8}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:L/I:L/A:L", "metrics": {"baseScore": 5, "exploitabilityScore": 1.7, "impactScore": 3.4}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-43800", "epss": 0.00056, "percentile": 0.17369, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "serve-static", "version": "1.13.2"}}, "found": {"vulnerabilityID": "GHSA-cm22-4g7w-348p", "versionConstraint": "<1.16.0 (unknown)"}, "fix": {"suggestedVersion": "1.16.0"}}], "artifact": {"id": "4440e843563cdc0e", "name": "serve-static", "version": "1.13.2", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:serve-static:serve-static:1.13.2:*:*:*:*:*:*:*", "cpe:2.3:a:serve-static:serve_static:1.13.2:*:*:*:*:*:*:*", "cpe:2.3:a:serve_static:serve-static:1.13.2:*:*:*:*:*:*:*", "cpe:2.3:a:serve_static:serve_static:1.13.2:*:*:*:*:*:*:*", "cpe:2.3:a:serve:serve-static:1.13.2:*:*:*:*:*:*:*", "cpe:2.3:a:serve:serve_static:1.13.2:*:*:*:*:*:*:*"], "purl": "pkg:npm/serve-static@1.13.2", "upstreams": []}}, {"vulnerability": {"id": "GHSA-fjxv-7rqg-78g4", "dataSource": "https://github.com/advisories/GHSA-fjxv-7rqg-78g4", "namespace": "github:language:javascript", "severity": "Critical", "urls": [], "description": "form-data uses unsafe random function in form-data for choosing boundary", "cvss": [], "epss": [{"cve": "CVE-2025-7783", "epss": 0.00024, "percentile": 0.04706, "date": "2025-08-23"}], "fix": {"versions": ["2.5.4"], "state": "fixed"}, "advisories": [], "risk": 0.0216}, "relatedVulnerabilities": [{"id": "CVE-2025-7783", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2025-7783", "namespace": "nvd:cpe", "severity": "Critical", "urls": ["https://github.com/form-data/form-data/commit/3d1723080e6577a66f17f163ecd345a21d8d0fd0", "https://github.com/form-data/form-data/security/advisories/GHSA-fjxv-7rqg-78g4", "https://github.com/form-data/form-data/security/advisories/GHSA-fjxv-7rqg-78g4"], "description": "Use of Insufficiently Random Values vulnerability in form-data allows HTTP Parameter Pollution (HPP). This vulnerability is associated with program files lib/form_data.Js.\n\nThis issue affects form-data: < 2.5.4, 3.0.0 - 3.0.3, 4.0.0 - 4.0.3.", "cvss": [{"source": "7ffcee3d-2c14-4c3e-b844-86c6a321a158", "type": "Secondary", "version": "4.0", "vector": "CVSS:4.0/AV:N/AC:H/AT:N/PR:N/UI:N/VC:H/VI:H/VA:N/SC:H/SI:H/SA:N/E:X/CR:X/IR:X/AR:X/MAV:X/MAC:X/MAT:X/MPR:X/MUI:X/MVC:X/MVI:X/MVA:X/MSC:X/MSI:X/MSA:X/S:X/AU:X/R:X/V:X/RE:X/U:X", "metrics": {"baseScore": 9.4}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2025-7783", "epss": 0.00024, "percentile": 0.04706, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "form-data", "version": "2.3.3"}}, "found": {"vulnerabilityID": "GHSA-fjxv-7rqg-78g4", "versionConstraint": "<2.5.4 (unknown)"}, "fix": {"suggestedVersion": "2.5.4"}}], "artifact": {"id": "473aca997706ab30", "name": "form-data", "version": "2.3.3", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:form-data:form-data:2.3.3:*:*:*:*:*:*:*", "cpe:2.3:a:form-data:form_data:2.3.3:*:*:*:*:*:*:*", "cpe:2.3:a:form_data:form-data:2.3.3:*:*:*:*:*:*:*", "cpe:2.3:a:form_data:form_data:2.3.3:*:*:*:*:*:*:*", "cpe:2.3:a:form:form-data:2.3.3:*:*:*:*:*:*:*", "cpe:2.3:a:form:form_data:2.3.3:*:*:*:*:*:*:*"], "purl": "pkg:npm/form-data@2.3.3", "upstreams": []}}, {"vulnerability": {"id": "GHSA-hjrf-2m68-5959", "dataSource": "https://github.com/advisories/GHSA-hjrf-2m68-5959", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "jsonwebtoken's insecure implementation of key retrieval function could lead to Forgeable Public/Private Tokens from RSA to HMAC", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:L/A:L", "metrics": {"baseScore": 5, "exploitabilityScore": 1.7, "impactScore": 3.4}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-23541", "epss": 0.00043, "percentile": 0.11912, "date": "2025-08-23"}], "fix": {"versions": ["9.0.0"], "state": "fixed"}, "advisories": [], "risk": 0.0215}, "relatedVulnerabilities": [{"id": "CVE-2022-23541", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2022-23541", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://github.com/auth0/node-jsonwebtoken/commit/e1fa9dcc12054a8681db4e6373da1b30cf7016e3", "https://github.com/auth0/node-jsonwebtoken/releases/tag/v9.0.0", "https://github.com/auth0/node-jsonwebtoken/security/advisories/GHSA-hjrf-2m68-5959", "https://security.netapp.com/advisory/ntap-20240621-0007/", "https://github.com/auth0/node-jsonwebtoken/commit/e1fa9dcc12054a8681db4e6373da1b30cf7016e3", "https://github.com/auth0/node-jsonwebtoken/releases/tag/v9.0.0", "https://github.com/auth0/node-jsonwebtoken/security/advisories/GHSA-hjrf-2m68-5959", "https://security.netapp.com/advisory/ntap-20240621-0007/"], "description": "jsonwebtoken is an implementation of JSON Web Tokens. Versions `<= 8.5.1` of `jsonwebtoken` library can be misconfigured so that passing a poorly implemented key retrieval function referring to the `secretOrPublicKey` argument from the readme link will result in incorrect verification of tokens. There is a possibility of using a different algorithm and key combination in verification, other than the one that was used to sign the tokens. Specifically, tokens signed with an asymmetric public key could be verified with a symmetric HS256 algorithm. This can lead to successful validation of  forged tokens. If your application is supporting usage of both symmetric key and asymmetric key in jwt.verify() implementation with the same key retrieval function. This issue has been patched, please update to version 9.0.0.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:L", "metrics": {"baseScore": 6.3, "exploitabilityScore": 2.9, "impactScore": 3.4}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:L/A:L", "metrics": {"baseScore": 5, "exploitabilityScore": 1.7, "impactScore": 3.4}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-23541", "epss": 0.00043, "percentile": 0.11912, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "jsonwebtoken", "version": "8.5.1"}}, "found": {"vulnerabilityID": "GHSA-hjrf-2m68-5959", "versionConstraint": "<=8.5.1 (unknown)"}, "fix": {"suggestedVersion": "9.0.0"}}], "artifact": {"id": "046b430aa2ecdcb0", "name": "jsonwebtoken", "version": "8.5.1", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:auth0:jsonwebtoken:8.5.1:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/jsonwebtoken@8.5.1", "upstreams": []}}, {"vulnerability": {"id": "GHSA-pxg6-pf52-xh8x", "dataSource": "https://github.com/advisories/GHSA-pxg6-pf52-xh8x", "namespace": "github:language:javascript", "severity": "Low", "urls": [], "description": "cookie accepts cookie name, path, and domain with out of bounds characters", "cvss": [], "epss": [{"cve": "CVE-2024-47764", "epss": 0.00069, "percentile": 0.21476, "date": "2025-08-23"}], "fix": {"versions": ["0.7.0"], "state": "fixed"}, "advisories": [], "risk": 0.0207}, "relatedVulnerabilities": [{"id": "CVE-2024-47764", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2024-47764", "namespace": "nvd:cpe", "severity": "Medium", "urls": ["https://github.com/jshttp/cookie/commit/e10042845354fea83bd8f34af72475eed1dadf5c", "https://github.com/jshttp/cookie/pull/167", "https://github.com/jshttp/cookie/security/advisories/GHSA-pxg6-pf52-xh8x"], "description": "cookie is a basic HTTP cookie parser and serializer for HTTP servers. The cookie name could be used to set other fields of the cookie, resulting in an unexpected cookie value. A similar escape can be used for path and domain, which could be abused to alter other fields of the cookie. Upgrade to 0.7.0, which updates the validation for name, path, and domain.", "cvss": [{"source": "<EMAIL>", "type": "Secondary", "version": "4.0", "vector": "CVSS:4.0/AV:N/AC:L/AT:N/PR:N/UI:N/VC:N/VI:L/VA:N/SC:N/SI:N/SA:N/E:X/CR:X/IR:X/AR:X/MAV:X/MAC:X/MAT:X/MPR:X/MUI:X/MVC:X/MVI:X/MVA:X/MSC:X/MSI:X/MSA:X/S:X/AU:X/R:X/V:X/RE:X/U:X", "metrics": {"baseScore": 6.9}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2024-47764", "epss": 0.00069, "percentile": 0.21476, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "cookie", "version": "0.3.1"}}, "found": {"vulnerabilityID": "GHSA-pxg6-pf52-xh8x", "versionConstraint": "<0.7.0 (unknown)"}, "fix": {"suggestedVersion": "0.7.0"}}], "artifact": {"id": "cdacbc7b4c394734", "name": "cookie", "version": "0.3.1", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:cookie:cookie:0.3.1:*:*:*:*:*:*:*"], "purl": "pkg:npm/cookie@0.3.1", "upstreams": []}}, {"vulnerability": {"id": "GHSA-qq89-hq3f-393p", "dataSource": "https://github.com/advisories/GHSA-qq89-hq3f-393p", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Arbitrary File Creation/Overwrite via insufficient symlink protection due to directory cache poisoning using symbolic links", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:N", "metrics": {"baseScore": 8.2, "exploitabilityScore": 1.9, "impactScore": 5.8}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-37712", "epss": 0.00023, "percentile": 0.0434, "date": "2025-08-23"}], "fix": {"versions": ["4.4.18"], "state": "fixed"}, "advisories": [], "risk": 0.018054999999999998}, "relatedVulnerabilities": [{"id": "CVE-2021-37712", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2021-37712", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://cert-portal.siemens.com/productcert/pdf/ssa-389290.pdf", "https://github.com/npm/node-tar/security/advisories/GHSA-qq89-hq3f-393p", "https://lists.debian.org/debian-lts-announce/2022/12/msg00023.html", "https://www.debian.org/security/2021/dsa-5008", "https://www.npmjs.com/package/tar", "https://www.oracle.com/security-alerts/cpuoct2021.html", "https://cert-portal.siemens.com/productcert/pdf/ssa-389290.pdf", "https://github.com/npm/node-tar/security/advisories/GHSA-qq89-hq3f-393p", "https://lists.debian.org/debian-lts-announce/2022/12/msg00023.html", "https://www.debian.org/security/2021/dsa-5008", "https://www.npmjs.com/package/tar", "https://www.oracle.com/security-alerts/cpuoct2021.html"], "description": "The npm package \"tar\" (aka node-tar) before versions 4.4.18, 5.0.10, and 6.1.9 has an arbitrary file creation/overwrite and arbitrary code execution vulnerability. node-tar aims to guarantee that any file whose location would be modified by a symbolic link is not extracted. This is, in part, achieved by ensuring that extracted directories are not symlinks. Additionally, in order to prevent unnecessary stat calls to determine whether a given path is a directory, paths are cached when directories are created. This logic was insufficient when extracting tar files that contained both a directory and a symlink with names containing unicode values that normalized to the same value. Additionally, on Windows systems, long path portions would resolve to the same file system entities as their 8.3 \"short path\" counterparts. A specially crafted tar archive could thus include a directory with one form of the path, followed by a symbolic link with a different string that resolves to the same file system entity, followed by a file using the first form. By first creating a directory, and then replacing that directory with a symlink that had a different apparent name that resolved to the same entry in the filesystem, it was thus possible to bypass node-tar symlink checks on directories, essentially allowing an untrusted tar file to symlink into an arbitrary location and subsequently extracting arbitrary files into that location, thus allowing arbitrary file creation and overwrite. These issues were addressed in releases 4.4.18, 5.0.10 and 6.1.9. The v3 branch of node-tar has been deprecated and did not receive patches for these issues. If you are still using a v3 release we recommend you update to a more recent version of node-tar. If this is not possible, a workaround is available in the referenced GHSA-qq89-hq3f-393p.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:H", "metrics": {"baseScore": 8.6, "exploitabilityScore": 1.9, "impactScore": 6.1}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Primary", "version": "2.0", "vector": "AV:L/AC:M/Au:N/C:P/I:P/A:P", "metrics": {"baseScore": 4.4, "exploitabilityScore": 3.4, "impactScore": 6.5}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:N", "metrics": {"baseScore": 8.2, "exploitabilityScore": 1.9, "impactScore": 5.8}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2021-37712", "epss": 0.00023, "percentile": 0.0434, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "tar", "version": "4.4.8"}}, "found": {"vulnerabilityID": "GHSA-qq89-hq3f-393p", "versionConstraint": ">=3.0.0,<4.4.18 (unknown)"}, "fix": {"suggestedVersion": "4.4.18"}}], "artifact": {"id": "6cfd37cbdc418b39", "name": "tar", "version": "4.4.8", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["ISC"], "cpes": ["cpe:2.3:a:tar_project:tar:4.4.8:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/tar@4.4.8", "upstreams": []}}, {"vulnerability": {"id": "GHSA-44fp-w29j-9vj5", "dataSource": "https://github.com/advisories/GHSA-44fp-w29j-9vj5", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Multer vulnerable to Denial of Service via memory leaks from unclosed streams", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2025-47935", "epss": 0.00018, "percentile": 0.02911, "date": "2025-08-23"}], "fix": {"versions": ["2.0.0"], "state": "fixed"}, "advisories": [], "risk": 0.0135}, "relatedVulnerabilities": [{"id": "CVE-2025-47935", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2025-47935", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/expressjs/multer/commit/2c8505f207d923dd8de13a9f93a4563e59933665", "https://github.com/expressjs/multer/pull/1120", "https://github.com/expressjs/multer/security/advisories/GHSA-44fp-w29j-9vj5"], "description": "Multer is a node.js middleware for handling `multipart/form-data`. Versions prior to 2.0.0 are vulnerable to a resource exhaustion and memory leak issue due to improper stream handling. When the HTTP request stream emits an error, the internal `busboy` stream is not closed, violating Node.js stream safety guidance. This leads to unclosed streams accumulating over time, consuming memory and file descriptors. Under sustained or repeated failure conditions, this can result in denial of service, requiring manual server restarts to recover. All users of Multer handling file uploads are potentially impacted. Users should upgrade to 2.0.0 to receive a patch. No known workarounds are available.", "cvss": [{"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H", "metrics": {"baseScore": 7.5, "exploitabilityScore": 3.9, "impactScore": 3.6}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2025-47935", "epss": 0.00018, "percentile": 0.02911, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "multer", "version": "1.4.2"}}, "found": {"vulnerabilityID": "GHSA-44fp-w29j-9vj5", "versionConstraint": "<2.0.0 (unknown)"}, "fix": {"suggestedVersion": "2.0.0"}}], "artifact": {"id": "ba75620ae1968669", "name": "multer", "version": "1.4.2", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:multer:multer:1.4.2:*:*:*:*:*:*:*"], "purl": "pkg:npm/multer@1.4.2", "upstreams": []}}, {"vulnerability": {"id": "GHSA-qwph-4952-7xr6", "dataSource": "https://github.com/advisories/GHSA-qwph-4952-7xr6", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "jsonwebtoken vulnerable to signature validation bypass due to insecure default algorithm in jwt.verify()", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:H/A:L", "metrics": {"baseScore": 6.4, "exploitabilityScore": 1.7, "impactScore": 4.8}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-23540", "epss": 0.00016, "percentile": 0.0227, "date": "2025-08-23"}], "fix": {"versions": ["9.0.0"], "state": "fixed"}, "advisories": [], "risk": 0.009120000000000001}, "relatedVulnerabilities": [{"id": "CVE-2022-23540", "dataSource": "https://nvd.nist.gov/vuln/detail/CVE-2022-23540", "namespace": "nvd:cpe", "severity": "High", "urls": ["https://github.com/auth0/node-jsonwebtoken/commit/e1fa9dcc12054a8681db4e6373da1b30cf7016e3", "https://github.com/auth0/node-jsonwebtoken/security/advisories/GHSA-qwph-4952-7xr6", "https://security.netapp.com/advisory/ntap-20240621-0007/", "https://github.com/auth0/node-jsonwebtoken/commit/e1fa9dcc12054a8681db4e6373da1b30cf7016e3", "https://github.com/auth0/node-jsonwebtoken/security/advisories/GHSA-qwph-4952-7xr6", "https://security.netapp.com/advisory/ntap-20240621-0007/"], "description": "In versions `<=8.5.1` of `jsonwebtoken` library, lack of algorithm definition in the `jwt.verify()` function can lead to signature validation bypass due to defaulting to the `none` algorithm for signature verification. Users are affected if you do not specify algorithms in the `jwt.verify()` function. This issue has been fixed, please update to version 9.0.0 which removes the default support for the none algorithm in the `jwt.verify()` method. There will be no impact, if you update to version 9.0.0 and you don’t need to allow for the `none` algorithm. If you need 'none' algorithm, you have to explicitly specify that in `jwt.verify()` options.", "cvss": [{"source": "<EMAIL>", "type": "Primary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:H/A:L", "metrics": {"baseScore": 7.6, "exploitabilityScore": 2.9, "impactScore": 4.8}, "vendorMetadata": {}}, {"source": "<EMAIL>", "type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:H/A:L", "metrics": {"baseScore": 6.4, "exploitabilityScore": 1.7, "impactScore": 4.8}, "vendorMetadata": {}}], "epss": [{"cve": "CVE-2022-23540", "epss": 0.00016, "percentile": 0.0227, "date": "2025-08-23"}]}], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "jsonwebtoken", "version": "8.5.1"}}, "found": {"vulnerabilityID": "GHSA-qwph-4952-7xr6", "versionConstraint": "<9.0.0 (unknown)"}, "fix": {"suggestedVersion": "9.0.0"}}], "artifact": {"id": "046b430aa2ecdcb0", "name": "jsonwebtoken", "version": "8.5.1", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:auth0:jsonwebtoken:8.5.1:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/jsonwebtoken@8.5.1", "upstreams": []}}, {"vulnerability": {"id": "GHSA-2cf5-4w76-r9qv", "dataSource": "https://github.com/advisories/GHSA-2cf5-4w76-r9qv", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Arbitrary Code Execution in handlebars", "cvss": [{"type": "Secondary", "version": "3.1", "vector": "CVSS:3.1/AV:L/AC:L/PR:L/UI:R/S:C/C:L/I:H/A:L", "metrics": {"baseScore": 7.3, "exploitabilityScore": 1.5, "impactScore": 5.3}, "vendorMetadata": {}}], "fix": {"versions": ["4.5.2"], "state": "fixed"}, "advisories": [], "risk": 0}, "relatedVulnerabilities": [], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "handlebars", "version": "4.1.2"}}, "found": {"vulnerabilityID": "GHSA-2cf5-4w76-r9qv", "versionConstraint": ">=4.0.0,<4.5.2 (unknown)"}, "fix": {"suggestedVersion": "4.5.2"}}], "artifact": {"id": "d777ff7ccae5fee5", "name": "handlebars", "version": "4.1.2", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:handlebars.js_project:handlebars.js:4.1.2:*:*:*:*:node.js:*:*", "cpe:2.3:a:handlebarsjs:handlebars:4.1.2:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/handlebars@4.1.2", "upstreams": []}}, {"vulnerability": {"id": "GHSA-g9r4-xpmj-mj65", "dataSource": "https://github.com/advisories/GHSA-g9r4-xpmj-mj65", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Prototype Pollution in handlebars", "cvss": [], "fix": {"versions": ["4.5.3"], "state": "fixed"}, "advisories": [], "risk": 0}, "relatedVulnerabilities": [], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "handlebars", "version": "4.1.2"}}, "found": {"vulnerabilityID": "GHSA-g9r4-xpmj-mj65", "versionConstraint": ">=4.0.0,<4.5.3 (unknown)"}, "fix": {"suggestedVersion": "4.5.3"}}], "artifact": {"id": "d777ff7ccae5fee5", "name": "handlebars", "version": "4.1.2", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:handlebars.js_project:handlebars.js:4.1.2:*:*:*:*:node.js:*:*", "cpe:2.3:a:handlebarsjs:handlebars:4.1.2:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/handlebars@4.1.2", "upstreams": []}}, {"vulnerability": {"id": "GHSA-q2c6-c6pm-g3gh", "dataSource": "https://github.com/advisories/GHSA-q2c6-c6pm-g3gh", "namespace": "github:language:javascript", "severity": "High", "urls": [], "description": "Arbitrary Code Execution in handlebars", "cvss": [], "fix": {"versions": ["4.5.3"], "state": "fixed"}, "advisories": [], "risk": 0}, "relatedVulnerabilities": [], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "handlebars", "version": "4.1.2"}}, "found": {"vulnerabilityID": "GHSA-q2c6-c6pm-g3gh", "versionConstraint": ">=4.0.0,<4.5.3 (unknown)"}, "fix": {"suggestedVersion": "4.5.3"}}], "artifact": {"id": "d777ff7ccae5fee5", "name": "handlebars", "version": "4.1.2", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:handlebars.js_project:handlebars.js:4.1.2:*:*:*:*:node.js:*:*", "cpe:2.3:a:handlebarsjs:handlebars:4.1.2:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/handlebars@4.1.2", "upstreams": []}}, {"vulnerability": {"id": "GHSA-f52g-6jhx-586p", "dataSource": "https://github.com/advisories/GHSA-f52g-6jhx-586p", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "Denial of Service in handlebars", "cvss": [], "fix": {"versions": ["4.4.5"], "state": "fixed"}, "advisories": [], "risk": 0}, "relatedVulnerabilities": [], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "handlebars", "version": "4.1.2"}}, "found": {"vulnerabilityID": "GHSA-f52g-6jhx-586p", "versionConstraint": ">=4.0.0,<4.4.5 (unknown)"}, "fix": {"suggestedVersion": "4.4.5"}}], "artifact": {"id": "d777ff7ccae5fee5", "name": "handlebars", "version": "4.1.2", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:handlebars.js_project:handlebars.js:4.1.2:*:*:*:*:node.js:*:*", "cpe:2.3:a:handlebarsjs:handlebars:4.1.2:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/handlebars@4.1.2", "upstreams": []}}, {"vulnerability": {"id": "GHSA-64g7-mvw6-v9qj", "dataSource": "https://github.com/advisories/GHSA-64g7-mvw6-v9qj", "namespace": "github:language:javascript", "severity": "Medium", "urls": [], "description": "Improper Privilege Management in shelljs", "cvss": [], "fix": {"versions": ["0.8.5"], "state": "fixed"}, "advisories": [], "risk": 0}, "relatedVulnerabilities": [], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "<PERSON><PERSON>s", "version": "0.8.3"}}, "found": {"vulnerabilityID": "GHSA-64g7-mvw6-v9qj", "versionConstraint": "<0.8.5 (unknown)"}, "fix": {"suggestedVersion": "0.8.5"}}], "artifact": {"id": "c6fe094cc73a284c", "name": "<PERSON><PERSON>s", "version": "0.8.3", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["BSD-3-<PERSON><PERSON>"], "cpes": ["cpe:2.3:a:shelljs:shelljs:0.8.3:*:*:*:*:*:*:*"], "purl": "pkg:npm/shelljs@0.8.3", "upstreams": []}}, {"vulnerability": {"id": "GHSA-ch52-vgq2-943f", "dataSource": "https://github.com/advisories/GHSA-ch52-vgq2-943f", "namespace": "github:language:javascript", "severity": "Low", "urls": [], "description": "Regular Expression Denial of Service in marked", "cvss": [], "fix": {"versions": ["0.7.0"], "state": "fixed"}, "advisories": [], "risk": 0}, "relatedVulnerabilities": [], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "marked", "version": "0.6.2"}}, "found": {"vulnerabilityID": "GHSA-ch52-vgq2-943f", "versionConstraint": ">=0.4.0,<0.7.0 (unknown)"}, "fix": {"suggestedVersion": "0.7.0"}}], "artifact": {"id": "f9d2a89384ac3c29", "name": "marked", "version": "0.6.2", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["MIT"], "cpes": ["cpe:2.3:a:marked_project:marked:0.6.2:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/marked@0.6.2", "upstreams": []}}, {"vulnerability": {"id": "GHSA-5rrq-pxf6-6jx5", "dataSource": "https://github.com/advisories/GHSA-5rrq-pxf6-6jx5", "namespace": "github:language:javascript", "severity": "Low", "urls": [], "description": "Prototype Pollution in node-forge debug API.", "cvss": [], "fix": {"versions": ["1.0.0"], "state": "fixed"}, "advisories": [], "risk": 0}, "relatedVulnerabilities": [], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "node-forge", "version": "0.8.5"}}, "found": {"vulnerabilityID": "GHSA-5rrq-pxf6-6jx5", "versionConstraint": "<1.0.0 (unknown)"}, "fix": {"suggestedVersion": "1.0.0"}}], "artifact": {"id": "087c9d217c8255bc", "name": "node-forge", "version": "0.8.5", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["(BSD-3-<PERSON><PERSON> OR GPL-2.0)"], "cpes": ["cpe:2.3:a:digitalbazaar:forge:0.8.5:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/node-forge@0.8.5", "upstreams": []}}, {"vulnerability": {"id": "GHSA-gf8q-jrpm-jvxq", "dataSource": "https://github.com/advisories/GHSA-gf8q-jrpm-jvxq", "namespace": "github:language:javascript", "severity": "Low", "urls": [], "description": "URL parsing in node-forge could lead to undesired behavior.", "cvss": [], "fix": {"versions": ["1.0.0"], "state": "fixed"}, "advisories": [], "risk": 0}, "relatedVulnerabilities": [], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "node-forge", "version": "0.8.5"}}, "found": {"vulnerabilityID": "GHSA-gf8q-jrpm-jvxq", "versionConstraint": "<1.0.0 (unknown)"}, "fix": {"suggestedVersion": "1.0.0"}}], "artifact": {"id": "087c9d217c8255bc", "name": "node-forge", "version": "0.8.5", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["(BSD-3-<PERSON><PERSON> OR GPL-2.0)"], "cpes": ["cpe:2.3:a:digitalbazaar:forge:0.8.5:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/node-forge@0.8.5", "upstreams": []}}, {"vulnerability": {"id": "GHSA-wxgw-qj99-44c2", "dataSource": "https://github.com/advisories/GHSA-wxgw-qj99-44c2", "namespace": "github:language:javascript", "severity": "Low", "urls": [], "description": "Prototype Pollution in node-forge util.setPath API", "cvss": [], "fix": {"versions": ["0.10.0"], "state": "fixed"}, "advisories": [], "risk": 0}, "relatedVulnerabilities": [], "matchDetails": [{"type": "exact-direct-match", "matcher": "javascript-matcher", "searchedBy": {"language": "javascript", "namespace": "github:language:javascript", "package": {"name": "node-forge", "version": "0.8.5"}}, "found": {"vulnerabilityID": "GHSA-wxgw-qj99-44c2", "versionConstraint": "<0.10.0 (unknown)"}, "fix": {"suggestedVersion": "0.10.0"}}], "artifact": {"id": "087c9d217c8255bc", "name": "node-forge", "version": "0.8.5", "type": "npm", "locations": [{"path": "/package-lock.json", "accessPath": "/package-lock.json", "annotations": {"evidence": "primary"}}], "language": "javascript", "licenses": ["(BSD-3-<PERSON><PERSON> OR GPL-2.0)"], "cpes": ["cpe:2.3:a:digitalbazaar:forge:0.8.5:*:*:*:*:node.js:*:*"], "purl": "pkg:npm/node-forge@0.8.5", "upstreams": []}}], "source": {"type": "directory", "target": "."}, "distro": {"name": "", "version": "", "idLike": null}, "descriptor": {"name": "grype", "version": "0.98.0", "configuration": {"output": ["json"], "file": "", "pretty": false, "distro": "", "add-cpes-if-none": false, "output-template-file": "", "check-for-app-update": true, "only-fixed": false, "only-notfixed": false, "ignore-wontfix": "", "platform": "", "search": {"scope": "squashed", "unindexed-archives": false, "indexed-archives": true}, "ignore": [{"vulnerability": "", "include-aliases": false, "reason": "", "namespace": "", "fix-state": "", "package": {"name": "kernel-headers", "version": "", "language": "", "type": "rpm", "location": "", "upstream-name": "kernel"}, "vex-status": "", "vex-justification": "", "match-type": "exact-indirect-match"}, {"vulnerability": "", "include-aliases": false, "reason": "", "namespace": "", "fix-state": "", "package": {"name": "linux(-.*)?-headers-.*", "version": "", "language": "", "type": "deb", "location": "", "upstream-name": "linux.*"}, "vex-status": "", "vex-justification": "", "match-type": "exact-indirect-match"}, {"vulnerability": "", "include-aliases": false, "reason": "", "namespace": "", "fix-state": "", "package": {"name": "linux-libc-dev", "version": "", "language": "", "type": "deb", "location": "", "upstream-name": "linux"}, "vex-status": "", "vex-justification": "", "match-type": "exact-indirect-match"}], "exclude": [], "externalSources": {"enable": false, "maven": {"searchUpstreamBySha1": true, "baseUrl": "https://search.maven.org/solrsearch/select", "rateLimit": 300000000}}, "match": {"java": {"using-cpes": false}, "jvm": {"using-cpes": true}, "dotnet": {"using-cpes": false}, "golang": {"using-cpes": false, "always-use-cpe-for-stdlib": true, "allow-main-module-pseudo-version-comparison": false}, "javascript": {"using-cpes": false}, "python": {"using-cpes": false}, "ruby": {"using-cpes": false}, "rust": {"using-cpes": false}, "stock": {"using-cpes": true}}, "fail-on-severity": "", "registry": {"insecure-skip-tls-verify": false, "insecure-use-http": false, "auth": null, "ca-cert": ""}, "show-suppressed": false, "by-cve": false, "SortBy": {"sort-by": "risk"}, "name": "", "default-image-pull-source": "", "vex-documents": [], "vex-add": [], "match-upstream-kernel-headers": false, "fix-channel": {"redhat-eus": {"apply": "auto", "versions": ">= 8.0"}}, "db": {"cache-dir": "/home/<USER>/.cache/grype/db", "update-url": "https://grype.anchore.io/databases", "ca-cert": "", "auto-update": true, "validate-by-hash-on-start": true, "validate-age": true, "max-allowed-built-age": 432000000000000, "require-update-check": false, "update-available-timeout": 30000000000, "update-download-timeout": 300000000000, "max-update-check-frequency": 7200000000000}, "exp": {}, "dev": {"db": {"debug": false}}}, "db": {"status": {"schemaVersion": "v6.1.0", "from": "https://grype.anchore.io/databases/v6/vulnerability-db_v6.1.0_2025-08-24T01:31:23Z_1756008838.tar.zst?checksum=sha256%3Aec38b995ff9e4db229c5580ccccbf32e2a63e9b87eed2f25cb5f32423d3184e5", "built": "2025-08-24T04:13:58Z", "path": "/home/<USER>/.cache/grype/db/6/vulnerability.db", "valid": true}, "providers": {"alpine": {"captured": "2025-08-24T01:32:23Z", "input": "xxh64:e283a74a355bcd4b"}, "amazon": {"captured": "2025-08-24T01:32:23Z", "input": "xxh64:31e95cb235122f27"}, "bitnami": {"captured": "2025-08-24T01:31:23Z", "input": "xxh64:35aa5b2bd0da7b24"}, "chainguard": {"captured": "2025-08-24T01:32:17Z", "input": "xxh64:89fcdaa909d40d4c"}, "debian": {"captured": "2025-08-24T01:32:29Z", "input": "xxh64:ed919c4cacf25562"}, "echo": {"captured": "2025-08-24T01:32:20Z", "input": "xxh64:98e9b2ab39883d74"}, "epss": {"captured": "2025-08-24T01:32:18Z", "input": "xxh64:566fa0659be0f16b"}, "github": {"captured": "2025-08-24T01:32:34Z", "input": "xxh64:77b2ea3b821a9bb1"}, "kev": {"captured": "2025-08-24T01:32:23Z", "input": "xxh64:b9e57c1e6d503e7a"}, "mariner": {"captured": "2025-08-24T01:32:19Z", "input": "xxh64:540b5113370dfd63"}, "minimos": {"captured": "2025-08-24T01:32:21Z", "input": "xxh64:ea95b57f330b6499"}, "nvd": {"captured": "2025-08-24T01:32:41Z", "input": "xxh64:5230dfc1938603e0"}, "oracle": {"captured": "2025-08-24T01:32:21Z", "input": "xxh64:2d1dabeb9ecaec11"}, "rhel": {"captured": "2025-08-24T01:33:10Z", "input": "xxh64:f29208cc197ed38b"}, "sles": {"captured": "2025-08-24T01:32:26Z", "input": "xxh64:df9993622058bc9e"}, "ubuntu": {"captured": "2025-08-24T01:33:36Z", "input": "xxh64:dd896b96d4ccb6db"}, "wolfi": {"captured": "2025-08-24T01:32:23Z", "input": "xxh64:bc3a5e0b99ba4875"}}}, "timestamp": "2025-08-24T02:45:06.227187407-05:00"}}