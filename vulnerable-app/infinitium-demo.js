#!/usr/bin/env node

/**
 * Infinitium Signal - Vulnerable Application Security Analysis Demo
 * 
 * This script demonstrates the comprehensive security analysis capabilities
 * of the Infinitium Signal cybersecurity platform using a deliberately
 * vulnerable Node.js application.
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

class InfinitiumSignalDemo {
    constructor() {
        this.results = {
            sbom: null,
            trivy: null,
            grype: null,
            snyk: null,
            npm_audit: null
        };
        
        this.vulnerabilityStats = {
            total: 0,
            critical: 0,
            high: 0,
            medium: 0,
            low: 0,
            negligible: 0
        };
    }

    async run() {
        console.log('🚀 Starting Infinitium Signal Security Analysis Demo');
        console.log('📋 Platform: Infinitium Signal v0.1.0');
        console.log('🎯 Target: Vulnerable Node.js Application');
        console.log('=' * 60);
        console.log();

        try {
            await this.checkToolAvailability();
            await this.loadExistingReports();
            await this.analyzeVulnerabilities();
            await this.generateSummaryReport();
            await this.demonstrateExploits();
        } catch (error) {
            console.error('❌ Demo failed:', error.message);
            process.exit(1);
        }
    }

    async checkToolAvailability() {
        console.log('🔧 Checking security tool availability:');
        
        const tools = [
            { name: 'Syft', command: 'syft --version' },
            { name: 'Trivy', command: 'trivy --version' },
            { name: 'Grype', command: 'grype --version' },
            { name: 'Snyk', command: 'snyk --version' },
            { name: 'NPM', command: 'npm --version' }
        ];

        for (const tool of tools) {
            try {
                await execAsync(tool.command);
                console.log(`✅ ${tool.name} is available`);
            } catch (error) {
                console.log(`❌ ${tool.name} is not available`);
            }
        }
        console.log();
    }

    async loadExistingReports() {
        console.log('📊 Loading security analysis reports:');
        
        const reports = [
            { name: 'SBOM', file: 'vulnerable-app-sbom.json', key: 'sbom' },
            { name: 'Trivy', file: 'trivy-scan.json', key: 'trivy' },
            { name: 'Grype', file: 'grype-scan.json', key: 'grype' },
            { name: 'Snyk', file: 'snyk-scan.json', key: 'snyk' }
        ];

        for (const report of reports) {
            try {
                if (fs.existsSync(report.file)) {
                    const stats = fs.statSync(report.file);
                    const content = fs.readFileSync(report.file, 'utf8');
                    this.results[report.key] = JSON.parse(content);
                    console.log(`✅ ${report.name}: ${this.formatBytes(stats.size)} loaded`);
                } else {
                    console.log(`⚠️  ${report.name}: Report not found`);
                }
            } catch (error) {
                console.log(`❌ ${report.name}: Failed to load - ${error.message}`);
            }
        }
        console.log();
    }

    async analyzeVulnerabilities() {
        console.log('🔍 Analyzing vulnerability data:');
        
        // Analyze Grype results
        if (this.results.grype && this.results.grype.matches) {
            const grypeStats = this.analyzeGrypeResults(this.results.grype);
            console.log(`📊 Grype found ${grypeStats.total} vulnerabilities:`);
            console.log(`   • Critical: ${grypeStats.critical}`);
            console.log(`   • High: ${grypeStats.high}`);
            console.log(`   • Medium: ${grypeStats.medium}`);
            console.log(`   • Low: ${grypeStats.low}`);
        }

        // Analyze Trivy results
        if (this.results.trivy && this.results.trivy.Results) {
            const trivyStats = this.analyzeTrivyResults(this.results.trivy);
            console.log(`📊 Trivy found ${trivyStats.total} vulnerabilities:`);
            console.log(`   • Critical: ${trivyStats.critical}`);
            console.log(`   • High: ${trivyStats.high}`);
            console.log(`   • Medium: ${trivyStats.medium}`);
            console.log(`   • Low: ${trivyStats.low}`);
        }

        // Analyze Snyk results
        if (this.results.snyk && this.results.snyk.vulnerabilities) {
            const snykStats = this.analyzeSnykResults(this.results.snyk);
            console.log(`📊 Snyk found ${snykStats.total} vulnerabilities:`);
            console.log(`   • Critical: ${snykStats.critical}`);
            console.log(`   • High: ${snykStats.high}`);
            console.log(`   • Medium: ${snykStats.medium}`);
            console.log(`   • Low: ${snykStats.low}`);
        }

        // Analyze SBOM
        if (this.results.sbom && this.results.sbom.components) {
            console.log(`📦 SBOM contains ${this.results.sbom.components.length} components`);
        }

        console.log();
    }

    analyzeGrypeResults(grypeData) {
        const stats = { total: 0, critical: 0, high: 0, medium: 0, low: 0, negligible: 0 };
        
        if (grypeData.matches) {
            stats.total = grypeData.matches.length;
            grypeData.matches.forEach(match => {
                const severity = match.vulnerability.severity.toLowerCase();
                if (stats.hasOwnProperty(severity)) {
                    stats[severity]++;
                }
            });
        }
        
        return stats;
    }

    analyzeTrivyResults(trivyData) {
        const stats = { total: 0, critical: 0, high: 0, medium: 0, low: 0, negligible: 0 };
        
        if (trivyData.Results) {
            trivyData.Results.forEach(result => {
                if (result.Vulnerabilities) {
                    result.Vulnerabilities.forEach(vuln => {
                        stats.total++;
                        const severity = vuln.Severity.toLowerCase();
                        if (stats.hasOwnProperty(severity)) {
                            stats[severity]++;
                        }
                    });
                }
            });
        }
        
        return stats;
    }

    analyzeSnykResults(snykData) {
        const stats = { total: 0, critical: 0, high: 0, medium: 0, low: 0 };
        
        if (snykData.vulnerabilities) {
            stats.total = snykData.vulnerabilities.length;
            snykData.vulnerabilities.forEach(vuln => {
                const severity = vuln.severity.toLowerCase();
                if (stats.hasOwnProperty(severity)) {
                    stats[severity]++;
                }
            });
        }
        
        return stats;
    }

    async generateSummaryReport() {
        console.log('📋 Security Analysis Summary:');
        console.log('=' * 50);
        
        const reportData = {
            timestamp: new Date().toISOString(),
            target: 'Vulnerable Node.js Demo Application',
            tools_used: ['Syft', 'Trivy', 'Grype', 'Snyk'],
            sbom_components: this.results.sbom ? this.results.sbom.components.length : 0,
            vulnerability_summary: this.vulnerabilityStats
        };

        console.log(`🎯 Target Application: ${reportData.target}`);
        console.log(`📅 Analysis Date: ${reportData.timestamp}`);
        console.log(`🔧 Tools Used: ${reportData.tools_used.join(', ')}`);
        console.log(`📦 SBOM Components: ${reportData.sbom_components}`);
        console.log();
        
        console.log('🚨 Key Vulnerabilities Detected:');
        console.log('• SQL Injection vulnerabilities in login endpoint');
        console.log('• Cross-Site Scripting (XSS) in search functionality');
        console.log('• Command Injection in ping utility');
        console.log('• Path Traversal in file download');
        console.log('• Insecure cryptographic implementations');
        console.log('• Authentication bypass mechanisms');
        console.log('• Multiple dependency vulnerabilities');
        console.log();

        // Save summary report
        fs.writeFileSync('security-analysis-summary.json', JSON.stringify(reportData, null, 2));
        console.log('✅ Summary report saved to security-analysis-summary.json');
        console.log();
    }

    async demonstrateExploits() {
        console.log('⚠️  Vulnerability Demonstration Examples:');
        console.log('=' * 50);
        
        console.log('🔓 SQL Injection Example:');
        console.log("   POST /login");
        console.log('   {"username": "admin\' OR 1=1 --", "password": "anything"}');
        console.log();
        
        console.log('🔓 XSS Example:');
        console.log("   GET /search?q=<script>alert('XSS')</script>");
        console.log();
        
        console.log('🔓 Command Injection Example:');
        console.log("   POST /ping");
        console.log('   {"host": "127.0.0.1; cat /etc/passwd"}');
        console.log();
        
        console.log('🔓 Path Traversal Example:');
        console.log("   GET /file/../../etc/passwd");
        console.log();
        
        console.log('⚠️  WARNING: These are demonstration examples only!');
        console.log('   Do not use these techniques against systems you do not own.');
        console.log();
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Run the demo
if (require.main === module) {
    const demo = new InfinitiumSignalDemo();
    demo.run().catch(console.error);
}

module.exports = InfinitiumSignalDemo;
